import{r as e,b0 as t,b5 as i,aP as n}from"./index-Cak6rALw.js";import{d as r,aG as s,S as o,i as l,c as a,h as d,b as u,t as c}from"./MyApp-DW5WH4Ub.js";import m from"./MyTable-DnjDmN7z.js";import{d as p}from"./dayjs.min-CzPn7FMI.js";import g from"./useCacheState-CAnxkewm.js";import{A as j}from"./index-DdM4T8-Q.js";import{D as v}from"./index-DwLJmsHL.js";import"./index-ByRdMNW-.js";import"./index-BzMPigdO.js";import"./DownOutlined-B-JcS-29.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./row-BMfM-of4.js";import"./index-PbLYNhdQ.js";import"./ClockCircleOutlined-BVovxNRp.js";function x(){const{message:x}=r(),[E,I]=g("Logs.time",p().format("YYYY-MM-DD")),[N,k]=g("Logs.allLogs."+E,[]),[w,M]=g("Logs.loading."+E,!1);e.useEffect((()=>{}),[]);const O=e.useMemo((()=>({version:s(N,"version"),eventName:s(N,"eventName"),uid:s(N,"uid")})),[N]),D=e=>{I(e.format("YYYY-MM-DD"))},P=[{title:"#",dataIndex:"i",key:"i",width:50},{title:"time",dataIndex:"timeString",key:"timeString",width:100},{title:O.version.length+" version",dataIndex:"version",key:"version",width:80,filters:O.version,onFilter:(e,t)=>t.version===e},{title:O.uid.length+" users",dataIndex:"uid",key:"uid",render:(e,i,n)=>t.jsxs(o,{children:[t.jsx(j,{shape:"square",src:l(i.uid,40),size:40}),t.jsx(a.Link,{href:d(i.uid),target:"_blank",children:i.uid})]}),width:200,filters:O.uid,onFilter:(e,t)=>t.uid===e},{title:O.eventName.length+" events",dataIndex:"eventName",key:"eventName",width:200,filters:O.eventName,onFilter:(e,t)=>t.eventName===e,filterSearch:!0,render:(e,i,n)=>null==e?void 0:e.split(":").map(((e,i)=>t.jsxs(u,{color:["blue","green","red","orange"][i],children:[0==i?"":":",e]},i)))},{title:"isScript",dataIndex:"isScript",key:"isScript",render:(e,t,i)=>e?"✅":"❌",filters:[{text:"✅ "+N.filter((e=>e.isScript)).length,value:!0},{text:"❌ "+N.filter((e=>!e.isScript)).length,value:!1}],onFilter:(e,t)=>t.isScript===e,width:50},{title:"total",dataIndex:"totalCount",key:"totalCount",width:70}];return t.jsxs("div",{children:[t.jsx("h3",{children:"Logs"}),t.jsxs("p",{children:[N.length," logs"]}),t.jsx(m,{columns:P,data:N,virtual:!0,searchable:!0,renderTitle:()=>t.jsxs(t.Fragment,{children:[t.jsx(i,{loading:w,onClick:()=>(async(e=!1)=>{if(!e&&N.length)return;const t="Logs:onReload:"+E;c(t),M(!0),x.loading({key:t,content:"Loading logs..."+E,duration:0});const i=await fetch(n.FB_AIO.server+"/log/"+E),r=(await i.text()).split("<br/>").filter((e=>e)).map((e=>e.trim()));if(console.log(r),!r.length)return console.log("no logs");"Log not found"!=r[0]&&r[0];const s=r.map(((e,t)=>{const i=h(e).replace(/\d+\/\d+\/\d+, /,""),n=null==i?void 0:i.split(" ")[1];let r=parseInt(null==i?void 0:i.split(":")[0])+("PM"==n?12:0);12==r&&"AM"==n&&(r=0);const s={i:t,log:e,uid:f(e),time:new Date(h(e)),timeString:i,hour:r,eventName:S(e),version:L(e),totalCount:parseInt(C(e))||0,isScript:y(e),fbName:"",fbAvatar:"",logPretty:""},o=s.eventName.replace("("+s.version+")","").trim(),l=function(e,t,i=" "){return e+(t-e.length>0?i.repeat(t-e.length):"")}(s.version,4," ");return s.logPretty=l&&o?`${s.timeString} | ${l} | ${o} - ${s.totalCount}`:s.log,s}));k(s),M(!1),x.success({key:t,content:"Loaded "+s.length+" logs: "+E})})(!0),icon:t.jsx("i",{className:"fa-solid fa-rotate-right"}),children:"Reload"}),t.jsx(v,{defaultValue:p(),format:"YYYY-MM-DD",value:p(E),onChange:D,prefix:t.jsx("i",{className:"fa-regular fa-calendar"}),allowClear:!1})]}),keyExtractor:e=>e.uid+e.log})]})}function f(e){var t;return(null==(t=/-(\d+)\)/.exec(e))?void 0:t[1])||"?"}function h(e){var t;return(null==(t=/\d{1,2}\/\d{1,2}\/\d{4}, \d{1,2}:\d{1,2}:\d{1,2} \w{2}/.exec(e))?void 0:t[0])||""}function S(e){var t;return(null==(t=/: (.*?) \(/.exec(e))?void 0:t[1])||""}function C(e){var t;return(null==(t=/ -> (\d+)/.exec(e))?void 0:t[1])||""}function L(e){var t;return(null==(t=/ \((.*?)-\d*\)/.exec(e))?void 0:t[1])||""}function y(e){return!(e.includes("INSTALLED")||e.includes("OPEN-")||e.includes("CLICK_")||e.includes("-INFO")||e.includes("-FAVORITE")||e.includes("-VIEW-SOURCE")||e.includes("CHECK-FOR-UPDATE")||e.includes("RESTORE")||e.includes("BACKUP")||e.includes("CHANGE-THEME")||e.includes("CHANGE-SMOOTH-SCROLL")||e.includes("getVIP"))}export{x as default};
