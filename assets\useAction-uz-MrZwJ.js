const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./MySwal-8JMBfvUJ.js","./sweetalert2.esm.all-BZxvatOx.js","./index-Cak6rALw.js","./index-DIX6R8z-.css"])))=>i.map(i=>d[i]);
import{r as e,b0 as n,aN as i,bg as t,aP as r}from"./index-Cak6rALw.js";import{u as a,d as s,t as o,S as c,c as l,aF as u,v as d,b as h}from"./MyApp-DW5WH4Ub.js";import{checkVIP as g}from"./useVIP-D5FAMGQQ.js";import{a as m}from"./index-QU7lSj_a.js";import{D as f}from"./index-CC5caqt_.js";function x(){const{ti:x}=a(),{message:p,notification:y}=s(),[w,k]=e.useState(!1),j=e.useRef(!1),v=async(e={},a=!1)=>{if(w)return!0;const s=(await i((async()=>{const{default:e}=await import("./MySwal-8JMBfvUJ.js");return{default:e}}),__vite__mapDeps([0,1,2,3]),import.meta.url)).default;return(await s.fire({title:(null==e?void 0:e.title)||x({en:"Confirm",vi:"Xác nhận"}),showCancelButton:!0,cancelButtonText:x({en:"Cancel",vi:"Huỷ"}),confirmButtonText:(null==e?void 0:e.confirmButtonText)||x({en:"OK",vi:"OK"}),html:n.jsxs(c,{direction:"vertical",children:[n.jsx(l.Text,{children:(null==e?void 0:e.text)||x({en:"Are you sure?",vi:"Chắc chứ?"})}),a?n.jsxs(n.Fragment,{children:[n.jsx(f,{}),n.jsx(m,{defaultChecked:j.current,name:"bypassError",onChange:e=>j.current=e.target.checked,children:n.jsxs(c,{direction:"vertical",size:0,children:[n.jsx(l.Text,{strong:!0,children:x({en:"Auto skip when error occurs?",vi:"Tự động bỏ qua khi gặp lỗi?"})}),n.jsx(l.Text,{type:"danger",style:{fontStyle:"italic"},children:x({en:"Recommend OFF, avoid Facebook ban when reach limit",vi:"Nên TẮT, tránh bị facebook ban khi dính limit"})})]})})]}):n.jsx(c,{children:n.jsx(t,{size:"small",expandIcon:()=>null,items:[{key:"1",label:n.jsxs(l.Text,{children:[n.jsx("i",{className:"fa-solid fa-eye-slash"})," ",x({en:"Do not show again",vi:"Không hiện lại"})]}),children:n.jsxs(c,{direction:"vertical",size:0,children:[n.jsx(h,{color:"error",children:x({en:"Warning",vi:"Lưu ý"})}),n.jsx("ul",{style:{textAlign:"left"},children:x({en:["⏳ Please wait few seconds each click","🚫 To prevent ban from Facebook","❌ Can not use action when you are banned"],vi:["⏳ Nên chờ vài giây giữa những lần click","🚫 Để tránh bị ban bởi Facebook","❌ Không dùng được chức năng nếu bị ban"]}).map(((e,i)=>n.jsx("li",{children:e},i)))}),n.jsx(h,{color:"success",children:x({en:"Recommend",vi:"Lời khuyên"})}),n.jsx("ul",{style:{textAlign:"left"},children:x({en:["✅ You can select multiple rows","🤖 Then click Bulk Actions","⚡ FB AIO has built-in wait time"],vi:["✅ Hãy tích chọn nhiều dòng","🤖 Rồi bấm chức năng chạy hàng loạt","⚡ FB AIO có sẵn cơ chế chờ"]}).map(((e,i)=>n.jsx("li",{children:e},i)))}),n.jsxs(l.Link,{href:r.FB_AIO.banWarning,target:"_blank",onClick:()=>{o("ban_warning")},children:[x({en:"Read more",vi:"Xem thêm"})," ",n.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})]}),n.jsx(m,{name:"confirm",onChange:e=>k(e.target.checked),children:x({en:"Understood. Dont show again",vi:"Đã hiểu. Không hỉện lại"})})]})}]})})]}),...e})).isConfirmed};return{onClickBulkActions:async function({data:e,key:t,actionFn:r,loadingText:a,successText:s,successDesc:h,successDescItem:f,waitTime:w=[2e3,5e3],requireVIP:k=!0,needConfirm:b=!0,showSuccessNoti:T=!0,confirmProps:C}){if(b){if(!(await v(C,!0)))return}if(k&&!(await g()))return;if(!(null==e?void 0:e.length))return;const A=t+":waiting";o(t);let F=!1,_=[];for(let o=0;o<e.length&&(p.destroy(A),!F);o++){const s={key:t,content:n.jsxs(n.Fragment,{children:[a(e[o],o,e)+" ("+(o+1)+"/"+e.length+")",n.jsx("br",{}),n.jsx("i",{children:x({en:"Click to stop",vi:"Bấm để dừng"})})]}),duration:0,onClick:()=>{p.loading({key:t,content:x({en:"Stopping...",vi:"Đang dừng..."}),duration:0}),F=!0}};p.loading(s);try{if(!(await r(e[o])))throw new Error(x({en:"Action failed",vi:"Chức năng không thành công"})+": "+x(a(e[o],o,e))+". "+x(null==f?void 0:f(e[o])));_.push(e[o]),p.success(s)}catch(B){if(j.current)y.error({message:B.message,duration:0});else{const e=(await i((async()=>{const{default:e}=await import("./MySwal-8JMBfvUJ.js");return{default:e}}),__vite__mapDeps([0,1,2,3]),import.meta.url)).default;if(!(await e.fire({title:x({en:"Error occurred",vi:"Đã xảy ra lỗi"}),text:B.message,icon:"error",showCancelButton:!0,cancelButtonText:x({en:"Stop",vi:"Dừng"}),confirmButtonText:x({en:"Continue",vi:"Tiếp tục"}),allowOutsideClick:!1,allowEscapeKey:!1,html:n.jsxs(c,{direction:"vertical",children:[n.jsx(l.Text,{children:B.message}),n.jsx(m,{name:"bypassError",onChange:e=>j.current=e.target.checked,children:x({en:"Auto skip when error occurs",vi:"Tự động bỏ qua khi gặp lỗi"})})]})})).isConfirmed){F=!0;break}}}if(F)break;if(w&&o<e.length-1){const e=u(w[0]||w,w[1]||w);await d(e,(e=>(p.loading({key:A,content:x({en:"Wating "+Math.round(e/1e3)+"s",vi:"Đang đợi "+Math.round(e/1e3)+"s"}),duration:0}),F)))}}p.destroy(t),p.destroy(A),T&&y.open({type:"success",duration:0,message:s(_,e)+` (${_.length} / ${e.length})`,description:f?n.jsx("ol",{style:{maxHeight:200,overflowY:"auto",whiteSpace:"pre-wrap"},children:_.map(((e,i)=>n.jsx("li",{children:f(e)},i)))}):null==h?void 0:h(_,e)})},onClickAction:async function({key:e,id:n,record:i,loadingText:t,successText:r,failedText:a,actionFn:s,onSuccess:c,requireVIP:l=!1,needConfirm:u=!0,confirmProps:d,finallyFn:h}){if(u){if(!(await v(d,!1)))return null==h||h(),!1}if(l&&!(await g()))return null==h||h(),!1;o(e),e+=":"+n;try{p.loading({key:e,content:t(i),duration:0});if(await s(i))return null==c||c(),p.success({key:e,content:r(i)}),!0;throw new Error("Action failed")}catch(m){return p.destroy(e),y.open({type:"error",message:a(i),description:m.message}),!1}finally{null==h||h()}}}}export{x as u};
