import{extractVideoVariants as i}from"./videos-D2LbKcXH.js";import{x as e,az as o}from"./MyApp-DW5WH4Ub.js";import{fetchInstaGraphQl as n,getBiggestUrl as l}from"./core-Dw7OsFL6.js";import"./index-Cak6rALw.js";function a(i,e){var o,n,a;const t=(null==(o=null==i?void 0:i.node)?void 0:o.media)||{};return{id:t.id,post_id:null==t?void 0:t.code,type:null==(n=null==i?void 0:i.node)?void 0:n.__typename,width:t.original_width,height:t.original_height,image:l(null==(a=t.image_versions2)?void 0:a.candidates),video:l(t.video_versions),comment_count:t.comment_count,like_count:t.like_count,play_count:t.play_count,view_count:t.view_count,cursor:(null==i?void 0:i.cursor)||(null==e?void 0:e.end_cursor),raw:i}}async function t(i="",l=""){const t=await n({fb_api_req_friendly_name:"PolarisProfileReelsTabContentQuery_connection",variables:{after:l,before:null,data:{include_feed_video:!0,page_size:12,target_user_id:i},first:4,last:null},doc_id:"7845543455542541"}),d=e(t);console.log("res ne",d);const{edges:r=[],page_info:u={}}=o(d);return r.map((i=>a(i,u)))}async function d(l=""){const t=await n({fb_api_req_friendly_name:"PolarisClipsTabDesktopContainerQuery",variables:{data:{container_module:"clips_tab_desktop_page"},first:null,__relay_internal__pv__PolarisReelsShareMenurelayprovider:!0},doc_id:8431013483653518}),d=e(t);console.log("getInstaReelsFeed",d);const{edges:r=[],page_info:u={}}=o(d);return r.map((e=>{var o,n,l,t,d,r,s,_;const c=(null==(o=null==e?void 0:e.node)?void 0:o.media)||{},v=null==(l=null==(n=null==c?void 0:c.clips_metadata)?void 0:n.music_info)?void 0:l.music_asset_info,p=null==(t=null==c?void 0:c.clips_metadata)?void 0:t.original_sound_info;return{...a(e,u),code:null==c?void 0:c.code,caption:null==(d=null==c?void 0:c.caption)?void 0:d.text,music:p?null:{id:null==v?void 0:v.audio_cluster_id,title:null==v?void 0:v.title,artist:null==v?void 0:v.display_artist,cover:null==v?void 0:v.cover_artwork_thumbnail_uri},user:{id:null==(r=null==c?void 0:c.user)?void 0:r.id,username:null==(s=null==c?void 0:c.user)?void 0:s.username,avatar:null==(_=null==c?void 0:c.user)?void 0:_.profile_pic_url},created_at:1e3*((null==c?void 0:c.taken_at)||0),variants:i(null==c?void 0:c.video_dash_manifest)}}))}export{t as getInstaReels,d as getInstaReelsFeed};
