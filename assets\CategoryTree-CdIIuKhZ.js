import{r as e,b0 as n}from"./index-Cak6rALw.js";import{C as t,a as r,b as i}from"./index-BatTeoJ3.js";import{u as o,s as c}from"./MyApp-DW5WH4Ub.js";import{u as s}from"./index-ByRdMNW-.js";import{I as l}from"./index-Bg865k-U.js";import{T as a}from"./index-D6X7PZwe.js";import"./Screen-Buq7HJpK.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./List-B6ZMXgC_.js";import"./index-CxAc8H5Q.js";function d({onCategorySelect:d}){const{ti:u,language:m}=o(),[j,y]=e.useState(""),[x,g]=e.useState([]),[k,b]=e.useState(!0),[v,S]=e.useState([]),w=e.useMemo((()=>{const e=p(r,u);if(!j)return e;const t=e=>e.map((e=>{const r={...e};if("string"==typeof e.title){const t=c(j,e.title);r.title=t?n.jsx("span",{style:h,children:e.title}):e.title}return e.children&&e.children.length>0&&(r.children=t(e.children)),r}));return t(e)}),[m,j]),C=(e,n)=>{const{node:r,checked:i}=n;if(!i)return d(null),void S([]);const o=r.key;if(o&&o in t){const e=[];e.push(o),d(o);const n=f(r.children);e.push(...n),S(e)}},O=s((e=>{if(y(e),!e)return g([]),void b(!1);const n=[],t=(r,o=[])=>{r.forEach((r=>{const s=i[r.key]||r.key;c(e,u(s))&&n.push(...o),r.children&&r.children.length>0&&t(r.children,[...o,r.key])}))};t(w),g([...new Set(n)]),b(!0)}),500);return n.jsxs("div",{style:{marginBottom:"20px"},children:[n.jsx(l.Search,{style:{marginBottom:8},placeholder:u({en:"Search categories",vi:"Tìm kiếm danh mục"}),onChange:e=>O(e.target.value),allowClear:!0}),n.jsx(a.DirectoryTree,{showIcon:!0,style:{width:"100%"},className:"custom-directory-tree",checkedKeys:v,expandedKeys:x,autoExpandParent:k,onCheck:C,onSelect:(e,n)=>{n.node.hasChildren||C(0,{node:n.node,checked:!n.node.checked})},onExpand:e=>{g(e),b(!1)},treeData:w,filterTreeNode:e=>!j||c(j,e.title),showLine:{showLeafIcon:!1},switcherIcon:e=>n.jsx("i",{className:e.expanded?"fa-solid fa-chevron-down":"fa-solid fa-chevron-right"}),checkable:!0})]})}const h={backgroundColor:"#ffd591",padding:"0 4px",borderRadius:"2px",color:"#000"},u=e=>{var n;return(null==(n=i[e])?void 0:n.icon)||"fa-solid fa-folder"},p=(e,n)=>{if(!e)return[];const t=[];return e.children&&Object.entries(e.children).forEach((([e,r])=>{const i=m(r,e,n);i&&t.push(i)})),t},m=(e,r,o)=>{var c;const s=Object.hasOwn(t,r),l={title:o(e.name||i[r]||r),key:r,selectable:s,checkable:s,children:[],icon:n.jsx("i",{className:(null==(c=e.name)?void 0:c.icon)||u(r)}),hasChildren:e.children&&Object.keys(e.children).length>0};return e.children&&Object.keys(e.children).length>0&&Object.entries(e.children).forEach((([e,n])=>{const t=m(n,e,o);t&&l.children.push(t)})),0!==l.children.length||s||"children"===r?l:null},f=e=>{let n=[];return e.forEach((e=>{e.children&&e.children.length>0?n=[...n,...f(e.children)]:e.checkable&&n.push(e.key)})),n};export{d as default};
