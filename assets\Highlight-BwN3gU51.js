const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./RecentStoryViewer-Bikh4z-C.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./stories-DQ6hTlzb.js","./MyApp-DW5WH4Ub.js","./useCacheState-CAnxkewm.js","./index-CDSnY0KE.js","./EyeOutlined-CNKPohhw.js","./addEventListener-C4tIKh7N.js","./index-DdM4T8-Q.js","./useBreakpoint-CPcdMRfj.js","./index-PbLYNhdQ.js"])))=>i.map(i=>d[i]);
import{b0 as t,b1 as i,b5 as e,aN as o}from"./index-Cak6rALw.js";import{S as r,B as s,c as n,h as a,aR as l,I as m}from"./MyApp-DW5WH4Ub.js";import{getHighlights as d}from"./highlights-BVXchAWb.js";import p from"./Collection-BYcChfHL.js";import{getCacheByKey as j}from"./useCacheState-CAnxkewm.js";import{getStoriesBucketsData as c}from"./stories-DQ6hTlzb.js";import{L as h}from"./index-jrOnBmdW.js";import{I as u}from"./index-CDSnY0KE.js";import"./index-ByRdMNW-.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-PbLYNhdQ.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CC5caqt_.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./addEventListener-C4tIKh7N.js";const x=i((()=>o((()=>import("./RecentStoryViewer-Bikh4z-C.js").then((t=>t.R))),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11]),import.meta.url)),{fallback:m});function f({target:i}){const o=(null==i?void 0:i.name)+" - Highlight";return t.jsx(p,{collectionName:o,fetchNext:async(t=[],e)=>{var o;if(!(null==i?void 0:i.id))return;const r=e||(null==(o=t[t.length-1])?void 0:o.cursor);return await d(null==i?void 0:i.id,r)},renderItem:(l,m)=>t.jsxs(h.Item,{className:"show-on-hover-trigger",children:[t.jsxs(r,{direction:"vertical",style:{position:"relative"},children:[t.jsx(s.Ribbon,{text:l.count,color:"blue",children:t.jsx(u,{src:l.thumbnail,fallback:l.thumbnail,style:{width:150,height:280,borderRadius:10,objectFit:"cover"},preview:{destroyOnClose:!0,imageRender:()=>t.jsx(x,{story:{bucket_id:l.id,owner:i},bucketsData:j("Collection.data."+o)||[],index:m}),toolbarRender:()=>null}})}),l.title&&t.jsx(n.Text,{children:l.title})]}),t.jsx(e,{type:"default",icon:t.jsx("i",{className:"fa-solid fa-up-right-from-square"}),style:{position:"absolute",bottom:10,right:10},className:"show-on-hover-item",target:"_blank",href:a(`stories/${l.id}/?source=profile_highlight`)})]}),downloadItem:async t=>{const i=await c(t.id);return console.log(i),i.map(((i,e)=>({name:l(t.title)+(i.video?".mp4":".jpg"),url:i.video||i.image||i.thumbnail})))},rowKey:t=>t.id})}export{f as default};
