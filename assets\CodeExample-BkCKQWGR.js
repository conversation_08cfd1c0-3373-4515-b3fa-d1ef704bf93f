import{r as n,b0 as e}from"./index-Cak6rALw.js";import a from"./useCacheState-CAnxkewm.js";import t from"./CodeBlock-BP5ddTLM.js";import{S as r}from"./MyApp-DW5WH4Ub.js";import{S as i}from"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./PurePanel-BvHjNOhQ.js";import"./index-SRy1SMeO.js";import"./move-ESLFEEsv.js";import"./DownOutlined-B-JcS-29.js";import"./SearchOutlined--mXbzQ68.js";function p({url:p,apiname:o,apiparams:s,clientId:l}){const c=n.useMemo((()=>[{key:"js",label:"Javascript",language:"javascript",code:m({url:p,clientId:l,apiname:o,apiparams:s})},{key:"curl",label:"cURL",language:"shell",code:y({url:p,clientId:l,apiname:o,apiparams:s})},{key:"python",label:"Python",language:"python",code:d({url:p,clientId:l,apiname:o,apiparams:s})},{key:"csharp",label:"C#",language:"csharp",code:g({url:p,clientId:l,apiname:o,apiparams:s})},{key:"java",label:"Java",language:"java",code:$({url:p,clientId:l,apiname:o,apiparams:s})},{key:"go",label:"Go",language:"go",code:j({url:p,clientId:l,apiname:o,apiparams:s})},{key:"php",label:"PHP",language:"php",code:f({url:p,clientId:l,apiname:o,apiparams:s})},{key:"ruby",label:"Ruby",language:"ruby",code:b({url:p,clientId:l,apiname:o,apiparams:s})}]),[o,s,l,p]),[u,h]=a("codeExample.activeTab",c[0].key,!0),I=c.find((n=>n.key===u));return e.jsxs(r,{direction:"vertical",style:{width:"100%"},children:[e.jsx(i,{value:u,onChange:h,popupMatchSelectWidth:!1,options:c.map((({key:n,label:e})=>({value:n,label:e})))}),I&&e.jsx(t,{code:I.code,language:I.language})]})}function o(n){return null===n?"nil":Array.isArray(n)?`[]interface{}{${n.map(o).join(", ")}}`:"object"==typeof n?`map[string]interface{}{${Object.entries(n).map((([n,e])=>`"${n}": ${o(e)}`)).join(", ")}}`:"string"==typeof n?`"${n.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}"`:"number"==typeof n||"boolean"==typeof n?String(n):"nil"}function s(n){return null===n?"null":Array.isArray(n)?"array("+n.map(s).join(", ")+")":"object"==typeof n?"array("+Object.entries(n).map((([n,e])=>`'${n}' => ${s(e)}`)).join(", ")+")":"string"==typeof n?`'${n.replace(/\\/g,"\\\\").replace(/'/g,"\\'")}'`:"number"==typeof n||"boolean"==typeof n?String(n):"null"}function l(n){return null===n?"nil":Array.isArray(n)?`[${n.map(l).join(", ")}]`:"object"==typeof n?`{ ${Object.entries(n).map((([n,e])=>`${/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(n)?n+":":`'${n}'=>`} ${l(e)}`)).join(", ")} }`:"string"==typeof n?`'${n.replace(/\\/g,"\\\\").replace(/'/g,"\\'")}'`:"number"==typeof n||"boolean"==typeof n?String(n):"nil"}function c(n){return null===n?"null":Array.isArray(n)?"new object[] {"+n.map(c).join(", ")+"}":"object"==typeof n?"new { "+Object.entries(n).map((([n,e])=>`${n} = ${c(e)}`)).join(", ")+" }":"string"==typeof n?`"${n.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}"`:"number"==typeof n||"boolean"==typeof n?String(n).toLowerCase():"null"}function u(n){return null===n?"None":Array.isArray(n)?`[${n.map(u).join(", ")}]`:"object"==typeof n?`{${Object.entries(n).map((([n,e])=>`'${n}': ${u(e)}`)).join(", ")}}`:"string"==typeof n?`'${n.replace(/\\/g,"\\\\").replace(/'/g,"\\'")}'`:"number"==typeof n||"boolean"==typeof n?String(n).toLowerCase():"None"}const m=({url:n,clientId:e,apiname:a,apiparams:t})=>`fetch('${n}', {\n  method: 'POST',\n  headers: { 'Content-Type': 'application/json' },\n  body: JSON.stringify({\n    id: '${e||"YOUR_CLIENT_ID"}',\n    apiname: '${a}',\n    apiparams: ${JSON.stringify(t)}\n  })\n})`,y=({url:n,clientId:e,apiname:a,apiparams:t})=>`curl -X POST '${n}'\n  -H 'Content-Type: application/json'\n  -d '{\n    "id":"${e||"YOUR_CLIENT_ID"}",\n    "apiname":"${a}",\n    "apiparams":${JSON.stringify(t)}\n}'`,d=({url:n,clientId:e,apiname:a,apiparams:t})=>`import requests\n\nurl = '${n}'\ndata = {\n  'id': '${e||"YOUR_CLIENT_ID"}',\n  'apiname': '${a}',\n  'apiparams': ${u(t)}\n}\nheaders = {'Content-Type': 'application/json'}\nr = requests.post(url, json=data, headers=headers)\nprint(r.json())`,g=({url:n,clientId:e,apiname:a,apiparams:t})=>`using System.Net.Http;\nusing System.Text;\nusing Newtonsoft.Json;\n\nvar url = "${n}";\nvar data = new {\n  id = "${e||"YOUR_CLIENT_ID"}",\n  apiname = "${a}",\n  apiparams = ${c(t)}\n};\nvar json = JsonConvert.SerializeObject(data);\nusing (var client = new HttpClient())\nusing (var content = new StringContent(json, Encoding.UTF8, "application/json"))\n{\n  var response = await client.PostAsync(url, content);\n  var result = await response.Content.ReadAsStringAsync();\n  System.Console.WriteLine(result);\n}`,$=({url:n,clientId:e,apiname:a,apiparams:t})=>`import java.net.*;\nimport java.io.*;\n\npublic class Main {\n  public static void main(String[] args) throws Exception {\n    HttpURLConnection c = (HttpURLConnection) new URL("${n}").openConnection();\n    c.setRequestMethod("POST");\n    c.setRequestProperty("Content-Type", "application/json");\n    c.setDoOutput(true);\n    String j = String.format(\n      "{\\"id\\":\\"%s\\",\\"apiname\\":\\"%s\\",\\"apiparams\\":%s}",\n      "${e||"YOUR_CLIENT_ID"}",\n      "${a}",\n      "${JSON.stringify(t).replace(/\\/g,"\\\\").replace(/"/g,'\\"')}"\n    );\n    try (OutputStream os = c.getOutputStream()) {\n      os.write(j.getBytes("utf-8"));\n    }\n    System.out.println(c.getResponseCode());\n  }\n}`,j=({url:n,clientId:e,apiname:a,apiparams:t})=>`package main\n\nimport (\n  "bytes"\n  "encoding/json"\n  "net/http"\n  "fmt"\n)\n\nfunc main() {\n  u := "${n}"\n  d := map[string]interface{}{\n    "id": "${e||"YOUR_CLIENT_ID"}",\n    "apiname": "${a}",\n    "apiparams": ${o(t)}\n  }\n  j, _ := json.Marshal(d)\n  r, _ := http.Post(u, "application/json", bytes.NewBuffer(j))\n  defer r.Body.Close()\n  fmt.Println(r.Status)\n}`,f=({url:n,clientId:e,apiname:a,apiparams:t})=>`<?php\n$u = "${n}";\n$d = array(\n  'id' => '${e||"YOUR_CLIENT_ID"}',\n  'apiname' => '${a}',\n  'apiparams' => ${s(t)}\n);\n$o = array(\n  'http' => array(\n    'header' => 'Content-type: application/json',\n    'method' => 'POST',\n    'content' => json_encode($d),\n  ),\n);\n$c = stream_context_create($o);\n$r = file_get_contents($u, false, $c);\nif ($r === FALSE) {}\necho $r;\n?>`,b=({url:n,clientId:e,apiname:a,apiparams:t})=>`require 'net/http'\nrequire 'json'\n\nu = URI('${n}')\nd = {\n  id: '${e||"YOUR_CLIENT_ID"}',\n  apiname: '${a}',\n  apiparams: ${l(t)}\n}\nh = Net::HTTP.new(u.host, u.port)\nh.use_ssl = u.scheme == 'https'\nreq = Net::HTTP::Post.new(u)\nreq['Content-Type'] = 'application/json'\nreq.body = d.to_json\nres = h.request(req)\nputs res.body`;export{p as default};
