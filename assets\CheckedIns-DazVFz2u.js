const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./MyApp-DW5WH4Ub.js","./index-Cak6rALw.js","./index-DIX6R8z-.css"])))=>i.map(i=>d[i]);
import{aN as e,aT as a,bc as i,r as t,b0 as l,b5 as n,bk as o}from"./index-Cak6rALw.js";import{u as r,t as s,S as d,c,T as p,A as u,o as m}from"./MyApp-DW5WH4Ub.js";import{u as v}from"./useForceStop-CvbJS7ei.js";import h from"./MyTable-DnjDmN7z.js";import{E as g}from"./ExportButton-CbyepAf_.js";import f,{getCacheByKey as j}from"./useCacheState-CAnxkewm.js";import{I as x}from"./index-CDSnY0KE.js";import"./index-ByRdMNW-.js";import"./index-BzMPigdO.js";import"./DownOutlined-B-JcS-29.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./row-BMfM-of4.js";async function k({uid:a="",cursor:i=""}){const{fetchGraphQl:t}=await e((async()=>{const{fetchGraphQl:e}=await import("./MyApp-DW5WH4Ub.js").then((e=>e.aV));return{fetchGraphQl:e}}),__vite__mapDeps([0,1,2]),import.meta.url),l=await t({fb_api_req_friendly_name:"ProfileCometAppCollectionListRendererPaginationQuery",variables:{cursor:i,count:8,scale:2,search:null,id:btoa("app_collection:"+a+":************:103")},doc_id:"25969811035995761"}),{parseSafe:n}=await e((async()=>{const{parseSafe:e}=await import("./MyApp-DW5WH4Ub.js").then((e=>e.aU));return{parseSafe:e}}),__vite__mapDeps([0,1,2]),import.meta.url),o=n(l),{findDataObject:r}=await e((async()=>{const{findDataObject:e}=await import("./MyApp-DW5WH4Ub.js").then((e=>e.aX));return{findDataObject:e}}),__vite__mapDeps([0,1,2]),import.meta.url),{edges:s=[],page_info:d={}}=r(o);return s.map((e=>{var a,i,t,l,n,o,r,s,c,p,u,m,v,h,g,f,j,x;return{id:null==(a=null==e?void 0:e.node)?void 0:a.id,pageId:null==(t=null==(i=null==e?void 0:e.node)?void 0:i.node)?void 0:t.id,pageName:null==(n=null==(l=null==e?void 0:e.node)?void 0:l.title)?void 0:n.text,place:null==(c=null==(s=null==(r=null==(o=null==e?void 0:e.node)?void 0:o.subtitle_text)?void 0:r.text)?void 0:s.split("\n"))?void 0:c[0],image:null==(u=null==(p=null==e?void 0:e.node)?void 0:p.image)?void 0:u.uri,url:(null==(m=null==e?void 0:e.node)?void 0:m.url)||(null==(h=null==(v=null==e?void 0:e.node)?void 0:v.node)?void 0:h.url),timeString:null==(x=null==(j=null==(f=null==(g=null==e?void 0:e.node)?void 0:g.subtitle_text)?void 0:f.text)?void 0:j.split("\n"))?void 0:x[1],cursor:(null==e?void 0:e.cursor)||(null==d?void 0:d.end_cursor)}}))}function y({target:e}){const{ti:y}=r(),{message:_}=a.useApp(),w=i(),b=v(),[I,S]=t.useState(!1),[N,C]=f("CheckedIns.data."+(null==e?void 0:e.id),[]),P=t.useMemo((()=>{const e=new Map,a=new Map;return N.forEach((i=>{e.has(i.place)||e.set(i.place,i),a.has(i.pageId)||a.set(i.pageId,i)})),{allPlaces:Array.from(e.values()),allPages:Array.from(a.values())}}),[N]);t.useEffect((()=>{var a;(null==e?void 0:e.id)&&A(!(null==(a=j("CheckedIns.data."+(null==e?void 0:e.id)))?void 0:a.length))}),[null==e?void 0:e.id]);const A=async(a=!1)=>{var i,t;if(!(null==e?void 0:e.id))return;const l=b.start();let n=[...a?[]:j("CheckedIns.data."+(null==e?void 0:e.id))||[]],o=(null==(i=n[n.length-1])?void 0:i.cursor)||"";s("CheckedIns:onReload"),S(!0);try{for(;!l.value();){const a=await k({uid:e.id,cursor:o});if(!(null==a?void 0:a.length))break;o=null==(t=a[a.length-1])?void 0:t.cursor,n=[...n,...a].map(((e,a)=>({...e,recent:a}))),C(n)}}catch(r){_.error({content:y({en:"Error: ",vi:"Lỗi: "})+r.message})}finally{C(n),S(!1)}},D=[{title:"#",dataIndex:"recent",key:"recent",render:(e,a,i)=>(a.recent||0)+1,sorter:(e,a)=>e.recent-a.recent,width:60},{title:y({en:"Cover",vi:"Ảnh"}),key:"cover",dataIndex:"image",render:(e,a,i)=>l.jsx(x,{src:a.image,style:{width:120,height:120,objectFit:"contain"}}),width:120},{title:P.allPages.length+" "+y({en:"Page",vi:"Trang"}),key:"name",dataIndex:"name",sorter:(e,a)=>e.pageName.localeCompare(a.pageName),render:(e,a,i)=>l.jsxs(d,{direction:"vertical",style:{maxWidth:250},children:[l.jsx(c.Link,{href:a.url,target:"_blank",children:l.jsx("b",{children:a.pageName})}),l.jsx("span",{style:{opacity:.6},children:a.pageId})]}),filters:P.allPages.map((e=>{let a=N.filter((a=>a.pageId===e.pageId)).length;return{count:a,value:e.pageId,text:e.pageName+" ("+a+")"}})).sort(((e,a)=>a.count-e.count)),onFilter:(e,a)=>a.pageId==e,filterSearch:!0},{title:P.allPlaces.length+" "+y({en:"Place",vi:"Địa điểm"}),key:"place",dataIndex:"place",sorter:(e,a)=>e.place.localeCompare(a.place),filters:P.allPlaces.map((e=>{let a=N.filter((a=>a.place===e.place)).length;return{count:a,value:e.place,text:e.place+" ("+a+")"}})).sort(((e,a)=>a.count-e.count)),onFilter:(e,a)=>a.place==e,filterSearch:!0,width:200},{title:y({en:"Time",vi:"Thời gian"}),key:"timeString",dataIndex:"timeString",sorter:(e,a)=>e.timeString.localeCompare(a.timeString),width:250},{title:y({en:"Actions",vi:"Hành động"}),key:"actions",render:(e,a,i)=>l.jsxs(d.Compact,{children:[l.jsx(p,{title:y({en:"Show map",vi:"Xem bản đồ"}),children:l.jsx(n,{type:"default",onClick:()=>{window.open("https://www.google.com/maps/search/"+a.pageName,"_blank")},icon:l.jsx("i",{className:"fa-solid fa-location-dot"})})}),l.jsx(p,{title:y({en:"Bulk Downloader",vi:"Tải hàng loạt"}),children:l.jsx(n,{type:"default",onClick:()=>{return e=a.pageId,void w("/bulk-downloader",{state:{targetId:e,platform:o.Facebook}});var e},icon:l.jsx("i",{className:"fa-solid fa-download"})})})]}),width:100,align:"end"}];return l.jsxs("div",{style:{display:"flex",flexDirection:"column",width:"100%",height:"100%"},children:[l.jsx(u,{type:"success",showIcon:!0,message:y({en:`Checked in ${N.length} times`,vi:`Đã check in ${N.length} lần`}),style:{alignSelf:"center",marginBottom:10}}),l.jsx(h,{columns:D,data:N,keyExtractor:e=>e.id,searchable:!0,pageSize:3,renderTitle:a=>l.jsxs(l.Fragment,{children:[l.jsx(n,{disabled:I,type:"primary",icon:I?l.jsx("i",{className:"fa-solid fa-rotate-right fa-spin"}):l.jsx("i",{className:"fa-solid fa-rotate-right"}),onClick:()=>A(!0),children:y({en:"Reload",vi:"Tải lại"})}),l.jsx(g,{data:a.length?a:N,options:[{key:"id",label:".txt (page id)",prepareData:a=>({fileName:(null==e?void 0:e.name)+"_check_in_id.txt",data:a.map((e=>e.pageId)).join("\n")})},{key:"id_name",label:".csv (page id+name)",prepareData:a=>({fileName:(null==e?void 0:e.name)+"_check_in_id_name.csv",data:m(a.map((e=>({page:e.pageId,name:e.pageName,place:e.place}))))})},{key:"json",label:".json",prepareData:a=>({fileName:(null==e?void 0:e.name)+"_check_in.json",data:JSON.stringify(a,null,4)})},{key:"csv",label:".csv",prepareData:a=>({fileName:(null==e?void 0:e.name)+"_check_in.csv",data:m(a)})}]}),l.jsx(n,{onClick:()=>{var a;(null==(a=null==e?void 0:e.url)?void 0:a.includes("?id="))?window.open((null==e?void 0:e.url)+"&sk=map","_blank"):window.open((null==e?void 0:e.url)+"/map","_blank")},icon:l.jsx("i",{className:"fa fa-external-link-alt"}),children:y({vi:"Xem trên Facebook",en:"View on Facebook"})},"view-fb")]})})]})}export{y as default};
