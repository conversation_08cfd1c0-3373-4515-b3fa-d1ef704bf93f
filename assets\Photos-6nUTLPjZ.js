const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./ImageLazyPreview-D93UqUk2.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./useCacheState-CAnxkewm.js","./MyApp-DW5WH4Ub.js","./index-CDSnY0KE.js","./EyeOutlined-CNKPohhw.js","./addEventListener-C4tIKh7N.js"])))=>i.map(i=>d[i]);
import{r as i,b0 as e,b1 as t,b5 as o,aN as s}from"./index-Cak6rALw.js";import r from"./Collection-BYcChfHL.js";import{u as a,d as n,z as l,h as d}from"./MyApp-DW5WH4Ub.js";import{PhotoCollectionType as m,getGroupPhotos as p,getUserPhotos as u,PhotoCollectionTypeLang as c,getLargestPhoto as j}from"./photos-CDfkOCs1.js";import{S as h}from"./index-C5gzSBcY.js";import{L as g}from"./index-jrOnBmdW.js";import"./index-ByRdMNW-.js";import"./useCacheState-CAnxkewm.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-PbLYNhdQ.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CC5caqt_.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";const v=t((()=>s((()=>import("./ImageLazyPreview-D93UqUk2.js")),__vite__mapDeps([0,1,2,3,4,5,6,7]),import.meta.url)));function f({target:t}){const{ti:s}=a(),{message:f}=n(),[x,b]=i.useState(m.OWNER);i.useEffect((()=>{(null==t?void 0:t.id)&&b(m.OWNER)}),[null==t?void 0:t.id]);const y=i.useCallback((async(i,e)=>{var o,s;if((null==t?void 0:t.id)&&(null==t?void 0:t.type)){if(t.type===l.Group){const s=e||(null==(o=null==i?void 0:i[(null==i?void 0:i.length)-1])?void 0:o.cursor)||"";return await p({id:t.id,cursor:s})}{const o=null==i?void 0:i.filter((i=>i.photo_type===x)),r=e||(null==(s=null==o?void 0:o[(null==o?void 0:o.length)-1])?void 0:s.cursor)||"";return await u({id:t.id,cursor:r,type:x})}}}),[t,x]),w=i.useCallback(((i,o)=>(null==t?void 0:t.type)!==l.Group&&e.jsx(h,{disabled:o,value:x,onChange:b,popupMatchSelectWidth:!1,children:Object.entries(m).map((([i,t])=>e.jsx(h.Option,{value:t,children:s(c[t])},i)))})),[x,s]),k=i.useCallback((i=>e.jsxs(g.Item,{className:"show-on-hover-trigger",children:[e.jsx(v,{src:i.thumbnail,alt:i.accessibility_caption,width:150,height:150,style:{objectFit:"cover",borderRadius:"10px"},cacheId:"photos."+i.id,getPreview:()=>i.image||j(i.id).then((i=>i.image))}),e.jsx(o,{type:"default",icon:e.jsx("i",{className:"fa-solid fa-up-right-from-square"}),style:{position:"absolute",bottom:10,right:10},className:"show-on-hover-item",target:"_blank",href:d(i.id)})]},i.id)),[]),C=i.useCallback((async(i,e)=>{if(!i.image&&(null==t?void 0:t.type)===l.Group){const e=await j(i.id);e.image||f.error(s({en:"Cannot get largest photo",vi:"Lỗi lấy ảnh chất lượng cao"})),i.image=e.image||e.thumbnail||i.thumbnail}return{url:i.image,name:i.id+".jpg"}}),[null==t?void 0:t.id]);return e.jsx(r,{collectionName:(null==t?void 0:t.name)+" - Photos - "+s(c[x]),fetchNext:y,renderItem:k,downloadItem:C,headerButtons:w,getItemCursor:i=>i.cursor||"",rowKey:i=>i.id})}export{f as default};
