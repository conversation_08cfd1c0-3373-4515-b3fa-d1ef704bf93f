import{r as o,b0 as i}from"./index-Cak6rALw.js";import n from"./Collection-BYcChfHL.js";import{y as e,x as l,u as r,S as d,T as t,B as s,c as a,s as u}from"./MyApp-DW5WH4Ub.js";import{L as m}from"./index-jrOnBmdW.js";import{I as p}from"./index-CDSnY0KE.js";import"./index-ByRdMNW-.js";import"./useCacheState-CAnxkewm.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-PbLYNhdQ.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CC5caqt_.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./addEventListener-C4tIKh7N.js";function c({target:c}){const{ti:v}=r(),j=o.useCallback((async(o=[],i)=>{if(!(null==c?void 0:c.id)||!(null==c?void 0:c.type))return;const n=null==o?void 0:o[(null==o?void 0:o.length)-1];return await async function({groupId:o="",cursor:i=""}){var n,r;const d=await e({fb_api_req_friendly_name:"GroupsCometFilesTabPaginationQuery",variables:{count:15,cursor:i,groupDocsFileName:null,groupID:o,orderby:"TIME_DESC",scale:1,id:o},doc_id:"24326962373618252"}),t=l(d),{edges:s=[],page_info:a={}}=(null==(r=null==(n=null==t?void 0:t.data)?void 0:n.node)?void 0:r.group_docs_and_files)||{};return s.map((o=>{var i,n,e,l,r,d,t,s,u,m,p;return{download_url:null==(i=null==o?void 0:o.node)?void 0:i.download_url,name:null==(n=null==o?void 0:o.node)?void 0:n.name,post_url:null==(l=null==(e=null==o?void 0:o.node)?void 0:e.original_post)?void 0:l.url,creation_time:null==(d=null==(r=null==o?void 0:o.node)?void 0:r.original_post)?void 0:d.creation_time,icon:null==(s=null==(t=null==o?void 0:o.node)?void 0:t.icon)?void 0:s.uri,file_type:null==(u=null==o?void 0:o.node)?void 0:u.file_type,owners:null==(p=null==(m=null==o?void 0:o.node)?void 0:m.original_post)?void 0:p.actors.map((o=>({id:null==o?void 0:o.id,name:null==o?void 0:o.name,url:null==o?void 0:o.url}))),cursor:null==a?void 0:a.end_cursor}}))}({groupId:null==c?void 0:c.id,cursor:i||(null==n?void 0:n.cursor)||""})}),[c]),x=o.useCallback((o=>i.jsx(m.Item,{children:i.jsxs(d,{direction:"vertical",align:"center",style:{minWidth:150},children:[i.jsx(t,{title:o.download_url?v({en:"Download",vi:"Tải xuống"}):v({en:"No download link",vi:"Không có link tải"}),children:i.jsx(d,{style:{padding:"10px"},children:i.jsx(s.Ribbon,{text:i.jsx("i",{className:"fa fa-times"}),color:"red",style:{display:o.download_url?"none":"block"},children:i.jsx(p,{src:o.icon,alt:o.name,width:70,height:70,style:{objectFit:"cover",borderRadius:"10px",cursor:"pointer"},preview:!1,onClick:()=>o.download_url&&window.open(o.download_url)})})})}),i.jsx(t,{title:v({en:"View post",vi:"Xem bài viết"}),placement:"bottom",children:i.jsx(a.Paragraph,{style:{maxWidth:"150px",wordWrap:"break-word"},onClick:()=>window.open(o.post_url),children:o.name})})]})})),[]),_=o.useCallback((async o=>({url:o.download_url,name:o.name})),[]);return i.jsx(n,{collectionName:(null==c?void 0:c.name)+" - Files",fetchNext:j,renderItem:x,downloadItem:_,getItemCursor:o=>o.cursor||"",rowKey:o=>o.name+o.creation_time,onSearch:(o,i)=>u(o,i.name)})}export{c as default};
