import{bd as e,aJ as n}from"./index-Cak6rALw.js";function t(e,n){for(var t=0;t<n.length;t++){const r=n[t];if("string"!=typeof r&&!Array.isArray(r))for(const n in r)if("default"!==n&&!(n in e)){const t=Object.getOwnPropertyDescriptor(r,n);t&&Object.defineProperty(e,n,t.get?t:{enumerable:!0,get:()=>r[n]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var r,a={};var i=function(){if(r)return a;r=1,Object.defineProperty(a,"__esModule",{value:!0});var n=e();function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var i=t(n);!function(e){if("undefined"==typeof window)return;const n=document.createElement("style");n.setAttribute("type","text/css"),n.innerHTML=e,document.head.appendChild(n)}('.rfm-marquee-container {\n  overflow-x: hidden;\n  display: flex;\n  flex-direction: row;\n  position: relative;\n  width: var(--width);\n  transform: var(--transform);\n}\n.rfm-marquee-container:hover div {\n  animation-play-state: var(--pause-on-hover);\n}\n.rfm-marquee-container:active div {\n  animation-play-state: var(--pause-on-click);\n}\n\n.rfm-overlay {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n}\n.rfm-overlay::before, .rfm-overlay::after {\n  background: linear-gradient(to right, var(--gradient-color), rgba(255, 255, 255, 0));\n  content: "";\n  height: 100%;\n  position: absolute;\n  width: var(--gradient-width);\n  z-index: 2;\n  pointer-events: none;\n  touch-action: none;\n}\n.rfm-overlay::after {\n  right: 0;\n  top: 0;\n  transform: rotateZ(180deg);\n}\n.rfm-overlay::before {\n  left: 0;\n  top: 0;\n}\n\n.rfm-marquee {\n  flex: 0 0 auto;\n  min-width: var(--min-width);\n  z-index: 1;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  animation: scroll var(--duration) linear var(--delay) var(--iteration-count);\n  animation-play-state: var(--play);\n  animation-delay: var(--delay);\n  animation-direction: var(--direction);\n}\n@keyframes scroll {\n  0% {\n    transform: translateX(0%);\n  }\n  100% {\n    transform: translateX(-100%);\n  }\n}\n\n.rfm-initial-child-container {\n  flex: 0 0 auto;\n  display: flex;\n  min-width: auto;\n  flex-direction: row;\n  align-items: center;\n}\n\n.rfm-child {\n  transform: var(--transform);\n}');const o=n.forwardRef((function({style:e={},className:t="",autoFill:r=!1,play:a=!0,pauseOnHover:o=!1,pauseOnClick:l=!1,direction:s="left",speed:d=50,delay:u=0,loop:c=0,gradient:f=!1,gradientColor:m="white",gradientWidth:p=200,onFinish:v,onCycleComplete:y,onMount:h,children:g},w){const[b,x]=n.useState(0),[E,C]=n.useState(0),[O,M]=n.useState(1),[j,N]=n.useState(!1),k=n.useRef(null),q=w||k,A=n.useRef(null),R=n.useCallback((()=>{if(A.current&&q.current){const e=q.current.getBoundingClientRect(),n=A.current.getBoundingClientRect();let t=e.width,a=n.width;"up"!==s&&"down"!==s||(t=e.height,a=n.height),M(r&&t&&a&&a<t?Math.ceil(t/a):1),x(t),C(a)}}),[r,q,s]);n.useEffect((()=>{if(j&&(R(),A.current&&q.current)){const e=new ResizeObserver((()=>R()));return e.observe(q.current),e.observe(A.current),()=>{e&&e.disconnect()}}}),[R,q,j]),n.useEffect((()=>{R()}),[R,g]),n.useEffect((()=>{N(!0)}),[]),n.useEffect((()=>{"function"==typeof h&&h()}),[]);const S=n.useMemo((()=>r?E*O/d:E<b?b/d:E/d),[r,b,E,O,d]),_=n.useMemo((()=>Object.assign(Object.assign({},e),{"--pause-on-hover":!a||o?"paused":"running","--pause-on-click":!a||o&&!l||l?"paused":"running","--width":"up"===s||"down"===s?"100vh":"100%","--transform":"up"===s?"rotate(-90deg)":"down"===s?"rotate(90deg)":"none"})),[e,a,o,l,s]),z=n.useMemo((()=>({"--gradient-color":m,"--gradient-width":"number"==typeof p?`${p}px`:p})),[m,p]),F=n.useMemo((()=>({"--play":a?"running":"paused","--direction":"left"===s?"normal":"reverse","--duration":`${S}s`,"--delay":`${u}s`,"--iteration-count":c?`${c}`:"infinite","--min-width":r?"auto":"100%"})),[a,s,S,u,c,r]),P=n.useMemo((()=>({"--transform":"up"===s?"rotate(90deg)":"down"===s?"rotate(-90deg)":"none"})),[s]),$=n.useCallback((e=>[...Array(Number.isFinite(e)&&e>=0?e:0)].map(((e,t)=>i.default.createElement(n.Fragment,{key:t},n.Children.map(g,(e=>i.default.createElement("div",{style:P,className:"rfm-child"},e))))))),[P,g]);return j?i.default.createElement("div",{ref:q,style:_,className:"rfm-marquee-container "+t},f&&i.default.createElement("div",{style:z,className:"rfm-overlay"}),i.default.createElement("div",{className:"rfm-marquee",style:F,onAnimationIteration:y,onAnimationEnd:v},i.default.createElement("div",{className:"rfm-initial-child-container",ref:A},n.Children.map(g,(e=>i.default.createElement("div",{style:P,className:"rfm-child"},e)))),$(O-1)),i.default.createElement("div",{className:"rfm-marquee",style:F},$(O))):null}));return a.default=o,a}();const o=t({__proto__:null,default:n(i)},[i]);export{o as i};
