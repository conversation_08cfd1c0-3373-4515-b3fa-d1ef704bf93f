import{aJ as e}from"./index-Cak6rALw.js";var r,n,t,o,a,i={exports:{}};function p(){if(n)return r;n=1;return r="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}function s(){if(o)return t;o=1;var e=p();function r(){}function n(){}return n.resetWarningCache=r,t=function(){function t(r,n,t,o,a,i){if(i!==e){var p=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw p.name="Invariant Violation",p}}function o(){return t}t.isRequired=t;var a={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:o,element:t,elementType:t,instanceOf:o,node:t,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:n,resetWarningCache:r};return a.PropTypes=a,a}}function c(){return a||(a=1,i.exports=s()()),i.exports}const u=e(c());export{u as _,c as r};
