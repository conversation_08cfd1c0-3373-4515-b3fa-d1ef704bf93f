import{r as e,aP as t,b0 as a,b5 as r}from"./index-Cak6rALw.js";import{d as i,s as n,S as s,i as l,c as o,h as d,e as c,b as u,T as m,f,p as x,j}from"./MyApp-DW5WH4Ub.js";import h from"./ProfileCard-Cinxbxq-.js";import{getUidFromUrl as p}from"./getIds-DAzc-wzf.js";import{d as v}from"./dayjs.min-CzPn7FMI.js";import g from"./MyTable-DnjDmN7z.js";import{I as y}from"./index-CDSnY0KE.js";import{C as w}from"./index-D7dTP8lW.js";import{I as C}from"./index-Bg865k-U.js";import{D as I}from"./index-DwLJmsHL.js";import"./index-ByRdMNW-.js";import"./index-BzMPigdO.js";import"./DownOutlined-B-JcS-29.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./row-BMfM-of4.js";import"./EyeOutlined-CNKPohhw.js";import"./index-IxNfZAD-.js";import"./ClockCircleOutlined-BVovxNRp.js";const k="fbaio-dev-pass";function S(){const{message:S}=i(),[b,P]=e.useState(""),[D,T]=e.useState(!1),[N,R]=e.useState(null),[L,E]=e.useState(""),[V,M]=e.useState(""),[U,$]=e.useState(v().add(3,"day")),[F,O]=e.useState(""),[A,B]=e.useState(localStorage.getItem(k)||""),[Y,_]=e.useState({}),[z,W]=e.useState(!1),[q,G]=e.useState([]),H=e.useRef(null);e.useEffect((()=>{localStorage.setItem(k,A)}),[A]),e.useEffect((()=>{localStorage.getItem(k)&&Q()}),[]);const J=e.useMemo((()=>{const e=q.filter((e=>{var t;return null==(t=e.name)?void 0:t.startsWith("referral from ")}));return q.map((t=>({...t,profile:Y[t.uid],referralCount:e.filter((e=>e.name=="referral from "+t.uid)).length})))}),[q,Y]),K=async e=>{if(e){T(!0);try{const t=await p(e);if(!t)throw new Error("Cannot get UID from URL");const a=await j(t);if(!a)throw new Error("Cannot get profile from UID: "+t);R(a),E(t),M(a.name)}catch(t){S.error(t.message)}finally{T(!1)}}},Q=async()=>{const e="onPressLoad";S.loading({key:e,content:"Loading...",duration:0}),W(!0);try{const a=await fetch(t.FB_AIO.server+"/allVIP?password="+A),r=await a.json(),i=(new Date).getTime(),n=r.map(((e,t)=>{const a=parseInt(null==e?void 0:e[1]);return{index:t+1,uid:null==e?void 0:e[0],time:a,inVIP:a>i,name:null==e?void 0:e[2],referralCount:0}})).filter((e=>e.uid)).reverse();console.log(n),G(n),S.success({key:e,content:"Load success"})}catch(a){S.error({key:e,content:a.message})}finally{W(!1)}},X=[{title:"#",dataIndex:"index",key:"index",sorter:(e,t)=>e.index-t.index,width:80},{title:"User",dataIndex:"uid",key:"uid",render:(e,t,r)=>a.jsxs(s,{children:[a.jsx(y,{src:l(t.uid,50),width:50,height:50,preview:{src:l(t.uid)}}),a.jsxs(s,{size:0,direction:"vertical",children:[a.jsx(o.Link,{href:d(t.uid),target:"_blank",strong:!0,children:a.jsx("b",{children:t.name})}),t.profile&&a.jsxs(o.Text,{children:["(",t.profile.name,")"]}),a.jsx(o.Text,{type:"secondary",children:t.uid})]})]}),filters:[{text:`Người chia sẻ (${q.filter((e=>{var t;return null==(t=e.name)?void 0:t.includes("referral of")})).length})`,value:"referral of"},{text:`Người được chia sẻ (${q.filter((e=>{var t;return null==(t=e.name)?void 0:t.includes("referral from")})).length})`,value:"referral from"},{text:`review (${q.filter((e=>{var t;return null==(t=e.name)?void 0:t.includes("review")})).length})`,value:"review"}],onFilter:(e,t)=>n(e,t.name)},{title:"Expire time",dataIndex:"inVIP",key:"inVIP",render:(e,t,r)=>{const i=new Date(parseInt(t.time)),n=c(i.getTime());return a.jsxs(s,{direction:"vertical",children:[a.jsx(u,{color:t.inVIP?"success":"error",children:a.jsxs(o.Text,{children:[t.inVIP?"":"-",n]})}),a.jsxs(o.Text,{type:"secondary",children:[t.time," - ",i.toLocaleString()]})]})},filters:[{text:`✅ ${q.filter((e=>e.inVIP)).length}`,value:!0},{text:`❌ ${q.filter((e=>!e.inVIP)).length}`,value:!1}],onFilter:(e,t)=>t.inVIP===e,sorter:(e,t)=>(parseInt(e.time)||0)-(parseInt(t.time)||0)},{title:"Ref count",dataIndex:"referralCount",key:"referralCount",sorter:(e,t)=>e.referralCount-t.referralCount,width:100},{title:"Action",dataIndex:"action",key:"action",render:(e,t,i)=>a.jsxs(s.Compact,{children:[a.jsx(m,{title:"Update",children:a.jsx(r,{icon:a.jsx("i",{className:"fa-solid fa-pen"}),onClick:()=>(e=>{P(e.uid),K(e.uid),$(v(Number(e.time)))})(t)})}),t.referralCount>0&&a.jsx(m,{title:"Show ref",children:a.jsx(r,{icon:a.jsx("i",{className:"fa-solid fa-users"}),onClick:()=>(e=>{var t;null==(t=H.current)||t.setSearch(e.uid)})(t)})}),!t.profile&&a.jsx(m,{title:"Reveal",children:a.jsx(r,{icon:a.jsx("i",{className:"fa-solid fa-eye"}),onClick:()=>(async e=>{const t="onPressReveal:"+e.uid;S.loading({key:t,content:"Revealing... "+e.name,duration:0});try{const a=await j(e.uid);if(!a)throw new Error("Cannot reveal: "+e.name);_((t=>({...t,[e.uid]:a}))),S.success({key:t,content:"Revealed: "+a.name})}catch(a){S.error({key:t,content:"Cannot reveal: "+e.name})}})(t)})})]})}],Z=q.find((e=>e.uid==L));return a.jsxs("div",{children:[a.jsx("h1",{children:"Dev"}),a.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column"},children:[a.jsx(w,{style:{maxWidth:400},children:a.jsxs(s,{direction:"vertical",children:[a.jsx(C.Search,{value:b,onChange:e=>{const t=e.target.value;P(t),K(t)},onSearch:K,placeholder:"Enter FB URL",prefix:a.jsx("i",{className:"fa-brands fa-facebook"}),loading:D}),N&&a.jsx(h,{about:N,onClose:()=>{R(null)}}),a.jsx(C,{value:L,onChange:e=>E(e.target.value),placeholder:"UID",prefix:a.jsx("i",{className:"fa-solid fa-hashtag"}),allowClear:!0}),Z&&a.jsx(o.Text,{type:"secondary",children:Z.name}),a.jsx(C,{value:V,onChange:e=>M(e.target.value),placeholder:"NAME",prefix:a.jsx("i",{className:"fa-solid fa-user"}),allowClear:!0}),Z&&a.jsxs(o.Text,{type:"secondary",children:[f(Z.time)," (",Z.time<U.toDate().getTime()?"ít hơn ":"nhiều hơn ",x(Math.abs(Z.time-U.toDate().getTime())),")"]}),a.jsxs(s.Compact,{children:[a.jsx(I,{defaultValue:v(),format:"YYYY-MM-DD",value:U,onChange:$,prefix:a.jsx("i",{className:"fa-regular fa-calendar"}),allowClear:!1}),[{value:5,label:"5d"},{value:30,label:"1m"},{value:Math.floor(182.5),label:"6m"},{value:365,label:"1y"}].map((({value:e,label:t})=>a.jsx(r,{onClick:()=>{$(v().add(e,"day"))},children:t},e)))]}),a.jsxs(s.Compact,{children:[a.jsx(C,{value:F,onChange:e=>O(e.target.value),placeholder:"Reason",prefix:a.jsx("i",{className:"fa-solid fa-question"}),allowClear:!0}),["review","phunt","65k","3$"].map((e=>a.jsx(r,{style:{paddingLeft:5,paddingRight:5},onClick:()=>{O(e)},children:e},e)))]}),a.jsx(C.Password,{placeholder:"Password",onChange:e=>B(e.target.value),value:A,prefix:a.jsx("i",{className:"fa-solid fa-lock"}),allowClear:!0}),a.jsx(s,{style:{width:"100%",justifyContent:"flex-end"},children:a.jsx(r,{type:"primary",onClick:async()=>{const e="submit";S.loading({key:e,content:"Submitting...",duration:0});try{const a={uid:L,name:V+(F?` (${F})`:""),expireTime:U.toDate().getTime()+"",password:A},r=await fetch(t.FB_AIO.server+"/addVIP?"+new URLSearchParams(a)),i=await r.text();console.log(i),S.info({key:e,content:i})}catch(a){S.error({key:e,content:a.message})}},disabled:!V||!L||!A,children:"Submit"})})]})}),a.jsx("hr",{}),a.jsx(g,{ref:H,columns:X,data:J,searchable:!0,virtual:!0,pageSize:5,keyExtractor:e=>""+(null==e?void 0:e.uid),onSearchRow:(e,t)=>{var a;return n(e,(null==t?void 0:t.name)+(null==t?void 0:t.uid)+(null==(a=null==t?void 0:t.profile)?void 0:a.name))},renderTitle:()=>a.jsx(r,{onClick:Q,disabled:!A,loading:z,icon:a.jsx("i",{className:"fa-solid fa-sync"}),children:"Reload"})})]})]})}export{S as default};
