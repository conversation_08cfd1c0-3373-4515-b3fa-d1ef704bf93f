import{r as e,b0 as i,b5 as n}from"./index-Cak6rALw.js";import{u as t,d as o,t as l,S as r,i as a,h as d,l as s,e as m,f as u,T as v,o as c,b as j}from"./MyApp-DW5WH4Ub.js";import{g as p}from"./tagged-BWZdodWv.js";import g from"./useCacheState-CAnxkewm.js";import f from"./MyTable-DnjDmN7z.js";import{E as h}from"./ExportButton-CbyepAf_.js";import{checkVIP as x}from"./useVIP-D5FAMGQQ.js";import{I as y}from"./index-CDSnY0KE.js";import"./index-ByRdMNW-.js";import"./index-BzMPigdO.js";import"./DownOutlined-B-JcS-29.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./row-BMfM-of4.js";import"./sweetalert2.esm.all-BZxvatOx.js";import"./getIds-DAzc-wzf.js";import"./index-IxNfZAD-.js";function b({target:b}){const{ti:k}=t(),{message:w}=o(),[T,N]=g("Tagged."+(null==b?void 0:b.id),[]),[C,I]=g("Tagged.loading."+(null==b?void 0:b.id),!1),[_,L]=g("Tagged.loadMore."+(null==b?void 0:b.id),!1),[S,A]=e.useState(!0);e.useEffect((()=>{E()}),[null==b?void 0:b.id]);const D=e.useRef(null==b?void 0:b.id),E=(e=!1)=>{if(!(null==b?void 0:b.id)||C)return;const i=D.current!==(null==b?void 0:b.id);(e||!T.length||i)&&(D.current=null==b?void 0:b.id,l("Tagged:onClickReload"),I(!0),p(b.id).then((e=>{(null==e?void 0:e.length)?(console.log(e),N(e),A(!0)):(w.info(k({en:"No data: Comments Tagged",vi:"Không có dữ liệu: Lượt tag bình luận"})),A(!1))})).finally((()=>{I(!1)})))},M=i.jsx(n,{icon:i.jsx("i",{className:"fa-solid fa-play"}),loading:_,disabled:!S||C,onClick:async()=>{var e;(null==b?void 0:b.id)&&!_&&await x()&&(l("Tagged:onClickLoadMore"),L(!0),p(b.id,null==(e=T[T.length-1])?void 0:e.cursor).then((e=>{const i=new Set(T.map((e=>e.id))),n=e.filter((e=>!i.has(e.id)));n.length?(N((e=>[...e,...n])),A(!0)):(w.info(k({en:"No more data",vi:"Không còn dữ liệu"})),A(!1))})).finally((()=>L(!1))))},children:k(S?{en:"Load more",vi:"Tải thêm"}:{en:"No more data",vi:"Không còn dữ liệu"})}),O=[{title:"#",key:"index",dataIndex:"index"},{title:k({en:"Owner",vi:"Người tag"}),key:"from",dataIndex:"from",render:(e,n,t)=>{var o,l,s,m,u;return(null==(o=null==n?void 0:n.from)?void 0:o.id)?i.jsxs(r,{children:[i.jsx(y,{src:a(null==(l=null==n?void 0:n.from)?void 0:l.id),style:{width:45,height:45,borderRadius:5}}),i.jsxs(r,{direction:"vertical",size:0,children:[i.jsx("a",{href:d(null==(s=null==n?void 0:n.from)?void 0:s.id),target:"_blank",rel:"noreferrer",children:i.jsx("b",{children:null==(m=null==n?void 0:n.from)?void 0:m.name})}),i.jsx("span",{style:{opacity:.5},children:null==(u=null==n?void 0:n.from)?void 0:u.id})]})]}):k({en:"Anonymous",vi:"Ẩn danh"})},filters:Array.from(new Set(null==T?void 0:T.map((e=>{var i;return null==(i=e.from)?void 0:i.id})))).map((e=>{var i,n;const t=T.filter((i=>{var n;return(null==(n=i.from)?void 0:n.id)===e}));return{value:e||"",text:((null==(n=null==(i=null==t?void 0:t[0])?void 0:i.from)?void 0:n.name)||k({en:"Anonymous",vi:"Ẩn danh"}))+` (${null==t?void 0:t.length})`,count:null==t?void 0:t.length}})).sort(((e,i)=>i.count-e.count)),onFilter:(e,i)=>{var n;return(null==(n=i.from)?void 0:n.id)==e},width:300},{title:k({en:"Content",vi:"Nội dung"}),key:"message",dataIndex:"message",render:(e,n,t)=>{var o;return i.jsx("div",{style:{wordBreak:"break-word",maxWidth:400},children:(null==(o=s(null==n?void 0:n.message,200))?void 0:o.replace(null==b?void 0:b.name,""))||(null==b?void 0:b.name)})}},{title:k({en:"Created at",vi:"Thời gian"}),key:"created_time",dataIndex:"created_time",render:(e,n,t)=>i.jsxs("span",{children:[m(new Date(e).getTime())," - ",u(new Date(e))]})},{title:k({en:"Action",vi:"Hành động"}),key:"action",dataIndex:"action",render:(e,t,o)=>i.jsx(v,{title:k({en:"View post",vi:"Xem bài viết"}),children:i.jsx(n,{target:"_blank",href:d(null==t?void 0:t.id),icon:i.jsx("i",{className:"fa-solid fa-external-link"})})}),width:100}];return i.jsx(f,{data:T.map(((e,i)=>({...e,index:i+1}))),columns:O,searchable:!0,keyExtractor:e=>e.id,size:"small",renderTitle:()=>i.jsxs(i.Fragment,{children:[i.jsxs(r.Compact,{children:[i.jsx(n,{type:"primary",icon:i.jsx("i",{className:"fa-solid fa-rotate-right"}),loading:C,disabled:_,onClick:()=>E(!0),children:k({en:"Reload",vi:"Tải lại"})}),M]}),i.jsx(h,{data:T,options:[{key:"json",label:".json",prepareData:e=>({fileName:(null==b?void 0:b.name)+"_tagged_comment.json",data:JSON.stringify(e,null,4)})},{key:"csv",label:".csv",prepareData:e=>({fileName:(null==b?void 0:b.name)+"_tagged_comment.csv",data:c(e.map((e=>{var i,n;return{...e,from:null==(i=e.from)?void 0:i.id,fromName:null==(n=e.from)?void 0:n.name}})))})}]}),i.jsx(j,{color:"red",children:(null==T?void 0:T.length)+" "+k({en:"Tagged Comment",vi:"Lượt tag bình luận"})})]}),footer:i.jsx("div",{style:{width:"100%",display:"flex",justifyContent:"center"},children:M})})}export{b as default};
