const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./MyApp-DW5WH4Ub.js","./index-Cak6rALw.js","./index-DIX6R8z-.css"])))=>i.map(i=>d[i]);
import{aN as n,aM as t}from"./index-Cak6rALw.js";import{x as r,z as l,y as a}from"./MyApp-DW5WH4Ub.js";const e={cfrsToken:null,fb_dtsg:null};async function o(){var n;if(!e.fb_dtsg)try{const l=await t("https://www.threads.com");console.log(l);const a=(new DOMParser).parseFromString(l,"text/html");e.fb_dtsg=r((null==(n=null==a?void 0:a.querySelector("#__eqmc"))?void 0:n.textContent)||"{}").f}catch(l){console.log(l)}return e.fb_dtsg}async function i(t={},r="https://www.threads.com/api/graphql"){const l=await o();if(!l){const{trans:t}=await n((async()=>{const{trans:n}=await import("./MyApp-DW5WH4Ub.js").then((n=>n.aW));return{trans:n}}),__vite__mapDeps([0,1,2]),import.meta.url);throw new Error(t({en:"Cannot find dtsg token",vi:"Không lấy được dtsg token"}))}return a(t,r,!0,{fb_dtsg:l})}function s(n=""){var t,r,l;return(null==(t=/www.threads.net\/@([^\/]+)/.exec(n))?void 0:t[1])||(null==(r=/threads.com\/@([^\/]+)/.exec(n))?void 0:r[1])||(null==(l=/instagram.com\/([^\/]+)/.exec(n))?void 0:l[1])}async function d(n=""){var t,a,e,o,s,d,c,v,_,h,m,p,w,f;const g=await i({fb_api_req_friendly_name:"BarcelonaUsernameHoverCardImplQuery",variables:{username:n,__relay_internal__pv__BarcelonaShouldShowFediverseM075Featuresrelayprovider:!1},doc_id:"7679337195500348"}),y=r(g);return console.log(y),{id:null==(a=null==(t=null==y?void 0:y.data)?void 0:t.user)?void 0:a.id,name:null==(o=null==(e=null==y?void 0:y.data)?void 0:e.user)?void 0:o.full_name,username:null==(d=null==(s=null==y?void 0:y.data)?void 0:s.user)?void 0:d.username,avatar:null==(v=null==(c=null==y?void 0:y.data)?void 0:c.user)?void 0:v.profile_pic_url,avatarBig:null==(f=null==(w=null==(p=null==(m=null==(h=null==(_=null==y?void 0:y.data)?void 0:_.user)?void 0:h.hd_profile_pic_versions)?void 0:m.sort)?void 0:p.call(m,((n,t)=>t.width*t.height-n.width*n.height)))?void 0:w[0])?void 0:f.url,type:l.ThreadsUser,url:u("@"+n),raw:y}}function u(n=""){return`https://www.threads.com/${n}`}export{i as fetchThreadsGraphQl,o as getThreadsFbDtsg,u as getThreadsUrlFromId,d as getThreadsUserInfo,s as getThreadsUsernameFromURL};
