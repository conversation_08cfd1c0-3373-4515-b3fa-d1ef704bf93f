import{y as o,x as n,az as e,h as i,W as l,X as r,i as t}from"./MyApp-DW5WH4Ub.js";import{a}from"./gender-D7cqwEK5.js";import"./index-Cak6rALw.js";async function d({myUid:l,targetUid:r=""}){const t=await o({doc_id:"4936483286421335",variables:{id:r||l,query:"",scale:1}});try{const o=n(t||"{}"),{edges:l=[]}=e(o,!1);return l.map((o=>{var n,e,l,r,t,a;return{uid:null==(n=o.node)?void 0:n.id,url:(null==(e=o.node)?void 0:e.url)||i(null==(l=o.node)?void 0:l.id),name:null==(r=o.node)?void 0:r.name,avatar:null==(a=null==(t=o.node)?void 0:t.photo)?void 0:a.uri}}))}catch(a){return[]}}async function u({myUid:e="",targetUid:i}){var r;const t=await o({doc_id:"8752443744796374",variables:{input:{source:"friending_jewel",unfriended_user_id:i,actor_id:e||await l()},scale:1}}),a=n(t||"{}");if(null==(r=a.errors)?void 0:r.length)throw new Error(a.errors[0].message);return a}async function s({myUid:e=null,targetUid:i}){var r;const t=await o({doc_id:"6294978773852692",variables:{input:{attribution_id_v2:"FriendingCometRoot.react,comet.friending,tap_tabbar,1667288605315,127814,2356318349,",friend_requestee_ids:[i],people_you_may_know_location:"friends_center",refs:[null],source:"people_you_may_know",warn_ack_for_ids:[],actor_id:e||await l()},scale:2}}),a=n(t);if(null==(r=a.errors)?void 0:r.length)throw new Error(a.errors[0].message);return a}async function c({myUid:e=null,targetUid:i}){var r,t,a,d,u,s,c;const v=await o({doc_id:"5028133957233114",variables:{input:{actor_id:e||await l(),user_id:i}}}),_=n(v||"{}");if(null==(r=_.errors)?void 0:r.length)throw new Error(_.errors[0].message);if("PENDING"!=(null==(d=null==(a=null==(t=null==_?void 0:_.data)?void 0:t.user_poke)?void 0:a.user)?void 0:d.poke_status))throw new Error("Failed to poke friend: "+(null==(c=null==(s=null==(u=null==_?void 0:_.data)?void 0:u.user_poke)?void 0:s.user)?void 0:c.poke_status));return _}var v=(o=>(o.NameContainsSpecialCharacters="Name contains special characters",o.NameContainsNumbers="Name contains numbers",o.StrangeUid="Strange uid",o.NoAvatar="No avatar",o.Locked="Locked",o.BlockedMessages="Blocked messages",o.CurrentCity="Current city",o.Hometown="Hometown",o.Highschool="Highschool",o.Work="Work",o.Following="Following",o.Follower="Follower",o))(v||{});const _={"Name contains special characters":{en:"👽 Special characters",vi:"👽 Ký tự đặc biệt",color:"orange"},"Name contains numbers":{en:"🔟 Contains numbers",vi:"🔟 Chứa số",color:"purple"},"No avatar":{en:"👤 No avatar",vi:"👤 Không ảnh đại diện",color:"cyan"},"Strange uid":{en:"🆔 Strange UID",vi:"🆔 UID lạ",color:"blue"},Locked:{en:"🔒 Locked",vi:"🔒 Bị khoá",color:"red"},"Blocked messages":{en:"🚫 Blocked messages",vi:"🚫 Chặn tin nhắn",color:"red"},"Current city":{en:"🏙️ Current city",vi:"🏙️ Cùng tỉnh thành",color:"green"},Hometown:{en:"🏡 Hometown",vi:"🏡 Cùng quê",color:"green"},Highschool:{en:"📚 High school",vi:"📚 Cùng trung học",color:"green"},Work:{en:"💼 Work",vi:"💼 Cùng công ty",color:"green"},Following:{en:"👉 Following",vi:"👉 Đang theo dõi",color:"green"},Follower:{en:"👥 Follower",vi:"👥 Người theo dõi",color:"green"}};var m=(o=>(o[o.friends=2]="friends",o[o.recent_added=1]="recent_added",o[o.birthdays=123]="birthdays",o[o.works=56]="works",o[o.high_school=54]="high_school",o[o.current_city=124]="current_city",o[o.hometown=125]="hometown",o[o.followers=32]="followers",o[o.following=33]="following",o))(m||{});async function g({uid:i=null,cursor:r=null,section:t=2}){let d=await o({doc_id:"4186250744800382",variables:{count:8,cursor:r,scale:2,id:btoa(`app_collection:${i||await l()}:2356318349:${t}`)}});const u=n(d),{edges:s=[],page_info:c={}}=e(u)||{};return s.map((o=>{var n,e,i,l,r,t,d,u,s,v,_,m,g,y,p,f,h,w,b,k;let C=null==(e=null==(n=null==o?void 0:o.node)?void 0:n.node)?void 0:e.id;if(!/^\d+$/.exec(C))try{let o=atob(C);C=(null==(i=/\d+/.exec(o))?void 0:i[0])||C}catch(N){console.log(N)}return{uid:C,name:null==(r=null==(l=null==o?void 0:o.node)?void 0:l.title)?void 0:r.text,avatar:null==(d=null==(t=null==o?void 0:o.node)?void 0:t.image)?void 0:d.uri,url:null==(u=null==o?void 0:o.node)?void 0:u.url,mutualFriendCount:(null==(m=null==(_=null==(v=null==(s=null==o?void 0:o.node)?void 0:s.subtitle_text)?void 0:v.aggregated_ranges)?void 0:_[0])?void 0:m.count)||0,gender:(null==(w=null==(h=null==(f=null==(p=null==(y=null==(g=null==o?void 0:o.node)?void 0:g.actions_renderer)?void 0:y.action)?void 0:p.client_handler)?void 0:f.profile_action)?void 0:h.restrictable_profile_owner)?void 0:w.gender)||a.UNKNOWN,isLocked:!(null==(b=null==o?void 0:o.node)?void 0:b.subtitle_text)||!(null==(k=null==o?void 0:o.node)?void 0:k.url),cursor:(null==o?void 0:o.cursor)||(null==c?void 0:c.end_cursor)}}))}async function y(e=100){var i,l;const t=await o({fb_api_req_friendly_name:"CometHomeContactsContainerQuery",variables:{numContactsToFetch:e,scale:2,__relay_internal__pv__CometHomeContactListItemsContactListAnRankingIsEnabledrelayprovider:!0},doc_id:"8845828445504279"}),[a,d]=(null==(i=null==t?void 0:t.split)?void 0:i.call(t,"\n"))||[];console.log(t);const u=n(a),s=[];for(let o of r(u,"chat_sidebar_contacts")||[])s.push({uid:o.id,name:o.name,avatar:null==(l=o.profile_picture)?void 0:l.uri});return s}function p(o,n,e=!1){var i,l,r,a,d,u,s,c,v;const _=e?0:1,m=(null==(a=null==(r=null==(l=null==(i=null==o?void 0:o.node)?void 0:i.summary)?void 0:l.ranges)?void 0:r[_])?void 0:a.entity)||{},g=(null==m?void 0:m.id)||(null==(d=null==o?void 0:o.node)?void 0:d.post_id),y=(null==o?void 0:o.cursor)||(null==n?void 0:n.cursor);return{uid:g,name:null==m?void 0:m.short_name,desc:null==(s=null==(u=null==o?void 0:o.node)?void 0:u.summary)?void 0:s.text,url:(null==m?void 0:m.url)||(null==m?void 0:m.profile_url)||(null==m?void 0:m.mobileUrl),avatar:t(g,100),time:1e3*parseInt((null==(c=null==o?void 0:o.node)?void 0:c.creation_time)||(null==(v=null==y?void 0:y.split(":"))?void 0:v[0])||0),cursor:y}}async function f(i=""){const r=await o({fb_api_req_friendly_name:"CometActivityLogStoriesListPaginationQuery",variables:{audience:null,category:"REMOVEDFRIENDS",category_key:"REMOVEDFRIENDS",count:25,cursor:i,feedLocation:null,media_content_filters:[],month:null,person_id:null,privacy:"NONE",scale:2,timeline_visibility:"ALL",year:null,id:await l()},doc_id:"27684299287850969"}),t=n(r);console.log(t);const{edges:a=[],page_info:d={}}=e(t);return a.map((o=>p(o,d)))}async function h(i=""){const r=await o({fb_api_req_friendly_name:"CometActivityLogStoriesListPaginationQuery",variables:{audience:null,category:"FRIENDS",category_key:"FRIENDS",count:25,cursor:i,feedLocation:null,media_content_filters:[],month:null,person_id:null,privacy:"NONE",scale:2,timeline_visibility:"ALL",year:null,id:await l()},doc_id:"27684299287850969"}),t=n(r),{edges:a=[],page_info:d={}}=e(t);return a.map((o=>p(o,d)))}async function w(i="1"){const l=await o({fb_api_req_friendly_name:"BirthdayCometMonthlyBirthdaysRefetchQuery",variables:{count:2,cursor:i,offset_month:0,scale:2,stream_birthday_months:!1},doc_id:"27478190941824257"}),r=n(l),{edges:t=[],page_info:a={}}=e(r);return t.map((o=>{var n,e;const i=(null==(e=null==(n=null==o?void 0:o.node)?void 0:n.friends)?void 0:e.edges)||[];return null==i?void 0:i.map((o=>{var n,e,i,l,r,t,d,u,s,c,v,_;return{uid:null==(n=null==o?void 0:o.node)?void 0:n.id,name:null==(e=null==o?void 0:o.node)?void 0:e.name,url:(null==(i=null==o?void 0:o.node)?void 0:i.profile_url)||(null==(l=null==o?void 0:o.node)?void 0:l.url),avatar:null==(t=null==(r=null==o?void 0:o.node)?void 0:r.profile_picture)?void 0:t.uri,birthday:{day:null==(u=null==(d=null==o?void 0:o.node)?void 0:d.birthdate)?void 0:u.day,month:null==(c=null==(s=null==o?void 0:o.node)?void 0:s.birthdate)?void 0:c.month,year:null==(_=null==(v=null==o?void 0:o.node)?void 0:v.birthdate)?void 0:_.year},cursor:null==a?void 0:a.end_cursor}}))})).flat()}async function b(i=""){const r=await o({fb_api_req_friendly_name:"CometActivityLogStoriesListPaginationQuery",variables:{audience:null,category:"FOLLOWERS",category_key:"FOLLOWERS",count:25,cursor:i,feedLocation:null,media_content_filters:[],month:null,person_id:null,privacy:"NONE",scale:2,timeline_visibility:"ALL",year:null,id:await l()},doc_id:"7782012195256824"});btoa("app_collection:"+await l()+":2356318349:32");const t=n(r),{edges:a=[],page_info:d={}}=e(t);return a.map((o=>p(o,d,!0)))}export{m as FriendSection,_ as QuickFilterLang,v as QuickFilterType,s as addFriend,y as findBuddy,h as getAddFriendTime,d as getAllFriends,b as getFollowers,w as getFriendBirthdays,g as getProfileFriendsSection,f as getUnfriended,c as pokeFriend,u as unfriend};
