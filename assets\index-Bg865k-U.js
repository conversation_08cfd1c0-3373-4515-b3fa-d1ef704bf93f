import{r as e,l as t,n,T as a,i as r,$ as o,h as s,m as l,d as i,a4 as u,a6 as c,R as p,af as f,av as d,aw as m,at as v,au as g,v as y,U as b,G as x,L as C,M as h,w as O,aj as w,o as E,I as j,Z as $,b5 as N}from"./index-Cak6rALw.js";import{_ as S,$ as P,a0 as k,a1 as z,a2 as R,a3 as I,a4 as F,a5 as A,a6 as M,a7 as B,a8 as L,a9 as T,aa as D}from"./MyApp-DW5WH4Ub.js";import{R as K}from"./EyeOutlined-CNKPohhw.js";import{R as U}from"./SearchOutlined--mXbzQ68.js";var V=["autoComplete","onChange","onFocus","onBlur","onPressEnter","onKeyDown","onKeyUp","prefixCls","disabled","htmlSize","className","maxLength","suffix","showCount","count","type","classes","classNames","styles","onCompositionStart","onCompositionEnd"],X=e.forwardRef((function(p,f){var d=p.autoComplete,m=p.onChange,v=p.onFocus,g=p.onBlur,y=p.onPressEnter,b=p.onKeyDown,x=p.onKeyUp,C=p.prefixCls,h=void 0===C?"rc-input":C,O=p.disabled,w=p.htmlSize,E=p.className,j=p.maxLength,$=p.suffix,N=p.showCount,R=p.count,I=p.type,F=void 0===I?"text":I,A=p.classes,M=p.classNames,B=p.styles,L=p.onCompositionStart,T=p.onCompositionEnd,D=t(p,V),K=e.useState(!1),U=n(K,2),X=U[0],q=U[1],_=e.useRef(!1),G=e.useRef(!1),H=e.useRef(null),Q=e.useRef(null),W=function(e){H.current&&k(H.current,e)},Z=a(p.defaultValue,{value:p.value}),J=n(Z,2),Y=J[0],ee=J[1],te=null==Y?"":String(Y),ne=e.useState(null),ae=n(ne,2),re=ae[0],oe=ae[1],se=S(R,N),le=se.max||j,ie=se.strategy(te),ue=!!le&&ie>le;e.useImperativeHandle(f,(function(){var e;return{focus:W,blur:function(){var e;null===(e=H.current)||void 0===e||e.blur()},setSelectionRange:function(e,t,n){var a;null===(a=H.current)||void 0===a||a.setSelectionRange(e,t,n)},select:function(){var e;null===(e=H.current)||void 0===e||e.select()},input:H.current,nativeElement:(null===(e=Q.current)||void 0===e?void 0:e.nativeElement)||H.current}})),e.useEffect((function(){G.current&&(G.current=!1),q((function(e){return(!e||!O)&&e}))}),[O]);var ce=function(e,t,n){var a,r,o=t;if(!_.current&&se.exceedFormatter&&se.max&&se.strategy(t)>se.max)t!==(o=se.exceedFormatter(t,{max:se.max}))&&oe([(null===(a=H.current)||void 0===a?void 0:a.selectionStart)||0,(null===(r=H.current)||void 0===r?void 0:r.selectionEnd)||0]);else if("compositionEnd"===n.source)return;ee(o),H.current&&z(H.current,e,m,o)};e.useEffect((function(){var e;re&&(null===(e=H.current)||void 0===e||e.setSelectionRange.apply(e,r(re)))}),[re]);var pe,fe=function(e){ce(e,e.target.value,{source:"change"})},de=function(e){_.current=!1,ce(e,e.currentTarget.value,{source:"compositionEnd"}),null==T||T(e)},me=function(e){y&&"Enter"===e.key&&!G.current&&(G.current=!0,y(e)),null==b||b(e)},ve=function(e){"Enter"===e.key&&(G.current=!1),null==x||x(e)},ge=function(e){q(!0),null==v||v(e)},ye=function(e){G.current&&(G.current=!1),q(!1),null==g||g(e)},be=ue&&"".concat(h,"-out-of-range");return o.createElement(P,s({},D,{prefixCls:h,className:l(E,be),handleReset:function(e){ee(""),W(),H.current&&z(H.current,e,m)},value:te,focused:X,triggerFocus:W,suffix:function(){var e=Number(le)>0;if($||se.show){var t=se.showFormatter?se.showFormatter({value:te,count:ie,maxLength:le}):"".concat(ie).concat(e?" / ".concat(le):"");return o.createElement(o.Fragment,null,se.show&&o.createElement("span",{className:l("".concat(h,"-show-count-suffix"),u({},"".concat(h,"-show-count-has-suffix"),!!$),null==M?void 0:M.count),style:i({},null==B?void 0:B.count)},t),$)}return null}(),disabled:O,classes:A,classNames:M,styles:B,ref:Q}),(pe=c(p,["prefixCls","onPressEnter","addonBefore","addonAfter","prefix","suffix","allowClear","defaultValue","showCount","count","classes","htmlSize","styles","classNames","onClear"]),o.createElement("input",s({autoComplete:d},pe,{onChange:fe,onFocus:ge,onBlur:ye,onKeyDown:me,onKeyUp:ve,className:l(h,u({},"".concat(h,"-disabled"),O),null==M?void 0:M.input),style:null==B?void 0:B.input,ref:H,size:w,type:F,onCompositionStart:function(e){_.current=!0,null==L||L(e)},onCompositionEnd:de}))))}));function q(t,n){const a=e.useRef([]),r=()=>{a.current.push(setTimeout((()=>{var e,n,a,r;(null===(e=t.current)||void 0===e?void 0:e.input)&&"password"===(null===(n=t.current)||void 0===n?void 0:n.input.getAttribute("type"))&&(null===(a=t.current)||void 0===a?void 0:a.input.hasAttribute("value"))&&(null===(r=t.current)||void 0===r||r.input.removeAttribute("value"))})))};return e.useEffect((()=>(n&&r(),()=>a.current.forEach((e=>{e&&clearTimeout(e)})))),[]),r}const _=e.forwardRef(((t,n)=>{const{prefixCls:a,bordered:r=!0,status:s,size:i,disabled:u,onBlur:c,onFocus:x,suffix:C,allowClear:h,addonAfter:O,addonBefore:w,className:E,style:j,styles:$,rootClassName:N,onChange:S,classNames:P,variant:k}=t,z=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n}(t,["prefixCls","bordered","status","size","disabled","onBlur","onFocus","suffix","allowClear","addonAfter","addonBefore","className","style","styles","rootClassName","onChange","classNames","variant"]),{getPrefixCls:L,direction:T,allowClear:D,autoComplete:K,className:U,style:V,classNames:_,styles:G}=p("input"),H=L("input",a),Q=e.useRef(null),W=f(H),[Z,J,Y]=R(H,N),[ee]=I(H,W),{compactSize:te,compactItemClassnames:ne}=d(H,T),ae=m((e=>{var t;return null!==(t=null!=i?i:te)&&void 0!==t?t:e})),re=o.useContext(v),oe=null!=u?u:re,{status:se,hasFeedback:le,feedbackIcon:ie}=e.useContext(g),ue=B(se,s),ce=function(e){return!!(e.prefix||e.suffix||e.allowClear||e.showCount)}(t)||!!le;e.useRef(ce);const pe=q(Q,!0),fe=(le||C)&&o.createElement(o.Fragment,null,C,le&&ie),de=F(null!=h?h:D),[me,ve]=A("input",k,r);return Z(ee(o.createElement(X,Object.assign({ref:y(n,Q),prefixCls:H,autoComplete:K},z,{disabled:oe,onBlur:e=>{pe(),null==c||c(e)},onFocus:e=>{pe(),null==x||x(e)},style:Object.assign(Object.assign({},V),j),styles:Object.assign(Object.assign({},G),$),suffix:fe,allowClear:de,className:l(E,N,Y,W,ne,U),onChange:e=>{pe(),null==S||S(e)},addonBefore:w&&o.createElement(b,{form:!0,space:!0},w),addonAfter:O&&o.createElement(b,{form:!0,space:!0},O),classNames:Object.assign(Object.assign(Object.assign({},P),_),{input:l({[`${H}-sm`]:"small"===ae,[`${H}-lg`]:"large"===ae,[`${H}-rtl`]:"rtl"===T},null==P?void 0:P.input,_.input,J),variant:l({[`${H}-${me}`]:ve},M(H,ue)),affixWrapper:l({[`${H}-affix-wrapper-sm`]:"small"===ae,[`${H}-affix-wrapper-lg`]:"large"===ae,[`${H}-affix-wrapper-rtl`]:"rtl"===T},J),wrapper:l({[`${H}-group-rtl`]:"rtl"===T},J),groupWrapper:l({[`${H}-group-wrapper-sm`]:"small"===ae,[`${H}-group-wrapper-lg`]:"large"===ae,[`${H}-group-wrapper-rtl`]:"rtl"===T,[`${H}-group-wrapper-${me}`]:ve},M(`${H}-group-wrapper`,ue,le),J)})}))))})),G=e=>{const{componentCls:t,paddingXS:n}=e;return{[t]:{display:"inline-flex",alignItems:"center",flexWrap:"nowrap",columnGap:n,[`${t}-input-wrapper`]:{position:"relative",[`${t}-mask-icon`]:{position:"absolute",zIndex:"1",top:"50%",right:"50%",transform:"translate(50%, -50%)",pointerEvents:"none"},[`${t}-mask-input`]:{color:"transparent",caretColor:"var(--ant-color-text)"},[`${t}-mask-input[type=number]::-webkit-inner-spin-button`]:{"-webkit-appearance":"none",margin:0},[`${t}-mask-input[type=number]`]:{"-moz-appearance":"textfield"}},"&-rtl":{direction:"rtl"},[`${t}-input`]:{textAlign:"center",paddingInline:e.paddingXXS},[`&${t}-sm ${t}-input`]:{paddingInline:e.calc(e.paddingXXS).div(2).equal()},[`&${t}-lg ${t}-input`]:{paddingInline:e.paddingXS}}}},H=C(["Input","OTP"],(e=>{const t=h(e,L(e));return[G(t)]}),T);const Q=e.forwardRef(((t,n)=>{const{className:a,value:r,onChange:o,onActiveChange:s,index:i,mask:u}=t,c=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n}(t,["className","value","onChange","onActiveChange","index","mask"]),{getPrefixCls:p}=e.useContext(x),f=p("otp"),d="string"==typeof u?u:r,m=e.useRef(null);e.useImperativeHandle(n,(()=>m.current));const v=()=>{O((()=>{var e;const t=null===(e=m.current)||void 0===e?void 0:e.input;document.activeElement===t&&t&&t.select()}))};return e.createElement("span",{className:`${f}-input-wrapper`,role:"presentation"},u&&""!==r&&void 0!==r&&e.createElement("span",{className:`${f}-mask-icon`,"aria-hidden":"true"},d),e.createElement(_,Object.assign({"aria-label":`OTP Input ${i+1}`,type:!0===u?"password":"text"},c,{ref:m,value:r,onInput:e=>{o(i,e.target.value)},onFocus:v,onKeyDown:e=>{const{key:t,ctrlKey:n,metaKey:a}=e;"ArrowLeft"===t?s(i-1):"ArrowRight"===t?s(i+1):"z"===t&&(n||a)&&e.preventDefault(),v()},onKeyUp:e=>{"Backspace"!==e.key||r||s(i-1),v()},onMouseDown:v,onMouseUp:v,className:l(a,{[`${f}-mask-input`]:u})})))}));function W(e){return(e||"").split("")}const Z=t=>{const{index:n,prefixCls:a,separator:r}=t,o="function"==typeof r?r(n):r;return o?e.createElement("span",{className:`${a}-separator`},o):null},J=e.forwardRef(((t,n)=>{const{prefixCls:a,length:o=6,size:s,defaultValue:i,value:u,onChange:c,formatter:p,separator:f,variant:d,disabled:v,status:y,autoFocus:b,mask:C,type:h,onInput:O,inputMode:j}=t,$=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n}(t,["prefixCls","length","size","defaultValue","value","onChange","formatter","separator","variant","disabled","status","autoFocus","mask","type","onInput","inputMode"]),{getPrefixCls:N,direction:S}=e.useContext(x),P=N("otp",a),k=w($,{aria:!0,data:!0,attr:!0}),[z,R,I]=H(P),F=m((e=>null!=s?s:e)),A=e.useContext(g),M=B(A.status,y),L=e.useMemo((()=>Object.assign(Object.assign({},A),{status:M,hasFeedback:!1,feedbackIcon:null})),[A,M]),T=e.useRef(null),D=e.useRef({});e.useImperativeHandle(n,(()=>({focus:()=>{var e;null===(e=D.current[0])||void 0===e||e.focus()},blur:()=>{var e;for(let t=0;t<o;t+=1)null===(e=D.current[t])||void 0===e||e.blur()},nativeElement:T.current})));const K=e=>p?p(e):e,[U,V]=e.useState((()=>W(K(i||""))));e.useEffect((()=>{void 0!==u&&V(W(u))}),[u]);const X=E((e=>{V(e),O&&O(e),c&&e.length===o&&e.every((e=>e))&&e.some(((e,t)=>U[t]!==e))&&c(e.join(""))})),q=E(((e,t)=>{let n=r(U);for(let r=0;r<e;r+=1)n[r]||(n[r]="");t.length<=1?n[e]=t:n=n.slice(0,e).concat(W(t)),n=n.slice(0,o);for(let r=n.length-1;r>=0&&!n[r];r-=1)n.pop();const a=K(n.map((e=>e||" ")).join(""));return n=W(a).map(((e,t)=>" "!==e||n[t]?e:n[t])),n})),_=(e,t)=>{var n;const a=q(e,t),r=Math.min(e+t.length,o-1);r!==e&&void 0!==a[e]&&(null===(n=D.current[r])||void 0===n||n.focus()),X(a)},G=e=>{var t;null===(t=D.current[e])||void 0===t||t.focus()},J={variant:d,disabled:v,status:M,mask:C,type:h,inputMode:j};return z(e.createElement("div",Object.assign({},k,{ref:T,className:l(P,{[`${P}-sm`]:"small"===F,[`${P}-lg`]:"large"===F,[`${P}-rtl`]:"rtl"===S},I,R),role:"group"}),e.createElement(g.Provider,{value:L},Array.from({length:o}).map(((t,n)=>{const a=`otp-${n}`,r=U[n]||"";return e.createElement(e.Fragment,{key:a},e.createElement(Q,Object.assign({ref:e=>{D.current[n]=e},index:n,size:F,htmlSize:1,className:`${P}-input`,onChange:_,value:r,onActiveChange:G,autoFocus:0===n&&b},J)),n<o-1&&e.createElement(Z,{separator:f,index:n,prefixCls:P}))})))))}));var Y={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"}},{tag:"path",attrs:{d:"M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"}}]},name:"eye-invisible",theme:"outlined"},ee=function(t,n){return e.createElement(j,s({},t,{ref:n,icon:Y}))},te=e.forwardRef(ee);const ne=t=>t?e.createElement(K,null):e.createElement(te,null),ae={click:"onClick",hover:"onMouseOver"},re=e.forwardRef(((t,n)=>{const{disabled:a,action:r="click",visibilityToggle:o=!0,iconRender:s=ne}=t,i=e.useContext(v),u=null!=a?a:i,p="object"==typeof o&&void 0!==o.visible,[f,d]=e.useState((()=>!!p&&o.visible)),m=e.useRef(null);e.useEffect((()=>{p&&d(o.visible)}),[p,o]);const g=q(m),b=()=>{var e;if(u)return;f&&g();const t=!f;d(t),"object"==typeof o&&(null===(e=o.onVisibleChange)||void 0===e||e.call(o,t))},{className:C,prefixCls:h,inputPrefixCls:O,size:w}=t,E=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n}(t,["className","prefixCls","inputPrefixCls","size"]),{getPrefixCls:j}=e.useContext(x),$=j("input",O),N=j("input-password",h),S=o&&(t=>{const n=ae[r]||"",a=s(f),o={[n]:b,className:`${t}-icon`,key:"passwordIcon",onMouseDown:e=>{e.preventDefault()},onMouseUp:e=>{e.preventDefault()}};return e.cloneElement(e.isValidElement(a)?a:e.createElement("span",null,a),o)})(N),P=l(N,C,{[`${N}-${w}`]:!!w}),k=Object.assign(Object.assign({},c(E,["suffix","iconRender","visibilityToggle"])),{type:f?"text":"password",className:P,prefixCls:$,suffix:S});return w&&(k.size=w),e.createElement(_,Object.assign({ref:y(n,m)},k))}));const oe=e.forwardRef(((t,n)=>{const{prefixCls:a,inputPrefixCls:r,className:o,size:s,suffix:i,enterButton:u=!1,addonAfter:c,loading:p,disabled:f,onSearch:v,onChange:g,onCompositionStart:b,onCompositionEnd:C,variant:h,onPressEnter:O}=t,w=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n}(t,["prefixCls","inputPrefixCls","className","size","suffix","enterButton","addonAfter","loading","disabled","onSearch","onChange","onCompositionStart","onCompositionEnd","variant","onPressEnter"]),{getPrefixCls:E,direction:j}=e.useContext(x),S=e.useRef(!1),P=E("input-search",a),k=E("input",r),{compactSize:z}=d(P,j),R=m((e=>{var t;return null!==(t=null!=s?s:z)&&void 0!==t?t:e})),I=e.useRef(null),F=e=>{var t;document.activeElement===(null===(t=I.current)||void 0===t?void 0:t.input)&&e.preventDefault()},A=e=>{var t,n;v&&v(null===(n=null===(t=I.current)||void 0===t?void 0:t.input)||void 0===n?void 0:n.value,e,{source:"input"})},M="boolean"==typeof u?e.createElement(U,null):null,B=`${P}-button`;let L;const T=u||{},D=T.type&&!0===T.type.__ANT_BUTTON;L=D||"button"===T.type?$(T,Object.assign({onMouseDown:F,onClick:e=>{var t,n;null===(n=null===(t=null==T?void 0:T.props)||void 0===t?void 0:t.onClick)||void 0===n||n.call(t,e),A(e)},key:"enterButton"},D?{className:B,size:R}:{})):e.createElement(N,{className:B,color:u?"primary":"default",size:R,disabled:f,key:"enterButton",onMouseDown:F,onClick:A,loading:p,icon:M,variant:"borderless"===h||"filled"===h||"underlined"===h?"text":u?"solid":void 0},u),c&&(L=[L,$(c,{key:"addonAfter"})]);const K=l(P,{[`${P}-rtl`]:"rtl"===j,[`${P}-${R}`]:!!R,[`${P}-with-button`]:!!u},o),V=Object.assign(Object.assign({},w),{className:K,prefixCls:k,type:"search",size:R,variant:h,onPressEnter:e=>{S.current||p||(null==O||O(e),A(e))},onCompositionStart:e=>{S.current=!0,null==b||b(e)},onCompositionEnd:e=>{S.current=!1,null==C||C(e)},addonAfter:L,suffix:i,onChange:e=>{(null==e?void 0:e.target)&&"click"===e.type&&v&&v(e.target.value,e,{source:"clear"}),null==g||g(e)},disabled:f});return e.createElement(_,Object.assign({ref:y(I,n)},V))})),se=_;se.Group=t=>{const{getPrefixCls:n,direction:a}=e.useContext(x),{prefixCls:r,className:o}=t,s=n("input-group",r),i=n("input"),[u,c,p]=I(i),f=l(s,p,{[`${s}-lg`]:"large"===t.size,[`${s}-sm`]:"small"===t.size,[`${s}-compact`]:t.compact,[`${s}-rtl`]:"rtl"===a},c,o),d=e.useContext(g),m=e.useMemo((()=>Object.assign(Object.assign({},d),{isFormItemInput:!1})),[d]);return u(e.createElement("span",{className:f,style:t.style,onMouseEnter:t.onMouseEnter,onMouseLeave:t.onMouseLeave,onFocus:t.onFocus,onBlur:t.onBlur},e.createElement(g.Provider,{value:m},t.children)))},se.Search=oe,se.TextArea=D,se.Password=re,se.OTP=J;export{se as I,_ as a};
