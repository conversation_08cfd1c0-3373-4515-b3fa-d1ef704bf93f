import{p as t}from"./index-BK5PnqrR.js";import"./index-Cak6rALw.js";
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var e=function(t,r){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,r)};function r(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}var n=function(){return n=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},n.apply(this,arguments)};function o(t,e,r,n){return new(r||(r=Promise))((function(e,o){function i(t){try{s(n.next(t))}catch(e){o(e)}}function a(t){try{s(n.throw(t))}catch(e){o(e)}}function s(t){var n;t.done?e(t.value):(n=t.value,n instanceof r?n:new r((function(t){t(n)}))).then(i,a)}s((n=n.apply(t,[])).next())}))}function i(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(r)throw new TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(s){i=[6,s],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}function a(){for(var t=0,e=0,r=arguments.length;e<r;e++)t+=arguments[e].length;var n=Array(t),o=0;for(e=0;e<r;e++)for(var i=arguments[e],a=0,s=i.length;a<s;a++,o++)n[o]=i[a];return n}for(var s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",u=new Uint8Array(256),c=0;c<64;c++)u[s.charCodeAt(c)]=c;var f,h,l=function(t){for(var e="",r=t.length,n=0;n<r;n+=3)e+=s[t[n]>>2],e+=s[(3&t[n])<<4|t[n+1]>>4],e+=s[(15&t[n+1])<<2|t[n+2]>>6],e+=s[63&t[n+2]];return r%3==2?e=e.substring(0,e.length-1)+"=":r%3==1&&(e=e.substring(0,e.length-2)+"=="),e},d=function(t){var e,r,n,o,i,a=.75*t.length,s=t.length,c=0;"="===t[t.length-1]&&(a--,"="===t[t.length-2]&&a--);var f=new Uint8Array(a);for(e=0;e<s;e+=4)r=u[t.charCodeAt(e)],n=u[t.charCodeAt(e+1)],o=u[t.charCodeAt(e+2)],i=u[t.charCodeAt(e+3)],f[c++]=r<<2|n>>4,f[c++]=(15&n)<<4|o>>2,f[c++]=(3&o)<<6|63&i;return f},p=/^(data)?:?([\w\/\+]+)?;?(charset=[\w-]+|base64)?.*,/i,y=function(t){var e=t.trim(),r=e.substring(0,100).match(p);if(!r)return d(e);var n=r[0],o=e.substring(n.length);return d(o)},g=function(t){return t.charCodeAt(0)},v=function(t){return t.codePointAt(0)},m=function(t,e){return w(t.toString(16),e,"0").toUpperCase()},b=function(t){return m(t,2)},x=function(t){return String.fromCharCode(t)},F=function(t){return x(parseInt(t,16))},w=function(t,e,r){for(var n="",o=0,i=e-t.length;o<i;o++)n+=r;return n+t},S=function(t,e,r){for(var n=t.length,o=0;o<n;o++)e[r++]=t.charCodeAt(o);return n},C=function(t,e){return void 0===e&&(e=4),t+"-"+Math.floor(Math.random()*Math.pow(10,e))},T=function(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")},k=function(t){return t.replace(/\t|\u0085|\u2028|\u2029/g,"    ").replace(/[\b\v]/g,"")},O=["\\n","\\f","\\r","\\u000B"],P=["\n","\f","\r","\v"],A=function(t){return/^[\n\f\r\u000B]$/.test(t)},R=function(t){return t.split(/[\n\f\r\u000B]/)},N=function(t){return t.replace(/[\n\f\r\u000B]/g," ")},D=function(t,e){var r,n=t.charCodeAt(e),o=e+1,i=1;return n>=55296&&n<=56319&&t.length>o&&(r=t.charCodeAt(o))>=56320&&r<=57343&&(i=2),[t.slice(e,e+i),i]},j=function(t){for(var e=[],r=0,n=t.length;r<n;){var o=D(t,r),i=o[0],a=o[1];e.push(i),r+=a}return e},M=function(t,e,r,n){for(var o=function(t){for(var e=O.join("|"),r=["$"],n=0,o=t.length;n<o;n++){var i=t[n];if(A(i))throw new TypeError("`wordBreak` must not include "+e);r.push(""===i?".":T(i))}var a=r.join("|");return new RegExp("("+e+")|((.*?)("+a+"))","gm")}(e),i=k(t).match(o),a="",s=0,u=[],c=function(){""!==a&&u.push(a),a="",s=0},f=0,h=i.length;f<h;f++){var l=i[f];if(A(l))c();else{var d=n(l);s+d>r&&c(),a+=l,s+=d}}return c(),u},V=/^D:(\d\d\d\d)(\d\d)?(\d\d)?(\d\d)?(\d\d)?(\d\d)?([+\-Z])?(\d\d)?'?(\d\d)?'?$/,B=function(t){var e=t.match(V);if(e){var r=e[1],n=e[2],o=void 0===n?"01":n,i=e[3],a=void 0===i?"01":i,s=e[4],u=void 0===s?"00":s,c=e[5],f=void 0===c?"00":c,h=e[6],l=void 0===h?"00":h,d=e[7],p=void 0===d?"Z":d,y=e[8],g=void 0===y?"00":y,v=e[9];return new Date(r+"-"+o+"-"+a+"T"+u+":"+f+":"+l+("Z"===p?"Z":""+p+g+":"+(void 0===v?"00":v)))}},U=function(t,e){for(var r,n,o=0;o<t.length;){var i=t.substring(o).match(e);if(!i)return{match:n,pos:o};n=i,o+=(null!==(r=i.index)&&void 0!==r?r:0)+i[0].length}return{match:n,pos:o}},I=function(t){return t[t.length-1]},W=function(t){if(t instanceof Uint8Array)return t;for(var e=t.length,r=new Uint8Array(e),n=0;n<e;n++)r[n]=t.charCodeAt(n);return r},z=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=t.length,n=[],o=0;o<r;o++){var i=t[o];n[o]=i instanceof Uint8Array?i:W(i)}var a=0;for(o=0;o<r;o++)a+=t[o].length;for(var s=new Uint8Array(a),u=0,c=0;c<r;c++)for(var f=n[c],h=0,l=f.length;h<l;h++)s[u++]=f[h];return s},q=function(t){for(var e=0,r=0,n=t.length;r<n;r++)e+=t[r].length;var o=new Uint8Array(e),i=0;for(r=0,n=t.length;r<n;r++){var a=t[r];o.set(a,i),i+=a.length}return o},E=function(t){for(var e="",r=0,n=t.length;r<n;r++)e+=x(t[r]);return e},G=function(t,e){return t.id-e.id},K=function(t,e){for(var r=[],n=0,o=t.length;n<o;n++){var i=t[n],a=t[n-1];0!==n&&e(i)===e(a)||r.push(i)}return r},L=function(t){for(var e=t.length,r=0,n=Math.floor(e/2);r<n;r++){var o=r,i=e-r-1,a=t[r];t[o]=t[i],t[i]=a}return t},X=function(t){for(var e=0,r=0,n=t.length;r<n;r++)e+=t[r];return e},H=function(t,e){for(var r=new Array(e-t),n=0,o=r.length;n<o;n++)r[n]=t+n;return r},Z=function(t,e){for(var r=new Array(e.length),n=0,o=e.length;n<o;n++)r[n]=t[e[n]];return r},Y=function(t){return t instanceof Uint8Array||t instanceof ArrayBuffer||"string"==typeof t},J=function(t){if("string"==typeof t)return y(t);if(t instanceof ArrayBuffer)return new Uint8Array(t);if(t instanceof Uint8Array)return t;throw new TypeError("`input` must be one of `string | ArrayBuffer | Uint8Array`")},Q=function(){return new Promise((function(t){setTimeout((function(){return t()}),0)}))},_=function(t,e){void 0===e&&(e=!0);var r=[];e&&r.push(239,187,191);for(var n=0,o=t.length;n<o;){var i=t.codePointAt(n);if(i<128){var a=127&i;r.push(a),n+=1}else if(i<2048){a=i>>6&31|192;var s=63&i|128;r.push(a,s),n+=1}else if(i<65536){a=i>>12&15|224,s=i>>6&63|128;var u=63&i|128;r.push(a,s,u),n+=1}else{if(!(i<1114112))throw new Error("Invalid code point: 0x"+b(i));a=i>>18&7|240,s=i>>12&63|128,u=i>>6&63|128;var c=63&i|128;r.push(a,s,u,c),n+=2}}return new Uint8Array(r)},$=function(t,e){void 0===e&&(e=!0);var r=[];e&&r.push(65279);for(var n=0,o=t.length;n<o;){var i=t.codePointAt(n);if(i<65536)r.push(i),n+=1;else{if(!(i<1114112))throw new Error("Invalid code point: 0x"+b(i));r.push(rt(i),nt(i)),n+=2}}return new Uint16Array(r)},tt=function(t){return t>=0&&t<=65535},et=function(t){return t>=65536&&t<=1114111},rt=function(t){return Math.floor((t-65536)/1024)+55296},nt=function(t){return(t-65536)%1024+56320};(h=f||(f={})).BigEndian="BigEndian",h.LittleEndian="LittleEndian";for(var ot="�".codePointAt(0),it=function(t,e){if(void 0===e&&(e=!0),t.length<=1)return String.fromCodePoint(ot);for(var r=e?ct(t):f.BigEndian,n=e?2:0,o=[];t.length-n>=2;){var i=ut(t[n++],t[n++],r);if(at(i))if(t.length-n<2)o.push(ot);else{var a=ut(t[n++],t[n++],r);st(a)?o.push(i,a):o.push(ot)}else st(i)?(n+=2,o.push(ot)):o.push(i)}return n<t.length&&o.push(ot),String.fromCodePoint.apply(String,o)},at=function(t){return t>=55296&&t<=56319},st=function(t){return t>=56320&&t<=57343},ut=function(t,e,r){if(r===f.LittleEndian)return e<<8|t;if(r===f.BigEndian)return t<<8|e;throw new Error("Invalid byteOrder: "+r)},ct=function(t){return ft(t)?f.BigEndian:ht(t)?f.LittleEndian:f.BigEndian},ft=function(t){return 254===t[0]&&255===t[1]},ht=function(t){return 255===t[0]&&254===t[1]},lt=function(t){return ft(t)||ht(t)},dt=function(t){var e,r=String(t);if(Math.abs(t)<1){if(e=parseInt(t.toString().split("e-")[1])){var n=t<0;n&&(t*=-1),t*=Math.pow(10,e-1),r="0."+new Array(e).join("0")+t.toString().substring(2),n&&(r="-"+r)}}else(e=parseInt(t.toString().split("+")[1]))>20&&(e-=20,r=(t/=Math.pow(10,e)).toString()+new Array(e+1).join("0"));return r},pt=function(t){return Math.ceil(t.toString(2).length/8)},yt=function(t){for(var e=new Uint8Array(pt(t)),r=1;r<=e.length;r++)e[r-1]=t>>8*(e.length-r);return e},gt=function(t){throw new Error(t)},vt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",mt=new Uint8Array(256),bt=0;bt<64;bt++)mt[vt.charCodeAt(bt)]=bt;var xt=function(e){return function(t){for(var e="",r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e}(t.inflate(function(t){var e,r,n,o,i,a=.75*t.length,s=t.length,u=0;"="===t[t.length-1]&&(a--,"="===t[t.length-2]&&a--);var c=new Uint8Array(a);for(e=0;e<s;e+=4)r=mt[t.charCodeAt(e)],n=mt[t.charCodeAt(e+1)],o=mt[t.charCodeAt(e+2)],i=mt[t.charCodeAt(e+3)],c[u++]=r<<2|n>>4,c[u++]=(15&n)<<4|o>>2,c[u++]=(3&o)<<6|63&i;return c}(e)))};const Ft=JSON.parse('"eJyNnVtzG0eyrf8KA0/7RMhzJJK6+U2+zMX2mJYsEuJMzANEtihsgYQMEITaO/Z/P41CV+bKlaug86JQf6uArsrKXNVX8H8m3y9vb7u7+8m3k4t/btazm+7o5PmTZy+PTl88eXk6eTT56/Lu/tfZbTc0+Hu3eOju51ezb75bLq532maxYO2oarPb+aJndRCm3fzm425/Y8N/3M8W86tXdzeLoeXjYXv91/mX7vq3+f3Vx8m396tN92jy/cfZanZ1361+73af/PHLfXd33V2/Wd7O7sY+fvfd8svk239/8+T540ffHB+/ePTk8eOTRy+fHf/n0eR8aLxazO+635br+f18eTf59ptBBuHtx/nVp7tuvZ58+3TgF91qXZpNHj8+/svjx4+Hnfy6HAawG8z3y8/9ajeGo/+6+j9HT16+ePpo9+/z8u/L3b8vH5d/nx+9ul6+745+79f33e366B93V8vV5+Vqdt9d/+Xo6NVicfRm9z3rozfduls9DNTDOF8fzY7uV7Pr7na2+nS0/HD0y/xued9/7r4ZGi2OXv3taHZ3/X+Xq6P58AXrzfv1/Ho+W8279V+Gzv447Op6fnfz+9XHrsxA6cnv98NHZqvrqg4Nv599/vs4Ic+fvHg0eVe3np4cP5q8Wl/tAr0axR862/7m+PHzR5Pf76//Pp18+2QnDv+/2P3/9PF+vv7Z3a/mV0NA//0/k+m7ybfHz4dGvw5dWX+eDXH830d7fHJyssfdl6vF7Nb46fPTPf9jsxzi9X5hytOnz/bK3eb2/W6ibu6ydr1cLGYr4y+GiSn8c7e62qV7FZ4fH++F2e0grYf4mGQdLj0oM557/Xm26u4W3YeWRB+r3Zitd9+4/uQdfzEO9/Nis85duBqqdJZ38bH//LG7y82HocyXYiTrxWz9MQfrz261zHR512V4vxUt7z+uOtH2w3KzEnT+INqu518E7B46MbddiKmnw/xOpNXVcrG8y3jd3c6jZDOw2NlAot0fm9ki45tVN5SzD/PZkyc1abp1sZqqvHz+dJx7kX2vMvouo+8z+sH3/Oz5Hv2YO/NX/2BNhb/l7/p7Tph/5DD/lD/4c97jL156NeT/zB/8NffrLA/ot9zqdf6uN/mDv+d+vc0fPM8fvPBZOx0neppbvcvoMu/xXzn53g+L2afuPtiGhfz9oMU65c9FT7FUnK2v5vOr+epqc5tnbbOz7fWw/nR5j8XfQmfsY7M8nve51VVudZ1bieL8kD94k9HH3OV5Rv+d9/gpt/IStiXhNu/xLqNlRp9F1WerFxa4zpG4z9+1yR98yJWwza2Ek/aOdsc9xfRzV3f5FRPh+MXjmpWrRvtD2Xg/X1w3l/rr5VaYe1idPWL35TjNk+NJrbgPuwND9Fkfs1o7PiyWq7ng667xLVeb1bCMX3kAj0+wbNbzcuCaoluPWnRZ3Wzmg3K7vNdHDju5fPFX5Bh6S5wPc8HE8dNwKCcPB65nNzedSNs9x0MxOuDYzV236kTtD8dCs5vV7DOY2tOaWcNJRCd80MP7frY+EOHD6kofK9gERH04KRg/Pxxizz+v52shDWO9/7jchGPFtOyH5PaZW80eRD3Mrjb36tClePmHRfcla43Kup1drdThzvtVp3Z8vbyfXYWKc2k+zCQGwJQV1qF3trseQqqOUTd3N7PV5nYx24jdLG+Gw8xP4utmOA6Yl9uQsy688sOek+cjW66uPwzHeeHA0I9Q4iLrByCR+x7OYA/Pntoebgen2yxwF7ayzMRie70r+vVaLGCLuGNfeSK3I5KlGNRQn8Mp8ZD34hziH2lK3QliBvryH/PGlyY5qf51cfb86Cj3oC4X1/OHOSS0fyT2zA+YRXF4txsfOj/0ob4Rg3U596IygaHmr/T9hVJx3J6IGdWDfyb2zmeCPuBnAWknfs4weASchBxXJ1YDfX7yvIrjVQ+xK3IdXztjHvgodVx+VR3w8mjlaDRVP9KXw7FTqda3RWOFcCarhAzRw1yzJ/rha9z76ct66rn8s7u7EZn7Ju7Cz+LUID05DhbJocx9xQuJHc02xnrFY/Xznxw5i+rbj8uVGNUZ7d3DQFVgJ3pU8Kd1EaOwWTXRDjxienErFzjWm3KUsxL9jSnoUWzxaKtmgrebxf3886IX/WqU/9s4QEuk4Xjrfj5bXM8/fMhz1bet4de4H09YkSxeGwfT7MCq05auGuO9a9lgK2N+jQHyxZDqHy+/DUcMeA3OToFWy0/dHZ4ImTmuupv5Oh76eonGyYblONdFPdRYb4aqDucjHmw6hrTCbERm2Ur1fzU+8C+q8NOX9di1XOmK18Eszj/ef8zw+6YBLpRv2VjuGybTNVfHlvCqdfhwICtjgP18uVUavG9zhdaMtJae1jK6bu0517Ht++BhCa+Y9bigW9wLA78PJu2euF0ecMTUNfu6240YSWMNX8rjTK8FPvixq0/xCOfFySn4+JDAqyGR1/n7fud8Pa2Tv2gsJD8fXH9/iRPnpxJ2X0eZYrIFt4wYJuetGv8ldtviMETt42wBS0Mt8t2pSaxwnwu1BJgvx8MmT7WvTGCjFLrWgG6imeKAxmlVs6rPRn6XB4iWwbLnlhDXg010KmMbS/731AlbuMhtTs3Or+dXymh/iF8EB2aHDnd/pcNa625j3t4czuuD+3rV+M5XTZOOpwM2A/F73IgPHFD+2Fruad9+iVie3dkBWTwSsG87WAo0QeaXB/e0WN7s5vtuKcK9bJvpJq9jNYOGr2pU8s3Bye1gJfeYN9L3Tq7jdnHnLh80u+e3lrsfN7u7kf95NPm5W939NpuvdveQ/z15tbtbPXn0zenj/zwat/buEdC+nxGNpo7wb8PWU9/au0pAODAUzsL3nOUu4NIbuE1VoPv6Dyg4T1DGkAW2vzoU0L5wEL0OW2+HrZe+VWOGKIzehfMQi/M6ekBh9MBh9EDr6AHR6EGx0QMb6zqwYidILoatF7Y1Hbae2dblsPXkiW/WISGDvgPeDJsnvlU/CCjEAjh8H9AaC0AUC1AsFsAsFsDGWDh5CJmwDVoft/KI+tzzsRGWpiEqDuNUpM65UqsC5WqIata4LNyqnuXv5hI2rurYxFzMJlFFG9dlbTLXtglU4Mapyit/nRHUuyEqeueq8qt6niPKHmBcGYGJ2Q1MIkswrn3BZDYHE9ghTIg2UTF4RUVgGBWhaxhj6zBB+EfVwEQMUd0ZV3ZiYrsy2ViMa3cxmS3GBPYZE6LZVPyQE3KbW/UCNQIhXGg0A3QhQ1TfxsmFnLMLVQVcyBC5kHHpQlU9y9/NLmRcuZCJ2YVMIhcyrl3IZHYhE8iFjJMLVf46I3AhQ+RCzpULVfU8R5RdyLhyIROzC5lELmRcu5DJ7EImsAuZEF2oYnChisCFKkIXMsYuZIJwoaqBCxmi4jOuXMjEdmWyCxnXLmQyu5AJ7EImRBeq+CEn5Da36gVqBEK4EIYGrShyqvQokimRyM4UZLCnyMmjoiiNKjQ5a+yPLSuKyrdii2xeUScHi6K2sdiGvSyqZGhRJFcL4usGB3+LnEyOROV0ocl5Y17Y86KojC+2yO4XdbLAKGofjG3YDKPKjhjVaItBA28MHAwycHTJKLBVRlX4ZWgAphk5GUYUlX3GFl/xFTbSKGo3jW3YUqPKvhrVaK5Be2jUxbbRvm/xQ/ETrusEPRcpGRVK5LdBYrcFEbwWKTktStJnocGZ3A97LErKYVHP/ooquStK2luxBTsrauSrKJGrgvRaUnBUpOSnQVJuCg3OZezZSVFSPop6dlFUyUNR0g6KLdg/UWP3RC16JyjgnEDBN4GiayJmz0RNOCbI4JdIqdpRUl6J+kEvYJ9ESbsktmCPRI0dErXoj6A8yAzfyra9pu1ICVccR4+WaIhMxTiZoXN2wqqADRoiDzQuDbCqZ/m72fqMK98zMZueSeR4xrXdmcxeZwIZnXFyucpfZwT+ZojMzblytqqe54iypxlXhmZidjOTyMqMax8zmU3MBHYwE6J9VQzeVREYV0XoWsbYskwQflU1MCtDVH/GlU2Z2K5MNijj2p1MZmsygX3JhGhKFT/khNzmVr1AjUAIF6p9RRtyRhXuAhkRCOxEJoEVOSMvckGakcln4vvZjlxQfuRqNiTXyJFc0JbkOnuSK2RKLpArmfBaMPAlZ2RMIChnMvlcxJe9yQVlTq5md3KN7MkF7U+us0G5wg7lSrQo4+BRxsCkjKFLOWSbckX4lIlgVM6oQF1QVuXqgfpls3JBu5XrbFeusF+5Eg3L+IPI1a1o1yvWiolwrdoxdC1nZAQukGuBwK5lEriWM3ItF6RrmXwmvp9dywXlWq5m13KNXMsF7Vqus2u5Qq7lArmWCa8FA9dyRq4FgnItk89FfNm1XFCu5Wp2LdfItVzQruU6u5Yr7FquRNcyDq5lDFzLGLqWQ3YtV4RrmQiu5Ywq1AXlWq4eqF92LRe0a7nOruUKu5Yr0bWMP4hc3Yp2vWKtmAjXWo2/6OG7q4RMoGLyK8PsVqMAXlUJOVXF0qdG8Sx9L3tUxcqhqpb9qSrkThVrb6oqO1Pl5EsVkyuN+HUi4EiVkB8ZVm40iucphuxEFSsfqlp2oaqQB1WsHaiq7D+Vs/tUHr1npOA8IwHfGQm6TkXsOZULxxkl8JtKqLIqVl5TtWbNsc9UrF2mquwxlbPDVB79ZaQPKeu2qU2fiR69cJUx19FWDFHhGidjcc7OUhWwFkPkLcaluVT1LH8324tx5S8mZoMxiRzGuLYYk9ljTCCTMU4uU/nrjMBnDJHROFdOU9XzHFH2GuPKbEzMbmMS2Y1x7Tcms+GYwI5jQrScisFzKgLTqQhdxxjbjgnCd6oGxmOIas+4sh4T25XJ5mNcu4/JbD8msP+YEA2o4oeckNvcqheoEYjsQt8N9FXcip8tqDoGIBHSwvUeYiALoiAVRvEpLISmkFq+jnbV9cS3LJ0che4CxwRzWrsLiKYcFBsIMBsIsHEge/LDGPdT34pu+gPGHZDw1h8o7kCjo/4Q4g7Mugts7C6QaJs/jCXvW9OwtSv0575VRwcIuux0/3tsdXJ3ZPzJNUOj/2L4DFEMjVMgjatomphDahLF1TgH1wSOsAkxzIYp1pVfZDTNCEJviOJvPE9ClWgmKk7TUV4IjNNREU9H5TwdlcvpqKKYjirxdFSepqMKaTqqQNNRMU/HyC8ymmaE01ERT0flYjpGiadjxDQdfx1n4oVv1V0BqvEHFEIPHDoEtAYckMUamIUZ2BhhIDW4jnbjPPatOgJAdQSAwgiAwwiA1hEAshEAsxEAG0cApI7AUZ2tJ48N2UyN7Kdxqo59Kw70J5wqQGKgP9FUAY0D/SlMFTAa6E8wVUDiQH+CgTqxcTraxK08zE1jTBs5pk0eEx+SgSJGuxGj3YTR/jzZn/Kc+FY8LipIHAQVng6CCo0HQQXJA8mi0OFRYfV8BlA8Ftqhctzy1LbsWMhRPYFBFA6PnOPhEVB7TTRgO2py5MdGzvzYyNhyNwLfskg7ipF2jpF2apF2xJF2xSPtzCLtyCJtaBPivsn5oc47fp6oU46fJ+ls42eR1aCI/ODTi58nfGaxI70tUGUrLtEFpYU2vIsf6oIECgGpKhrUJAeGGlCMSNXhokYcOZKpyEileosqJD8JVIWkUkGyKmqTmuQy5Qa5YqkFFS+pXMckc0lHGaqbBCp0UlXNU5Nc/tSAnIBUbQrUiP2BZLIKUsk1orppJRJ7CalfLyThMNTgYCE1fIcaHS6k5EYkR2OKIngUCWRXpCbn+mWC1/DKVrx8t0fiyt1O2B3ej5eddptTO0bdbZULWce+aSUODOvScfwFzUE6jZLgfo3nl0m6vPPLRF3Z+SW/o+qIgnDwHVVTMRz4BueLiDAw+Q1OFkSIqtaKU9BbYp8DwWFrv/X4S8wriCAJFEdWVTRjG4xpVCCyUcD4ksJRJlnEOrZoRVy0Otykb4WS56BdwGOD0V5xDgxR9J2ruFcVI14ZxLoijLIxjq8JIrJVa8U06C2xz4HgCBpPsRuO08oJ5lPfirccCop3gwoSNyAKT/ceCo23HQqiWwqF0d2EwsKNhELqeunorZn5Gc45ojDdLlyE75mGrXdhy6/QnE3SxZmzibous6P13Nd3aee+I6oWA9NgiObCOE2IcTUrJuapMYnmxzhPkgk8UybE6TJMc4brDoWBZ6+x7pB6kb97mtG7jGBa00LEPE9wlWiWK+apDi9TwXxHTpMeRZr5KKrpjy1yDkSdEiGKnA1R5ZSIasyLqFFypPc6VfQ4TQ6916maXDT2N23wdw0O+aNfb5RizqSgUzoFjXMKXkSBjEJK+YQSZRNKKpdQz5mEKuURSpxFqHEOoRYzCBXKH3qHLceJc6f9DltucCH3M5X0naSQMerVLiHlbAGVcgUUzpT6pgCkiSHKEeOUIMZVdpiYU8MkygvjnBQmcEaYENPBMOUCvuxDYeAsaLzsQ+pF/u5pRu8ygmlP78YwzxNeJZrtinmq47k5zjgrNPEs0/yzrNKA2+Rs4BaUFCxzbrDOKcJ6zBRWKWFIftuMKadPklUWUaOL5n6nTeVdU4EMY4USjeWcb9SC0o5Uzj57uh/yzhllnAuUay6oLHM155drlFkucE65wtnkSswj55RB4UUejghnTetFHpYvxPdPBXsnGORFft8lCTkXTKMsMM7zX083YfoN0ewbp8k3rubexDz1JtHMG+eJN4Hn3YQ47YZp1vEaBIWB57xxDYLUi/zd04zeZQTTnS5KMM+TXSWa64p5qutTYzDVhmiqjdNUG1dTbWKeapNoqo3zVJvAU21CnGrDNNX44CeFgae68eAnqRf5u6cZvcsIpjo9J8k8T3WVaKorpqn+bZzl8cmE33CGkdXZRUZP1rkQHq1z7M/WOYNH6BzCM3QO7SE6R3UGgflzMmUrXjErKD7RWJC4q1J4uq5WaLx/UhDdDymMboIUFu58FBLvKv4G8zZeTdyh2KDLg7L7iIj0oDo5qHCbEHAeayfG2omxLkOK2f0+QOKRr8LTrZxC44NeBcmHw4tCT38VFh8JLyg+2/UbVscY/dcTfMS0bMVHTAsSj5gWnh4xLTQ+YlqQfMS0KPSIaWH0iGlh4RHT155GPow6tD15M9nfzYet+GxOQeLZnMLTszmFxmdzCpLP5hSFns0prE4RoPjY0ZvRn2GrZj6i4MounMetPN7zxnjP5XjP83h5IkER4z2nZ5HewEQ68WXkzQQfMnwzrhSuXcal+Q2tDyOtVzFh9g1RSIyruJiYg2MSRci4DpPJHCsTKEGMU5bgdWhGlC+N69CkngvUiJXMIRPbseJsMn44VimvTODkMiFmWL7UbghyDa+rUyvOOnVdfZTqg8SQeoYonMZVOE3M4TSJwmlch9NkDqcJlHrGKfUqfysQpZ5zlXpVPReoESuZeia2Y8WpZ/xwrFLqmcCpZ0JMPXy0nTIEUg8fbadWnHrq0fYqpefYjqXAoT3wHJtuIsKsn2PTaiPkjefYtMypqp9jk+rbpsDJe+h5B9nmvCkcjLlO6tjkazFPCR7V/5+Y52SPckr5KFPipwdBZJZiEaTnQOQnUkE0nwLZNximu5z9vfSt+g2A6hkToDApwGEPQGv4AVk4gVkMgY2BA1Lz15G/oPoWSxiQONV4S8UKNJ5qvBVlCQqdarzFAgQUTzV2aHeO98K34rsaBcV3NQoS72oUnt7VKDS+q1EQvatRGL2rUVh4V6OQ+K7GDl0tFzTyeu7qbXafeOZbdZSAqrEgwlECh1EihVNXwHXwgGzwwGzwzj72nz925Zzr2NgyjGqZZ2vZmJqlnJplnho+nQVFTJqdzgLKM2Sns45WcSsPZBW93IV1dzvPU74JpbjJ9rFpeMVGesUmewU/kgqKcJGNcJFNcpFtmPA+buUk7XPm4buILwlRENK7iMxVhNS7iCxRrPK7iCxwbPhdRMbktXj8fkqIXFcfv7OY/TcdvzPXTpyP31kgT07H78TBxQxRrRgnnzauHMHEbAsmkTcYZxswgQ3chOjihsko/LXPhQodmXrFXa4Ftnfj5PHOhdGb2K45Zfmmke8bZ/M3gVeAKqRloArLHAxeEIwfygGxNJjUyIHGImFyK0V4uTDeSAVeOCpfCdQYul5HqioWkyrBimKo4ahybTGx7Zy8yhjXS43JLWNNi44J2li3Odt6gRrlpFajcKCPa1IUOI5R5fUpqjLWsYmIeGzAcY9qCm+UU5CjTKGOIq9k6XLAqRR4VTtwOUA3ESucvhyg1cZq17gcoGVe+fTlAKmi7UeBiz6qvCJGVXpibCKcMTZgf4xqssEop/UyyrRqRpENM6jsaCTGdTS+SNeq5bSmRpVXVlLV+hqbfM1L5FobW/CKG9W07kY5rb5BzmtwfMmuFc60Hkf16xmo1ubY4GAGttbp2OhwmqY1O6oHEzGt30FdNYWDYWus6KGNWtdDA1zdo3BwbdIrfWzytdUnrfpRbaz9sdHhJSofB0T50BK1bdVA3xQOWkM+Sjif4BM953g8ACg+x3OeVn7g6XriOa7xgOiZnfOwmgMLT+qc47rtqNroiRH6IZR6PRnH2nj1xjmN+tCrNy7m8TdevXHOkWi9euNCjEnj1RvjFJ30ysrIG6+sEKdgHXplhUQVtq+8skI6BfDgKyukcigPvLJCGgVVvr2hIsjhlW9vBEqhbb+9ESQV1oNvbwSVQnrg7Y2gcTibb28EhUIpXm3IseIw5lcbHFEAG682OFeha7/a4BIFrfVqgwscLv1qg2MKFL8SQKHgEDVfCUgKBezwKwFJVuH76isBqQUF8yuvBCSdQ3vwlYCkUqAbz8LruHLYxbPwwCjUrWfhQVDhPfAsPGgU0uaz8KBwGBvPwgOn0KVHxzkqHC77iW0IlzMKlwsULhdUuFzN4XKNwuUCh8sVDpcrMVzOKVwmULiMc7jGXw6GYFVCoaqYAlWxClPVcpCqQiGqmANUOYen8hicSik0I6bAjJTCcjGG5IVvxdOVCwwFIHG2d0EhABrP6y7C0IHRNYQLGDKQeJK2Q/6zzGUrzlxB8SzLhbO4FVOhIDHfhae5LjTOc0Hy94KLQrNfWD0/BRSnd4d20/rMt+IpS0E1BIDEdYvC0ylNofH6Q0F00aEwutJQ2DhjQOoIHMXT2YtJekR7h+Kguzw5dqUGkZ6vTs5XuBADOE9jJyarozLdMbu44tm5u6Dy0rfiKXlB4jy88HTyXWg84y5InmYXhc6tC6s5Biheyr2Y5Ke2dyxfiNjRTZjZTc7GTSP1NjL1Njn1+DICKCIpNyIpNyEpp6PrwVbs9RRdD5AYyJRcD2gcyDS4HjDq7hRcD0isoekEH7iboncBEo95Tcm7gMYHuqbCu0ChR7em6F2A4oNx09G7Tn0r3gyYoncBEjcFpuRdQOPl/2nwLmD0q7VT8C4g8Vr+FLzrCRC8Cj0drWv/I2VTtC5A9nYJoPwLbVOyLqT4donj+BNt02BdwPztEmNmXT7UZUi4ZS6SZaMilrIilrki2LpAEbVi1gUoFwZdqJ2Sc/m87Zzr1MZvzgUoJp5zTDynlniO+GaTK56SzjwlndWUNNKHeupz3fepvi9Hwxt/qekSHQ+ZvZEGLL6IAwK+iQPYXsUB5m/cAPRXbgDWd24A2RtpznbW99y34ot8l8n6gKd3+y7R+gDRxIFigwFW8xJQ7bajmS2wl2h9gOLN4stkfcDTscElWh8gOgK4DNYHLFxHv0Trc1RL6CmQW/xl5svR+174VjyfuETvQ5TPJy7J+5CC9wGOpxmXwfuA0WnG5Wh0MARzOmTq1cxL8jrE9GrmpXA7lPitzUv0O2T0hublJP8Y9iVZns/XJjbaiIFuWgPd6IFuxEDZ91BSA3XnQxhfT7206/RgBukmRBLY0/RtiKQKd0s3IpKQfC7fikgKOV66GcECeF96x4y5ckH1jhlL5Ietd8xYZmdM75gxJ4+sHIzSELmlcbJM48o3TczmaRI5qHG2URPYS02IhmqYXNVvMoVS5XtPXANgc4bIaY2T3ToXnmtiNl6XsvuaRhZsnH3YBDbjKizFoJMtmyAty1ThW6axeZnQcDDTk42ZwqZtAjt3upPIgvDwKm1E8+TmJhyMj/J101rxaTm86c34ZK83hQyfbvlVJ1T3/JTGzt+866caCP9X9/2UllYBeedPibQWqHt/QoMVASktCiipdQH1vDSgSqsDSnqBwBa8RqBGywRKtFKABIsFUlovUKIlAyW1aqCeFw5Uae1AiZcP1HgFQS0uIqjQOhJuBgfHELeJRYGBaSOlNQUlWlaCJFYW1PPiEtS8vqBMSwxKvMqgxgsNaEsdkrTcoCYdFRsIU0WZfRW1hrVik+SuKPIChBqvQepRAaGJlQjUjf5QWo9Q+1oA1aqE8oEAttYmbHIogHmFQjEuUkM5TfxXQsqW/66PoXj/yYXd3yTc/5WH3dY2bPl1nrIVr/MUlK7zVNfDHhmibhmXfasqdLCibUZ97gH313ju9Ngx7LQh6rRx2emqQqcr2mbU5x5wp43nTodnlaDnkVP3oyjHEJrAQALfNnjf6B+PK4p5cJDuMDSkNDCU5LCgAQwK6FbSXvaJh4NSHkx9zAdGYoiGYVyOoaowgIq2GfW5B9xv47nT9tgH9NoZddsF2W+ToePGtoL1oh/cdxdy5+0hDOi8M+q8C7Lz4c/Tjx0Nf56eWS/6wZ2Xf55+1MYHJaDrlVDHK5bdhr96PXYQ/up1JH3aN3dX/NXrUam/QAe9NUTdNS77i38kd+we/pFcQn3uAfdZ/ZHcvfR+oAvbc9ny4wRDqpdF8IObijbhq+nv4b1PxxrAZd/o7+G9FwcUoNCN0Pfh8AFY+LWK92OkfauPW3kMOY5XA/VA7LY+Be2T+gGRqzH4sBX3dZWDD0K8xXs1dtx70MeZvKKOj7QeC3zMCIZgSPamqguBaETGD38RjQ2PbaiTPEp1bDNK9uJrRjBUQ7KHVV0IREM1fviLaKj4viR1koeq3pes0nBat1jMaLAGcbgOdT9NX0jIg3bla1/HAzelV11Og3clD39/cjRZf55d7T5yOtJywp3/bM1xlhta/MLh9GxybTstW1f7v10LyE38Ovj3dR2ob9kIHeHQ9nTcA+7YEO298of86W1GvUDUI+OpW7uKG4O03zleSj028hA+sA1bX8JWH7diR1J97yldpx87whd2jyN+yJ/fZvQlo14g6qb0or1EPz4w9pVfTz+O+CF/fpvRl4x6gaiv0kxGSbwmUjus3hI5FtpD4+u2Df6lwfsW5+G0zqpGPV+IG0ckrsEcJ+VBftFW0i+S9prSKBonU1X1a3M8CFB4FCA96O/aavxF476BeSio5bHQayHjOPitkOOIH/Lntxl9yagXiPqrzgdHiV8PGDub3g44Jv4gvmIr2BfBesWoy/I0cNT4Gf2xz+kR/WPiD+IrtoJ9EaxXjPosz/722ocJXiSvpItb8aigoHotHFH+AePC05HDnuKflHUcf9e4IPr14sLo14t3bGlHOWUrHjIVJE6KCk8nGoXGk6KC5ElRUeikqLB46FVQfDr0wyRcgq6IDp1OohDozX6unvjGOGwg40whgTgA9jAg9GkCOsYGSA0AoDpHjvykXVxeaF5aqO1gpEbicA3HMTvOAzctjd6VFAKTYhwMUzCMU0TyZeCbxmXgm4OXgSOEMOkfgdBiDNmBn4DQLVL42j8AoRvEUDZ+/kGrFNao3rTCxCEmVQW6/knNY9+KNsN/SHNPP43utHfcT+hOgKJ9Ok+W/QndCRDfA3LFHdSZXVVyZHfK9ij/SoYWaCyHfiVDN8kjbPxKhlb1uFu/kqFlikbjVzL26iKszouwBi/y6ruQ6+4inwct8knPonHSs2if9MQrAvj1+QchtEC7av8gxNig/v2XbUa9QPT16u/P7qXbCV7pLFux2goSi3rhqQoLjYt6QXJRLwot6oXRlc7CwpXO2wn+2d1bHDEg6N2e3k3qTWXbikddd2mwwNMh1t0k3DA2JP9GxN0k3h42RkdZdxO8GVzJ7uD11LbcHsU9FH335C4+4RURBaH1fFcUczjE012R68CoZ7uiwCHKT3YFDMHKt5LvUrUzz7HD37t7Qohip3/vjsUcu/R7d8x17PLv3bHAsePfuyMMscNLLhQIjp265FKl9JtCT6TAcTzwm0K6iYip/k0hrTbi2/hNIS2nWMvfFJIixj0tITKUaQ6aS8jYoN47gzkwRNE3ruJuYo64SRRr4zrKJnN8TeDImhBjivcbTyPqcyA4gu2bi8sJ3llbhnV4t+V/uGkZdrXMe1nqHaB3EYJd4UXck9iqzx/kPbcdbpmucCoOHUlXOE9E+77xPdyvrzw3Aoeu2DV5uRIpdEs++xEodengsx9LvGpHCLqCV+1OYqs+f5B70H6Kg47FsRekQGdIgT6R0je/jXvIcu5ouF7IDDoXrheeULtefJa7cuCxkXrWgX3IB9OGoAd4fE0f5P2r4+tRQksiBLuvCHafjWvZMK5l27g+T/D84DN+FlA6K6gXzFp3GKPeEuM9RvoqU1+4uug+3Ncv3f//m9NnptYPXscPGa73DIXmN3wjjnGMmrrpG1vEa49BC3ERY1jFsBiuHVJavRostdBZ0WI3t88ErjtUWvzFUtLqTWuthu6oFnnyq+SFMgRp96wHbsUJK6j2EpF1DuB4/f2ZkeugW/o4urF6KFt2KcsRXb8ywV569y9bxq08EHXlvPBU1IXGk+yC5El2Uegku7CYvQXFK+c7ZFfOPWx/hAbrMO51NJcVZhEimx+EjVje11s5ZSO0cv5QL0yu9oYHG+GC7Cra3QjtdrsPzRBNlHFKO+ece3Qvv0ay4uvcklPRnqn2uBiipDQuo2lPSFF6Vr4UqDF+ma0m5pQ1ifLWuE5ekzmDTaA0Nk65zM9O8DT8kZuuc+A4v41TkjvnTHfl0AR5bhtRiQ8nDZTJfSaxDsS5wKjY8xweEUOUDMapGJxzMfBfqngW8XVuycVQORSDISoG4zLW6Y9H0A6WAjXGL4tB/e0IlqgYWn87gmUuhvS3I5hTMaS/HUHT8Eduus6B42IwTsXgnIvBlUMT5PluRBUDXMGiTO4zicUgLl9VJVxUwZKIAidGVLk8SE1FEnUqlSBetz6Vyibfr3uqBC6hg/frVJtUTukGlxYORlAXWPMGl27AxXbwBpdulApP3+DSKhdhUFMpBvWP1sfWrWlIxRlVLlFSU6GS/vU0gLqMXJYuXwqV1de3OBVz6zroXo/Xi2qYEOUHEj0gATbuAcJLjXQKPG6Vv905vuhnyJ/1IU63yIN6YadQlUwT2f0JyvHM3JAlB3G8EBClevY+npa/yOKo7PN3mMOJO1rZigVeUDUbQKLQC0/VXWgs6YKoRAuj+4mFhfuJhcT6fADrfWFk518nvhVvOj4kpwKebkY+oCcBIiMCxX9xzVm1HEB1HI7op8u2MLRTI27N2+zH24YJb6XzbrPdbpseuxXGus1uus0WusWh7Qeyu4Ls9x3KVry1UVB8rm6P8o2OwtM9jj1Nz9UVHO96FER3NAqjmxn9WCsnvhXzqsdaASRSradaARpTrQ+1Asx/ws/ZWCtAYo71qVb6MA99noc+z0PfmIdezkOv56HP89CLeegb81CK4KltWRE4ikXgHIvAqRWBIy4CV7wInFkROLIiMET1XRdEzCpDlFrGKb+MqyQzMWeaSZRuxjnnTODEMyFmn2FKQb7MQqGAdDBEGWmc0tK5yE0Tc4K6lLPUNEpV45yvJnDShms3TyOi9G1cuyExJ3K+dkNcp7S4dkMCJXe+dhM5pzncpINMR0rJjhLlO0oq5VHPWY8qJT5KnPuocfqjFisAFSqC/C6IiBWkG1KqBpSoIIIkagL1XBZBzZWBMhUHSlwfqHGJgAZVgpQKBSVVK6jnckGVKgYlXTTYgusGNSodlKh6xGtAY1L8OYHnmP+EHAASnlj+k2ccMJ9n/UnzCzQ8hfwnziag+Lzxn+DjTGKn2cUTzt0XHp6UNBB2cMY0pOTfI68nm10mcVyG47gc53GZlsblShqXSXFchmlcxmlc+JJUp2kcX5DiGKOUxxn0NNaopvEGOY45SDTuoMHY//O//w/7Vd1G"'),wt=JSON.parse('"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"'),St=JSON.parse('"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"'),Ct=JSON.parse('"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"'),Tt=JSON.parse('"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"'),kt=JSON.parse('"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"'),Ot=JSON.parse('"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"');var Pt,At,Rt={Courier:"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","Courier-Bold":"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","Courier-Oblique":"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","Courier-BoldOblique":"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",Helvetica:Ct,"Helvetica-Bold":Ft,"Helvetica-Oblique":St,"Helvetica-BoldOblique":wt,"Times-Roman":JSON.parse('"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"'),"Times-Bold":Tt,"Times-Italic":Ot,"Times-BoldItalic":kt,Symbol:"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",ZapfDingbats:"eJxtmNtu20YQhl+F4FULyMGeD7pz3AY1ChtG7NpFA18w1NomIlECSRcxgrx7SVk7+wOdG8H5OJydf2Z2d5gf9cV+t0v9VK/r+6vXsXlOlbHe28paq229qj/t++m62aXZ4J/m8PRb1z9/baZxefK63Z6eXN5dVMvTCh83u277xr/6kLrnl2XNq7TpXnczuZyabdee98/b2VzM/x4/dd/T5qab2pd6PQ2vaVVfvDRD005puE3Lu7eH1HbN9hTjx4/77/X6y5lcnUmjVzHIVVDicVX/1W/SsO36dLMfu6nb9/X6TAoBD+5euvZbn8axXtuZ36dhPJrVQqgPQoh5hev91LWLkIv94W1Ygq9+aX+tZAx2tfz64284/sblN/rqfLP/mqrbt3FKu7G67Nv9cNgPzZQ2H6rz7bb6vLgZq89pTMO/M/xfEqturJpqSM/d7GJIm2oamk3aNcO3av80O5xh3yyKmm1193ZIT02bqovTKjP+MAf++7zsZvZ3276kYyWWXB0z99S18/PbafPHQ71W4fjn/fxnFO+ZvkrT0LVzTr78qB/+nk38bHM9exgP8zr1z9U7jt6840YW5uSJKcZOCaBBnKgm5mU8MVNYyMwWFvO7Ukagkmgg6sDWQ5yFFqjzUrLEaQ3BEmiwNsMSaZS0vgWfOkPHWQowNeTUc0kumnxZvsgPxlGai6VTGUqAVCTQ6QkWnc77DKEiLktSUBJKqHIQZ86d8gCpHYoiEzMsb1ubYy8vW50DChB5ZhGqrijD0EqUIeiaEHIfCg5Kpuu0ApiToaGPSY0uaQsyr65L2oKi1yFt1PLaQ3lzfXTgXodGoJYzglndSLDMPg1sTPJpQJHJigw0QrGERqD9YhyTOgONQDUyuF1zaxuokc/BW2ztXCMrGZ9WMW1oQZHIXWNBkSCfRZEL5BMUiZw6CzVSFCfUSGZFNjIldoKDkonTKQiJIGzWmFd3BizJJ9SINoLDriOfUCOZS+zg+KGD1qGiLNMLxtJD1/ns00ON6EzyUCM6vbxhoBKaqbG3DFQCNiL1iHccBPV0DHhQH/JW8EW90dkyFKGywCJU0WkVSvSGeiSUODWFFD0HYdPQVoiRgfPMA+/nnRgiAyNYSjpWNQcNSMrtFCUH4ZIRpSCWocFCSuhCEY6hoUClc0WC52BJlCYYLQdhN+hygRRRlo5BKRRLS6oihSqh+ZzzRGG1Mo4Iz1LoP0qsxDGFzk0JE42ji0jCPejomJKCuwil4m5CiRMEUMVSzVLDUstSx1Juc0oVWMpqY295qVltmtWmWW2a1aZZbZrVplltmtWmWW2G1WZYbYbVZlhthtVmWG2G1WZYbYbVZlhtltVmWW2W1WZZbZbVZlltltVmWW2W1QYjQCh7E2aAQHeGhCFgPoNoy8KNb2wxBhmGKBxoUZXlLGsLI6AsftEDHV0wIURVbANLcTKlGGBIKPOAxCmhePCKUwFzAmpDFRQvjA9R06Hq8TONvshgKDCuRAZTXigUxjxNFfKRo3CLhnIJBMFRvMZpqpNBMlQJzGT5WFQMVQI/AikPMIhEU1aDjqJvQwmjSHB05cC9jbYwc5UtAHNLhDw41ha+lEqF4JaH3gmB61SYcqInxTDmQK8v08vjqv4zDf1N0w3Lf4A8/vwPpfK11w=="};(At=Pt||(Pt={})).Courier="Courier",At.CourierBold="Courier-Bold",At.CourierOblique="Courier-Oblique",At.CourierBoldOblique="Courier-BoldOblique",At.Helvetica="Helvetica",At.HelveticaBold="Helvetica-Bold",At.HelveticaOblique="Helvetica-Oblique",At.HelveticaBoldOblique="Helvetica-BoldOblique",At.TimesRoman="Times-Roman",At.TimesRomanBold="Times-Bold",At.TimesRomanItalic="Times-Italic",At.TimesRomanBoldItalic="Times-BoldItalic",At.Symbol="Symbol",At.ZapfDingbats="ZapfDingbats";var Nt={},Dt=function(){function t(){var t=this;this.getWidthOfGlyph=function(e){return t.CharWidths[e]},this.getXAxisKerningForPair=function(e,r){return(t.KernPairXAmounts[e]||{})[r]}}return t.load=function(e){var r=Nt[e];if(r)return r;var n=xt(Rt[e]),o=Object.assign(new t,JSON.parse(n));return o.CharWidths=o.CharMetrics.reduce((function(t,e){return t[e.N]=e.WX,t}),{}),o.KernPairXAmounts=o.KernPairs.reduce((function(t,e){var r=e[0],n=e[1],o=e[2];return t[r]||(t[r]={}),t[r][n]=o,t}),{}),Nt[e]=o,o},t}();for(var jt=xt("eJztWsuy48iN/Ret74KZfHtX47meqfGjPHaXx4/wgpJ4JbooUU1JVXXb0f9u4JwESF13R7TD29koIpFi8gCJBHDA/Pvm+nraTuPmZ3/f5HHzs7/k8WlzvXS7fvPXp02eqyR/2vRfd2N3gqhUUfm0Od9P236+DoczxLWK66fNpZ93/fkGWaOy5mnTnUR67c57lRaZSItM/tnN/XnsX/DfIqg0JOk8HI4UK4BCAFzG+xWCQgXF02Y3nU4dJJVKKrx5mPgKBVMImOvYXY+QKJRCoHzXzxMErQrap810hqaloioF1e0L5kvFUwqe23Hu+Q+1TinWeZnuMwSKrRRsL8Nn/kOxlYLtOnzFWE1Viqmu/eceVioVaylYe1OwVKilQD0PCYgiLRtVcJz4kEItW13mNLi0UsCVAB77KyxTKeJKEPff3rsREkVcCeLD3He3HqArBV0J6G/v/fU2cK1WH23l0e3c7T71N9uUVv/c5i73bWlVs1Y0u5/3srO7aQb2EPUB+eUTva0TYgG5mGbbzZSUkJTpn75ygF4PThhq1SMGMds4HYZdN54n/rdWc8rv02bfH9I2hbqGsKbPnIYzHSc0qmTIxI6nuwpiAIQmU8F4Gy7jK8RwntAI1v3wedj39FmFECp508s4zUOyGmwpKrwbL8eOIlVU//Yf/S1J9C212Pa/uuSwbVDYlWzxf/aj/UtfWgm258t1GG1X1BVawfdnX0xdoRbjPCdBVGs1svo3R/tPVD1r2YL3k0kUfC04f9ldLkmk0NVwv+pO232SKXa126/vHAO5wPxNGivsRsZ/HDhWzLVg/iBuOSfMUTGrTX+b/qSIG0H8u+NEl1J4jcD7/XBI9kDcUYN/0/FNCDuNAP64skYOeLrykUsjElWC9+cmAEAB9NtrEijCplaE/YHvKuC5Iup8zxBAWtFrayakC2QC8uCbhggSskx9zXYNQSRkeuZWQBFKQowabNIfS/qeqOgSOFTINcC4DKcnE70H2zqElJAJ3k++dwgrIRPA47J5iCwr724RWELINFBTAAWiCL7SOogrIQj6abWBOH8hCPoL/4a4EoJgn9MWIq40lcY52cJAGbCHMgkpA3g9t7e0sRWgB1HnvjJYRez6yrSTlYJvRZmdCQhe80Pa24roNYL75uLo10WyKYHVeFLjYnImilM0qPDOJOKWNGlFCJsIrw/qsNv7OPY3SnNYSQ9DP46DLHylvGCcEFU08Nz6JIVx9Chd+93ENNhEWroSuC8SAi0WNznNpqH9+c5k1RQ0nIbi9/LnTzdmoKZAaAwaib/0g0Ti29wxG8gUgLey/O8eHmmqt4eiKTNYo416LPrLkcIWa2u06eZ5+mLBXCaoTp4m7pckBm41P8Qe0mUG6DUCYWY/fTmnCQbwkCa2043vrhA2gqakncwM3aGfe9GAj1Vw9qiuzPW2o4Or4PcxhmUu4atwAGKMy8wCscJhiDFfJh1lhY2K6mo250DrTJXOC82EUgVIkTMmOd0moqC5Dd24H15e0hRKJS0Cvg7Xm9RKgz9ErdWrTpfb6zV5Wx2ytwlDZLplUQ/8Ye72Qyq5RI5kqY4t6fe0iHOItdCYbo8zKOi0vLjvjrdjZ2IYRAPUZZ72910SI7vEiL9LaHSvrZFkipKOf02y8gc9vEbmKHQjRP95uH6ShZI9c9pao41otTPLICMETXSC5jLNupbP8bxo2Dy/DOfh9prk8BKNk935MPIo1jiKUSNQqiVSVSozBWYan5nmNMGz1+r6AleO8KJJwXdk2H8XwgVVP31AticBhdvqIZPwNPcvqWhqah74iIB6GsYuvbdGeYFS93yY775hPNh6giUlzNNXr/eaJmNYKrnLKznOt4ZsEQ6f5ZCfWVvJFK2Xs5BcP8ND23r5uJqDyaPmM90Oscl9a87aIC3HLCxz+uOzNFgOhA+P4XRq8hPTjP3Xhzn4oiYIm1svybSpOX03zDuJX4kqyAx3rrKZdZ3XNMggGh9lsUt/Fm+7m+1bGCxqOttPN/fOFiExKh+xnb1d0gz8qiiXmS0r5YxLaaULN/TaOsu4WEgTS3Fd1TCvlsvj9F1/PvQpPzHAZqiN9yZEntcyaDfet0mGOKLl5LGX6EMhU5ZGkf3QnVIWqvJA5FoG7KbLK1BcBcyLTfNYZGr7g8ar+WEWm63VgmSefX/q5k+r6Rplrdo/Heb+q00gKzcWUiVy3pY5RkGL7kept7/zSRS8Uc+Kw+nOV5ukqeu1KqtZ2Ds2a6yrWZghX/NS7q3OwQZ5WM0tgGCBPK7muPM6B2fP8wditayKMKG5YzW7rIvzkJcPs8vKOBGaRJxo+boMocrFfe407G0SJlJS7pO+KOrwqKkAcw4lp28Xi28vU7AM2Lfz9gUITKM8fJlcnoRtlJIvkwsSRtD2kXkuC8M2ytbX08vSME4ZHqd9cTQgojL5hXr60uhDxDJfTy7WQ3kXy2I9q+t+L7V+d3nZD+fDtrtdf7iZ8gPUNhVNSLOdFKmrqgg5UGR5ktUWkERW4ETnYSnQpK5PsqU2k3I5yZbCTGhJki0lmbJ2ypxOd8rYKXM23Slnp6yxclZkVZK1li1EVlMWmY0yyJokC5bIRdYm6sDCW/9X54knZEYnurpKJCEzNtHVdYqTmdGJrm6SiJRMsdWJmTS1MYWuSZwAHg3D5dSJO6tnpqPiNXIHapSQHkL9WNCyDwEZymTtQzyGcfx/rQVukWUP4RgGS29oG5RieEMSVKm67GISoHZUs0g6TKImlZMdbde2cDMFUCZBSBWevKlNIlRrBNQkEVpt0CXUSYTWGvzG1q5TldeFIklgFfiMvQ6tNXgMtk5IM+qSAjbJSpOh4wdUtYnQYgOqxkRosgFVayK02SJsYCJ02tRw9HkVodUG00UTodcG4+UmQrdN0dPhVYR2m8KPBhX1t/bkumgaofzWplwXDT2Oo9K2Lhp6dogUvT+HBpGC98fQxlDs/lSVCr/OVGZ7CGY3lXEIKyD3fylyrQS63P4VjTl0uRkGJxB+l5th2CBS5LkZhg0iRZ6bYdgPUqC5aYMEh8CSmzrsCinU3PRBKkNYyQ0qTgSiSmFQcSAQVAqDimSFmFIYVPaKFGphUNktUqiFQUVaUvLVFbaHSEZK47vC0LNfpOgLQ8+OkaIvDD2SjZbOXWHokWBQgJeGHkmlwaEz9EglKHFKQ48og8qmNPQgJEp0u9LQg4mAjJeGnm0rRV8aeratFH1p6EE8tBnQlYYebSutwLrS0KNrhRZYZegRbpV3dpWhR8tKSU9XGXr2rJTsdJXBTz0ruLjhT00rVaAyBVLTSjWoTIPUs1IVKlOBbSulAV1lOrBzpZS2q0wJNq8yhH7TovIOb1cb5tSXUny14Ut9KUYQUyS1phRgbaDZmEIiFrKThCnpIMMYGrZh0JBo7M01e+H65sZeUpPp6ZsbX4+dcH1xa1YgxYsIAWYF9rXBI1p/L9tiiL6ZmYGtrYpZybaz8caUCA1iA4iIPcEN0ZAQIuq70g2ZPCOQ7R+yE5riIjTojfMRESbsge1zHMhgsSlk5PR4u0WnQDraMOdEE7JTj7dbhAqpw4K3W4wKGZv3eHtempBkA+nHQldgrwXHM1jwCgj0pB7BwlcIbI7BnhbAAmsvHNJgISyw+MIxDRbEAqsvHNRgYSyw/GqZSE0j1l84rMFCWWABhuMaLJgFVmA4sMHCWUi8CRpZQAvkSzizwUJaIE/CoQ0W1ALpEU5tsLDGDzqg6yI0jaKzfxGaRuRBOLjBglsgAcpYHZhG5D04usECXCDdQd0WLMQFshwc6GBBLqQOETSyMBdIa3DMgwW6QD6Dcx4s1AXyDpSRYmoTsrpmzWKQyDJw0GWjTci2GCBZIAtkFDj+wSJZIJPA+Q8WygIJRCQkw8meFCJAsGAWCu8BiNAsjzTAXkKwEBfYg2IQqM3y7EFFauT/ZAcUGlk0DAU7nyzETPeSHBIa1aZmSe4IjWpTsyRphEa1qVmSTFMjU7Mki4ZGreEsSZ+hUWO6s7+bc4/8cdJlaNSYQdjTRbEbM3+c5BgaWTgOSA7stkSLiqFiCwbgLUiHinQX4C1Kh4pEl+BN94oEl+DNdBWJLcH74yS0AG8RPeCjRmRZ3JiR0ZWKrItbW7MmZWVlbG+vSVWxHY2tyW+lJTUy0yEVgdTKmmYlNplKagSDCMFlTIaH8GmVMWkpIj6sMsQv+Ae3UmUIX3AP6q0yRC94x/IOBC84B4+VyhC7yHTIELQRhGgM32hchmAM14hMRCpEMIZrNC6DJvAMWkxl0ASOQYOpDJqACrX+EmgCX9EQ8f3T5stwlggXf/otCfss8O19uvX7LfqmP3Z1AiRPP2JPY2pA/vTbFIhHqhFedB2s0/2v3bIAG1z14yH8CVcvwJFFoePr5cgbDv9/G+Pfvo2BUIP6ix0r8EO9ZYARuKFeMMAIvFA/gWMESqifiTACG9QrBTpCBFGK9wuMQKz0UgJGoH+C7L8xAvPTL40Y4au7gPkfjEAB9SYBRmB/eokAIxA/vT6AETifXh7ACHRPrwroqAFX0i/5GIEmCZb/xQj8Tu8LYARqp5cFMAKr03sCGIHQ6SUBjMDlBMsfMLIP//+HERicXlzACORNsPxJR2iW4I4FRj92EQa8TTuGInY3/vHrMSBwuoPX3TDot4c7osKPXJtBm0XLvsPc0XfRZkHNhxE4nLZsMQJ902/jDOQIkriXkAL7JhEyNh1ZemtZ98IxCZvebeCYZE3AHjkmUdMPGRyTpAm6v3FMgqY3EjgmOdPPZhyTmOlFBIwZxHEPgWNeJ9BbBxyz+af9c45J2PRMcEyyph8EOSZP03PMMTmaXjLgmN0+vWLAMfBpFfeZY7838AVjNilxLYJj4NOy7ZVjUju9zcHxv3/FiVcKULCpf9yGcb9qEOPL/6pp7GyO2cU+S7N2AaOzDMHKBXxO4/goyYBiZ3S7+yxxf0fNKud0r31a0gnddp4+9WfTpHJOt/r4yfIlfVDq5z7dgWABg8amf4SBnLxZQ9A0718keFqMZSGDNurhPoxjf5r84LGeQY/77d0vb3QvyYc1DTrd9nWo56movd196uyqy792faz2prfkJHyAHPiBONTe+kZ2ephrlhb4Ll0HSRfRNOLxqk5onB1LWu4kCPAGRmicIDOZ6j67Ro0T5V2/F6t1lDpTlkz6iMTpspj/JI53H83+jZNmt/+ybY2TZ1lRctmcUldonEDLxLEbGV5aZ9AwRnqAJmydSFu6c2dunU6/8yDIL5Og0+8W67VOp98xsL6kr1H8FglO/W45Uq1z6ncPXto6rX432zlpnVW/e6bAGfXPV0aOmXPqZwcbM+fUzw42Zs6pnx/BxsyJ9fMaV8ycW79fre3c+v1qbefW79+u7QT7/ePazrGf+UE7Zk6wf+Mmi8EJ9ocFQnCC/WGBEJxgf3gDgddNNIp/WC3Mb12i24cHXIEfkcs3FzGDM/UPnnJjcKb+cQXOmfrHFThn6h/fgItO1z8+4IjO2P+0LBOdsX9znHgBKUYn7Id+Pkklvh3TCgtpX9DFhbSvll1I+1t0C3NfTBcX5v4IeSHv5sYxX7g7H86dt+/Wbpw7c+8XsLkz934Bmztz79+AzZ2+9w+4cmfww2ptZ/DDam1n8MPbtZ3GDw9rs9ui3KZPblw4tz8vJiuc208LhMK5/bRAKJzbT28gFE7wp9XCTvCnR1zO8ZeLw7Fwjj8tTlw4x78v0Ern+PcFWukc//4GWulE//6AonSu/7paxrn+zZ2YnRclRK/rBXJsCAjxh2cKEAWVJ02ku/wOoFv2+12XkmnODwHgW4uQGVbZ0uM7mAJ1b/68/JlpUMnWdy5MF6/Vd5eL19YYSPd6FqPwBkNQo/h2NQxdQQ3bn/dpCxrGrqCW7U8rKZl/mfi0Xytk3Am66ZhYbg4y+KAVslDwbXdNL2d5qU5hnYBlTZaa6hs2t1qWdaeeTptcLco+hl5R7w4H5uOGcQbtEkpT18GusOI2xT9dYcVJf7zCSjmbD+Iud2s1NPRb9E+0UICmizb8ZK/+5JOLOulSqwaw5VJr2vB8dSFn89fvv/8H0oq1dA=="),Mt=JSON.parse(jt),Vt=function(){return function(t,e){var r=this;this.canEncodeUnicodeCodePoint=function(t){return t in r.unicodeMappings},this.encodeUnicodeCodePoint=function(t){var e=r.unicodeMappings[t];if(!e){var n=String.fromCharCode(t),o="0x"+function(t,e,r){for(var n="",o=0,i=e-t.length;o<i;o++)n+=r;return n+t}(t.toString(16),4,"0"),i=r.name+' cannot encode "'+n+'" ('+o+")";throw new Error(i)}return{code:e[0],name:e[1]}},this.name=t,this.supportedCodePoints=Object.keys(e).map(Number).sort((function(t,e){return t-e})),this.unicodeMappings=e}}(),Bt={Symbol:new Vt("Symbol",Mt.symbol),ZapfDingbats:new Vt("ZapfDingbats",Mt.zapfdingbats),WinAnsi:new Vt("WinAnsi",Mt.win1252)},Ut=function(t){return Object.keys(t).map((function(e){return t[e]}))},It=Ut(Pt),Wt=function(t){return It.includes(t)},zt=function(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height},qt=function(t){return"`"+t+"`"},Et=function(t){return"'"+t+"'"},Gt=function(t){var e=typeof t;return"string"===e?Et(t):"undefined"===e?qt(t):t},Kt=function(t,e,r){for(var n=new Array(r.length),o=0,i=r.length;o<i;o++){var a=r[o];n[o]=Gt(a)}var s=n.join(" or ");return qt(e)+" must be one of "+s+", but was actually "+Gt(t)},Lt=function(t,e,r){Array.isArray(r)||(r=Ut(r));for(var n=0,o=r.length;n<o;n++)if(t===r[n])return;throw new TypeError(Kt(t,e,r))},Xt=function(t,e,r){Array.isArray(r)||(r=Ut(r)),Lt(t,e,r.concat(void 0))},Ht=function(t,e,r){Array.isArray(r)||(r=Ut(r));for(var n=0,o=t.length;n<o;n++)Lt(t[n],e,r)},Zt=function(t){return null===t?"null":void 0===t?"undefined":"string"==typeof t?"string":isNaN(t)?"NaN":"number"==typeof t?"number":"boolean"==typeof t?"boolean":"symbol"==typeof t?"symbol":"bigint"==typeof t?"bigint":t.constructor&&t.constructor.name?t.constructor.name:t.name?t.name:t.constructor?String(t.constructor):String(t)},Yt=function(t,e){return"null"===e?null===t:"undefined"===e?void 0===t:"string"===e?"string"==typeof t:"number"===e?"number"==typeof t&&!isNaN(t):"boolean"===e?"boolean"==typeof t:"symbol"===e?"symbol"==typeof t:"bigint"===e?"bigint"==typeof t:e===Date?t instanceof Date:e===Array?t instanceof Array:e===Uint8Array?t instanceof Uint8Array:e===ArrayBuffer?t instanceof ArrayBuffer:e===Function?t instanceof Function:t instanceof e[0]},Jt=function(t,e,r){for(var n=new Array(r.length),o=0,i=r.length;o<i;o++){var a=r[o];"null"===a&&(n[o]=qt("null")),"undefined"===a&&(n[o]=qt("undefined")),"string"===a?n[o]=qt("string"):"number"===a?n[o]=qt("number"):"boolean"===a?n[o]=qt("boolean"):"symbol"===a?n[o]=qt("symbol"):"bigint"===a?n[o]=qt("bigint"):a===Array?n[o]=qt("Array"):a===Uint8Array?n[o]=qt("Uint8Array"):a===ArrayBuffer?n[o]=qt("ArrayBuffer"):n[o]=qt(a[1])}var s=n.join(" or ");return qt(e)+" must be of type "+s+", but was actually of type "+qt(Zt(t))},Qt=function(t,e,r){for(var n=0,o=r.length;n<o;n++)if(Yt(t,r[n]))return;throw new TypeError(Jt(t,e,r))},_t=function(t,e,r){Qt(t,e,r.concat("undefined"))},$t=function(t,e,r){for(var n=0,o=t.length;n<o;n++)Qt(t[n],e,r)},te=function(t,e,r,n){if(Qt(t,e,["number"]),Qt(r,"min",["number"]),Qt(n,"max",["number"]),n=Math.max(r,n),t<r||t>n)throw new Error(qt(e)+" must be at least "+r+" and at most "+n+", but was actually "+t)},ee=function(t,e,r,n){Qt(t,e,["number","undefined"]),"number"==typeof t&&te(t,e,r,n)},re=function(t,e,r){if(Qt(t,e,["number"]),t%r!==0)throw new Error(qt(e)+" must be a multiple of "+r+", but was actually "+t)},ne=function(t,e){if(!Number.isInteger(t))throw new Error(qt(e)+" must be an integer, but was actually "+t)},oe=function(t,e){if(![1,0].includes(Math.sign(t)))throw new Error(qt(e)+" must be a positive number or 0, but was actually "+t)},ie=new Uint16Array(256),ae=0;ae<256;ae++)ie[ae]=ae;ie[22]=g(""),ie[24]=g("˘"),ie[25]=g("ˇ"),ie[26]=g("ˆ"),ie[27]=g("˙"),ie[28]=g("˝"),ie[29]=g("˛"),ie[30]=g("˚"),ie[31]=g("˜"),ie[127]=g("�"),ie[128]=g("•"),ie[129]=g("†"),ie[130]=g("‡"),ie[131]=g("…"),ie[132]=g("—"),ie[133]=g("–"),ie[134]=g("ƒ"),ie[135]=g("⁄"),ie[136]=g("‹"),ie[137]=g("›"),ie[138]=g("−"),ie[139]=g("‰"),ie[140]=g("„"),ie[141]=g("“"),ie[142]=g("”"),ie[143]=g("‘"),ie[144]=g("’"),ie[145]=g("‚"),ie[146]=g("™"),ie[147]=g("ﬁ"),ie[148]=g("ﬂ"),ie[149]=g("Ł"),ie[150]=g("Œ"),ie[151]=g("Š"),ie[152]=g("Ÿ"),ie[153]=g("Ž"),ie[154]=g("ı"),ie[155]=g("ł"),ie[156]=g("œ"),ie[157]=g("š"),ie[158]=g("ž"),ie[159]=g("�"),ie[160]=g("€"),ie[173]=g("�");var se,ue,ce=function(t){for(var e=new Array(t.length),r=0,n=t.length;r<n;r++)e[r]=ie[t[r]];return String.fromCodePoint.apply(String,e)},fe=function(){function t(t){this.populate=t,this.value=void 0}return t.prototype.getValue=function(){return this.value},t.prototype.access=function(){return this.value||(this.value=this.populate()),this.value},t.prototype.invalidate=function(){this.value=void 0},t.populatedBy=function(e){return new t(e)},t}(),he=function(t){function e(e,r){var n="Method "+e+"."+r+"() not implemented";return t.call(this,n)||this}return r(e,t),e}(Error),le=function(t){function e(e){var r="Cannot construct "+e+" - it has a private constructor";return t.call(this,r)||this}return r(e,t),e}(Error),de=function(t){function e(e,r){var n=function(t){var e,r;return null!==(e=null==t?void 0:t.name)&&void 0!==e?e:null===(r=null==t?void 0:t.constructor)||void 0===r?void 0:r.name},o="Expected instance of "+(Array.isArray(e)?e.map(n):[n(e)]).join(" or ")+", but got instance of "+(r?n(r):r);return t.call(this,o)||this}return r(e,t),e}(Error),pe=function(t){function e(e){var r=e+" stream encoding not supported";return t.call(this,r)||this}return r(e,t),e}(Error),ye=function(t){function e(e,r){var n="Cannot call "+e+"."+r+"() more than once";return t.call(this,n)||this}return r(e,t),e}(Error),ge=function(t){function e(e){var r="Missing catalog (ref="+e+")";return t.call(this,r)||this}return r(e,t),e}(Error),ve=function(t){function e(){return t.call(this,"Can't embed page with missing Contents")||this}return r(e,t),e}(Error),me=function(t){function e(e){var r,n,o,i="Unrecognized stream type: "+(null!==(o=null!==(n=null===(r=null==e?void 0:e.contructor)||void 0===r?void 0:r.name)&&void 0!==n?n:null==e?void 0:e.name)&&void 0!==o?o:e);return t.call(this,i)||this}return r(e,t),e}(Error),be=function(t){function e(){return t.call(this,"Found mismatched contexts while embedding pages. All pages in the array passed to `PDFDocument.embedPages()` must be from the same document.")||this}return r(e,t),e}(Error),xe=function(t){function e(e){var r="Attempted to convert PDFArray with "+e+" elements to rectangle, but must have exactly 4 elements.";return t.call(this,r)||this}return r(e,t),e}(Error),Fe=function(t){function e(e){var r='Attempted to convert "'+e+'" to a date, but it does not match the PDF date string format.';return t.call(this,r)||this}return r(e,t),e}(Error),we=function(t){function e(e,r){var n="Invalid targetIndex specified: targetIndex="+e+" must be less than Count="+r;return t.call(this,n)||this}return r(e,t),e}(Error),Se=function(t){function e(e,r){var n="Failed to "+r+" at targetIndex="+e+" due to corrupt page tree: It is likely that one or more 'Count' entries are invalid";return t.call(this,n)||this}return r(e,t),e}(Error),Ce=function(t){function e(e,r,n){var o="index should be at least "+r+" and at most "+n+", but was actually "+e;return t.call(this,o)||this}return r(e,t),e}(Error),Te=function(t){function e(){return t.call(this,"Attempted to set invalid field value")||this}return r(e,t),e}(Error),ke=function(t){function e(){return t.call(this,"Attempted to select multiple values for single-select field")||this}return r(e,t),e}(Error),Oe=function(t){function e(e){var r="No /DA (default appearance) entry found for field: "+e;return t.call(this,r)||this}return r(e,t),e}(Error),Pe=function(t){function e(e){var r="No Tf operator found for DA of field: "+e;return t.call(this,r)||this}return r(e,t),e}(Error),Ae=function(t){function e(e,r){var n="Failed to parse number (line:"+e.line+" col:"+e.column+" offset="+e.offset+'): "'+r+'"';return t.call(this,n)||this}return r(e,t),e}(Error),Re=function(t){function e(e,r){var n="Failed to parse PDF document (line:"+e.line+" col:"+e.column+" offset="+e.offset+"): "+r;return t.call(this,n)||this}return r(e,t),e}(Error),Ne=function(t){function e(e,r,n){var o="Expected next byte to be "+r+" but it was actually "+n;return t.call(this,e,o)||this}return r(e,t),e}(Re),De=function(t){function e(e,r){var n="Failed to parse PDF object starting with the following byte: "+r;return t.call(this,e,n)||this}return r(e,t),e}(Re),je=function(t){function e(e){return t.call(this,e,"Failed to parse invalid PDF object")||this}return r(e,t),e}(Re),Me=function(t){function e(e){return t.call(this,e,"Failed to parse PDF stream")||this}return r(e,t),e}(Re),Ve=function(t){function e(e){return t.call(this,e,"Failed to parse PDF literal string due to unbalanced parenthesis")||this}return r(e,t),e}(Re),Be=function(t){function e(e){return t.call(this,e,"Parser stalled")||this}return r(e,t),e}(Re),Ue=function(t){function e(e){return t.call(this,e,"No PDF header found")||this}return r(e,t),e}(Re),Ie=function(t){function e(e,r){var n="Did not find expected keyword '"+E(r)+"'";return t.call(this,e,n)||this}return r(e,t),e}(Re);(ue=se||(se={}))[ue.Null=0]="Null",ue[ue.Backspace=8]="Backspace",ue[ue.Tab=9]="Tab",ue[ue.Newline=10]="Newline",ue[ue.FormFeed=12]="FormFeed",ue[ue.CarriageReturn=13]="CarriageReturn",ue[ue.Space=32]="Space",ue[ue.ExclamationPoint=33]="ExclamationPoint",ue[ue.Hash=35]="Hash",ue[ue.Percent=37]="Percent",ue[ue.LeftParen=40]="LeftParen",ue[ue.RightParen=41]="RightParen",ue[ue.Plus=43]="Plus",ue[ue.Minus=45]="Minus",ue[ue.Dash=45]="Dash",ue[ue.Period=46]="Period",ue[ue.ForwardSlash=47]="ForwardSlash",ue[ue.Zero=48]="Zero",ue[ue.One=49]="One",ue[ue.Two=50]="Two",ue[ue.Three=51]="Three",ue[ue.Four=52]="Four",ue[ue.Five=53]="Five",ue[ue.Six=54]="Six",ue[ue.Seven=55]="Seven",ue[ue.Eight=56]="Eight",ue[ue.Nine=57]="Nine",ue[ue.LessThan=60]="LessThan",ue[ue.GreaterThan=62]="GreaterThan",ue[ue.A=65]="A",ue[ue.D=68]="D",ue[ue.E=69]="E",ue[ue.F=70]="F",ue[ue.O=79]="O",ue[ue.P=80]="P",ue[ue.R=82]="R",ue[ue.LeftSquareBracket=91]="LeftSquareBracket",ue[ue.BackSlash=92]="BackSlash",ue[ue.RightSquareBracket=93]="RightSquareBracket",ue[ue.a=97]="a",ue[ue.b=98]="b",ue[ue.d=100]="d",ue[ue.e=101]="e",ue[ue.f=102]="f",ue[ue.i=105]="i",ue[ue.j=106]="j",ue[ue.l=108]="l",ue[ue.m=109]="m",ue[ue.n=110]="n",ue[ue.o=111]="o",ue[ue.r=114]="r",ue[ue.s=115]="s",ue[ue.t=116]="t",ue[ue.u=117]="u",ue[ue.x=120]="x",ue[ue.LeftCurly=123]="LeftCurly",ue[ue.RightCurly=125]="RightCurly",ue[ue.Tilde=126]="Tilde";var We=function(){function t(t,e){this.major=String(t),this.minor=String(e)}return t.prototype.toString=function(){var t=x(129);return"%PDF-"+this.major+"."+this.minor+"\n%"+t+t+t+t},t.prototype.sizeInBytes=function(){return 12+this.major.length+this.minor.length},t.prototype.copyBytesInto=function(t,e){var r=e;return t[e++]=se.Percent,t[e++]=se.P,t[e++]=se.D,t[e++]=se.F,t[e++]=se.Dash,e+=S(this.major,t,e),t[e++]=se.Period,e+=S(this.minor,t,e),t[e++]=se.Newline,t[e++]=se.Percent,t[e++]=129,t[e++]=129,t[e++]=129,t[e++]=129,e-r},t.forVersion=function(e,r){return new t(e,r)},t}(),ze=function(){function t(){}return t.prototype.clone=function(t){throw new he(this.constructor.name,"clone")},t.prototype.toString=function(){throw new he(this.constructor.name,"toString")},t.prototype.sizeInBytes=function(){throw new he(this.constructor.name,"sizeInBytes")},t.prototype.copyBytesInto=function(t,e){throw new he(this.constructor.name,"copyBytesInto")},t}(),qe=function(t){function e(e){var r=t.call(this)||this;return r.numberValue=e,r.stringValue=dt(e),r}return r(e,t),e.prototype.asNumber=function(){return this.numberValue},e.prototype.value=function(){return this.numberValue},e.prototype.clone=function(){return e.of(this.numberValue)},e.prototype.toString=function(){return this.stringValue},e.prototype.sizeInBytes=function(){return this.stringValue.length},e.prototype.copyBytesInto=function(t,e){return e+=S(this.stringValue,t,e),this.stringValue.length},e.of=function(t){return new e(t)},e}(ze),Ee=function(t){function e(e){var r=t.call(this)||this;return r.array=[],r.context=e,r}return r(e,t),e.prototype.size=function(){return this.array.length},e.prototype.push=function(t){this.array.push(t)},e.prototype.insert=function(t,e){this.array.splice(t,0,e)},e.prototype.indexOf=function(t){var e=this.array.indexOf(t);return-1===e?void 0:e},e.prototype.remove=function(t){this.array.splice(t,1)},e.prototype.set=function(t,e){this.array[t]=e},e.prototype.get=function(t){return this.array[t]},e.prototype.lookupMaybe=function(t){for(var e,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return(e=this.context).lookupMaybe.apply(e,a([this.get(t)],r))},e.prototype.lookup=function(t){for(var e,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return(e=this.context).lookup.apply(e,a([this.get(t)],r))},e.prototype.asRectangle=function(){if(4!==this.size())throw new xe(this.size());var t=this.lookup(0,qe).asNumber(),e=this.lookup(1,qe).asNumber();return{x:t,y:e,width:this.lookup(2,qe).asNumber()-t,height:this.lookup(3,qe).asNumber()-e}},e.prototype.asArray=function(){return this.array.slice()},e.prototype.clone=function(t){for(var r=e.withContext(t||this.context),n=0,o=this.size();n<o;n++)r.push(this.array[n]);return r},e.prototype.toString=function(){for(var t="[ ",e=0,r=this.size();e<r;e++)t+=this.get(e).toString(),t+=" ";return t+="]"},e.prototype.sizeInBytes=function(){for(var t=3,e=0,r=this.size();e<r;e++)t+=this.get(e).sizeInBytes()+1;return t},e.prototype.copyBytesInto=function(t,e){var r=e;t[e++]=se.LeftSquareBracket,t[e++]=se.Space;for(var n=0,o=this.size();n<o;n++)e+=this.get(n).copyBytesInto(t,e),t[e++]=se.Space;return t[e++]=se.RightSquareBracket,e-r},e.prototype.scalePDFNumbers=function(t,e){for(var r=0,n=this.size();r<n;r++){var o=this.lookup(r);if(o instanceof qe){var i=r%2==0?t:e;this.set(r,qe.of(o.asNumber()*i))}}},e.withContext=function(t){return new e(t)},e}(ze),Ge={},Ke=function(t){function e(e,r){var n=this;if(e!==Ge)throw new le("PDFBool");return(n=t.call(this)||this).value=r,n}return r(e,t),e.prototype.asBoolean=function(){return this.value},e.prototype.clone=function(){return this},e.prototype.toString=function(){return String(this.value)},e.prototype.sizeInBytes=function(){return this.value?4:5},e.prototype.copyBytesInto=function(t,e){return this.value?(t[e++]=se.t,t[e++]=se.r,t[e++]=se.u,t[e++]=se.e,4):(t[e++]=se.f,t[e++]=se.a,t[e++]=se.l,t[e++]=se.s,t[e++]=se.e,5)},e.True=new e(Ge,!0),e.False=new e(Ge,!1),e}(ze),Le=new Uint8Array(256);Le[se.LeftParen]=1,Le[se.RightParen]=1,Le[se.LessThan]=1,Le[se.GreaterThan]=1,Le[se.LeftSquareBracket]=1,Le[se.RightSquareBracket]=1,Le[se.LeftCurly]=1,Le[se.RightCurly]=1,Le[se.ForwardSlash]=1,Le[se.Percent]=1;var Xe=new Uint8Array(256);Xe[se.Null]=1,Xe[se.Tab]=1,Xe[se.Newline]=1,Xe[se.FormFeed]=1,Xe[se.CarriageReturn]=1,Xe[se.Space]=1;for(var He=new Uint8Array(256),Ze=0;Ze<256;Ze++)He[Ze]=Xe[Ze]||Le[Ze]?1:0;He[se.Hash]=1;var Ye={},Je=new Map,Qe=function(t){function e(e,r){var n=this;if(e!==Ye)throw new le("PDFName");n=t.call(this)||this;for(var o,i="/",a=0,s=r.length;a<s;a++){var u=r[a],c=g(u);i+=(o=c)>=se.ExclamationPoint&&o<=se.Tilde&&!He[o]?u:"#"+b(c)}return n.encodedName=i,n}return r(e,t),e.prototype.asBytes=function(){for(var t=[],e="",r=!1,n=function(e){void 0!==e&&t.push(e),r=!1},o=1,i=this.encodedName.length;o<i;o++){var a=this.encodedName[o],s=g(a),u=this.encodedName[o+1];r?s>=se.Zero&&s<=se.Nine||s>=se.a&&s<=se.f||s>=se.A&&s<=se.F?2!==(e+=a).length&&(u>="0"&&u<="9"||u>="a"&&u<="f"||u>="A"&&u<="F")||(n(parseInt(e,16)),e=""):n(s):s===se.Hash?r=!0:n(s)}return new Uint8Array(t)},e.prototype.decodeText=function(){var t=this.asBytes();return String.fromCharCode.apply(String,Array.from(t))},e.prototype.asString=function(){return this.encodedName},e.prototype.value=function(){return this.encodedName},e.prototype.clone=function(){return this},e.prototype.toString=function(){return this.encodedName},e.prototype.sizeInBytes=function(){return this.encodedName.length},e.prototype.copyBytesInto=function(t,e){return e+=S(this.encodedName,t,e),this.encodedName.length},e.of=function(t){var r=function(t){return t.replace(/#([\dABCDEF]{2})/g,(function(t,e){return F(e)}))}(t),n=Je.get(r);return n||(n=new e(Ye,r),Je.set(r,n)),n},e.Length=e.of("Length"),e.FlateDecode=e.of("FlateDecode"),e.Resources=e.of("Resources"),e.Font=e.of("Font"),e.XObject=e.of("XObject"),e.ExtGState=e.of("ExtGState"),e.Contents=e.of("Contents"),e.Type=e.of("Type"),e.Parent=e.of("Parent"),e.MediaBox=e.of("MediaBox"),e.Page=e.of("Page"),e.Annots=e.of("Annots"),e.TrimBox=e.of("TrimBox"),e.ArtBox=e.of("ArtBox"),e.BleedBox=e.of("BleedBox"),e.CropBox=e.of("CropBox"),e.Rotate=e.of("Rotate"),e.Title=e.of("Title"),e.Author=e.of("Author"),e.Subject=e.of("Subject"),e.Creator=e.of("Creator"),e.Keywords=e.of("Keywords"),e.Producer=e.of("Producer"),e.CreationDate=e.of("CreationDate"),e.ModDate=e.of("ModDate"),e}(ze);const _e=new(function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype.asNull=function(){return null},e.prototype.clone=function(){return this},e.prototype.toString=function(){return"null"},e.prototype.sizeInBytes=function(){return 4},e.prototype.copyBytesInto=function(t,e){return t[e++]=se.n,t[e++]=se.u,t[e++]=se.l,t[e++]=se.l,4},e}(ze));var $e,tr,er=function(t){function e(e,r){var n=t.call(this)||this;return n.dict=e,n.context=r,n}return r(e,t),e.prototype.keys=function(){return Array.from(this.dict.keys())},e.prototype.values=function(){return Array.from(this.dict.values())},e.prototype.entries=function(){return Array.from(this.dict.entries())},e.prototype.set=function(t,e){this.dict.set(t,e)},e.prototype.get=function(t,e){void 0===e&&(e=!1);var r=this.dict.get(t);if(r!==_e||e)return r},e.prototype.has=function(t){var e=this.dict.get(t);return void 0!==e&&e!==_e},e.prototype.lookupMaybe=function(t){for(var e,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var o=r.includes(_e),i=(e=this.context).lookupMaybe.apply(e,a([this.get(t,o)],r));if(i!==_e||o)return i},e.prototype.lookup=function(t){for(var e,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var o=r.includes(_e),i=(e=this.context).lookup.apply(e,a([this.get(t,o)],r));if(i!==_e||o)return i},e.prototype.delete=function(t){return this.dict.delete(t)},e.prototype.asMap=function(){return new Map(this.dict)},e.prototype.uniqueKey=function(t){void 0===t&&(t="");for(var e=this.keys(),r=Qe.of(this.context.addRandomSuffix(t,10));e.includes(r);)r=Qe.of(this.context.addRandomSuffix(t,10));return r},e.prototype.clone=function(t){for(var r=e.withContext(t||this.context),n=this.entries(),o=0,i=n.length;o<i;o++){var a=n[o],s=a[0],u=a[1];r.set(s,u)}return r},e.prototype.toString=function(){for(var t="<<\n",e=this.entries(),r=0,n=e.length;r<n;r++){var o=e[r],i=o[0],a=o[1];t+=i.toString()+" "+a.toString()+"\n"}return t+=">>"},e.prototype.sizeInBytes=function(){for(var t=5,e=this.entries(),r=0,n=e.length;r<n;r++){var o=e[r],i=o[0],a=o[1];t+=i.sizeInBytes()+a.sizeInBytes()+2}return t},e.prototype.copyBytesInto=function(t,e){var r=e;t[e++]=se.LessThan,t[e++]=se.LessThan,t[e++]=se.Newline;for(var n=this.entries(),o=0,i=n.length;o<i;o++){var a=n[o],s=a[0],u=a[1];e+=s.copyBytesInto(t,e),t[e++]=se.Space,e+=u.copyBytesInto(t,e),t[e++]=se.Newline}return t[e++]=se.GreaterThan,t[e++]=se.GreaterThan,e-r},e.withContext=function(t){return new e(new Map,t)},e.fromMapWithContext=function(t,r){return new e(t,r)},e}(ze),rr=function(t){function e(e){var r=t.call(this)||this;return r.dict=e,r}return r(e,t),e.prototype.clone=function(t){throw new he(this.constructor.name,"clone")},e.prototype.getContentsString=function(){throw new he(this.constructor.name,"getContentsString")},e.prototype.getContents=function(){throw new he(this.constructor.name,"getContents")},e.prototype.getContentsSize=function(){throw new he(this.constructor.name,"getContentsSize")},e.prototype.updateDict=function(){var t=this.getContentsSize();this.dict.set(Qe.Length,qe.of(t))},e.prototype.sizeInBytes=function(){return this.updateDict(),this.dict.sizeInBytes()+this.getContentsSize()+18},e.prototype.toString=function(){this.updateDict();var t=this.dict.toString();return t+="\nstream\n",t+=this.getContentsString(),t+="\nendstream"},e.prototype.copyBytesInto=function(t,e){this.updateDict();var r=e;e+=this.dict.copyBytesInto(t,e),t[e++]=se.Newline,t[e++]=se.s,t[e++]=se.t,t[e++]=se.r,t[e++]=se.e,t[e++]=se.a,t[e++]=se.m,t[e++]=se.Newline;for(var n=this.getContents(),o=0,i=n.length;o<i;o++)t[e++]=n[o];return t[e++]=se.Newline,t[e++]=se.e,t[e++]=se.n,t[e++]=se.d,t[e++]=se.s,t[e++]=se.t,t[e++]=se.r,t[e++]=se.e,t[e++]=se.a,t[e++]=se.m,e-r},e}(ze),nr=function(t){function e(e,r){var n=t.call(this,e)||this;return n.contents=r,n}return r(e,t),e.prototype.asUint8Array=function(){return this.contents.slice()},e.prototype.clone=function(t){return e.of(this.dict.clone(t),this.contents.slice())},e.prototype.getContentsString=function(){return E(this.contents)},e.prototype.getContents=function(){return this.contents},e.prototype.getContentsSize=function(){return this.contents.length},e.of=function(t,r){return new e(t,r)},e}(rr),or={},ir=new Map,ar=function(t){function e(e,r,n){var o=this;if(e!==or)throw new le("PDFRef");return(o=t.call(this)||this).objectNumber=r,o.generationNumber=n,o.tag=r+" "+n+" R",o}return r(e,t),e.prototype.clone=function(){return this},e.prototype.toString=function(){return this.tag},e.prototype.sizeInBytes=function(){return this.tag.length},e.prototype.copyBytesInto=function(t,e){return e+=S(this.tag,t,e),this.tag.length},e.of=function(t,r){void 0===r&&(r=0);var n=t+" "+r+" R",o=ir.get(n);return o||(o=new e(or,t,r),ir.set(n,o)),o},e}(ze),sr=function(){function t(t,e){this.name=t,this.args=e||[]}return t.prototype.clone=function(e){for(var r=new Array(this.args.length),n=0,o=r.length;n<o;n++){var i=this.args[n];r[n]=i instanceof ze?i.clone(e):i}return t.of(this.name,r)},t.prototype.toString=function(){for(var t="",e=0,r=this.args.length;e<r;e++)t+=String(this.args[e])+" ";return t+=this.name},t.prototype.sizeInBytes=function(){for(var t=0,e=0,r=this.args.length;e<r;e++){var n=this.args[e];t+=(n instanceof ze?n.sizeInBytes():n.length)+1}return t+=this.name.length},t.prototype.copyBytesInto=function(t,e){for(var r=e,n=0,o=this.args.length;n<o;n++){var i=this.args[n];e+=i instanceof ze?i.copyBytesInto(t,e):S(i,t,e),t[e++]=se.Space}return(e+=S(this.name,t,e))-r},t.of=function(e,r){return new t(e,r)},t}();(tr=$e||($e={})).NonStrokingColor="sc",tr.NonStrokingColorN="scn",tr.NonStrokingColorRgb="rg",tr.NonStrokingColorGray="g",tr.NonStrokingColorCmyk="k",tr.NonStrokingColorspace="cs",tr.StrokingColor="SC",tr.StrokingColorN="SCN",tr.StrokingColorRgb="RG",tr.StrokingColorGray="G",tr.StrokingColorCmyk="K",tr.StrokingColorspace="CS",tr.BeginMarkedContentSequence="BDC",tr.BeginMarkedContent="BMC",tr.EndMarkedContent="EMC",tr.MarkedContentPointWithProps="DP",tr.MarkedContentPoint="MP",tr.DrawObject="Do",tr.ConcatTransformationMatrix="cm",tr.PopGraphicsState="Q",tr.PushGraphicsState="q",tr.SetFlatness="i",tr.SetGraphicsStateParams="gs",tr.SetLineCapStyle="J",tr.SetLineDashPattern="d",tr.SetLineJoinStyle="j",tr.SetLineMiterLimit="M",tr.SetLineWidth="w",tr.SetTextMatrix="Tm",tr.SetRenderingIntent="ri",tr.AppendRectangle="re",tr.BeginInlineImage="BI",tr.BeginInlineImageData="ID",tr.EndInlineImage="EI",tr.ClipEvenOdd="W*",tr.ClipNonZero="W",tr.CloseAndStroke="s",tr.CloseFillEvenOddAndStroke="b*",tr.CloseFillNonZeroAndStroke="b",tr.ClosePath="h",tr.AppendBezierCurve="c",tr.CurveToReplicateFinalPoint="y",tr.CurveToReplicateInitialPoint="v",tr.EndPath="n",tr.FillEvenOddAndStroke="B*",tr.FillEvenOdd="f*",tr.FillNonZeroAndStroke="B",tr.FillNonZero="f",tr.LegacyFillNonZero="F",tr.LineTo="l",tr.MoveTo="m",tr.ShadingFill="sh",tr.StrokePath="S",tr.BeginText="BT",tr.EndText="ET",tr.MoveText="Td",tr.MoveTextSetLeading="TD",tr.NextLine="T*",tr.SetCharacterSpacing="Tc",tr.SetFontAndSize="Tf",tr.SetTextHorizontalScaling="Tz",tr.SetTextLineHeight="TL",tr.SetTextRenderingMode="Tr",tr.SetTextRise="Ts",tr.SetWordSpacing="Tw",tr.ShowText="Tj",tr.ShowTextAdjusted="TJ",tr.ShowTextLine="'",tr.ShowTextLineAndSpace='"',tr.Type3D0="d0",tr.Type3D1="d1",tr.BeginCompatibilitySection="BX",tr.EndCompatibilitySection="EX";var ur,cr,fr=function(e){function n(r,n){var o=e.call(this,r)||this;return o.computeContents=function(){var e=o.getUnencodedContents();return o.encode?t.deflate(e):e},o.encode=n,n&&r.set(Qe.of("Filter"),Qe.of("FlateDecode")),o.contentsCache=fe.populatedBy(o.computeContents),o}return r(n,e),n.prototype.getContents=function(){return this.contentsCache.access()},n.prototype.getContentsSize=function(){return this.contentsCache.access().length},n.prototype.getUnencodedContents=function(){throw new he(this.constructor.name,"getUnencodedContents")},n}(rr),hr=function(t){function e(e,r,n){void 0===n&&(n=!0);var o=t.call(this,e,n)||this;return o.operators=r,o}return r(e,t),e.prototype.push=function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];(t=this.operators).push.apply(t,e)},e.prototype.clone=function(t){for(var r=new Array(this.operators.length),n=0,o=this.operators.length;n<o;n++)r[n]=this.operators[n].clone(t);var i=this.dict,a=this.encode;return e.of(i.clone(t),r,a)},e.prototype.getContentsString=function(){for(var t="",e=0,r=this.operators.length;e<r;e++)t+=this.operators[e]+"\n";return t},e.prototype.getUnencodedContents=function(){for(var t=new Uint8Array(this.getUnencodedContentsSize()),e=0,r=0,n=this.operators.length;r<n;r++)e+=this.operators[r].copyBytesInto(t,e),t[e++]=se.Newline;return t},e.prototype.getUnencodedContentsSize=function(){for(var t=0,e=0,r=this.operators.length;e<r;e++)t+=this.operators[e].sizeInBytes()+1;return t},e.of=function(t,r,n){return void 0===n&&(n=!0),new e(t,r,n)},e}(fr),lr=function(){function t(t){this.seed=t}return t.prototype.nextInt=function(){var t=1e4*Math.sin(this.seed++);return t-Math.floor(t)},t.withSeed=function(e){return new t(e)},t}(),dr=function(t,e){var r=t[0],n=e[0];return r.objectNumber-n.objectNumber},pr=function(){function e(){this.largestObjectNumber=0,this.header=We.forVersion(1,7),this.trailerInfo={},this.indirectObjects=new Map,this.rng=lr.withSeed(1)}return e.prototype.assign=function(t,e){this.indirectObjects.set(t,e),t.objectNumber>this.largestObjectNumber&&(this.largestObjectNumber=t.objectNumber)},e.prototype.nextRef=function(){return this.largestObjectNumber+=1,ar.of(this.largestObjectNumber)},e.prototype.register=function(t){var e=this.nextRef();return this.assign(e,t),e},e.prototype.delete=function(t){return this.indirectObjects.delete(t)},e.prototype.lookupMaybe=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n=e.includes(_e),o=t instanceof ar?this.indirectObjects.get(t):t;if(o&&(o!==_e||n)){for(var i=0,a=e.length;i<a;i++){var s=e[i];if(s===_e){if(o===_e)return o}else if(o instanceof s)return o}throw new de(e,o)}},e.prototype.lookup=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n=t instanceof ar?this.indirectObjects.get(t):t;if(0===e.length)return n;for(var o=0,i=e.length;o<i;o++){var a=e[o];if(a===_e){if(n===_e)return n}else if(n instanceof a)return n}throw new de(e,n)},e.prototype.getObjectRef=function(t){for(var e=Array.from(this.indirectObjects.entries()),r=0,n=e.length;r<n;r++){var o=e[r],i=o[0];if(o[1]===t)return i}},e.prototype.enumerateIndirectObjects=function(){return Array.from(this.indirectObjects.entries()).sort(dr)},e.prototype.obj=function(t){if(t instanceof ze)return t;if(null==t)return _e;if("string"==typeof t)return Qe.of(t);if("number"==typeof t)return qe.of(t);if("boolean"==typeof t)return t?Ke.True:Ke.False;if(Array.isArray(t)){for(var e=Ee.withContext(this),r=0,n=t.length;r<n;r++)e.push(this.obj(t[r]));return e}var o=er.withContext(this),i=Object.keys(t);for(r=0,n=i.length;r<n;r++){var a=i[r],s=t[a];void 0!==s&&o.set(Qe.of(a),this.obj(s))}return o},e.prototype.stream=function(t,e){return void 0===e&&(e={}),nr.of(this.obj(e),W(t))},e.prototype.flateStream=function(e,r){return void 0===r&&(r={}),this.stream(t.deflate(W(e)),n(n({},r),{Filter:"FlateDecode"}))},e.prototype.contentStream=function(t,e){return void 0===e&&(e={}),hr.of(this.obj(e),t)},e.prototype.formXObject=function(t,e){return void 0===e&&(e={}),this.contentStream(t,n(n({BBox:this.obj([0,0,0,0]),Matrix:this.obj([1,0,0,1,0,0])},e),{Type:"XObject",Subtype:"Form"}))},e.prototype.getPushGraphicsStateContentStream=function(){if(this.pushGraphicsStateContentStreamRef)return this.pushGraphicsStateContentStreamRef;var t=this.obj({}),e=sr.of($e.PushGraphicsState),r=hr.of(t,[e]);return this.pushGraphicsStateContentStreamRef=this.register(r),this.pushGraphicsStateContentStreamRef},e.prototype.getPopGraphicsStateContentStream=function(){if(this.popGraphicsStateContentStreamRef)return this.popGraphicsStateContentStreamRef;var t=this.obj({}),e=sr.of($e.PopGraphicsState),r=hr.of(t,[e]);return this.popGraphicsStateContentStreamRef=this.register(r),this.popGraphicsStateContentStreamRef},e.prototype.addRandomSuffix=function(t,e){return void 0===e&&(e=4),t+"-"+Math.floor(this.rng.nextInt()*Math.pow(10,e))},e.create=function(){return new e},e}(),yr=function(t){function e(e,r,n){void 0===n&&(n=!0);var o=t.call(this,e,r)||this;return o.normalized=!1,o.autoNormalizeCTM=n,o}return r(e,t),e.prototype.clone=function(t){for(var r=e.fromMapWithContext(new Map,t||this.context,this.autoNormalizeCTM),n=this.entries(),o=0,i=n.length;o<i;o++){var a=n[o],s=a[0],u=a[1];r.set(s,u)}return r},e.prototype.Parent=function(){return this.lookupMaybe(Qe.Parent,er)},e.prototype.Contents=function(){return this.lookup(Qe.of("Contents"))},e.prototype.Annots=function(){return this.lookupMaybe(Qe.Annots,Ee)},e.prototype.BleedBox=function(){return this.lookupMaybe(Qe.BleedBox,Ee)},e.prototype.TrimBox=function(){return this.lookupMaybe(Qe.TrimBox,Ee)},e.prototype.ArtBox=function(){return this.lookupMaybe(Qe.ArtBox,Ee)},e.prototype.Resources=function(){var t=this.getInheritableAttribute(Qe.Resources);return this.context.lookupMaybe(t,er)},e.prototype.MediaBox=function(){var t=this.getInheritableAttribute(Qe.MediaBox);return this.context.lookup(t,Ee)},e.prototype.CropBox=function(){var t=this.getInheritableAttribute(Qe.CropBox);return this.context.lookupMaybe(t,Ee)},e.prototype.Rotate=function(){var t=this.getInheritableAttribute(Qe.Rotate);return this.context.lookupMaybe(t,qe)},e.prototype.getInheritableAttribute=function(t){var e;return this.ascend((function(r){e||(e=r.get(t))})),e},e.prototype.setParent=function(t){this.set(Qe.Parent,t)},e.prototype.addContentStream=function(t){var e=this.normalizedEntries().Contents||this.context.obj([]);this.set(Qe.Contents,e),e.push(t)},e.prototype.wrapContentStreams=function(t,e){var r=this.Contents();return r instanceof Ee&&(r.insert(0,t),r.push(e),!0)},e.prototype.addAnnot=function(t){this.normalizedEntries().Annots.push(t)},e.prototype.removeAnnot=function(t){var e=this.normalizedEntries().Annots,r=e.indexOf(t);void 0!==r&&e.remove(r)},e.prototype.setFontDictionary=function(t,e){this.normalizedEntries().Font.set(t,e)},e.prototype.newFontDictionaryKey=function(t){return this.normalizedEntries().Font.uniqueKey(t)},e.prototype.newFontDictionary=function(t,e){var r=this.newFontDictionaryKey(t);return this.setFontDictionary(r,e),r},e.prototype.setXObject=function(t,e){this.normalizedEntries().XObject.set(t,e)},e.prototype.newXObjectKey=function(t){return this.normalizedEntries().XObject.uniqueKey(t)},e.prototype.newXObject=function(t,e){var r=this.newXObjectKey(t);return this.setXObject(r,e),r},e.prototype.setExtGState=function(t,e){this.normalizedEntries().ExtGState.set(t,e)},e.prototype.newExtGStateKey=function(t){return this.normalizedEntries().ExtGState.uniqueKey(t)},e.prototype.newExtGState=function(t,e){var r=this.newExtGStateKey(t);return this.setExtGState(r,e),r},e.prototype.ascend=function(t){t(this);var e=this.Parent();e&&e.ascend(t)},e.prototype.normalize=function(){if(!this.normalized){var t=this.context,e=this.get(Qe.Contents);this.context.lookup(e)instanceof rr&&this.set(Qe.Contents,t.obj([e])),this.autoNormalizeCTM&&this.wrapContentStreams(this.context.getPushGraphicsStateContentStream(),this.context.getPopGraphicsStateContentStream());var r=this.getInheritableAttribute(Qe.Resources),n=t.lookupMaybe(r,er)||t.obj({});this.set(Qe.Resources,n);var o=n.lookupMaybe(Qe.Font,er)||t.obj({});n.set(Qe.Font,o);var i=n.lookupMaybe(Qe.XObject,er)||t.obj({});n.set(Qe.XObject,i);var a=n.lookupMaybe(Qe.ExtGState,er)||t.obj({});n.set(Qe.ExtGState,a);var s=this.Annots()||t.obj([]);this.set(Qe.Annots,s),this.normalized=!0}},e.prototype.normalizedEntries=function(){this.normalize();var t=this.Annots(),e=this.Resources();return{Annots:t,Resources:e,Contents:this.Contents(),Font:e.lookup(Qe.Font,er),XObject:e.lookup(Qe.XObject,er),ExtGState:e.lookup(Qe.ExtGState,er)}},e.InheritableEntries=["Resources","MediaBox","CropBox","Rotate"],e.withContextAndParent=function(t,r){var n=new Map;return n.set(Qe.Type,Qe.Page),n.set(Qe.Parent,r),n.set(Qe.Resources,t.obj({})),n.set(Qe.MediaBox,t.obj([0,0,612,792])),new e(n,t,!1)},e.fromMapWithContext=function(t,r,n){return void 0===n&&(n=!0),new e(t,r,n)},e}(er),gr=function(){function t(t,e){var r=this;this.traversedObjects=new Map,this.copy=function(t){return t instanceof yr?r.copyPDFPage(t):t instanceof er?r.copyPDFDict(t):t instanceof Ee?r.copyPDFArray(t):t instanceof rr?r.copyPDFStream(t):t instanceof ar?r.copyPDFIndirectObject(t):t.clone()},this.copyPDFPage=function(t){for(var e=t.clone(),n=yr.InheritableEntries,o=0,i=n.length;o<i;o++){var a=Qe.of(n[o]),s=e.getInheritableAttribute(a);!e.get(a)&&s&&e.set(a,s)}return e.delete(Qe.of("Parent")),r.copyPDFDict(e)},this.copyPDFDict=function(t){if(r.traversedObjects.has(t))return r.traversedObjects.get(t);var e=t.clone(r.dest);r.traversedObjects.set(t,e);for(var n=t.entries(),o=0,i=n.length;o<i;o++){var a=n[o],s=a[0],u=a[1];e.set(s,r.copy(u))}return e},this.copyPDFArray=function(t){if(r.traversedObjects.has(t))return r.traversedObjects.get(t);var e=t.clone(r.dest);r.traversedObjects.set(t,e);for(var n=0,o=t.size();n<o;n++){var i=t.get(n);e.set(n,r.copy(i))}return e},this.copyPDFStream=function(t){if(r.traversedObjects.has(t))return r.traversedObjects.get(t);var e=t.clone(r.dest);r.traversedObjects.set(t,e);for(var n=t.dict.entries(),o=0,i=n.length;o<i;o++){var a=n[o],s=a[0],u=a[1];e.dict.set(s,r.copy(u))}return e},this.copyPDFIndirectObject=function(t){if(!r.traversedObjects.has(t)){var e=r.dest.nextRef();r.traversedObjects.set(t,e);var n=r.src.lookup(t);if(n){var o=r.copy(n);r.dest.assign(e,o)}}return r.traversedObjects.get(t)},this.src=t,this.dest=e}return t.for=function(e,r){return new t(e,r)},t}(),vr=function(){function t(t){this.subsections=t?[[t]]:[],this.chunkIdx=0,this.chunkLength=t?1:0}return t.prototype.addEntry=function(t,e){this.append({ref:t,offset:e,deleted:!1})},t.prototype.addDeletedEntry=function(t,e){this.append({ref:t,offset:e,deleted:!0})},t.prototype.toString=function(){for(var t="xref\n",e=0,r=this.subsections.length;e<r;e++){var n=this.subsections[e];t+=n[0].ref.objectNumber+" "+n.length+"\n";for(var o=0,i=n.length;o<i;o++){var a=n[o];t+=w(String(a.offset),10,"0"),t+=" ",t+=w(String(a.ref.generationNumber),5,"0"),t+=" ",t+=a.deleted?"f":"n",t+=" \n"}}return t},t.prototype.sizeInBytes=function(){for(var t=5,e=0,r=this.subsections.length;e<r;e++){var n=this.subsections[e],o=n.length,i=n[0];t+=2,t+=String(i.ref.objectNumber).length,t+=String(o).length,t+=20*o}return t},t.prototype.copyBytesInto=function(t,e){var r=e;return t[e++]=se.x,t[e++]=se.r,t[e++]=se.e,t[e++]=se.f,t[e++]=se.Newline,(e+=this.copySubsectionsIntoBuffer(this.subsections,t,e))-r},t.prototype.copySubsectionsIntoBuffer=function(t,e,r){for(var n=r,o=t.length,i=0;i<o;i++){var a=this.subsections[i],s=String(a[0].ref.objectNumber);r+=S(s,e,r),e[r++]=se.Space;var u=String(a.length);r+=S(u,e,r),e[r++]=se.Newline,r+=this.copyEntriesIntoBuffer(a,e,r)}return r-n},t.prototype.copyEntriesIntoBuffer=function(t,e,r){for(var n=t.length,o=0;o<n;o++){var i=t[o],a=w(String(i.offset),10,"0");r+=S(a,e,r),e[r++]=se.Space;var s=w(String(i.ref.generationNumber),5,"0");r+=S(s,e,r),e[r++]=se.Space,e[r++]=i.deleted?se.f:se.n,e[r++]=se.Space,e[r++]=se.Newline}return 20*n},t.prototype.append=function(t){if(0===this.chunkLength)return this.subsections.push([t]),this.chunkIdx=0,void(this.chunkLength=1);var e=this.subsections[this.chunkIdx],r=e[this.chunkLength-1];t.ref.objectNumber-r.ref.objectNumber>1?(this.subsections.push([t]),this.chunkIdx+=1,this.chunkLength=1):(e.push(t),this.chunkLength+=1)},t.create=function(){return new t({ref:ar.of(0,65535),offset:0,deleted:!0})},t.createEmpty=function(){return new t},t}(),mr=function(){function t(t){this.lastXRefOffset=String(t)}return t.prototype.toString=function(){return"startxref\n"+this.lastXRefOffset+"\n%%EOF"},t.prototype.sizeInBytes=function(){return 16+this.lastXRefOffset.length},t.prototype.copyBytesInto=function(t,e){var r=e;return t[e++]=se.s,t[e++]=se.t,t[e++]=se.a,t[e++]=se.r,t[e++]=se.t,t[e++]=se.x,t[e++]=se.r,t[e++]=se.e,t[e++]=se.f,t[e++]=se.Newline,e+=S(this.lastXRefOffset,t,e),t[e++]=se.Newline,t[e++]=se.Percent,t[e++]=se.Percent,t[e++]=se.E,t[e++]=se.O,t[e++]=se.F,e-r},t.forLastCrossRefSectionOffset=function(e){return new t(e)},t}(),br=function(){function t(t){this.dict=t}return t.prototype.toString=function(){return"trailer\n"+this.dict.toString()},t.prototype.sizeInBytes=function(){return 8+this.dict.sizeInBytes()},t.prototype.copyBytesInto=function(t,e){var r=e;return t[e++]=se.t,t[e++]=se.r,t[e++]=se.a,t[e++]=se.i,t[e++]=se.l,t[e++]=se.e,t[e++]=se.r,t[e++]=se.Newline,(e+=this.dict.copyBytesInto(t,e))-r},t.of=function(e){return new t(e)},t}(),xr=function(t){function e(e,r,n){void 0===n&&(n=!0);var o=t.call(this,e.obj({}),n)||this;return o.objects=r,o.offsets=o.computeObjectOffsets(),o.offsetsString=o.computeOffsetsString(),o.dict.set(Qe.of("Type"),Qe.of("ObjStm")),o.dict.set(Qe.of("N"),qe.of(o.objects.length)),o.dict.set(Qe.of("First"),qe.of(o.offsetsString.length)),o}return r(e,t),e.prototype.getObjectsCount=function(){return this.objects.length},e.prototype.clone=function(t){return e.withContextAndObjects(t||this.dict.context,this.objects.slice(),this.encode)},e.prototype.getContentsString=function(){for(var t=this.offsetsString,e=0,r=this.objects.length;e<r;e++){t+=this.objects[e][1]+"\n"}return t},e.prototype.getUnencodedContents=function(){for(var t=new Uint8Array(this.getUnencodedContentsSize()),e=S(this.offsetsString,t,0),r=0,n=this.objects.length;r<n;r++){e+=this.objects[r][1].copyBytesInto(t,e),t[e++]=se.Newline}return t},e.prototype.getUnencodedContentsSize=function(){return this.offsetsString.length+I(this.offsets)[1]+I(this.objects)[1].sizeInBytes()+1},e.prototype.computeOffsetsString=function(){for(var t="",e=0,r=this.offsets.length;e<r;e++){var n=this.offsets[e];t+=n[0]+" "+n[1]+" "}return t},e.prototype.computeObjectOffsets=function(){for(var t=0,e=new Array(this.objects.length),r=0,n=this.objects.length;r<n;r++){var o=this.objects[r],i=o[0],a=o[1];e[r]=[i.objectNumber,t],t+=a.sizeInBytes()+1}return e},e.withContextAndObjects=function(t,r,n){return void 0===n&&(n=!0),new e(t,r,n)},e}(fr),Fr=function(){function t(t,e){var r=this;this.parsedObjects=0,this.shouldWaitForTick=function(t){return r.parsedObjects+=t,r.parsedObjects%r.objectsPerTick===0},this.context=t,this.objectsPerTick=e}return t.prototype.serializeToBuffer=function(){return o(this,0,void 0,(function(){var t,e,r,n,o,a,s,u,c,f,h,l,d,p,y,g,v;return i(this,(function(i){switch(i.label){case 0:return[4,this.computeBufferSize()];case 1:t=i.sent(),e=t.size,r=t.header,n=t.indirectObjects,o=t.xref,a=t.trailerDict,s=t.trailer,u=0,c=new Uint8Array(e),u+=r.copyBytesInto(c,u),c[u++]=se.Newline,c[u++]=se.Newline,f=0,h=n.length,i.label=2;case 2:return f<h?(l=n[f],d=l[0],p=l[1],y=String(d.objectNumber),u+=S(y,c,u),c[u++]=se.Space,g=String(d.generationNumber),u+=S(g,c,u),c[u++]=se.Space,c[u++]=se.o,c[u++]=se.b,c[u++]=se.j,c[u++]=se.Newline,u+=p.copyBytesInto(c,u),c[u++]=se.Newline,c[u++]=se.e,c[u++]=se.n,c[u++]=se.d,c[u++]=se.o,c[u++]=se.b,c[u++]=se.j,c[u++]=se.Newline,c[u++]=se.Newline,v=p instanceof xr?p.getObjectsCount():1,this.shouldWaitForTick(v)?[4,Q()]:[3,4]):[3,5];case 3:i.sent(),i.label=4;case 4:return f++,[3,2];case 5:return o&&(u+=o.copyBytesInto(c,u),c[u++]=se.Newline),a&&(u+=a.copyBytesInto(c,u),c[u++]=se.Newline,c[u++]=se.Newline),u+=s.copyBytesInto(c,u),[2,c]}}))}))},t.prototype.computeIndirectObjectSize=function(t){var e=t[0],r=t[1];return e.sizeInBytes()+3+(r.sizeInBytes()+9)},t.prototype.createTrailerDict=function(){return this.context.obj({Size:this.context.largestObjectNumber+1,Root:this.context.trailerInfo.Root,Encrypt:this.context.trailerInfo.Encrypt,Info:this.context.trailerInfo.Info,ID:this.context.trailerInfo.ID})},t.prototype.computeBufferSize=function(){return o(this,0,void 0,(function(){var t,e,r,n,o,a,s,u,c,f,h;return i(this,(function(i){switch(i.label){case 0:t=We.forVersion(1,7),e=t.sizeInBytes()+2,r=vr.create(),n=this.context.enumerateIndirectObjects(),o=0,a=n.length,i.label=1;case 1:return o<a?(s=n[o],u=s[0],r.addEntry(u,e),e+=this.computeIndirectObjectSize(s),this.shouldWaitForTick(1)?[4,Q()]:[3,3]):[3,4];case 2:i.sent(),i.label=3;case 3:return o++,[3,1];case 4:return c=e,e+=r.sizeInBytes()+1,f=br.of(this.createTrailerDict()),e+=f.sizeInBytes()+2,h=mr.forLastCrossRefSectionOffset(c),[2,{size:e+=h.sizeInBytes(),header:t,indirectObjects:n,xref:r,trailerDict:f,trailer:h}]}}))}))},t.forContext=function(e,r){return new t(e,r)},t}(),wr=function(t){function e(e){var r=t.call(this)||this;return r.data=e,r}return r(e,t),e.prototype.clone=function(){return e.of(this.data.slice())},e.prototype.toString=function(){return"PDFInvalidObject("+this.data.length+" bytes)"},e.prototype.sizeInBytes=function(){return this.data.length},e.prototype.copyBytesInto=function(t,e){for(var r=this.data.length,n=0;n<r;n++)t[e++]=this.data[n];return r},e.of=function(t){return new e(t)},e}(ze);(cr=ur||(ur={}))[cr.Deleted=0]="Deleted",cr[cr.Uncompressed=1]="Uncompressed",cr[cr.Compressed=2]="Compressed";var Sr,Cr,Tr=function(t){function e(e,r,n){void 0===n&&(n=!0);var o=t.call(this,e,n)||this;return o.computeIndex=function(){for(var t=[],e=0,r=0,n=o.entries.length;r<n;r++){var i=o.entries[r],a=o.entries[r-1];0===r?t.push(i.ref.objectNumber):i.ref.objectNumber-a.ref.objectNumber>1&&(t.push(e),t.push(i.ref.objectNumber),e=0),e+=1}return t.push(e),t},o.computeEntryTuples=function(){for(var t=new Array(o.entries.length),e=0,r=o.entries.length;e<r;e++){var n=o.entries[e];if(n.type===ur.Deleted){var i=n.type,a=n.nextFreeObjectNumber,s=n.ref;t[e]=[i,a,s.generationNumber]}if(n.type===ur.Uncompressed){i=n.type;var u=n.offset;s=n.ref;t[e]=[i,u,s.generationNumber]}if(n.type===ur.Compressed){i=n.type;var c=n.objectStreamRef,f=n.index;t[e]=[i,c.objectNumber,f]}}return t},o.computeMaxEntryByteWidths=function(){for(var t=o.entryTuplesCache.access(),e=[0,0,0],r=0,n=t.length;r<n;r++){var i=t[r],a=i[0],s=i[1],u=i[2],c=pt(a),f=pt(s),h=pt(u);c>e[0]&&(e[0]=c),f>e[1]&&(e[1]=f),h>e[2]&&(e[2]=h)}return e},o.entries=r||[],o.entryTuplesCache=fe.populatedBy(o.computeEntryTuples),o.maxByteWidthsCache=fe.populatedBy(o.computeMaxEntryByteWidths),o.indexCache=fe.populatedBy(o.computeIndex),e.set(Qe.of("Type"),Qe.of("XRef")),o}return r(e,t),e.prototype.addDeletedEntry=function(t,e){var r=ur.Deleted;this.entries.push({type:r,ref:t,nextFreeObjectNumber:e}),this.entryTuplesCache.invalidate(),this.maxByteWidthsCache.invalidate(),this.indexCache.invalidate(),this.contentsCache.invalidate()},e.prototype.addUncompressedEntry=function(t,e){var r=ur.Uncompressed;this.entries.push({type:r,ref:t,offset:e}),this.entryTuplesCache.invalidate(),this.maxByteWidthsCache.invalidate(),this.indexCache.invalidate(),this.contentsCache.invalidate()},e.prototype.addCompressedEntry=function(t,e,r){var n=ur.Compressed;this.entries.push({type:n,ref:t,objectStreamRef:e,index:r}),this.entryTuplesCache.invalidate(),this.maxByteWidthsCache.invalidate(),this.indexCache.invalidate(),this.contentsCache.invalidate()},e.prototype.clone=function(t){var r=this,n=r.dict,o=r.entries,i=r.encode;return e.of(n.clone(t),o.slice(),i)},e.prototype.getContentsString=function(){for(var t=this.entryTuplesCache.access(),e=this.maxByteWidthsCache.access(),r="",n=0,o=t.length;n<o;n++){for(var i=t[n],a=i[0],s=i[1],u=i[2],c=L(yt(a)),f=L(yt(s)),h=L(yt(u)),l=e[0]-1;l>=0;l--)r+=(c[l]||0).toString(2);for(l=e[1]-1;l>=0;l--)r+=(f[l]||0).toString(2);for(l=e[2]-1;l>=0;l--)r+=(h[l]||0).toString(2)}return r},e.prototype.getUnencodedContents=function(){for(var t=this.entryTuplesCache.access(),e=this.maxByteWidthsCache.access(),r=new Uint8Array(this.getUnencodedContentsSize()),n=0,o=0,i=t.length;o<i;o++){for(var a=t[o],s=a[0],u=a[1],c=a[2],f=L(yt(s)),h=L(yt(u)),l=L(yt(c)),d=e[0]-1;d>=0;d--)r[n++]=f[d]||0;for(d=e[1]-1;d>=0;d--)r[n++]=h[d]||0;for(d=e[2]-1;d>=0;d--)r[n++]=l[d]||0}return r},e.prototype.getUnencodedContentsSize=function(){var t=this.maxByteWidthsCache.access();return X(t)*this.entries.length},e.prototype.updateDict=function(){t.prototype.updateDict.call(this);var e=this.maxByteWidthsCache.access(),r=this.indexCache.access(),n=this.dict.context;this.dict.set(Qe.of("W"),n.obj(e)),this.dict.set(Qe.of("Index"),n.obj(r))},e.create=function(t,r){void 0===r&&(r=!0);var n=new e(t,[],r);return n.addDeletedEntry(ar.of(0,65535),0),n},e.of=function(t,r,n){return void 0===n&&(n=!0),new e(t,r,n)},e}(fr),kr=function(t){function e(e,r,n,o){var i=t.call(this,e,r)||this;return i.encodeStreams=n,i.objectsPerStream=o,i}return r(e,t),e.prototype.computeBufferSize=function(){return o(this,0,void 0,(function(){var t,e,r,n,o,a,s,u,c,f,h,l,d,p,y,g,v,m,b;return i(this,(function(i){switch(i.label){case 0:t=this.context.largestObjectNumber+1,e=We.forVersion(1,7),r=e.sizeInBytes()+2,n=Tr.create(this.createTrailerDict(),this.encodeStreams),o=[],a=[],s=[],u=this.context.enumerateIndirectObjects(),l=0,d=u.length,i.label=1;case 1:return l<d?(c=u[l],y=c[0],f=c[1],y===this.context.trailerInfo.Encrypt||f instanceof rr||f instanceof wr||0!==y.generationNumber?(o.push(c),n.addUncompressedEntry(y,r),r+=this.computeIndirectObjectSize(c),this.shouldWaitForTick(1)?[4,Q()]:[3,3]):[3,4]):[3,6];case 2:i.sent(),i.label=3;case 3:return[3,5];case 4:p=I(a),h=I(s),p&&p.length%this.objectsPerStream!==0||(p=[],a.push(p),h=ar.of(t++),s.push(h)),n.addCompressedEntry(y,h,p.length),p.push(c),i.label=5;case 5:return l++,[3,1];case 6:l=0,d=a.length,i.label=7;case 7:return l<d?(p=a[l],y=s[l],g=xr.withContextAndObjects(this.context,p,this.encodeStreams),n.addUncompressedEntry(y,r),r+=this.computeIndirectObjectSize([y,g]),o.push([y,g]),this.shouldWaitForTick(p.length)?[4,Q()]:[3,9]):[3,10];case 8:i.sent(),i.label=9;case 9:return l++,[3,7];case 10:return v=ar.of(t++),n.dict.set(Qe.of("Size"),qe.of(t)),n.addUncompressedEntry(v,r),m=r,r+=this.computeIndirectObjectSize([v,n]),o.push([v,n]),b=mr.forLastCrossRefSectionOffset(m),[2,{size:r+=b.sizeInBytes(),header:e,indirectObjects:o,trailer:b}]}}))}))},e.forContext=function(t,r,n,o){return void 0===n&&(n=!0),void 0===o&&(o=50),new e(t,r,n,o)},e}(Fr),Or=function(t){function e(e){var r=t.call(this)||this;return r.value=e,r}return r(e,t),e.prototype.asBytes=function(){for(var t=this.value+(this.value.length%2==1?"0":""),e=t.length,r=new Uint8Array(t.length/2),n=0,o=0;n<e;){var i=parseInt(t.substring(n,n+2),16);r[o]=i,n+=2,o+=1}return r},e.prototype.decodeText=function(){var t=this.asBytes();return lt(t)?it(t):ce(t)},e.prototype.decodeDate=function(){var t=this.decodeText(),e=B(t);if(!e)throw new Fe(t);return e},e.prototype.asString=function(){return this.value},e.prototype.clone=function(){return e.of(this.value)},e.prototype.toString=function(){return"<"+this.value+">"},e.prototype.sizeInBytes=function(){return this.value.length+2},e.prototype.copyBytesInto=function(t,e){return t[e++]=se.LessThan,e+=S(this.value,t,e),t[e++]=se.GreaterThan,this.value.length+2},e.of=function(t){return new e(t)},e.fromText=function(t){for(var r=$(t),n="",o=0,i=r.length;o<i;o++)n+=m(r[o],4);return new e(n)},e}(ze),Pr=function(){function t(t,e){this.encoding=t===Pt.ZapfDingbats?Bt.ZapfDingbats:t===Pt.Symbol?Bt.Symbol:Bt.WinAnsi,this.font=Dt.load(t),this.fontName=this.font.FontName,this.customName=e}return t.prototype.encodeText=function(t){for(var e=this.encodeTextAsGlyphs(t),r=new Array(e.length),n=0,o=e.length;n<o;n++)r[n]=b(e[n].code);return Or.of(r.join(""))},t.prototype.widthOfTextAtSize=function(t,e){for(var r=this.encodeTextAsGlyphs(t),n=0,o=0,i=r.length;o<i;o++){var a=r[o].name,s=(r[o+1]||{}).name,u=this.font.getXAxisKerningForPair(a,s)||0;n+=this.widthOfGlyph(a)+u}return n*(e/1e3)},t.prototype.heightOfFontAtSize=function(t,e){void 0===e&&(e={});var r=e.descender,n=void 0===r||r,o=this.font,i=o.Ascender,a=o.Descender,s=o.FontBBox,u=(i||s[3])-(a||s[1]);return n||(u+=a||0),u/1e3*t},t.prototype.sizeOfFontAtHeight=function(t){var e=this.font,r=e.Ascender,n=e.Descender,o=e.FontBBox;return 1e3*t/((r||o[3])-(n||o[1]))},t.prototype.embedIntoContext=function(t,e){var r=t.obj({Type:"Font",Subtype:"Type1",BaseFont:this.customName||this.fontName,Encoding:this.encoding===Bt.WinAnsi?"WinAnsiEncoding":void 0});return e?(t.assign(e,r),e):t.register(r)},t.prototype.widthOfGlyph=function(t){return this.font.getWidthOfGlyph(t)||250},t.prototype.encodeTextAsGlyphs=function(t){for(var e=Array.from(t),r=new Array(e.length),n=0,o=e.length;n<o;n++){var i=v(e[n]);r[n]=this.encoding.encodeUnicodeCodePoint(i)}return r},t.for=function(e,r){return new t(e,r)},t}(),Ar=function(t){return"/CIDInit /ProcSet findresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo <<\n  /Registry (Adobe)\n  /Ordering (UCS)\n  /Supplement 0\n>> def\n/CMapName /Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<0000><ffff>\nendcodespacerange\n"+t.length+" beginbfchar\n"+t.map((function(t){return t[0]+" "+t[1]})).join("\n")+"\nendbfchar\nendcmap\nCMapName currentdict /CMap defineresource pop\nend\nend"},Rr=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return"<"+t.join("")+">"},Nr=function(t){return m(t,4)},Dr=function(t){if(tt(t))return Nr(t);if(et(t)){var e=rt(t),r=nt(t);return""+Nr(e)+Nr(r)}var n=b(t);throw new Error("0x"+n+" is not a valid UTF-8 or UTF-16 codepoint.")},jr=function(t){function e(e){var r=t.call(this)||this;return r.value=e,r}return r(e,t),e.prototype.asBytes=function(){for(var t=[],e="",r=!1,n=function(e){void 0!==e&&t.push(e),r=!1},o=0,i=this.value.length;o<i;o++){var a=this.value[o],s=g(a),u=this.value[o+1];r?s===se.Newline||s===se.CarriageReturn?n():s===se.n?n(se.Newline):s===se.r?n(se.CarriageReturn):s===se.t?n(se.Tab):s===se.b?n(se.Backspace):s===se.f?n(se.FormFeed):s===se.LeftParen?n(se.LeftParen):s===se.RightParen?n(se.RightParen):s===se.Backspace?n(se.BackSlash):s>=se.Zero&&s<=se.Seven?3!==(e+=a).length&&u>="0"&&u<="7"||(n(parseInt(e,8)),e=""):n(s):s===se.BackSlash?r=!0:n(s)}return new Uint8Array(t)},e.prototype.decodeText=function(){var t=this.asBytes();return lt(t)?it(t):ce(t)},e.prototype.decodeDate=function(){var t=this.decodeText(),e=B(t);if(!e)throw new Fe(t);return e},e.prototype.asString=function(){return this.value},e.prototype.clone=function(){return e.of(this.value)},e.prototype.toString=function(){return"("+this.value+")"},e.prototype.sizeInBytes=function(){return this.value.length+2},e.prototype.copyBytesInto=function(t,e){return t[e++]=se.LeftParen,e+=S(this.value,t,e),t[e++]=se.RightParen,this.value.length+2},e.of=function(t){return new e(t)},e.fromDate=function(t){return new e("D:"+w(String(t.getUTCFullYear()),4,"0")+w(String(t.getUTCMonth()+1),2,"0")+w(String(t.getUTCDate()),2,"0")+w(String(t.getUTCHours()),2,"0")+w(String(t.getUTCMinutes()),2,"0")+w(String(t.getUTCSeconds()),2,"0")+"Z")},e}(ze),Mr=function(){function t(t,e,r,n){var o=this;this.allGlyphsInFontSortedById=function(){for(var t=new Array(o.font.characterSet.length),e=0,r=t.length;e<r;e++){var n=o.font.characterSet[e];t[e]=o.font.glyphForCodePoint(n)}return K(t.sort(G),(function(t){return t.id}))},this.font=t,this.scale=1e3/this.font.unitsPerEm,this.fontData=e,this.fontName=this.font.postscriptName||"Font",this.customName=r,this.fontFeatures=n,this.baseFontName="",this.glyphCache=fe.populatedBy(this.allGlyphsInFontSortedById)}return t.for=function(e,r,n,a){return o(this,0,void 0,(function(){return i(this,(function(o){switch(o.label){case 0:return[4,e.create(r)];case 1:return[2,new t(o.sent(),r,n,a)]}}))}))},t.prototype.encodeText=function(t){for(var e=this.font.layout(t,this.fontFeatures).glyphs,r=new Array(e.length),n=0,o=e.length;n<o;n++)r[n]=m(e[n].id,4);return Or.of(r.join(""))},t.prototype.widthOfTextAtSize=function(t,e){for(var r=this.font.layout(t,this.fontFeatures).glyphs,n=0,o=0,i=r.length;o<i;o++)n+=r[o].advanceWidth*this.scale;return n*(e/1e3)},t.prototype.heightOfFontAtSize=function(t,e){void 0===e&&(e={});var r=e.descender,n=void 0===r||r,o=this.font,i=o.ascent,a=o.descent,s=o.bbox,u=(i||s.maxY)*this.scale-(a||s.minY)*this.scale;return n||(u-=Math.abs(a)||0),u/1e3*t},t.prototype.sizeOfFontAtHeight=function(t){var e=this.font,r=e.ascent,n=e.descent,o=e.bbox;return 1e3*t/((r||o.maxY)*this.scale-(n||o.minY)*this.scale)},t.prototype.embedIntoContext=function(t,e){return this.baseFontName=this.customName||t.addRandomSuffix(this.fontName),this.embedFontDict(t,e)},t.prototype.embedFontDict=function(t,e){return o(this,0,void 0,(function(){var r,n,o;return i(this,(function(i){switch(i.label){case 0:return[4,this.embedCIDFontDict(t)];case 1:return r=i.sent(),n=this.embedUnicodeCmap(t),o=t.obj({Type:"Font",Subtype:"Type0",BaseFont:this.baseFontName,Encoding:"Identity-H",DescendantFonts:[r],ToUnicode:n}),e?(t.assign(e,o),[2,e]):[2,t.register(o)]}}))}))},t.prototype.isCFF=function(){return this.font.cff},t.prototype.embedCIDFontDict=function(t){return o(this,0,void 0,(function(){var e,r;return i(this,(function(n){switch(n.label){case 0:return[4,this.embedFontDescriptor(t)];case 1:return e=n.sent(),r=t.obj({Type:"Font",Subtype:this.isCFF()?"CIDFontType0":"CIDFontType2",CIDToGIDMap:"Identity",BaseFont:this.baseFontName,CIDSystemInfo:{Registry:jr.of("Adobe"),Ordering:jr.of("Identity"),Supplement:0},FontDescriptor:e,W:this.computeWidths()}),[2,t.register(r)]}}))}))},t.prototype.embedFontDescriptor=function(t){return o(this,0,void 0,(function(){var e,r,n,o,a,s,u,c,f,h,l,d,p,y,g;return i(this,(function(i){switch(i.label){case 0:return[4,this.embedFontStream(t)];case 1:return e=i.sent(),r=this.scale,n=this.font,o=n.italicAngle,a=n.ascent,s=n.descent,u=n.capHeight,c=n.xHeight,f=this.font.bbox,h=f.minX,l=f.minY,d=f.maxX,p=f.maxY,y=t.obj(((g={Type:"FontDescriptor",FontName:this.baseFontName,Flags:(x=this.font,F=x["OS/2"]?x["OS/2"].sFamilyClass:0,v={fixedPitch:x.post.isFixedPitch,serif:1<=F&&F<=7,script:10===F,italic:x.head.macStyle.italic},m=0,b=function(t){m|=1<<t-1},v.fixedPitch&&b(1),v.serif&&b(2),b(3),v.script&&b(4),v.nonsymbolic&&b(6),v.italic&&b(7),v.allCap&&b(17),v.smallCap&&b(18),v.forceBold&&b(19),m),FontBBox:[h*r,l*r,d*r,p*r],ItalicAngle:o,Ascent:a*r,Descent:s*r,CapHeight:(u||a)*r,XHeight:(c||0)*r,StemV:0})[this.isCFF()?"FontFile3":"FontFile2"]=e,g)),[2,t.register(y)]}var v,m,b,x,F}))}))},t.prototype.serializeFont=function(){return o(this,0,void 0,(function(){return i(this,(function(t){return[2,this.fontData]}))}))},t.prototype.embedFontStream=function(t){return o(this,0,void 0,(function(){var e,r,n;return i(this,(function(o){switch(o.label){case 0:return n=(r=t).flateStream,[4,this.serializeFont()];case 1:return e=n.apply(r,[o.sent(),{Subtype:this.isCFF()?"CIDFontType0C":void 0}]),[2,t.register(e)]}}))}))},t.prototype.embedUnicodeCmap=function(t){var e=function(t,e){for(var r=new Array(t.length),n=0,o=t.length;n<o;n++){var i=t[n],a=Rr(Nr(e(i))),s=Rr.apply(void 0,i.codePoints.map(Dr));r[n]=[a,s]}return Ar(r)}(this.glyphCache.access(),this.glyphId.bind(this)),r=t.flateStream(e);return t.register(r)},t.prototype.glyphId=function(t){return t?t.id:-1},t.prototype.computeWidths=function(){for(var t=this.glyphCache.access(),e=[],r=[],n=0,o=t.length;n<o;n++){var i=t[n],a=t[n-1],s=this.glyphId(i),u=this.glyphId(a);0===n?e.push(s):s-u!==1&&(e.push(r),e.push(s),r=[]),r.push(i.advanceWidth*this.scale)}return e.push(r),e},t}(),Vr=function(t){function e(e,r,n,o){var i=t.call(this,e,r,n,o)||this;return i.subset=i.font.createSubset(),i.glyphs=[],i.glyphCache=fe.populatedBy((function(){return i.glyphs})),i.glyphIdMap=new Map,i}return r(e,t),e.for=function(t,r,n,a){return o(this,0,void 0,(function(){return i(this,(function(o){switch(o.label){case 0:return[4,t.create(r)];case 1:return[2,new e(o.sent(),r,n,a)]}}))}))},e.prototype.encodeText=function(t){for(var e=this.font.layout(t,this.fontFeatures).glyphs,r=new Array(e.length),n=0,o=e.length;n<o;n++){var i=e[n],a=this.subset.includeGlyph(i);this.glyphs[a-1]=i,this.glyphIdMap.set(i.id,a),r[n]=m(a,4)}return this.glyphCache.invalidate(),Or.of(r.join(""))},e.prototype.isCFF=function(){return this.subset.cff},e.prototype.glyphId=function(t){return t?this.glyphIdMap.get(t.id):-1},e.prototype.serializeFont=function(){var t=this;return new Promise((function(e,r){var n=[];t.subset.encodeStream().on("data",(function(t){return n.push(t)})).on("end",(function(){return e(q(n))})).on("error",(function(t){return r(t)}))}))},e}(Mr);(Cr=Sr||(Sr={})).Source="Source",Cr.Data="Data",Cr.Alternative="Alternative",Cr.Supplement="Supplement",Cr.EncryptedPayload="EncryptedPayload",Cr.FormData="EncryptedPayload",Cr.Schema="Schema",Cr.Unspecified="Unspecified";var Br,Ur,Ir=function(){function t(t,e,r){void 0===r&&(r={}),this.fileData=t,this.fileName=e,this.options=r}return t.for=function(e,r,n){return void 0===n&&(n={}),new t(e,r,n)},t.prototype.embedIntoContext=function(t,e){return o(this,0,void 0,(function(){var r,n,o,a,s,u,c,f,h;return i(this,(function(i){return r=this.options,n=r.mimeType,o=r.description,a=r.creationDate,s=r.modificationDate,u=r.afRelationship,c=t.flateStream(this.fileData,{Type:"EmbeddedFile",Subtype:null!=n?n:void 0,Params:{Size:this.fileData.length,CreationDate:a?jr.fromDate(a):void 0,ModDate:s?jr.fromDate(s):void 0}}),f=t.register(c),h=t.obj({Type:"Filespec",F:jr.of(this.fileName),UF:Or.fromText(this.fileName),EF:{F:f},Desc:o?Or.fromText(o):void 0,AFRelationship:null!=u?u:void 0}),e?(t.assign(e,h),[2,e]):[2,t.register(h)]}))}))},t}(),Wr=[65472,65473,65474,65475,65477,65478,65479,65480,65481,65482,65483,65484,65485,65486,65487];(Ur=Br||(Br={})).DeviceGray="DeviceGray",Ur.DeviceRGB="DeviceRGB",Ur.DeviceCMYK="DeviceCMYK";var zr,qr,Er,Gr={1:Br.DeviceGray,3:Br.DeviceRGB,4:Br.DeviceCMYK},Kr=function(){function t(t,e,r,n,o){this.imageData=t,this.bitsPerComponent=e,this.width=r,this.height=n,this.colorSpace=o}return t.for=function(e){return o(this,0,void 0,(function(){var r,n,o,a,s,u,c,f;return i(this,(function(i){if(r=new DataView(e.buffer),65496!==r.getUint16(0))throw new Error("SOI not found in JPEG");for(n=2;n<r.byteLength&&(o=r.getUint16(n),n+=2,!Wr.includes(o));)n+=r.getUint16(n);if(!Wr.includes(o))throw new Error("Invalid JPEG");if(n+=2,a=r.getUint8(n++),s=r.getUint16(n),n+=2,u=r.getUint16(n),n+=2,c=r.getUint8(n++),!(f=Gr[c]))throw new Error("Unknown JPEG channel.");return[2,new t(e,a,u,s,f)]}))}))},t.prototype.embedIntoContext=function(t,e){return o(this,0,void 0,(function(){var r;return i(this,(function(n){return r=t.stream(this.imageData,{Type:"XObject",Subtype:"Image",BitsPerComponent:this.bitsPerComponent,Width:this.width,Height:this.height,ColorSpace:this.colorSpace,Filter:"DCTDecode",Decode:this.colorSpace===Br.DeviceCMYK?[1,0,1,0,1,0,1,0]:void 0}),e?(t.assign(e,r),[2,e]):[2,t.register(r)]}))}))},t}(),Lr={};Lr.toRGBA8=function(t){var e=t.width,r=t.height;if(null==t.tabs.acTL)return[Lr.toRGBA8.decodeImage(t.data,e,r,t).buffer];var n=[];null==t.frames[0].data&&(t.frames[0].data=t.data);for(var o=e*r*4,i=new Uint8Array(o),a=new Uint8Array(o),s=new Uint8Array(o),u=0;u<t.frames.length;u++){var c=t.frames[u],f=c.rect.x,h=c.rect.y,l=c.rect.width,d=c.rect.height,p=Lr.toRGBA8.decodeImage(c.data,l,d,t);if(0!=u)for(var y=0;y<o;y++)s[y]=i[y];if(0==c.blend?Lr._copyTile(p,l,d,i,e,r,f,h,0):1==c.blend&&Lr._copyTile(p,l,d,i,e,r,f,h,1),n.push(i.buffer.slice(0)),0==c.dispose);else if(1==c.dispose)Lr._copyTile(a,l,d,i,e,r,f,h,0);else if(2==c.dispose)for(y=0;y<o;y++)i[y]=s[y]}return n},Lr.toRGBA8.decodeImage=function(t,e,r,n){var o=e*r,i=Lr.decode._getBPP(n),a=Math.ceil(e*i/8),s=new Uint8Array(4*o),u=new Uint32Array(s.buffer),c=n.ctype,f=n.depth,h=Lr._bin.readUshort;if(6==c){var l=o<<2;if(8==f)for(var d=0;d<l;d+=4)s[d]=t[d],s[d+1]=t[d+1],s[d+2]=t[d+2],s[d+3]=t[d+3];if(16==f)for(d=0;d<l;d++)s[d]=t[d<<1]}else if(2==c){var p=n.tabs.tRNS;if(null==p){if(8==f)for(d=0;d<o;d++){var y=3*d;u[d]=255<<24|t[y+2]<<16|t[y+1]<<8|t[y]}if(16==f)for(d=0;d<o;d++){y=6*d;u[d]=255<<24|t[y+4]<<16|t[y+2]<<8|t[y]}}else{var g=p[0],v=p[1],m=p[2];if(8==f)for(d=0;d<o;d++){var b=d<<2;y=3*d;u[d]=255<<24|t[y+2]<<16|t[y+1]<<8|t[y],t[y]==g&&t[y+1]==v&&t[y+2]==m&&(s[b+3]=0)}if(16==f)for(d=0;d<o;d++){b=d<<2,y=6*d;u[d]=255<<24|t[y+4]<<16|t[y+2]<<8|t[y],h(t,y)==g&&h(t,y+2)==v&&h(t,y+4)==m&&(s[b+3]=0)}}}else if(3==c){var x=n.tabs.PLTE,F=n.tabs.tRNS,w=F?F.length:0;if(1==f)for(var S=0;S<r;S++){var C=S*a,T=S*e;for(d=0;d<e;d++){b=T+d<<2;var k=3*(O=t[C+(d>>3)]>>7-(7&d)&1);s[b]=x[k],s[b+1]=x[k+1],s[b+2]=x[k+2],s[b+3]=O<w?F[O]:255}}if(2==f)for(S=0;S<r;S++)for(C=S*a,T=S*e,d=0;d<e;d++){b=T+d<<2,k=3*(O=t[C+(d>>2)]>>6-((3&d)<<1)&3);s[b]=x[k],s[b+1]=x[k+1],s[b+2]=x[k+2],s[b+3]=O<w?F[O]:255}if(4==f)for(S=0;S<r;S++)for(C=S*a,T=S*e,d=0;d<e;d++){b=T+d<<2,k=3*(O=t[C+(d>>1)]>>4-((1&d)<<2)&15);s[b]=x[k],s[b+1]=x[k+1],s[b+2]=x[k+2],s[b+3]=O<w?F[O]:255}if(8==f)for(d=0;d<o;d++){var O;b=d<<2,k=3*(O=t[d]);s[b]=x[k],s[b+1]=x[k+1],s[b+2]=x[k+2],s[b+3]=O<w?F[O]:255}}else if(4==c){if(8==f)for(d=0;d<o;d++){b=d<<2;var P=t[A=d<<1];s[b]=P,s[b+1]=P,s[b+2]=P,s[b+3]=t[A+1]}if(16==f)for(d=0;d<o;d++){var A;b=d<<2,P=t[A=d<<2];s[b]=P,s[b+1]=P,s[b+2]=P,s[b+3]=t[A+2]}}else if(0==c)for(g=n.tabs.tRNS?n.tabs.tRNS:-1,S=0;S<r;S++){var R=S*a,N=S*e;if(1==f)for(var D=0;D<e;D++){var j=(P=255*(t[R+(D>>>3)]>>>7-(7&D)&1))==255*g?0:255;u[N+D]=j<<24|P<<16|P<<8|P}else if(2==f)for(D=0;D<e;D++){j=(P=85*(t[R+(D>>>2)]>>>6-((3&D)<<1)&3))==85*g?0:255;u[N+D]=j<<24|P<<16|P<<8|P}else if(4==f)for(D=0;D<e;D++){j=(P=17*(t[R+(D>>>1)]>>>4-((1&D)<<2)&15))==17*g?0:255;u[N+D]=j<<24|P<<16|P<<8|P}else if(8==f)for(D=0;D<e;D++){j=(P=t[R+D])==g?0:255;u[N+D]=j<<24|P<<16|P<<8|P}else if(16==f)for(D=0;D<e;D++){P=t[R+(D<<1)],j=h(t,R+(D<<d))==g?0:255;u[N+D]=j<<24|P<<16|P<<8|P}}return s},Lr.decode=function(t){for(var e,r=new Uint8Array(t),n=8,o=Lr._bin,i=o.readUshort,a=o.readUint,s={tabs:{},frames:[]},u=new Uint8Array(r.length),c=0,f=0,h=[137,80,78,71,13,10,26,10],l=0;l<8;l++)if(r[l]!=h[l])throw"The input is not a PNG file!";for(;n<r.length;){var d=o.readUint(r,n);n+=4;var p=o.readASCII(r,n,4);if(n+=4,"IHDR"==p)Lr.decode._IHDR(r,n,s);else if("IDAT"==p){for(l=0;l<d;l++)u[c+l]=r[n+l];c+=d}else if("acTL"==p)s.tabs[p]={num_frames:a(r,n),num_plays:a(r,n+4)},e=new Uint8Array(r.length);else if("fcTL"==p){var y;if(0!=f)(y=s.frames[s.frames.length-1]).data=Lr.decode._decompress(s,e.slice(0,f),y.rect.width,y.rect.height),f=0;var g={x:a(r,n+12),y:a(r,n+16),width:a(r,n+4),height:a(r,n+8)},v=i(r,n+22);v=i(r,n+20)/(0==v?100:v);var m={rect:g,delay:Math.round(1e3*v),dispose:r[n+24],blend:r[n+25]};s.frames.push(m)}else if("fdAT"==p){for(l=0;l<d-4;l++)e[f+l]=r[n+l+4];f+=d-4}else if("pHYs"==p)s.tabs[p]=[o.readUint(r,n),o.readUint(r,n+4),r[n+8]];else if("cHRM"==p){s.tabs[p]=[];for(l=0;l<8;l++)s.tabs[p].push(o.readUint(r,n+4*l))}else if("tEXt"==p){null==s.tabs[p]&&(s.tabs[p]={});var b=o.nextZero(r,n),x=o.readASCII(r,n,b-n),F=o.readASCII(r,b+1,n+d-b-1);s.tabs[p][x]=F}else if("iTXt"==p){null==s.tabs[p]&&(s.tabs[p]={});b=0;var w=n;b=o.nextZero(r,w);x=o.readASCII(r,w,b-w);r[w=b+1],r[w+1],w+=2,b=o.nextZero(r,w),o.readASCII(r,w,b-w),w=b+1,b=o.nextZero(r,w),o.readUTF8(r,w,b-w),w=b+1;F=o.readUTF8(r,w,d-(w-n));s.tabs[p][x]=F}else if("PLTE"==p)s.tabs[p]=o.readBytes(r,n,d);else if("hIST"==p){var S=s.tabs.PLTE.length/3;s.tabs[p]=[];for(l=0;l<S;l++)s.tabs[p].push(i(r,n+2*l))}else if("tRNS"==p)3==s.ctype?s.tabs[p]=o.readBytes(r,n,d):0==s.ctype?s.tabs[p]=i(r,n):2==s.ctype&&(s.tabs[p]=[i(r,n),i(r,n+2),i(r,n+4)]);else if("gAMA"==p)s.tabs[p]=o.readUint(r,n)/1e5;else if("sRGB"==p)s.tabs[p]=r[n];else if("bKGD"==p)0==s.ctype||4==s.ctype?s.tabs[p]=[i(r,n)]:2==s.ctype||6==s.ctype?s.tabs[p]=[i(r,n),i(r,n+2),i(r,n+4)]:3==s.ctype&&(s.tabs[p]=r[n]);else if("IEND"==p)break;n+=d,o.readUint(r,n),n+=4}0!=f&&((y=s.frames[s.frames.length-1]).data=Lr.decode._decompress(s,e.slice(0,f),y.rect.width,y.rect.height),f=0);return s.data=Lr.decode._decompress(s,u,s.width,s.height),delete s.compress,delete s.interlace,delete s.filter,s},Lr.decode._decompress=function(t,e,r,n){var o=Lr.decode._getBPP(t),i=Math.ceil(r*o/8),a=new Uint8Array((i+1+t.interlace)*n);return e=Lr.decode._inflate(e,a),0==t.interlace?e=Lr.decode._filterZero(e,t,0,r,n):1==t.interlace&&(e=Lr.decode._readInterlace(e,t)),e},Lr.decode._inflate=function(t,e){return Lr.inflateRaw(new Uint8Array(t.buffer,2,t.length-6),e)},Lr.inflateRaw=((Er={}).H={},Er.H.N=function(t,e){var r,n,o=Uint8Array,i=0,a=0,s=0,u=0,c=0,f=0,h=0,l=0,d=0;if(3==t[0]&&0==t[1])return e||new o(0);var p=Er.H,y=p.b,g=p.e,v=p.R,m=p.n,b=p.A,x=p.Z,F=p.m,w=null==e;for(w&&(e=new o(t.length>>>2<<3));0==i;)if(i=y(t,d,1),a=y(t,d+1,2),d+=3,0!=a){if(w&&(e=Er.H.W(e,l+(1<<17))),1==a&&(r=F.J,n=F.h,f=511,h=31),2==a){s=g(t,d,5)+257,u=g(t,d+5,5)+1,c=g(t,d+10,4)+4,d+=14;for(var S=1,C=0;C<38;C+=2)F.Q[C]=0,F.Q[C+1]=0;for(C=0;C<c;C++){var T=g(t,d+3*C,3);F.Q[1+(F.X[C]<<1)]=T,T>S&&(S=T)}d+=3*c,m(F.Q,S),b(F.Q,S,F.u),r=F.w,n=F.d,d=v(F.u,(1<<S)-1,s+u,t,d,F.v);var k=p.V(F.v,0,s,F.C);f=(1<<k)-1;var O=p.V(F.v,s,u,F.D);h=(1<<O)-1,m(F.C,k),b(F.C,k,r),m(F.D,O),b(F.D,O,n)}for(;;){var P=r[x(t,d)&f];d+=15&P;var A=P>>>4;if(A>>>8==0)e[l++]=A;else{if(256==A)break;var R=l+A-254;if(A>264){var N=F.q[A-257];R=l+(N>>>3)+g(t,d,7&N),d+=7&N}var D=n[x(t,d)&h];d+=15&D;var j=D>>>4,M=F.c[j],V=(M>>>4)+y(t,d,15&M);for(d+=15&M;l<R;)e[l]=e[l++-V],e[l]=e[l++-V],e[l]=e[l++-V],e[l]=e[l++-V];l=R}}}else{7&d&&(d+=8-(7&d));var B=(d>>>3)+4,U=t[B-4]|t[B-3]<<8;w&&(e=Er.H.W(e,l+U)),e.set(new o(t.buffer,t.byteOffset+B,U),l),d=B+U<<3,l+=U}return e.length==l?e:e.slice(0,l)},Er.H.W=function(t,e){var r=t.length;if(e<=r)return t;var n=new Uint8Array(r<<1);return n.set(t,0),n},Er.H.R=function(t,e,r,n,o,i){for(var a=Er.H.e,s=Er.H.Z,u=0;u<r;){var c=t[s(n,o)&e];o+=15&c;var f=c>>>4;if(f<=15)i[u]=f,u++;else{var h=0,l=0;16==f?(l=3+a(n,o,2),o+=2,h=i[u-1]):17==f?(l=3+a(n,o,3),o+=3):18==f&&(l=11+a(n,o,7),o+=7);for(var d=u+l;u<d;)i[u]=h,u++}}return o},Er.H.V=function(t,e,r,n){for(var o=0,i=0,a=n.length>>>1;i<r;){var s=t[i+e];n[i<<1]=0,n[1+(i<<1)]=s,s>o&&(o=s),i++}for(;i<a;)n[i<<1]=0,n[1+(i<<1)]=0,i++;return o},Er.H.n=function(t,e){for(var r,n,o,i,a=Er.H.m,s=t.length,u=a.j,c=0;c<=e;c++)u[c]=0;for(c=1;c<s;c+=2)u[t[c]]++;var f=a.K;for(r=0,u[0]=0,n=1;n<=e;n++)r=r+u[n-1]<<1,f[n]=r;for(o=0;o<s;o+=2)0!=(i=t[o+1])&&(t[o]=f[i],f[i]++)},Er.H.A=function(t,e,r){for(var n=t.length,o=Er.H.m.r,i=0;i<n;i+=2)if(0!=t[i+1])for(var a=i>>1,s=t[i+1],u=a<<4|s,c=e-s,f=t[i]<<c,h=f+(1<<c);f!=h;)r[o[f]>>>15-e]=u,f++},Er.H.l=function(t,e){for(var r=Er.H.m.r,n=15-e,o=0;o<t.length;o+=2){var i=t[o]<<e-t[o+1];t[o]=r[i]>>>n}},Er.H.M=function(t,e,r){r<<=7&e;var n=e>>>3;t[n]|=r,t[n+1]|=r>>>8},Er.H.I=function(t,e,r){r<<=7&e;var n=e>>>3;t[n]|=r,t[n+1]|=r>>>8,t[n+2]|=r>>>16},Er.H.e=function(t,e,r){return(t[e>>>3]|t[(e>>>3)+1]<<8)>>>(7&e)&(1<<r)-1},Er.H.b=function(t,e,r){return(t[e>>>3]|t[(e>>>3)+1]<<8|t[(e>>>3)+2]<<16)>>>(7&e)&(1<<r)-1},Er.H.Z=function(t,e){return(t[e>>>3]|t[(e>>>3)+1]<<8|t[(e>>>3)+2]<<16)>>>(7&e)},Er.H.i=function(t,e){return(t[e>>>3]|t[(e>>>3)+1]<<8|t[(e>>>3)+2]<<16|t[(e>>>3)+3]<<24)>>>(7&e)},Er.H.m=(zr=Uint16Array,qr=Uint32Array,{K:new zr(16),j:new zr(16),X:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],T:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],q:new zr(32),p:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],z:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],c:new qr(32),J:new zr(512),_:[],h:new zr(32),$:[],w:new zr(32768),C:[],v:[],d:new zr(32768),D:[],u:new zr(512),Q:[],r:new zr(32768),s:new qr(286),Y:new qr(30),a:new qr(19),t:new qr(15e3),k:new zr(65536),g:new zr(32768)}),function(){for(var t=Er.H.m,e=0;e<32768;e++){var r=e;r=(4278255360&(r=(4042322160&(r=(3435973836&(r=(2863311530&r)>>>1|(1431655765&r)<<1))>>>2|(858993459&r)<<2))>>>4|(252645135&r)<<4))>>>8|(16711935&r)<<8,t.r[e]=(r>>>16|r<<16)>>>17}function n(t,e,r){for(;0!=e--;)t.push(0,r)}for(e=0;e<32;e++)t.q[e]=t.S[e]<<3|t.T[e],t.c[e]=t.p[e]<<4|t.z[e];n(t._,144,8),n(t._,112,9),n(t._,24,7),n(t._,8,8),Er.H.n(t._,9),Er.H.A(t._,9,t.J),Er.H.l(t._,9),n(t.$,32,5),Er.H.n(t.$,5),Er.H.A(t.$,5,t.h),Er.H.l(t.$,5),n(t.Q,19,0),n(t.C,286,0),n(t.D,30,0),n(t.v,320,0)}(),Er.H.N),Lr.decode._readInterlace=function(t,e){for(var r=e.width,n=e.height,o=Lr.decode._getBPP(e),i=o>>3,a=Math.ceil(r*o/8),s=new Uint8Array(n*a),u=0,c=[0,0,4,0,2,0,1],f=[0,4,0,2,0,1,0],h=[8,8,8,4,4,2,2],l=[8,8,4,4,2,2,1],d=0;d<7;){for(var p=h[d],y=l[d],g=0,v=0,m=c[d];m<n;)m+=p,v++;for(var b=f[d];b<r;)b+=y,g++;var x=Math.ceil(g*o/8);Lr.decode._filterZero(t,e,u,g,v);for(var F=0,w=c[d];w<n;){for(var S=f[d],C=u+F*x<<3;S<r;){var T;if(1==o)T=(T=t[C>>3])>>7-(7&C)&1,s[w*a+(S>>3)]|=T<<7-(7&S);if(2==o)T=(T=t[C>>3])>>6-(7&C)&3,s[w*a+(S>>2)]|=T<<6-((3&S)<<1);if(4==o)T=(T=t[C>>3])>>4-(7&C)&15,s[w*a+(S>>1)]|=T<<4-((1&S)<<2);if(o>=8)for(var k=w*a+S*i,O=0;O<i;O++)s[k+O]=t[(C>>3)+O];C+=o,S+=y}F++,w+=p}g*v!=0&&(u+=v*(1+x)),d+=1}return s},Lr.decode._getBPP=function(t){return[1,null,3,1,2,null,4][t.ctype]*t.depth},Lr.decode._filterZero=function(t,e,r,n,o){var i=Lr.decode._getBPP(e),a=Math.ceil(n*i/8),s=Lr.decode._paeth;i=Math.ceil(i/8);var u=0,c=1,f=t[r],h=0;if(f>1&&(t[r]=[0,0,1][f-2]),3==f)for(h=i;h<a;h++)t[h+1]=t[h+1]+(t[h+1-i]>>>1)&255;for(var l=0;l<o;l++)if(h=0,0==(f=t[(c=(u=r+l*a)+l+1)-1]))for(;h<a;h++)t[u+h]=t[c+h];else if(1==f){for(;h<i;h++)t[u+h]=t[c+h];for(;h<a;h++)t[u+h]=t[c+h]+t[u+h-i]}else if(2==f)for(;h<a;h++)t[u+h]=t[c+h]+t[u+h-a];else if(3==f){for(;h<i;h++)t[u+h]=t[c+h]+(t[u+h-a]>>>1);for(;h<a;h++)t[u+h]=t[c+h]+(t[u+h-a]+t[u+h-i]>>>1)}else{for(;h<i;h++)t[u+h]=t[c+h]+s(0,t[u+h-a],0);for(;h<a;h++)t[u+h]=t[c+h]+s(t[u+h-i],t[u+h-a],t[u+h-i-a])}return t},Lr.decode._paeth=function(t,e,r){var n=t+e-r,o=n-t,i=n-e,a=n-r;return o*o<=i*i&&o*o<=a*a?t:i*i<=a*a?e:r},Lr.decode._IHDR=function(t,e,r){var n=Lr._bin;r.width=n.readUint(t,e),e+=4,r.height=n.readUint(t,e),e+=4,r.depth=t[e],e++,r.ctype=t[e],e++,r.compress=t[e],e++,r.filter=t[e],e++,r.interlace=t[e],e++},Lr._bin={nextZero:function(t,e){for(;0!=t[e];)e++;return e},readUshort:function(t,e){return t[e]<<8|t[e+1]},writeUshort:function(t,e,r){t[e]=r>>8&255,t[e+1]=255&r},readUint:function(t,e){return 16777216*t[e]+(t[e+1]<<16|t[e+2]<<8|t[e+3])},writeUint:function(t,e,r){t[e]=r>>24&255,t[e+1]=r>>16&255,t[e+2]=r>>8&255,t[e+3]=255&r},readASCII:function(t,e,r){for(var n="",o=0;o<r;o++)n+=String.fromCharCode(t[e+o]);return n},writeASCII:function(t,e,r){for(var n=0;n<r.length;n++)t[e+n]=r.charCodeAt(n)},readBytes:function(t,e,r){for(var n=[],o=0;o<r;o++)n.push(t[e+o]);return n},pad:function(t){return t.length<2?"0"+t:t},readUTF8:function(t,e,r){for(var n,o="",i=0;i<r;i++)o+="%"+Lr._bin.pad(t[e+i].toString(16));try{n=decodeURIComponent(o)}catch(a){return Lr._bin.readASCII(t,e,r)}return n}},Lr._copyTile=function(t,e,r,n,o,i,a,s,u){for(var c=Math.min(e,o),f=Math.min(r,i),h=0,l=0,d=0;d<f;d++)for(var p=0;p<c;p++)if(a>=0&&s>=0?(h=d*e+p<<2,l=(s+d)*o+a+p<<2):(h=(-s+d)*e-a+p<<2,l=d*o+p<<2),0==u)n[l]=t[h],n[l+1]=t[h+1],n[l+2]=t[h+2],n[l+3]=t[h+3];else if(1==u){var y=t[h+3]*(1/255),g=t[h]*y,v=t[h+1]*y,m=t[h+2]*y,b=n[l+3]*(1/255),x=n[l]*b,F=n[l+1]*b,w=n[l+2]*b,S=1-y,C=y+b*S,T=0==C?0:1/C;n[l+3]=255*C,n[l+0]=(g+x*S)*T,n[l+1]=(v+F*S)*T,n[l+2]=(m+w*S)*T}else if(2==u){y=t[h+3],g=t[h],v=t[h+1],m=t[h+2],b=n[l+3],x=n[l],F=n[l+1],w=n[l+2];y==b&&g==x&&v==F&&m==w?(n[l]=0,n[l+1]=0,n[l+2]=0,n[l+3]=0):(n[l]=g,n[l+1]=v,n[l+2]=m,n[l+3]=y)}else if(3==u){y=t[h+3],g=t[h],v=t[h+1],m=t[h+2],b=n[l+3],x=n[l],F=n[l+1],w=n[l+2];if(y==b&&g==x&&v==F&&m==w)continue;if(y<220&&b>20)return!1}return!0},Lr.encode=function(t,e,r,n,o,i,a){null==n&&(n=0),null==a&&(a=!1);var s=Lr.encode.compress(t,e,r,n,[!1,!1,!1,0,a]);return Lr.encode.compressPNG(s,-1),Lr.encode._main(s,e,r,o,i)},Lr.encodeLL=function(t,e,r,n,o,i,a,s){for(var u={ctype:0+(1==n?0:2)+(0==o?0:4),depth:i,frames:[]},c=(n+o)*i,f=c*e,h=0;h<t.length;h++)u.frames.push({rect:{x:0,y:0,width:e,height:r},img:new Uint8Array(t[h]),blend:0,dispose:1,bpp:Math.ceil(c/8),bpl:Math.ceil(f/8)});return Lr.encode.compressPNG(u,0,!0),Lr.encode._main(u,e,r,a,s)},Lr.encode._main=function(t,e,r,n,o){null==o&&(o={});var i=Lr.crc.crc,a=Lr._bin.writeUint,s=Lr._bin.writeUshort,u=Lr._bin.writeASCII,c=8,f=t.frames.length>1,h=!1,l=33+(f?20:0);if(null!=o.sRGB&&(l+=13),null!=o.pHYs&&(l+=21),3==t.ctype){for(var d=t.plte.length,p=0;p<d;p++)t.plte[p]>>>24!=255&&(h=!0);l+=8+3*d+4+(h?8+1*d+4:0)}for(var y=0;y<t.frames.length;y++){f&&(l+=38),l+=(C=t.frames[y]).cimg.length+12,0!=y&&(l+=4)}l+=12;var g=new Uint8Array(l),v=[137,80,78,71,13,10,26,10];for(p=0;p<8;p++)g[p]=v[p];if(a(g,c,13),u(g,c+=4,"IHDR"),a(g,c+=4,e),a(g,c+=4,r),g[c+=4]=t.depth,g[++c]=t.ctype,g[++c]=0,g[++c]=0,g[++c]=0,a(g,++c,i(g,c-17,17)),c+=4,null!=o.sRGB&&(a(g,c,1),u(g,c+=4,"sRGB"),g[c+=4]=o.sRGB,a(g,++c,i(g,c-5,5)),c+=4),null!=o.pHYs&&(a(g,c,9),u(g,c+=4,"pHYs"),a(g,c+=4,o.pHYs[0]),a(g,c+=4,o.pHYs[1]),g[c+=4]=o.pHYs[2],a(g,++c,i(g,c-13,13)),c+=4),f&&(a(g,c,8),u(g,c+=4,"acTL"),a(g,c+=4,t.frames.length),a(g,c+=4,null!=o.loop?o.loop:0),a(g,c+=4,i(g,c-12,12)),c+=4),3==t.ctype){a(g,c,3*(d=t.plte.length)),u(g,c+=4,"PLTE"),c+=4;for(p=0;p<d;p++){var m=3*p,b=t.plte[p],x=255&b,F=b>>>8&255,w=b>>>16&255;g[c+m+0]=x,g[c+m+1]=F,g[c+m+2]=w}if(a(g,c+=3*d,i(g,c-3*d-4,3*d+4)),c+=4,h){a(g,c,d),u(g,c+=4,"tRNS"),c+=4;for(p=0;p<d;p++)g[c+p]=t.plte[p]>>>24&255;a(g,c+=d,i(g,c-d-4,d+4)),c+=4}}var S=0;for(y=0;y<t.frames.length;y++){var C=t.frames[y];f&&(a(g,c,26),u(g,c+=4,"fcTL"),a(g,c+=4,S++),a(g,c+=4,C.rect.width),a(g,c+=4,C.rect.height),a(g,c+=4,C.rect.x),a(g,c+=4,C.rect.y),s(g,c+=4,n[y]),s(g,c+=2,1e3),g[c+=2]=C.dispose,g[++c]=C.blend,a(g,++c,i(g,c-30,30)),c+=4);var T=C.cimg;a(g,c,(d=T.length)+(0==y?0:4));var k=c+=4;u(g,c,0==y?"IDAT":"fdAT"),c+=4,0!=y&&(a(g,c,S++),c+=4),g.set(T,c),a(g,c+=d,i(g,k,c-k)),c+=4}return a(g,c,0),u(g,c+=4,"IEND"),a(g,c+=4,i(g,c-4,4)),c+=4,g.buffer},Lr.encode.compressPNG=function(t,e,r){for(var n=0;n<t.frames.length;n++){var o=t.frames[n];o.rect.width;var i=o.rect.height,a=new Uint8Array(i*o.bpl+i);o.cimg=Lr.encode._filterZero(o.img,i,o.bpp,o.bpl,a,e,r)}},Lr.encode.compress=function(t,e,r,n,o){for(var i=o[0],a=o[1],s=o[2],u=o[3],c=o[4],f=6,h=8,l=255,d=0;d<t.length;d++)for(var p=new Uint8Array(t[d]),y=p.length,g=0;g<y;g+=4)l&=p[g+3];var v=255!=l,m=Lr.encode.framize(t,e,r,i,a,s),b={},x=[],F=[];if(0!=n){var w=[];for(g=0;g<m.length;g++)w.push(m[g].img.buffer);var S=Lr.encode.concatRGBA(w),C=Lr.quantize(S,n),T=0,k=new Uint8Array(C.abuf);for(g=0;g<m.length;g++){var O=(L=m[g].img).length;F.push(new Uint8Array(C.inds.buffer,T>>2,O>>2));for(d=0;d<O;d+=4)L[d]=k[T+d],L[d+1]=k[T+d+1],L[d+2]=k[T+d+2],L[d+3]=k[T+d+3];T+=O}for(g=0;g<C.plte.length;g++)x.push(C.plte[g].est.rgba)}else for(d=0;d<m.length;d++){var P=m[d],A=new Uint32Array(P.img.buffer),R=P.rect.width,N=(y=A.length,new Uint8Array(y));F.push(N);for(g=0;g<y;g++){var D=A[g];if(0!=g&&D==A[g-1])N[g]=N[g-1];else if(g>R&&D==A[g-R])N[g]=N[g-R];else{var j=b[D];if(null==j&&(b[D]=j=x.length,x.push(D),x.length>=300))break;N[g]=j}}}var M=x.length;M<=256&&0==c&&(h=M<=2?1:M<=4?2:M<=16?4:8,h=Math.max(h,u));for(d=0;d<m.length;d++){(P=m[d]).rect.x,P.rect.y;R=P.rect.width;var V=P.rect.height,B=P.img;new Uint32Array(B.buffer);var U=4*R,I=4;if(M<=256&&0==c){U=Math.ceil(h*R/8);for(var W=new Uint8Array(U*V),z=F[d],q=0;q<V;q++){g=q*U;var E=q*R;if(8==h)for(var G=0;G<R;G++)W[g+G]=z[E+G];else if(4==h)for(G=0;G<R;G++)W[g+(G>>1)]|=z[E+G]<<4-4*(1&G);else if(2==h)for(G=0;G<R;G++)W[g+(G>>2)]|=z[E+G]<<6-2*(3&G);else if(1==h)for(G=0;G<R;G++)W[g+(G>>3)]|=z[E+G]<<7-1*(7&G)}B=W,f=3,I=1}else if(0==v&&1==m.length){W=new Uint8Array(R*V*3);var K=R*V;for(g=0;g<K;g++){var L,X=4*g;W[L=3*g]=B[X],W[L+1]=B[X+1],W[L+2]=B[X+2]}B=W,f=2,I=3,U=3*R}P.img=B,P.bpl=U,P.bpp=I}return{ctype:f,depth:h,plte:x,frames:m}},Lr.encode.framize=function(t,e,r,n,o,i){for(var a=[],s=0;s<t.length;s++){var u,c=new Uint8Array(t[s]),f=new Uint32Array(c.buffer),h=0,l=0,d=e,p=r,y=n?1:0;if(0!=s){for(var g=i||n||1==s||0!=a[s-2].dispose?1:2,v=0,m=1e9,b=0;b<g;b++){for(var x=new Uint8Array(t[s-1-b]),F=new Uint32Array(t[s-1-b]),w=e,S=r,C=-1,T=-1,k=0;k<r;k++)for(var O=0;O<e;O++){f[M=k*e+O]!=F[M]&&(O<w&&(w=O),O>C&&(C=O),k<S&&(S=k),k>T&&(T=k))}-1==C&&(w=S=C=T=0),o&&(1&~w||w--,1&~S||S--);var P=(C-w+1)*(T-S+1);P<m&&(m=P,v=b,h=w,l=S,d=C-w+1,p=T-S+1)}x=new Uint8Array(t[s-1-v]);1==v&&(a[s-1].dispose=2),u=new Uint8Array(d*p*4),Lr._copyTile(x,e,r,u,d,p,-h,-l,0),1==(y=Lr._copyTile(c,e,r,u,d,p,-h,-l,3)?1:0)?Lr.encode._prepareDiff(c,e,r,u,{x:h,y:l,width:d,height:p}):Lr._copyTile(c,e,r,u,d,p,-h,-l,0)}else u=c.slice(0);a.push({rect:{x:h,y:l,width:d,height:p},img:u,blend:y,dispose:0})}if(n)for(s=0;s<a.length;s++){if(1!=(V=a[s]).blend){var A=V.rect,R=a[s-1].rect,N=Math.min(A.x,R.x),D=Math.min(A.y,R.y),j={x:N,y:D,width:Math.max(A.x+A.width,R.x+R.width)-N,height:Math.max(A.y+A.height,R.y+R.height)-D};a[s-1].dispose=1,s-1!=0&&Lr.encode._updateFrame(t,e,r,a,s-1,j,o),Lr.encode._updateFrame(t,e,r,a,s,j,o)}}if(1!=t.length)for(var M=0;M<a.length;M++){var V;(V=a[M]).rect.width*V.rect.height}return a},Lr.encode._updateFrame=function(t,e,r,n,o,i,a){for(var s=Uint8Array,u=Uint32Array,c=new s(t[o-1]),f=new u(t[o-1]),h=o+1<t.length?new s(t[o+1]):null,l=new s(t[o]),d=new u(l.buffer),p=e,y=r,g=-1,v=-1,m=0;m<i.height;m++)for(var b=0;b<i.width;b++){var x=i.x+b,F=i.y+m,w=F*e+x,S=d[w];0==S||0==n[o-1].dispose&&f[w]==S&&(null==h||0!=h[4*w+3])||(x<p&&(p=x),x>g&&(g=x),F<y&&(y=F),F>v&&(v=F))}-1==g&&(p=y=g=v=0),a&&(1&~p||p--,1&~y||y--),i={x:p,y:y,width:g-p+1,height:v-y+1};var C=n[o];C.rect=i,C.blend=1,C.img=new Uint8Array(i.width*i.height*4),0==n[o-1].dispose?(Lr._copyTile(c,e,r,C.img,i.width,i.height,-i.x,-i.y,0),Lr.encode._prepareDiff(l,e,r,C.img,i)):Lr._copyTile(l,e,r,C.img,i.width,i.height,-i.x,-i.y,0)},Lr.encode._prepareDiff=function(t,e,r,n,o){Lr._copyTile(t,e,r,n,o.width,o.height,-o.x,-o.y,2)},Lr.encode._filterZero=function(e,r,n,o,i,a,s){var u,c=[],f=[0,1,2,3,4];-1!=a?f=[a]:(r*o>5e5||1==n)&&(f=[0]),s&&(u={level:0});for(var h=s&&null!=UZIP?UZIP:t,l=0;l<f.length;l++){for(var d=0;d<r;d++)Lr.encode._filterLine(i,e,d,o,n,f[l]);c.push(h.deflate(i,u))}var p,y=1e9;for(l=0;l<c.length;l++)c[l].length<y&&(p=l,y=c[l].length);return c[p]},Lr.encode._filterLine=function(t,e,r,n,o,i){var a=r*n,s=a+r,u=Lr.decode._paeth;if(t[s]=i,s++,0==i)if(n<500)for(var c=0;c<n;c++)t[s+c]=e[a+c];else t.set(new Uint8Array(e.buffer,a,n),s);else if(1==i){for(c=0;c<o;c++)t[s+c]=e[a+c];for(c=o;c<n;c++)t[s+c]=e[a+c]-e[a+c-o]+256&255}else if(0==r){for(c=0;c<o;c++)t[s+c]=e[a+c];if(2==i)for(c=o;c<n;c++)t[s+c]=e[a+c];if(3==i)for(c=o;c<n;c++)t[s+c]=e[a+c]-(e[a+c-o]>>1)+256&255;if(4==i)for(c=o;c<n;c++)t[s+c]=e[a+c]-u(e[a+c-o],0,0)+256&255}else{if(2==i)for(c=0;c<n;c++)t[s+c]=e[a+c]+256-e[a+c-n]&255;if(3==i){for(c=0;c<o;c++)t[s+c]=e[a+c]+256-(e[a+c-n]>>1)&255;for(c=o;c<n;c++)t[s+c]=e[a+c]+256-(e[a+c-n]+e[a+c-o]>>1)&255}if(4==i){for(c=0;c<o;c++)t[s+c]=e[a+c]+256-u(0,e[a+c-n],0)&255;for(c=o;c<n;c++)t[s+c]=e[a+c]+256-u(e[a+c-o],e[a+c-n],e[a+c-o-n])&255}}},Lr.crc={table:function(){for(var t=new Uint32Array(256),e=0;e<256;e++){for(var r=e,n=0;n<8;n++)1&r?r=3988292384^r>>>1:r>>>=1;t[e]=r}return t}(),update:function(t,e,r,n){for(var o=0;o<n;o++)t=Lr.crc.table[255&(t^e[r+o])]^t>>>8;return t},crc:function(t,e,r){return 4294967295^Lr.crc.update(4294967295,t,e,r)}},Lr.quantize=function(t,e){var r=new Uint8Array(t),n=r.slice(0),o=new Uint32Array(n.buffer),i=Lr.quantize.getKDtree(n,e),a=i[0],s=i[1];Lr.quantize.planeDst;for(var u=r,c=o,f=u.length,h=new Uint8Array(r.length>>2),l=0;l<f;l+=4){var d=u[l]*(1/255),p=u[l+1]*(1/255),y=u[l+2]*(1/255),g=u[l+3]*(1/255),v=Lr.quantize.getNearest(a,d,p,y,g);h[l>>2]=v.ind,c[l>>2]=v.est.rgba}return{abuf:n.buffer,inds:h,plte:s}},Lr.quantize.getKDtree=function(t,e,r){null==r&&(r=1e-4);var n=new Uint32Array(t.buffer),o={i0:0,i1:t.length,bst:null,est:null,tdst:0,left:null,right:null};o.bst=Lr.quantize.stats(t,o.i0,o.i1),o.est=Lr.quantize.estats(o.bst);for(var i=[o];i.length<e;){for(var a=0,s=0,u=0;u<i.length;u++)i[u].est.L>a&&(a=i[u].est.L,s=u);if(a<r)break;var c=i[s],f=Lr.quantize.splitPixels(t,n,c.i0,c.i1,c.est.e,c.est.eMq255);if(c.i0>=f||c.i1<=f)c.est.L=0;else{var h={i0:c.i0,i1:f,bst:null,est:null,tdst:0,left:null,right:null};h.bst=Lr.quantize.stats(t,h.i0,h.i1),h.est=Lr.quantize.estats(h.bst);var l={i0:f,i1:c.i1,bst:null,est:null,tdst:0,left:null,right:null};l.bst={R:[],m:[],N:c.bst.N-h.bst.N};for(u=0;u<16;u++)l.bst.R[u]=c.bst.R[u]-h.bst.R[u];for(u=0;u<4;u++)l.bst.m[u]=c.bst.m[u]-h.bst.m[u];l.est=Lr.quantize.estats(l.bst),c.left=h,c.right=l,i[s]=h,i.push(l)}}i.sort((function(t,e){return e.bst.N-t.bst.N}));for(u=0;u<i.length;u++)i[u].ind=u;return[o,i]},Lr.quantize.getNearest=function(t,e,r,n,o){if(null==t.left)return t.tdst=Lr.quantize.dist(t.est.q,e,r,n,o),t;var i=Lr.quantize.planeDst(t.est,e,r,n,o),a=t.left,s=t.right;i>0&&(a=t.right,s=t.left);var u=Lr.quantize.getNearest(a,e,r,n,o);if(u.tdst<=i*i)return u;var c=Lr.quantize.getNearest(s,e,r,n,o);return c.tdst<u.tdst?c:u},Lr.quantize.planeDst=function(t,e,r,n,o){var i=t.e;return i[0]*e+i[1]*r+i[2]*n+i[3]*o-t.eMq},Lr.quantize.dist=function(t,e,r,n,o){var i=e-t[0],a=r-t[1],s=n-t[2],u=o-t[3];return i*i+a*a+s*s+u*u},Lr.quantize.splitPixels=function(t,e,r,n,o,i){var a=Lr.quantize.vecDot;for(n-=4;r<n;){for(;a(t,r,o)<=i;)r+=4;for(;a(t,n,o)>i;)n-=4;if(r>=n)break;var s=e[r>>2];e[r>>2]=e[n>>2],e[n>>2]=s,r+=4,n-=4}for(;a(t,r,o)>i;)r-=4;return r+4},Lr.quantize.vecDot=function(t,e,r){return t[e]*r[0]+t[e+1]*r[1]+t[e+2]*r[2]+t[e+3]*r[3]},Lr.quantize.stats=function(t,e,r){for(var n=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],o=[0,0,0,0],i=r-e>>2,a=e;a<r;a+=4){var s=t[a]*(1/255),u=t[a+1]*(1/255),c=t[a+2]*(1/255),f=t[a+3]*(1/255);o[0]+=s,o[1]+=u,o[2]+=c,o[3]+=f,n[0]+=s*s,n[1]+=s*u,n[2]+=s*c,n[3]+=s*f,n[5]+=u*u,n[6]+=u*c,n[7]+=u*f,n[10]+=c*c,n[11]+=c*f,n[15]+=f*f}return n[4]=n[1],n[8]=n[2],n[9]=n[6],n[12]=n[3],n[13]=n[7],n[14]=n[11],{R:n,m:o,N:i}},Lr.quantize.estats=function(t){var e=t.R,r=t.m,n=t.N,o=r[0],i=r[1],a=r[2],s=r[3],u=0==n?0:1/n,c=[e[0]-o*o*u,e[1]-o*i*u,e[2]-o*a*u,e[3]-o*s*u,e[4]-i*o*u,e[5]-i*i*u,e[6]-i*a*u,e[7]-i*s*u,e[8]-a*o*u,e[9]-a*i*u,e[10]-a*a*u,e[11]-a*s*u,e[12]-s*o*u,e[13]-s*i*u,e[14]-s*a*u,e[15]-s*s*u],f=c,h=Lr.M4,l=[.5,.5,.5,.5],d=0,p=0;if(0!=n)for(var y=0;y<10&&(l=h.multVec(f,l),p=Math.sqrt(h.dot(l,l)),l=h.sml(1/p,l),!(Math.abs(p-d)<1e-9));y++)d=p;var g=[o*u,i*u,a*u,s*u];return{Cov:c,q:g,e:l,L:d,eMq255:h.dot(h.sml(255,g),l),eMq:h.dot(l,g),rgba:(Math.round(255*g[3])<<24|Math.round(255*g[2])<<16|Math.round(255*g[1])<<8|Math.round(255*g[0]))>>>0}},Lr.M4={multVec:function(t,e){return[t[0]*e[0]+t[1]*e[1]+t[2]*e[2]+t[3]*e[3],t[4]*e[0]+t[5]*e[1]+t[6]*e[2]+t[7]*e[3],t[8]*e[0]+t[9]*e[1]+t[10]*e[2]+t[11]*e[3],t[12]*e[0]+t[13]*e[1]+t[14]*e[2]+t[15]*e[3]]},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]+t[3]*e[3]},sml:function(t,e){return[t*e[0],t*e[1],t*e[2],t*e[3]]}},Lr.encode.concatRGBA=function(t){for(var e=0,r=0;r<t.length;r++)e+=t[r].byteLength;var n=new Uint8Array(e),o=0;for(r=0;r<t.length;r++){for(var i=new Uint8Array(t[r]),a=i.length,s=0;s<a;s+=4){var u=i[s],c=i[s+1],f=i[s+2],h=i[s+3];0==h&&(u=c=f=0),n[o+s]=u,n[o+s+1]=c,n[o+s+2]=f,n[o+s+3]=h}o+=a}return n.buffer};var Xr,Hr;(Hr=Xr||(Xr={})).Greyscale="Greyscale",Hr.Truecolour="Truecolour",Hr.IndexedColour="IndexedColour",Hr.GreyscaleWithAlpha="GreyscaleWithAlpha",Hr.TruecolourWithAlpha="TruecolourWithAlpha";var Zr,Yr,Jr,Qr,_r,$r,tn,en,rn=function(){function t(t){var e=Lr.decode(t),r=Lr.toRGBA8(e);if(r.length>1)throw new Error("Animated PNGs are not supported");var n=function(t){for(var e=Math.floor(t.length/4),r=new Uint8Array(3*e),n=new Uint8Array(1*e),o=0,i=0,a=0;o<t.length;)r[i++]=t[o++],r[i++]=t[o++],r[i++]=t[o++],n[a++]=t[o++];return{rgbChannel:r,alphaChannel:n}}(new Uint8Array(r[0])),o=n.rgbChannel,i=n.alphaChannel;this.rgbChannel=o,i.some((function(t){return t<255}))&&(this.alphaChannel=i),this.type=function(t){if(0===t)return Xr.Greyscale;if(2===t)return Xr.Truecolour;if(3===t)return Xr.IndexedColour;if(4===t)return Xr.GreyscaleWithAlpha;if(6===t)return Xr.TruecolourWithAlpha;throw new Error("Unknown color type: "+t)}(e.ctype),this.width=e.width,this.height=e.height,this.bitsPerComponent=8}return t.load=function(e){return new t(e)},t}(),nn=function(){function t(t){this.image=t,this.bitsPerComponent=t.bitsPerComponent,this.width=t.width,this.height=t.height,this.colorSpace="DeviceRGB"}return t.for=function(e){return o(this,0,void 0,(function(){return i(this,(function(r){return[2,new t(rn.load(e))]}))}))},t.prototype.embedIntoContext=function(t,e){return o(this,0,void 0,(function(){var r,n;return i(this,(function(o){return r=this.embedAlphaChannel(t),n=t.flateStream(this.image.rgbChannel,{Type:"XObject",Subtype:"Image",BitsPerComponent:this.image.bitsPerComponent,Width:this.image.width,Height:this.image.height,ColorSpace:this.colorSpace,SMask:r}),e?(t.assign(e,n),[2,e]):[2,t.register(n)]}))}))},t.prototype.embedAlphaChannel=function(t){if(this.image.alphaChannel){var e=t.flateStream(this.image.alphaChannel,{Type:"XObject",Subtype:"Image",Height:this.image.height,Width:this.image.width,BitsPerComponent:this.image.bitsPerComponent,ColorSpace:"DeviceGray",Decode:[0,1]});return t.register(e)}},t}(),on=function(){function t(t,e,r){this.bytes=t,this.start=e||0,this.pos=this.start,this.end=e&&r?e+r:this.bytes.length}return Object.defineProperty(t.prototype,"length",{get:function(){return this.end-this.start},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isEmpty",{get:function(){return 0===this.length},enumerable:!1,configurable:!0}),t.prototype.getByte=function(){return this.pos>=this.end?-1:this.bytes[this.pos++]},t.prototype.getUint16=function(){var t=this.getByte(),e=this.getByte();return-1===t||-1===e?-1:(t<<8)+e},t.prototype.getInt32=function(){return(this.getByte()<<24)+(this.getByte()<<16)+(this.getByte()<<8)+this.getByte()},t.prototype.getBytes=function(t,e){void 0===e&&(e=!1);var r=this.bytes,n=this.pos,o=this.end;if(t){var i=n+t;i>o&&(i=o),this.pos=i;a=r.subarray(n,i);return e?new Uint8ClampedArray(a):a}var a=r.subarray(n,o);return e?new Uint8ClampedArray(a):a},t.prototype.peekByte=function(){var t=this.getByte();return this.pos--,t},t.prototype.peekBytes=function(t,e){void 0===e&&(e=!1);var r=this.getBytes(t,e);return this.pos-=r.length,r},t.prototype.skip=function(t){t||(t=1),this.pos+=t},t.prototype.reset=function(){this.pos=this.start},t.prototype.moveStart=function(){this.start=this.pos},t.prototype.makeSubStream=function(e,r){return new t(this.bytes,e,r)},t.prototype.decode=function(){return this.bytes},t}(),an=new Uint8Array(0),sn=function(){function t(t){if(this.pos=0,this.bufferLength=0,this.eof=!1,this.buffer=an,this.minBufferLength=512,t)for(;this.minBufferLength<t;)this.minBufferLength*=2}return Object.defineProperty(t.prototype,"isEmpty",{get:function(){for(;!this.eof&&0===this.bufferLength;)this.readBlock();return 0===this.bufferLength},enumerable:!1,configurable:!0}),t.prototype.getByte=function(){for(var t=this.pos;this.bufferLength<=t;){if(this.eof)return-1;this.readBlock()}return this.buffer[this.pos++]},t.prototype.getUint16=function(){var t=this.getByte(),e=this.getByte();return-1===t||-1===e?-1:(t<<8)+e},t.prototype.getInt32=function(){return(this.getByte()<<24)+(this.getByte()<<16)+(this.getByte()<<8)+this.getByte()},t.prototype.getBytes=function(t,e){var r;void 0===e&&(e=!1);var n=this.pos;if(t){for(this.ensureBuffer(n+t),r=n+t;!this.eof&&this.bufferLength<r;)this.readBlock();var o=this.bufferLength;r>o&&(r=o)}else{for(;!this.eof;)this.readBlock();r=this.bufferLength}this.pos=r;var i=this.buffer.subarray(n,r);return!e||i instanceof Uint8ClampedArray?i:new Uint8ClampedArray(i)},t.prototype.peekByte=function(){var t=this.getByte();return this.pos--,t},t.prototype.peekBytes=function(t,e){void 0===e&&(e=!1);var r=this.getBytes(t,e);return this.pos-=r.length,r},t.prototype.skip=function(t){t||(t=1),this.pos+=t},t.prototype.reset=function(){this.pos=0},t.prototype.makeSubStream=function(t,e){for(var r=t+e;this.bufferLength<=r&&!this.eof;)this.readBlock();return new on(this.buffer,t,e)},t.prototype.decode=function(){for(;!this.eof;)this.readBlock();return this.buffer.subarray(0,this.bufferLength)},t.prototype.readBlock=function(){throw new he(this.constructor.name,"readBlock")},t.prototype.ensureBuffer=function(t){var e=this.buffer;if(t<=e.byteLength)return e;for(var r=this.minBufferLength;r<t;)r*=2;var n=new Uint8Array(r);return n.set(e),this.buffer=n},t}(),un=function(t){return 32===t||9===t||13===t||10===t},cn=function(t){function e(e,r){var n=t.call(this,r)||this;return n.stream=e,n.input=new Uint8Array(5),r&&(r*=.8),n}return r(e,t),e.prototype.readBlock=function(){for(var t=this.stream,e=t.getByte();un(e);)e=t.getByte();if(-1!==e&&126!==e){var r,n,o=this.bufferLength;if(122===e){for(r=this.ensureBuffer(o+4),n=0;n<4;++n)r[o+n]=0;this.bufferLength+=4}else{var i=this.input;for(i[0]=e,n=1;n<5;++n){for(e=t.getByte();un(e);)e=t.getByte();if(i[n]=e,-1===e||126===e)break}if(r=this.ensureBuffer(o+n-1),this.bufferLength+=n-1,n<5){for(;n<5;++n)i[n]=117;this.eof=!0}var a=0;for(n=0;n<5;++n)a=85*a+(i[n]-33);for(n=3;n>=0;--n)r[o+n]=255&a,a>>=8}}else this.eof=!0},e}(sn),fn=function(t){function e(e,r){var n=t.call(this,r)||this;return n.stream=e,n.firstDigit=-1,r&&(r*=.5),n}return r(e,t),e.prototype.readBlock=function(){var t=this.stream.getBytes(8e3);if(t.length){for(var e=t.length+1>>1,r=this.ensureBuffer(this.bufferLength+e),n=this.bufferLength,o=this.firstDigit,i=0,a=t.length;i<a;i++){var s=t[i],u=void 0;if(s>=48&&s<=57)u=15&s;else{if(!(s>=65&&s<=70||s>=97&&s<=102)){if(62===s){this.eof=!0;break}continue}u=9+(15&s)}o<0?o=u:(r[n++]=o<<4|u,o=-1)}o>=0&&this.eof&&(r[n++]=o<<4,o=-1),this.firstDigit=o,this.bufferLength=n}else this.eof=!0},e}(sn),hn=new Int32Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),ln=new Int32Array([3,4,5,6,7,8,9,10,65547,65549,65551,65553,131091,131095,131099,131103,196643,196651,196659,196667,262211,262227,262243,262259,327811,327843,327875,327907,258,258,258]),dn=new Int32Array([1,2,3,4,65541,65543,131081,131085,196625,196633,262177,262193,327745,327777,393345,393409,459009,459137,524801,525057,590849,591361,657409,658433,724993,727041,794625,798721,868353,876545]),pn=[new Int32Array([459008,524368,524304,524568,459024,524400,524336,590016,459016,524384,524320,589984,524288,524416,524352,590048,459012,524376,524312,589968,459028,524408,524344,590032,459020,524392,524328,59e4,524296,524424,524360,590064,459010,524372,524308,524572,459026,524404,524340,590024,459018,524388,524324,589992,524292,524420,524356,590056,459014,524380,524316,589976,459030,524412,524348,590040,459022,524396,524332,590008,524300,524428,524364,590072,459009,524370,524306,524570,459025,524402,524338,590020,459017,524386,524322,589988,524290,524418,524354,590052,459013,524378,524314,589972,459029,524410,524346,590036,459021,524394,524330,590004,524298,524426,524362,590068,459011,524374,524310,524574,459027,524406,524342,590028,459019,524390,524326,589996,524294,524422,524358,590060,459015,524382,524318,589980,459031,524414,524350,590044,459023,524398,524334,590012,524302,524430,524366,590076,459008,524369,524305,524569,459024,524401,524337,590018,459016,524385,524321,589986,524289,524417,524353,590050,459012,524377,524313,589970,459028,524409,524345,590034,459020,524393,524329,590002,524297,524425,524361,590066,459010,524373,524309,524573,459026,524405,524341,590026,459018,524389,524325,589994,524293,524421,524357,590058,459014,524381,524317,589978,459030,524413,524349,590042,459022,524397,524333,590010,524301,524429,524365,590074,459009,524371,524307,524571,459025,524403,524339,590022,459017,524387,524323,589990,524291,524419,524355,590054,459013,524379,524315,589974,459029,524411,524347,590038,459021,524395,524331,590006,524299,524427,524363,590070,459011,524375,524311,524575,459027,524407,524343,590030,459019,524391,524327,589998,524295,524423,524359,590062,459015,524383,524319,589982,459031,524415,524351,590046,459023,524399,524335,590014,524303,524431,524367,590078,459008,524368,524304,524568,459024,524400,524336,590017,459016,524384,524320,589985,524288,524416,524352,590049,459012,524376,524312,589969,459028,524408,524344,590033,459020,524392,524328,590001,524296,524424,524360,590065,459010,524372,524308,524572,459026,524404,524340,590025,459018,524388,524324,589993,524292,524420,524356,590057,459014,524380,524316,589977,459030,524412,524348,590041,459022,524396,524332,590009,524300,524428,524364,590073,459009,524370,524306,524570,459025,524402,524338,590021,459017,524386,524322,589989,524290,524418,524354,590053,459013,524378,524314,589973,459029,524410,524346,590037,459021,524394,524330,590005,524298,524426,524362,590069,459011,524374,524310,524574,459027,524406,524342,590029,459019,524390,524326,589997,524294,524422,524358,590061,459015,524382,524318,589981,459031,524414,524350,590045,459023,524398,524334,590013,524302,524430,524366,590077,459008,524369,524305,524569,459024,524401,524337,590019,459016,524385,524321,589987,524289,524417,524353,590051,459012,524377,524313,589971,459028,524409,524345,590035,459020,524393,524329,590003,524297,524425,524361,590067,459010,524373,524309,524573,459026,524405,524341,590027,459018,524389,524325,589995,524293,524421,524357,590059,459014,524381,524317,589979,459030,524413,524349,590043,459022,524397,524333,590011,524301,524429,524365,590075,459009,524371,524307,524571,459025,524403,524339,590023,459017,524387,524323,589991,524291,524419,524355,590055,459013,524379,524315,589975,459029,524411,524347,590039,459021,524395,524331,590007,524299,524427,524363,590071,459011,524375,524311,524575,459027,524407,524343,590031,459019,524391,524327,589999,524295,524423,524359,590063,459015,524383,524319,589983,459031,524415,524351,590047,459023,524399,524335,590015,524303,524431,524367,590079]),9],yn=[new Int32Array([327680,327696,327688,327704,327684,327700,327692,327708,327682,327698,327690,327706,327686,327702,327694,0,327681,327697,327689,327705,327685,327701,327693,327709,327683,327699,327691,327707,327687,327703,327695,0]),5],gn=function(t){function e(e,r){var n=t.call(this,r)||this;n.stream=e;var o=e.getByte(),i=e.getByte();if(-1===o||-1===i)throw new Error("Invalid header in flate stream: "+o+", "+i);if(8!=(15&o))throw new Error("Unknown compression method in flate stream: "+o+", "+i);if(((o<<8)+i)%31!=0)throw new Error("Bad FCHECK in flate stream: "+o+", "+i);if(32&i)throw new Error("FDICT bit set in flate stream: "+o+", "+i);return n.codeSize=0,n.codeBuf=0,n}return r(e,t),e.prototype.readBlock=function(){var t,e,r=this.stream,n=this.getBits(3);if(1&n&&(this.eof=!0),0!==(n>>=1)){var o,i;if(1===n)o=pn,i=yn;else{if(2!==n)throw new Error("Unknown block type in flate stream");var a=this.getBits(5)+257,s=this.getBits(5)+1,u=this.getBits(4)+4,c=new Uint8Array(hn.length),f=void 0;for(f=0;f<u;++f)c[hn[f]]=this.getBits(3);var h=this.generateHuffmanTable(c);e=0,f=0;for(var l=a+s,d=new Uint8Array(l),p=void 0,y=void 0,g=void 0;f<l;){var v=this.getCode(h);if(16===v)p=2,y=3,g=e;else if(17===v)p=3,y=3,g=e=0;else{if(18!==v){d[f++]=e=v;continue}p=7,y=11,g=e=0}for(var m=this.getBits(p)+y;m-- >0;)d[f++]=g}o=this.generateHuffmanTable(d.subarray(0,a)),i=this.generateHuffmanTable(d.subarray(a,l))}for(var b=(t=this.buffer)?t.length:0,x=this.bufferLength;;){var F=this.getCode(o);if(F<256)x+1>=b&&(b=(t=this.ensureBuffer(x+1)).length),t[x++]=F;else{if(256===F)return void(this.bufferLength=x);var w=(F=ln[F-=257])>>16;w>0&&(w=this.getBits(w)),e=(65535&F)+w,F=this.getCode(i),(w=(F=dn[F])>>16)>0&&(w=this.getBits(w));var S=(65535&F)+w;x+e>=b&&(b=(t=this.ensureBuffer(x+e)).length);for(var C=0;C<e;++C,++x)t[x]=t[x-S]}}}else{var T=void 0;if(-1===(T=r.getByte()))throw new Error("Bad block header in flate stream");var k=T;if(-1===(T=r.getByte()))throw new Error("Bad block header in flate stream");if(k|=T<<8,-1===(T=r.getByte()))throw new Error("Bad block header in flate stream");var O=T;if(-1===(T=r.getByte()))throw new Error("Bad block header in flate stream");if((O|=T<<8)!==(65535&~k)&&(0!==k||0!==O))throw new Error("Bad uncompressed block length in flate stream");this.codeBuf=0,this.codeSize=0;var P=this.bufferLength;t=this.ensureBuffer(P+k);var A=P+k;if(this.bufferLength=A,0===k)-1===r.peekByte()&&(this.eof=!0);else for(var R=P;R<A;++R){if(-1===(T=r.getByte())){this.eof=!0;break}t[R]=T}}},e.prototype.getBits=function(t){for(var e,r=this.stream,n=this.codeSize,o=this.codeBuf;n<t;){if(-1===(e=r.getByte()))throw new Error("Bad encoding in flate stream");o|=e<<n,n+=8}return e=o&(1<<t)-1,this.codeBuf=o>>t,this.codeSize=n-=t,e},e.prototype.getCode=function(t){for(var e,r=this.stream,n=t[0],o=t[1],i=this.codeSize,a=this.codeBuf;i<o&&-1!==(e=r.getByte());)a|=e<<i,i+=8;var s=n[a&(1<<o)-1];"number"==typeof n&&console.log("FLATE:",s);var u=s>>16,c=65535&s;if(u<1||i<u)throw new Error("Bad encoding in flate stream");return this.codeBuf=a>>u,this.codeSize=i-u,c},e.prototype.generateHuffmanTable=function(t){var e,r=t.length,n=0;for(e=0;e<r;++e)t[e]>n&&(n=t[e]);for(var o=1<<n,i=new Int32Array(o),a=1,s=0,u=2;a<=n;++a,s<<=1,u<<=1)for(var c=0;c<r;++c)if(t[c]===a){var f=0,h=s;for(e=0;e<a;++e)f=f<<1|1&h,h>>=1;for(e=f;e<o;e+=u)i[e]=a<<16|c;++s}return[i,n]},e}(sn),vn=function(t){function e(e,r,n){var o=t.call(this,r)||this;o.stream=e,o.cachedData=0,o.bitsCached=0;for(var i=4096,a={earlyChange:n,codeLength:9,nextCode:258,dictionaryValues:new Uint8Array(i),dictionaryLengths:new Uint16Array(i),dictionaryPrevCodes:new Uint16Array(i),currentSequence:new Uint8Array(i),currentSequenceLength:0},s=0;s<256;++s)a.dictionaryValues[s]=s,a.dictionaryLengths[s]=1;return o.lzwState=a,o}return r(e,t),e.prototype.readBlock=function(){var t,e,r,n=1024,o=this.lzwState;if(o){var i=o.earlyChange,a=o.nextCode,s=o.dictionaryValues,u=o.dictionaryLengths,c=o.dictionaryPrevCodes,f=o.codeLength,h=o.prevCode,l=o.currentSequence,d=o.currentSequenceLength,p=0,y=this.bufferLength,g=this.ensureBuffer(this.bufferLength+n);for(t=0;t<512;t++){var v=this.readBits(f),m=d>0;if(!v||v<256)l[0]=v,d=1;else{if(!(v>=258)){if(256===v){f=9,a=258,d=0;continue}this.eof=!0,delete this.lzwState;break}if(v<a)for(e=(d=u[v])-1,r=v;e>=0;e--)l[e]=s[r],r=c[r];else l[d++]=l[0]}if(m&&(c[a]=h,u[a]=u[h]+1,s[a]=l[0],f=++a+i&a+i-1?f:0|Math.min(Math.log(a+i)/.6931471805599453+1,12)),h=v,n<(p+=d)){do{n+=512}while(n<p);g=this.ensureBuffer(this.bufferLength+n)}for(e=0;e<d;e++)g[y++]=l[e]}o.nextCode=a,o.codeLength=f,o.prevCode=h,o.currentSequenceLength=d,this.bufferLength=y}},e.prototype.readBits=function(t){for(var e=this.bitsCached,r=this.cachedData;e<t;){var n=this.stream.getByte();if(-1===n)return this.eof=!0,null;r=r<<8|n,e+=8}return this.bitsCached=e-=t,this.cachedData=r,r>>>e&(1<<t)-1},e}(sn),mn=function(t){function e(e,r){var n=t.call(this,r)||this;return n.stream=e,n}return r(e,t),e.prototype.readBlock=function(){var t=this.stream.getBytes(2);if(!t||t.length<2||128===t[0])this.eof=!0;else{var e,r=this.bufferLength,n=t[0];if(n<128){if((e=this.ensureBuffer(r+n+1))[r++]=t[1],n>0){var o=this.stream.getBytes(n);e.set(o,r),r+=n}}else{n=257-n;var i=t[1];e=this.ensureBuffer(r+n+1);for(var a=0;a<n;a++)e[r++]=i}this.bufferLength=r}},e}(sn),bn=function(t,e,r){if(e===Qe.of("FlateDecode"))return new gn(t);if(e===Qe.of("LZWDecode")){var n=1;if(r instanceof er){var o=r.lookup(Qe.of("EarlyChange"));o instanceof qe&&(n=o.asNumber())}return new vn(t,void 0,n)}if(e===Qe.of("ASCII85Decode"))return new cn(t);if(e===Qe.of("ASCIIHexDecode"))return new fn(t);if(e===Qe.of("RunLengthDecode"))return new mn(t);throw new pe(e.asString())},xn=function(t){var e=t.dict,r=t.contents,n=new on(r),o=e.lookup(Qe.of("Filter")),i=e.lookup(Qe.of("DecodeParms"));if(o instanceof Qe)n=bn(n,o,i);else if(o instanceof Ee)for(var a=0,s=o.size();a<s;a++)n=bn(n,o.lookup(a,Qe),i&&i.lookupMaybe(a,er));else if(o)throw new de([Qe,Ee],o);return n},Fn=function(){function t(t,e,r){this.page=t;var n=null!=e?e:function(t){var e=t.MediaBox();return{left:0,bottom:0,right:e.lookup(2,qe).asNumber()-e.lookup(0,qe).asNumber(),top:e.lookup(3,qe).asNumber()-e.lookup(1,qe).asNumber()}}(t);this.width=n.right-n.left,this.height=n.top-n.bottom,this.boundingBox=n,this.transformationMatrix=null!=r?r:function(t){return[1,0,0,1,-t.left,-t.bottom]}(n)}return t.for=function(e,r,n){return o(this,0,void 0,(function(){return i(this,(function(o){return[2,new t(e,r,n)]}))}))},t.prototype.embedIntoContext=function(t,e){return o(this,0,void 0,(function(){var r,n,o,a,s,u,c,f,h,l;return i(this,(function(i){if(r=this.page.normalizedEntries(),n=r.Contents,o=r.Resources,!n)throw new ve;return a=this.decodeContents(n),s=this.boundingBox,u=s.left,c=s.bottom,f=s.right,h=s.top,l=t.flateStream(a,{Type:"XObject",Subtype:"Form",FormType:1,BBox:[u,c,f,h],Matrix:this.transformationMatrix,Resources:o}),e?(t.assign(e,l),[2,e]):[2,t.register(l)]}))}))},t.prototype.decodeContents=function(t){for(var e=Uint8Array.of(se.Newline),r=[],n=0,o=t.size();n<o;n++){var i=t.lookup(n,rr),a=void 0;if(i instanceof nr)a=xn(i).decode();else{if(!(i instanceof hr))throw new me(i);a=i.getUnencodedContents()}r.push(a,e)}return z.apply(void 0,r)},t}(),wn=function(t,e){if(void 0!==t)return e[t]};(Yr=Zr||(Zr={})).UseNone="UseNone",Yr.UseOutlines="UseOutlines",Yr.UseThumbs="UseThumbs",Yr.UseOC="UseOC",(Qr=Jr||(Jr={})).L2R="L2R",Qr.R2L="R2L",($r=_r||(_r={})).None="None",$r.AppDefault="AppDefault",(en=tn||(tn={})).Simplex="Simplex",en.DuplexFlipShortEdge="DuplexFlipShortEdge",en.DuplexFlipLongEdge="DuplexFlipLongEdge";var Sn,Cn,Tn,kn,On,Pn,An,Rn,Nn=function(){function t(t){this.dict=t}return t.prototype.lookupBool=function(t){var e=this.dict.lookup(Qe.of(t));if(e instanceof Ke)return e},t.prototype.lookupName=function(t){var e=this.dict.lookup(Qe.of(t));if(e instanceof Qe)return e},t.prototype.HideToolbar=function(){return this.lookupBool("HideToolbar")},t.prototype.HideMenubar=function(){return this.lookupBool("HideMenubar")},t.prototype.HideWindowUI=function(){return this.lookupBool("HideWindowUI")},t.prototype.FitWindow=function(){return this.lookupBool("FitWindow")},t.prototype.CenterWindow=function(){return this.lookupBool("CenterWindow")},t.prototype.DisplayDocTitle=function(){return this.lookupBool("DisplayDocTitle")},t.prototype.NonFullScreenPageMode=function(){return this.lookupName("NonFullScreenPageMode")},t.prototype.Direction=function(){return this.lookupName("Direction")},t.prototype.PrintScaling=function(){return this.lookupName("PrintScaling")},t.prototype.Duplex=function(){return this.lookupName("Duplex")},t.prototype.PickTrayByPDFSize=function(){return this.lookupBool("PickTrayByPDFSize")},t.prototype.PrintPageRange=function(){var t=this.dict.lookup(Qe.of("PrintPageRange"));if(t instanceof Ee)return t},t.prototype.NumCopies=function(){var t=this.dict.lookup(Qe.of("NumCopies"));if(t instanceof qe)return t},t.prototype.getHideToolbar=function(){var t,e;return null!==(e=null===(t=this.HideToolbar())||void 0===t?void 0:t.asBoolean())&&void 0!==e&&e},t.prototype.getHideMenubar=function(){var t,e;return null!==(e=null===(t=this.HideMenubar())||void 0===t?void 0:t.asBoolean())&&void 0!==e&&e},t.prototype.getHideWindowUI=function(){var t,e;return null!==(e=null===(t=this.HideWindowUI())||void 0===t?void 0:t.asBoolean())&&void 0!==e&&e},t.prototype.getFitWindow=function(){var t,e;return null!==(e=null===(t=this.FitWindow())||void 0===t?void 0:t.asBoolean())&&void 0!==e&&e},t.prototype.getCenterWindow=function(){var t,e;return null!==(e=null===(t=this.CenterWindow())||void 0===t?void 0:t.asBoolean())&&void 0!==e&&e},t.prototype.getDisplayDocTitle=function(){var t,e;return null!==(e=null===(t=this.DisplayDocTitle())||void 0===t?void 0:t.asBoolean())&&void 0!==e&&e},t.prototype.getNonFullScreenPageMode=function(){var t,e,r=null===(t=this.NonFullScreenPageMode())||void 0===t?void 0:t.decodeText();return null!==(e=wn(r,Zr))&&void 0!==e?e:Zr.UseNone},t.prototype.getReadingDirection=function(){var t,e,r=null===(t=this.Direction())||void 0===t?void 0:t.decodeText();return null!==(e=wn(r,Jr))&&void 0!==e?e:Jr.L2R},t.prototype.getPrintScaling=function(){var t,e,r=null===(t=this.PrintScaling())||void 0===t?void 0:t.decodeText();return null!==(e=wn(r,_r))&&void 0!==e?e:_r.AppDefault},t.prototype.getDuplex=function(){var t,e=null===(t=this.Duplex())||void 0===t?void 0:t.decodeText();return wn(e,tn)},t.prototype.getPickTrayByPDFSize=function(){var t;return null===(t=this.PickTrayByPDFSize())||void 0===t?void 0:t.asBoolean()},t.prototype.getPrintPageRange=function(){var t=this.PrintPageRange();if(!t)return[];for(var e=[],r=0;r<t.size();r+=2){var n=t.lookup(r,qe).asNumber(),o=t.lookup(r+1,qe).asNumber();e.push({start:n,end:o})}return e},t.prototype.getNumCopies=function(){var t,e;return null!==(e=null===(t=this.NumCopies())||void 0===t?void 0:t.asNumber())&&void 0!==e?e:1},t.prototype.setHideToolbar=function(t){var e=this.dict.context.obj(t);this.dict.set(Qe.of("HideToolbar"),e)},t.prototype.setHideMenubar=function(t){var e=this.dict.context.obj(t);this.dict.set(Qe.of("HideMenubar"),e)},t.prototype.setHideWindowUI=function(t){var e=this.dict.context.obj(t);this.dict.set(Qe.of("HideWindowUI"),e)},t.prototype.setFitWindow=function(t){var e=this.dict.context.obj(t);this.dict.set(Qe.of("FitWindow"),e)},t.prototype.setCenterWindow=function(t){var e=this.dict.context.obj(t);this.dict.set(Qe.of("CenterWindow"),e)},t.prototype.setDisplayDocTitle=function(t){var e=this.dict.context.obj(t);this.dict.set(Qe.of("DisplayDocTitle"),e)},t.prototype.setNonFullScreenPageMode=function(t){Lt(t,"nonFullScreenPageMode",Zr);var e=Qe.of(t);this.dict.set(Qe.of("NonFullScreenPageMode"),e)},t.prototype.setReadingDirection=function(t){Lt(t,"readingDirection",Jr);var e=Qe.of(t);this.dict.set(Qe.of("Direction"),e)},t.prototype.setPrintScaling=function(t){Lt(t,"printScaling",_r);var e=Qe.of(t);this.dict.set(Qe.of("PrintScaling"),e)},t.prototype.setDuplex=function(t){Lt(t,"duplex",tn);var e=Qe.of(t);this.dict.set(Qe.of("Duplex"),e)},t.prototype.setPickTrayByPDFSize=function(t){var e=this.dict.context.obj(t);this.dict.set(Qe.of("PickTrayByPDFSize"),e)},t.prototype.setPrintPageRange=function(t){Array.isArray(t)||(t=[t]);for(var e=[],r=0,n=t.length;r<n;r++)e.push(t[r].start),e.push(t[r].end);$t(e,"printPageRange",["number"]);var o=this.dict.context.obj(e);this.dict.set(Qe.of("PrintPageRange"),o)},t.prototype.setNumCopies=function(t){te(t,"numCopies",1,Number.MAX_VALUE),ne(t,"numCopies");var e=this.dict.context.obj(t);this.dict.set(Qe.of("NumCopies"),e)},t.fromDict=function(e){return new t(e)},t.create=function(e){return new t(e.obj({}))},t}(),Dn=/\/([^\0\t\n\f\r\ ]+)[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]+Tf/,jn=function(){function t(t,e){this.dict=t,this.ref=e}return t.prototype.T=function(){return this.dict.lookupMaybe(Qe.of("T"),jr,Or)},t.prototype.Ff=function(){var t=this.getInheritableAttribute(Qe.of("Ff"));return this.dict.context.lookupMaybe(t,qe)},t.prototype.V=function(){var t=this.getInheritableAttribute(Qe.of("V"));return this.dict.context.lookup(t)},t.prototype.Kids=function(){return this.dict.lookupMaybe(Qe.of("Kids"),Ee)},t.prototype.DA=function(){var t=this.dict.lookup(Qe.of("DA"));if(t instanceof jr||t instanceof Or)return t},t.prototype.setKids=function(t){this.dict.set(Qe.of("Kids"),this.dict.context.obj(t))},t.prototype.getParent=function(){var e=this.dict.get(Qe.of("Parent"));if(e instanceof ar)return new t(this.dict.lookup(Qe.of("Parent"),er),e)},t.prototype.setParent=function(t){t?this.dict.set(Qe.of("Parent"),t):this.dict.delete(Qe.of("Parent"))},t.prototype.getFullyQualifiedName=function(){var t=this.getParent();return t?t.getFullyQualifiedName()+"."+this.getPartialName():this.getPartialName()},t.prototype.getPartialName=function(){var t;return null===(t=this.T())||void 0===t?void 0:t.decodeText()},t.prototype.setPartialName=function(t){t?this.dict.set(Qe.of("T"),Or.fromText(t)):this.dict.delete(Qe.of("T"))},t.prototype.setDefaultAppearance=function(t){this.dict.set(Qe.of("DA"),jr.of(t))},t.prototype.getDefaultAppearance=function(){var t=this.DA();return t instanceof Or?t.decodeText():null==t?void 0:t.asString()},t.prototype.setFontSize=function(t){var e,r=null!==(e=this.getFullyQualifiedName())&&void 0!==e?e:"",n=this.getDefaultAppearance();if(!n)throw new Oe(r);var o=U(n,Dn);if(!o.match)throw new Pe(r);var i=n.slice(0,o.pos-o.match[0].length),a=o.pos<=n.length?n.slice(o.pos):"",s=i+" /"+o.match[1]+" "+t+" Tf "+a;this.setDefaultAppearance(s)},t.prototype.getFlags=function(){var t,e;return null!==(e=null===(t=this.Ff())||void 0===t?void 0:t.asNumber())&&void 0!==e?e:0},t.prototype.setFlags=function(t){this.dict.set(Qe.of("Ff"),qe.of(t))},t.prototype.hasFlag=function(t){return 0!==(this.getFlags()&t)},t.prototype.setFlag=function(t){var e=this.getFlags();this.setFlags(e|t)},t.prototype.clearFlag=function(t){var e=this.getFlags();this.setFlags(e&~t)},t.prototype.setFlagTo=function(t,e){e?this.setFlag(t):this.clearFlag(t)},t.prototype.getInheritableAttribute=function(t){var e;return this.ascend((function(r){e||(e=r.dict.get(t))})),e},t.prototype.ascend=function(t){t(this);var e=this.getParent();e&&e.ascend(t)},t}(),Mn=function(){function t(t){this.dict=t}return t.prototype.W=function(){var t=this.dict.lookup(Qe.of("W"));if(t instanceof qe)return t},t.prototype.getWidth=function(){var t,e;return null!==(e=null===(t=this.W())||void 0===t?void 0:t.asNumber())&&void 0!==e?e:1},t.prototype.setWidth=function(t){var e=this.dict.context.obj(t);this.dict.set(Qe.of("W"),e)},t.fromDict=function(e){return new t(e)},t}(),Vn=function(){function t(t){this.dict=t}return t.prototype.Rect=function(){return this.dict.lookup(Qe.of("Rect"),Ee)},t.prototype.AP=function(){return this.dict.lookupMaybe(Qe.of("AP"),er)},t.prototype.F=function(){var t=this.dict.lookup(Qe.of("F"));return this.dict.context.lookupMaybe(t,qe)},t.prototype.getRectangle=function(){var t,e=this.Rect();return null!==(t=null==e?void 0:e.asRectangle())&&void 0!==t?t:{x:0,y:0,width:0,height:0}},t.prototype.setRectangle=function(t){var e=t.x,r=t.y,n=t.width,o=t.height,i=this.dict.context.obj([e,r,e+n,r+o]);this.dict.set(Qe.of("Rect"),i)},t.prototype.getAppearanceState=function(){var t=this.dict.lookup(Qe.of("AS"));if(t instanceof Qe)return t},t.prototype.setAppearanceState=function(t){this.dict.set(Qe.of("AS"),t)},t.prototype.setAppearances=function(t){this.dict.set(Qe.of("AP"),t)},t.prototype.ensureAP=function(){var t=this.AP();return t||(t=this.dict.context.obj({}),this.dict.set(Qe.of("AP"),t)),t},t.prototype.getNormalAppearance=function(){var t=this.ensureAP().get(Qe.of("N"));if(t instanceof ar||t instanceof er)return t;throw new Error("Unexpected N type: "+(null==t?void 0:t.constructor.name))},t.prototype.setNormalAppearance=function(t){this.ensureAP().set(Qe.of("N"),t)},t.prototype.setRolloverAppearance=function(t){this.ensureAP().set(Qe.of("R"),t)},t.prototype.setDownAppearance=function(t){this.ensureAP().set(Qe.of("D"),t)},t.prototype.removeRolloverAppearance=function(){var t=this.AP();null==t||t.delete(Qe.of("R"))},t.prototype.removeDownAppearance=function(){var t=this.AP();null==t||t.delete(Qe.of("D"))},t.prototype.getAppearances=function(){var t=this.AP();if(t)return{normal:t.lookup(Qe.of("N"),er,rr),rollover:t.lookupMaybe(Qe.of("R"),er,rr),down:t.lookupMaybe(Qe.of("D"),er,rr)}},t.prototype.getFlags=function(){var t,e;return null!==(e=null===(t=this.F())||void 0===t?void 0:t.asNumber())&&void 0!==e?e:0},t.prototype.setFlags=function(t){this.dict.set(Qe.of("F"),qe.of(t))},t.prototype.hasFlag=function(t){return 0!==(this.getFlags()&t)},t.prototype.setFlag=function(t){var e=this.getFlags();this.setFlags(e|t)},t.prototype.clearFlag=function(t){var e=this.getFlags();this.setFlags(e&~t)},t.prototype.setFlagTo=function(t,e){e?this.setFlag(t):this.clearFlag(t)},t.fromDict=function(e){return new t(e)},t}(),Bn=function(){function t(t){this.dict=t}return t.prototype.R=function(){var t=this.dict.lookup(Qe.of("R"));if(t instanceof qe)return t},t.prototype.BC=function(){var t=this.dict.lookup(Qe.of("BC"));if(t instanceof Ee)return t},t.prototype.BG=function(){var t=this.dict.lookup(Qe.of("BG"));if(t instanceof Ee)return t},t.prototype.CA=function(){var t=this.dict.lookup(Qe.of("CA"));if(t instanceof Or||t instanceof jr)return t},t.prototype.RC=function(){var t=this.dict.lookup(Qe.of("RC"));if(t instanceof Or||t instanceof jr)return t},t.prototype.AC=function(){var t=this.dict.lookup(Qe.of("AC"));if(t instanceof Or||t instanceof jr)return t},t.prototype.getRotation=function(){var t;return null===(t=this.R())||void 0===t?void 0:t.asNumber()},t.prototype.getBorderColor=function(){var t=this.BC();if(t){for(var e=[],r=0,n=null==t?void 0:t.size();r<n;r++){var o=t.get(r);o instanceof qe&&e.push(o.asNumber())}return e}},t.prototype.getBackgroundColor=function(){var t=this.BG();if(t){for(var e=[],r=0,n=null==t?void 0:t.size();r<n;r++){var o=t.get(r);o instanceof qe&&e.push(o.asNumber())}return e}},t.prototype.getCaptions=function(){var t=this.CA(),e=this.RC(),r=this.AC();return{normal:null==t?void 0:t.decodeText(),rollover:null==e?void 0:e.decodeText(),down:null==r?void 0:r.decodeText()}},t.prototype.setRotation=function(t){var e=this.dict.context.obj(t);this.dict.set(Qe.of("R"),e)},t.prototype.setBorderColor=function(t){var e=this.dict.context.obj(t);this.dict.set(Qe.of("BC"),e)},t.prototype.setBackgroundColor=function(t){var e=this.dict.context.obj(t);this.dict.set(Qe.of("BG"),e)},t.prototype.setCaptions=function(t){var e=Or.fromText(t.normal);if(this.dict.set(Qe.of("CA"),e),t.rollover){var r=Or.fromText(t.rollover);this.dict.set(Qe.of("RC"),r)}else this.dict.delete(Qe.of("RC"));if(t.down){var n=Or.fromText(t.down);this.dict.set(Qe.of("AC"),n)}else this.dict.delete(Qe.of("AC"))},t.fromDict=function(e){return new t(e)},t}(),Un=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype.MK=function(){var t=this.dict.lookup(Qe.of("MK"));if(t instanceof er)return t},e.prototype.BS=function(){var t=this.dict.lookup(Qe.of("BS"));if(t instanceof er)return t},e.prototype.DA=function(){var t=this.dict.lookup(Qe.of("DA"));if(t instanceof jr||t instanceof Or)return t},e.prototype.P=function(){var t=this.dict.get(Qe.of("P"));if(t instanceof ar)return t},e.prototype.setP=function(t){this.dict.set(Qe.of("P"),t)},e.prototype.setDefaultAppearance=function(t){this.dict.set(Qe.of("DA"),jr.of(t))},e.prototype.getDefaultAppearance=function(){var t=this.DA();return t instanceof Or?t.decodeText():null==t?void 0:t.asString()},e.prototype.getAppearanceCharacteristics=function(){var t=this.MK();if(t)return Bn.fromDict(t)},e.prototype.getOrCreateAppearanceCharacteristics=function(){var t=this.MK();if(t)return Bn.fromDict(t);var e=Bn.fromDict(this.dict.context.obj({}));return this.dict.set(Qe.of("MK"),e.dict),e},e.prototype.getBorderStyle=function(){var t=this.BS();if(t)return Mn.fromDict(t)},e.prototype.getOrCreateBorderStyle=function(){var t=this.BS();if(t)return Mn.fromDict(t);var e=Mn.fromDict(this.dict.context.obj({}));return this.dict.set(Qe.of("BS"),e.dict),e},e.prototype.getOnValue=function(){var t,e=null===(t=this.getAppearances())||void 0===t?void 0:t.normal;if(e instanceof er)for(var r=e.keys(),n=0,o=r.length;n<o;n++){var i=r[n];if(i!==Qe.of("Off"))return i}},e.fromDict=function(t){return new e(t)},e.create=function(t,r){return new e(t.obj({Type:"Annot",Subtype:"Widget",Rect:[0,0,0,0],Parent:r}))},e}(Vn),In=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype.FT=function(){var t=this.getInheritableAttribute(Qe.of("FT"));return this.dict.context.lookup(t,Qe)},e.prototype.getWidgets=function(){var t=this.Kids();if(!t)return[Un.fromDict(this.dict)];for(var e=new Array(t.size()),r=0,n=t.size();r<n;r++){var o=t.lookup(r,er);e[r]=Un.fromDict(o)}return e},e.prototype.addWidget=function(t){this.normalizedEntries().Kids.push(t)},e.prototype.removeWidget=function(t){var e=this.Kids();if(e){if(t<0||t>e.size())throw new Ce(t,0,e.size());e.remove(t)}else{if(0!==t)throw new Ce(t,0,0);this.setKids([])}},e.prototype.normalizedEntries=function(){var t=this.Kids();return t||(t=this.dict.context.obj([this.ref]),this.dict.set(Qe.of("Kids"),t)),{Kids:t}},e.fromDict=function(t,r){return new e(t,r)},e}(jn),Wn=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype.Opt=function(){return this.dict.lookupMaybe(Qe.of("Opt"),jr,Or,Ee)},e.prototype.setOpt=function(t){this.dict.set(Qe.of("Opt"),this.dict.context.obj(t))},e.prototype.getExportValues=function(){var t=this.Opt();if(t){if(t instanceof jr||t instanceof Or)return[t];for(var e=[],r=0,n=t.size();r<n;r++){var o=t.lookup(r);(o instanceof jr||o instanceof Or)&&e.push(o)}return e}},e.prototype.removeExportValue=function(t){var e=this.Opt();if(e)if(e instanceof jr||e instanceof Or){if(0!==t)throw new Ce(t,0,0);this.setOpt([])}else{if(t<0||t>e.size())throw new Ce(t,0,e.size());e.remove(t)}},e.prototype.normalizeExportValues=function(){for(var t,e,r,n,o=null!==(t=this.getExportValues())&&void 0!==t?t:[],i=[],a=this.getWidgets(),s=0,u=a.length;s<u;s++){var c=a[s],f=null!==(e=o[s])&&void 0!==e?e:Or.fromText(null!==(n=null===(r=c.getOnValue())||void 0===r?void 0:r.decodeText())&&void 0!==n?n:"");i.push(f)}this.setOpt(i)},e.prototype.addOpt=function(t,e){var r;this.normalizeExportValues();var n,o=t.decodeText();if(e)for(var i=null!==(r=this.getExportValues())&&void 0!==r?r:[],a=0,s=i.length;a<s;a++){i[a].decodeText()===o&&(n=a)}var u=this.Opt();return u.push(t),null!=n?n:u.size()-1},e.prototype.addWidgetWithOpt=function(t,e,r){var n=this.addOpt(e,r),o=Qe.of(String(n));return this.addWidget(t),o},e}(In),zn=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype.setValue=function(t){var e;if(t!==(null!==(e=this.getOnValue())&&void 0!==e?e:Qe.of("Yes"))&&t!==Qe.of("Off"))throw new Te;this.dict.set(Qe.of("V"),t);for(var r=this.getWidgets(),n=0,o=r.length;n<o;n++){var i=r[n],a=i.getOnValue()===t?t:Qe.of("Off");i.setAppearanceState(a)}},e.prototype.getValue=function(){var t=this.V();return t instanceof Qe?t:Qe.of("Off")},e.prototype.getOnValue=function(){var t=this.getWidgets()[0];return null==t?void 0:t.getOnValue()},e.fromDict=function(t,r){return new e(t,r)},e.create=function(t){var r=t.obj({FT:"Btn",Kids:[]});return new e(r,t.register(r))},e}(Wn),qn=function(t){return 1<<t};(Cn=Sn||(Sn={}))[Cn.ReadOnly=qn(0)]="ReadOnly",Cn[Cn.Required=qn(1)]="Required",Cn[Cn.NoExport=qn(2)]="NoExport",(kn=Tn||(Tn={}))[kn.NoToggleToOff=qn(14)]="NoToggleToOff",kn[kn.Radio=qn(15)]="Radio",kn[kn.PushButton=qn(16)]="PushButton",kn[kn.RadiosInUnison=qn(25)]="RadiosInUnison",(Pn=On||(On={}))[Pn.Multiline=qn(12)]="Multiline",Pn[Pn.Password=qn(13)]="Password",Pn[Pn.FileSelect=qn(20)]="FileSelect",Pn[Pn.DoNotSpellCheck=qn(22)]="DoNotSpellCheck",Pn[Pn.DoNotScroll=qn(23)]="DoNotScroll",Pn[Pn.Comb=qn(24)]="Comb",Pn[Pn.RichText=qn(25)]="RichText",(Rn=An||(An={}))[Rn.Combo=qn(17)]="Combo",Rn[Rn.Edit=qn(18)]="Edit",Rn[Rn.Sort=qn(19)]="Sort",Rn[Rn.MultiSelect=qn(21)]="MultiSelect",Rn[Rn.DoNotSpellCheck=qn(22)]="DoNotSpellCheck",Rn[Rn.CommitOnSelChange=qn(26)]="CommitOnSelChange";var En=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype.setValues=function(t){if(this.hasFlag(An.Combo)&&!this.hasFlag(An.Edit)&&!this.valuesAreValid(t))throw new Te;if(0===t.length&&this.dict.delete(Qe.of("V")),1===t.length&&this.dict.set(Qe.of("V"),t[0]),t.length>1){if(!this.hasFlag(An.MultiSelect))throw new ke;this.dict.set(Qe.of("V"),this.dict.context.obj(t))}this.updateSelectedIndices(t)},e.prototype.valuesAreValid=function(t){for(var e=this.getOptions(),r=function(r,n){var o=t[r].decodeText();if(!e.find((function(t){return o===(t.display||t.value).decodeText()})))return{value:!1}},n=0,o=t.length;n<o;n++){var i=r(n);if("object"==typeof i)return i.value}return!0},e.prototype.updateSelectedIndices=function(t){if(t.length>1){for(var e=new Array(t.length),r=this.getOptions(),n=function(n,o){var i=t[n].decodeText();e[n]=r.findIndex((function(t){return i===(t.display||t.value).decodeText()}))},o=0,i=t.length;o<i;o++)n(o);this.dict.set(Qe.of("I"),this.dict.context.obj(e.sort()))}else this.dict.delete(Qe.of("I"))},e.prototype.getValues=function(){var t=this.V();if(t instanceof jr||t instanceof Or)return[t];if(t instanceof Ee){for(var e=[],r=0,n=t.size();r<n;r++){var o=t.lookup(r);(o instanceof jr||o instanceof Or)&&e.push(o)}return e}return[]},e.prototype.Opt=function(){return this.dict.lookupMaybe(Qe.of("Opt"),jr,Or,Ee)},e.prototype.setOptions=function(t){for(var e=new Array(t.length),r=0,n=t.length;r<n;r++){var o=t[r],i=o.value,a=o.display;e[r]=this.dict.context.obj([i,a||i])}this.dict.set(Qe.of("Opt"),this.dict.context.obj(e))},e.prototype.getOptions=function(){var t=this.Opt();if(t instanceof jr||t instanceof Or)return[{value:t,display:t}];if(t instanceof Ee){for(var e=[],r=0,n=t.size();r<n;r++){var o=t.lookup(r);if((o instanceof jr||o instanceof Or)&&e.push({value:o,display:o}),o instanceof Ee&&o.size()>0){var i=o.lookup(0,jr,Or),a=o.lookupMaybe(1,jr,Or);e.push({value:i,display:a||i})}}return e}return[]},e}(In),Gn=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.fromDict=function(t,r){return new e(t,r)},e.create=function(t){var r=t.obj({FT:"Ch",Ff:An.Combo,Kids:[]});return new e(r,t.register(r))},e}(En),Kn=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype.addField=function(t){var e=this.normalizedEntries().Kids;null==e||e.push(t)},e.prototype.normalizedEntries=function(){var t=this.Kids();return t||(t=this.dict.context.obj([]),this.dict.set(Qe.of("Kids"),t)),{Kids:t}},e.fromDict=function(t,r){return new e(t,r)},e.create=function(t){var r=t.obj({});return new e(r,t.register(r))},e}(jn),Ln=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.fromDict=function(t,r){return new e(t,r)},e}(In),Xn=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype.MaxLen=function(){var t=this.dict.lookup(Qe.of("MaxLen"));if(t instanceof qe)return t},e.prototype.Q=function(){var t=this.dict.lookup(Qe.of("Q"));if(t instanceof qe)return t},e.prototype.setMaxLength=function(t){this.dict.set(Qe.of("MaxLen"),qe.of(t))},e.prototype.removeMaxLength=function(){this.dict.delete(Qe.of("MaxLen"))},e.prototype.getMaxLength=function(){var t;return null===(t=this.MaxLen())||void 0===t?void 0:t.asNumber()},e.prototype.setQuadding=function(t){this.dict.set(Qe.of("Q"),qe.of(t))},e.prototype.getQuadding=function(){var t;return null===(t=this.Q())||void 0===t?void 0:t.asNumber()},e.prototype.setValue=function(t){this.dict.set(Qe.of("V"),t)},e.prototype.removeValue=function(){this.dict.delete(Qe.of("V"))},e.prototype.getValue=function(){var t=this.V();if(t instanceof jr||t instanceof Or)return t},e.fromDict=function(t,r){return new e(t,r)},e.create=function(t){var r=t.obj({FT:"Tx",Kids:[]});return new e(r,t.register(r))},e}(In),Hn=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.fromDict=function(t,r){return new e(t,r)},e.create=function(t){var r=t.obj({FT:"Btn",Ff:Tn.PushButton,Kids:[]});return new e(r,t.register(r))},e}(Wn),Zn=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype.setValue=function(t){if(!this.getOnValues().includes(t)&&t!==Qe.of("Off"))throw new Te;this.dict.set(Qe.of("V"),t);for(var e=this.getWidgets(),r=0,n=e.length;r<n;r++){var o=e[r],i=o.getOnValue()===t?t:Qe.of("Off");o.setAppearanceState(i)}},e.prototype.getValue=function(){var t=this.V();return t instanceof Qe?t:Qe.of("Off")},e.prototype.getOnValues=function(){for(var t=this.getWidgets(),e=[],r=0,n=t.length;r<n;r++){var o=t[r].getOnValue();o&&e.push(o)}return e},e.fromDict=function(t,r){return new e(t,r)},e.create=function(t){var r=t.obj({FT:"Btn",Ff:Tn.Radio,Kids:[]});return new e(r,t.register(r))},e}(Wn),Yn=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.fromDict=function(t,r){return new e(t,r)},e.create=function(t){var r=t.obj({FT:"Ch",Kids:[]});return new e(r,t.register(r))},e}(En),Jn=function(t){if(!t)return[];for(var e=[],r=0,n=t.size();r<n;r++){var o=t.get(r),i=t.lookup(r);o instanceof ar&&i instanceof er&&e.push([Qn(i,o),o])}return e},Qn=function(t,e){return _n(t)?Kn.fromDict(t,e):$n(t,e)},_n=function(t){var e=t.lookup(Qe.of("Kids"));if(e instanceof Ee)for(var r=0,n=e.size();r<n;r++){var o=e.lookup(r);if(o instanceof er&&o.has(Qe.of("T")))return!0}return!1},$n=function(t,e){var r=no(t,Qe.of("FT")),n=t.context.lookup(r,Qe);return n===Qe.of("Btn")?to(t,e):n===Qe.of("Ch")?eo(t,e):n===Qe.of("Tx")?Xn.fromDict(t,e):n===Qe.of("Sig")?Ln.fromDict(t,e):In.fromDict(t,e)},to=function(t,e){var r,n=no(t,Qe.of("Ff")),o=t.context.lookupMaybe(n,qe),i=null!==(r=null==o?void 0:o.asNumber())&&void 0!==r?r:0;return ro(i,Tn.PushButton)?Hn.fromDict(t,e):ro(i,Tn.Radio)?Zn.fromDict(t,e):zn.fromDict(t,e)},eo=function(t,e){var r,n=no(t,Qe.of("Ff")),o=t.context.lookupMaybe(n,qe),i=null!==(r=null==o?void 0:o.asNumber())&&void 0!==r?r:0;return ro(i,An.Combo)?Gn.fromDict(t,e):Yn.fromDict(t,e)},ro=function(t,e){return 0!==(t&e)},no=function(t,e){var r;return oo(t,(function(t){r||(r=t.get(e))})),r},oo=function(t,e){e(t);var r=t.lookupMaybe(Qe.of("Parent"),er);r&&oo(r,e)},io=function(){function t(t){this.dict=t}return t.prototype.Fields=function(){var t=this.dict.lookup(Qe.of("Fields"));if(t instanceof Ee)return t},t.prototype.getFields=function(){for(var t=this.normalizedEntries().Fields,e=new Array(t.size()),r=0,n=t.size();r<n;r++){var o=t.get(r),i=t.lookup(r,er);e[r]=[Qn(i,o),o]}return e},t.prototype.getAllFields=function(){var t=[],e=function(r){if(r)for(var n=0,o=r.length;n<o;n++){var i=r[n];t.push(i);var a=i[0];a instanceof Kn&&e(Jn(a.Kids()))}};return e(this.getFields()),t},t.prototype.addField=function(t){var e=this.normalizedEntries().Fields;null==e||e.push(t)},t.prototype.removeField=function(t){var e=t.getParent(),r=void 0===e?this.normalizedEntries().Fields:e.Kids(),n=null==r?void 0:r.indexOf(t.ref);if(void 0===r||void 0===n)throw new Error("Tried to remove inexistent field "+t.getFullyQualifiedName());r.remove(n),void 0!==e&&0===r.size()&&this.removeField(e)},t.prototype.normalizedEntries=function(){var t=this.Fields();return t||(t=this.dict.context.obj([]),this.dict.set(Qe.of("Fields"),t)),{Fields:t}},t.fromDict=function(e){return new t(e)},t.create=function(e){return new t(e.obj({Fields:[]}))},t}(),ao=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype.Pages=function(){return this.lookup(Qe.of("Pages"),er)},e.prototype.AcroForm=function(){return this.lookupMaybe(Qe.of("AcroForm"),er)},e.prototype.getAcroForm=function(){var t=this.AcroForm();if(t)return io.fromDict(t)},e.prototype.getOrCreateAcroForm=function(){var t=this.getAcroForm();if(!t){t=io.create(this.context);var e=this.context.register(t.dict);this.set(Qe.of("AcroForm"),e)}return t},e.prototype.ViewerPreferences=function(){return this.lookupMaybe(Qe.of("ViewerPreferences"),er)},e.prototype.getViewerPreferences=function(){var t=this.ViewerPreferences();if(t)return Nn.fromDict(t)},e.prototype.getOrCreateViewerPreferences=function(){var t=this.getViewerPreferences();if(!t){t=Nn.create(this.context);var e=this.context.register(t.dict);this.set(Qe.of("ViewerPreferences"),e)}return t},e.prototype.insertLeafNode=function(t,e){var r=this.get(Qe.of("Pages"));return this.Pages().insertLeafNode(t,e)||r},e.prototype.removeLeafNode=function(t){this.Pages().removeLeafNode(t)},e.withContextAndPages=function(t,r){var n=new Map;return n.set(Qe.of("Type"),Qe.of("Catalog")),n.set(Qe.of("Pages"),r),new e(n,t)},e.fromMapWithContext=function(t,r){return new e(t,r)},e}(er),so=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype.Parent=function(){return this.lookup(Qe.of("Parent"))},e.prototype.Kids=function(){return this.lookup(Qe.of("Kids"),Ee)},e.prototype.Count=function(){return this.lookup(Qe.of("Count"),qe)},e.prototype.pushTreeNode=function(t){this.Kids().push(t)},e.prototype.pushLeafNode=function(t){var e=this.Kids();this.insertLeafKid(e.size(),t)},e.prototype.insertLeafNode=function(t,r){var n=this.Kids(),o=this.Count().asNumber();if(r>o)throw new we(r,o);for(var i=r,a=0,s=n.size();a<s;a++){if(0===i)return void this.insertLeafKid(a,t);var u=n.get(a),c=this.context.lookup(u);if(c instanceof e){if(c.Count().asNumber()>i)return c.insertLeafNode(t,i)||u;i-=c.Count().asNumber()}c instanceof yr&&(i-=1)}if(0!==i)throw new Se(r,"insertLeafNode");this.insertLeafKid(n.size(),t)},e.prototype.removeLeafNode=function(t,r){void 0===r&&(r=!0);var n=this.Kids(),o=this.Count().asNumber();if(t>=o)throw new we(t,o);for(var i=t,a=0,s=n.size();a<s;a++){var u=n.get(a),c=this.context.lookup(u);if(c instanceof e){if(c.Count().asNumber()>i)return c.removeLeafNode(i,r),void(r&&0===c.Kids().size()&&n.remove(a));i-=c.Count().asNumber()}if(c instanceof yr){if(0===i)return void this.removeKid(a);i-=1}}throw new Se(t,"removeLeafNode")},e.prototype.ascend=function(t){t(this);var e=this.Parent();e&&e.ascend(t)},e.prototype.traverse=function(t){for(var r=this.Kids(),n=0,o=r.size();n<o;n++){var i=r.get(n),a=this.context.lookup(i);a instanceof e&&a.traverse(t),t(a,i)}},e.prototype.insertLeafKid=function(t,e){var r=this.Kids();this.ascend((function(t){var e=t.Count().asNumber()+1;t.set(Qe.of("Count"),qe.of(e))})),r.insert(t,e)},e.prototype.removeKid=function(t){var e=this.Kids();e.lookup(t)instanceof yr&&this.ascend((function(t){var e=t.Count().asNumber()-1;t.set(Qe.of("Count"),qe.of(e))})),e.remove(t)},e.withContext=function(t,r){var n=new Map;return n.set(Qe.of("Type"),Qe.of("Pages")),n.set(Qe.of("Kids"),t.obj([])),n.set(Qe.of("Count"),t.obj(0)),r&&n.set(Qe.of("Parent"),r),new e(n,t)},e.fromMapWithContext=function(t,r){return new e(t,r)},e}(er),uo=new Uint8Array(256);uo[se.Zero]=1,uo[se.One]=1,uo[se.Two]=1,uo[se.Three]=1,uo[se.Four]=1,uo[se.Five]=1,uo[se.Six]=1,uo[se.Seven]=1,uo[se.Eight]=1,uo[se.Nine]=1;var co=new Uint8Array(256);co[se.Period]=1,co[se.Plus]=1,co[se.Minus]=1;for(var fo=new Uint8Array(256),ho=0;ho<256;ho++)fo[ho]=uo[ho]||co[ho]?1:0;var lo,po,yo=se.Newline,go=se.CarriageReturn,vo=function(){function t(t,e){void 0===e&&(e=!1),this.bytes=t,this.capNumbers=e}return t.prototype.parseRawInt=function(){for(var t="";!this.bytes.done();){var e=this.bytes.peek();if(!uo[e])break;t+=x(this.bytes.next())}var r=Number(t);if(!t||!isFinite(r))throw new Ae(this.bytes.position(),t);return r},t.prototype.parseRawNumber=function(){for(var t="";!this.bytes.done();){var e=this.bytes.peek();if(!fo[e])break;if(t+=x(this.bytes.next()),e===se.Period)break}for(;!this.bytes.done();){e=this.bytes.peek();if(!uo[e])break;t+=x(this.bytes.next())}var r=Number(t);if(!t||!isFinite(r))throw new Ae(this.bytes.position(),t);if(r>Number.MAX_SAFE_INTEGER){if(this.capNumbers){var n="Parsed number that is too large for some PDF readers: "+t+", using Number.MAX_SAFE_INTEGER instead.";return console.warn(n),Number.MAX_SAFE_INTEGER}n="Parsed number that is too large for some PDF readers: "+t+", not capping.";console.warn(n)}return r},t.prototype.skipWhitespace=function(){for(;!this.bytes.done()&&Xe[this.bytes.peek()];)this.bytes.next()},t.prototype.skipLine=function(){for(;!this.bytes.done();){var t=this.bytes.peek();if(t===yo||t===go)return;this.bytes.next()}},t.prototype.skipComment=function(){if(this.bytes.peek()!==se.Percent)return!1;for(;!this.bytes.done();){var t=this.bytes.peek();if(t===yo||t===go)return!0;this.bytes.next()}return!0},t.prototype.skipWhitespaceAndComments=function(){for(this.skipWhitespace();this.skipComment();)this.skipWhitespace()},t.prototype.matchKeyword=function(t){for(var e=this.bytes.offset(),r=0,n=t.length;r<n;r++)if(this.bytes.done()||this.bytes.next()!==t[r])return this.bytes.moveTo(e),!1;return!0},t}(),mo=function(){function t(t){this.idx=0,this.line=0,this.column=0,this.bytes=t,this.length=this.bytes.length}return t.prototype.moveTo=function(t){this.idx=t},t.prototype.next=function(){var t=this.bytes[this.idx++];return t===se.Newline?(this.line+=1,this.column=0):this.column+=1,t},t.prototype.assertNext=function(t){if(this.peek()!==t)throw new Ne(this.position(),t,this.peek());return this.next()},t.prototype.peek=function(){return this.bytes[this.idx]},t.prototype.peekAhead=function(t){return this.bytes[this.idx+t]},t.prototype.peekAt=function(t){return this.bytes[t]},t.prototype.done=function(){return this.idx>=this.length},t.prototype.offset=function(){return this.idx},t.prototype.slice=function(t,e){return this.bytes.slice(t,e)},t.prototype.position=function(){return{line:this.line,column:this.column,offset:this.idx}},t.of=function(e){return new t(e)},t.fromPDFRawStream=function(e){return t.of(xn(e).decode())},t}(),bo=se.Space,xo=se.CarriageReturn,Fo=se.Newline,wo=[se.s,se.t,se.r,se.e,se.a,se.m],So=[se.e,se.n,se.d,se.s,se.t,se.r,se.e,se.a,se.m],Co={header:[se.Percent,se.P,se.D,se.F,se.Dash],eof:[se.Percent,se.Percent,se.E,se.O,se.F],obj:[se.o,se.b,se.j],endobj:[se.e,se.n,se.d,se.o,se.b,se.j],xref:[se.x,se.r,se.e,se.f],trailer:[se.t,se.r,se.a,se.i,se.l,se.e,se.r],startxref:[se.s,se.t,se.a,se.r,se.t,se.x,se.r,se.e,se.f],true:[se.t,se.r,se.u,se.e],false:[se.f,se.a,se.l,se.s,se.e],null:[se.n,se.u,se.l,se.l],stream:wo,streamEOF1:a(wo,[bo,xo,Fo]),streamEOF2:a(wo,[xo,Fo]),streamEOF3:a(wo,[xo]),streamEOF4:a(wo,[Fo]),endstream:So,EOF1endstream:a([xo,Fo],So),EOF2endstream:a([xo],So),EOF3endstream:a([Fo],So)},To=function(t){function e(e,r,n){void 0===n&&(n=!1);var o=t.call(this,e,n)||this;return o.context=r,o}return r(e,t),e.prototype.parseObject=function(){if(this.skipWhitespaceAndComments(),this.matchKeyword(Co.true))return Ke.True;if(this.matchKeyword(Co.false))return Ke.False;if(this.matchKeyword(Co.null))return _e;var t=this.bytes.peek();if(t===se.LessThan&&this.bytes.peekAhead(1)===se.LessThan)return this.parseDictOrStream();if(t===se.LessThan)return this.parseHexString();if(t===se.LeftParen)return this.parseString();if(t===se.ForwardSlash)return this.parseName();if(t===se.LeftSquareBracket)return this.parseArray();if(fo[t])return this.parseNumberOrRef();throw new De(this.bytes.position(),t)},e.prototype.parseNumberOrRef=function(){var t=this.parseRawNumber();this.skipWhitespaceAndComments();var e=this.bytes.offset();if(uo[this.bytes.peek()]){var r=this.parseRawNumber();if(this.skipWhitespaceAndComments(),this.bytes.peek()===se.R)return this.bytes.assertNext(se.R),ar.of(t,r)}return this.bytes.moveTo(e),qe.of(t)},e.prototype.parseHexString=function(){var t="";for(this.bytes.assertNext(se.LessThan);!this.bytes.done()&&this.bytes.peek()!==se.GreaterThan;)t+=x(this.bytes.next());return this.bytes.assertNext(se.GreaterThan),Or.of(t)},e.prototype.parseString=function(){for(var t=0,e=!1,r="";!this.bytes.done();){var n=this.bytes.next();if(r+=x(n),e||(n===se.LeftParen&&(t+=1),n===se.RightParen&&(t-=1)),n===se.BackSlash?e=!e:e&&(e=!1),0===t)return jr.of(r.substring(1,r.length-1))}throw new Ve(this.bytes.position())},e.prototype.parseName=function(){this.bytes.assertNext(se.ForwardSlash);for(var t="";!this.bytes.done();){var e=this.bytes.peek();if(Xe[e]||Le[e])break;t+=x(e),this.bytes.next()}return Qe.of(t)},e.prototype.parseArray=function(){this.bytes.assertNext(se.LeftSquareBracket),this.skipWhitespaceAndComments();for(var t=Ee.withContext(this.context);this.bytes.peek()!==se.RightSquareBracket;){var e=this.parseObject();t.push(e),this.skipWhitespaceAndComments()}return this.bytes.assertNext(se.RightSquareBracket),t},e.prototype.parseDict=function(){this.bytes.assertNext(se.LessThan),this.bytes.assertNext(se.LessThan),this.skipWhitespaceAndComments();for(var t=new Map;!this.bytes.done()&&this.bytes.peek()!==se.GreaterThan&&this.bytes.peekAhead(1)!==se.GreaterThan;){var e=this.parseName(),r=this.parseObject();t.set(e,r),this.skipWhitespaceAndComments()}this.skipWhitespaceAndComments(),this.bytes.assertNext(se.GreaterThan),this.bytes.assertNext(se.GreaterThan);var n=t.get(Qe.of("Type"));return n===Qe.of("Catalog")?ao.fromMapWithContext(t,this.context):n===Qe.of("Pages")?so.fromMapWithContext(t,this.context):n===Qe.of("Page")?yr.fromMapWithContext(t,this.context):er.fromMapWithContext(t,this.context)},e.prototype.parseDictOrStream=function(){var t=this.bytes.position(),e=this.parseDict();if(this.skipWhitespaceAndComments(),!(this.matchKeyword(Co.streamEOF1)||this.matchKeyword(Co.streamEOF2)||this.matchKeyword(Co.streamEOF3)||this.matchKeyword(Co.streamEOF4)||this.matchKeyword(Co.stream)))return e;var r,n=this.bytes.offset(),o=e.get(Qe.of("Length"));o instanceof qe?(r=n+o.asNumber(),this.bytes.moveTo(r),this.skipWhitespaceAndComments(),this.matchKeyword(Co.endstream)||(this.bytes.moveTo(n),r=this.findEndOfStreamFallback(t))):r=this.findEndOfStreamFallback(t);var i=this.bytes.slice(n,r);return nr.of(e,i)},e.prototype.findEndOfStreamFallback=function(t){for(var e=1,r=this.bytes.offset();!this.bytes.done()&&(r=this.bytes.offset(),this.matchKeyword(Co.stream)?e+=1:this.matchKeyword(Co.EOF1endstream)||this.matchKeyword(Co.EOF2endstream)||this.matchKeyword(Co.EOF3endstream)||this.matchKeyword(Co.endstream)?e-=1:this.bytes.next(),0!==e););if(0!==e)throw new Me(t);return r},e.forBytes=function(t,r,n){return new e(mo.of(t),r,n)},e.forByteStream=function(t,r,n){return void 0===n&&(n=!1),new e(t,r,n)},e}(vo),ko=function(t){function e(e,r){var n=t.call(this,mo.fromPDFRawStream(e),e.dict.context)||this,o=e.dict;return n.alreadyParsed=!1,n.shouldWaitForTick=r||function(){return!1},n.firstOffset=o.lookup(Qe.of("First"),qe).asNumber(),n.objectCount=o.lookup(Qe.of("N"),qe).asNumber(),n}return r(e,t),e.prototype.parseIntoContext=function(){return o(this,0,void 0,(function(){var t,e,r,n,o,a,s,u;return i(this,(function(i){switch(i.label){case 0:if(this.alreadyParsed)throw new ye("PDFObjectStreamParser","parseIntoContext");this.alreadyParsed=!0,t=this.parseOffsetsAndObjectNumbers(),e=0,r=t.length,i.label=1;case 1:return e<r?(n=t[e],o=n.objectNumber,a=n.offset,this.bytes.moveTo(this.firstOffset+a),s=this.parseObject(),u=ar.of(o,0),this.context.assign(u,s),this.shouldWaitForTick()?[4,Q()]:[3,3]):[3,4];case 2:i.sent(),i.label=3;case 3:return e++,[3,1];case 4:return[2]}}))}))},e.prototype.parseOffsetsAndObjectNumbers=function(){for(var t=[],e=0,r=this.objectCount;e<r;e++){this.skipWhitespaceAndComments();var n=this.parseRawInt();this.skipWhitespaceAndComments();var o=this.parseRawInt();t.push({objectNumber:n,offset:o})}return t},e.forStream=function(t,r){return new e(t,r)},e}(To),Oo=function(){function t(t){this.alreadyParsed=!1,this.dict=t.dict,this.bytes=mo.fromPDFRawStream(t),this.context=this.dict.context;var e=this.dict.lookup(Qe.of("Size"),qe),r=this.dict.lookup(Qe.of("Index"));if(r instanceof Ee){this.subsections=[];for(var n=0,o=r.size();n<o;n+=2){var i=r.lookup(n+0,qe).asNumber(),a=r.lookup(n+1,qe).asNumber();this.subsections.push({firstObjectNumber:i,length:a})}}else this.subsections=[{firstObjectNumber:0,length:e.asNumber()}];var s=this.dict.lookup(Qe.of("W"),Ee);this.byteWidths=[-1,-1,-1];for(n=0,o=s.size();n<o;n++)this.byteWidths[n]=s.lookup(n,qe).asNumber()}return t.prototype.parseIntoContext=function(){if(this.alreadyParsed)throw new ye("PDFXRefStreamParser","parseIntoContext");return this.alreadyParsed=!0,this.context.trailerInfo={Root:this.dict.get(Qe.of("Root")),Encrypt:this.dict.get(Qe.of("Encrypt")),Info:this.dict.get(Qe.of("Info")),ID:this.dict.get(Qe.of("ID"))},this.parseEntries()},t.prototype.parseEntries=function(){for(var t=[],e=this.byteWidths,r=e[0],n=e[1],o=e[2],i=0,a=this.subsections.length;i<a;i++)for(var s=this.subsections[i],u=s.firstObjectNumber,c=s.length,f=0;f<c;f++){for(var h=0,l=0,d=r;l<d;l++)h=h<<8|this.bytes.next();var p=0;for(l=0,d=n;l<d;l++)p=p<<8|this.bytes.next();var y=0;for(l=0,d=o;l<d;l++)y=y<<8|this.bytes.next();0===r&&(h=1);var g=u+f,v={ref:ar.of(g,y),offset:p,deleted:0===h,inObjectStream:2===h};t.push(v)}return t},t.forStream=function(e){return new t(e)},t}(),Po=function(t){function e(e,r,n,o){void 0===r&&(r=1/0),void 0===n&&(n=!1),void 0===o&&(o=!1);var i=t.call(this,mo.of(e),pr.create(),o)||this;return i.alreadyParsed=!1,i.parsedObjects=0,i.shouldWaitForTick=function(){return i.parsedObjects+=1,i.parsedObjects%i.objectsPerTick===0},i.objectsPerTick=r,i.throwOnInvalidObject=n,i}return r(e,t),e.prototype.parseDocument=function(){return o(this,0,void 0,(function(){var t,e;return i(this,(function(r){switch(r.label){case 0:if(this.alreadyParsed)throw new ye("PDFParser","parseDocument");this.alreadyParsed=!0,this.context.header=this.parseHeader(),r.label=1;case 1:return this.bytes.done()?[3,3]:[4,this.parseDocumentSection()];case 2:if(r.sent(),(e=this.bytes.offset())===t)throw new Be(this.bytes.position());return t=e,[3,1];case 3:return this.maybeRecoverRoot(),this.context.lookup(ar.of(0))&&(console.warn("Removing parsed object: 0 0 R"),this.context.delete(ar.of(0))),[2,this.context]}}))}))},e.prototype.maybeRecoverRoot=function(){var t=function(t){return t instanceof er&&t.lookup(Qe.of("Type"))===Qe.of("Catalog")};if(!t(this.context.lookup(this.context.trailerInfo.Root)))for(var e=this.context.enumerateIndirectObjects(),r=0,n=e.length;r<n;r++){var o=e[r],i=o[0];t(o[1])&&(this.context.trailerInfo.Root=i)}},e.prototype.parseHeader=function(){for(;!this.bytes.done();){if(this.matchKeyword(Co.header)){var t=this.parseRawInt();this.bytes.assertNext(se.Period);var e=this.parseRawInt(),r=We.forVersion(t,e);return this.skipBinaryHeaderComment(),r}this.bytes.next()}throw new Ue(this.bytes.position())},e.prototype.parseIndirectObjectHeader=function(){this.skipWhitespaceAndComments();var t=this.parseRawInt();this.skipWhitespaceAndComments();var e=this.parseRawInt();if(this.skipWhitespaceAndComments(),!this.matchKeyword(Co.obj))throw new Ie(this.bytes.position(),Co.obj);return ar.of(t,e)},e.prototype.matchIndirectObjectHeader=function(){var t=this.bytes.offset();try{return this.parseIndirectObjectHeader(),!0}catch(e){return this.bytes.moveTo(t),!1}},e.prototype.parseIndirectObject=function(){return o(this,0,void 0,(function(){var t,e;return i(this,(function(r){switch(r.label){case 0:return t=this.parseIndirectObjectHeader(),this.skipWhitespaceAndComments(),e=this.parseObject(),this.skipWhitespaceAndComments(),this.matchKeyword(Co.endobj),e instanceof nr&&e.dict.lookup(Qe.of("Type"))===Qe.of("ObjStm")?[4,ko.forStream(e,this.shouldWaitForTick).parseIntoContext()]:[3,2];case 1:return r.sent(),[3,3];case 2:e instanceof nr&&e.dict.lookup(Qe.of("Type"))===Qe.of("XRef")?Oo.forStream(e).parseIntoContext():this.context.assign(t,e),r.label=3;case 3:return[2,t]}}))}))},e.prototype.tryToParseInvalidIndirectObject=function(){var t=this.bytes.position(),e="Trying to parse invalid object: "+JSON.stringify(t)+")";if(this.throwOnInvalidObject)throw new Error(e);console.warn(e);var r=this.parseIndirectObjectHeader();console.warn("Invalid object ref: "+r),this.skipWhitespaceAndComments();for(var n=this.bytes.offset(),o=!0;!this.bytes.done()&&(this.matchKeyword(Co.endobj)&&(o=!1),o);)this.bytes.next();if(o)throw new je(t);var i=this.bytes.offset()-Co.endobj.length,a=wr.of(this.bytes.slice(n,i));return this.context.assign(r,a),r},e.prototype.parseIndirectObjects=function(){return o(this,0,void 0,(function(){var t;return i(this,(function(e){switch(e.label){case 0:this.skipWhitespaceAndComments(),e.label=1;case 1:if(this.bytes.done()||!uo[this.bytes.peek()])return[3,8];t=this.bytes.offset(),e.label=2;case 2:return e.trys.push([2,4,,5]),[4,this.parseIndirectObject()];case 3:return e.sent(),[3,5];case 4:return e.sent(),this.bytes.moveTo(t),this.tryToParseInvalidIndirectObject(),[3,5];case 5:return this.skipWhitespaceAndComments(),this.skipJibberish(),this.shouldWaitForTick()?[4,Q()]:[3,7];case 6:e.sent(),e.label=7;case 7:return[3,1];case 8:return[2]}}))}))},e.prototype.maybeParseCrossRefSection=function(){if(this.skipWhitespaceAndComments(),this.matchKeyword(Co.xref)){this.skipWhitespaceAndComments();for(var t=-1,e=vr.createEmpty();!this.bytes.done()&&uo[this.bytes.peek()];){var r=this.parseRawInt();this.skipWhitespaceAndComments();var n=this.parseRawInt();this.skipWhitespaceAndComments();var o=this.bytes.peek();if(o===se.n||o===se.f){var i=ar.of(t,n);this.bytes.next()===se.n?e.addEntry(i,r):e.addDeletedEntry(i,r),t+=1}else t=r;this.skipWhitespaceAndComments()}return e}},e.prototype.maybeParseTrailerDict=function(){if(this.skipWhitespaceAndComments(),this.matchKeyword(Co.trailer)){this.skipWhitespaceAndComments();var t=this.parseDict(),e=this.context;e.trailerInfo={Root:t.get(Qe.of("Root"))||e.trailerInfo.Root,Encrypt:t.get(Qe.of("Encrypt"))||e.trailerInfo.Encrypt,Info:t.get(Qe.of("Info"))||e.trailerInfo.Info,ID:t.get(Qe.of("ID"))||e.trailerInfo.ID}}},e.prototype.maybeParseTrailer=function(){if(this.skipWhitespaceAndComments(),this.matchKeyword(Co.startxref)){this.skipWhitespaceAndComments();var t=this.parseRawInt();return this.skipWhitespace(),this.matchKeyword(Co.eof),this.skipWhitespaceAndComments(),this.matchKeyword(Co.eof),this.skipWhitespaceAndComments(),mr.forLastCrossRefSectionOffset(t)}},e.prototype.parseDocumentSection=function(){return o(this,0,void 0,(function(){return i(this,(function(t){switch(t.label){case 0:return[4,this.parseIndirectObjects()];case 1:return t.sent(),this.maybeParseCrossRefSection(),this.maybeParseTrailerDict(),this.maybeParseTrailer(),this.skipJibberish(),[2]}}))}))},e.prototype.skipJibberish=function(){for(this.skipWhitespaceAndComments();!this.bytes.done();){var t=this.bytes.offset(),e=this.bytes.peek();if(e>=se.Space&&e<=se.Tilde&&(this.matchKeyword(Co.xref)||this.matchKeyword(Co.trailer)||this.matchKeyword(Co.startxref)||this.matchIndirectObjectHeader())){this.bytes.moveTo(t);break}this.bytes.next()}},e.prototype.skipBinaryHeaderComment=function(){this.skipWhitespaceAndComments();try{var t=this.bytes.offset();this.parseIndirectObjectHeader(),this.bytes.moveTo(t)}catch(e){this.bytes.next(),this.skipWhitespaceAndComments()}},e.forBytesWithOptions=function(t,r,n,o){return new e(t,r,n,o)},e}(To),Ao=function(t){return 1<<t};(po=lo||(lo={}))[po.Invisible=Ao(0)]="Invisible",po[po.Hidden=Ao(1)]="Hidden",po[po.Print=Ao(2)]="Print",po[po.NoZoom=Ao(3)]="NoZoom",po[po.NoRotate=Ao(4)]="NoRotate",po[po.NoView=Ao(5)]="NoView",po[po.ReadOnly=Ao(6)]="ReadOnly",po[po.Locked=Ao(7)]="Locked",po[po.ToggleNoView=Ao(8)]="ToggleNoView",po[po.LockedContents=Ao(9)]="LockedContents";var Ro,No,Do=function(t){return t instanceof Qe?t:Qe.of(t)},jo=function(t){return t instanceof qe?t:qe.of(t)},Mo=function(t){return t instanceof qe?t.asNumber():t};(No=Ro||(Ro={})).Degrees="degrees",No.Radians="radians";var Vo,Bo,Uo=function(t){return Qt(t,"radianAngle",["number"]),{type:Ro.Radians,angle:t}},Io=function(t){return Qt(t,"degreeAngle",["number"]),{type:Ro.Degrees,angle:t}},Wo=Ro.Radians,zo=Ro.Degrees,qo=function(t){return t*Math.PI/180},Eo=function(t){return 180*t/Math.PI},Go=function(t){return t.type===Wo?t.angle:t.type===zo?qo(t.angle):gt("Invalid rotation: "+JSON.stringify(t))},Ko=function(t){return t.type===Wo?Eo(t.angle):t.type===zo?t.angle:gt("Invalid rotation: "+JSON.stringify(t))},Lo=function(t){void 0===t&&(t=0);var e=t/90%4;return 0===e?0:1===e?90:2===e?180:3===e?270:0},Xo=function(t,e){void 0===e&&(e=0);var r=Lo(e);return 90===r||270===r?{width:t.height,height:t.width}:{width:t.width,height:t.height}},Ho=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0);var n=t.x,o=t.y,i=t.width,a=t.height,s=Lo(r),u=e/2;return 0===s?{x:n-u,y:o-u,width:i,height:a}:90===s?{x:n-a+u,y:o-u,width:a,height:i}:180===s?{x:n-i+u,y:o-a+u,width:i,height:a}:270===s?{x:n-u,y:o-i+u,width:a,height:i}:{x:n-u,y:o-u,width:i,height:a}},Zo=function(){return sr.of($e.ClipNonZero)},Yo=function(){return sr.of($e.ClipEvenOdd)},Jo=Math.cos,Qo=Math.sin,_o=Math.tan,$o=function(t,e,r,n,o,i){return sr.of($e.ConcatTransformationMatrix,[jo(t),jo(e),jo(r),jo(n),jo(o),jo(i)])},ti=function(t,e){return $o(1,0,0,1,t,e)},ei=function(t,e){return $o(t,0,0,e,0,0)},ri=function(t){return $o(Jo(Mo(t)),Qo(Mo(t)),-Qo(Mo(t)),Jo(Mo(t)),0,0)},ni=function(t){return ri(qo(Mo(t)))},oi=function(t,e){return $o(1,_o(Mo(t)),_o(Mo(e)),1,0,0)},ii=function(t,e){return oi(qo(Mo(t)),qo(Mo(e)))},ai=function(t,e){return sr.of($e.SetLineDashPattern,["["+t.map(jo).join(" ")+"]",jo(e)])},si=function(){return ai([],0)};(Bo=Vo||(Vo={}))[Bo.Butt=0]="Butt",Bo[Bo.Round=1]="Round",Bo[Bo.Projecting=2]="Projecting";var ui,ci,fi=function(t){return sr.of($e.SetLineCapStyle,[jo(t)])};(ci=ui||(ui={}))[ci.Miter=0]="Miter",ci[ci.Round=1]="Round",ci[ci.Bevel=2]="Bevel";var hi,li,di=function(t){return sr.of($e.SetLineJoinStyle,[jo(t)])},pi=function(t){return sr.of($e.SetGraphicsStateParams,[Do(t)])},yi=function(){return sr.of($e.PushGraphicsState)},gi=function(){return sr.of($e.PopGraphicsState)},vi=function(t){return sr.of($e.SetLineWidth,[jo(t)])},mi=function(t,e,r,n,o,i){return sr.of($e.AppendBezierCurve,[jo(t),jo(e),jo(r),jo(n),jo(o),jo(i)])},bi=function(t,e,r,n){return sr.of($e.CurveToReplicateInitialPoint,[jo(t),jo(e),jo(r),jo(n)])},xi=function(){return sr.of($e.ClosePath)},Fi=function(t,e){return sr.of($e.MoveTo,[jo(t),jo(e)])},wi=function(t,e){return sr.of($e.LineTo,[jo(t),jo(e)])},Si=function(t,e,r,n){return sr.of($e.AppendRectangle,[jo(t),jo(e),jo(r),jo(n)])},Ci=function(t,e,r){return Si(t,e,r,r)},Ti=function(){return sr.of($e.StrokePath)},ki=function(){return sr.of($e.FillNonZero)},Oi=function(){return sr.of($e.FillNonZeroAndStroke)},Pi=function(){return sr.of($e.EndPath)},Ai=function(){return sr.of($e.NextLine)},Ri=function(t,e){return sr.of($e.MoveText,[jo(t),jo(e)])},Ni=function(t){return sr.of($e.ShowText,[t])},Di=function(){return sr.of($e.BeginText)},ji=function(){return sr.of($e.EndText)},Mi=function(t,e){return sr.of($e.SetFontAndSize,[Do(t),jo(e)])},Vi=function(t){return sr.of($e.SetCharacterSpacing,[jo(t)])},Bi=function(t){return sr.of($e.SetWordSpacing,[jo(t)])},Ui=function(t){return sr.of($e.SetTextHorizontalScaling,[jo(t)])},Ii=function(t){return sr.of($e.SetTextLineHeight,[jo(t)])},Wi=function(t){return sr.of($e.SetTextRise,[jo(t)])};(li=hi||(hi={}))[li.Fill=0]="Fill",li[li.Outline=1]="Outline",li[li.FillAndOutline=2]="FillAndOutline",li[li.Invisible=3]="Invisible",li[li.FillAndClip=4]="FillAndClip",li[li.OutlineAndClip=5]="OutlineAndClip",li[li.FillAndOutlineAndClip=6]="FillAndOutlineAndClip",li[li.Clip=7]="Clip";var zi,qi,Ei=function(t){return sr.of($e.SetTextRenderingMode,[jo(t)])},Gi=function(t,e,r,n,o,i){return sr.of($e.SetTextMatrix,[jo(t),jo(e),jo(r),jo(n),jo(o),jo(i)])},Ki=function(t,e,r,n,o){return Gi(Jo(Mo(t)),Qo(Mo(t))+_o(Mo(e)),-Qo(Mo(t))+_o(Mo(r)),Jo(Mo(t)),n,o)},Li=function(t,e,r,n,o){return Ki(qo(Mo(t)),qo(Mo(e)),qo(Mo(r)),n,o)},Xi=function(t){return sr.of($e.DrawObject,[Do(t)])},Hi=function(t){return sr.of($e.NonStrokingColorGray,[jo(t)])},Zi=function(t){return sr.of($e.StrokingColorGray,[jo(t)])},Yi=function(t,e,r){return sr.of($e.NonStrokingColorRgb,[jo(t),jo(e),jo(r)])},Ji=function(t,e,r){return sr.of($e.StrokingColorRgb,[jo(t),jo(e),jo(r)])},Qi=function(t,e,r,n){return sr.of($e.NonStrokingColorCmyk,[jo(t),jo(e),jo(r),jo(n)])},_i=function(t,e,r,n){return sr.of($e.StrokingColorCmyk,[jo(t),jo(e),jo(r),jo(n)])},$i=function(t){return sr.of($e.BeginMarkedContent,[Do(t)])},ta=function(){return sr.of($e.EndMarkedContent)};(qi=zi||(zi={})).Grayscale="Grayscale",qi.RGB="RGB",qi.CMYK="CMYK";var ea,ra,na=function(t){return te(t,"gray",0,1),{type:zi.Grayscale,gray:t}},oa=function(t,e,r){return te(t,"red",0,1),te(e,"green",0,1),te(r,"blue",0,1),{type:zi.RGB,red:t,green:e,blue:r}},ia=function(t,e,r,n){return te(t,"cyan",0,1),te(e,"magenta",0,1),te(r,"yellow",0,1),te(n,"key",0,1),{type:zi.CMYK,cyan:t,magenta:e,yellow:r,key:n}},aa=zi.Grayscale,sa=zi.RGB,ua=zi.CMYK,ca=function(t){return t.type===aa?Hi(t.gray):t.type===sa?Yi(t.red,t.green,t.blue):t.type===ua?Qi(t.cyan,t.magenta,t.yellow,t.key):gt("Invalid color: "+JSON.stringify(t))},fa=function(t){return t.type===aa?Zi(t.gray):t.type===sa?Ji(t.red,t.green,t.blue):t.type===ua?_i(t.cyan,t.magenta,t.yellow,t.key):gt("Invalid color: "+JSON.stringify(t))},ha=function(t,e){return void 0===e&&(e=1),1===(null==t?void 0:t.length)?na(t[0]*e):3===(null==t?void 0:t.length)?oa(t[0]*e,t[1]*e,t[2]*e):4===(null==t?void 0:t.length)?ia(t[0]*e,t[1]*e,t[2]*e,t[3]*e):void 0},la=function(t){return t.type===aa?[t.gray]:t.type===sa?[t.red,t.green,t.blue]:t.type===ua?[t.cyan,t.magenta,t.yellow,t.key]:gt("Invalid color: "+JSON.stringify(t))},da=0,pa=0,ya=0,ga=0,va=0,ma=0,ba=new Map([["A",7],["a",7],["C",6],["c",6],["H",1],["h",1],["L",2],["l",2],["M",2],["m",2],["Q",4],["q",4],["S",4],["s",4],["T",2],["t",2],["V",1],["v",1],["Z",0],["z",0]]),xa={M:function(t){return da=t[0],pa=t[1],ya=ga=null,va=da,ma=pa,Fi(da,pa)},m:function(t){return da+=t[0],pa+=t[1],ya=ga=null,va=da,ma=pa,Fi(da,pa)},C:function(t){return da=t[4],pa=t[5],ya=t[2],ga=t[3],mi(t[0],t[1],t[2],t[3],t[4],t[5])},c:function(t){var e=mi(t[0]+da,t[1]+pa,t[2]+da,t[3]+pa,t[4]+da,t[5]+pa);return ya=da+t[2],ga=pa+t[3],da+=t[4],pa+=t[5],e},S:function(t){null!==ya&&null!==ga||(ya=da,ga=pa);var e=mi(da-(ya-da),pa-(ga-pa),t[0],t[1],t[2],t[3]);return ya=t[0],ga=t[1],da=t[2],pa=t[3],e},s:function(t){null!==ya&&null!==ga||(ya=da,ga=pa);var e=mi(da-(ya-da),pa-(ga-pa),da+t[0],pa+t[1],da+t[2],pa+t[3]);return ya=da+t[0],ga=pa+t[1],da+=t[2],pa+=t[3],e},Q:function(t){return ya=t[0],ga=t[1],da=t[2],pa=t[3],bi(t[0],t[1],da,pa)},q:function(t){var e=bi(t[0]+da,t[1]+pa,t[2]+da,t[3]+pa);return ya=da+t[0],ga=pa+t[1],da+=t[2],pa+=t[3],e},T:function(t){null===ya||null===ga?(ya=da,ga=pa):(ya=da-(ya-da),ga=pa-(ga-pa));var e=bi(ya,ga,t[0],t[1]);return ya=da-(ya-da),ga=pa-(ga-pa),da=t[0],pa=t[1],e},t:function(t){null===ya||null===ga?(ya=da,ga=pa):(ya=da-(ya-da),ga=pa-(ga-pa));var e=bi(ya,ga,da+t[0],pa+t[1]);return da+=t[0],pa+=t[1],e},A:function(t){var e=Fa(da,pa,t);return da=t[5],pa=t[6],e},a:function(t){t[5]+=da,t[6]+=pa;var e=Fa(da,pa,t);return da=t[5],pa=t[6],e},L:function(t){return da=t[0],pa=t[1],ya=ga=null,wi(da,pa)},l:function(t){return da+=t[0],pa+=t[1],ya=ga=null,wi(da,pa)},H:function(t){return da=t[0],ya=ga=null,wi(da,pa)},h:function(t){return da+=t[0],ya=ga=null,wi(da,pa)},V:function(t){return pa=t[0],ya=ga=null,wi(da,pa)},v:function(t){return pa+=t[0],ya=ga=null,wi(da,pa)},Z:function(){var t=xi();return da=va,pa=ma,t},z:function(){var t=xi();return da=va,pa=ma,t}},Fa=function(t,e,r){for(var n=r[0],o=r[1],i=r[2],a=r[3],s=r[4],u=r[5],c=r[6],f=[],h=0,l=wa(u,c,n,o,a,s,i,t,e);h<l.length;h++){var d=l[h],p=Sa.apply(void 0,d);f.push(mi.apply(void 0,p))}return f},wa=function(t,e,r,n,o,i,a,s,u){var c=a*(Math.PI/180),f=Math.sin(c),h=Math.cos(c);r=Math.abs(r),n=Math.abs(n);var l=(ya=h*(s-t)*.5+f*(u-e)*.5)*ya/(r*r)+(ga=h*(u-e)*.5-f*(s-t)*.5)*ga/(n*n);l>1&&(r*=l=Math.sqrt(l),n*=l);var d=h/r,p=f/r,y=-f/n,g=h/n,v=d*s+p*u,m=y*s+g*u,b=d*t+p*e,x=y*t+g*e,F=1/((b-v)*(b-v)+(x-m)*(x-m))-.25;F<0&&(F=0);var w=Math.sqrt(F);i===o&&(w=-w);var S=.5*(v+b)-w*(x-m),C=.5*(m+x)+w*(b-v),T=Math.atan2(m-C,v-S),k=Math.atan2(x-C,b-S)-T;k<0&&1===i?k+=2*Math.PI:k>0&&0===i&&(k-=2*Math.PI);for(var O=Math.ceil(Math.abs(k/(.5*Math.PI+.001))),P=[],A=0;A<O;A++){var R=T+A*k/O,N=T+(A+1)*k/O;P[A]=[S,C,R,N,r,n,f,h]}return P},Sa=function(t,e,r,n,o,i,a,s){var u=s*o,c=-a*i,f=a*o,h=s*i,l=.5*(n-r),d=8/3*Math.sin(.5*l)*Math.sin(.5*l)/Math.sin(l),p=t+Math.cos(r)-d*Math.sin(r),y=e+Math.sin(r)+d*Math.cos(r),g=t+Math.cos(n),v=e+Math.sin(n),m=g+d*Math.sin(n),b=v-d*Math.cos(n);return[u*p+c*y,f*p+h*y,u*m+c*b,f*m+h*b,u*g+c*v,f*g+h*v]},Ca=function(t){return function(t){da=pa=ya=ga=va=ma=0;for(var e=[],r=0;r<t.length;r++){var n=t[r];if(n.cmd&&"function"==typeof xa[n.cmd]){var o=xa[n.cmd](n.args);Array.isArray(o)?e=e.concat(o):e.push(o)}}return e}(function(t){for(var e,r=[],n=[],o="",i=!1,a=0,s=0,u=t;s<u.length;s++){var c=u[s];if(ba.has(c))a=ba.get(c),e&&(o.length>0&&(n[n.length]=+o),r[r.length]={cmd:e,args:n},n=[],o="",i=!1),e=c;else if([" ",","].includes(c)||"-"===c&&o.length>0&&"e"!==o[o.length-1]||"."===c&&i){if(0===o.length)continue;n.length===a?(r[r.length]={cmd:e,args:n},n=[+o],"M"===e&&(e="L"),"m"===e&&(e="l")):n[n.length]=+o,i="."===c,o=["-","."].includes(c)?c:""}else o+=c,"."===c&&(i=!0)}return o.length>0&&(n.length===a?(r[r.length]={cmd:e,args:n},n=[+o],"M"===e&&(e="L"),"m"===e&&(e="l")):n[n.length]=+o),r[r.length]={cmd:e,args:n},r}(t))},Ta=function(t,e){return[yi(),e.graphicsState&&pi(e.graphicsState),Di(),ca(e.color),Mi(e.font,e.size),Ki(Go(e.rotate),Go(e.xSkew),Go(e.ySkew),e.x,e.y),Ni(t),ji(),gi()].filter(Boolean)},ka=function(t,e){for(var r=[yi(),e.graphicsState&&pi(e.graphicsState),Di(),ca(e.color),Mi(e.font,e.size),Ii(e.lineHeight),Ki(Go(e.rotate),Go(e.xSkew),Go(e.ySkew),e.x,e.y)].filter(Boolean),n=0,o=t.length;n<o;n++)r.push(Ni(t[n]),Ai());return r.push(ji(),gi()),r},Oa=function(t,e){return[yi(),e.graphicsState&&pi(e.graphicsState),ti(e.x,e.y),ri(Go(e.rotate)),ei(e.width,e.height),oi(Go(e.xSkew),Go(e.ySkew)),Xi(t),gi()].filter(Boolean)},Pa=function(t,e){return[yi(),e.graphicsState&&pi(e.graphicsState),ti(e.x,e.y),ri(Go(e.rotate)),ei(e.xScale,e.yScale),oi(Go(e.xSkew),Go(e.ySkew)),Xi(t),gi()].filter(Boolean)},Aa=function(t){var e,r;return[yi(),t.graphicsState&&pi(t.graphicsState),t.color&&fa(t.color),vi(t.thickness),ai(null!==(e=t.dashArray)&&void 0!==e?e:[],null!==(r=t.dashPhase)&&void 0!==r?r:0),Fi(t.start.x,t.start.y),t.lineCap&&fi(t.lineCap),Fi(t.start.x,t.start.y),wi(t.end.x,t.end.y),Ti(),gi()].filter(Boolean)},Ra=function(t){var e,r;return[yi(),t.graphicsState&&pi(t.graphicsState),t.color&&ca(t.color),t.borderColor&&fa(t.borderColor),vi(t.borderWidth),t.borderLineCap&&fi(t.borderLineCap),ai(null!==(e=t.borderDashArray)&&void 0!==e?e:[],null!==(r=t.borderDashPhase)&&void 0!==r?r:0),ti(t.x,t.y),ri(Go(t.rotate)),oi(Go(t.xSkew),Go(t.ySkew)),Fi(0,0),wi(0,t.height),wi(t.width,t.height),wi(t.width,0),xi(),t.color&&t.borderWidth?Oi():t.color?ki():t.borderColor?Ti():xi(),gi()].filter(Boolean)},Na=(Math.sqrt(2)-1)/3*4,Da=function(t){var e=Mo(t.x),r=Mo(t.y),n=Mo(t.xScale),o=Mo(t.yScale),i=n*Na,a=o*Na,s=(e-=n)+2*n,u=(r-=o)+2*o,c=e+n,f=r+o;return[yi(),Fi(e,f),mi(e,f-a,c-i,r,c,r),mi(c+i,r,s,f-a,s,f),mi(s,f+a,c+i,u,c,u),mi(c-i,u,e,f+a,e,f),gi()]},ja=function(t){var e,r,n,o,i,s,u,c,f,h,l,d,p,y,g,v;return a([yi(),t.graphicsState&&pi(t.graphicsState),t.color&&ca(t.color),t.borderColor&&fa(t.borderColor),vi(t.borderWidth),t.borderLineCap&&fi(t.borderLineCap),ai(null!==(e=t.borderDashArray)&&void 0!==e?e:[],null!==(r=t.borderDashPhase)&&void 0!==r?r:0)],void 0===t.rotate?Da({x:t.x,y:t.y,xScale:t.xScale,yScale:t.yScale}):(o={x:t.x,y:t.y,xScale:t.xScale,yScale:t.yScale,rotate:null!==(n=t.rotate)&&void 0!==n?n:Io(0)},i=Mo(o.x),s=Mo(o.y),u=Mo(o.xScale),c=Mo(o.yScale),f=-u,h=-c,l=u*Na,d=c*Na,p=f+2*u,y=h+2*c,g=f+u,v=h+c,[ti(i,s),ri(Go(o.rotate)),Fi(f,v),mi(f,v-d,g-l,h,g,h),mi(g+l,h,p,v-d,p,v),mi(p,v+d,g+l,y,g,y),mi(g-l,y,f,v+d,f,v)]),[t.color&&t.borderWidth?Oi():t.color?ki():t.borderColor?Ti():xi(),gi()]).filter(Boolean)},Ma=function(t,e){var r,n,o;return a([yi(),e.graphicsState&&pi(e.graphicsState),ti(e.x,e.y),ri(Go(null!==(r=e.rotate)&&void 0!==r?r:Io(0))),e.scale?ei(e.scale,-e.scale):ei(1,-1),e.color&&ca(e.color),e.borderColor&&fa(e.borderColor),e.borderWidth&&vi(e.borderWidth),e.borderLineCap&&fi(e.borderLineCap),ai(null!==(n=e.borderDashArray)&&void 0!==n?n:[],null!==(o=e.borderDashPhase)&&void 0!==o?o:0)],Ca(t),[e.color&&e.borderWidth?Oi():e.color?ki():e.borderColor?Ti():xi(),gi()]).filter(Boolean)},Va=function(t){var e=Mo(t.size),r=-.49,n=.3995/.965+r;return[yi(),t.color&&fa(t.color),vi(t.thickness),ti(t.x,t.y),Fi(-.675*e,n*e),wi(-.25*e,r*e),wi(.69*e,.475*e),Ti(),gi()].filter(Boolean)},Ba=function(t){return 0===t.rotation?[ti(0,0),ni(0)]:90===t.rotation?[ti(t.width,0),ni(90)]:180===t.rotation?[ti(t.width,t.height),ni(180)]:270===t.rotation?[ti(0,t.height),ni(270)]:[]},Ua=function(t){var e=Ra({x:t.x,y:t.y,width:t.width,height:t.height,borderWidth:t.borderWidth,color:t.color,borderColor:t.borderColor,rotate:Io(0),xSkew:Io(0),ySkew:Io(0)});if(!t.filled)return e;var r=Mo(t.width),n=Mo(t.height),o=Math.min(r,n)/2,i=Va({x:r/2,y:n/2,size:o,thickness:t.thickness,color:t.markColor});return a([yi()],e,i,[gi()])},Ia=function(t){var e=Mo(t.width),r=Mo(t.height),n=Math.min(e,r)/2,o=ja({x:t.x,y:t.y,xScale:n,yScale:n,color:t.color,borderColor:t.borderColor,borderWidth:t.borderWidth});if(!t.filled)return o;var i=ja({x:t.x,y:t.y,xScale:.45*n,yScale:.45*n,color:t.dotColor,borderColor:void 0,borderWidth:0});return a([yi()],o,i,[gi()])},Wa=function(t){var e=Mo(t.x),r=Mo(t.y),n=Mo(t.width),o=Mo(t.height),i=Ra({x:e,y:r,width:n,height:o,borderWidth:t.borderWidth,color:t.color,borderColor:t.borderColor,rotate:Io(0),xSkew:Io(0),ySkew:Io(0)}),s=za(t.textLines,{color:t.textColor,font:t.font,size:t.fontSize,rotate:Io(0),xSkew:Io(0),ySkew:Io(0)});return a([yi()],i,s,[gi()])},za=function(t,e){for(var r=[Di(),ca(e.color),Mi(e.font,e.size)],n=0,o=t.length;n<o;n++){var i=t[n],a=i.encoded,s=i.x,u=i.y;r.push(Ki(Go(e.rotate),Go(e.xSkew),Go(e.ySkew),s,u),Ni(a))}return r.push(ji()),r},qa=function(t){var e=Mo(t.x),r=Mo(t.y),n=Mo(t.width),o=Mo(t.height),i=Mo(t.borderWidth),s=Mo(t.padding),u=e+i/2+s,c=r+i/2+s,f=n-2*(i/2+s),h=o-2*(i/2+s),l=[Fi(u,c),wi(u,c+h),wi(u+f,c+h),wi(u+f,c),xi(),Zo(),Pi()],d=Ra({x:e,y:r,width:n,height:o,borderWidth:t.borderWidth,color:t.color,borderColor:t.borderColor,rotate:Io(0),xSkew:Io(0),ySkew:Io(0)}),p=za(t.textLines,{color:t.textColor,font:t.font,size:t.fontSize,rotate:Io(0),xSkew:Io(0),ySkew:Io(0)}),y=a([$i("Tx"),yi()],p,[gi(),ta()]);return a([yi()],d,l,y,[gi()])},Ea=function(t){for(var e=Mo(t.x),r=Mo(t.y),n=Mo(t.width),o=Mo(t.height),i=Mo(t.lineHeight),s=Mo(t.borderWidth),u=Mo(t.padding),c=e+s/2+u,f=r+s/2+u,h=n-2*(s/2+u),l=o-2*(s/2+u),d=[Fi(c,f),wi(c,f+l),wi(c+h,f+l),wi(c+h,f),xi(),Zo(),Pi()],p=Ra({x:e,y:r,width:n,height:o,borderWidth:t.borderWidth,color:t.color,borderColor:t.borderColor,rotate:Io(0),xSkew:Io(0),ySkew:Io(0)}),y=[],g=0,v=t.selectedLines.length;g<v;g++){var m=t.textLines[t.selectedLines[g]];y.push.apply(y,Ra({x:m.x-u,y:m.y-(i-m.height)/2,width:n-s,height:m.height+(i-m.height)/2,borderWidth:0,color:t.selectedColor,borderColor:void 0,rotate:Io(0),xSkew:Io(0),ySkew:Io(0)}))}var b=za(t.textLines,{color:t.textColor,font:t.font,size:t.fontSize,rotate:Io(0),xSkew:Io(0),ySkew:Io(0)}),x=a([$i("Tx"),yi()],b,[gi(),ta()]);return a([yi()],p,y,d,x,[gi()])},Ga=function(t){function e(){return t.call(this,"Input document to `PDFDocument.load` is encrypted. You can use `PDFDocument.load(..., { ignoreEncryption: true })` if you wish to load the document anyways.")||this}return r(e,t),e}(Error),Ka=function(t){function e(){return t.call(this,"Input to `PDFDocument.embedFont` was a custom font, but no `fontkit` instance was found. You must register a `fontkit` instance with `PDFDocument.registerFontkit(...)` before embedding custom fonts.")||this}return r(e,t),e}(Error),La=function(t){function e(){return t.call(this,"A `page` passed to `PDFDocument.addPage` or `PDFDocument.insertPage` was from a different (foreign) PDF document. If you want to copy pages from one PDFDocument to another, you must use `PDFDocument.copyPages(...)` to copy the pages before adding or inserting them.")||this}return r(e,t),e}(Error),Xa=function(t){function e(){return t.call(this,"PDFDocument has no pages so `PDFDocument.removePage` cannot be called")||this}return r(e,t),e}(Error),Ha=function(t){function e(e){var r='PDFDocument has no form field with the name "'+e+'"';return t.call(this,r)||this}return r(e,t),e}(Error),Za=function(t){function e(e,r,n){var o,i,a='Expected field "'+e+'" to be of type '+(null==r?void 0:r.name)+", but it is actually of type "+(null!==(i=null===(o=null==n?void 0:n.constructor)||void 0===o?void 0:o.name)&&void 0!==i?i:n);return t.call(this,a)||this}return r(e,t),e}(Error),Ya=function(t){function e(e){var r='Failed to select check box due to missing onValue: "'+e+'"';return t.call(this,r)||this}return r(e,t),e}(Error),Ja=function(t){function e(e){var r='A field already exists with the specified name: "'+e+'"';return t.call(this,r)||this}return r(e,t),e}(Error),Qa=function(t){function e(e){var r='Field name contains invalid component: "'+e+'"';return t.call(this,r)||this}return r(e,t),e}(Error),_a=function(t){function e(e){var r='A non-terminal field already exists with the specified name: "'+e+'"';return t.call(this,r)||this}return r(e,t),e}(Error),$a=function(t){function e(e){var r="Reading rich text fields is not supported: Attempted to read rich text field: "+e;return t.call(this,r)||this}return r(e,t),e}(Error),ts=function(t){function e(e,r){var n="Failed to layout combed text as lineLength="+e+" is greater than cellCount="+r;return t.call(this,n)||this}return r(e,t),e}(Error),es=function(t){function e(e,r,n){var o="Attempted to set text with length="+e+" for TextField with maxLength="+r+" and name="+n;return t.call(this,o)||this}return r(e,t),e}(Error),rs=function(t){function e(e,r,n){var o="Attempted to set maxLength="+r+", which is less than "+e+", the length of this field's current value (name="+n+")";return t.call(this,o)||this}return r(e,t),e}(Error);(ra=ea||(ea={}))[ra.Left=0]="Left",ra[ra.Center=1]="Center",ra[ra.Right=2]="Right";var ns,os,is=function(t,e,r,n){void 0===n&&(n=!1);for(var o=4;o<500;){for(var i=0,a=0,s=t.length;a<s;a++){i+=1;for(var u=t[a].split(" "),c=r.width,f=0,h=u.length;f<h;f++){var l=f===h-1?u[f]:u[f]+" ",d=e.widthOfTextAtSize(l,o);(c-=d)<=0&&(i+=1,c=r.width-d)}}if(!n&&i>t.length)return o-1;var p=e.heightAtSize(o);if((p+.2*p)*i>Math.abs(r.height))return o-1;o+=1}return o},as=function(t){for(var e=t.length;e>0;e--)if(/\s/.test(t[e]))return e},ss=function(t,e,r,n){for(var o,i=t.length;i>0;){var a=t.substring(0,i),s=r.encodeText(a),u=r.widthOfTextAtSize(a,n);if(u<e)return{line:a,encoded:s,width:u,remainder:t.substring(i)||void 0};i=null!==(o=as(a))&&void 0!==o?o:0}return{line:t,encoded:r.encodeText(t),width:r.widthOfTextAtSize(t,n),remainder:void 0}},us=function(t,e){var r=e.alignment,n=e.fontSize,o=e.font,i=e.bounds,a=R(k(t));void 0!==n&&0!==n||(n=is(a,o,i,!0));for(var s=o.heightAtSize(n),u=s+.2*s,c=[],f=i.x,h=i.y,l=i.x+i.width,d=i.y+i.height,p=i.y+i.height,y=0,g=a.length;y<g;y++)for(var v=a[y];void 0!==v;){var m=ss(v,i.width,o,n),b=m.line,x=m.encoded,F=m.width,w=m.remainder,S=r===ea.Left?i.x:r===ea.Center?i.x+i.width/2-F/2:r===ea.Right?i.x+i.width-F:i.x;S<f&&(f=S),(p-=u)<h&&(h=p),S+F>l&&(l=S+F),p+s>d&&(d=p+s),c.push({text:b,encoded:x,width:F,height:s,x:S,y:p}),v=null==w?void 0:w.trim()}return{fontSize:n,lineHeight:u,lines:c,bounds:{x:f,y:h,width:l-f,height:d-h}}},cs=function(t,e){var r=e.fontSize,n=e.font,o=e.bounds,i=e.cellCount,a=N(k(t));if(a.length>i)throw new ts(a.length,i);void 0!==r&&0!==r||(r=function(t,e,r,n){for(var o=r.width/n,i=r.height,a=4,s=j(t);a<500;){for(var u=0,c=s.length;u<c;u++){var f=s[u];if(e.widthOfTextAtSize(f,a)>.75*o)return a-1}if(e.heightAtSize(a,{descender:!1})>i)return a-1;a+=1}return a}(a,n,o,i));for(var s=o.width/i,u=n.heightAtSize(r,{descender:!1}),c=o.y+(o.height/2-u/2),f=[],h=o.x,l=o.y,d=o.x+o.width,p=o.y+o.height,y=0,g=0;y<i;){var v=D(a,g),m=v[0],b=v[1],x=n.encodeText(m),F=n.widthOfTextAtSize(m,r),w=o.x+(s*y+s/2)-F/2;w<h&&(h=w),c<l&&(l=c),w+F>d&&(d=w+F),c+u>p&&(p=c+u),f.push({text:a,encoded:x,width:F,height:u,x:w,y:c}),y+=1,g+=b}return{fontSize:r,cells:f,bounds:{x:h,y:l,width:d-h,height:p-l}}},fs=function(t,e){var r=e.alignment,n=e.fontSize,o=e.font,i=e.bounds,a=N(k(t));void 0!==n&&0!==n||(n=is([a],o,i));var s=o.encodeText(a),u=o.widthOfTextAtSize(a,n),c=o.heightAtSize(n,{descender:!1}),f=r===ea.Left?i.x:r===ea.Center?i.x+i.width/2-u/2:r===ea.Right?i.x+i.width-u:i.x,h=i.y+(i.height/2-c/2);return{fontSize:n,line:{text:a,encoded:s,width:u,height:c,x:f,y:h},bounds:{x:f,y:h,width:u,height:c}}},hs=function(t){return"normal"in t?t:{normal:t}},ls=/\/([^\0\t\n\f\r\ ]+)[\0\t\n\f\r\ ]+(\d*\.\d+|\d+)[\0\t\n\f\r\ ]+Tf/,ds=function(t){var e,r,n=null!==(e=t.getDefaultAppearance())&&void 0!==e?e:"",o=null!==(r=U(n,ls).match)&&void 0!==r?r:[],i=Number(o[2]);return isFinite(i)?i:void 0},ps=/(\d*\.\d+|\d+)[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]+(g|rg|k)/,ys=function(t){var e,r=null!==(e=t.getDefaultAppearance())&&void 0!==e?e:"",n=U(r,ps).match,o=null!=n?n:[],i=o[1],a=o[2],s=o[3],u=o[4],c=o[5];return"g"===c&&i?na(Number(i)):"rg"===c&&i&&a&&s?oa(Number(i),Number(a),Number(s)):"k"===c&&i&&a&&s&&u?ia(Number(i),Number(a),Number(s),Number(u)):void 0},gs=function(t,e,r,n){var o;void 0===n&&(n=0);var i=[ca(e).toString(),Mi(null!==(o=null==r?void 0:r.name)&&void 0!==o?o:"dummy__noop",n).toString()].join("\n");t.setDefaultAppearance(i)},vs=function(t,e){var r,o,i,s=ys(e),u=ys(t.acroField),c=e.getRectangle(),f=e.getAppearanceCharacteristics(),h=e.getBorderStyle(),l=null!==(r=null==h?void 0:h.getWidth())&&void 0!==r?r:0,d=Lo(null==f?void 0:f.getRotation()),p=Xo(c,d),y=p.width,g=p.height,v=Ba(n(n({},c),{rotation:d})),m=oa(0,0,0),b=null!==(o=ha(null==f?void 0:f.getBorderColor()))&&void 0!==o?o:m,x=ha(null==f?void 0:f.getBackgroundColor()),F=ha(null==f?void 0:f.getBackgroundColor(),.8),w=null!==(i=null!=s?s:u)&&void 0!==i?i:m;gs(s?e:t.acroField,w);var S={x:0+l/2,y:0+l/2,width:y-l,height:g-l,thickness:1.5,borderWidth:l,borderColor:b,markColor:w};return{normal:{on:a(v,Ua(n(n({},S),{color:x,filled:!0}))),off:a(v,Ua(n(n({},S),{color:x,filled:!1})))},down:{on:a(v,Ua(n(n({},S),{color:F,filled:!0}))),off:a(v,Ua(n(n({},S),{color:F,filled:!1})))}}},ms=function(t,e){var r,o,i,s=ys(e),u=ys(t.acroField),c=e.getRectangle(),f=e.getAppearanceCharacteristics(),h=e.getBorderStyle(),l=null!==(r=null==h?void 0:h.getWidth())&&void 0!==r?r:0,d=Lo(null==f?void 0:f.getRotation()),p=Xo(c,d),y=p.width,g=p.height,v=Ba(n(n({},c),{rotation:d})),m=oa(0,0,0),b=null!==(o=ha(null==f?void 0:f.getBorderColor()))&&void 0!==o?o:m,x=ha(null==f?void 0:f.getBackgroundColor()),F=ha(null==f?void 0:f.getBackgroundColor(),.8),w=null!==(i=null!=s?s:u)&&void 0!==i?i:m;gs(s?e:t.acroField,w);var S={x:y/2,y:g/2,width:y-l,height:g-l,borderWidth:l,borderColor:b,dotColor:w};return{normal:{on:a(v,Ia(n(n({},S),{color:x,filled:!0}))),off:a(v,Ia(n(n({},S),{color:x,filled:!1})))},down:{on:a(v,Ia(n(n({},S),{color:F,filled:!0}))),off:a(v,Ia(n(n({},S),{color:F,filled:!1})))}}},bs=function(t,e,r){var o,i,s,u,c,f=ys(e),h=ys(t.acroField),l=ds(e),d=ds(t.acroField),p=e.getRectangle(),y=e.getAppearanceCharacteristics(),g=e.getBorderStyle(),v=null==y?void 0:y.getCaptions(),m=null!==(o=null==v?void 0:v.normal)&&void 0!==o?o:"",b=null!==(s=null!==(i=null==v?void 0:v.down)&&void 0!==i?i:m)&&void 0!==s?s:"",x=null!==(u=null==g?void 0:g.getWidth())&&void 0!==u?u:0,F=Lo(null==y?void 0:y.getRotation()),w=Xo(p,F),S=w.width,C=w.height,T=Ba(n(n({},p),{rotation:F})),k=oa(0,0,0),O=ha(null==y?void 0:y.getBorderColor()),P=ha(null==y?void 0:y.getBackgroundColor()),A=ha(null==y?void 0:y.getBackgroundColor(),.8),R={x:x,y:x,width:S-2*x,height:C-2*x},N=fs(m,{alignment:ea.Center,fontSize:null!=l?l:d,font:r,bounds:R}),D=fs(b,{alignment:ea.Center,fontSize:null!=l?l:d,font:r,bounds:R}),j=Math.min(N.fontSize,D.fontSize),M=null!==(c=null!=f?f:h)&&void 0!==c?c:k;gs(f||void 0!==l?e:t.acroField,M,r,j);var V={x:0+x/2,y:0+x/2,width:S-x,height:C-x,borderWidth:x,borderColor:O,textColor:M,font:r.name,fontSize:j};return{normal:a(T,Wa(n(n({},V),{color:P,textLines:[N.line]}))),down:a(T,Wa(n(n({},V),{color:A,textLines:[D.line]})))}},xs=function(t,e,r){var o,i,s,u,c,f,h=ys(e),l=ys(t.acroField),d=ds(e),p=ds(t.acroField),y=e.getRectangle(),g=e.getAppearanceCharacteristics(),v=e.getBorderStyle(),m=null!==(o=t.getText())&&void 0!==o?o:"",b=null!==(i=null==v?void 0:v.getWidth())&&void 0!==i?i:0,x=Lo(null==g?void 0:g.getRotation()),F=Xo(y,x),w=F.width,S=F.height,C=Ba(n(n({},y),{rotation:x})),T=oa(0,0,0),k=ha(null==g?void 0:g.getBorderColor()),O=ha(null==g?void 0:g.getBackgroundColor()),P=t.isCombed()?0:1,A={x:b+P,y:b+P,width:w-2*(b+P),height:S-2*(b+P)};if(t.isMultiline())c=(R=us(m,{alignment:t.getAlignment(),fontSize:null!=d?d:p,font:r,bounds:A})).lines,f=R.fontSize;else if(t.isCombed()){c=(R=cs(m,{fontSize:null!=d?d:p,font:r,bounds:A,cellCount:null!==(s=t.getMaxLength())&&void 0!==s?s:0})).cells,f=R.fontSize}else{var R;c=[(R=fs(m,{alignment:t.getAlignment(),fontSize:null!=d?d:p,font:r,bounds:A})).line],f=R.fontSize}var N=null!==(u=null!=h?h:l)&&void 0!==u?u:T;gs(h||void 0!==d?e:t.acroField,N,r,f);var D={x:0+b/2,y:0+b/2,width:w-b,height:S-b,borderWidth:null!=b?b:0,borderColor:k,textColor:N,font:r.name,fontSize:f,color:O,textLines:c,padding:P};return a(C,qa(D))},Fs=function(t,e,r){var o,i,s,u=ys(e),c=ys(t.acroField),f=ds(e),h=ds(t.acroField),l=e.getRectangle(),d=e.getAppearanceCharacteristics(),p=e.getBorderStyle(),y=null!==(o=t.getSelected()[0])&&void 0!==o?o:"",g=null!==(i=null==p?void 0:p.getWidth())&&void 0!==i?i:0,v=Lo(null==d?void 0:d.getRotation()),m=Xo(l,v),b=m.width,x=m.height,F=Ba(n(n({},l),{rotation:v})),w=oa(0,0,0),S=ha(null==d?void 0:d.getBorderColor()),C=ha(null==d?void 0:d.getBackgroundColor()),T={x:g+1,y:g+1,width:b-2*(g+1),height:x-2*(g+1)},k=fs(y,{alignment:ea.Left,fontSize:null!=f?f:h,font:r,bounds:T}),O=k.line,P=k.fontSize,A=null!==(s=null!=u?u:c)&&void 0!==s?s:w;gs(u||void 0!==f?e:t.acroField,A,r,P);var R={x:0+g/2,y:0+g/2,width:b-g,height:x-g,borderWidth:null!=g?g:0,borderColor:S,textColor:A,font:r.name,fontSize:P,color:C,textLines:[O],padding:1};return a(F,qa(R))},ws=function(t,e,r){var o,i,s=ys(e),u=ys(t.acroField),c=ds(e),f=ds(t.acroField),h=e.getRectangle(),l=e.getAppearanceCharacteristics(),d=e.getBorderStyle(),p=null!==(o=null==d?void 0:d.getWidth())&&void 0!==o?o:0,y=Lo(null==l?void 0:l.getRotation()),g=Xo(h,y),v=g.width,m=g.height,b=Ba(n(n({},h),{rotation:y})),x=oa(0,0,0),F=ha(null==l?void 0:l.getBorderColor()),w=ha(null==l?void 0:l.getBackgroundColor()),S=t.getOptions(),C=t.getSelected();t.isSorted()&&S.sort();for(var T="",k=0,O=S.length;k<O;k++)T+=S[k],k<O-1&&(T+="\n");var P={x:p+1,y:p+1,width:v-2*(p+1),height:m-2*(p+1)},A=us(T,{alignment:ea.Left,fontSize:null!=c?c:f,font:r,bounds:P}),R=A.lines,N=A.fontSize,D=A.lineHeight,j=[];for(k=0,O=R.length;k<O;k++){var M=R[k];C.includes(M.text)&&j.push(k)}var V=oa(.6,193/255,218/255),B=null!==(i=null!=s?s:u)&&void 0!==i?i:x;return gs(s||void 0!==c?e:t.acroField,B,r,N),a(b,Ea({x:0+p/2,y:0+p/2,width:v-p,height:m-p,borderWidth:null!=p?p:0,borderColor:F,textColor:B,font:r.name,fontSize:N,color:w,textLines:R,lineHeight:D,selectedColor:V,selectedLines:j,padding:1}))},Ss=function(){function t(t,e,r){this.alreadyEmbedded=!1,Qt(t,"ref",[[ar,"PDFRef"]]),Qt(e,"doc",[[Zs,"PDFDocument"]]),Qt(r,"embedder",[[Fn,"PDFPageEmbedder"]]),this.ref=t,this.doc=e,this.width=r.width,this.height=r.height,this.embedder=r}return t.prototype.scale=function(t){return Qt(t,"factor",["number"]),{width:this.width*t,height:this.height*t}},t.prototype.size=function(){return this.scale(1)},t.prototype.embed=function(){return o(this,0,void 0,(function(){return i(this,(function(t){switch(t.label){case 0:return this.alreadyEmbedded?[3,2]:[4,this.embedder.embedIntoContext(this.doc.context,this.ref)];case 1:t.sent(),this.alreadyEmbedded=!0,t.label=2;case 2:return[2]}}))}))},t.of=function(e,r,n){return new t(e,r,n)},t}(),Cs=function(){function t(t,e,r){this.modified=!0,Qt(t,"ref",[[ar,"PDFRef"]]),Qt(e,"doc",[[Zs,"PDFDocument"]]),Qt(r,"embedder",[[Mr,"CustomFontEmbedder"],[Pr,"StandardFontEmbedder"]]),this.ref=t,this.doc=e,this.name=r.fontName,this.embedder=r}return t.prototype.encodeText=function(t){return Qt(t,"text",["string"]),this.modified=!0,this.embedder.encodeText(t)},t.prototype.widthOfTextAtSize=function(t,e){return Qt(t,"text",["string"]),Qt(e,"size",["number"]),this.embedder.widthOfTextAtSize(t,e)},t.prototype.heightAtSize=function(t,e){var r;return Qt(t,"size",["number"]),_t(null==e?void 0:e.descender,"options.descender",["boolean"]),this.embedder.heightOfFontAtSize(t,{descender:null===(r=null==e?void 0:e.descender)||void 0===r||r})},t.prototype.sizeAtHeight=function(t){return Qt(t,"height",["number"]),this.embedder.sizeOfFontAtHeight(t)},t.prototype.getCharacterSet=function(){return this.embedder instanceof Pr?this.embedder.encoding.supportedCodePoints:this.embedder.font.characterSet},t.prototype.embed=function(){return o(this,0,void 0,(function(){return i(this,(function(t){switch(t.label){case 0:return this.modified?[4,this.embedder.embedIntoContext(this.doc.context,this.ref)]:[3,2];case 1:t.sent(),this.modified=!1,t.label=2;case 2:return[2]}}))}))},t.of=function(e,r,n){return new t(e,r,n)},t}(),Ts=function(){function t(t,e,r){Qt(t,"ref",[[ar,"PDFRef"]]),Qt(e,"doc",[[Zs,"PDFDocument"]]),Qt(r,"embedder",[[Kr,"JpegEmbedder"],[nn,"PngEmbedder"]]),this.ref=t,this.doc=e,this.width=r.width,this.height=r.height,this.embedder=r}return t.prototype.scale=function(t){return Qt(t,"factor",["number"]),{width:this.width*t,height:this.height*t}},t.prototype.scaleToFit=function(t,e){Qt(t,"width",["number"]),Qt(e,"height",["number"]);var r=t/this.width,n=e/this.height,o=Math.min(r,n);return this.scale(o)},t.prototype.size=function(){return this.scale(1)},t.prototype.embed=function(){return o(this,0,void 0,(function(){var t,e,r;return i(this,(function(n){switch(n.label){case 0:return this.embedder?(this.embedTask||(e=(t=this).doc,r=t.ref,this.embedTask=this.embedder.embedIntoContext(e.context,r)),[4,this.embedTask]):[2];case 1:return n.sent(),this.embedder=void 0,[2]}}))}))},t.of=function(e,r,n){return new t(e,r,n)},t}();(os=ns||(ns={}))[os.Left=0]="Left",os[os.Center=1]="Center",os[os.Right=2]="Right";var ks,Os,Ps=function(t){_t(null==t?void 0:t.x,"options.x",["number"]),_t(null==t?void 0:t.y,"options.y",["number"]),_t(null==t?void 0:t.width,"options.width",["number"]),_t(null==t?void 0:t.height,"options.height",["number"]),_t(null==t?void 0:t.textColor,"options.textColor",[[Object,"Color"]]),_t(null==t?void 0:t.backgroundColor,"options.backgroundColor",[[Object,"Color"]]),_t(null==t?void 0:t.borderColor,"options.borderColor",[[Object,"Color"]]),_t(null==t?void 0:t.borderWidth,"options.borderWidth",["number"]),_t(null==t?void 0:t.rotate,"options.rotate",[[Object,"Rotation"]])},As=function(){function t(t,e,r){Qt(t,"acroField",[[In,"PDFAcroTerminal"]]),Qt(e,"ref",[[ar,"PDFRef"]]),Qt(r,"doc",[[Zs,"PDFDocument"]]),this.acroField=t,this.ref=e,this.doc=r}return t.prototype.getName=function(){var t;return null!==(t=this.acroField.getFullyQualifiedName())&&void 0!==t?t:""},t.prototype.isReadOnly=function(){return this.acroField.hasFlag(Sn.ReadOnly)},t.prototype.enableReadOnly=function(){this.acroField.setFlagTo(Sn.ReadOnly,!0)},t.prototype.disableReadOnly=function(){this.acroField.setFlagTo(Sn.ReadOnly,!1)},t.prototype.isRequired=function(){return this.acroField.hasFlag(Sn.Required)},t.prototype.enableRequired=function(){this.acroField.setFlagTo(Sn.Required,!0)},t.prototype.disableRequired=function(){this.acroField.setFlagTo(Sn.Required,!1)},t.prototype.isExported=function(){return!this.acroField.hasFlag(Sn.NoExport)},t.prototype.enableExporting=function(){this.acroField.setFlagTo(Sn.NoExport,!1)},t.prototype.disableExporting=function(){this.acroField.setFlagTo(Sn.NoExport,!0)},t.prototype.needsAppearancesUpdate=function(){throw new he(this.constructor.name,"needsAppearancesUpdate")},t.prototype.defaultUpdateAppearances=function(t){throw new he(this.constructor.name,"defaultUpdateAppearances")},t.prototype.markAsDirty=function(){this.doc.getForm().markFieldAsDirty(this.ref)},t.prototype.markAsClean=function(){this.doc.getForm().markFieldAsClean(this.ref)},t.prototype.isDirty=function(){return this.doc.getForm().fieldIsDirty(this.ref)},t.prototype.createWidget=function(t){var e,r=t.textColor,n=t.backgroundColor,o=t.borderColor,i=t.borderWidth,a=Ko(t.rotate),s=t.caption,u=t.x,c=t.y,f=t.width+i,h=t.height+i,l=Boolean(t.hidden),d=t.page;re(a,"degreesAngle",90);var p=Un.create(this.doc.context,this.ref),y=Ho({x:u,y:c,width:f,height:h},i,a);p.setRectangle(y),d&&p.setP(d);var g=p.getOrCreateAppearanceCharacteristics();n&&g.setBackgroundColor(la(n)),g.setRotation(a),s&&g.setCaptions({normal:s}),o&&g.setBorderColor(la(o));var v=p.getOrCreateBorderStyle();if(void 0!==i&&v.setWidth(i),p.setFlagTo(lo.Print,!0),p.setFlagTo(lo.Hidden,l),p.setFlagTo(lo.Invisible,!1),r){var m=(null!==(e=this.acroField.getDefaultAppearance())&&void 0!==e?e:"")+"\n"+ca(r).toString();this.acroField.setDefaultAppearance(m)}return p},t.prototype.updateWidgetAppearanceWithFont=function(t,e,r){var n=r.normal,o=r.rollover,i=r.down;this.updateWidgetAppearances(t,{normal:this.createAppearanceStream(t,n,e),rollover:o&&this.createAppearanceStream(t,o,e),down:i&&this.createAppearanceStream(t,i,e)})},t.prototype.updateOnOffWidgetAppearance=function(t,e,r){var n=r.normal,o=r.rollover,i=r.down;this.updateWidgetAppearances(t,{normal:this.createAppearanceDict(t,n,e),rollover:o&&this.createAppearanceDict(t,o,e),down:i&&this.createAppearanceDict(t,i,e)})},t.prototype.updateWidgetAppearances=function(t,e){var r=e.normal,n=e.rollover,o=e.down;t.setNormalAppearance(r),n?t.setRolloverAppearance(n):t.removeRolloverAppearance(),o?t.setDownAppearance(o):t.removeDownAppearance()},t.prototype.createAppearanceStream=function(t,e,r){var n,o=this.acroField.dict.context,i=t.getRectangle(),a=i.width,s=i.height,u=r&&{Font:(n={},n[r.name]=r.ref,n)},c=o.formXObject(e,{Resources:u,BBox:o.obj([0,0,a,s]),Matrix:o.obj([1,0,0,1,0,0])});return o.register(c)},t.prototype.createImageAppearanceStream=function(t,e,r){var o,i,s=this.acroField.dict.context,u=t.getRectangle(),c=t.getAppearanceCharacteristics(),f=t.getBorderStyle(),h=null!==(i=null==f?void 0:f.getWidth())&&void 0!==i?i:0,l=Lo(null==c?void 0:c.getRotation()),d=Ba(n(n({},u),{rotation:l})),p=Xo(u,l),y=e.scaleToFit(p.width-2*h,p.height-2*h),g={x:h,y:h,width:y.width,height:y.height,rotate:Io(0),xSkew:Io(0),ySkew:Io(0)};r===ns.Center?(g.x+=(p.width-2*h)/2-y.width/2,g.y+=(p.height-2*h)/2-y.height/2):r===ns.Right&&(g.x=p.width-h-y.width,g.y=p.height-h-y.height);var v=this.doc.context.addRandomSuffix("Image",10),m=a(d,Oa(v,g)),b={XObject:(o={},o[v]=e.ref,o)},x=s.formXObject(m,{Resources:b,BBox:s.obj([0,0,u.width,u.height]),Matrix:s.obj([1,0,0,1,0,0])});return s.register(x)},t.prototype.createAppearanceDict=function(t,e,r){var n=this.acroField.dict.context,o=this.createAppearanceStream(t,e.on),i=this.createAppearanceStream(t,e.off),a=n.obj({});return a.set(r,o),a.set(Qe.of("Off"),i),a},t}(),Rs=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return Qt(e,"acroCheckBox",[[zn,"PDFAcroCheckBox"]]),o.acroField=e,o}return r(e,t),e.prototype.check=function(){var t,e=null!==(t=this.acroField.getOnValue())&&void 0!==t?t:Qe.of("Yes");this.markAsDirty(),this.acroField.setValue(e)},e.prototype.uncheck=function(){this.markAsDirty(),this.acroField.setValue(Qe.of("Off"))},e.prototype.isChecked=function(){var t=this.acroField.getOnValue();return!!t&&t===this.acroField.getValue()},e.prototype.addToPage=function(t,e){var r,n,o,i,a,s;Qt(t,"page",[[Js,"PDFPage"]]),Ps(e),e||(e={}),"textColor"in e||(e.textColor=oa(0,0,0)),"backgroundColor"in e||(e.backgroundColor=oa(1,1,1)),"borderColor"in e||(e.borderColor=oa(0,0,0)),"borderWidth"in e||(e.borderWidth=1);var u=this.createWidget({x:null!==(r=e.x)&&void 0!==r?r:0,y:null!==(n=e.y)&&void 0!==n?n:0,width:null!==(o=e.width)&&void 0!==o?o:50,height:null!==(i=e.height)&&void 0!==i?i:50,textColor:e.textColor,backgroundColor:e.backgroundColor,borderColor:e.borderColor,borderWidth:null!==(a=e.borderWidth)&&void 0!==a?a:0,rotate:null!==(s=e.rotate)&&void 0!==s?s:Io(0),hidden:e.hidden,page:t.ref}),c=this.doc.context.register(u.dict);this.acroField.addWidget(c),u.setAppearanceState(Qe.of("Off")),this.updateWidgetAppearance(u,Qe.of("Yes")),t.node.addAnnot(c)},e.prototype.needsAppearancesUpdate=function(){for(var t,e=this.acroField.getWidgets(),r=0,n=e.length;r<n;r++){var o=e[r],i=o.getAppearanceState(),a=null===(t=o.getAppearances())||void 0===t?void 0:t.normal;if(!(a instanceof er))return!0;if(i&&!a.has(i))return!0}return!1},e.prototype.defaultUpdateAppearances=function(){this.updateAppearances()},e.prototype.updateAppearances=function(t){var e;_t(t,"provider",[Function]);for(var r=this.acroField.getWidgets(),n=0,o=r.length;n<o;n++){var i=r[n],a=null!==(e=i.getOnValue())&&void 0!==e?e:Qe.of("Yes");a&&this.updateWidgetAppearance(i,a,t)}this.markAsClean()},e.prototype.updateWidgetAppearance=function(t,e,r){var n=hs((null!=r?r:vs)(this,t));this.updateOnOffWidgetAppearance(t,e,n)},e.of=function(t,r,n){return new e(t,r,n)},e}(As),Ns=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return Qt(e,"acroComboBox",[[Gn,"PDFAcroComboBox"]]),o.acroField=e,o}return r(e,t),e.prototype.getOptions=function(){for(var t=this.acroField.getOptions(),e=new Array(t.length),r=0,n=e.length;r<n;r++){var o=t[r],i=o.display,a=o.value;e[r]=(null!=i?i:a).decodeText()}return e},e.prototype.getSelected=function(){for(var t=this.acroField.getValues(),e=new Array(t.length),r=0,n=t.length;r<n;r++)e[r]=t[r].decodeText();return e},e.prototype.setOptions=function(t){Qt(t,"options",[Array]);for(var e=new Array(t.length),r=0,n=t.length;r<n;r++)e[r]={value:Or.fromText(t[r])};this.acroField.setOptions(e)},e.prototype.addOptions=function(t){Qt(t,"options",["string",Array]);for(var e=Array.isArray(t)?t:[t],r=this.acroField.getOptions(),n=new Array(e.length),o=0,i=e.length;o<i;o++)n[o]={value:Or.fromText(e[o])};this.acroField.setOptions(r.concat(n))},e.prototype.select=function(t,e){void 0===e&&(e=!1),Qt(t,"options",["string",Array]),Qt(e,"merge",["boolean"]);var r=Array.isArray(t)?t:[t],n=this.getOptions();r.find((function(t){return!n.includes(t)}))&&this.enableEditing(),this.markAsDirty(),(r.length>1||1===r.length&&e)&&this.enableMultiselect();for(var o=new Array(r.length),i=0,a=r.length;i<a;i++)o[i]=Or.fromText(r[i]);if(e){var s=this.acroField.getValues();this.acroField.setValues(s.concat(o))}else this.acroField.setValues(o)},e.prototype.clear=function(){this.markAsDirty(),this.acroField.setValues([])},e.prototype.setFontSize=function(t){oe(t,"fontSize"),this.acroField.setFontSize(t),this.markAsDirty()},e.prototype.isEditable=function(){return this.acroField.hasFlag(An.Edit)},e.prototype.enableEditing=function(){this.acroField.setFlagTo(An.Edit,!0)},e.prototype.disableEditing=function(){this.acroField.setFlagTo(An.Edit,!1)},e.prototype.isSorted=function(){return this.acroField.hasFlag(An.Sort)},e.prototype.enableSorting=function(){this.acroField.setFlagTo(An.Sort,!0)},e.prototype.disableSorting=function(){this.acroField.setFlagTo(An.Sort,!1)},e.prototype.isMultiselect=function(){return this.acroField.hasFlag(An.MultiSelect)},e.prototype.enableMultiselect=function(){this.acroField.setFlagTo(An.MultiSelect,!0)},e.prototype.disableMultiselect=function(){this.acroField.setFlagTo(An.MultiSelect,!1)},e.prototype.isSpellChecked=function(){return!this.acroField.hasFlag(An.DoNotSpellCheck)},e.prototype.enableSpellChecking=function(){this.acroField.setFlagTo(An.DoNotSpellCheck,!1)},e.prototype.disableSpellChecking=function(){this.acroField.setFlagTo(An.DoNotSpellCheck,!0)},e.prototype.isSelectOnClick=function(){return this.acroField.hasFlag(An.CommitOnSelChange)},e.prototype.enableSelectOnClick=function(){this.acroField.setFlagTo(An.CommitOnSelChange,!0)},e.prototype.disableSelectOnClick=function(){this.acroField.setFlagTo(An.CommitOnSelChange,!1)},e.prototype.addToPage=function(t,e){var r,n,o,i,a,s,u;Qt(t,"page",[[Js,"PDFPage"]]),Ps(e),e||(e={}),"textColor"in e||(e.textColor=oa(0,0,0)),"backgroundColor"in e||(e.backgroundColor=oa(1,1,1)),"borderColor"in e||(e.borderColor=oa(0,0,0)),"borderWidth"in e||(e.borderWidth=1);var c=this.createWidget({x:null!==(r=e.x)&&void 0!==r?r:0,y:null!==(n=e.y)&&void 0!==n?n:0,width:null!==(o=e.width)&&void 0!==o?o:200,height:null!==(i=e.height)&&void 0!==i?i:50,textColor:e.textColor,backgroundColor:e.backgroundColor,borderColor:e.borderColor,borderWidth:null!==(a=e.borderWidth)&&void 0!==a?a:0,rotate:null!==(s=e.rotate)&&void 0!==s?s:Io(0),hidden:e.hidden,page:t.ref}),f=this.doc.context.register(c.dict);this.acroField.addWidget(f);var h=null!==(u=e.font)&&void 0!==u?u:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(c,h),t.node.addAnnot(f)},e.prototype.needsAppearancesUpdate=function(){var t;if(this.isDirty())return!0;for(var e=this.acroField.getWidgets(),r=0,n=e.length;r<n;r++){if(!((null===(t=e[r].getAppearances())||void 0===t?void 0:t.normal)instanceof rr))return!0}return!1},e.prototype.defaultUpdateAppearances=function(t){Qt(t,"font",[[Cs,"PDFFont"]]),this.updateAppearances(t)},e.prototype.updateAppearances=function(t,e){Qt(t,"font",[[Cs,"PDFFont"]]),_t(e,"provider",[Function]);for(var r=this.acroField.getWidgets(),n=0,o=r.length;n<o;n++){var i=r[n];this.updateWidgetAppearance(i,t,e)}this.markAsClean()},e.prototype.updateWidgetAppearance=function(t,e,r){var n=hs((null!=r?r:Fs)(this,t,e));this.updateWidgetAppearanceWithFont(t,e,n)},e.of=function(t,r,n){return new e(t,r,n)},e}(As),Ds=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return Qt(e,"acroListBox",[[Yn,"PDFAcroListBox"]]),o.acroField=e,o}return r(e,t),e.prototype.getOptions=function(){for(var t=this.acroField.getOptions(),e=new Array(t.length),r=0,n=e.length;r<n;r++){var o=t[r],i=o.display,a=o.value;e[r]=(null!=i?i:a).decodeText()}return e},e.prototype.getSelected=function(){for(var t=this.acroField.getValues(),e=new Array(t.length),r=0,n=t.length;r<n;r++)e[r]=t[r].decodeText();return e},e.prototype.setOptions=function(t){Qt(t,"options",[Array]),this.markAsDirty();for(var e=new Array(t.length),r=0,n=t.length;r<n;r++)e[r]={value:Or.fromText(t[r])};this.acroField.setOptions(e)},e.prototype.addOptions=function(t){Qt(t,"options",["string",Array]),this.markAsDirty();for(var e=Array.isArray(t)?t:[t],r=this.acroField.getOptions(),n=new Array(e.length),o=0,i=e.length;o<i;o++)n[o]={value:Or.fromText(e[o])};this.acroField.setOptions(r.concat(n))},e.prototype.select=function(t,e){void 0===e&&(e=!1),Qt(t,"options",["string",Array]),Qt(e,"merge",["boolean"]);var r=Array.isArray(t)?t:[t],n=this.getOptions();Ht(r,"option",n),this.markAsDirty(),(r.length>1||1===r.length&&e)&&this.enableMultiselect();for(var o=new Array(r.length),i=0,a=r.length;i<a;i++)o[i]=Or.fromText(r[i]);if(e){var s=this.acroField.getValues();this.acroField.setValues(s.concat(o))}else this.acroField.setValues(o)},e.prototype.clear=function(){this.markAsDirty(),this.acroField.setValues([])},e.prototype.setFontSize=function(t){oe(t,"fontSize"),this.acroField.setFontSize(t),this.markAsDirty()},e.prototype.isSorted=function(){return this.acroField.hasFlag(An.Sort)},e.prototype.enableSorting=function(){this.acroField.setFlagTo(An.Sort,!0)},e.prototype.disableSorting=function(){this.acroField.setFlagTo(An.Sort,!1)},e.prototype.isMultiselect=function(){return this.acroField.hasFlag(An.MultiSelect)},e.prototype.enableMultiselect=function(){this.acroField.setFlagTo(An.MultiSelect,!0)},e.prototype.disableMultiselect=function(){this.acroField.setFlagTo(An.MultiSelect,!1)},e.prototype.isSelectOnClick=function(){return this.acroField.hasFlag(An.CommitOnSelChange)},e.prototype.enableSelectOnClick=function(){this.acroField.setFlagTo(An.CommitOnSelChange,!0)},e.prototype.disableSelectOnClick=function(){this.acroField.setFlagTo(An.CommitOnSelChange,!1)},e.prototype.addToPage=function(t,e){var r,n,o,i,a,s,u;Qt(t,"page",[[Js,"PDFPage"]]),Ps(e),e||(e={}),"textColor"in e||(e.textColor=oa(0,0,0)),"backgroundColor"in e||(e.backgroundColor=oa(1,1,1)),"borderColor"in e||(e.borderColor=oa(0,0,0)),"borderWidth"in e||(e.borderWidth=1);var c=this.createWidget({x:null!==(r=e.x)&&void 0!==r?r:0,y:null!==(n=e.y)&&void 0!==n?n:0,width:null!==(o=e.width)&&void 0!==o?o:200,height:null!==(i=e.height)&&void 0!==i?i:100,textColor:e.textColor,backgroundColor:e.backgroundColor,borderColor:e.borderColor,borderWidth:null!==(a=e.borderWidth)&&void 0!==a?a:0,rotate:null!==(s=e.rotate)&&void 0!==s?s:Io(0),hidden:e.hidden,page:t.ref}),f=this.doc.context.register(c.dict);this.acroField.addWidget(f);var h=null!==(u=e.font)&&void 0!==u?u:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(c,h),t.node.addAnnot(f)},e.prototype.needsAppearancesUpdate=function(){var t;if(this.isDirty())return!0;for(var e=this.acroField.getWidgets(),r=0,n=e.length;r<n;r++){if(!((null===(t=e[r].getAppearances())||void 0===t?void 0:t.normal)instanceof rr))return!0}return!1},e.prototype.defaultUpdateAppearances=function(t){Qt(t,"font",[[Cs,"PDFFont"]]),this.updateAppearances(t)},e.prototype.updateAppearances=function(t,e){Qt(t,"font",[[Cs,"PDFFont"]]),_t(e,"provider",[Function]);for(var r=this.acroField.getWidgets(),n=0,o=r.length;n<o;n++){var i=r[n];this.updateWidgetAppearance(i,t,e)}this.markAsClean()},e.prototype.updateWidgetAppearance=function(t,e,r){var n=hs((null!=r?r:ws)(this,t,e));this.updateWidgetAppearanceWithFont(t,e,n)},e.of=function(t,r,n){return new e(t,r,n)},e}(As),js=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return Qt(e,"acroRadioButton",[[Zn,"PDFAcroRadioButton"]]),o.acroField=e,o}return r(e,t),e.prototype.getOptions=function(){var t=this.acroField.getExportValues();if(t){for(var e=new Array(t.length),r=0,n=t.length;r<n;r++)e[r]=t[r].decodeText();return e}var o=this.acroField.getOnValues(),i=new Array(o.length);for(r=0,n=i.length;r<n;r++)i[r]=o[r].decodeText();return i},e.prototype.getSelected=function(){var t=this.acroField.getValue();if(t!==Qe.of("Off")){var e=this.acroField.getExportValues();if(e)for(var r=this.acroField.getOnValues(),n=0,o=r.length;n<o;n++)if(r[n]===t)return e[n].decodeText();return t.decodeText()}},e.prototype.select=function(t){Qt(t,"option",["string"]);var e=this.getOptions();Lt(t,"option",e),this.markAsDirty();var r=this.acroField.getOnValues(),n=this.acroField.getExportValues();if(n)for(var o=0,i=n.length;o<i;o++)n[o].decodeText()===t&&this.acroField.setValue(r[o]);else for(o=0,i=r.length;o<i;o++){var a=r[o];a.decodeText()===t&&this.acroField.setValue(a)}},e.prototype.clear=function(){this.markAsDirty(),this.acroField.setValue(Qe.of("Off"))},e.prototype.isOffToggleable=function(){return!this.acroField.hasFlag(Tn.NoToggleToOff)},e.prototype.enableOffToggling=function(){this.acroField.setFlagTo(Tn.NoToggleToOff,!1)},e.prototype.disableOffToggling=function(){this.acroField.setFlagTo(Tn.NoToggleToOff,!0)},e.prototype.isMutuallyExclusive=function(){return!this.acroField.hasFlag(Tn.RadiosInUnison)},e.prototype.enableMutualExclusion=function(){this.acroField.setFlagTo(Tn.RadiosInUnison,!1)},e.prototype.disableMutualExclusion=function(){this.acroField.setFlagTo(Tn.RadiosInUnison,!0)},e.prototype.addOptionToPage=function(t,e,r){var n,o,i,a,s,u,c,f,h;Qt(t,"option",["string"]),Qt(e,"page",[[Js,"PDFPage"]]),Ps(r);var l=this.createWidget({x:null!==(n=null==r?void 0:r.x)&&void 0!==n?n:0,y:null!==(o=null==r?void 0:r.y)&&void 0!==o?o:0,width:null!==(i=null==r?void 0:r.width)&&void 0!==i?i:50,height:null!==(a=null==r?void 0:r.height)&&void 0!==a?a:50,textColor:null!==(s=null==r?void 0:r.textColor)&&void 0!==s?s:oa(0,0,0),backgroundColor:null!==(u=null==r?void 0:r.backgroundColor)&&void 0!==u?u:oa(1,1,1),borderColor:null!==(c=null==r?void 0:r.borderColor)&&void 0!==c?c:oa(0,0,0),borderWidth:null!==(f=null==r?void 0:r.borderWidth)&&void 0!==f?f:1,rotate:null!==(h=null==r?void 0:r.rotate)&&void 0!==h?h:Io(0),hidden:null==r?void 0:r.hidden,page:e.ref}),d=this.doc.context.register(l.dict),p=this.acroField.addWidgetWithOpt(d,Or.fromText(t),!this.isMutuallyExclusive());l.setAppearanceState(Qe.of("Off")),this.updateWidgetAppearance(l,p),e.node.addAnnot(d)},e.prototype.needsAppearancesUpdate=function(){for(var t,e=this.acroField.getWidgets(),r=0,n=e.length;r<n;r++){var o=e[r],i=o.getAppearanceState(),a=null===(t=o.getAppearances())||void 0===t?void 0:t.normal;if(!(a instanceof er))return!0;if(i&&!a.has(i))return!0}return!1},e.prototype.defaultUpdateAppearances=function(){this.updateAppearances()},e.prototype.updateAppearances=function(t){_t(t,"provider",[Function]);for(var e=this.acroField.getWidgets(),r=0,n=e.length;r<n;r++){var o=e[r],i=o.getOnValue();i&&this.updateWidgetAppearance(o,i,t)}},e.prototype.updateWidgetAppearance=function(t,e,r){var n=hs((null!=r?r:ms)(this,t));this.updateOnOffWidgetAppearance(t,e,n)},e.of=function(t,r,n){return new e(t,r,n)},e}(As),Ms=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return Qt(e,"acroSignature",[[Ln,"PDFAcroSignature"]]),o.acroField=e,o}return r(e,t),e.prototype.needsAppearancesUpdate=function(){return!1},e.of=function(t,r,n){return new e(t,r,n)},e}(As),Vs=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return Qt(e,"acroText",[[Xn,"PDFAcroText"]]),o.acroField=e,o}return r(e,t),e.prototype.getText=function(){var t=this.acroField.getValue();if(!t&&this.isRichFormatted())throw new $a(this.getName());return null==t?void 0:t.decodeText()},e.prototype.setText=function(t){_t(t,"text",["string"]);var e=this.getMaxLength();if(void 0!==e&&t&&t.length>e)throw new es(t.length,e,this.getName());this.markAsDirty(),this.disableRichFormatting(),t?this.acroField.setValue(Or.fromText(t)):this.acroField.removeValue()},e.prototype.getAlignment=function(){var t=this.acroField.getQuadding();return 0===t?ea.Left:1===t?ea.Center:2===t?ea.Right:ea.Left},e.prototype.setAlignment=function(t){Lt(t,"alignment",ea),this.markAsDirty(),this.acroField.setQuadding(t)},e.prototype.getMaxLength=function(){return this.acroField.getMaxLength()},e.prototype.setMaxLength=function(t){if(ee(t,"maxLength",0,Number.MAX_SAFE_INTEGER),this.markAsDirty(),void 0===t)this.acroField.removeMaxLength();else{var e=this.getText();if(e&&e.length>t)throw new rs(e.length,t,this.getName());this.acroField.setMaxLength(t)}},e.prototype.removeMaxLength=function(){this.markAsDirty(),this.acroField.removeMaxLength()},e.prototype.setImage=function(t){for(var e=this.getAlignment(),r=e===ea.Center?ns.Center:e===ea.Right?ns.Right:ns.Left,n=this.acroField.getWidgets(),o=0,i=n.length;o<i;o++){var a=n[o],s=this.createImageAppearanceStream(a,t,r);this.updateWidgetAppearances(a,{normal:s})}this.markAsClean()},e.prototype.setFontSize=function(t){oe(t,"fontSize"),this.acroField.setFontSize(t),this.markAsDirty()},e.prototype.isMultiline=function(){return this.acroField.hasFlag(On.Multiline)},e.prototype.enableMultiline=function(){this.markAsDirty(),this.acroField.setFlagTo(On.Multiline,!0)},e.prototype.disableMultiline=function(){this.markAsDirty(),this.acroField.setFlagTo(On.Multiline,!1)},e.prototype.isPassword=function(){return this.acroField.hasFlag(On.Password)},e.prototype.enablePassword=function(){this.acroField.setFlagTo(On.Password,!0)},e.prototype.disablePassword=function(){this.acroField.setFlagTo(On.Password,!1)},e.prototype.isFileSelector=function(){return this.acroField.hasFlag(On.FileSelect)},e.prototype.enableFileSelection=function(){this.acroField.setFlagTo(On.FileSelect,!0)},e.prototype.disableFileSelection=function(){this.acroField.setFlagTo(On.FileSelect,!1)},e.prototype.isSpellChecked=function(){return!this.acroField.hasFlag(On.DoNotSpellCheck)},e.prototype.enableSpellChecking=function(){this.acroField.setFlagTo(On.DoNotSpellCheck,!1)},e.prototype.disableSpellChecking=function(){this.acroField.setFlagTo(On.DoNotSpellCheck,!0)},e.prototype.isScrollable=function(){return!this.acroField.hasFlag(On.DoNotScroll)},e.prototype.enableScrolling=function(){this.acroField.setFlagTo(On.DoNotScroll,!1)},e.prototype.disableScrolling=function(){this.acroField.setFlagTo(On.DoNotScroll,!0)},e.prototype.isCombed=function(){return this.acroField.hasFlag(On.Comb)&&!this.isMultiline()&&!this.isPassword()&&!this.isFileSelector()&&void 0!==this.getMaxLength()},e.prototype.enableCombing=function(){if(void 0===this.getMaxLength()){console.warn("PDFTextFields must have a max length in order to be combed")}this.markAsDirty(),this.disableMultiline(),this.disablePassword(),this.disableFileSelection(),this.acroField.setFlagTo(On.Comb,!0)},e.prototype.disableCombing=function(){this.markAsDirty(),this.acroField.setFlagTo(On.Comb,!1)},e.prototype.isRichFormatted=function(){return this.acroField.hasFlag(On.RichText)},e.prototype.enableRichFormatting=function(){this.acroField.setFlagTo(On.RichText,!0)},e.prototype.disableRichFormatting=function(){this.acroField.setFlagTo(On.RichText,!1)},e.prototype.addToPage=function(t,e){var r,n,o,i,a,s,u;Qt(t,"page",[[Js,"PDFPage"]]),Ps(e),e||(e={}),"textColor"in e||(e.textColor=oa(0,0,0)),"backgroundColor"in e||(e.backgroundColor=oa(1,1,1)),"borderColor"in e||(e.borderColor=oa(0,0,0)),"borderWidth"in e||(e.borderWidth=1);var c=this.createWidget({x:null!==(r=e.x)&&void 0!==r?r:0,y:null!==(n=e.y)&&void 0!==n?n:0,width:null!==(o=e.width)&&void 0!==o?o:200,height:null!==(i=e.height)&&void 0!==i?i:50,textColor:e.textColor,backgroundColor:e.backgroundColor,borderColor:e.borderColor,borderWidth:null!==(a=e.borderWidth)&&void 0!==a?a:0,rotate:null!==(s=e.rotate)&&void 0!==s?s:Io(0),hidden:e.hidden,page:t.ref}),f=this.doc.context.register(c.dict);this.acroField.addWidget(f);var h=null!==(u=e.font)&&void 0!==u?u:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(c,h),t.node.addAnnot(f)},e.prototype.needsAppearancesUpdate=function(){var t;if(this.isDirty())return!0;for(var e=this.acroField.getWidgets(),r=0,n=e.length;r<n;r++){if(!((null===(t=e[r].getAppearances())||void 0===t?void 0:t.normal)instanceof rr))return!0}return!1},e.prototype.defaultUpdateAppearances=function(t){Qt(t,"font",[[Cs,"PDFFont"]]),this.updateAppearances(t)},e.prototype.updateAppearances=function(t,e){Qt(t,"font",[[Cs,"PDFFont"]]),_t(e,"provider",[Function]);for(var r=this.acroField.getWidgets(),n=0,o=r.length;n<o;n++){var i=r[n];this.updateWidgetAppearance(i,t,e)}this.markAsClean()},e.prototype.updateWidgetAppearance=function(t,e,r){var n=hs((null!=r?r:xs)(this,t,e));this.updateWidgetAppearanceWithFont(t,e,n)},e.of=function(t,r,n){return new e(t,r,n)},e}(As);(Os=ks||(ks={})).Courier="Courier",Os.CourierBold="Courier-Bold",Os.CourierOblique="Courier-Oblique",Os.CourierBoldOblique="Courier-BoldOblique",Os.Helvetica="Helvetica",Os.HelveticaBold="Helvetica-Bold",Os.HelveticaOblique="Helvetica-Oblique",Os.HelveticaBoldOblique="Helvetica-BoldOblique",Os.TimesRoman="Times-Roman",Os.TimesRomanBold="Times-Bold",Os.TimesRomanItalic="Times-Italic",Os.TimesRomanBoldItalic="Times-BoldItalic",Os.Symbol="Symbol",Os.ZapfDingbats="ZapfDingbats";var Bs,Us,Is=function(){function t(t,e){var r=this;this.embedDefaultFont=function(){return r.doc.embedStandardFont(ks.Helvetica)},Qt(t,"acroForm",[[io,"PDFAcroForm"]]),Qt(e,"doc",[[Zs,"PDFDocument"]]),this.acroForm=t,this.doc=e,this.dirtyFields=new Set,this.defaultFontCache=fe.populatedBy(this.embedDefaultFont)}return t.prototype.hasXFA=function(){return this.acroForm.dict.has(Qe.of("XFA"))},t.prototype.deleteXFA=function(){this.acroForm.dict.delete(Qe.of("XFA"))},t.prototype.getFields=function(){for(var t=this.acroForm.getAllFields(),e=[],r=0,n=t.length;r<n;r++){var o=t[r],i=o[0],a=o[1],s=Ws(i,a,this.doc);s&&e.push(s)}return e},t.prototype.getFieldMaybe=function(t){Qt(t,"name",["string"]);for(var e=this.getFields(),r=0,n=e.length;r<n;r++){var o=e[r];if(o.getName()===t)return o}},t.prototype.getField=function(t){Qt(t,"name",["string"]);var e=this.getFieldMaybe(t);if(e)return e;throw new Ha(t)},t.prototype.getButton=function(t){Qt(t,"name",["string"]);var e=this.getField(t);if(e instanceof Qs)return e;throw new Za(t,Qs,e)},t.prototype.getCheckBox=function(t){Qt(t,"name",["string"]);var e=this.getField(t);if(e instanceof Rs)return e;throw new Za(t,Rs,e)},t.prototype.getDropdown=function(t){Qt(t,"name",["string"]);var e=this.getField(t);if(e instanceof Ns)return e;throw new Za(t,Ns,e)},t.prototype.getOptionList=function(t){Qt(t,"name",["string"]);var e=this.getField(t);if(e instanceof Ds)return e;throw new Za(t,Ds,e)},t.prototype.getRadioGroup=function(t){Qt(t,"name",["string"]);var e=this.getField(t);if(e instanceof js)return e;throw new Za(t,js,e)},t.prototype.getSignature=function(t){Qt(t,"name",["string"]);var e=this.getField(t);if(e instanceof Ms)return e;throw new Za(t,Ms,e)},t.prototype.getTextField=function(t){Qt(t,"name",["string"]);var e=this.getField(t);if(e instanceof Vs)return e;throw new Za(t,Vs,e)},t.prototype.createButton=function(t){Qt(t,"name",["string"]);var e=zs(t),r=this.findOrCreateNonTerminals(e.nonTerminal),n=Hn.create(this.doc.context);return n.setPartialName(e.terminal),qs(r,[n,n.ref],e.terminal),Qs.of(n,n.ref,this.doc)},t.prototype.createCheckBox=function(t){Qt(t,"name",["string"]);var e=zs(t),r=this.findOrCreateNonTerminals(e.nonTerminal),n=zn.create(this.doc.context);return n.setPartialName(e.terminal),qs(r,[n,n.ref],e.terminal),Rs.of(n,n.ref,this.doc)},t.prototype.createDropdown=function(t){Qt(t,"name",["string"]);var e=zs(t),r=this.findOrCreateNonTerminals(e.nonTerminal),n=Gn.create(this.doc.context);return n.setPartialName(e.terminal),qs(r,[n,n.ref],e.terminal),Ns.of(n,n.ref,this.doc)},t.prototype.createOptionList=function(t){Qt(t,"name",["string"]);var e=zs(t),r=this.findOrCreateNonTerminals(e.nonTerminal),n=Yn.create(this.doc.context);return n.setPartialName(e.terminal),qs(r,[n,n.ref],e.terminal),Ds.of(n,n.ref,this.doc)},t.prototype.createRadioGroup=function(t){Qt(t,"name",["string"]);var e=zs(t),r=this.findOrCreateNonTerminals(e.nonTerminal),n=Zn.create(this.doc.context);return n.setPartialName(e.terminal),qs(r,[n,n.ref],e.terminal),js.of(n,n.ref,this.doc)},t.prototype.createTextField=function(t){Qt(t,"name",["string"]);var e=zs(t),r=this.findOrCreateNonTerminals(e.nonTerminal),n=Xn.create(this.doc.context);return n.setPartialName(e.terminal),qs(r,[n,n.ref],e.terminal),Vs.of(n,n.ref,this.doc)},t.prototype.flatten=function(t){void 0===t&&(t={updateFieldAppearances:!0}),t.updateFieldAppearances&&this.updateFieldAppearances();for(var e=this.getFields(),r=0,o=e.length;r<o;r++){for(var i=e[r],s=i.acroField.getWidgets(),u=0,c=s.length;u<c;u++){var f=s[u],h=this.findWidgetPage(f),l=this.findWidgetAppearanceRef(i,f),d=h.node.newXObject("FlatWidget",l),p=f.getRectangle(),y=a([yi(),ti(p.x,p.y)],Ba(n(n({},p),{rotation:0})),[Xi(d),gi()]).filter(Boolean);h.pushOperators.apply(h,y)}this.removeField(i)}},t.prototype.removeField=function(t){for(var e=t.acroField.getWidgets(),r=new Set,n=0,o=e.length;n<o;n++){var i=e[n],a=this.findWidgetAppearanceRef(t,i),s=this.findWidgetPage(i);r.add(s),s.node.removeAnnot(a)}r.forEach((function(e){return e.node.removeAnnot(t.ref)})),this.acroForm.removeField(t.acroField);for(var u=t.acroField.normalizedEntries().Kids,c=u.size(),f=0;f<c;f++){var h=u.get(f);h instanceof ar&&this.doc.context.delete(h)}this.doc.context.delete(t.ref)},t.prototype.updateFieldAppearances=function(t){_t(t,"font",[[Cs,"PDFFont"]]),t=null!=t?t:this.getDefaultFont();for(var e=this.getFields(),r=0,n=e.length;r<n;r++){var o=e[r];o.needsAppearancesUpdate()&&o.defaultUpdateAppearances(t)}},t.prototype.markFieldAsDirty=function(t){_t(t,"fieldRef",[[ar,"PDFRef"]]),this.dirtyFields.add(t)},t.prototype.markFieldAsClean=function(t){_t(t,"fieldRef",[[ar,"PDFRef"]]),this.dirtyFields.delete(t)},t.prototype.fieldIsDirty=function(t){return _t(t,"fieldRef",[[ar,"PDFRef"]]),this.dirtyFields.has(t)},t.prototype.getDefaultFont=function(){return this.defaultFontCache.access()},t.prototype.findWidgetPage=function(t){var e=t.P(),r=this.doc.getPages().find((function(t){return t.ref===e}));if(void 0===r){var n=this.doc.context.getObjectRef(t.dict);if(void 0===n)throw new Error("Could not find PDFRef for PDFObject");if(void 0===(r=this.doc.findPageForAnnotationRef(n)))throw new Error("Could not find page for PDFRef "+n)}return r},t.prototype.findWidgetAppearanceRef=function(t,e){var r,n=e.getNormalAppearance();if(n instanceof er&&(t instanceof Rs||t instanceof js)){var o=t.acroField.getValue(),i=null!==(r=n.get(o))&&void 0!==r?r:n.get(Qe.of("Off"));i instanceof ar&&(n=i)}if(!(n instanceof ar)){var a=t.getName();throw new Error("Failed to extract appearance ref for: "+a)}return n},t.prototype.findOrCreateNonTerminals=function(t){for(var e=[this.acroForm],r=0,n=t.length;r<n;r++){var o=t[r];if(!o)throw new Qa(o);var i=e[0],a=e[1],s=this.findNonTerminal(o,i);if(s)e=s;else{var u=Kn.create(this.doc.context);u.setPartialName(o),u.setParent(a);var c=this.doc.context.register(u.dict);i.addField(c),e=[u,c]}}return e},t.prototype.findNonTerminal=function(t,e){for(var r=e instanceof io?this.acroForm.getFields():Jn(e.Kids()),n=0,o=r.length;n<o;n++){var i=r[n],a=i[0],s=i[1];if(a.getPartialName()===t){if(a instanceof Kn)return[a,s];throw new Ja(t)}}},t.of=function(e,r){return new t(e,r)},t}(),Ws=function(t,e,r){return t instanceof Hn?Qs.of(t,e,r):t instanceof zn?Rs.of(t,e,r):t instanceof Gn?Ns.of(t,e,r):t instanceof Yn?Ds.of(t,e,r):t instanceof Xn?Vs.of(t,e,r):t instanceof Zn?js.of(t,e,r):t instanceof Ln?Ms.of(t,e,r):void 0},zs=function(t){if(0===t.length)throw new Error("PDF field names must not be empty strings");for(var e=t.split("."),r=0,n=e.length;r<n;r++)if(""===e[r])throw new Error('Periods in PDF field names must be separated by at least one character: "'+t+'"');return 1===e.length?{nonTerminal:[],terminal:e[0]}:{nonTerminal:e.slice(0,e.length-1),terminal:e[e.length-1]}},qs=function(t,e,r){for(var n=t[0],o=t[1],i=e[0],a=e[1],s=n.normalizedEntries(),u=(Jn("Kids"in s?s.Kids:s.Fields)),c=0,f=u.length;c<f;c++)if(u[c][0].getPartialName()===r)throw new Ja(r);n.addField(a),i.setParent(o)},Es={"4A0":[4767.87,6740.79],"2A0":[3370.39,4767.87],A0:[2383.94,3370.39],A1:[1683.78,2383.94],A2:[1190.55,1683.78],A3:[841.89,1190.55],A4:[595.28,841.89],A5:[419.53,595.28],A6:[297.64,419.53],A7:[209.76,297.64],A8:[147.4,209.76],A9:[104.88,147.4],A10:[73.7,104.88],B0:[2834.65,4008.19],B1:[2004.09,2834.65],B2:[1417.32,2004.09],B3:[1000.63,1417.32],B4:[708.66,1000.63],B5:[498.9,708.66],B6:[354.33,498.9],B7:[249.45,354.33],B8:[175.75,249.45],B9:[124.72,175.75],B10:[87.87,124.72],C0:[2599.37,3676.54],C1:[1836.85,2599.37],C2:[1298.27,1836.85],C3:[918.43,1298.27],C4:[649.13,918.43],C5:[459.21,649.13],C6:[323.15,459.21],C7:[229.61,323.15],C8:[161.57,229.61],C9:[113.39,161.57],C10:[79.37,113.39],RA0:[2437.8,3458.27],RA1:[1729.13,2437.8],RA2:[1218.9,1729.13],RA3:[864.57,1218.9],RA4:[609.45,864.57],SRA0:[2551.18,3628.35],SRA1:[1814.17,2551.18],SRA2:[1275.59,1814.17],SRA3:[907.09,1275.59],SRA4:[637.8,907.09],Executive:[521.86,756],Folio:[612,936],Legal:[612,1008],Letter:[612,792],Tabloid:[792,1224]};(Us=Bs||(Bs={}))[Us.Fastest=1/0]="Fastest",Us[Us.Fast=1500]="Fast",Us[Us.Medium=500]="Medium",Us[Us.Slow=100]="Slow";var Gs,Ks,Ls=function(){function t(t,e,r){this.alreadyEmbedded=!1,this.ref=t,this.doc=e,this.embedder=r}return t.prototype.embed=function(){return o(this,0,void 0,(function(){var t,e,r,n;return i(this,(function(o){switch(o.label){case 0:return this.alreadyEmbedded?[3,2]:[4,this.embedder.embedIntoContext(this.doc.context,this.ref)];case 1:t=o.sent(),this.doc.catalog.has(Qe.of("Names"))||this.doc.catalog.set(Qe.of("Names"),this.doc.context.obj({})),(e=this.doc.catalog.lookup(Qe.of("Names"),er)).has(Qe.of("EmbeddedFiles"))||e.set(Qe.of("EmbeddedFiles"),this.doc.context.obj({})),(r=e.lookup(Qe.of("EmbeddedFiles"),er)).has(Qe.of("Names"))||r.set(Qe.of("Names"),this.doc.context.obj([])),(n=r.lookup(Qe.of("Names"),Ee)).push(Or.fromText(this.embedder.fileName)),n.push(t),this.doc.catalog.has(Qe.of("AF"))||this.doc.catalog.set(Qe.of("AF"),this.doc.context.obj([])),this.doc.catalog.lookup(Qe.of("AF"),Ee).push(t),this.alreadyEmbedded=!0,o.label=2;case 2:return[2]}}))}))},t.of=function(e,r,n){return new t(e,r,n)},t}(),Xs=function(){function t(t,e,r){this.alreadyEmbedded=!1,this.ref=t,this.doc=e,this.embedder=r}return t.prototype.embed=function(){return o(this,0,void 0,(function(){var t,e,r,n,o,a,s;return i(this,(function(i){switch(i.label){case 0:return this.alreadyEmbedded?[3,2]:(t=this.doc,e=t.catalog,r=t.context,[4,this.embedder.embedIntoContext(this.doc.context,this.ref)]);case 1:n=i.sent(),e.has(Qe.of("Names"))||e.set(Qe.of("Names"),r.obj({})),(o=e.lookup(Qe.of("Names"),er)).has(Qe.of("JavaScript"))||o.set(Qe.of("JavaScript"),r.obj({})),(a=o.lookup(Qe.of("JavaScript"),er)).has(Qe.of("Names"))||a.set(Qe.of("Names"),r.obj([])),(s=a.lookup(Qe.of("Names"),Ee)).push(Or.fromText(this.embedder.scriptName)),s.push(n),this.alreadyEmbedded=!0,i.label=2;case 2:return[2]}}))}))},t.of=function(e,r,n){return new t(e,r,n)},t}(),Hs=function(){function t(t,e){this.script=t,this.scriptName=e}return t.for=function(e,r){return new t(e,r)},t.prototype.embedIntoContext=function(t,e){return o(this,0,void 0,(function(){var r;return i(this,(function(n){return r=t.obj({Type:"Action",S:"JavaScript",JS:Or.fromText(this.script)}),e?(t.assign(e,r),[2,e]):[2,t.register(r)]}))}))},t}(),Zs=function(){function t(t,e,r){var n=this;if(this.defaultWordBreaks=[" "],this.computePages=function(){var t=[];return n.catalog.Pages().traverse((function(e,r){if(e instanceof yr){var o=n.pageMap.get(e);o||(o=Js.of(e,r,n),n.pageMap.set(e,o)),t.push(o)}})),t},this.getOrCreateForm=function(){var t=n.catalog.getOrCreateAcroForm();return Is.of(t,n)},Qt(t,"context",[[pr,"PDFContext"]]),Qt(e,"ignoreEncryption",["boolean"]),this.context=t,this.catalog=t.lookup(t.trailerInfo.Root),this.isEncrypted=!!t.lookup(t.trailerInfo.Encrypt),this.pageCache=fe.populatedBy(this.computePages),this.pageMap=new Map,this.formCache=fe.populatedBy(this.getOrCreateForm),this.fonts=[],this.images=[],this.embeddedPages=[],this.embeddedFiles=[],this.javaScripts=[],!e&&this.isEncrypted)throw new Ga;r&&this.updateInfoDict()}return t.load=function(e,r){return void 0===r&&(r={}),o(this,0,void 0,(function(){var n,o,a,s,u,c,f,h,l,d,p;return i(this,(function(i){switch(i.label){case 0:return n=r.ignoreEncryption,o=void 0!==n&&n,a=r.parseSpeed,s=void 0===a?Bs.Slow:a,u=r.throwOnInvalidObject,c=void 0!==u&&u,f=r.updateMetadata,h=void 0===f||f,l=r.capNumbers,d=void 0!==l&&l,Qt(e,"pdf",["string",Uint8Array,ArrayBuffer]),Qt(o,"ignoreEncryption",["boolean"]),Qt(s,"parseSpeed",["number"]),Qt(c,"throwOnInvalidObject",["boolean"]),p=J(e),[4,Po.forBytesWithOptions(p,s,c,d).parseDocument()];case 1:return[2,new t(i.sent(),o,h)]}}))}))},t.create=function(e){return void 0===e&&(e={}),o(this,0,void 0,(function(){var r,n,o,a,s,u;return i(this,(function(i){return r=e.updateMetadata,n=void 0===r||r,o=pr.create(),a=so.withContext(o),s=o.register(a),u=ao.withContextAndPages(o,s),o.trailerInfo.Root=o.register(u),[2,new t(o,!1,n)]}))}))},t.prototype.registerFontkit=function(t){this.fontkit=t},t.prototype.getForm=function(){var t=this.formCache.access();return t.hasXFA()&&(console.warn("Removing XFA form data as pdf-lib does not support reading or writing XFA"),t.deleteXFA()),t},t.prototype.getTitle=function(){var t=this.getInfoDict().lookup(Qe.Title);if(t)return Ys(t),t.decodeText()},t.prototype.getAuthor=function(){var t=this.getInfoDict().lookup(Qe.Author);if(t)return Ys(t),t.decodeText()},t.prototype.getSubject=function(){var t=this.getInfoDict().lookup(Qe.Subject);if(t)return Ys(t),t.decodeText()},t.prototype.getKeywords=function(){var t=this.getInfoDict().lookup(Qe.Keywords);if(t)return Ys(t),t.decodeText()},t.prototype.getCreator=function(){var t=this.getInfoDict().lookup(Qe.Creator);if(t)return Ys(t),t.decodeText()},t.prototype.getProducer=function(){var t=this.getInfoDict().lookup(Qe.Producer);if(t)return Ys(t),t.decodeText()},t.prototype.getCreationDate=function(){var t=this.getInfoDict().lookup(Qe.CreationDate);if(t)return Ys(t),t.decodeDate()},t.prototype.getModificationDate=function(){var t=this.getInfoDict().lookup(Qe.ModDate);if(t)return Ys(t),t.decodeDate()},t.prototype.setTitle=function(t,e){Qt(t,"title",["string"]);var r=Qe.of("Title");(this.getInfoDict().set(r,Or.fromText(t)),null==e?void 0:e.showInWindowTitleBar)&&this.catalog.getOrCreateViewerPreferences().setDisplayDocTitle(!0)},t.prototype.setAuthor=function(t){Qt(t,"author",["string"]);var e=Qe.of("Author");this.getInfoDict().set(e,Or.fromText(t))},t.prototype.setSubject=function(t){Qt(t,"author",["string"]);var e=Qe.of("Subject");this.getInfoDict().set(e,Or.fromText(t))},t.prototype.setKeywords=function(t){Qt(t,"keywords",[Array]);var e=Qe.of("Keywords");this.getInfoDict().set(e,Or.fromText(t.join(" ")))},t.prototype.setCreator=function(t){Qt(t,"creator",["string"]);var e=Qe.of("Creator");this.getInfoDict().set(e,Or.fromText(t))},t.prototype.setProducer=function(t){Qt(t,"creator",["string"]);var e=Qe.of("Producer");this.getInfoDict().set(e,Or.fromText(t))},t.prototype.setLanguage=function(t){Qt(t,"language",["string"]);var e=Qe.of("Lang");this.catalog.set(e,jr.of(t))},t.prototype.setCreationDate=function(t){Qt(t,"creationDate",[[Date,"Date"]]);var e=Qe.of("CreationDate");this.getInfoDict().set(e,jr.fromDate(t))},t.prototype.setModificationDate=function(t){Qt(t,"modificationDate",[[Date,"Date"]]);var e=Qe.of("ModDate");this.getInfoDict().set(e,jr.fromDate(t))},t.prototype.getPageCount=function(){return void 0===this.pageCount&&(this.pageCount=this.getPages().length),this.pageCount},t.prototype.getPages=function(){return this.pageCache.access()},t.prototype.getPage=function(t){var e=this.getPages();return te(t,"index",0,e.length-1),e[t]},t.prototype.getPageIndices=function(){return H(0,this.getPageCount())},t.prototype.removePage=function(t){var e=this.getPageCount();if(0===this.pageCount)throw new Xa;te(t,"index",0,e-1),this.catalog.removeLeafNode(t),this.pageCount=e-1},t.prototype.addPage=function(t){return Qt(t,"page",["undefined",[Js,"PDFPage"],Array]),this.insertPage(this.getPageCount(),t)},t.prototype.insertPage=function(t,e){var r=this.getPageCount();if(te(t,"index",0,r),Qt(e,"page",["undefined",[Js,"PDFPage"],Array]),!e||Array.isArray(e)){var n=Array.isArray(e)?e:Es.A4;(e=Js.create(this)).setSize.apply(e,n)}else if(e.doc!==this)throw new La;var o=this.catalog.insertLeafNode(e.ref,t);return e.node.setParent(o),this.pageMap.set(e.node,e),this.pageCache.invalidate(),this.pageCount=r+1,e},t.prototype.copyPages=function(e,r){return o(this,0,void 0,(function(){var n,o,a,s,u,c,f,h;return i(this,(function(i){switch(i.label){case 0:return Qt(e,"srcDoc",[[t,"PDFDocument"]]),Qt(r,"indices",[Array]),[4,e.flush()];case 1:for(i.sent(),n=gr.for(e.context,this.context),o=e.getPages(),a=new Array(r.length),s=0,u=r.length;s<u;s++)c=o[r[s]],f=n.copy(c.node),h=this.context.register(f),a[s]=Js.of(f,h,this);return[2,a]}}))}))},t.prototype.copy=function(){return o(this,0,void 0,(function(){var e,r,n,o;return i(this,(function(i){switch(i.label){case 0:return[4,t.create()];case 1:return[4,(e=i.sent()).copyPages(this,this.getPageIndices())];case 2:for(r=i.sent(),n=0,o=r.length;n<o;n++)e.addPage(r[n]);return void 0!==this.getAuthor()&&e.setAuthor(this.getAuthor()),void 0!==this.getCreationDate()&&e.setCreationDate(this.getCreationDate()),void 0!==this.getCreator()&&e.setCreator(this.getCreator()),void 0!==this.getModificationDate()&&e.setModificationDate(this.getModificationDate()),void 0!==this.getProducer()&&e.setProducer(this.getProducer()),void 0!==this.getSubject()&&e.setSubject(this.getSubject()),void 0!==this.getTitle()&&e.setTitle(this.getTitle()),e.defaultWordBreaks=this.defaultWordBreaks,[2,e]}}))}))},t.prototype.addJavaScript=function(t,e){Qt(t,"name",["string"]),Qt(e,"script",["string"]);var r=Hs.for(e,t),n=this.context.nextRef(),o=Xs.of(n,this,r);this.javaScripts.push(o)},t.prototype.attach=function(t,e,r){return void 0===r&&(r={}),o(this,0,void 0,(function(){var n,o,a,s;return i(this,(function(i){return Qt(t,"attachment",["string",Uint8Array,ArrayBuffer]),Qt(e,"name",["string"]),_t(r.mimeType,"mimeType",["string"]),_t(r.description,"description",["string"]),_t(r.creationDate,"options.creationDate",[Date]),_t(r.modificationDate,"options.modificationDate",[Date]),Xt(r.afRelationship,"options.afRelationship",Sr),n=J(t),o=Ir.for(n,e,r),a=this.context.nextRef(),s=Ls.of(a,this,o),this.embeddedFiles.push(s),[2]}))}))},t.prototype.embedFont=function(t,e){return void 0===e&&(e={}),o(this,0,void 0,(function(){var r,n,o,a,s,u,c,f,h,l;return i(this,(function(i){switch(i.label){case 0:return r=e.subset,n=void 0!==r&&r,o=e.customName,a=e.features,Qt(t,"font",["string",Uint8Array,ArrayBuffer]),Qt(n,"subset",["boolean"]),Wt(t)?(s=Pr.for(t,o),[3,7]):[3,1];case 1:return Y(t)?(u=J(t),c=this.assertFontkit(),n?[4,Vr.for(c,u,o,a)]:[3,3]):[3,6];case 2:return f=i.sent(),[3,5];case 3:return[4,Mr.for(c,u,o,a)];case 4:f=i.sent(),i.label=5;case 5:return s=f,[3,7];case 6:throw new TypeError("`font` must be one of `StandardFonts | string | Uint8Array | ArrayBuffer`");case 7:return h=this.context.nextRef(),l=Cs.of(h,this,s),this.fonts.push(l),[2,l]}}))}))},t.prototype.embedStandardFont=function(t,e){if(Qt(t,"font",["string"]),!Wt(t))throw new TypeError("`font` must be one of type `StandardFonts`");var r=Pr.for(t,e),n=this.context.nextRef(),o=Cs.of(n,this,r);return this.fonts.push(o),o},t.prototype.embedJpg=function(t){return o(this,0,void 0,(function(){var e,r,n,o;return i(this,(function(i){switch(i.label){case 0:return Qt(t,"jpg",["string",Uint8Array,ArrayBuffer]),e=J(t),[4,Kr.for(e)];case 1:return r=i.sent(),n=this.context.nextRef(),o=Ts.of(n,this,r),this.images.push(o),[2,o]}}))}))},t.prototype.embedPng=function(t){return o(this,0,void 0,(function(){var e,r,n,o;return i(this,(function(i){switch(i.label){case 0:return Qt(t,"png",["string",Uint8Array,ArrayBuffer]),e=J(t),[4,nn.for(e)];case 1:return r=i.sent(),n=this.context.nextRef(),o=Ts.of(n,this,r),this.images.push(o),[2,o]}}))}))},t.prototype.embedPdf=function(e,r){return void 0===r&&(r=[0]),o(this,0,void 0,(function(){var n,o;return i(this,(function(i){switch(i.label){case 0:return Qt(e,"pdf",["string",Uint8Array,ArrayBuffer,[t,"PDFDocument"]]),Qt(r,"indices",[Array]),e instanceof t?(n=e,[3,3]):[3,1];case 1:return[4,t.load(e)];case 2:n=i.sent(),i.label=3;case 3:return o=Z(n.getPages(),r),[2,this.embedPages(o)]}}))}))},t.prototype.embedPage=function(t,e,r){return o(this,0,void 0,(function(){return i(this,(function(n){switch(n.label){case 0:return Qt(t,"page",[[Js,"PDFPage"]]),[4,this.embedPages([t],[e],[r])];case 1:return[2,n.sent()[0]]}}))}))},t.prototype.embedPages=function(t,e,r){return void 0===e&&(e=[]),void 0===r&&(r=[]),o(this,0,void 0,(function(){var n,o,a,s,u,c,f,h,l,d,p,y,g;return i(this,(function(i){switch(i.label){case 0:if(0===t.length)return[2,[]];for(c=0,f=t.length-1;c<f;c++)if(n=t[c],o=t[c+1],n.node.context!==o.node.context)throw new be;a=t[0].node.context,s=a===this.context?function(t){return t}:gr.for(a,this.context).copy,u=new Array(t.length),c=0,f=t.length,i.label=1;case 1:return c<f?(h=s(t[c].node),l=e[c],d=r[c],[4,Fn.for(h,l,d)]):[3,4];case 2:p=i.sent(),y=this.context.nextRef(),u[c]=Ss.of(y,this,p),i.label=3;case 3:return c++,[3,1];case 4:return(g=this.embeddedPages).push.apply(g,u),[2,u]}}))}))},t.prototype.flush=function(){return o(this,0,void 0,(function(){return i(this,(function(t){switch(t.label){case 0:return[4,this.embedAll(this.fonts)];case 1:return t.sent(),[4,this.embedAll(this.images)];case 2:return t.sent(),[4,this.embedAll(this.embeddedPages)];case 3:return t.sent(),[4,this.embedAll(this.embeddedFiles)];case 4:return t.sent(),[4,this.embedAll(this.javaScripts)];case 5:return t.sent(),[2]}}))}))},t.prototype.save=function(t){return void 0===t&&(t={}),o(this,0,void 0,(function(){var e,r,n,o,a,s,u,c,f;return i(this,(function(i){switch(i.label){case 0:return e=t.useObjectStreams,r=void 0===e||e,n=t.addDefaultPage,o=void 0===n||n,a=t.objectsPerTick,s=void 0===a?50:a,u=t.updateFieldAppearances,c=void 0===u||u,Qt(r,"useObjectStreams",["boolean"]),Qt(o,"addDefaultPage",["boolean"]),Qt(s,"objectsPerTick",["number"]),Qt(c,"updateFieldAppearances",["boolean"]),o&&0===this.getPageCount()&&this.addPage(),c&&(f=this.formCache.getValue())&&f.updateFieldAppearances(),[4,this.flush()];case 1:return i.sent(),[2,(r?kr:Fr).forContext(this.context,s).serializeToBuffer()]}}))}))},t.prototype.saveAsBase64=function(t){return void 0===t&&(t={}),o(this,0,void 0,(function(){var e,r,n,o,a;return i(this,(function(i){switch(i.label){case 0:return e=t.dataUri,r=void 0!==e&&e,n=function(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(t);o<n.length;o++)e.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(r[n[o]]=t[n[o]])}return r}(t,["dataUri"]),Qt(r,"dataUri",["boolean"]),[4,this.save(n)];case 1:return o=i.sent(),a=l(o),[2,r?"data:application/pdf;base64,"+a:a]}}))}))},t.prototype.findPageForAnnotationRef=function(t){for(var e=this.getPages(),r=0,n=e.length;r<n;r++){var o=e[r],i=o.node.Annots();if(void 0!==(null==i?void 0:i.indexOf(t)))return o}},t.prototype.embedAll=function(t){return o(this,0,void 0,(function(){var e,r;return i(this,(function(n){switch(n.label){case 0:e=0,r=t.length,n.label=1;case 1:return e<r?[4,t[e].embed()]:[3,4];case 2:n.sent(),n.label=3;case 3:return e++,[3,1];case 4:return[2]}}))}))},t.prototype.updateInfoDict=function(){var t="pdf-lib (https://github.com/Hopding/pdf-lib)",e=new Date,r=this.getInfoDict();this.setProducer(t),this.setModificationDate(e),r.get(Qe.of("Creator"))||this.setCreator(t),r.get(Qe.of("CreationDate"))||this.setCreationDate(e)},t.prototype.getInfoDict=function(){var t=this.context.lookup(this.context.trailerInfo.Info);if(t instanceof er)return t;var e=this.context.obj({});return this.context.trailerInfo.Info=this.context.register(e),e},t.prototype.assertFontkit=function(){if(!this.fontkit)throw new Ka;return this.fontkit},t}();function Ys(t){if(!(t instanceof Or||t instanceof jr))throw new de([Or,jr],t)}(Ks=Gs||(Gs={})).Normal="Normal",Ks.Multiply="Multiply",Ks.Screen="Screen",Ks.Overlay="Overlay",Ks.Darken="Darken",Ks.Lighten="Lighten",Ks.ColorDodge="ColorDodge",Ks.ColorBurn="ColorBurn",Ks.HardLight="HardLight",Ks.SoftLight="SoftLight",Ks.Difference="Difference",Ks.Exclusion="Exclusion";var Js=function(){function t(t,e,r){this.fontSize=24,this.fontColor=oa(0,0,0),this.lineHeight=24,this.x=0,this.y=0,Qt(t,"leafNode",[[yr,"PDFPageLeaf"]]),Qt(e,"ref",[[ar,"PDFRef"]]),Qt(r,"doc",[[Zs,"PDFDocument"]]),this.node=t,this.ref=e,this.doc=r}return t.prototype.setRotation=function(t){var e=Ko(t);re(e,"degreesAngle",90),this.node.set(Qe.of("Rotate"),this.doc.context.obj(e))},t.prototype.getRotation=function(){var t=this.node.Rotate();return Io(t?t.asNumber():0)},t.prototype.setSize=function(t,e){Qt(t,"width",["number"]),Qt(e,"height",["number"]);var r=this.getMediaBox();this.setMediaBox(r.x,r.y,t,e);var n=this.getCropBox(),o=this.getBleedBox(),i=this.getTrimBox(),a=this.getArtBox(),s=this.node.CropBox(),u=this.node.BleedBox(),c=this.node.TrimBox(),f=this.node.ArtBox();s&&zt(n,r)&&this.setCropBox(r.x,r.y,t,e),u&&zt(o,r)&&this.setBleedBox(r.x,r.y,t,e),c&&zt(i,r)&&this.setTrimBox(r.x,r.y,t,e),f&&zt(a,r)&&this.setArtBox(r.x,r.y,t,e)},t.prototype.setWidth=function(t){Qt(t,"width",["number"]),this.setSize(t,this.getSize().height)},t.prototype.setHeight=function(t){Qt(t,"height",["number"]),this.setSize(this.getSize().width,t)},t.prototype.setMediaBox=function(t,e,r,n){Qt(t,"x",["number"]),Qt(e,"y",["number"]),Qt(r,"width",["number"]),Qt(n,"height",["number"]);var o=this.doc.context.obj([t,e,t+r,e+n]);this.node.set(Qe.MediaBox,o)},t.prototype.setCropBox=function(t,e,r,n){Qt(t,"x",["number"]),Qt(e,"y",["number"]),Qt(r,"width",["number"]),Qt(n,"height",["number"]);var o=this.doc.context.obj([t,e,t+r,e+n]);this.node.set(Qe.CropBox,o)},t.prototype.setBleedBox=function(t,e,r,n){Qt(t,"x",["number"]),Qt(e,"y",["number"]),Qt(r,"width",["number"]),Qt(n,"height",["number"]);var o=this.doc.context.obj([t,e,t+r,e+n]);this.node.set(Qe.BleedBox,o)},t.prototype.setTrimBox=function(t,e,r,n){Qt(t,"x",["number"]),Qt(e,"y",["number"]),Qt(r,"width",["number"]),Qt(n,"height",["number"]);var o=this.doc.context.obj([t,e,t+r,e+n]);this.node.set(Qe.TrimBox,o)},t.prototype.setArtBox=function(t,e,r,n){Qt(t,"x",["number"]),Qt(e,"y",["number"]),Qt(r,"width",["number"]),Qt(n,"height",["number"]);var o=this.doc.context.obj([t,e,t+r,e+n]);this.node.set(Qe.ArtBox,o)},t.prototype.getSize=function(){var t=this.getMediaBox();return{width:t.width,height:t.height}},t.prototype.getWidth=function(){return this.getSize().width},t.prototype.getHeight=function(){return this.getSize().height},t.prototype.getMediaBox=function(){return this.node.MediaBox().asRectangle()},t.prototype.getCropBox=function(){var t,e=this.node.CropBox();return null!==(t=null==e?void 0:e.asRectangle())&&void 0!==t?t:this.getMediaBox()},t.prototype.getBleedBox=function(){var t,e=this.node.BleedBox();return null!==(t=null==e?void 0:e.asRectangle())&&void 0!==t?t:this.getCropBox()},t.prototype.getTrimBox=function(){var t,e=this.node.TrimBox();return null!==(t=null==e?void 0:e.asRectangle())&&void 0!==t?t:this.getCropBox()},t.prototype.getArtBox=function(){var t,e=this.node.ArtBox();return null!==(t=null==e?void 0:e.asRectangle())&&void 0!==t?t:this.getCropBox()},t.prototype.translateContent=function(t,e){Qt(t,"x",["number"]),Qt(e,"y",["number"]),this.node.normalize(),this.getContentStream();var r=this.createContentStream(yi(),ti(t,e)),n=this.doc.context.register(r),o=this.createContentStream(gi()),i=this.doc.context.register(o);this.node.wrapContentStreams(n,i)},t.prototype.scale=function(t,e){Qt(t,"x",["number"]),Qt(e,"y",["number"]),this.setSize(this.getWidth()*t,this.getHeight()*e),this.scaleContent(t,e),this.scaleAnnotations(t,e)},t.prototype.scaleContent=function(t,e){Qt(t,"x",["number"]),Qt(e,"y",["number"]),this.node.normalize(),this.getContentStream();var r=this.createContentStream(yi(),ei(t,e)),n=this.doc.context.register(r),o=this.createContentStream(gi()),i=this.doc.context.register(o);this.node.wrapContentStreams(n,i)},t.prototype.scaleAnnotations=function(t,e){Qt(t,"x",["number"]),Qt(e,"y",["number"]);var r=this.node.Annots();if(r)for(var n=0;n<r.size();n++){var o=r.lookup(n);o instanceof er&&this.scaleAnnot(o,t,e)}},t.prototype.resetPosition=function(){this.getContentStream(!1),this.x=0,this.y=0},t.prototype.setFont=function(t){Qt(t,"font",[[Cs,"PDFFont"]]),this.font=t,this.fontKey=this.node.newFontDictionary(this.font.name,this.font.ref)},t.prototype.setFontSize=function(t){Qt(t,"fontSize",["number"]),this.fontSize=t},t.prototype.setFontColor=function(t){Qt(t,"fontColor",[[Object,"Color"]]),this.fontColor=t},t.prototype.setLineHeight=function(t){Qt(t,"lineHeight",["number"]),this.lineHeight=t},t.prototype.getPosition=function(){return{x:this.x,y:this.y}},t.prototype.getX=function(){return this.x},t.prototype.getY=function(){return this.y},t.prototype.moveTo=function(t,e){Qt(t,"x",["number"]),Qt(e,"y",["number"]),this.x=t,this.y=e},t.prototype.moveDown=function(t){Qt(t,"yDecrease",["number"]),this.y-=t},t.prototype.moveUp=function(t){Qt(t,"yIncrease",["number"]),this.y+=t},t.prototype.moveLeft=function(t){Qt(t,"xDecrease",["number"]),this.x-=t},t.prototype.moveRight=function(t){Qt(t,"xIncrease",["number"]),this.x+=t},t.prototype.pushOperators=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];$t(t,"operator",[[sr,"PDFOperator"]]);var r=this.getContentStream();r.push.apply(r,t)},t.prototype.drawText=function(t,e){var r,n,o,i,a,s,u;void 0===e&&(e={}),Qt(t,"text",["string"]),_t(e.color,"options.color",[[Object,"Color"]]),ee(e.opacity,"opacity.opacity",0,1),_t(e.font,"options.font",[[Cs,"PDFFont"]]),_t(e.size,"options.size",["number"]),_t(e.rotate,"options.rotate",[[Object,"Rotation"]]),_t(e.xSkew,"options.xSkew",[[Object,"Rotation"]]),_t(e.ySkew,"options.ySkew",[[Object,"Rotation"]]),_t(e.x,"options.x",["number"]),_t(e.y,"options.y",["number"]),_t(e.lineHeight,"options.lineHeight",["number"]),_t(e.maxWidth,"options.maxWidth",["number"]),_t(e.wordBreaks,"options.wordBreaks",[Array]),Xt(e.blendMode,"options.blendMode",Gs);for(var c=this.setOrEmbedFont(e.font),f=c.oldFont,h=c.newFont,l=c.newFontKey,d=e.size||this.fontSize,p=e.wordBreaks||this.doc.defaultWordBreaks,y=void 0===e.maxWidth?R(k(t)):M(t,p,e.maxWidth,(function(t){return h.widthOfTextAtSize(t,d)})),g=new Array(y.length),v=0,m=y.length;v<m;v++)g[v]=h.encodeText(y[v]);var b=this.maybeEmbedGraphicsState({opacity:e.opacity,blendMode:e.blendMode}),x=this.getContentStream();x.push.apply(x,ka(g,{color:null!==(r=e.color)&&void 0!==r?r:this.fontColor,font:l,size:d,rotate:null!==(n=e.rotate)&&void 0!==n?n:Io(0),xSkew:null!==(o=e.xSkew)&&void 0!==o?o:Io(0),ySkew:null!==(i=e.ySkew)&&void 0!==i?i:Io(0),x:null!==(a=e.x)&&void 0!==a?a:this.x,y:null!==(s=e.y)&&void 0!==s?s:this.y,lineHeight:null!==(u=e.lineHeight)&&void 0!==u?u:this.lineHeight,graphicsState:b})),e.font&&(f?this.setFont(f):this.resetFont())},t.prototype.drawImage=function(t,e){var r,n,o,i,a,s,u;void 0===e&&(e={}),Qt(t,"image",[[Ts,"PDFImage"]]),_t(e.x,"options.x",["number"]),_t(e.y,"options.y",["number"]),_t(e.width,"options.width",["number"]),_t(e.height,"options.height",["number"]),_t(e.rotate,"options.rotate",[[Object,"Rotation"]]),_t(e.xSkew,"options.xSkew",[[Object,"Rotation"]]),_t(e.ySkew,"options.ySkew",[[Object,"Rotation"]]),ee(e.opacity,"opacity.opacity",0,1),Xt(e.blendMode,"options.blendMode",Gs);var c=this.node.newXObject("Image",t.ref),f=this.maybeEmbedGraphicsState({opacity:e.opacity,blendMode:e.blendMode}),h=this.getContentStream();h.push.apply(h,Oa(c,{x:null!==(r=e.x)&&void 0!==r?r:this.x,y:null!==(n=e.y)&&void 0!==n?n:this.y,width:null!==(o=e.width)&&void 0!==o?o:t.size().width,height:null!==(i=e.height)&&void 0!==i?i:t.size().height,rotate:null!==(a=e.rotate)&&void 0!==a?a:Io(0),xSkew:null!==(s=e.xSkew)&&void 0!==s?s:Io(0),ySkew:null!==(u=e.ySkew)&&void 0!==u?u:Io(0),graphicsState:f}))},t.prototype.drawPage=function(t,e){var r,n,o,i,a;void 0===e&&(e={}),Qt(t,"embeddedPage",[[Ss,"PDFEmbeddedPage"]]),_t(e.x,"options.x",["number"]),_t(e.y,"options.y",["number"]),_t(e.xScale,"options.xScale",["number"]),_t(e.yScale,"options.yScale",["number"]),_t(e.width,"options.width",["number"]),_t(e.height,"options.height",["number"]),_t(e.rotate,"options.rotate",[[Object,"Rotation"]]),_t(e.xSkew,"options.xSkew",[[Object,"Rotation"]]),_t(e.ySkew,"options.ySkew",[[Object,"Rotation"]]),ee(e.opacity,"opacity.opacity",0,1),Xt(e.blendMode,"options.blendMode",Gs);var s=this.node.newXObject("EmbeddedPdfPage",t.ref),u=this.maybeEmbedGraphicsState({opacity:e.opacity,blendMode:e.blendMode}),c=void 0!==e.width?e.width/t.width:void 0!==e.xScale?e.xScale:1,f=void 0!==e.height?e.height/t.height:void 0!==e.yScale?e.yScale:1,h=this.getContentStream();h.push.apply(h,Pa(s,{x:null!==(r=e.x)&&void 0!==r?r:this.x,y:null!==(n=e.y)&&void 0!==n?n:this.y,xScale:c,yScale:f,rotate:null!==(o=e.rotate)&&void 0!==o?o:Io(0),xSkew:null!==(i=e.xSkew)&&void 0!==i?i:Io(0),ySkew:null!==(a=e.ySkew)&&void 0!==a?a:Io(0),graphicsState:u}))},t.prototype.drawSvgPath=function(t,e){var r,n,o,i,a,s,u,c,f;void 0===e&&(e={}),Qt(t,"path",["string"]),_t(e.x,"options.x",["number"]),_t(e.y,"options.y",["number"]),_t(e.scale,"options.scale",["number"]),_t(e.rotate,"options.rotate",[[Object,"Rotation"]]),_t(e.borderWidth,"options.borderWidth",["number"]),_t(e.color,"options.color",[[Object,"Color"]]),ee(e.opacity,"opacity.opacity",0,1),_t(e.borderColor,"options.borderColor",[[Object,"Color"]]),_t(e.borderDashArray,"options.borderDashArray",[Array]),_t(e.borderDashPhase,"options.borderDashPhase",["number"]),Xt(e.borderLineCap,"options.borderLineCap",Vo),ee(e.borderOpacity,"options.borderOpacity",0,1),Xt(e.blendMode,"options.blendMode",Gs);var h=this.maybeEmbedGraphicsState({opacity:e.opacity,borderOpacity:e.borderOpacity,blendMode:e.blendMode});"color"in e||"borderColor"in e||(e.borderColor=oa(0,0,0));var l=this.getContentStream();l.push.apply(l,Ma(t,{x:null!==(r=e.x)&&void 0!==r?r:this.x,y:null!==(n=e.y)&&void 0!==n?n:this.y,scale:e.scale,rotate:null!==(o=e.rotate)&&void 0!==o?o:Io(0),color:null!==(i=e.color)&&void 0!==i?i:void 0,borderColor:null!==(a=e.borderColor)&&void 0!==a?a:void 0,borderWidth:null!==(s=e.borderWidth)&&void 0!==s?s:0,borderDashArray:null!==(u=e.borderDashArray)&&void 0!==u?u:void 0,borderDashPhase:null!==(c=e.borderDashPhase)&&void 0!==c?c:void 0,borderLineCap:null!==(f=e.borderLineCap)&&void 0!==f?f:void 0,graphicsState:h}))},t.prototype.drawLine=function(t){var e,r,n,o,i;Qt(t.start,"options.start",[[Object,"{ x: number, y: number }"]]),Qt(t.end,"options.end",[[Object,"{ x: number, y: number }"]]),Qt(t.start.x,"options.start.x",["number"]),Qt(t.start.y,"options.start.y",["number"]),Qt(t.end.x,"options.end.x",["number"]),Qt(t.end.y,"options.end.y",["number"]),_t(t.thickness,"options.thickness",["number"]),_t(t.color,"options.color",[[Object,"Color"]]),_t(t.dashArray,"options.dashArray",[Array]),_t(t.dashPhase,"options.dashPhase",["number"]),Xt(t.lineCap,"options.lineCap",Vo),ee(t.opacity,"opacity.opacity",0,1),Xt(t.blendMode,"options.blendMode",Gs);var a=this.maybeEmbedGraphicsState({borderOpacity:t.opacity,blendMode:t.blendMode});"color"in t||(t.color=oa(0,0,0));var s=this.getContentStream();s.push.apply(s,Aa({start:t.start,end:t.end,thickness:null!==(e=t.thickness)&&void 0!==e?e:1,color:null!==(r=t.color)&&void 0!==r?r:void 0,dashArray:null!==(n=t.dashArray)&&void 0!==n?n:void 0,dashPhase:null!==(o=t.dashPhase)&&void 0!==o?o:void 0,lineCap:null!==(i=t.lineCap)&&void 0!==i?i:void 0,graphicsState:a}))},t.prototype.drawRectangle=function(t){var e,r,n,o,i,a,s,u,c,f,h,l,d;void 0===t&&(t={}),_t(t.x,"options.x",["number"]),_t(t.y,"options.y",["number"]),_t(t.width,"options.width",["number"]),_t(t.height,"options.height",["number"]),_t(t.rotate,"options.rotate",[[Object,"Rotation"]]),_t(t.xSkew,"options.xSkew",[[Object,"Rotation"]]),_t(t.ySkew,"options.ySkew",[[Object,"Rotation"]]),_t(t.borderWidth,"options.borderWidth",["number"]),_t(t.color,"options.color",[[Object,"Color"]]),ee(t.opacity,"opacity.opacity",0,1),_t(t.borderColor,"options.borderColor",[[Object,"Color"]]),_t(t.borderDashArray,"options.borderDashArray",[Array]),_t(t.borderDashPhase,"options.borderDashPhase",["number"]),Xt(t.borderLineCap,"options.borderLineCap",Vo),ee(t.borderOpacity,"options.borderOpacity",0,1),Xt(t.blendMode,"options.blendMode",Gs);var p=this.maybeEmbedGraphicsState({opacity:t.opacity,borderOpacity:t.borderOpacity,blendMode:t.blendMode});"color"in t||"borderColor"in t||(t.color=oa(0,0,0));var y=this.getContentStream();y.push.apply(y,Ra({x:null!==(e=t.x)&&void 0!==e?e:this.x,y:null!==(r=t.y)&&void 0!==r?r:this.y,width:null!==(n=t.width)&&void 0!==n?n:150,height:null!==(o=t.height)&&void 0!==o?o:100,rotate:null!==(i=t.rotate)&&void 0!==i?i:Io(0),xSkew:null!==(a=t.xSkew)&&void 0!==a?a:Io(0),ySkew:null!==(s=t.ySkew)&&void 0!==s?s:Io(0),borderWidth:null!==(u=t.borderWidth)&&void 0!==u?u:0,color:null!==(c=t.color)&&void 0!==c?c:void 0,borderColor:null!==(f=t.borderColor)&&void 0!==f?f:void 0,borderDashArray:null!==(h=t.borderDashArray)&&void 0!==h?h:void 0,borderDashPhase:null!==(l=t.borderDashPhase)&&void 0!==l?l:void 0,graphicsState:p,borderLineCap:null!==(d=t.borderLineCap)&&void 0!==d?d:void 0}))},t.prototype.drawSquare=function(t){void 0===t&&(t={});var e=t.size;_t(e,"size",["number"]),this.drawRectangle(n(n({},t),{width:e,height:e}))},t.prototype.drawEllipse=function(t){var e,r,n,o,i,a,s,u,c,f,h;void 0===t&&(t={}),_t(t.x,"options.x",["number"]),_t(t.y,"options.y",["number"]),_t(t.xScale,"options.xScale",["number"]),_t(t.yScale,"options.yScale",["number"]),_t(t.rotate,"options.rotate",[[Object,"Rotation"]]),_t(t.color,"options.color",[[Object,"Color"]]),ee(t.opacity,"opacity.opacity",0,1),_t(t.borderColor,"options.borderColor",[[Object,"Color"]]),ee(t.borderOpacity,"options.borderOpacity",0,1),_t(t.borderWidth,"options.borderWidth",["number"]),_t(t.borderDashArray,"options.borderDashArray",[Array]),_t(t.borderDashPhase,"options.borderDashPhase",["number"]),Xt(t.borderLineCap,"options.borderLineCap",Vo),Xt(t.blendMode,"options.blendMode",Gs);var l=this.maybeEmbedGraphicsState({opacity:t.opacity,borderOpacity:t.borderOpacity,blendMode:t.blendMode});"color"in t||"borderColor"in t||(t.color=oa(0,0,0));var d=this.getContentStream();d.push.apply(d,ja({x:null!==(e=t.x)&&void 0!==e?e:this.x,y:null!==(r=t.y)&&void 0!==r?r:this.y,xScale:null!==(n=t.xScale)&&void 0!==n?n:100,yScale:null!==(o=t.yScale)&&void 0!==o?o:100,rotate:null!==(i=t.rotate)&&void 0!==i?i:void 0,color:null!==(a=t.color)&&void 0!==a?a:void 0,borderColor:null!==(s=t.borderColor)&&void 0!==s?s:void 0,borderWidth:null!==(u=t.borderWidth)&&void 0!==u?u:0,borderDashArray:null!==(c=t.borderDashArray)&&void 0!==c?c:void 0,borderDashPhase:null!==(f=t.borderDashPhase)&&void 0!==f?f:void 0,borderLineCap:null!==(h=t.borderLineCap)&&void 0!==h?h:void 0,graphicsState:l}))},t.prototype.drawCircle=function(t){void 0===t&&(t={});var e=t.size,r=void 0===e?100:e;_t(r,"size",["number"]),this.drawEllipse(n(n({},t),{xScale:r,yScale:r}))},t.prototype.setOrEmbedFont=function(t){var e=this.font,r=this.fontKey;return t?this.setFont(t):this.getFont(),{oldFont:e,oldFontKey:r,newFont:this.font,newFontKey:this.fontKey}},t.prototype.getFont=function(){if(!this.font||!this.fontKey){var t=this.doc.embedStandardFont(ks.Helvetica);this.setFont(t)}return[this.font,this.fontKey]},t.prototype.resetFont=function(){this.font=void 0,this.fontKey=void 0},t.prototype.getContentStream=function(t){return void 0===t&&(t=!0),t&&this.contentStream||(this.contentStream=this.createContentStream(),this.contentStreamRef=this.doc.context.register(this.contentStream),this.node.addContentStream(this.contentStreamRef)),this.contentStream},t.prototype.createContentStream=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=this.doc.context.obj({});return hr.of(r,t)},t.prototype.maybeEmbedGraphicsState=function(t){var e=t.opacity,r=t.borderOpacity,n=t.blendMode;if(void 0!==e||void 0!==r||void 0!==n){var o=this.doc.context.obj({Type:"ExtGState",ca:e,CA:r,BM:n});return this.node.newExtGState("GS",o)}},t.prototype.scaleAnnot=function(t,e,r){for(var n=["RD","CL","Vertices","QuadPoints","L","Rect"],o=0,i=n.length;o<i;o++){var a=t.lookup(Qe.of(n[o]));a instanceof Ee&&a.scalePDFNumbers(e,r)}var s=t.lookup(Qe.of("InkList"));if(s instanceof Ee)for(o=0,i=s.size();o<i;o++){var u=s.lookup(o);u instanceof Ee&&u.scalePDFNumbers(e,r)}},t.of=function(e,r,n){return new t(e,r,n)},t.create=function(e){Qt(e,"doc",[[Zs,"PDFDocument"]]);var r=ar.of(-1),n=yr.withContextAndParent(e.context,r);return new t(n,e.context.register(n),e)},t}(),Qs=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return Qt(e,"acroButton",[[Hn,"PDFAcroPushButton"]]),o.acroField=e,o}return r(e,t),e.prototype.setImage=function(t,e){void 0===e&&(e=ns.Center);for(var r=this.acroField.getWidgets(),n=0,o=r.length;n<o;n++){var i=r[n],a=this.createImageAppearanceStream(i,t,e);this.updateWidgetAppearances(i,{normal:a})}this.markAsClean()},e.prototype.setFontSize=function(t){oe(t,"fontSize"),this.acroField.setFontSize(t),this.markAsDirty()},e.prototype.addToPage=function(t,e,r){var n,o,i,a,s,u,c,f,h,l,d;_t(t,"text",["string"]),_t(e,"page",[[Js,"PDFPage"]]),Ps(r);var p=this.createWidget({x:(null!==(n=null==r?void 0:r.x)&&void 0!==n?n:0)-(null!==(o=null==r?void 0:r.borderWidth)&&void 0!==o?o:0)/2,y:(null!==(i=null==r?void 0:r.y)&&void 0!==i?i:0)-(null!==(a=null==r?void 0:r.borderWidth)&&void 0!==a?a:0)/2,width:null!==(s=null==r?void 0:r.width)&&void 0!==s?s:100,height:null!==(u=null==r?void 0:r.height)&&void 0!==u?u:50,textColor:null!==(c=null==r?void 0:r.textColor)&&void 0!==c?c:oa(0,0,0),backgroundColor:null!==(f=null==r?void 0:r.backgroundColor)&&void 0!==f?f:oa(.75,.75,.75),borderColor:null==r?void 0:r.borderColor,borderWidth:null!==(h=null==r?void 0:r.borderWidth)&&void 0!==h?h:0,rotate:null!==(l=null==r?void 0:r.rotate)&&void 0!==l?l:Io(0),caption:t,hidden:null==r?void 0:r.hidden,page:e.ref}),y=this.doc.context.register(p.dict);this.acroField.addWidget(y);var g=null!==(d=null==r?void 0:r.font)&&void 0!==d?d:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(p,g),e.node.addAnnot(y)},e.prototype.needsAppearancesUpdate=function(){var t;if(this.isDirty())return!0;for(var e=this.acroField.getWidgets(),r=0,n=e.length;r<n;r++){if(!((null===(t=e[r].getAppearances())||void 0===t?void 0:t.normal)instanceof rr))return!0}return!1},e.prototype.defaultUpdateAppearances=function(t){Qt(t,"font",[[Cs,"PDFFont"]]),this.updateAppearances(t)},e.prototype.updateAppearances=function(t,e){Qt(t,"font",[[Cs,"PDFFont"]]),_t(e,"provider",[Function]);for(var r=this.acroField.getWidgets(),n=0,o=r.length;n<o;n++){var i=r[n];this.updateWidgetAppearance(i,t,e)}},e.prototype.updateWidgetAppearance=function(t,e,r){var n=hs((null!=r?r:bs)(this,t,e));this.updateWidgetAppearanceWithFont(t,e,n)},e.of=function(t,r,n){return new e(t,r,n)},e}(As);export{Sr as AFRelationship,Tn as AcroButtonFlags,An as AcroChoiceFlags,Sn as AcroFieldFlags,On as AcroTextFlags,lo as AnnotationFlags,Bn as AppearanceCharacteristics,Gs as BlendMode,fe as Cache,se as CharCodes,zi as ColorTypes,ts as CombedTextLayoutError,Se as CorruptPageTreeError,Mr as CustomFontEmbedder,Vr as CustomFontSubsetEmbedder,tn as Duplex,Ga as EncryptedPDFError,es as ExceededMaxLengthError,Ja as FieldAlreadyExistsError,_a as FieldExistsAsNonTerminalError,Ir as FileEmbedder,Ka as FontkitNotRegisteredError,La as ForeignPageError,ns as ImageAlignment,Ce as IndexOutOfBoundsError,Te as InvalidAcroFieldValueError,Qa as InvalidFieldNamePartError,rs as InvalidMaxLengthError,Fe as InvalidPDFDateStringError,we as InvalidTargetIndexError,Kr as JpegEmbedder,Vo as LineCapStyle,ui as LineJoinStyle,he as MethodNotImplementedError,ge as MissingCatalogError,Oe as MissingDAEntryError,Ie as MissingKeywordError,Ya as MissingOnValueCheckError,Ue as MissingPDFHeaderError,ve as MissingPageContentsEmbeddingError,Pe as MissingTfOperatorError,ke as MultiSelectValueError,Ne as NextByteAssertionError,Ha as NoSuchFieldError,Zr as NonFullScreenPageMode,Ae as NumberParsingError,Wn as PDFAcroButton,zn as PDFAcroCheckBox,En as PDFAcroChoice,Gn as PDFAcroComboBox,jn as PDFAcroField,io as PDFAcroForm,Yn as PDFAcroListBox,Kn as PDFAcroNonTerminal,Hn as PDFAcroPushButton,Zn as PDFAcroRadioButton,Ln as PDFAcroSignature,In as PDFAcroTerminal,Xn as PDFAcroText,Vn as PDFAnnotation,Ee as PDFArray,xe as PDFArrayIsNotRectangleError,Ke as PDFBool,Qs as PDFButton,ao as PDFCatalog,Rs as PDFCheckBox,hr as PDFContentStream,pr as PDFContext,vr as PDFCrossRefSection,Tr as PDFCrossRefStream,er as PDFDict,Zs as PDFDocument,Ns as PDFDropdown,Ss as PDFEmbeddedPage,As as PDFField,fr as PDFFlateStream,Cs as PDFFont,Is as PDFForm,We as PDFHeader,Or as PDFHexString,Ts as PDFImage,wr as PDFInvalidObject,je as PDFInvalidObjectParsingError,Xs as PDFJavaScript,Qe as PDFName,_e as PDFNull,qe as PDFNumber,ze as PDFObject,gr as PDFObjectCopier,To as PDFObjectParser,De as PDFObjectParsingError,xr as PDFObjectStream,ko as PDFObjectStreamParser,sr as PDFOperator,$e as PDFOperatorNames,Ds as PDFOptionList,Js as PDFPage,Fn as PDFPageEmbedder,yr as PDFPageLeaf,so as PDFPageTree,Po as PDFParser,Re as PDFParsingError,js as PDFRadioGroup,nr as PDFRawStream,ar as PDFRef,Ms as PDFSignature,rr as PDFStream,Me as PDFStreamParsingError,kr as PDFStreamWriter,jr as PDFString,Vs as PDFTextField,mr as PDFTrailer,br as PDFTrailerDict,Un as PDFWidgetAnnotation,Fr as PDFWriter,Oo as PDFXRefStreamParser,be as PageEmbeddingMismatchedContextError,Es as PageSizes,Bs as ParseSpeeds,nn as PngEmbedder,_r as PrintScaling,le as PrivateConstructorError,Jr as ReadingDirection,Xa as RemovePageFromEmptyDocumentError,ye as ReparseError,$a as RichTextFieldReadError,Ro as RotationTypes,Be as StalledParserError,Pr as StandardFontEmbedder,It as StandardFontValues,ks as StandardFonts,ea as TextAlignment,hi as TextRenderingMode,Ve as UnbalancedParenthesisError,Za as UnexpectedFieldTypeError,de as UnexpectedObjectTypeError,me as UnrecognizedStreamTypeError,pe as UnsupportedEncodingError,Nn as ViewerPreferences,C as addRandomSuffix,Xo as adjustDimsForRotation,mi as appendBezierCurve,bi as appendQuadraticCurve,E as arrayAsString,Mo as asNumber,Do as asPDFName,jo as asPDFNumber,$t as assertEachIs,ne as assertInteger,Qt as assertIs,Lt as assertIsOneOf,Xt as assertIsOneOfOrUndefined,Ht as assertIsSubset,re as assertMultiple,_t as assertOrUndefined,oe as assertPositive,te as assertRange,ee as assertRangeOrUndefined,qt as backtick,$i as beginMarkedContent,Di as beginText,M as breakTextIntoLines,G as byAscendingId,yt as bytesFor,Y as canBeConvertedToUint8Array,D as charAtIndex,x as charFromCode,F as charFromHexCode,j as charSplit,k as cleanText,Zo as clip,Yo as clipEvenOdd,xi as closePath,ia as cmyk,la as colorToComponents,ha as componentsToColor,$o as concatTransformationMatrix,S as copyStringIntoBuffer,Qn as createPDFAcroField,Jn as createPDFAcroFields,Jt as createTypeErrorMsg,Kt as createValueErrorMsg,d as decodeFromBase64,y as decodeFromBase64DataUri,xn as decodePDFRawStream,bs as defaultButtonAppearanceProvider,vs as defaultCheckBoxAppearanceProvider,Fs as defaultDropdownAppearanceProvider,ws as defaultOptionListAppearanceProvider,ms as defaultRadioGroupAppearanceProvider,xs as defaultTextFieldAppearanceProvider,Io as degrees,qo as degreesToRadians,Wa as drawButton,Ua as drawCheckBox,Va as drawCheckMark,ja as drawEllipse,Da as drawEllipsePath,Oa as drawImage,Aa as drawLine,ka as drawLinesOfText,Xi as drawObject,Ea as drawOptionList,Pa as drawPage,Ia as drawRadioButton,Ra as drawRectangle,Ma as drawSvgPath,Ta as drawText,qa as drawTextField,za as drawTextLines,l as encodeToBase64,ta as endMarkedContent,Pi as endPath,ji as endText,gt as error,T as escapeRegExp,O as escapedNewlineChars,ki as fill,Oi as fillAndStroke,U as findLastMatch,Zt as getType,na as grayscale,et as hasSurrogates,lt as hasUtf16BOM,rt as highSurrogate,A as isNewlineChar,Wt as isStandardFont,Yt as isType,tt as isWithinBMP,I as last,cs as layoutCombedText,us as layoutMultilineText,fs as layoutSinglelineText,R as lineSplit,wi as lineTo,nt as lowSurrogate,z as mergeIntoTypedArray,N as mergeLines,q as mergeUint8Arrays,Ri as moveText,Fi as moveTo,P as newlineChars,Ai as nextLine,hs as normalizeAppearance,dt as numberToString,w as padStart,B as parseDate,ce as pdfDocEncodingDecode,Z as pluckIndices,gi as popGraphicsState,yi as pushGraphicsState,Uo as radians,Eo as radiansToDegrees,H as range,Si as rectangle,zt as rectanglesAreEqual,Lo as reduceRotation,si as restoreDashPattern,L as reverseArray,oa as rgb,Li as rotateAndSkewTextDegreesAndTranslate,Ki as rotateAndSkewTextRadiansAndTranslate,ni as rotateDegrees,Ba as rotateInPlace,ri as rotateRadians,Ho as rotateRectangle,ei as scale,Vi as setCharacterSpacing,Ui as setCharacterSqueeze,ai as setDashPattern,Qi as setFillingCmykColor,ca as setFillingColor,Hi as setFillingGrayscaleColor,Yi as setFillingRgbColor,Mi as setFontAndSize,pi as setGraphicsState,fi as setLineCap,Ii as setLineHeight,di as setLineJoin,vi as setLineWidth,_i as setStrokingCmykColor,fa as setStrokingColor,Zi as setStrokingGrayscaleColor,Ji as setStrokingRgbColor,Gi as setTextMatrix,Ei as setTextRenderingMode,Wi as setTextRise,Bi as setWordSpacing,Ni as showText,Et as singleQuote,pt as sizeInBytes,ii as skewDegrees,oi as skewRadians,K as sortedUniq,Ci as square,Ti as stroke,X as sum,g as toCharCode,v as toCodePoint,Ko as toDegrees,b as toHexString,m as toHexStringOfMinLength,Go as toRadians,J as toUint8Array,ti as translate,W as typedArrayFor,it as utf16Decode,$ as utf16Encode,_ as utf8Encode,Ut as values,Q as waitForTick};
