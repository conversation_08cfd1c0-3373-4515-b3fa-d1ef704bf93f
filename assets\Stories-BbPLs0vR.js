const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./RecentStoryViewer-Bikh4z-C.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./stories-DQ6hTlzb.js","./MyApp-DW5WH4Ub.js","./useCacheState-CAnxkewm.js","./index-CDSnY0KE.js","./EyeOutlined-CNKPohhw.js","./addEventListener-C4tIKh7N.js","./index-DdM4T8-Q.js","./useBreakpoint-CPcdMRfj.js","./index-PbLYNhdQ.js"])))=>i.map(i=>d[i]);
import{b0 as e,b1 as t,b5 as i,aN as o}from"./index-Cak6rALw.js";import{u as s,A as r,S as n,T as a,c as m,e as l,h as d,I as c}from"./MyApp-DW5WH4Ub.js";import p from"./Collection-BYcChfHL.js";import{i as j}from"./icons-DFJmfgN3.js";import{getFirstStoryBucketIdToShow as u,getStoriesBucketsData as h}from"./stories-DQ6hTlzb.js";import{getCacheByKey as x}from"./useCacheState-CAnxkewm.js";import{L as b}from"./index-jrOnBmdW.js";import{I as y}from"./index-CDSnY0KE.js";import"./index-ByRdMNW-.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-PbLYNhdQ.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CC5caqt_.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./addEventListener-C4tIKh7N.js";const f=t((()=>o((()=>import("./RecentStoryViewer-Bikh4z-C.js").then((e=>e.R))),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11]),import.meta.url)),{fallback:c});function v({target:t}){const{ti:o}=s(),c=(null==t?void 0:t.name)+" - Stories";return e.jsx(p,{collectionName:c,fetchNext:async(e=[],i)=>{if(!(null==t?void 0:t.id))return;const{bucketId:o,firstStoryId:s}=await u(null==t?void 0:t.id);if(!o||!s)return[];return await h(o)},renderItem:(s,r)=>{const p=s.seen?e.jsx("span",{children:"👀"}):e.jsx("i",{className:"fa fa-eye-slash",style:{color:"gray"}});return e.jsxs(b.Item,{className:"show-on-hover-trigger",children:[e.jsxs(n,{direction:"vertical",style:{position:"relative"},children:[e.jsx(y,{src:s.thumbnail,fallback:s.image,style:{width:200,height:330,borderRadius:10,objectFit:"cover"},preview:{destroyOnClose:!0,imageRender:()=>e.jsx(f,{story:{bucket_id:s.bucket_id,owner:t},bucketsData:x("Collection.data."+c)||[],index:r}),toolbarRender:()=>null}}),e.jsx(a,{placement:"bottom",title:e.jsxs(e.Fragment,{children:[p," ",null==t?void 0:t.name,s.seen?o({en:" KNOWS you saw",vi:" BIẾT bạn đã xem"}):o({en:" DOESN'T know you saw",vi:" KHÔNG biết bạn đã xem"})]}),children:e.jsxs(m.Text,{children:[p," ",l(s.creation_time)," ",o({vi:"trước",en:"ago"}),e.jsx("br",{}),s.react_count," reactions ",s.reaction]})}),s.caption&&e.jsx(m.Text,{children:s.caption}),s.video&&e.jsx("div",{style:{position:"absolute",top:10,right:10,pointerEvents:"none"},children:j.IGVideo})]}),e.jsx(i,{type:"default",icon:e.jsx("i",{className:"fa-solid fa-up-right-from-square"}),style:{position:"absolute",bottom:10,right:10},className:"show-on-hover-item",target:"_blank",href:d("/stories/"+s.bucket_id+"/"+s.id)})]})},downloadItem:async(e,t)=>({name:e.id+(e.video?".mp4":".jpg"),url:e.video||e.image}),rowKey:e=>e.id,once:!0,header:i=>e.jsx(r,{showIcon:!0,type:"info",message:(null==t?void 0:t.name)+((null==i?void 0:i.length)?o({en:" will not know you see this story",vi:" sẽ không biết bạn đã xem tin"}):o({en:" has no story today",vi:" không có tin nào hôm nay"}))})})}export{v as default};
