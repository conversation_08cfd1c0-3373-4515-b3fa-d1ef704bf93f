import{r as e,b0 as t}from"./index-Cak6rALw.js";import{u as a}from"./MyApp-DW5WH4Ub.js";const n=()=>{const{language:n}=a(),[s,l]=e.useState(!1);return e.useEffect((()=>{l(!0);var e=document.createElement("script");e.setAttribute("src","//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"),document.body.appendChild(e),window.googleTranslateElementInit=()=>{l(!1),new window.google.translate.TranslateElement({pageLanguage:n,autoDisplay:!1},"google_translate_element")}}),[]),t.jsxs("div",{children:[s&&t.jsx("i",{className:"fa fa-spinner fa-spin"}),t.jsx("div",{id:"google_translate_element"})]})};export{n as default};
