const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./Collection-BYcChfHL.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./index-ByRdMNW-.js","./MyApp-DW5WH4Ub.js","./useCacheState-CAnxkewm.js","./react-hotkeys-hook.esm-wjq6pdfC.js","./index-PbLYNhdQ.js","./index-QU7lSj_a.js","./index-CxAc8H5Q.js","./index-Dg3cFar0.js","./Dropdown-BcL-JHCf.js","./PurePanel-BvHjNOhQ.js","./move-ESLFEEsv.js","./index-Bg865k-U.js","./EyeOutlined-CNKPohhw.js","./SearchOutlined--mXbzQ68.js","./index-jrOnBmdW.js","./useBreakpoint-CPcdMRfj.js","./index-C5gzSBcY.js","./List-B6ZMXgC_.js","./index-SRy1SMeO.js","./DownOutlined-B-JcS-29.js","./row-BMfM-of4.js","./Pagination-2X6SDgot.js","./col-CzA09p0P.js","./index-CC5caqt_.js","./core-Cg0rLx7n.js","./window-pPC-ycGO.js","./core-BMtEFOPZ.js","./pdf-CE945Q9Q.js","./useVIP-D5FAMGQQ.js","./sweetalert2.esm.all-BZxvatOx.js","./getIds-DAzc-wzf.js","./index-IxNfZAD-.js"])))=>i.map(i=>d[i]);
import{bl as e,r as t,aN as i,b0 as o,bk as s,bg as n,bn as a,b5 as r,b1 as l}from"./index-Cak6rALw.js";import{u as c,d,t as m,S as h,c as p,T as g,A as u,b as f,I as x}from"./MyApp-DW5WH4Ub.js";import{checkFolderUrl as j}from"./core-Cg0rLx7n.js";import w from"./useCacheState-CAnxkewm.js";import{S as v}from"./Screen-Buq7HJpK.js";import{I as D}from"./index-Bg865k-U.js";import{C as y}from"./index-D7dTP8lW.js";import{L as P}from"./index-jrOnBmdW.js";import{I as b}from"./index-CDSnY0KE.js";import{S as k}from"./index-Bp2s1Wws.js";import{a as F}from"./index-DRXkAFwH.js";import"./window-pPC-ycGO.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./index-IxNfZAD-.js";import"./Dropdown-BcL-JHCf.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./PurePanel-BvHjNOhQ.js";import"./index-SRy1SMeO.js";import"./move-ESLFEEsv.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./addEventListener-C4tIKh7N.js";const _=l((()=>i((()=>import("./Collection-BYcChfHL.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]),import.meta.url)),{fallback:x}),T={[s.GGDrive]:["📕PDF: https://drive.google.com/file/d/..."],[s.Scribd]:["📕 https://www.scribd.com/document/..."]},C={[s.GGDrive]:"https://www.facebook.com/groups/fbaio/posts/****************/",[s.Scribd]:"https://www.facebook.com/groups/fbaio/posts/1607278516593497"};function I(){const{ti:s}=c(),{message:l}=d(),[x,I]=e(),[L,N]=w("DocDownloader.url",""),[S,E]=t.useState(!1),[O,A]=w("videoDownloader.pdfInfo",null),[R,U]=t.useState(!1),[V,B]=w("DocDownloader.pdfPageWidth",1e3),[G,W]=w("DocDownloader.injectText",!1),q=t.useMemo((()=>O?O.pages.map((e=>e.replace(/\&w=\d+/,`&w=${V}`))):[]),[O,V]);t.useEffect((()=>{m("DocDownloader:onLoad")}),[]),t.useEffect((()=>{const e=x.get("url");if(e){const t=setTimeout((()=>{N(e),K(e),x.delete("url"),I(x)}),200);return()=>clearTimeout(t)}}),[x]);const K=async(e=L)=>{const t="DocDownloader:onSearch";m(t),E(!0),l.loading({key:t,content:s({en:"Finding data...",vi:"Đang tìm dữ liệu..."}),duration:0});try{if(e.includes("drive.google.com")){if(!(await j(e,s)))throw new Error(s({en:"Folder not supported",vi:"Chưa hỗ trợ folder"}));const{getGoogleDrivePDFFromURL:o}=await i((async()=>{const{getGoogleDrivePDFFromURL:e}=await import("./core-Cg0rLx7n.js");return{getGoogleDrivePDFFromURL:e}}),__vite__mapDeps([27,1,2,4,28]),import.meta.url),n=await o(e);n&&(A(n),B(Math.min(n.maxPageWidth,1e3)),l.success({key:t,content:s({en:"Download doc success",vi:"Tải tài liệu thành công"})}))}else{if(!e.includes("scribd.com"))throw new Error(s({en:"Unsupported URL",vi:"LINK không được hỗ trợ"}));{const o=(await i((async()=>{const{default:e}=await import("./sweetalert2.esm.all-BZxvatOx.js");return{default:e}}),[],import.meta.url)).default,n=await o.fire({title:s({en:"Scribd will open new tab",vi:"Scribd sẽ mở tab mới"}),text:s({en:"FB AIO will auto scroll to end of document. Then you can Ctrl+P to print the document",vi:"FB AIO sẽ tự động cuộn đến cuối tài liệu. Sau đó bạn có thể Ctrl+P để in tài liệu"}),icon:"info",confirmButtonText:s({en:"Start",vi:"Bắt đầu"}),showCancelButton:!0});l.destroy(t),n.isConfirmed&&i((()=>import("./core-BMtEFOPZ.js")),__vite__mapDeps([29,1,2]),import.meta.url).then((t=>{t.downloadScribdDocument(e)}))}}}catch(o){console.log(o),l.error({key:t,content:s({en:"Download doc fail",vi:"Lỗi tải tài liệu"})+": "+o})}finally{E(!1)}},$=async(e=q)=>{const t="VideoDownloader:downloadPDF";if(!R){U(!0);try{l.loading({key:t,content:s({en:"Loading library...",vi:"Đang tải thư viện..."}),duration:0});const{downloadPDFFromImageUrls:o}=await i((async()=>{const{downloadPDFFromImageUrls:e}=await import("./pdf-CE945Q9Q.js");return{downloadPDFFromImageUrls:e}}),__vite__mapDeps([30,1,2]),import.meta.url);await o({injectText:G,imgUrls:e,contentUrls:e.map((e=>e.replace("/img?","/presspage?"))),fileName:O.title,onProgress:(e,i)=>{l.loading({key:t,content:s({en:"Creating PDF...",vi:"Đang tạo PDF..."})+`${e}/${i}`,duration:0})}}),l.success({key:t,content:s({en:"PDF created",vi:"Tạo PDF thành công"})})}catch(o){console.log(o),l.error({key:t,content:s({en:"Download PDF fail",vi:"Tải PDF lỗi"})+" "+o})}finally{U(!1)}}};return o.jsx(v,{mode:"center",title:s({en:"Document downloader",vi:"Tải tài liệu"})+" 📚",children:o.jsxs(h,{align:"center",direction:"vertical",style:{width:"100%"},children:[o.jsx(D.Search,{placeholder:s({en:"Enter document URL",vi:"Nhập LINK tài liệu"}),size:"large",style:{width:350,maxWidth:"90vw"},value:L,onChange:e=>{var t;return N(null==(t=e.target.value)?void 0:t.trim())},onSearch:()=>K(),enterButton:S?null:o.jsx("i",{className:"fa-solid fa-magnifying-glass"}),loading:S}),(null==q?void 0:q.length)?o.jsxs("div",{style:{position:"relative"},children:[o.jsx(y,{children:o.jsx(_,{initialData:q,collectionName:O.title,pageSize:5,showPagination:!0,downloadItem:(e,t)=>({name:t+".jpg",url:e}),renderItem:(e,t)=>o.jsx(P.Item,{children:o.jsx(b,{src:e,style:{maxWidth:300,maxHeight:300},fallback:"https://placehold.co/300x300",crossOrigin:O.isNeedAuth?"use-credentials":void 0},t)}),rowKey:e=>e,header:()=>o.jsxs(h,{direction:"vertical",children:[o.jsx(u,{type:"success",showIcon:!0,message:s({en:o.jsxs(p.Text,{children:["Support both"," ",o.jsx(g,{title:"Anyone can view",children:o.jsxs(f,{children:[o.jsx("i",{className:"fa-solid fa-globe"})," Public"]})})," ","and"," ",o.jsx(g,{title:"Only your google account can view",children:o.jsxs(f,{children:[o.jsx("i",{className:"fa-solid fa-user"})," Private"]})})," ","shared PDF",o.jsxs(p.Link,{href:"https://www.facebook.com/groups/fbaio/posts/****************/",target:"_blank",children:["See more"," ",o.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})]})]}),vi:o.jsxs(p.Text,{children:["Hỗ trợ cả file PDF chia sẻ"," ",o.jsx(g,{title:"Ai cũng xem được",children:o.jsxs(f,{children:[o.jsx("i",{className:"fa-solid fa-globe"})," Công khai"]})})," ","và"," ",o.jsx(g,{title:"Chỉ tài khoản google của bạn mới xem được",children:o.jsxs(f,{children:[o.jsx("i",{className:"fa-solid fa-user"})," Riêng tư"]})})," ",o.jsxs(p.Link,{href:"https://www.facebook.com/groups/fbaio/posts/****************/",target:"_blank",children:["Xem thêm"," ",o.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})]})]})})}),o.jsx(u,{type:"info",showIcon:!0,message:o.jsxs(o.Fragment,{children:[s({en:"Feature not working? Try ",vi:"Chức năng không hoạt động? Hãy thử "}),o.jsxs(p.Link,{href:"https://www.facebook.com/groups/fbaio/posts/1608246173163398",target:"_blank",children:["Useful Script"," ",o.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})]})]})}),o.jsx(y,{children:o.jsxs(h,{direction:"vertical",style:{width:"100%"},children:[o.jsxs(p.Title,{level:3,style:{margin:0,marginBottom:15},children:[O.title," (",null==q?void 0:q.length," ",s({en:"pages",vi:"trang"}),")"]}),o.jsxs(g,{title:s({en:o.jsxs(o.Fragment,{children:[o.jsx("i",{className:"fa-solid fa-crown",style:{color:"yellow"}})," ","ON: Can search text in PDF",o.jsx("br",{})," OFF: Can only view PDF (Scan version)"]}),vi:o.jsxs(o.Fragment,{children:[o.jsx("i",{className:"fa-solid fa-crown",style:{color:"yellow"}})," ","BẬT: Có thể tìm kiếm văn bản trong PDF",o.jsx("br",{})," TẮT: Chỉ có thể xem PDF (Bản Scan)"]})}),children:[o.jsxs(p.Text,{children:[o.jsx("i",{className:"fa-solid fa-search fa-lg"})," ",s({en:"Support text search",vi:"Hỗ trợ tìm kiếm văn bản"})," ",o.jsx("i",{className:"fa-solid fa-crown",style:{color:"yellow"}}),":"," "]}),o.jsx(k,{checked:G,onChange:async e=>{const{checkVIP:t}=await i((async()=>{const{checkVIP:e}=await import("./useVIP-D5FAMGQQ.js");return{checkVIP:e}}),__vite__mapDeps([31,1,2,4,32,33,14,15,16,34,11]),import.meta.url);e&&!(await t())||W(e)},checkedChildren:s({en:"Yes",vi:"Có"}),unCheckedChildren:s({en:"No",vi:"Không"})})]}),o.jsx(g,{title:s({en:"Larger width will result in sharper images, but the PDF file will be larger (recommend: 1000)",vi:"Lớn hơn sẽ nét hơn, nhưng file PDF nặng hơn (khuyên dùng: 1000)"}),children:o.jsxs(p.Text,{children:[o.jsx("i",{className:"fa-solid fa-ruler fa-lg"})," ",s({en:"Page width",vi:"Chiều rộng trang"}),":"," ",o.jsxs(f,{color:"success",children:[V,"px"]})]})}),o.jsx(F,{defaultValue:V,max:O.maxPageWidth,min:100,step:100,onChangeComplete:e=>B(e),dots:!0,marks:{100:"100px",1e3:"1000px",[O.maxPageWidth]:O.maxPageWidth+"px"},tooltip:{forceRender:!0,formatter:e=>`${e}px`}})]})})]}),downloadOptions:[{key:"pdf",labelFn:e=>s({en:`📕 Download PDF ${e.length} pages`,vi:`📕 Tải PDF ${e.length} trang`}),onClick(e){$(e)}}],headerButtons:()=>o.jsx(r,{loading:R,type:"primary",icon:o.jsx("i",{className:"fa-solid fa-file-pdf"}),onClick:()=>$(),children:s({en:"Download PDF",vi:"Tải PDF"})})})}),o.jsx(r,{type:"text",icon:o.jsx("i",{className:"fa-solid fa-xmark"}),onClick:()=>{N(""),A(null)},style:{position:"absolute",right:5,top:5,zIndex:2}})]}):o.jsx(n,{style:{maxWidth:"90vw"},defaultActiveKey:[0],items:[{label:s({en:"Supported URL formats ?",vi:"Các định dạng LINK được hỗ trợ ?"}),children:Object.entries(T).map((([e,t])=>o.jsxs("div",{children:[o.jsx("i",{className:a[e]})," ",o.jsx(p.Text,{strong:!0,children:C[e]?o.jsx(g,{title:s({en:"Tutorial",vi:"Hướng dẫn"}),children:o.jsxs(p.Link,{href:C[e],target:"_blank",children:[e," ",o.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})]})}):e}),o.jsx("ul",{children:t.map((e=>o.jsx("li",{children:e},e)))})]},e)))}]})]})})}export{C as TutorialPostURLs,I as default};
