import{r as e,l as t,T as a,n,m as l,a4 as r,h as s,d as c,$ as o,w as u,G as i,au as d,at as p,v as f,af as v,bP as m,aG as b,a6 as g,i as h}from"./index-Cak6rALw.js";import{u as C}from"./index-CxAc8H5Q.js";var y=["prefixCls","className","style","checked","disabled","defaultChecked","type","title","onChange"],x=e.forwardRef((function(o,u){var i=o.prefixCls,d=void 0===i?"rc-checkbox":i,p=o.className,f=o.style,v=o.checked,m=o.disabled,b=o.defaultChecked,g=void 0!==b&&b,h=o.type,C=void 0===h?"checkbox":h,x=o.title,O=o.onChange,k=t(o,y),E=e.useRef(null),N=e.useRef(null),j=a(g,{value:v}),w=n(j,2),P=w[0],$=w[1];e.useImperativeHandle(u,(function(){return{focus:function(e){var t;null===(t=E.current)||void 0===t||t.focus(e)},blur:function(){var e;null===(e=E.current)||void 0===e||e.blur()},input:E.current,nativeElement:N.current}}));var M=l(d,p,r(r({},"".concat(d,"-checked"),P),"".concat(d,"-disabled"),m));return e.createElement("span",{className:M,title:x,style:f,ref:N},e.createElement("input",s({},k,{className:"".concat(d,"-input"),ref:E,onChange:function(e){m||("checked"in o||$(e.target.checked),null==O||O({target:c(c({},o),{},{type:C,checked:e.target.checked}),stopPropagation:function(){e.stopPropagation()},preventDefault:function(){e.preventDefault()},nativeEvent:e.nativeEvent}))},disabled:m,checked:!!P,type:C})),e.createElement("span",{className:"".concat(d,"-inner")}))}));function O(e){const t=o.useRef(null),a=()=>{u.cancel(t.current),t.current=null};return[()=>{a(),t.current=u((()=>{t.current=null}))},n=>{t.current&&(n.stopPropagation(),a()),null==e||e(n)}]}const k=o.createContext(null);const E=(t,a)=>{var n;const{prefixCls:r,className:s,rootClassName:c,children:o,indeterminate:u=!1,style:g,onMouseEnter:h,onMouseLeave:y,skipGroup:E=!1,disabled:N}=t,j=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var l=0;for(n=Object.getOwnPropertySymbols(e);l<n.length;l++)t.indexOf(n[l])<0&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(a[n[l]]=e[n[l]])}return a}(t,["prefixCls","className","rootClassName","children","indeterminate","style","onMouseEnter","onMouseLeave","skipGroup","disabled"]),{getPrefixCls:w,direction:P,checkbox:$}=e.useContext(i),M=e.useContext(k),{isFormItemInput:R}=e.useContext(d),V=e.useContext(p),I=null!==(n=(null==M?void 0:M.disabled)||N)&&void 0!==n?n:V,S=e.useRef(j.value),G=e.useRef(null),L=f(a,G);e.useEffect((()=>{null==M||M.registerValue(j.value)}),[]),e.useEffect((()=>{if(!E)return j.value!==S.current&&(null==M||M.cancelValue(S.current),null==M||M.registerValue(j.value),S.current=j.value),()=>null==M?void 0:M.cancelValue(j.value)}),[j.value]),e.useEffect((()=>{var e;(null===(e=G.current)||void 0===e?void 0:e.input)&&(G.current.input.indeterminate=u)}),[u]);const _=w("checkbox",r),q=v(_),[D,H,T]=C(_,q),A=Object.assign({},j);M&&!E&&(A.onChange=(...e)=>{j.onChange&&j.onChange.apply(j,e),M.toggleOption&&M.toggleOption({label:o,value:j.value})},A.name=M.name,A.checked=M.value.includes(j.value));const B=l(`${_}-wrapper`,{[`${_}-rtl`]:"rtl"===P,[`${_}-wrapper-checked`]:A.checked,[`${_}-wrapper-disabled`]:I,[`${_}-wrapper-in-form-item`]:R},null==$?void 0:$.className,s,c,T,q,H),F=l({[`${_}-indeterminate`]:u},m,H),[K,X]=O(A.onClick);return D(e.createElement(b,{component:"Checkbox",disabled:I},e.createElement("label",{className:B,style:Object.assign(Object.assign({},null==$?void 0:$.style),g),onMouseEnter:h,onMouseLeave:y,onClick:K},e.createElement(x,Object.assign({},A,{onClick:X,prefixCls:_,className:F,disabled:I,ref:L})),null!=o&&e.createElement("span",{className:`${_}-label`},o))))},N=e.forwardRef(E);const j=e.forwardRef(((t,a)=>{const{defaultValue:n,children:r,options:s=[],prefixCls:c,className:o,rootClassName:u,style:d,onChange:p}=t,f=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var l=0;for(n=Object.getOwnPropertySymbols(e);l<n.length;l++)t.indexOf(n[l])<0&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(a[n[l]]=e[n[l]])}return a}(t,["defaultValue","children","options","prefixCls","className","rootClassName","style","onChange"]),{getPrefixCls:m,direction:b}=e.useContext(i),[y,x]=e.useState(f.value||n||[]),[O,E]=e.useState([]);e.useEffect((()=>{"value"in f&&x(f.value||[])}),[f.value]);const j=e.useMemo((()=>s.map((e=>"string"==typeof e||"number"==typeof e?{label:e,value:e}:e))),[s]),w=e=>{E((t=>t.filter((t=>t!==e))))},P=e=>{E((t=>[].concat(h(t),[e])))},$=e=>{const t=y.indexOf(e.value),a=h(y);-1===t?a.push(e.value):a.splice(t,1),"value"in f||x(a),null==p||p(a.filter((e=>O.includes(e))).sort(((e,t)=>j.findIndex((t=>t.value===e))-j.findIndex((e=>e.value===t)))))},M=m("checkbox",c),R=`${M}-group`,V=v(M),[I,S,G]=C(M,V),L=g(f,["value","disabled"]),_=s.length?j.map((t=>e.createElement(N,{prefixCls:M,key:t.value.toString(),disabled:"disabled"in t?t.disabled:f.disabled,value:t.value,checked:y.includes(t.value),onChange:t.onChange,className:l(`${R}-item`,t.className),style:t.style,title:t.title,id:t.id,required:t.required},t.label))):r,q=e.useMemo((()=>({toggleOption:$,value:y,disabled:f.disabled,name:f.name,registerValue:P,cancelValue:w})),[$,y,f.disabled,f.name,P,w]),D=l(R,{[`${R}-rtl`]:"rtl"===b},o,u,G,V,S);return I(e.createElement("div",Object.assign({className:D,style:d},L,{ref:a}),e.createElement(k.Provider,{value:q},_)))})),w=N;w.Group=j,w.__ANT_CHECKBOX=!0;export{x as C,w as a,O as u};
