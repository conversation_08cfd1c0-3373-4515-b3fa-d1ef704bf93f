import{Q as e,$ as s,r,q as n}from"./index-Cak6rALw.js";import{ae as t,af as i}from"./MyApp-DW5WH4Ub.js";const c=["xxl","xl","lg","md","sm","xs"],a=()=>{const[,r]=e(),n=(e=>({xs:`(max-width: ${e.screenXSMax}px)`,sm:`(min-width: ${e.screenSM}px)`,md:`(min-width: ${e.screenMD}px)`,lg:`(min-width: ${e.screenLG}px)`,xl:`(min-width: ${e.screenXL}px)`,xxl:`(min-width: ${e.screenXXL}px)`}))((e=>{const s=e,r=[].concat(c).reverse();return r.forEach(((e,n)=>{const t=e.toUpperCase(),i=`screen${t}Min`,c=`screen${t}`;if(!(s[i]<=s[c]))throw new Error(`${i}<=${c} fails : !(${s[i]}<=${s[c]})`);if(n<r.length-1){const e=`screen${t}Max`;if(!(s[c]<=s[e]))throw new Error(`${c}<=${e} fails : !(${s[c]}<=${s[e]})`);const i=`screen${r[n+1].toUpperCase()}Min`;if(!(s[e]<=s[i]))throw new Error(`${e}<=${i} fails : !(${s[e]}<=${s[i]})`)}})),e})(r));return s.useMemo((()=>{const e=new Map;let s=-1,r={};return{responsiveMap:n,matchHandlers:{},dispatch:s=>(r=s,e.forEach((e=>e(r))),e.size>=1),subscribe(n){return e.size||this.register(),s+=1,e.set(s,n),n(r),s},unsubscribe(s){e.delete(s),e.size||this.unregister()},register(){Object.entries(n).forEach((([e,s])=>{const n=({matches:s})=>{this.dispatch(Object.assign(Object.assign({},r),{[e]:s}))},t=window.matchMedia(s);i(t,n),this.matchHandlers[s]={mql:t,listener:n},n(t)}))},unregister(){Object.values(n).forEach((e=>{const s=this.matchHandlers[e];t(null==s?void 0:s.mql,null==s?void 0:s.listener)})),e.clear()}}}),[r])};function o(){const[,e]=r.useReducer((e=>e+1),0);return e}function u(e=!0,s={}){const t=r.useRef(s),i=o(),c=a();return n((()=>{const s=c.subscribe((s=>{t.current=s,e&&i()}));return()=>c.unsubscribe(s)}),[]),t.current}export{o as a,c as r,u};
