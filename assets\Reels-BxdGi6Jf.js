const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./VideoViewer-DSZVlhxl.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./videos-D2LbKcXH.js","./MyApp-DW5WH4Ub.js"])))=>i.map(i=>d[i]);
import{r as e,b0 as i,b1 as s,b5 as r,aN as t}from"./index-Cak6rALw.js";import{u as o,S as a,h as l,c as n,l as d,aQ as m,e as c,s as p,f as j,I as u}from"./MyApp-DW5WH4Ub.js";import h from"./Collection-BYcChfHL.js";import{getUserReels as x}from"./reels-Bl7il3t1.js";import{L as f}from"./index-jrOnBmdW.js";import{I as g}from"./index-CDSnY0KE.js";import"./index-ByRdMNW-.js";import"./useCacheState-CAnxkewm.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-PbLYNhdQ.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CC5caqt_.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./addEventListener-C4tIKh7N.js";const v=s((()=>t((()=>import("./VideoViewer-DSZVlhxl.js")),__vite__mapDeps([0,1,2,3,4]),import.meta.url)),{fallback:u});function b({target:s}){const{ti:t}=o(),u=e.useCallback((async(e=[],i)=>{var r;if(!(null==s?void 0:s.id)||!(null==s?void 0:s.type))return;i=i||(null==(r=null==e?void 0:e[(null==e?void 0:e.length)-1])?void 0:r.cursor)||"";return await x({id:s.id,cursor:i})}),[s]),b=e.useCallback((async e=>({url:e.source,name:e.id+".mp4"})),[]),w=e.useCallback((e=>i.jsx(f.Item,{children:i.jsxs(a,{direction:"vertical",children:[i.jsxs("div",{className:"show-on-hover-trigger",children:[i.jsx(g,{src:e.thumbnail,width:200,height:300,style:{objectFit:"cover",borderRadius:10},preview:{destroyOnClose:!0,imageRender:()=>i.jsx(v,{info:e,style:{maxWidth:"90vw",maxHeight:"90vh"}}),toolbarRender:()=>null}}),i.jsx(r,{type:"default",icon:i.jsx("i",{className:"fa-solid fa-up-right-from-square"}),style:{position:"absolute",bottom:10,right:10},className:"show-on-hover-item",target:"_blank",href:l(e.id)})]}),i.jsx(n.Paragraph,{style:{maxWidth:"150px",wordWrap:"break-word"},onClick:()=>window.open(e.url),children:d(e.description,100)}),i.jsxs(a,{direction:"vertical",size:1,style:{position:"absolute",top:0,left:0,padding:10,paddingBottom:50,width:"100%",background:"linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%)",pointerEvents:"none"},children:[i.jsxs(a,{children:[i.jsx("i",{className:"fa-regular fa-eye"}),e.viewCount]}),i.jsxs(a,{children:[i.jsx("i",{className:"fa-solid fa-film"}),e.length?m(e.length):null]}),i.jsxs(a,{children:[i.jsx("i",{className:"fa-regular fa-clock"}),c(parseInt(e.created_time))]}),e.music?i.jsxs(a,{align:"start",size:4,children:[i.jsx("i",{className:"fa-solid fa-music"})," ",e.music]}):null]})]})})),[]);return i.jsx(h,{collectionName:(null==s?void 0:s.name)+" - Reels",fetchNext:u,renderItem:w,downloadItem:b,getItemCursor:e=>e.cursor,rowKey:e=>e.id,searchPlaceholder:e=>t({en:`Search in ${null==e?void 0:e.length} Reels`,vi:`Tìm kiếm trong ${null==e?void 0:e.length} Reels`}),onSearch:(e,i)=>p(e,i.description+i.music+j(i.created_time))})}export{b as default};
