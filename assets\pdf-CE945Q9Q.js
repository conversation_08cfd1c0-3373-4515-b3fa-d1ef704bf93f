const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./fontkit.es-Cx868p02.js","./index-BK5PnqrR.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./index-hvoKDiJD.js","./MyApp-DW5WH4Ub.js"])))=>i.map(i=>d[i]);
import{aN as t,aM as e,cR as a}from"./index-Cak6rALw.js";async function o({imgUrls:a,contentUrls:o,fileName:c,injectText:d=!0,onProgress:l}){const w=a.length;let h=null;const m=(await t((async()=>{const{default:t}=await import("./fontkit.es-Cx868p02.js");return{default:t}}),__vite__mapDeps([0,1,2,3]),import.meta.url)).default,g=await t((()=>import("./index-hvoKDiJD.js")),__vite__mapDeps([4,1,2,3]),import.meta.url),{PDFDocument:u,PDFName:f,PDFString:p,rgb:_}=g,y=await u.create();y.registerFontkit(m);const A=(await t((async()=>{const{default:t}=await import("./NotoSans-Regular-COT0XZjX.js");return{default:t}}),[],import.meta.url)).default,b=await fetch(A).then((t=>t.arrayBuffer())),P=await y.embedFont(b),{parseSafe:R}=await t((async()=>{const{parseSafe:t}=await import("./MyApp-DW5WH4Ub.js").then((t=>t.aU));return{parseSafe:t}}),__vite__mapDeps([5,2,3]),import.meta.url);for(let t=0;t<w;t++)try{l(t,w);const c=a[t],m=await i(c);console.log(m),h||(h=await r(m));const g=m.startsWith("data:image/webp;base64,"),u=m.startsWith("data:image/jpeg;base64,"),A=g?await s(m):m,b=u?await y.embedJpg(A):await y.embedPng(A),x=y.addPage([h.width,h.height]);if(x.drawImage(b,{x:0,y:0,width:h.width,height:h.height}),o[t]){const a=await e(o[t]),i=R(a.split("\n").at(-1)),[r,s,c,l,w,m]=i;console.log(l);const g=h.width/s;if(d&&(null==l?void 0:l.length)){const t=n(l);for(const e of t)try{const t=e[0],a=e[1],[o,n,i,r]=t.map((t=>t*g));let s=i/P.heightAtSize(i);s*=r/P.widthOfTextAtSize(a,s),x.drawText(a+" ",{x:n,y:h.height-o-s,size:s,font:P,color:_(1,1,1),opacity:0})}catch(E){console.error(E)}}if(d&&(null==m?void 0:m.length)){console.log(m);const t=h.height,e=[];for(const a of m){const[o,n,i,r]=a[1].map((t=>Math.round(t*g))),s=[n,t-o-i,n+r,t-o],c=x.doc.context.register(x.doc.context.obj({Type:"Annot",Subtype:"Link",Rect:s,Border:[0,0,0],A:{Type:"Action",S:"URI",URI:p.of(a[3])}}));e.push(c),console.log(g,s,a[3])}x.node.set(f.of("Annots"),y.context.obj(e))}}}catch(E){console.error("downloadPDFFromImageUrls",t,E)}const x=await y.save(),I=new Blob([x],{type:"application/pdf"}),j=URL.createObjectURL(I),{download:D}=await t((async()=>{const{download:t}=await import("./download-KJKGHAGZ.js");return{download:t}}),[],import.meta.url);D(j,c)}function n(t){const e=[];for(const a of t)"string"==typeof a[1]?e.push(a):Array.isArray(a[1])&&e.push(...n(a[1]));return e}async function i(t){return(await a(t)).body}async function r(t){const e=new Image;return e.src=t,await e.decode(),{width:e.width,height:e.height}}function s(t){return new Promise((e=>{const a=new Image;a.onload=()=>{const t=document.createElement("canvas");t.width=a.width,t.height=a.height;t.getContext("2d").drawImage(a,0,0);const o=t.toDataURL("image/png");e(o),a.remove(),t.remove()},a.src=t}))}export{o as downloadPDFFromImageUrls};
