import{r as e,b0 as t,b5 as o}from"./index-Cak6rALw.js";import{createComment as n}from"./comments-BafLCvtq.js";import{getPostIdFromUrl as s}from"./getIds-DAzc-wzf.js";import i from"./MyTable-DnjDmN7z.js";import r from"./useCacheState-CAnxkewm.js";import{u as m,d as a,S as d,v as l}from"./MyApp-DW5WH4Ub.js";import{C as p}from"./col-CzA09p0P.js";import{D as c}from"./index-CC5caqt_.js";import{C as j}from"./index-D7dTP8lW.js";import{I as x}from"./index-Bg865k-U.js";import{T as h}from"./index-BzMPigdO.js";import"./videos-D2LbKcXH.js";import"./index-ByRdMNW-.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./DownOutlined-B-JcS-29.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./row-BMfM-of4.js";import"./EyeOutlined-CNKPohhw.js";import"./index-IxNfZAD-.js";function u(){const{ti:u}=m(),{message:f}=a(),[g,C]=r("CommentToPost.postID",""),[v,I]=r("CommentToPost.comment",""),[w,P]=r("CommentToPost.maxComment",100),[T,y]=r("CommentToPost.waitTime",1e3),[D,b]=r("CommentToPost.commentHistory",[]),[L,k]=r("CommentToPost.running",!1),[F,U]=e.useState(!1),M=async()=>{if(L||!g||!v)return k(!1);k(!0);const e=[...D];for(;e.length<w;){const t=null==v?void 0:v.replace("{i}",e.length+1+""),o=await n({postID:g,comment:t});if(await l(T),o){const n={index:e.length,time:Date.now(),comment:t,postID:g,url:o};e.unshift(n),b((e=>[n,...e])),f.success({content:"Commented "+t})}else f.error({content:"Failed to comment"})}k(!1)};return t.jsxs(p,{children:[t.jsx(j,{title:u({en:"Config",vi:"Cấu hình"}),children:t.jsxs(d,{direction:"vertical",children:[t.jsx("span",{children:u({en:"Enter Post URL",vi:"Nhập URL bài viết"})}),t.jsx(x,{placeholder:"Post URL/ID",value:g,disabled:F,onChange:async e=>{C(e.target.value);const t="CommentToPost:getPostIdFromUrl";U(!0),f.loading({key:t,content:"Getting post ID from URL...",duration:0});const o=await s(e.target.value);o?(C(o),f.success({key:t,content:"Found post ID: "+o})):(C(""),f.error({key:t,content:"Failed to get post ID"})),U(!1)}}),t.jsx("span",{children:"Comment:"}),t.jsx(x.TextArea,{placeholder:"Comment",value:v,onChange:e=>I(e.target.value)}),t.jsx("span",{children:"Max comment:"}),t.jsx(h,{placeholder:"Max comment",min:0,step:1,value:w,onChange:e=>P(e)}),t.jsx("span",{children:"Wait time:"}),t.jsx(h,{placeholder:"Wait time",min:500,max:1e4,step:100,value:T,onChange:e=>y(e)}),t.jsx(o,{type:"primary",disabled:!g||!v,onClick:M,icon:t.jsx("i",{className:"fa-solid fa-plus"}),children:u({en:"Add job",vi:"Thêm công việc"})})]})}),t.jsx(c,{}),t.jsx("h2",{children:"Comment history"}),t.jsx(i,{columns:[{title:"#",dataIndex:"index",width:50},{title:"Time",dataIndex:"time",render:e=>t.jsx(t.Fragment,{children:new Date(e).toLocaleString()}),width:150},{title:"Comment",dataIndex:"comment",width:200},{title:"URL",dataIndex:"url",render:e=>t.jsx("a",{href:e,children:t.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})}),width:50}],data:D,searchable:!0})]})}export{u as default};
