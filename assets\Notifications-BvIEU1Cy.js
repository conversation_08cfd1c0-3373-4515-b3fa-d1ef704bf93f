const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./MyApp-DW5WH4Ub.js","./index-Cak6rALw.js","./index-DIX6R8z-.css"])))=>i.map(i=>d[i]);
import{r as e,b0 as i,b5 as s,aN as t}from"./index-Cak6rALw.js";import{d as l,u as n,r as o,B as a,S as r,c,f as d,e as m}from"./MyApp-DW5WH4Ub.js";import{P as h}from"./index-PbLYNhdQ.js";import{L as j}from"./index-jrOnBmdW.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./PurePanel-BvHjNOhQ.js";import"./index-SRy1SMeO.js";import"./move-ESLFEEsv.js";import"./DownOutlined-B-JcS-29.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";function x(e){switch(e){case"success":return i.jsx("i",{className:"fa-solid fa-circle-check fa-lg",style:{color:"lightgreen"}});case"info":return i.jsx("i",{className:"fa-solid fa-circle-info fa-lg",style:{color:"lightblue"}});case"warning":return i.jsx("i",{className:"fa-solid fa-circle-exclamation fa-lg",style:{color:"orange"}});case"error":return i.jsx("i",{className:"fa-solid fa-circle-xmark fa-lg",style:{color:"red"}});default:return null}}function p(){const{notifications:p,clear:f,remove:u}=l(),{ti:g}=n(),[y,v]=e.useState(!1),w=e.useMemo((()=>o(p)),[p]);return i.jsx(h,{trigger:"click",placement:"bottomRight",title:(null==p?void 0:p.length)?null:g({en:"Notification",vi:"Thông báo"}),onOpenChange:e=>{v(e),e&&t((()=>import("./MyApp-DW5WH4Ub.js").then((e=>e.aV))),__vite__mapDeps([0,1,2]),import.meta.url).then((e=>{e.trackEvent("Notifications:open")}))},content:i.jsxs(i.Fragment,{children:[(null==p?void 0:p.length)?i.jsxs(r,{align:"end",style:{width:"100%",justifyContent:"space-between",alignItems:"center",marginBottom:10},children:[i.jsx(c.Title,{level:5,style:{margin:0},children:(null==p?void 0:p.length)+g({en:" Notifications",vi:" Thông báo"})}),i.jsx(s,{icon:i.jsx("i",{className:"fa-solid fa-eraser"}),onClick:f,children:g({en:"Clear",vi:"Xóa"})})]}):null,y?i.jsx("div",{style:{maxHeight:"70vh",overflowY:"auto",maxWidth:"85vw"},children:i.jsx(j,{dataSource:w,renderItem:(e,t)=>{var l;return i.jsxs(r,{align:"start",style:{paddingTop:10,width:"100%"},size:5,className:"show-on-hover-trigger",children:[x(e.type),i.jsxs(r,{direction:"vertical",size:0,style:{minWidth:"11ch"},children:[i.jsx(c.Text,{children:m(e.time)}),d(e.time).split(" ").map((e=>i.jsx(c.Text,{type:"secondary",children:e},e)))]}),i.jsxs(r,{direction:"vertical",size:0,style:{maxWidth:400},children:[i.jsx(c.Text,{style:{fontWeight:"bold"},children:e.message}),null==(l=e.description)?void 0:l.split("\n").map(((e,s)=>i.jsx(c.Text,{type:"secondary",children:e},t+"_"+s)))]}),i.jsx(s,{className:"show-on-hover-item",icon:i.jsx("i",{className:"fa-solid fa-xmark"}),onClick:()=>u(e.id),style:{position:"absolute",top:0,right:0}})]})},rowKey:e=>e.id,style:{maxWidth:500,minWidth:250}})}):null]}),children:i.jsx(a,{count:(null==p?void 0:p.length)||0,overflowCount:9,style:{color:"white"},children:i.jsx(s,{icon:i.jsx("i",{className:"fa-solid fa-bell"})})})})}export{p as default};
