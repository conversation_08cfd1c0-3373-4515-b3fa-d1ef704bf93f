const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./ArchivedStoryViewer-BpKctZ3s.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./MyApp-DW5WH4Ub.js","./useCacheState-CAnxkewm.js","./stories-DQ6hTlzb.js","./index-CDSnY0KE.js","./EyeOutlined-CNKPohhw.js","./addEventListener-C4tIKh7N.js","./index-DdM4T8-Q.js","./useBreakpoint-CPcdMRfj.js","./index-PbLYNhdQ.js","./index-IxNfZAD-.js","./Dropdown-BcL-JHCf.js"])))=>i.map(i=>d[i]);
import{r as e,b0 as t,b1 as o,b5 as i,bi as s,aN as r}from"./index-Cak6rALw.js";import{u as n,d as a,t as l,S as d,c,f as m,p,h as j,w as u,v as x,I as h}from"./MyApp-DW5WH4Ub.js";import g from"./Collection-BYcChfHL.js";import{getCacheByKey as f,setCacheByKey as v}from"./useCacheState-CAnxkewm.js";import{getArchivedStories as y,getArchivedStoriesContent as b,deleteArchivedStory as w}from"./stories-DQ6hTlzb.js";import{u as k}from"./useAction-uz-MrZwJ.js";import{u as _}from"./useDevMode-yj0U7_8D.js";import{L as S}from"./index-jrOnBmdW.js";import{I as D}from"./index-CDSnY0KE.js";import"./index-ByRdMNW-.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-PbLYNhdQ.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CC5caqt_.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./useVIP-D5FAMGQQ.js";import"./sweetalert2.esm.all-BZxvatOx.js";import"./getIds-DAzc-wzf.js";import"./index-IxNfZAD-.js";import"./addEventListener-C4tIKh7N.js";const T=o((()=>r((()=>import("./ArchivedStoryViewer-BpKctZ3s.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url)),{fallback:h});function C(){const{ti:o}=n(),{message:r}=a(),{onClickAction:h,onClickBulkActions:C}=k(),{devMode:A}=_();e.useEffect((()=>{l("ArchivedStories:onLoad")}),[]);const N=async(e,t,i=!1)=>h({record:e,id:e.id,key:"ArchivedStories.deleteStory",actionFn:()=>A?x(1e3):w(e.id),loadingText:()=>o({en:"Deleting story...",vi:"Đang xóa story..."})+e.creation_time,successText:()=>o({en:"Story deleted. ",vi:"Đã xóa story. "})+e.creation_time,failedText:()=>o({en:"Failed to delete story. ",vi:"Chưa xóa được story. "})+e.creation_time,onSuccess:()=>{t((t=>s(t,(t=>{const o=t.find((t=>t.id===e.id));return o&&(o.deleted=!0),t}))))},needConfirm:i,confirmProps:{title:o({en:"Delete this story?",vi:"Xoá story này?"})}});return t.jsx(g,{collectionName:"Archived Stories",fetchNext:async(e,t)=>{var o;return await y(t||(null==(o=null==e?void 0:e[e.length-1])?void 0:o.cursor)||"")},renderItem:(e,s,r)=>{const n=e.deleted;return t.jsxs(S.Item,{className:"show-on-hover-trigger",children:[t.jsx(D,{src:e.thumbnail,style:{width:160,height:270,borderRadius:10,objectFit:"cover"},preview:!n&&{destroyOnClose:!0,toolbarRender:()=>null,imageRender:()=>t.jsx(T,{story:e})}}),t.jsxs(d,{direction:"vertical",style:{background:"linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%)",position:"absolute",bottom:0,left:0,width:"100%",padding:"5px 10px",paddingTop:"50px",pointerEvents:"none"},children:[n&&t.jsx(c.Text,{style:{color:"red",fontWeight:"bold",backgroundColor:"#3339",padding:5,fontSize:"1.2em",width:"100%"},children:o({en:"DELETED",vi:"ĐÃ XOÁ"})}),t.jsxs(c.Text,{style:{color:"#ccc"},children:[t.jsxs("span",{children:[t.jsx("i",{className:"fa fa-eye"})," ",e.seen_count]}),t.jsx("br",{}),t.jsxs("span",{children:[t.jsx("i",{className:"fa fa-clock"})," ",p((new Date).getTime()-new Date(e.creation_time).getTime())]}),t.jsx("br",{}),m(new Date(e.creation_time))]})]}),!n&&t.jsxs(d,{className:"show-on-hover-item",style:{position:"absolute",top:10,right:10},size:3,children:[t.jsx(i,{danger:!0,type:"default",icon:t.jsx("i",{className:"fa-solid fa-trash-can"}),onClick:()=>N(e,r,!0)}),t.jsx(i,{type:"default",icon:t.jsx("i",{className:"fa-solid fa-up-right-from-square"}),target:"_blank",href:j("stories/?card_id="+e.id)})]})]})},downloadItem:async e=>{const t="ArchivedStoryViewer.stories."+e.creation_time,o=f(t)||await b({creationTime:e.creation_time});v(t,o);const i=o.find((t=>t.id===e.id)),s=u(new Date(e.creation_time));return[{url:(null==i?void 0:i.video)||(null==i?void 0:i.image)||"",name:s+((null==i?void 0:i.video)?".mp4":".jpg")},{url:(null==i?void 0:i.image_background)||"",name:s+"_bg.jpg"}].filter((e=>null==e?void 0:e.url))},headerButtons:e=>t.jsx(i,{onClick:()=>{window.open("https://www.facebook.com/me/allactivity?log_filter=archivedstories","_blank")},icon:t.jsx("i",{className:"fa fa-external-link-alt"}),children:o({vi:"Xem trên Facebook",en:"View on Facebook"})},"view-fb"),getItemCursor:e=>null==e?void 0:e.cursor,rowKey:e=>e.id,downloadOptions:[{key:"delete-story",labelFn:e=>{const t=e.filter((e=>!e.deleted)).length;return o({en:`🗑️ Delete ${t} story`,vi:`🗑️ Xoá ${t} story`})},onClick:async(e,t)=>{const i=e.filter((e=>!e.deleted));return console.log(i),i.length?C({key:"ArchivedStories.deleteSelectedStories",data:i,actionFn:e=>N(e,t,!1),loadingText:()=>o({en:"Deleting stories...",vi:"Đang xóa story..."}),successText:()=>o({en:"Stories deleted!",vi:"Xóa story xong!"}),confirmProps:{title:o({en:"Delete "+i.length+" stories?",vi:"Xoá "+i.length+" story?"}),text:o({en:"This action can not be undo",vi:"Không thể hoàn tác"})}}):r.error(o({en:"There is no story to delete",vi:"Không còn story nào cần xoá"}))}}]})}export{C as default};
