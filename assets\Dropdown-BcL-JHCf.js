import{r as e,w as t,a3 as o,v as n,g as r,$ as i,s as a,l as s,n as l,m as f,h as c,a4 as u}from"./index-Cak6rALw.js";import{U as p}from"./MyApp-DW5WH4Ub.js";var v=o.ESC,m=o.TAB;var d=e.forwardRef((function(t,o){var s=t.overlay,l=t.arrow,f=t.prefixCls,c=e.useMemo((function(){return"function"==typeof s?s():s}),[s]),u=n(o,r(c));return i.createElement(i.Fragment,null,l&&i.createElement("div",{className:"".concat(f,"-arrow")}),i.cloneElement(c,{ref:a(c)?u:void 0}))})),g={adjustX:1,adjustY:1},w=[0,0],h={topLeft:{points:["bl","tl"],overflow:g,offset:[0,-4],targetOffset:w},top:{points:["bc","tc"],overflow:g,offset:[0,-4],targetOffset:w},topRight:{points:["br","tr"],overflow:g,offset:[0,-4],targetOffset:w},bottomLeft:{points:["tl","bl"],overflow:g,offset:[0,4],targetOffset:w},bottom:{points:["tc","bc"],overflow:g,offset:[0,4],targetOffset:w},bottomRight:{points:["tr","br"],overflow:g,offset:[0,4],targetOffset:w}},b=["arrow","prefixCls","transitionName","animation","align","placement","placements","getPopupContainer","showAction","hideAction","overlayClassName","overlayStyle","visible","trigger","autoFocus","overlay","children","onVisibleChange"];function y(o,g){var w,y=o.arrow,C=void 0!==y&&y,R=o.prefixCls,A=void 0===R?"rc-dropdown":R,E=o.transitionName,N=o.animation,O=o.align,x=o.placement,P=void 0===x?"bottomLeft":x,k=o.placements,V=void 0===k?h:k,F=o.getPopupContainer,L=o.showAction,M=o.hideAction,S=o.overlayClassName,j=o.overlayStyle,T=o.visible,W=o.trigger,D=void 0===W?["hover"]:W,B=o.autoFocus,H=o.overlay,I=o.children,U=o.onVisibleChange,X=s(o,b),Y=i.useState(),$=l(Y,2),q=$[0],z=$[1],G="visible"in o?T:q,J=i.useRef(null),K=i.useRef(null),Q=i.useRef(null);i.useImperativeHandle(g,(function(){return J.current}));var Z=function(e){z(e),null==U||U(e)};!function(o){var n=o.visible,r=o.triggerRef,i=o.onVisibleChange,a=o.autoFocus,s=o.overlayRef,l=e.useRef(!1),f=function(){var e,t;n&&(null===(e=r.current)||void 0===e||null===(t=e.focus)||void 0===t||t.call(e),null==i||i(!1))},c=function(){var e;return!(null===(e=s.current)||void 0===e||!e.focus||(s.current.focus(),l.current=!0,0))},u=function(e){switch(e.keyCode){case v:f();break;case m:var t=!1;l.current||(t=c()),t?e.preventDefault():f()}};e.useEffect((function(){return n?(window.addEventListener("keydown",u),a&&t(c,3),function(){window.removeEventListener("keydown",u),l.current=!1}):function(){l.current=!1}}),[n])}({visible:G,triggerRef:Q,onVisibleChange:Z,autoFocus:B,overlayRef:K});var _,ee,te,oe=function(){return i.createElement(d,{ref:K,overlay:H,prefixCls:A,arrow:C})},ne=i.cloneElement(I,{className:f(null===(w=I.props)||void 0===w?void 0:w.className,G&&(_=o.openClassName,void 0!==_?_:"".concat(A,"-open"))),ref:a(I)?n(Q,r(I)):void 0}),re=M;return re||-1===D.indexOf("contextMenu")||(re=["click"]),i.createElement(p,c({builtinPlacements:V},X,{prefixCls:A,ref:J,popupClassName:f(S,u({},"".concat(A,"-show-arrow"),C)),popupStyle:j,action:D,showAction:L,hideAction:re,popupPlacement:P,popupAlign:O,popupTransitionName:E,popupAnimation:N,popupVisible:G,stretch:(ee=o.minOverlayWidthMatchTrigger,te=o.alignPoint,("minOverlayWidthMatchTrigger"in o?ee:!te)?"minWidth":""),popup:"function"==typeof H?oe:oe(),onPopupVisibleChange:Z,onPopupClick:function(e){var t=o.onOverlayClick;z(!1),t&&t(e)},getPopupContainer:F}),ne)}const C=i.forwardRef(y);export{C as D};
