const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./VideoViewer-DSZVlhxl.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./videos-D2LbKcXH.js","./MyApp-DW5WH4Ub.js"])))=>i.map(i=>d[i]);
import{b0 as i,b1 as e,aN as t}from"./index-Cak6rALw.js";import{getMediasetContent as o}from"./posts-Fn0UXLRN.js";import r from"./Collection-BYcChfHL.js";import{B as s,I as n}from"./MyApp-DW5WH4Ub.js";import{L as m}from"./index-jrOnBmdW.js";import{I as d}from"./index-CDSnY0KE.js";import"./videos-D2LbKcXH.js";import"./index-ByRdMNW-.js";import"./useCacheState-CAnxkewm.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-PbLYNhdQ.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CC5caqt_.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./addEventListener-C4tIKh7N.js";const a=e((()=>t((()=>import("./VideoViewer-DSZVlhxl.js")),__vite__mapDeps([0,1,2,3,4]),import.meta.url)),{fallback:n});function p({name:e,token:n}){if(!n)return null;return i.jsx(r,{collectionName:e+" - MediaSet",fetchNext:async(i=[],e)=>{var t;if(!n)return;const r=e||(null==(t=i[i.length-1])?void 0:t.cursor);return await o(n,r)},renderItem:e=>i.jsx(m.Item,{style:{position:"relative"},children:e.isVideo?i.jsx(s.Ribbon,{text:"Video",children:i.jsx(d,{src:e.image,width:200/e.height*e.width,height:200,style:{objectFit:"cover",borderRadius:10},preview:e.isVideo?{destroyOnClose:!0,imageRender:()=>i.jsx(a,{info:{id:e.id}}),toolbarRender:()=>null}:void 0})}):i.jsx(d,{src:e.image,width:200/e.height*e.width,height:200,style:{objectFit:"cover",borderRadius:10}})}),downloadItem:async i=>{if(i.isVideo){const{getVideoInfo:e}=await t((async()=>{const{getVideoInfo:i}=await import("./videos-D2LbKcXH.js");return{getVideoInfo:i}}),__vite__mapDeps([3,1,2,4]),import.meta.url),o=await e(i.id);return{name:i.id+".mp4",url:o.source}}return{name:i.id+".jpg",url:i.image}},getItemCursor:i=>i.cursor,rowKey:i=>i.id,stop:!n})}export{p as default};
