import{r as n,b0 as t,aP as e}from"./index-Cak6rALw.js";import{u as i,t as h,c}from"./MyApp-DW5WH4Ub.js";import{S as s}from"./Screen-Buq7HJpK.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";function r(){const{ti:r}=i();return n.useEffect((()=>{h("Disclaimer:onLoad")}),[]),t.jsx(s,{title:r({en:"🤝 Disclaimer & Transparency Commitment",vi:"🤝 <PERSON>yên bố Miễn trừ Trách nhiệm & Cam kết <PERSON> bạch"}),titleLevel:1,mode:"center",children:t.jsxs("div",{style:{maxWidth:600},children:[t.jsx(c.Title,{level:3,children:r({en:"📌 Important Notice for Users:",vi:"📌 Lưu ý quan trọng dành cho người dùng:"})}),t.jsxs("ul",{children:[t.jsx("li",{children:t.jsx(c.Text,{strong:!0,children:r({en:"🛠 This extension is an independent tool developed by a third party. It is not owned, affiliated with, sponsored, or supported by Meta Platforms, Inc. (Facebook, Messenger, Instagram…) or any of its subsidiaries.",vi:"🛠 Tiện ích này là một công cụ độc lập, được phát triển bởi bên thứ ba, không thuộc sở hữu, không liên kết và không được tài trợ hoặc hỗ trợ bởi Meta Platforms, Inc. (Facebook, Messenger, Instagram…) hay bất kỳ công ty con nào của Meta."})})}),t.jsx("li",{children:t.jsx(c.Text,{strong:!0,children:r({en:"🖥 The goal of this tool is to enhance user experience by utilizing publicly available data and legally authorized APIs. It does not interfere with Meta’s internal systems or access users' private data.",vi:"🖥 Mục tiêu của tiện ích là mang lại trải nghiệm tốt hơn cho người dùng thông qua việc sử dụng các dữ liệu công khai và API hợp pháp được cấp quyền, không can thiệp vào hệ thống nội bộ hay dữ liệu cá nhân của Meta."})})}),t.jsx("li",{children:t.jsx(c.Text,{strong:!0,children:r({en:"⚙️ The extension operates transparently, respects user privacy, and fully complies with Facebook’s platform policies and applicable laws.",vi:"⚙️ Tiện ích hoạt động minh bạch, tôn trọng quyền riêng tư và tuân thủ đầy đủ các chính sách, điều khoản sử dụng của nền tảng Facebook và pháp luật hiện hành."})})})]}),t.jsx("h2",{children:r({en:"📜 User Responsibility:",vi:"📜 Trách nhiệm người dùng:"})}),t.jsxs("ul",{children:[t.jsx("li",{children:t.jsx(c.Text,{strong:!0,children:r({en:"👥 Users are responsible for using the extension appropriately and for its intended purposes. We are not liable for any misuse or policy violations committed by users.",vi:"👥 Người dùng có trách nhiệm sử dụng tiện ích một cách hợp lý, đúng mục đích. Chúng tôi không chịu trách nhiệm cho bất kỳ hành vi lạm dụng hoặc vi phạm chính sách nào từ phía người dùng."})})}),t.jsx("li",{children:t.jsx(c.Text,{strong:!0,children:r({en:"🚫 While the development team makes every effort to maintain stability and security, we do not guarantee absolute accuracy, continuous operation, or full compatibility with changes made by Facebook or related platforms.",vi:"🚫 Mặc dù nhóm phát triển luôn nỗ lực duy trì tính ổn định và bảo mật, chúng tôi không đảm bảo tuyệt đối về độ chính xác, tính liên tục hoặc khả năng tương thích hoàn hảo với mọi thay đổi từ phía Facebook hoặc các nền tảng liên quan."})})})]}),t.jsx("h2",{children:r({en:"📢 Support Contact:",vi:"📢 Hỗ trợ & liên hệ:"})}),t.jsx("ul",{children:t.jsx("li",{children:t.jsxs(c.Text,{strong:!0,children:[r({en:"If you need technical assistance or have any questions about the extension, feel free to contact us via: ",vi:"Nếu bạn cần hỗ trợ kỹ thuật hoặc có bất kỳ thắc mắc nào về tiện ích, vui lòng liên hệ trực tiếp qua: "}),t.jsx(c.Link,{href:e.GroupFB,target:"_blank",children:"Group FB"})," / ","Email:"," ",t.jsx(c.Link,{href:"mailto:<EMAIL>",children:"<EMAIL>"})," / ","Phone:"," ",t.jsx(c.Link,{href:"tel:+848765423185",children:"(+84) 765 423 185"})]})})})]})})}export{r as default};
