import{$ as t,m as n,b$ as e,bd as r,aJ as o}from"./index-Cak6rALw.js";import{_ as a,r as i}from"./index-D3qvoGmV.js";function f(t,n){for(var e=0;e<n.length;e++){const r=n[e];if("string"!=typeof r&&!Array.isArray(r))for(const n in r)if("default"!==n&&!(n in t)){const e=Object.getOwnPropertyDescriptor(r,n);e&&Object.defineProperty(t,n,e.get?e:{enumerable:!0,get:()=>r[n]})}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}var u={},l=["top","left","transform","className","children","innerRef"];function c(){return c=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var e=arguments[n];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t},c.apply(this,arguments)}function s(e){var r=e.top,o=void 0===r?0:r,a=e.left,i=void 0===a?0:a,f=e.transform,u=e.className,s=e.children,h=e.innerRef,y=function(t,n){if(null==t)return{};var e,r,o={},a=Object.keys(t);for(r=0;r<a.length;r++)e=a[r],n.indexOf(e)>=0||(o[e]=t[e]);return o}(e,l);return t.createElement("g",c({ref:h,className:n("visx-group",u),transform:f||"translate("+i+", "+o+")"},y),s)}s.propTypes={top:a.number,left:a.number,transform:a.string,className:a.string,children:a.node,innerRef:a.oneOfType([a.string,a.func,a.object])};const h=e(Object.freeze(Object.defineProperty({__proto__:null,Group:s},Symbol.toStringTag,{value:"Module"})));var y={},d={value:function(){}};function p(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r||/[\s.]/.test(t))throw new Error("illegal type: "+t);r[t]=[]}return new v(r)}function v(t){this._=t}function g(t,n){for(var e,r=0,o=t.length;r<o;++r)if((e=t[r]).name===n)return e.value}function x(t,n,e){for(var r=0,o=t.length;r<o;++r)if(t[r].name===n){t[r]=d,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=e&&t.push({name:n,value:e}),t}v.prototype=p.prototype={constructor:v,on:function(t,n){var e,r,o=this._,a=(r=o,(t+"").trim().split(/^|\s+/).map((function(t){var n="",e=t.indexOf(".");if(e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),t&&!r.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:n}}))),i=-1,f=a.length;if(!(arguments.length<2)){if(null!=n&&"function"!=typeof n)throw new Error("invalid callback: "+n);for(;++i<f;)if(e=(t=a[i]).type)o[e]=x(o[e],t.name,n);else if(null==n)for(e in o)o[e]=x(o[e],t.name,null);return this}for(;++i<f;)if((e=(t=a[i]).type)&&(e=g(o[e],t.name)))return e},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new v(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,o=new Array(e),a=0;a<e;++a)o[a]=arguments[a+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(a=0,e=(r=this._[t]).length;a<e;++a)r[a].value.apply(n,o)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],o=0,a=r.length;o<a;++o)r[o].value.apply(n,e)}};const m=e(Object.freeze(Object.defineProperty({__proto__:null,dispatch:p},Symbol.toStringTag,{value:"Module"})));var w,b,_,M;function O(){if(b)return w;b=1;const t=m.dispatch,n=Math.PI/180,e={archimedean:y,rectangular:function(t){var n=4*t[0]/t[1],e=0,r=0;return function(t){var o=t<0?-1:1;switch(Math.sqrt(1+4*o*t)-o&3){case 0:e+=n;break;case 1:r+=4;break;case 2:e-=n;break;default:r-=4}return[e,r]}}},r=2048;function o(t){return t.text}function a(){return"serif"}function i(){return"normal"}function f(t){return Math.sqrt(t.value)}function u(){return 30*(~~(6*random())-3)}function l(){return 1}function c(t,e,o,a){if(!e.sprite){var i=t.context,f=t.ratio;i.clearRect(0,0,2048/f,r/f);var u=0,l=0,c=0,s=o.length;for(--a;++a<s;){e=o[a],i.save(),i.font=e.style+" "+e.weight+" "+~~((e.size+1)/f)+"px "+e.font;const t=i.measureText(e.text),s=-Math.floor(t.width/2);let x=(t.width+1)*f,m=e.size<<1;if(e.rotate){var h=Math.sin(e.rotate*n),y=Math.cos(e.rotate*n),d=x*y,p=x*h,v=m*y,g=m*h;x=Math.max(Math.abs(d+g),Math.abs(d-g))+31>>5<<5,m=~~Math.max(Math.abs(p+v),Math.abs(p-v))}else x=x+31>>5<<5;if(m>c&&(c=m),u+x>=2048&&(u=0,l+=c,c=0),l+m>=r)break;i.translate((u+(x>>1))/f,(l+(m>>1))/f),e.rotate&&i.rotate(e.rotate*n),i.fillText(e.text,s,0),e.padding&&(i.lineWidth=2*e.padding,i.strokeText(e.text,s,0)),i.restore(),e.width=x,e.height=m,e.xoff=u,e.yoff=l,e.x1=x>>1,e.y1=m>>1,e.x0=-e.x1,e.y0=-e.y1,e.hasText=!0,u+=x}for(var x=i.getImageData(0,0,2048/f,r/f).data,m=[];--a>=0;)if((e=o[a]).hasText){for(var w=e.width,b=w>>5,_=e.y1-e.y0,M=0;M<_*b;M++)m[M]=0;if(null==(u=e.xoff))return;l=e.yoff;for(var O=0,j=-1,S=0;S<_;S++){for(M=0;M<w;M++){var k=b*S+(M>>5),z=x[2048*(l+S)+(u+M)<<2]?1<<31-M%32:0;m[k]|=z,O|=z}O?j=S:(e.y0++,_--,S--,l++)}e.y1=e.y0+j,e.sprite=m.slice(0,(e.y1-e.y0)*b)}}}function s(t,n,e){e>>=5;for(var r,o=t.sprite,a=t.width>>5,i=t.x-(a<<4),f=127&i,u=32-f,l=t.y1-t.y0,c=(t.y+t.y0)*e+(i>>5),s=0;s<l;s++){r=0;for(var h=0;h<=a;h++)if((r<<u|(h<a?(r=o[s*a+h])>>>f:0))&n[c+h])return!0;c+=e}return!1}function h(t,n){var e=t[0],r=t[1];n.x+n.x0<e.x&&(e.x=n.x+n.x0),n.y+n.y0<e.y&&(e.y=n.y+n.y0),n.x+n.x1>r.x&&(r.x=n.x+n.x1),n.y+n.y1>r.y&&(r.y=n.y+n.y1)}function y(t){var n=t[0]/t[1];return function(t){return[n*(t*=.1)*Math.cos(t),t*Math.sin(t)]}}function d(){return document.createElement("canvas")}function p(t){return"function"==typeof t?t:function(){return t}}return w=function(){var n=[256,256],v=o,g=a,x=f,m=i,w=i,b=u,_=l,M=y,O=[],j=1/0,S=t("word","end"),k=null,z=Math.random,T={},E=d;function P(t,e,r){n[0],n[1];for(var o,a,i,f,u,l=e.x,c=e.y,h=Math.sqrt(n[0]*n[0]+n[1]*n[1]),y=M(n),d=z()<.5?1:-1,p=-d;(o=y(p+=d))&&(a=~~o[0],i=~~o[1],!(Math.min(Math.abs(a),Math.abs(i))>=h));)if(e.x=l+a,e.y=c+i,!(e.x+e.x0<0||e.y+e.y0<0||e.x+e.x1>n[0]||e.y+e.y1>n[1]||r&&(u=r,!((f=e).x+f.x1>u[0].x&&f.x+f.x0<u[1].x&&f.y+f.y1>u[0].y&&f.y+f.y0<u[1].y))||s(e,t,n[0]))){for(var v,g=e.sprite,x=e.width>>5,m=n[0]>>5,w=e.x-(x<<4),b=127&w,_=32-b,O=e.y1-e.y0,j=(e.y+e.y0)*m+(w>>5),S=0;S<O;S++){v=0;for(var k=0;k<=x;k++)t[j+k]|=v<<_|(k<x?(v=g[S*x+k])>>>b:0);j+=m}return!0}return!1}return T.canvas=function(t){return arguments.length?(E=p(t),T):E},T.start=function(){var t=function(t){const n=t.getContext("2d",{willReadFrequently:!0});t.width=t.height=1;const e=Math.sqrt(n.getImageData(0,0,1,1).data.length>>2);return t.width=2048/e,t.height=r/e,n.fillStyle=n.strokeStyle="red",{context:n,ratio:e}}(E()),e=function(t){var n=[],e=-1;for(;++e<t;)n[e]=0;return n}((n[0]>>5)*n[1]),o=null,a=O.length,i=-1,f=[],u=O.map((function(t,n){return t.text=v.call(this,t,n),t.font=g.call(this,t,n),t.style=m.call(this,t,n),t.weight=w.call(this,t,n),t.rotate=b.call(this,t,n),t.size=~~x.call(this,t,n),t.padding=_.call(this,t,n),t})).sort((function(t,n){return n.size-t.size}));return k&&clearInterval(k),k=setInterval(l,0),l(),T;function l(){for(var r=Date.now();Date.now()-r<j&&++i<a&&k;){var l=u[i];l.x=n[0]*(z()+.5)>>1,l.y=n[1]*(z()+.5)>>1,c(t,l,u,i),l.hasText&&P(e,l,o)&&(f.push(l),S.call("word",T,l),o?h(o,l):o=[{x:l.x+l.x0,y:l.y+l.y0},{x:l.x+l.x1,y:l.y+l.y1}],l.x-=n[0]>>1,l.y-=n[1]>>1)}i>=a&&(T.stop(),S.call("end",T,f,o))}},T.stop=function(){k&&(clearInterval(k),k=null);for(const t of O)delete t.sprite;return T},T.timeInterval=function(t){return arguments.length?(j=null==t?1/0:t,T):j},T.words=function(t){return arguments.length?(O=t,T):O},T.size=function(t){return arguments.length?(n=[+t[0],+t[1]],T):n},T.font=function(t){return arguments.length?(g=p(t),T):g},T.fontStyle=function(t){return arguments.length?(m=p(t),T):m},T.fontWeight=function(t){return arguments.length?(w=p(t),T):w},T.rotate=function(t){return arguments.length?(b=p(t),T):b},T.text=function(t){return arguments.length?(v=p(t),T):v},T.spiral=function(t){return arguments.length?(M=e[t]||t,T):M},T.fontSize=function(t){return arguments.length?(x=p(t),T):x},T.padding=function(t){return arguments.length?(_=p(t),T):_},T.random=function(t){return arguments.length?(z=t,T):z},T.on=function(){var t=S.on.apply(S,arguments);return t===S?T:t},T},w}function j(){if(_)return y;_=1,y.__esModule=!0,y.default=function(t){var r=t.width,o=t.height,a=t.font,i=t.fontSize,f=t.fontStyle,u=t.fontWeight,l=t.padding,c=t.random,s=t.rotate,h=t.spiral,y=t.words,d=(0,n.useState)([]),p=d[0],v=d[1];return(0,n.useEffect)((function(){if(0!==r&&0!==o){var t=(0,e.default)();return t.size([r,o]),t.words(y),void 0!==c&&t.random(c),void 0!==a&&t.font(a),void 0!==l&&t.padding(l),void 0!==i&&t.fontSize(i),void 0!==f&&t.fontStyle(f),void 0!==u&&t.fontWeight(u),void 0!==s&&t.rotate(s),void 0!==h&&t.spiral(h),t.on("end",v),t.start(),function(){t.stop()}}}),[r,o,a,i,f,u,l,c,s,h,y]),p};var t,n=r(),e=(t=O())&&t.__esModule?t:{default:t};return y}function S(){if(M)return u;M=1,u.__esModule=!0,u.default=l;var t=f(i()),n=f(r()),e=h,o=f(j()),a=["children"];function f(t){return t&&t.__esModule?t:{default:t}}function l(t){var r=t.children,i=function(t,n){if(null==t)return{};var e,r,o={},a=Object.keys(t);for(r=0;r<a.length;r++)e=a[r],n.indexOf(e)>=0||(o[e]=t[e]);return o}(t,a),f=i.width,u=i.height,l=(0,o.default)(i);return 0===f||0===u?null:n.default.createElement("svg",{width:f,height:u},n.default.createElement(e.Group,{left:f/2,top:u/2},r(l)))}return l.propTypes={children:t.default.func.isRequired},u}var k=S();const z=f({__proto__:null,default:o(k)},[k]);export{z as W};
