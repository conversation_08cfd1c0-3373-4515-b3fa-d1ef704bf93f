const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./photos-CDfkOCs1.js","./MyApp-DW5WH4Ub.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./videos-D2LbKcXH.js","./posts-Fn0UXLRN.js"])))=>i.map(i=>d[i]);
import{bp as e,aN as t,aM as n}from"./index-Cak6rALw.js";import{y as i,x as r,X as o}from"./MyApp-DW5WH4Ub.js";const a={urlToId:{},videoIdToPostId:{}};async function l(e,t){try{if(a.urlToId[e])return a.urlToId[e];let i=await n(e);if(i)for(const n of t){let t=n.exec(i);if(null==t?void 0:t.length)return a.urlToId[e]=t[0],t[0]}}catch(i){}return null}async function s(n,i=!0){var r,o,a,c,u,f,_,p;if(/^\d+$/.test(n))return n;n.includes("https")||n.includes("/")||(n=`https://fb.com/${n}`);const v=null==(r=/\?profile_id=(\d+)/.exec(n))?void 0:r[1];if(v)return v;const w=null==(o=/\/groups\/(\d+)\/user\/(\d+)/.exec(n))?void 0:o[2];if(w)return w;const m=null==(a=/groups\/(\d+)/.exec(n))?void 0:a[1];if(m)return m;const h=null==(c=/profile.php\?id=(\d+)/.exec(n))?void 0:c[1];if(h)return h;if(n.includes("permalink.php?")){const e=new URLSearchParams(n.split("?")[1]);if(e.has("story_fbid")&&e.has("id"))return e.get("id")}const y=null==(u=/facebook.com\/(.*?)\/posts\//.exec(n))?void 0:u[1];y&&(n=`https://www.facebook.com/${y}`);if(null==(f=/\/groups\/([^\/]+)/.exec(n))?void 0:f[1]){let e=n;return n.includes("/permalink/")&&(e=n.split("/permalink/")[0]),await l(e,[new RegExp('(?<="groupID":")(.\\d+?)(?=")')])}if(n.includes("/photo")){const e=null==(_=/fbid=(\d+)/.exec(n))?void 0:_[1],{getBasicPhotoInfo:i}=await t((async()=>{const{getBasicPhotoInfo:e}=await import("./photos-CDfkOCs1.js");return{getBasicPhotoInfo:e}}),__vite__mapDeps([0,1,2,3]),import.meta.url),r=null==(p=(await i(e)).owner)?void 0:p.id;if(r)return r}if(i){const t=await e(n);if(t&&t!==n)return await s(t,!1)}const g=await l(n,[new RegExp('(?<="userID":")(.\\d+?)(?=")')]);if(g)return g;const I=await d(n);if(I){const{getVideoInfo:e}=await t((async()=>{const{getVideoInfo:e}=await import("./videos-D2LbKcXH.js");return{getVideoInfo:e}}),__vite__mapDeps([4,2,3,1]),import.meta.url),n=await e(I);if(null==n?void 0:n.owner)return n.owner}return null}async function d(n,i=!0){var r,o,a,l,u,f,_;if(/^\d+$/.test(n))return n;const p=null==(r=/share_url=(.*?)v=(\d+)/.exec(n))?void 0:r[2];if(p)return p;const v=null==(o=/\/watch[\/]?\?v=(\d+)/.exec(n))?void 0:o[1];if(v)return v;const w=null==(a=/\/videos\/(\d+)/.exec(n))?void 0:a[1];if(w)return w;const m=null==(l=/\/reel\/(\d+)/.exec(n))?void 0:l[1];if(m)return m;const h=null==(u=/\/watch\/\?(.*?)v=(\d+)/.exec(n))?void 0:u[2];if(h)return h;if(i){const t=await e(n);if(t&&t!==n)return await d(t,!1)}const y=await c(n,!1,!1);if(y){const e=await s(n);if(e){const{getPostContent:n}=await t((async()=>{const{getPostContent:e}=await import("./posts-Fn0UXLRN.js");return{getPostContent:e}}),__vite__mapDeps([5,2,3,1,4]),import.meta.url),i=await n(e,y);if(null==(_=null==(f=i.media)?void 0:f.video)?void 0:_.id)return i.media.video.id}}return null}async function c(t,n=!0,i=!0){var r,o,a,l,s,u,f;if(/^\d+$/.test(t))return t;let v=null==(r=/\/posts\/(\d+)/.exec(t))?void 0:r[1];if(v)return v;let w=null==(o=/\/posts\/(pfbid\w+)/.exec(t))?void 0:o[1];if(w)return await _(w);const m=null==(a=/\/pending_posts\/(\d+)/.exec(t))?void 0:a[1];if(m)return m;if(t.includes("permalink.php?")){const e=new URLSearchParams(t.split("?")[1]);if(e.has("story_fbid")&&e.has("id"))return await _(e.get("story_fbid"))}if(t.includes("multi_permalinks=")){const e=new URLSearchParams(t.split("?")[1]);if(e.has("multi_permalinks"))return e.get("multi_permalinks")}if(t.includes("/permalink/")&&t.includes("/groups/")){const e=null==(l=/\/permalink\/(\d+)/.exec(t))?void 0:l[1];if(e)return e}if(t.includes("/photo/")){const e=new URLSearchParams(t.split("?")[1]);if(e.has("fbid"))return e.get("fbid")}const h=null==(s=/\/groups\/(.*?)\/posts\/(\d+)\//.exec(t))?void 0:s[2];if(h)return h;const y=null==(u=/\/posts\/(.*?)\/(\d+)/.exec(t))?void 0:u[2];if(y)return y;const g=null==(f=/\?fbid=(\d+)/.exec(t))?void 0:f[1];if(g)return g;if(n){const n=await e(t);if(n&&n!==t)return await c(n,!1,i)}if(i){const e=await d(t);if(e)return await p(e)}return null}async function u(t,n=!0){var i;if(/^\d+$/.test(t))return t;const r=null==(i=/\/stories\/(\d+)/.exec(t))?void 0:i[1];if(r)return r;if(n){const n=await e(t);if(n&&n!==t)return await u(n,!1)}return null}function f(e){try{const t=atob(e).split(":").pop();return/^\d+$/.test(t)?t:null}catch(t){return null}}async function _(e){var t;const n=await i({q:`node(${e}){id}`}),o=r(n);return null==(t=Object.keys(o))?void 0:t[0]}async function p(e){var t,n;if(a.videoIdToPostId[e])return a.videoIdToPostId[e];const l=await i({fb_api_req_friendly_name:"CometTahoeRootQuery",variables:{caller:"TAHOE",chainingCursor:null,chainingSeedVideoId:null,channelEntryPoint:"TAHOE",channelID:"",feedbackSource:41,feedLocation:"TAHOE",focusCommentID:null,isCrawler:!1,privacySelectorRenderLocation:"COMET_STREAM",renderLocation:"video_channel",scale:2,streamChainingSection:!1,useDefaultActor:!1,videoChainingContext:null,videoID:e,__relay_internal__pv__CometUFIShareActionMigrationrelayprovider:!0,__relay_internal__pv__GHLShouldChangeSponsoredDataFieldNamerelayprovider:!1,__relay_internal__pv__GHLShouldChangeAdIdFieldNamerelayprovider:!1,__relay_internal__pv__IsWorkUserrelayprovider:!1,__relay_internal__pv__CometUFIReactionsEnableShortNamerelayprovider:!1,__relay_internal__pv__StoriesLWRVariantrelayprovider:"www_new_reactions"},doc_id:"9471108166319679"}),s=r(l,[],!0);console.log("videoIdToPostId",s);const d=o(s,"post_id")||o(s,"parent_feedback.share_fbid")||(null==(n=null==(t=o(s,"legacy_token"))?void 0:t.split("_"))?void 0:n[0])||o(s,"feedback.subscription_target_id");return a.videoIdToPostId[e]=d,d}export{l as getIdFromUrl,c as getPostIdFromUrl,u as getStoryBucketIdFromUrl,s as getUidFromUrl,d as getVideoIdFromUrl,f as postIdFromBase64Id,_ as storyIdToPostId,p as videoIdToPostId};
