import{r as e,b0 as i,b5 as t}from"./index-Cak6rALw.js";import{u as o}from"./useForceStop-CvbJS7ei.js";import{u as n,d as r,S as a,T as s,A as m,g as d,t as l}from"./MyApp-DW5WH4Ub.js";import p from"./MyTable-DnjDmN7z.js";import c from"./useCacheState-CAnxkewm.js";import{g as j,a as u}from"./groupMembers-T0pfmfMg.js";import{R as x}from"./row-BMfM-of4.js";import{A as v}from"./index-DdM4T8-Q.js";import{I as h}from"./index-CDSnY0KE.js";import{T as f}from"./index-BzMPigdO.js";import"./index-ByRdMNW-.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./DownOutlined-B-JcS-29.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./index-PbLYNhdQ.js";function g({target:g}){const{ti:y}=n();o();const{message:b}=r(),[T,k]=e.useState(!1),[C,M]=c("GroupMembers."+(null==g?void 0:g.id)+".count",null),[I,A]=c("GroupMembers."+(null==g?void 0:g.id)+".data",[]),[w,L]=c("GroupMembers.loadMoreCount",10),S=e.useMemo((()=>I.map(((e,i)=>({...e,recent:i})))),[I]);e.useEffect((()=>{(null==g?void 0:g.id)&&(null==g?void 0:g.type)&&(k(!0),j(null==g?void 0:g.id).then((e=>{M(e)})).finally((()=>{k(!1)})))}),[g]);const G=async()=>{if(!(null==g?void 0:g.id))return;const e="GroupMembers."+(null==g?void 0:g.id)+".reload";l(e),b.loading({key:e,content:y({en:"Reloading...",vi:"Đang tải lại..."}),duration:0}),k(!0);const i=await u({groupId:(null==g?void 0:g.id)||""});A(i),k(!1),b.success({key:e,content:y({en:"Reloaded",vi:"Đã tải xong"})})},R=()=>{},E=[{title:"#",dataIndex:"recent",key:"recent",render:(e,i,t)=>(i.recent||0)+1},{title:y({en:"Name",vi:"Tên"}),key:"name",dataIndex:"name",sorter:(e,i)=>e.name.localeCompare(i.name),render:(e,t,o)=>i.jsxs(x,{align:"middle",children:[i.jsx(v,{shape:"square",src:i.jsx(h,{src:t.avatar}),size:50}),i.jsx("a",{href:t.url,target:"_blank",style:{marginLeft:"10px"},children:i.jsx("b",{children:t.name})})]}),width:"auto"},{title:"Uid",dataIndex:"id",key:"id",sorter:(e,i)=>e.id.localeCompare(i.id),width:150},{title:y({en:"Joined time",vi:"Tham gia lúc"}),dataIndex:"joinedTime",key:"joinedTime",sorter:(e,i)=>e.joinedTime.localeCompare(i.joinedTime)},{title:y({en:"Last Active",vi:"Hoạt động gần nhất"}),dataIndex:"lastActiveTime",key:"lastActiveTime",sorter:(e,i)=>e.lastActiveTime.localeCompare(i.lastActiveTime)}];return i.jsx(p,{columns:E,data:S,keyExtractor:e=>e.id,searchable:!0,renderTitle:e=>i.jsxs(i.Fragment,{children:[i.jsx(t,{type:"primary",loading:T,icon:i.jsx("i",{className:"fa-solid fa-rotate-right"}),onClick:G,children:y({en:"Reload",vi:"Tải lại"})}),i.jsxs(a.Compact,{children:[i.jsx(t,{loading:T,onClick:R,icon:i.jsx("i",{className:"fa-solid fa-play"}),children:y({en:"Load more",vi:"Tải thêm"})}),i.jsx(s,{title:y({en:"Number of members to load more",vi:"Số lượng thành viên muốn tải thêm"}),children:i.jsx(f,{min:1,max:null==C?void 0:C.memberCount,value:w,onChange:e=>L(e)})})]}),i.jsx(m,{showIcon:!0,type:"info",message:`${null==C?void 0:C.adminCount} admins. ${d(null==C?void 0:C.memberCount)} ${y({en:"members",vi:"thành viên"})}`})]})})}export{g as default};
