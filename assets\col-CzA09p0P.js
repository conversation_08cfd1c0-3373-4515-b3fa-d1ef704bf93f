import{r as e,G as t,bw as s,m as r}from"./index-Cak6rALw.js";import{a as o}from"./row-BMfM-of4.js";function l(e){return"number"==typeof e?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}const n=["xs","sm","md","lg","xl","xxl"],f=e.forwardRef(((f,p)=>{const{getPrefixCls:a,direction:i}=e.useContext(t),{gutter:$,wrap:c}=e.useContext(o),{prefixCls:u,span:d,order:x,offset:m,push:b,pull:g,className:h,children:y,flex:O,style:j}=f,w=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(s[r[o]]=e[r[o]])}return s}(f,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),v=a("col",u),[C,P,E]=s(v),N={};let R={};n.forEach((e=>{let t={};const s=f[e];"number"==typeof s?t.span=s:"object"==typeof s&&(t=s||{}),delete w[e],R=Object.assign(Object.assign({},R),{[`${v}-${e}-${t.span}`]:void 0!==t.span,[`${v}-${e}-order-${t.order}`]:t.order||0===t.order,[`${v}-${e}-offset-${t.offset}`]:t.offset||0===t.offset,[`${v}-${e}-push-${t.push}`]:t.push||0===t.push,[`${v}-${e}-pull-${t.pull}`]:t.pull||0===t.pull,[`${v}-rtl`]:"rtl"===i}),t.flex&&(R[`${v}-${e}-flex`]=!0,N[`--${v}-${e}-flex`]=l(t.flex))}));const S=r(v,{[`${v}-${d}`]:void 0!==d,[`${v}-order-${x}`]:x,[`${v}-offset-${m}`]:m,[`${v}-push-${b}`]:b,[`${v}-pull-${g}`]:g},h,R,P,E),W={};if($&&$[0]>0){const e=$[0]/2;W.paddingLeft=e,W.paddingRight=e}return O&&(W.flex=l(O),!1!==c||W.minWidth||(W.minWidth=0)),C(e.createElement("div",Object.assign({},w,{style:Object.assign(Object.assign(Object.assign({},W),j),N),className:S,ref:p}),y))}));export{f as C};
