import{r as e,b0 as i,b5 as t}from"./index-Cak6rALw.js";import{u as s,d as n,z as a,S as o,B as l,g as r,h as d,T as m,c,e as u,f as p,A as h,s as j}from"./MyApp-DW5WH4Ub.js";import x from"./Collection-BYcChfHL.js";import{getHiddenAlbums as g,getGroupAlbum as b,getUserAlbum as f}from"./albums-Bt4OWMRt.js";import v from"./useCacheState-CAnxkewm.js";import{B as w}from"./BadgeWrapper-BCbYXXfB.js";import{L as y}from"./index-jrOnBmdW.js";import{I as k}from"./index-CDSnY0KE.js";import"./index-ByRdMNW-.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-PbLYNhdQ.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CC5caqt_.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./addEventListener-C4tIKh7N.js";function T({target:T,onOpenAlbum:C}){const{ti:_}=s(),{message:N}=n(),[S,P]=v("hiddenAlbums_"+(null==T?void 0:T.id),[]),[B,D]=e.useState(!1),I=e.useCallback((async()=>{if(!(null==T?void 0:T.id)||!(null==T?void 0:T.type))return;D(!0);const e=await g(T.id);console.log(e),e?(N.success(_({en:`Found ${e.length} hidden albums`,vi:`Tìm thấy ${e.length} album ẩn`})),P(e)):N.error(_({en:"No hidden albums found",vi:"Không tìm thấy album ẩn nào"})),D(!1)}),[T,C]),$=e.useCallback((async(e=[])=>{var i,t;if(!(null==T?void 0:T.id)||!(null==T?void 0:T.type))return;const s=null==(i=null==e?void 0:e[(null==e?void 0:e.length)-1])?void 0:i.cursor,n=T.type===a.Group?await b({groupId:T.id,cursor:s}):await f({uid:T.id,cursor:s});if(null==(t=n.albums)?void 0:t.length){let i=new Set(e.map((e=>e.id)));return n.albums.filter((e=>!i.has(e.id)))}return[]}),[T]),A=e.useCallback((e=>i.jsx(y.Item,{children:i.jsxs(o,{direction:"vertical",children:[i.jsx(l.Ribbon,{text:r(e.count),children:i.jsxs("div",{className:"show-on-hover-trigger",children:[i.jsx(k,{src:e.picture,alt:e.name,width:150,height:150,fallback:"https://placehold.co/150x150",style:{objectFit:"cover",borderRadius:"10px",cursor:"pointer"},preview:!1,onClick:()=>{null==C||C(e)}}),i.jsx(t,{type:"default",icon:i.jsx("i",{className:"fa-solid fa-up-right-from-square"}),style:{position:"absolute",bottom:5,right:5},className:"show-on-hover-item",target:"_blank",href:d(e.id)}),(e.created_time||e.updated_time)&&i.jsxs(o,{direction:"vertical",style:{position:"absolute",bottom:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",padding:5,borderTopRightRadius:10},children:[e.created_time&&i.jsx(m,{title:_({en:"Created at ",vi:"Tạo lúc "})+p(new Date(e.created_time).getTime(),{showTime:!0}),children:i.jsxs(c.Text,{children:[i.jsx("i",{className:"fa-solid fa-plus"})," ",u(new Date(e.created_time).getTime())]})}),e.updated_time&&i.jsx(m,{title:_({en:"Last updated at ",vi:"Cập nhật lần cuối lúc "})+p(new Date(e.updated_time).getTime(),{showTime:!0}),children:i.jsxs(c.Text,{children:[i.jsx("i",{className:"fa-solid fa-pen-to-square"})," ",u(new Date(e.updated_time).getTime())]})})]})]})}),i.jsx(c.Paragraph,{style:{maxWidth:"150px",wordWrap:"break-word"},onClick:()=>window.open(e.link),children:e.name})]})},e.id)),[]),F=e.useCallback((()=>i.jsxs(o,{direction:"vertical",align:"center",children:[(null==S?void 0:S.length)>0&&i.jsxs(o,{direction:"vertical",align:"center",children:[i.jsxs(c.Title,{level:4,children:[_({en:`Found ${S.length} hidden albums`,vi:`Tìm thấy ${S.length} album ẩn`}),":"]}),i.jsx(o,{wrap:!0,align:"start",style:{justifyContent:"center"},children:S.map(A)})]}),i.jsx(h,{showIcon:!0,type:"info",message:_({en:"Click any album to view photos",vi:"Click album bất kỳ để xem ảnh"})})]})),[_,S,A]);return i.jsx(x,{collectionName:(null==T?void 0:T.name)+" - Albums",fetchNext:$,renderItem:A,rowKey:e=>e.id,header:F,searchPlaceholder:e=>_({en:`Search in ${null==e?void 0:e.length} albums...`,vi:`Tìm trong ${e.length} albums...`}),onSearch:(e,i)=>j(e,i.name),headerButtons:e=>(null==T?void 0:T.type)===a.Group?null:i.jsx(w,{type:"new",children:i.jsx(m,{title:i.jsxs(i.Fragment,{children:[_({en:"Including wall album, mobile album, ...",vi:"Bao gồm album Ảnh trên dòng thời gian, Tải lên từ di động, ..."}),i.jsx(t,{type:"link",href:"https://www.facebook.com/groups/fbaio/posts/1668900333764648",target:"_blank",icon:i.jsx("i",{className:"fa-solid fa-up-right-from-square"}),iconPosition:"end",children:_({en:"See more",vi:"Xem thêm"})})]}),children:i.jsx(t,{icon:i.jsx("i",{className:"fa-solid fa-eye-slash"}),onClick:I,loading:B,children:_({en:"Find hidden albums",vi:"Tìm album ẩn"})})})})})}export{T as default};
