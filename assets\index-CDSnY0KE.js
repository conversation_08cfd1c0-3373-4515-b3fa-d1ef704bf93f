import{r as e,m as n,a4 as t,C as o,F as r,d as i,a3 as a,n as c,a2 as l,w as s,l as u,$ as m,h as f,bM as d,i as p,e as g,T as v,I as h,L as w,M as b,ad as C,bN as y,ar as x,N as S,aa as I,J as E,a7 as N,as as k,G as M,af as R,X as O,Y as z,R as T,aL as j}from"./index-Cak6rALw.js";import{R as L}from"./EyeOutlined-CNKPohhw.js";import{a as P}from"./addEventListener-C4tIKh7N.js";import{R as A}from"./MyApp-DW5WH4Ub.js";function D(){return{width:document.documentElement.clientWidth,height:window.innerHeight||document.documentElement.clientHeight}}var Y=e.createContext(null),$=function(c){var l=c.visible,s=c.maskTransitionName,u=c.getContainer,m=c.prefixCls,f=c.rootClassName,d=c.icons,p=c.countRender,g=c.showSwitch,v=c.showProgress,h=c.current,w=c.transform,b=c.count,C=c.scale,y=c.minScale,x=c.maxScale,S=c.closeIcon,I=c.onActive,E=c.onClose,N=c.onZoomIn,k=c.onZoomOut,M=c.onRotateRight,R=c.onRotateLeft,O=c.onFlipX,z=c.onFlipY,T=c.onReset,j=c.toolbarRender,L=c.zIndex,P=c.image,A=e.useContext(Y),D=d.rotateLeft,$=d.rotateRight,X=d.zoomIn,H=d.zoomOut,Z=d.close,B=d.left,W=d.right,V=d.flipX,F=d.flipY,G="".concat(m,"-operations-operation");e.useEffect((function(){var e=function(e){e.keyCode===a.ESC&&E()};return l&&window.addEventListener("keydown",e),function(){window.removeEventListener("keydown",e)}}),[l]);var U=function(e,n){e.preventDefault(),e.stopPropagation(),I(n)},J=e.useCallback((function(o){var r=o.type,i=o.disabled,a=o.onClick,c=o.icon;return e.createElement("div",{key:r,className:n(G,"".concat(m,"-operations-operation-").concat(r),t({},"".concat(m,"-operations-operation-disabled"),!!i)),onClick:a},c)}),[G,m]),Q=g?J({icon:B,onClick:function(e){return U(e,-1)},type:"prev",disabled:0===h}):void 0,_=g?J({icon:W,onClick:function(e){return U(e,1)},type:"next",disabled:h===b-1}):void 0,q=J({icon:F,onClick:z,type:"flipY"}),K=J({icon:V,onClick:O,type:"flipX"}),ee=J({icon:D,onClick:R,type:"rotateLeft"}),ne=J({icon:$,onClick:M,type:"rotateRight"}),te=J({icon:H,onClick:k,type:"zoomOut",disabled:C<=y}),oe=J({icon:X,onClick:N,type:"zoomIn",disabled:C===x}),re=e.createElement("div",{className:"".concat(m,"-operations")},q,K,ee,ne,te,oe);return e.createElement(o,{visible:l,motionName:s},(function(o){var a=o.className,c=o.style;return e.createElement(r,{open:!0,getContainer:null!=u?u:document.body},e.createElement("div",{className:n("".concat(m,"-operations-wrapper"),a,f),style:i(i({},c),{},{zIndex:L})},null===S?null:e.createElement("button",{className:"".concat(m,"-close"),onClick:E},S||Z),g&&e.createElement(e.Fragment,null,e.createElement("div",{className:n("".concat(m,"-switch-left"),t({},"".concat(m,"-switch-left-disabled"),0===h)),onClick:function(e){return U(e,-1)}},B),e.createElement("div",{className:n("".concat(m,"-switch-right"),t({},"".concat(m,"-switch-right-disabled"),h===b-1)),onClick:function(e){return U(e,1)}},W)),e.createElement("div",{className:"".concat(m,"-footer")},v&&e.createElement("div",{className:"".concat(m,"-progress")},p?p(h+1,b):e.createElement("bdi",null,"".concat(h+1," / ").concat(b))),j?j(re,i(i({icons:{prevIcon:Q,nextIcon:_,flipYIcon:q,flipXIcon:K,rotateLeftIcon:ee,rotateRightIcon:ne,zoomOutIcon:te,zoomInIcon:oe},actions:{onActive:I,onFlipY:z,onFlipX:O,onRotateLeft:R,onRotateRight:M,onZoomOut:k,onZoomIn:N,onReset:T,onClose:E},transform:w},A?{current:h,total:b}:{}),{},{image:P})):re)))}))},X={x:0,y:0,rotate:0,scale:1,flipX:!1,flipY:!1};function H(e,n,o,r){var i=n+o,a=(o-r)/2;if(o>r){if(n>0)return t({},e,a);if(n<0&&i<r)return t({},e,-a)}else if(n<0||i>r)return t({},e,n<0?a:-a);return{}}function Z(e,n,t,o){var r=D(),a=r.width,c=r.height,l=null;return e<=a&&n<=c?l={x:0,y:0}:(e>a||n>c)&&(l=i(i({},H("x",t,e,a)),H("y",o,n,c))),l}function B(n){var t=n.src,o=n.isCustomPlaceholder,r=n.fallback,i=e.useState(o?"loading":"normal"),a=c(i,2),l=a[0],s=a[1],u=e.useRef(!1),m="error"===l;e.useEffect((function(){var e=!0;return function(e){return new Promise((function(n){if(e){var t=document.createElement("img");t.onerror=function(){return n(!1)},t.onload=function(){return n(!0)},t.src=e}else n(!1)}))}(t).then((function(n){!n&&e&&s("error")})),function(){e=!1}}),[t]),e.useEffect((function(){o&&!u.current?s("loading"):m&&s("normal")}),[t]);var f=function(){s("normal")};return[function(e){u.current=!1,"loading"===l&&null!=e&&e.complete&&(e.naturalWidth||e.naturalHeight)&&(u.current=!0,f())},m&&r?{src:r}:{onLoad:f,src:t},l]}function W(e,n){var t=e.x-n.x,o=e.y-n.y;return Math.hypot(t,o)}function V(n,t,o,r,a,l,s){var u=a.rotate,m=a.scale,f=a.x,d=a.y,p=e.useState(!1),g=c(p,2),v=g[0],h=g[1],w=e.useRef({point1:{x:0,y:0},point2:{x:0,y:0},eventType:"none"}),b=function(e){w.current=i(i({},w.current),e)};return e.useEffect((function(){var e;return o&&t&&(e=P(window,"touchmove",(function(e){return e.preventDefault()}),{passive:!1})),function(){var n;null===(n=e)||void 0===n||n.remove()}}),[o,t]),{isTouching:v,onTouchStart:function(e){if(t){e.stopPropagation(),h(!0);var n=e.touches,o=void 0===n?[]:n;o.length>1?b({point1:{x:o[0].clientX,y:o[0].clientY},point2:{x:o[1].clientX,y:o[1].clientY},eventType:"touchZoom"}):b({point1:{x:o[0].clientX-f,y:o[0].clientY-d},eventType:"move"})}},onTouchMove:function(e){var n=e.touches,t=void 0===n?[]:n,o=w.current,r=o.point1,i=o.point2,a=o.eventType;if(t.length>1&&"touchZoom"===a){var u={x:t[0].clientX,y:t[0].clientY},m={x:t[1].clientX,y:t[1].clientY},f=function(e,n,t,o){var r=W(e,t),i=W(n,o);if(0===r&&0===i)return[e.x,e.y];var a=r/(r+i);return[e.x+a*(n.x-e.x),e.y+a*(n.y-e.y)]}(r,i,u,m),d=c(f,2),p=d[0],g=d[1],v=W(u,m)/W(r,i);s(v,"touchZoom",p,g,!0),b({point1:u,point2:m,eventType:"touchZoom"})}else"move"===a&&(l({x:t[0].clientX-r.x,y:t[0].clientY-r.y},"move"),b({eventType:"move"}))},onTouchEnd:function(){if(o){if(v&&h(!1),b({eventType:"none"}),r>m)return l({x:0,y:0,scale:r},"touchZoom");var e=n.current.offsetWidth*m,t=n.current.offsetHeight*m,a=n.current.getBoundingClientRect(),c=a.left,s=a.top,f=u%180!=0,d=Z(f?t:e,f?e:t,c,s);d&&l(i({},d),"dragRebound")}}}}var F=["fallback","src","imgRef"],G=["prefixCls","src","alt","imageInfo","fallback","movable","onClose","visible","icons","rootClassName","closeIcon","getContainer","current","count","countRender","scaleStep","minScale","maxScale","transitionName","maskTransitionName","imageRender","imgCommonProps","toolbarRender","onTransform","onChange"],U=function(e){var n=e.fallback,t=e.src,o=e.imgRef,r=u(e,F),i=B({src:t,fallback:n}),a=c(i,2),l=a[0],s=a[1];return m.createElement("img",f({ref:function(e){o.current=e,l(e)}},r,s))},J=function(o){var r=o.prefixCls,p=o.src,g=o.alt,v=o.imageInfo,h=o.fallback,w=o.movable,b=void 0===w||w,C=o.onClose,y=o.visible,x=o.icons,S=void 0===x?{}:x,I=o.rootClassName,E=o.closeIcon,N=o.getContainer,k=o.current,M=void 0===k?0:k,R=o.count,O=void 0===R?1:R,z=o.countRender,T=o.scaleStep,j=void 0===T?.5:T,L=o.minScale,A=void 0===L?1:L,H=o.maxScale,B=void 0===H?50:H,W=o.transitionName,F=void 0===W?"zoom":W,J=o.maskTransitionName,Q=void 0===J?"fade":J,_=o.imageRender,q=o.imgCommonProps,K=o.toolbarRender,ee=o.onTransform,ne=o.onChange,te=u(o,G),oe=e.useRef(),re=e.useContext(Y),ie=re&&O>1,ae=re&&O>=1,ce=e.useState(!0),le=c(ce,2),se=le[0],ue=le[1],me=function(n,t,o,r){var a=e.useRef(null),u=e.useRef([]),m=e.useState(X),f=c(m,2),d=f[0],p=f[1],g=function(e,n){null===a.current&&(u.current=[],a.current=s((function(){p((function(e){var t=e;return u.current.forEach((function(e){t=i(i({},t),e)})),a.current=null,null==r||r({transform:t,action:n}),t}))}))),u.current.push(i(i({},d),e))};return{transform:d,resetTransform:function(e){p(X),l(X,d)||null==r||r({transform:X,action:e})},updateTransform:g,dispatchZoomChange:function(e,r,i,a,c){var l=n.current,s=l.width,u=l.height,m=l.offsetWidth,f=l.offsetHeight,p=l.offsetLeft,v=l.offsetTop,h=e,w=d.scale*e;w>o?(w=o,h=o/d.scale):w<t&&(h=(w=c?w:t)/d.scale);var b=null!=i?i:innerWidth/2,C=null!=a?a:innerHeight/2,y=h-1,x=y*s*.5,S=y*u*.5,I=y*(b-d.x-p),E=y*(C-d.y-v),N=d.x-(I-x),k=d.y-(E-S);if(e<1&&1===w){var M=m*w,R=f*w,O=D(),z=O.width,T=O.height;M<=z&&R<=T&&(N=0,k=0)}g({x:N,y:k,scale:w},r)}}}(oe,A,B,ee),fe=me.transform,de=me.resetTransform,pe=me.updateTransform,ge=me.dispatchZoomChange,ve=function(n,t,o,r,a,l,s){var u=a.rotate,m=a.scale,f=a.x,d=a.y,p=e.useState(!1),g=c(p,2),v=g[0],h=g[1],w=e.useRef({diffX:0,diffY:0,transformX:0,transformY:0}),b=function(e){o&&v&&l({x:e.pageX-w.current.diffX,y:e.pageY-w.current.diffY},"move")},C=function(){if(o&&v){h(!1);var e=w.current,t=e.transformX,r=e.transformY;if(f===t||d===r)return;var a=n.current.offsetWidth*m,c=n.current.offsetHeight*m,s=n.current.getBoundingClientRect(),p=s.left,g=s.top,b=u%180!=0,C=Z(b?c:a,b?a:c,p,g);C&&l(i({},C),"dragRebound")}};return e.useEffect((function(){var e,n,o,r;if(t){o=P(window,"mouseup",C,!1),r=P(window,"mousemove",b,!1);try{window.top!==window.self&&(e=P(window.top,"mouseup",C,!1),n=P(window.top,"mousemove",b,!1))}catch(i){}}return function(){var t,i,a,c;null===(t=o)||void 0===t||t.remove(),null===(i=r)||void 0===i||i.remove(),null===(a=e)||void 0===a||a.remove(),null===(c=n)||void 0===c||c.remove()}}),[o,v,f,d,u,t]),{isMoving:v,onMouseDown:function(e){t&&0===e.button&&(e.preventDefault(),e.stopPropagation(),w.current={diffX:e.pageX-f,diffY:e.pageY-d,transformX:f,transformY:d},h(!0))},onMouseMove:b,onMouseUp:C,onWheel:function(e){if(o&&0!=e.deltaY){var n=Math.abs(e.deltaY/100),t=1+Math.min(n,1)*r;e.deltaY>0&&(t=1/t),s(t,"wheel",e.clientX,e.clientY)}}}}(oe,b,y,j,fe,pe,ge),he=ve.isMoving,we=ve.onMouseDown,be=ve.onWheel,Ce=V(oe,b,y,A,fe,pe,ge),ye=Ce.isTouching,xe=Ce.onTouchStart,Se=Ce.onTouchMove,Ie=Ce.onTouchEnd,Ee=fe.rotate,Ne=fe.scale,ke=n(t({},"".concat(r,"-moving"),he));e.useEffect((function(){se||ue(!0)}),[se]);var Me=function(e){var n=M+e;!Number.isInteger(n)||n<0||n>O-1||(ue(!1),de(e<0?"prev":"next"),null==ne||ne(n,M))},Re=function(e){y&&ie&&(e.keyCode===a.LEFT?Me(-1):e.keyCode===a.RIGHT&&Me(1))};e.useEffect((function(){var e=P(window,"keydown",Re,!1);return function(){e.remove()}}),[y,ie,M]);var Oe=m.createElement(U,f({},q,{width:o.width,height:o.height,imgRef:oe,className:"".concat(r,"-img"),alt:g,style:{transform:"translate3d(".concat(fe.x,"px, ").concat(fe.y,"px, 0) scale3d(").concat(fe.flipX?"-":"").concat(Ne,", ").concat(fe.flipY?"-":"").concat(Ne,", 1) rotate(").concat(Ee,"deg)"),transitionDuration:(!se||ye)&&"0s"},fallback:h,src:p,onWheel:be,onMouseDown:we,onDoubleClick:function(e){y&&(1!==Ne?pe({x:0,y:0,scale:1},"doubleClick"):ge(1+j,"doubleClick",e.clientX,e.clientY))},onTouchStart:xe,onTouchMove:Se,onTouchEnd:Ie,onTouchCancel:Ie})),ze=i({url:p,alt:g},v);return m.createElement(m.Fragment,null,m.createElement(d,f({transitionName:F,maskTransitionName:Q,closable:!1,keyboard:!0,prefixCls:r,onClose:C,visible:y,classNames:{wrapper:ke},rootClassName:I,getContainer:N},te,{afterClose:function(){de("close")}}),m.createElement("div",{className:"".concat(r,"-img-wrapper")},_?_(Oe,i({transform:fe,image:ze},re?{current:M}:{})):Oe)),m.createElement($,{visible:y,transform:fe,maskTransitionName:Q,closeIcon:E,getContainer:N,prefixCls:r,rootClassName:I,icons:S,countRender:z,showSwitch:ie,showProgress:ae,current:M,count:O,scale:Ne,minScale:A,maxScale:B,toolbarRender:K,onActive:Me,onZoomIn:function(){ge(1+j,"zoomIn")},onZoomOut:function(){ge(1/(1+j),"zoomOut")},onRotateRight:function(){pe({rotate:Ee+90},"rotateRight")},onRotateLeft:function(){pe({rotate:Ee-90},"rotateLeft")},onFlipX:function(){pe({flipX:!fe.flipX},"flipX")},onFlipY:function(){pe({flipY:!fe.flipY},"flipY")},onClose:C,onReset:function(){de("reset")},zIndex:void 0!==te.zIndex?te.zIndex+1:void 0,image:ze}))},Q=["crossOrigin","decoding","draggable","loading","referrerPolicy","sizes","srcSet","useMap","alt"];var _=["visible","onVisibleChange","getContainer","current","movable","minScale","maxScale","countRender","closeIcon","onChange","onTransform","toolbarRender","imageRender"],q=["src"],K=0;var ee=["src","alt","onPreviewClose","prefixCls","previewPrefixCls","placeholder","fallback","width","height","style","preview","className","onClick","onError","wrapperClassName","wrapperStyle","rootClassName"],ne=["src","visible","onVisibleChange","getContainer","mask","maskClassName","movable","icons","scaleStep","minScale","maxScale","imageRender","toolbarRender"],te=function(o){var r=o.src,a=o.alt,l=o.onPreviewClose,s=o.prefixCls,m=void 0===s?"rc-image":s,d=o.previewPrefixCls,p=void 0===d?"".concat(m,"-preview"):d,h=o.placeholder,w=o.fallback,b=o.width,C=o.height,y=o.style,x=o.preview,S=void 0===x||x,I=o.className,E=o.onClick,N=o.onError,k=o.wrapperClassName,M=o.wrapperStyle,R=o.rootClassName,O=u(o,ee),z=h&&!0!==h,T="object"===g(S)?S:{},j=T.src,L=T.visible,P=void 0===L?void 0:L,A=T.onVisibleChange,D=void 0===A?l:A,$=T.getContainer,X=void 0===$?void 0:$,H=T.mask,Z=T.maskClassName,W=T.movable,V=T.icons,F=T.scaleStep,G=T.minScale,U=T.maxScale,_=T.imageRender,q=T.toolbarRender,te=u(T,ne),oe=null!=j?j:r,re=v(!!P,{value:P,onChange:D}),ie=c(re,2),ae=ie[0],ce=ie[1],le=B({src:r,isCustomPlaceholder:z,fallback:w}),se=c(le,3),ue=se[0],me=se[1],fe=se[2],de=e.useState(null),pe=c(de,2),ge=pe[0],ve=pe[1],he=e.useContext(Y),we=!!S,be=n(m,k,R,t({},"".concat(m,"-error"),"error"===fe)),Ce=e.useMemo((function(){var e={};return Q.forEach((function(n){void 0!==o[n]&&(e[n]=o[n])})),e}),Q.map((function(e){return o[e]}))),ye=function(n,t){var o=e.useState((function(){return String(K+=1)})),r=c(o,1)[0],i=e.useContext(Y),a={data:t,canPreview:n};return e.useEffect((function(){if(i)return i.register(r,a)}),[]),e.useEffect((function(){i&&i.register(r,a)}),[n,t]),r}(we,e.useMemo((function(){return i(i({},Ce),{},{src:oe})}),[oe,Ce]));return e.createElement(e.Fragment,null,e.createElement("div",f({},O,{className:be,onClick:we?function(e){var n,t,o,r=(n=e.target,t=n.getBoundingClientRect(),o=document.documentElement,{left:t.left+(window.pageXOffset||o.scrollLeft)-(o.clientLeft||document.body.clientLeft||0),top:t.top+(window.pageYOffset||o.scrollTop)-(o.clientTop||document.body.clientTop||0)}),i=r.left,a=r.top;he?he.onPreview(ye,oe,i,a):(ve({x:i,y:a}),ce(!0)),null==E||E(e)}:E,style:i({width:b,height:C},M)}),e.createElement("img",f({},Ce,{className:n("".concat(m,"-img"),t({},"".concat(m,"-img-placeholder"),!0===h),I),style:i({height:C},y),ref:ue},me,{width:b,height:C,onError:N})),"loading"===fe&&e.createElement("div",{"aria-hidden":"true",className:"".concat(m,"-placeholder")},h),H&&we&&e.createElement("div",{className:n("".concat(m,"-mask"),Z),style:{display:"none"===(null==y?void 0:y.display)?"none":void 0}},H)),!he&&we&&e.createElement(J,f({"aria-hidden":!ae,visible:ae,prefixCls:p,onClose:function(){ce(!1),ve(null)},mousePosition:ge,src:oe,alt:a,imageInfo:{width:b,height:C},fallback:w,getContainer:X,icons:V,movable:W,scaleStep:F,minScale:G,maxScale:U,rootClassName:R,imageRender:_,imgCommonProps:Ce,toolbarRender:q},te)))};te.PreviewGroup=function(n){var o,r=n.previewPrefixCls,a=void 0===r?"rc-image-preview":r,l=n.children,s=n.icons,m=void 0===s?{}:s,d=n.items,h=n.preview,w=n.fallback,b="object"===g(h)?h:{},C=b.visible,y=b.onVisibleChange,x=b.getContainer,S=b.current,I=b.movable,E=b.minScale,N=b.maxScale,k=b.countRender,M=b.closeIcon,R=b.onChange,O=b.onTransform,z=b.toolbarRender,T=b.imageRender,j=u(b,_),L=function(n){var o=e.useState({}),r=c(o,2),a=r[0],l=r[1],s=e.useCallback((function(e,n){return l((function(o){return i(i({},o),{},t({},e,n))})),function(){l((function(n){var t=i({},n);return delete t[e],t}))}}),[]);return[e.useMemo((function(){return n?n.map((function(e){if("string"==typeof e)return{data:{src:e}};var n={};return Object.keys(e).forEach((function(t){["src"].concat(p(Q)).includes(t)&&(n[t]=e[t])})),{data:n}})):Object.keys(a).reduce((function(e,n){var t=a[n],o=t.canPreview,r=t.data;return o&&e.push({data:r,id:n}),e}),[])}),[n,a]),s,!!n]}(d),P=c(L,3),A=P[0],D=P[1],$=P[2],X=v(0,{value:S}),H=c(X,2),Z=H[0],B=H[1],W=e.useState(!1),V=c(W,2),F=V[0],G=V[1],U=(null===(o=A[Z])||void 0===o?void 0:o.data)||{},K=U.src,ee=u(U,q),ne=v(!!C,{value:C,onChange:function(e,n){null==y||y(e,n,Z)}}),te=c(ne,2),oe=te[0],re=te[1],ie=e.useState(null),ae=c(ie,2),ce=ae[0],le=ae[1],se=e.useCallback((function(e,n,t,o){var r=$?A.findIndex((function(e){return e.data.src===n})):A.findIndex((function(n){return n.id===e}));B(r<0?0:r),re(!0),le({x:t,y:o}),G(!0)}),[A,$]);e.useEffect((function(){oe?F||B(0):G(!1)}),[oe]);var ue=e.useMemo((function(){return{register:D,onPreview:se}}),[D,se]);return e.createElement(Y.Provider,{value:ue},l,e.createElement(J,f({"aria-hidden":!oe,movable:I,visible:oe,prefixCls:a,closeIcon:M,onClose:function(){re(!1),le(null)},mousePosition:ce,imgCommonProps:ee,src:K,fallback:w,icons:m,minScale:E,maxScale:N,getContainer:x,current:Z,count:A.length,countRender:k,onTransform:O,toolbarRender:z,imageRender:T,onChange:function(e,n){B(e),null==R||R(e,n)}},j)))};var oe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H188V494h440v326z"}},{tag:"path",attrs:{d:"M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7.4 12.6-6.1v-63.9c12.9.1 25.9.9 38.8 2.5 42.1 5.2 82.1 18.2 119 38.7 38.1 21.2 71.2 49.7 98.4 84.3 27.1 34.7 46.7 73.7 58.1 115.8a325.95 325.95 0 016.5 140.9h74.9c14.8-103.6-11.3-213-81-302.3z"}}]},name:"rotate-left",theme:"outlined"},re=function(n,t){return e.createElement(h,f({},n,{ref:t,icon:oe}))},ie=e.forwardRef(re),ae={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-.4-12.6 6.1l-.2 64c-118.6.5-235.8 53.4-314.6 154.2A399.75 399.75 0 00123.5 631h74.9c-.9-5.3-1.7-10.7-2.4-16.1-5.1-42.1-2.1-84.1 8.9-124.8 11.4-42.2 31-81.1 58.1-115.8 27.2-34.7 60.3-63.2 98.4-84.3 37-20.6 76.9-33.6 119.1-38.8z"}},{tag:"path",attrs:{d:"M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H396V494h440v326z"}}]},name:"rotate-right",theme:"outlined"},ce=function(n,t){return e.createElement(h,f({},n,{ref:t,icon:ae}))},le=e.forwardRef(ce),se={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"},ue=function(n,t){return e.createElement(h,f({},n,{ref:t,icon:se}))},me=e.forwardRef(ue),fe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-in",theme:"outlined"},de=function(n,t){return e.createElement(h,f({},n,{ref:t,icon:fe}))},pe=e.forwardRef(de),ge={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h312c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-out",theme:"outlined"},ve=function(n,t){return e.createElement(h,f({},n,{ref:t,icon:ge}))},he=e.forwardRef(ve);const we=e=>({position:e||"absolute",inset:0}),be=e=>{const{iconCls:n,motionDurationSlow:t,paddingXXS:o,marginXXS:r,prefixCls:i,colorTextLightSolid:a}=e;return{position:"absolute",inset:0,display:"flex",alignItems:"center",justifyContent:"center",color:a,background:new C("#000").setA(.5).toRgbString(),cursor:"pointer",opacity:0,transition:`opacity ${t}`,[`.${i}-mask-info`]:Object.assign(Object.assign({},I),{padding:`0 ${E(o)}`,[n]:{marginInlineEnd:r,svg:{verticalAlign:"baseline"}}})}},Ce=e=>{const{previewCls:n,modalMaskBg:t,paddingSM:o,marginXL:r,margin:i,paddingLG:a,previewOperationColorDisabled:c,previewOperationHoverColor:l,motionDurationSlow:s,iconCls:u,colorTextLightSolid:m}=e,f=new C(t).setA(.1),d=f.clone().setA(.2);return{[`${n}-footer`]:{position:"fixed",bottom:r,left:{_skip_check_:!0,value:"50%"},display:"flex",flexDirection:"column",alignItems:"center",color:e.previewOperationColor,transform:"translateX(-50%)"},[`${n}-progress`]:{marginBottom:i},[`${n}-close`]:{position:"fixed",top:r,right:{_skip_check_:!0,value:r},display:"flex",color:m,backgroundColor:f.toRgbString(),borderRadius:"50%",padding:o,outline:0,border:0,cursor:"pointer",transition:`all ${s}`,"&:hover":{backgroundColor:d.toRgbString()},[`& > ${u}`]:{fontSize:e.previewOperationSize}},[`${n}-operations`]:{display:"flex",alignItems:"center",padding:`0 ${E(a)}`,backgroundColor:f.toRgbString(),borderRadius:100,"&-operation":{marginInlineStart:o,padding:o,cursor:"pointer",transition:`all ${s}`,userSelect:"none",[`&:not(${n}-operations-operation-disabled):hover > ${u}`]:{color:l},"&-disabled":{color:c,cursor:"not-allowed"},"&:first-of-type":{marginInlineStart:0},[`& > ${u}`]:{fontSize:e.previewOperationSize}}}}},ye=e=>{const{modalMaskBg:n,iconCls:t,previewOperationColorDisabled:o,previewCls:r,zIndexPopup:i,motionDurationSlow:a}=e,c=new C(n).setA(.1),l=c.clone().setA(.2);return{[`${r}-switch-left, ${r}-switch-right`]:{position:"fixed",insetBlockStart:"50%",zIndex:e.calc(i).add(1).equal(),display:"flex",alignItems:"center",justifyContent:"center",width:e.imagePreviewSwitchSize,height:e.imagePreviewSwitchSize,marginTop:e.calc(e.imagePreviewSwitchSize).mul(-1).div(2).equal(),color:e.previewOperationColor,background:c.toRgbString(),borderRadius:"50%",transform:"translateY(-50%)",cursor:"pointer",transition:`all ${a}`,userSelect:"none","&:hover":{background:l.toRgbString()},"&-disabled":{"&, &:hover":{color:o,background:"transparent",cursor:"not-allowed",[`> ${t}`]:{cursor:"not-allowed"}}},[`> ${t}`]:{fontSize:e.previewOperationSize}},[`${r}-switch-left`]:{insetInlineStart:e.marginSM},[`${r}-switch-right`]:{insetInlineEnd:e.marginSM}}},xe=e=>{const{motionEaseOut:n,previewCls:t,motionDurationSlow:o,componentCls:r}=e;return[{[`${r}-preview-root`]:{[t]:{height:"100%",textAlign:"center",pointerEvents:"none"},[`${t}-body`]:Object.assign(Object.assign({},we()),{overflow:"hidden"}),[`${t}-img`]:{maxWidth:"100%",maxHeight:"70%",verticalAlign:"middle",transform:"scale3d(1, 1, 1)",cursor:"grab",transition:`transform ${o} ${n} 0s`,userSelect:"none","&-wrapper":Object.assign(Object.assign({},we()),{transition:`transform ${o} ${n} 0s`,display:"flex",justifyContent:"center",alignItems:"center","& > *":{pointerEvents:"auto"},"&::before":{display:"inline-block",width:1,height:"50%",marginInlineEnd:-1,content:'""'}})},[`${t}-moving`]:{[`${t}-preview-img`]:{cursor:"grabbing","&-wrapper":{transitionDuration:"0s"}}}}},{[`${r}-preview-root`]:{[`${t}-wrap`]:{zIndex:e.zIndexPopup}}},{[`${r}-preview-operations-wrapper`]:{position:"fixed",zIndex:e.calc(e.zIndexPopup).add(1).equal()},"&":[Ce(e),ye(e)]}]},Se=e=>{const{componentCls:n}=e;return{[n]:{position:"relative",display:"inline-block",[`${n}-img`]:{width:"100%",height:"auto",verticalAlign:"middle"},[`${n}-img-placeholder`]:{backgroundColor:e.colorBgContainerDisabled,backgroundImage:"url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQuNSAyLjVoLTEzQS41LjUgMCAwIDAgMSAzdjEwYS41LjUgMCAwIDAgLjUuNWgxM2EuNS41IDAgMCAwIC41LS41VjNhLjUuNSAwIDAgMC0uNS0uNXpNNS4yODEgNC43NWExIDEgMCAwIDEgMCAyIDEgMSAwIDAgMSAwLTJ6bTguMDMgNi44M2EuMTI3LjEyNyAwIDAgMS0uMDgxLjAzSDIuNzY5YS4xMjUuMTI1IDAgMCAxLS4wOTYtLjIwN2wyLjY2MS0zLjE1NmEuMTI2LjEyNiAwIDAgMSAuMTc3LS4wMTZsLjAxNi4wMTZMNy4wOCAxMC4wOWwyLjQ3LTIuOTNhLjEyNi4xMjYgMCAwIDEgLjE3Ny0uMDE2bC4wMTUuMDE2IDMuNTg4IDQuMjQ0YS4xMjcuMTI3IDAgMCAxLS4wMi4xNzV6IiBmaWxsPSIjOEM4QzhDIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=')",backgroundRepeat:"no-repeat",backgroundPosition:"center center",backgroundSize:"30%"},[`${n}-mask`]:Object.assign({},be(e)),[`${n}-mask:hover`]:{opacity:1},[`${n}-placeholder`]:Object.assign({},we())}}},Ie=e=>{const{previewCls:n}=e;return{[`${n}-root`]:S(e,"zoom"),"&":x(e,!0)}},Ee=w("Image",(e=>{const n=`${e.componentCls}-preview`,t=b(e,{previewCls:n,modalMaskBg:new C("#000").setA(.45).toRgbString(),imagePreviewSwitchSize:e.controlHeightLG});return[Se(t),xe(t),y(b(t,{componentCls:n})),Ie(t)]}),(e=>({zIndexPopup:e.zIndexPopupBase+80,previewOperationColor:new C(e.colorTextLightSolid).setA(.65).toRgbString(),previewOperationHoverColor:new C(e.colorTextLightSolid).setA(.85).toRgbString(),previewOperationColorDisabled:new C(e.colorTextLightSolid).setA(.25).toRgbString(),previewOperationSize:1.5*e.fontSizeIcon})));const Ne={rotateLeft:e.createElement(ie,null),rotateRight:e.createElement(le,null),zoomIn:e.createElement(pe,null),zoomOut:e.createElement(he,null),close:e.createElement(k,null),left:e.createElement(A,null),right:e.createElement(N,null),flipX:e.createElement(me,null),flipY:e.createElement(me,{rotate:90})};var ke=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(t[o[r]]=e[o[r]])}return t};const Me=t=>{const{prefixCls:o,preview:r,className:i,rootClassName:a,style:c}=t,l=ke(t,["prefixCls","preview","className","rootClassName","style"]),{getPrefixCls:s,getPopupContainer:u,className:m,style:f,preview:d}=T("image"),[p]=j("Image"),g=s("image",o),v=s(),h=R(g),[w,b,C]=Ee(g,h),y=n(a,b,C,h),x=n(i,b,m),[S]=O("ImagePreview","object"==typeof r?r.zIndex:void 0),I=e.useMemo((()=>{if(!1===r)return r;const t="object"==typeof r?r:{},{getContainer:o,closeIcon:i,rootClassName:a,destroyOnClose:c,destroyOnHidden:l}=t,s=ke(t,["getContainer","closeIcon","rootClassName","destroyOnClose","destroyOnHidden"]);return Object.assign(Object.assign({mask:e.createElement("div",{className:`${g}-mask-info`},e.createElement(L,null),null==p?void 0:p.preview),icons:Ne},s),{destroyOnClose:null!=l?l:c,rootClassName:n(y,a),getContainer:null!=o?o:u,transitionName:z(v,"zoom",t.transitionName),maskTransitionName:z(v,"fade",t.maskTransitionName),zIndex:S,closeIcon:null!=i?i:null==d?void 0:d.closeIcon})}),[r,p,null==d?void 0:d.closeIcon]),E=Object.assign(Object.assign({},f),c);return w(e.createElement(te,Object.assign({prefixCls:g,preview:I,rootClassName:y,className:x,style:E},l)))};Me.PreviewGroup=t=>{var{previewPrefixCls:o,preview:r}=t,i=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(t[o[r]]=e[o[r]])}return t}(t,["previewPrefixCls","preview"]);const{getPrefixCls:a,direction:c}=e.useContext(M),l=a("image",o),s=`${l}-preview`,u=a(),m=R(l),[f,d,p]=Ee(l,m),[g]=O("ImagePreview","object"==typeof r?r.zIndex:void 0),v=e.useMemo((()=>Object.assign(Object.assign({},Ne),{left:"rtl"===c?e.createElement(N,null):e.createElement(A,null),right:"rtl"===c?e.createElement(A,null):e.createElement(N,null)})),[c]),h=e.useMemo((()=>{var e;if(!1===r)return r;const t="object"==typeof r?r:{},o=n(d,p,m,null!==(e=t.rootClassName)&&void 0!==e?e:"");return Object.assign(Object.assign({},t),{transitionName:z(u,"zoom",t.transitionName),maskTransitionName:z(u,"fade",t.maskTransitionName),rootClassName:o,zIndex:g})}),[r]);return f(e.createElement(te.PreviewGroup,Object.assign({preview:h,previewPrefixCls:s,icons:v},i)))};export{Me as I};
