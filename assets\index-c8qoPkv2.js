import{bc as e,r as i,b0 as n,b5 as t,bk as s,bi as r}from"./index-Cak6rALw.js";import{d as a}from"./dayjs.min-CzPn7FMI.js";import{d as o,u as l,t as d,S as m,c,f as u,T as p,b as h,s as g,v as j,o as x,h as v,aF as f}from"./MyApp-DW5WH4Ub.js";import{u as y}from"./useForceStop-CvbJS7ei.js";import{E as k}from"./ExportButton-CbyepAf_.js";import T from"./useCacheState-CAnxkewm.js";import{getMyManagedGroups as b,UserTypeInGroup as L,getMyJoinedGroups as N,SubspaceType as _,leaveGroup as D}from"./groups-D1WZyVD8.js";import{g as w}from"./groupMembers-T0pfmfMg.js";import{checkVIP as E}from"./useVIP-D5FAMGQQ.js";import M from"./WordStatisticButton-D2UxB2M4.js";import{u as O}from"./useAction-uz-MrZwJ.js";import{u as C}from"./useDevMode-yj0U7_8D.js";import{S as F}from"./Screen-Buq7HJpK.js";import I from"./MyTable-DnjDmN7z.js";import{A as G}from"./index-DdM4T8-Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./sweetalert2.esm.all-BZxvatOx.js";import"./getIds-DAzc-wzf.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./index-IxNfZAD-.js";import"./BadgeWrapper-BCbYXXfB.js";import"./index-Bp2s1Wws.js";import"./index-Bx3o6rwA.js";import"./index-BzMPigdO.js";import"./DownOutlined-B-JcS-29.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-CC5caqt_.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-ByRdMNW-.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./index-D6X7PZwe.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./Pagination-2X6SDgot.js";import"./index-PbLYNhdQ.js";function P(){const{message:P,notification:R}=o(),S=e(),{ti:V}=l(),{onClickAction:Y,onClickBulkActions:$}=O(),{devMode:A}=C(),B=y(),[H,J]=T("Groups.data",[]),[W,U]=T("Groups.finished",!1),[q,z]=i.useState(!1),K=i.useRef(null);i.useEffect((()=>{W||Q(!1)}),[]);const Q=async(e=!1)=>{var i;if(q&&!e)return;const n=B.start(),t=e?[]:[...H],s=new Set(t.map((e=>e.id))),r="Groups:onReload";d(r),z(!0),U(!1);try{for(const[a,o]of[[b,L.ADMIN],[N,L.MEMBER]]){const l=t[t.length-1];let d=e?"":(null==l?void 0:l.userType)===o?null==l?void 0:l.cursor:"";for(;!n.value();){P.loading({key:r,content:V({en:"Loading groups...",vi:"Đang tải nhóm..."})+t.length,duration:0});const e=await a({cursor:d}),n=null==e?void 0:e.filter((e=>!s.has(e.id)));if(e.forEach((e=>s.add(e.id))),!(null==n?void 0:n.length))break;t.push(...n),d=null==(i=null==t?void 0:t[(null==t?void 0:t.length)-1])?void 0:i.cursor,J([...t])}}P.success({key:r,content:(n.value()?V({en:"Load groups stopped ",vi:"Dừng tải nhóm "}):V({en:"Load groups completed ",vi:"Tải xong nhóm "}))+t.length}),U(!n.value())}catch(a){P.error({content:V({en:"Failed to load groups: ",vi:"Lỗi tải nhóm: "})+a.message})}finally{z(!1)}},X=(e,i=!1)=>Y({key:"Groups:onClickLeaveGroup",id:e.id,record:e,actionFn:()=>A?j(1e3):D(e.id),loadingText:e=>V({en:"Leaving group...",vi:"Đang rời nhóm..."})+e.name,successText:e=>V({en:"Left group ",vi:"Đã rời nhóm "})+e.name,failedText:e=>V({en:"Failed to leave group: ",vi:"Lỗi rời nhóm: "})+e.name,onSuccess(){((e,i=L.NOT_JOINED)=>{J((n=>r(n,(n=>{const t=n.findIndex((i=>i.id==e));return n[t].userType=i,n}))))})(e.id)},needConfirm:i,confirmProps:{title:V({en:"Leave group?",vi:"Rời nhóm?"}),text:e.name}}),Z=e=>{switch(e){case L.MEMBER:return{color:"default",text:V({en:"Member",vi:"Thành viên"})};case L.ADMIN:return{color:"success",text:"Admin"};case L.NOT_JOINED:return{color:"error",text:V({en:"Left group",vi:"Đã rời nhóm"})};case L.PENDING:return{color:"warning",text:V({en:"Pending",vi:"Đang chờ"})};default:return{color:"default",text:"?"}}},ee=[{title:"#",key:"index",dataIndex:"index"},{title:V({en:"Group",vi:"Nhóm"}),key:"group",dataIndex:"group",sorter:(e,i)=>e.name.localeCompare(i.name),render:(e,i,t)=>n.jsxs(m,{style:{maxWidth:400},align:"start",children:[n.jsx(G,{src:i.avatar,size:45}),n.jsxs(m,{direction:"vertical",size:0,children:[n.jsx(c.Link,{href:i.url,target:"_blank",children:n.jsx("b",{children:i.name})}),n.jsx(c.Text,{type:"secondary",children:i.id})]})]})},{title:V({en:"Last visit",vi:"Lần xem gần nhất"}),key:"lastVisitedTime",dataIndex:"lastVisitedTime",sorter:(e,i)=>e.lastVisitedTime-i.lastVisitedTime,render:(e,i,n)=>u(i.lastVisitedTime),align:"right"},{title:V({en:"Type",vi:"Loại"}),key:"type",dataIndex:"userType",render:(e,i,t)=>{const{color:s,text:r}=Z(i.userType);return n.jsxs(n.Fragment,{children:[i.subspaceType==_.TOP_LEVEL_GROUP&&n.jsxs(n.Fragment,{children:[n.jsx(p,{title:V({en:"Your most interested group",vi:"Nhóm bạn quan tâm nhất"}),children:n.jsx(h,{color:"purple",children:"Top level"})}),n.jsx("br",{})]}),n.jsx(h,{color:s,children:r})]})},filters:[...Object.values(L).map((e=>{const{text:i}=Z(e);return{text:i+" ("+H.filter((i=>i.userType==e)).length+")",value:e}})),{text:"Top level ("+H.filter((e=>e.subspaceType==_.TOP_LEVEL_GROUP)).length+")",value:_.TOP_LEVEL_GROUP}],onFilter:(e,i)=>e==_.TOP_LEVEL_GROUP?i.subspaceType==e:i.userType==e,align:"right"},{title:V({en:"Action",vi:"Hành động"}),key:"action",render:(e,i,r)=>n.jsxs(m.Compact,{style:{minWidth:30},children:[n.jsx(p,{title:V({en:"Bulk Downloader",vi:"Tải hàng loạt"}),children:n.jsx(t,{type:"default",onClick:()=>(e=>{S("/bulk-downloader",{state:{targetId:e.id,platform:s.Facebook}})})(i),icon:n.jsx("i",{className:"fa-solid fa-download"})})}),i.userType!==L.NOT_JOINED?n.jsx(p,{title:V({en:"Leave group",vi:"Rời nhóm"}),children:n.jsx(t,{danger:!0,onClick:()=>X(i,!0),icon:n.jsx("i",{className:"fa-regular fa-trash-can"})})}):null]}),align:"right"}];return n.jsx(F,{title:V({en:"Groups manager",vi:"Quản lý nhóm"}),titleSuffix:n.jsx(h,{style:{marginLeft:"10px",fontWeight:"bold",color:"#888"},children:H.length}),children:n.jsx(I,{ref:K,data:[...H].sort(((e,i)=>i.subspaceType.localeCompare(e.subspaceType))).map(((e,i)=>({...e,index:i+1}))),columns:ee,renderTitle:e=>{var i,s;const r=(null==e?void 0:e.length)?[...e]:[...H],o=(null==(i=null==r?void 0:r.map((e=>H.find((i=>i.id===e.id)))))?void 0:i.filter((e=>e&&e.userType!==L.NOT_JOINED)))||[],l=(null==(s=null==r?void 0:r.map((e=>H.find((i=>i.id===e.id)))))?void 0:s.filter((e=>e&&e.userType!==L.ADMIN&&e.userType!==L.PENDING)))||[];return n.jsxs(n.Fragment,{children:[n.jsx(t,{type:"primary",icon:q?n.jsx("i",{className:"fa-solid fa-rotate-right fa-spin"}):n.jsx("i",{className:"fa-solid fa-rotate-right"}),onClick:()=>Q(!0),children:V({en:"Reload",vi:"Tải lại"})}),n.jsx(k,{data:r,options:[{key:"id",label:".txt (group id)",prepareData:e=>({fileName:"your_groups_id"+a().format("YYYY-MM-DD-HHmmss")+".txt",data:null==e?void 0:e.map((e=>e.id)).join("\n")})},{key:"id_name",label:".csv (id+name)",prepareData:e=>({fileName:"my_joined_groups_id_name.csv",data:x(e.map((e=>({uid:e.id,name:e.name}))))})},{key:"json",label:".json",prepareData:e=>({fileName:"your_groups"+a().format("YYYY-MM-DD-HHmmss")+".json",data:JSON.stringify(null==e?void 0:e.map((e=>({...e,last_visited:u(e.lastVisitedTime)}))),null,4)})},{key:"csv",label:".csv",prepareData:e=>({fileName:"your_groups"+a().format("YYYY-MM-DD-HHmmss")+".csv",data:x(e)})}]}),n.jsx(p,{title:V({en:`Leave ${o.length} selected groups`,vi:`Rời ${o.length} nhóm được chọn`}),children:n.jsx(t,{danger:!0,disabled:o.length<=0,icon:n.jsx("i",{className:"fa-regular fa-trash-can"}),onClick:()=>{return $({data:e=o,key:"Groups:onClickLeaveSelectedGroups",actionFn:X,loadingText:(e,i,n)=>V({en:"Leaving groups...",vi:"Đang rời nhóm..."}),successText:(e,i)=>V({en:"Bulk leave groups completed",vi:"Hoàn tất Rời nhóm hàng loạt"}),successDescItem:e=>n.jsx("a",{target:"_blank",href:v(e.id),children:e.name}),confirmProps:{title:V({en:`Leave ${e.length} groups`,vi:`Rời ${e.length} nhóm`}),text:V({en:`Are your sure want to Leave ${e.length} groups?`,vi:`Bản có chãc muốn rời ${e.length} nhóm?`})}});var e},children:o.length})}),n.jsx(p,{title:V({en:`Find groups that have no admin in ${l.length} selected groups`,vi:`Tìm nhóm không có admin quản lý trong ${l.length} nhóm được chọn`}),children:n.jsx(t,{icon:n.jsx("i",{className:"fa-solid fa-user-large-slash"}),onClick:()=>(async e=>{var i,t;if(!(await E()))return;const s="Groups:onClickFindNoAdminGroups",r=s+":waiting";d(s),P.loading({key:s,duration:0,content:V({en:"Finding groups no admin... ",vi:"Đang tìm nhóm không có admin... "})});let a=[],o=!1;for(let l=0;l<e.length&&!o;l++){const d=e[l],m={key:s,duration:0,content:n.jsxs(n.Fragment,{children:[V({en:"Checking group... ",vi:"Đang kiểm tra... "})+`${l+1}/${e.length}`,n.jsx("br",{}),d.name,n.jsx("br",{}),V({en:"Found ",vi:"Tìm thấy "})+a.length,n.jsx("br",{}),n.jsx("i",{children:V({en:"Click to stop",vi:"Bấm để dừng"})})]}),onClick:()=>{o=!0}};if(P.loading(m),0===(await w(d.id)).adminCount&&(a.push(d),null==(t=null==(i=K.current)?void 0:i.setDataSelected)||t.call(i,[...a])),P.success(m),l<e.length-1){const e=f(2e3,5e3);await j(e,(e=>(P.loading({key:r,content:V({en:"Waiting "+Math.round(e/1e3)+"s",vi:"Đang đợi "+Math.round(e/1e3)+"s"}),duration:0}),o)))}}P.destroy(s),P.destroy(r),R.open({type:"success",duration:0,message:V({en:`Found ${a.length}/${e.length} groups no admin`,vi:`Tìm thấy ${a.length}/${e.length} nhóm không có admin`}),description:n.jsx("ol",{style:{maxHeight:300,overflowY:"auto",whiteSpace:"pre-wrap"},children:a.map((e=>n.jsx("li",{children:n.jsx("a",{target:"_blank",href:v(e.id),children:e.name})},e.id)))})})})(l),disabled:l.length<=0,children:l.length})}),n.jsx(M,{name:"Groups",text:H.map((e=>e.name)).join(" ")}),n.jsx(p,{title:V({en:"View on Facebook",vi:"Xem trên Facebook"}),children:n.jsx(t,{icon:n.jsx("i",{className:"fa-solid fa-external-link"}),onClick:()=>window.open("https://www.facebook.com/groups/joins","_blank")})})]})},size:"small",searchable:!0,selectable:!0,onSearchRow:(e,i)=>g(e,(null==i?void 0:i.name)+(null==i?void 0:i.id)+u(null==i?void 0:i.lastVisitedTime)),keyExtractor:e=>e.id})})}export{P as default};
