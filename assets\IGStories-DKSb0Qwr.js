const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./VideoWithMuted-CYlwayKv.js","./index-Cak6rALw.js","./index-DIX6R8z-.css"])))=>i.map(i=>d[i]);
import{r as e,b0 as s,b1 as i,b5 as o,aN as t}from"./index-Cak6rALw.js";import{i as r}from"./icons-DFJmfgN3.js";import a from"./Collection-BYcChfHL.js";import{getIGUserStories as n}from"./stories-CXRVmsoM.js";import{u as m,e as l,A as d,I as j}from"./MyApp-DW5WH4Ub.js";import{getInstaUrlFromId as p}from"./core-Dw7OsFL6.js";import{L as c}from"./index-jrOnBmdW.js";import{I as u}from"./index-CDSnY0KE.js";import"./index-ByRdMNW-.js";import"./useCacheState-CAnxkewm.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-PbLYNhdQ.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CC5caqt_.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./addEventListener-C4tIKh7N.js";const h=i((()=>t((()=>import("./VideoWithMuted-CYlwayKv.js")),__vite__mapDeps([0,1,2]),import.meta.url)),{fallback:j});function x({target:i}){const{ti:t}=m(),j=e.useCallback((async e=>{if(!(null==i?void 0:i.id))return;return await n(i.id)}),[i]),x=e.useCallback((e=>s.jsxs(c.Item,{className:"show-on-hover-trigger",children:[s.jsx(u,{src:e.image,width:200,height:330,style:{objectFit:"cover",borderRadius:10},preview:!e.video||{destroyOnClose:!0,imageRender:()=>s.jsx(h,{src:e.video,style:{maxWidth:"90vw",maxHeight:"90vh"}}),toolbarRender:()=>null}}),e.video&&s.jsx("div",{style:{position:"absolute",top:10,right:10,pointerEvents:"none"},children:r.IGVideo}),s.jsx("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",background:"linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 50%)",pointerEvents:"none"},children:s.jsxs("div",{style:{position:"absolute",bottom:15,left:15},children:[e.music&&s.jsxs(s.Fragment,{children:[s.jsx("i",{className:"fa-solid fa-music"})," ",e.music,s.jsx("br",{})]}),e.seen_at?s.jsxs(s.Fragment,{children:[s.jsx("i",{className:"fa-solid fa-eye"})," ",l(e.seen_at)]}):s.jsxs(s.Fragment,{children:[s.jsx("i",{className:"fa-solid fa-eye-slash"})," ",t({en:"Not seen",vi:"Chưa xem"})]}),s.jsx("br",{}),s.jsx("i",{className:"fa-solid fa-clock"})," ",l(e.taken_at)]})}),s.jsx(o,{type:"default",icon:s.jsx("i",{className:"fa-solid fa-up-right-from-square"}),style:{position:"absolute",bottom:10,right:10},className:"show-on-hover-item",target:"_blank",href:p("stories/"+(null==i?void 0:i.username)+"/"+e.pk)})]})),[]),f=e.useCallback(((e,s)=>{const i=!!e.video;return{url:i?e.video:e.image,name:e.id+(i?".mp4":".jpg")}}),[]);return s.jsx(a,{collectionName:(null==i?void 0:i.username)+" - IG Stories",fetchNext:j,renderItem:x,downloadItem:f,rowKey:e=>e.id,once:!0,header:()=>s.jsx(d,{showIcon:!0,type:"info",message:(null==i?void 0:i.name)+t({en:" will not know you see this story",vi:" sẽ không biết bạn đã xem tin"})})})}export{x as default};
