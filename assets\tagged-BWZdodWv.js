import{aM as e}from"./index-Cak6rALw.js";import{aC as i,aD as a,aE as o,x as r}from"./MyApp-DW5WH4Ub.js";async function t(t,n=""){var l;const s=await i(a.FBAndroid)||await i(a.EAAB),d=await e(`https://graph.facebook.com/v12.0/${t}/tagged?fields=from,id,message,created_time`+(n?`&after=${n}`:"")+`&access_token=${s}`);o(d);const c=r(d);return console.log(c),(null==(l=null==c?void 0:c.data)?void 0:l.map((e=>{var i,a,o,r;return{id:e.id,message:e.message,created_time:e.created_time,from:{id:null==(i=e.from)?void 0:i.id,name:null==(a=e.from)?void 0:a.name},cursor:null==(r=null==(o=null==c?void 0:c.paging)?void 0:o.cursors)?void 0:r.after}})))||[]}async function n(t,n=""){var l,s;const d=await i(a.FBAndroid)||await i(a.EAAB),c=await e(`https://graph.facebook.com/v17.0/${t}?fields=videos${n?`.after(${n})`:""}{created_time,description,from,id,source,title,permalink_url,length,views,picture}&access_token=${d}`);o(c);const u=r(c);return console.log(u),(null==(s=null==(l=null==u?void 0:u.videos)?void 0:l.data)?void 0:s.map((e=>{var i,a,o,r,t;return{id:e.id,created_time:e.created_time,description:e.description,from:{id:null==(i=e.from)?void 0:i.id,name:null==(a=e.from)?void 0:a.name},source:e.source,title:e.title,permalink_url:e.permalink_url,length:e.length,views:e.views,picture:e.picture,cursor:null==(t=null==(r=null==(o=null==u?void 0:u.videos)?void 0:o.paging)?void 0:r.cursors)?void 0:t.after}})))||[]}export{n as a,t as g};
