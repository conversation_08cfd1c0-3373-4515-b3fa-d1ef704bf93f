import{r as e,d as t,a4 as n,h as r,m as o,n as i,e as u,w as a,c,b as l,q as s,l as f,o as v,p as d}from"./index-Cak6rALw.js";import{Z as h}from"./MyApp-DW5WH4Ub.js";var m=e.forwardRef((function(i,u){var a=i.height,c=i.offsetY,l=i.offsetX,s=i.children,f=i.prefixCls,v=i.onInnerResize,d=i.innerProps,m=i.rtl,g=i.extra,p={},w={display:"flex",flexDirection:"column"};return void 0!==c&&(p={height:a,position:"relative",overflow:"hidden"},w=t(t({},w),{},n(n(n(n(n({transform:"translateY(".concat(c,"px)")},m?"marginRight":"marginLeft",-l),"position","absolute"),"left",0),"right",0),"top",0))),e.createElement("div",{style:p},e.createElement(h,{onResize:function(e){e.offsetHeight&&v&&v()}},e.createElement("div",r({style:w,className:o(n({},"".concat(f,"-holder-inner"),f)),ref:u},d),s,g)))}));function g(t){var n=t.children,r=t.setRef,o=e.useCallback((function(e){r(e)}),[]);return e.cloneElement(n,{ref:o})}function p(t,n,r){var o=e.useState(t),u=i(o,2),a=u[0],c=u[1],l=e.useState(null),s=i(l,2),f=s[0],v=s[1];return e.useEffect((function(){var e=function(e,t,n){var r,o,i=e.length,u=t.length;if(0===i&&0===u)return null;i<u?(r=e,o=t):(r=t,o=e);var a={__EMPTY_ITEM__:!0};function c(e){return void 0!==e?n(e):a}for(var l=null,s=1!==Math.abs(i-u),f=0;f<o.length;f+=1){var v=c(r[f]);if(v!==c(o[f])){l=f,s=s||v!==c(o[f+1]);break}}return null===l?null:{index:l,multiple:s}}(a||[],t||[],n);void 0!==(null==e?void 0:e.index)&&v(t[e.index]),c(t)}),[t]),[f]}m.displayName="Filler";var w="object"===("undefined"==typeof navigator?"undefined":u(navigator))&&/Firefox/i.test(navigator.userAgent);const R=function(t,n,r,o){var i=e.useRef(!1),u=e.useRef(null);var a=e.useRef({top:t,bottom:n,left:r,right:o});return a.current.top=t,a.current.bottom=n,a.current.left=r,a.current.right=o,function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=e?t<0&&a.current.left||t>0&&a.current.right:t<0&&a.current.top||t>0&&a.current.bottom;return n&&r?(clearTimeout(u.current),i.current=!1):r&&!i.current||(clearTimeout(u.current),i.current=!0,u.current=setTimeout((function(){i.current=!1}),50)),!i.current&&r}};function M(t,n,r,o,i,u,c){var l=e.useRef(0),s=e.useRef(null),f=e.useRef(null),v=e.useRef(!1),d=R(n,r,o,i);var h=e.useRef(null),m=e.useRef(null);return[function(e){if(t){a.cancel(m.current),m.current=a((function(){h.current=null}),2);var n=e.deltaX,r=e.deltaY,o=e.shiftKey,i=n,g=r;("sx"===h.current||!h.current&&o&&r&&!n)&&(i=r,g=0,h.current="sx");var p=Math.abs(i),R=Math.abs(g);null===h.current&&(h.current=u&&p>R?"x":"y"),"y"===h.current?function(e,t){if(a.cancel(s.current),!d(!1,t)){var n=e;n._virtualHandled||(n._virtualHandled=!0,l.current+=t,f.current=t,w||n.preventDefault(),s.current=a((function(){var e=v.current?10:1;c(l.current*e,!1),l.current=0})))}}(e,g):function(e,t){c(t,!0),w||e.preventDefault()}(e,i)}},function(e){t&&(v.current=e.detail===f.current)}]}var E=function(){function e(){l(this,e),n(this,"maps",void 0),n(this,"id",0),n(this,"diffRecords",new Map),this.maps=Object.create(null)}return c(e,[{key:"set",value:function(e,t){this.diffRecords.set(e,this.maps[e]),this.maps[e]=t,this.id+=1}},{key:"get",value:function(e){return this.maps[e]}},{key:"resetRecord",value:function(){this.diffRecords.clear()}},{key:"getRecord",value:function(){return this.diffRecords}}]),e}();function S(e){var t=parseFloat(e);return isNaN(t)?0:t}var b=14/15;function y(e){return Math.floor(Math.pow(e,.5))}function x(e,t){return("touches"in e?e.touches[0]:e)[t?"pageX":"pageY"]-window[t?"scrollX":"scrollY"]}var L=e.forwardRef((function(r,u){var c=r.prefixCls,l=r.rtl,s=r.scrollOffset,f=r.scrollRange,v=r.onStartMove,d=r.onStopMove,h=r.onScroll,m=r.horizontal,g=r.spinSize,p=r.containerSize,w=r.style,R=r.thumbStyle,M=r.showScrollBar,E=e.useState(!1),S=i(E,2),b=S[0],y=S[1],L=e.useState(null),H=i(L,2),T=H[0],z=H[1],D=e.useState(null),k=i(D,2),N=k[0],Y=k[1],C=!l,B=e.useRef(),I=e.useRef(),P=e.useState(M),X=i(P,2),_=X[0],A=X[1],j=e.useRef(),O=function(){!0!==M&&!1!==M&&(clearTimeout(j.current),A(!0),j.current=setTimeout((function(){A(!1)}),3e3))},K=f-p||0,V=p-g||0,W=e.useMemo((function(){return 0===s||0===K?0:s/K*V}),[s,K,V]),F=e.useRef({top:W,dragging:b,pageY:T,startTop:N});F.current={top:W,dragging:b,pageY:T,startTop:N};var q=function(e){y(!0),z(x(e,m)),Y(F.current.top),v(),e.stopPropagation(),e.preventDefault()};e.useEffect((function(){var e=function(e){e.preventDefault()},t=B.current,n=I.current;return t.addEventListener("touchstart",e,{passive:!1}),n.addEventListener("touchstart",q,{passive:!1}),function(){t.removeEventListener("touchstart",e),n.removeEventListener("touchstart",q)}}),[]);var Z=e.useRef();Z.current=K;var G=e.useRef();G.current=V,e.useEffect((function(){if(b){var e,t=function(t){var n=F.current,r=n.dragging,o=n.pageY,i=n.startTop;a.cancel(e);var u=B.current.getBoundingClientRect(),c=p/(m?u.width:u.height);if(r){var l=(x(t,m)-o)*c,s=i;!C&&m?s-=l:s+=l;var f=Z.current,v=G.current,d=v?s/v:0,g=Math.ceil(d*f);g=Math.max(g,0),g=Math.min(g,f),e=a((function(){h(g,m)}))}},n=function(){y(!1),d()};return window.addEventListener("mousemove",t,{passive:!0}),window.addEventListener("touchmove",t,{passive:!0}),window.addEventListener("mouseup",n,{passive:!0}),window.addEventListener("touchend",n,{passive:!0}),function(){window.removeEventListener("mousemove",t),window.removeEventListener("touchmove",t),window.removeEventListener("mouseup",n),window.removeEventListener("touchend",n),a.cancel(e)}}}),[b]),e.useEffect((function(){return O(),function(){clearTimeout(j.current)}}),[s]),e.useImperativeHandle(u,(function(){return{delayHidden:O}}));var J="".concat(c,"-scrollbar"),Q={position:"absolute",visibility:_?null:"hidden"},U={position:"absolute",background:"rgba(0, 0, 0, 0.5)",borderRadius:99,cursor:"pointer",userSelect:"none"};return m?(Q.height=8,Q.left=0,Q.right=0,Q.bottom=0,U.height="100%",U.width=g,C?U.left=W:U.right=W):(Q.width=8,Q.top=0,Q.bottom=0,C?Q.right=0:Q.left=0,U.width="100%",U.height=g,U.top=W),e.createElement("div",{ref:B,className:o(J,n(n(n({},"".concat(J,"-horizontal"),m),"".concat(J,"-vertical"),!m),"".concat(J,"-visible"),_)),style:t(t({},Q),w),onMouseDown:function(e){e.stopPropagation(),e.preventDefault()},onMouseMove:O},e.createElement("div",{ref:I,className:o("".concat(J,"-thumb"),n({},"".concat(J,"-thumb-moving"),b)),style:t(t({},U),R),onMouseDown:q}))}));function H(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=e/(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0)*e;return isNaN(t)&&(t=0),t=Math.max(t,20),Math.floor(t)}var T=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],z=[],D={overflowY:"auto",overflowAnchor:"none"};function k(c,l){var w=c.prefixCls,k=void 0===w?"rc-virtual-list":w,N=c.className,Y=c.height,C=c.itemHeight,B=c.fullHeight,I=void 0===B||B,P=c.style,X=c.data,_=c.children,A=c.itemKey,j=c.virtual,O=c.direction,K=c.scrollWidth,V=c.component,W=void 0===V?"div":V,F=c.onScroll,q=c.onVirtualScroll,Z=c.onVisibleChange,G=c.innerProps,J=c.extraRender,Q=c.styles,U=c.showScrollBar,$=void 0===U?"optional":U,ee=f(c,T),te=e.useCallback((function(e){return"function"==typeof A?A(e):null==e?void 0:e[A]}),[A]),ne=function(t){var n=e.useState(0),r=i(n,2),o=r[0],u=r[1],a=e.useRef(new Map),c=e.useRef(new E),l=e.useRef(0);function s(){l.current+=1}function f(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];s();var t=function(){var e=!1;a.current.forEach((function(t,n){if(t&&t.offsetParent){var r=t.offsetHeight,o=getComputedStyle(t),i=o.marginTop,u=o.marginBottom,a=r+S(i)+S(u);c.current.get(n)!==a&&(c.current.set(n,a),e=!0)}})),e&&u((function(e){return e+1}))};if(e)t();else{l.current+=1;var n=l.current;Promise.resolve().then((function(){n===l.current&&t()}))}}return e.useEffect((function(){return s}),[]),[function(e,n){var r=t(e);a.current.get(r),n?(a.current.set(r,n),f()):a.current.delete(r)},f,c.current,o]}(te),re=i(ne,4),oe=re[0],ie=re[1],ue=re[2],ae=re[3],ce=!(!1===j||!Y||!C),le=e.useMemo((function(){return Object.values(ue.maps).reduce((function(e,t){return e+t}),0)}),[ue.id,ue.maps]),se=ce&&X&&(Math.max(C*X.length,le)>Y||!!K),fe="rtl"===O,ve=o(k,n({},"".concat(k,"-rtl"),fe),N),de=X||z,he=e.useRef(),me=e.useRef(),ge=e.useRef(),pe=e.useState(0),we=i(pe,2),Re=we[0],Me=we[1],Ee=e.useState(0),Se=i(Ee,2),be=Se[0],ye=Se[1],xe=e.useState(!1),Le=i(xe,2),He=Le[0],Te=Le[1],ze=function(){Te(!0)},De=function(){Te(!1)},ke={getKey:te};function Ne(e){Me((function(t){var n=function(e){var t=e;Number.isNaN(Qe.current)||(t=Math.min(t,Qe.current));return t=Math.max(t,0),t}("function"==typeof e?e(t):e);return he.current.scrollTop=n,n}))}var Ye=e.useRef({start:0,end:de.length}),Ce=e.useRef(),Be=p(de,te),Ie=i(Be,1)[0];Ce.current=Ie;var Pe=e.useMemo((function(){if(!ce)return{scrollHeight:void 0,start:0,end:de.length-1,offset:void 0};var e;if(!se)return{scrollHeight:(null===(e=me.current)||void 0===e?void 0:e.offsetHeight)||0,start:0,end:de.length-1,offset:void 0};for(var t,n,r,o=0,i=de.length,u=0;u<i;u+=1){var a=de[u],c=te(a),l=ue.get(c),s=o+(void 0===l?C:l);s>=Re&&void 0===t&&(t=u,n=o),s>Re+Y&&void 0===r&&(r=u),o=s}return void 0===t&&(t=0,n=0,r=Math.ceil(Y/C)),void 0===r&&(r=de.length-1),{scrollHeight:o,start:t,end:r=Math.min(r+1,de.length-1),offset:n}}),[se,ce,Re,de,ae,Y]),Xe=Pe.scrollHeight,_e=Pe.start,Ae=Pe.end,je=Pe.offset;Ye.current.start=_e,Ye.current.end=Ae,e.useLayoutEffect((function(){var e=ue.getRecord();if(1===e.size){var t=Array.from(e.keys())[0],n=e.get(t),r=de[_e];if(r&&void 0===n)if(te(r)===t){var o=ue.get(t)-C;Ne((function(e){return e+o}))}}ue.resetRecord()}),[Xe]);var Oe=e.useState({width:0,height:Y}),Ke=i(Oe,2),Ve=Ke[0],We=Ke[1],Fe=e.useRef(),qe=e.useRef(),Ze=e.useMemo((function(){return H(Ve.width,K)}),[Ve.width,K]),Ge=e.useMemo((function(){return H(Ve.height,Xe)}),[Ve.height,Xe]),Je=Xe-Y,Qe=e.useRef(Je);Qe.current=Je;var Ue=Re<=0,$e=Re>=Je,et=be<=0,tt=be>=K,nt=R(Ue,$e,et,tt),rt=function(){return{x:fe?-be:be,y:Re}},ot=e.useRef(rt()),it=v((function(e){if(q){var n=t(t({},rt()),e);ot.current.x===n.x&&ot.current.y===n.y||(q(n),ot.current=n)}}));function ut(e,t){var n=e;t?(d.flushSync((function(){ye(n)})),it()):Ne(n)}var at=function(e){var t=e,n=K?K-Ve.width:0;return t=Math.max(t,0),t=Math.min(t,n)},ct=v((function(e,t){t?(d.flushSync((function(){ye((function(t){return at(t+(fe?-e:e))}))})),it()):Ne((function(t){return t+e}))})),lt=M(ce,Ue,$e,et,tt,!!K,ct),st=i(lt,2),ft=st[0],vt=st[1];!function(t,n,r){var o,i=e.useRef(!1),u=e.useRef(0),a=e.useRef(0),c=e.useRef(null),l=e.useRef(null),f=function(e){if(i.current){var t=Math.ceil(e.touches[0].pageX),n=Math.ceil(e.touches[0].pageY),o=u.current-t,c=a.current-n,s=Math.abs(o)>Math.abs(c);s?u.current=t:a.current=n;var f=r(s,s?o:c,!1,e);f&&e.preventDefault(),clearInterval(l.current),f&&(l.current=setInterval((function(){s?o*=b:c*=b;var e=Math.floor(s?o:c);(!r(s,e,!0)||Math.abs(e)<=.1)&&clearInterval(l.current)}),16))}},v=function(){i.current=!1,o()},d=function(e){o(),1!==e.touches.length||i.current||(i.current=!0,u.current=Math.ceil(e.touches[0].pageX),a.current=Math.ceil(e.touches[0].pageY),c.current=e.target,c.current.addEventListener("touchmove",f,{passive:!1}),c.current.addEventListener("touchend",v,{passive:!0}))};o=function(){c.current&&(c.current.removeEventListener("touchmove",f),c.current.removeEventListener("touchend",v))},s((function(){return t&&n.current.addEventListener("touchstart",d,{passive:!0}),function(){var e;null===(e=n.current)||void 0===e||e.removeEventListener("touchstart",d),o(),clearInterval(l.current)}}),[t])}(ce,he,(function(e,t,n,r){var o=r;return!nt(e,t,n)&&((!o||!o._virtualHandled)&&(o&&(o._virtualHandled=!0),ft({preventDefault:function(){},deltaX:e?t:0,deltaY:e?0:t}),!0))})),function(t,n,r){e.useEffect((function(){var e=n.current;if(t&&e){var o,i,u=!1,c=function(){a.cancel(o)},l=function e(){c(),o=a((function(){r(i),e()}))},s=function(e){if(!e.target.draggable&&0===e.button){var t=e;t._virtualHandled||(t._virtualHandled=!0,u=!0)}},f=function(){u=!1,c()},v=function(t){if(u){var n=x(t,!1),r=e.getBoundingClientRect(),o=r.top,a=r.bottom;n<=o?(i=-y(o-n),l()):n>=a?(i=y(n-a),l()):c()}};return e.addEventListener("mousedown",s),e.ownerDocument.addEventListener("mouseup",f),e.ownerDocument.addEventListener("mousemove",v),function(){e.removeEventListener("mousedown",s),e.ownerDocument.removeEventListener("mouseup",f),e.ownerDocument.removeEventListener("mousemove",v),c()}}}),[t])}(se,he,(function(e){Ne((function(t){return t+e}))})),s((function(){function e(e){var t=Ue&&e.detail<0,n=$e&&e.detail>0;!ce||t||n||e.preventDefault()}var t=he.current;return t.addEventListener("wheel",ft,{passive:!1}),t.addEventListener("DOMMouseScroll",vt,{passive:!0}),t.addEventListener("MozMousePixelScroll",e,{passive:!1}),function(){t.removeEventListener("wheel",ft),t.removeEventListener("DOMMouseScroll",vt),t.removeEventListener("MozMousePixelScroll",e)}}),[ce,Ue,$e]),s((function(){if(K){var e=at(be);ye(e),it({x:e})}}),[Ve.width,K]);var dt=function(){var e,t;null===(e=Fe.current)||void 0===e||e.delayHidden(),null===(t=qe.current)||void 0===t||t.delayHidden()},ht=function(n,r,o,c,l,f,v,d){var h=e.useRef(),m=e.useState(null),g=i(m,2),p=g[0],w=g[1];return s((function(){if(p&&p.times<10){if(!n.current)return void w((function(e){return t({},e)}));f();var e=p.targetAlign,i=p.originAlign,u=p.index,a=p.offset,s=n.current.clientHeight,d=!1,h=e,m=null;if(s){for(var g=e||i,R=0,M=0,E=0,S=Math.min(r.length-1,u),b=0;b<=S;b+=1){var y=l(r[b]);M=R;var x=o.get(y);R=E=M+(void 0===x?c:x)}for(var L="top"===g?a:s-a,H=S;H>=0;H-=1){var T=l(r[H]),z=o.get(T);if(void 0===z){d=!0;break}if((L-=z)<=0)break}switch(g){case"top":m=M-a;break;case"bottom":m=E-s+a;break;default:var D=n.current.scrollTop;M<D?h="top":E>D+s&&(h="bottom")}null!==m&&v(m),m!==p.lastTop&&(d=!0)}d&&w(t(t({},p),{},{times:p.times+1,targetAlign:h,lastTop:m}))}}),[p,n.current]),function(e){if(null!=e){if(a.cancel(h.current),"number"==typeof e)v(e);else if(e&&"object"===u(e)){var t,n=e.align;t="index"in e?e.index:r.findIndex((function(t){return l(t)===e.key}));var o=e.offset;w({times:0,index:t,offset:void 0===o?0:o,originAlign:n})}}else d()}}(he,de,ue,C,te,(function(){return ie(!0)}),Ne,dt);e.useImperativeHandle(l,(function(){return{nativeElement:ge.current,getScrollInfo:rt,scrollTo:function(e){var t;(t=e)&&"object"===u(t)&&("left"in t||"top"in t)?(void 0!==e.left&&ye(at(e.left)),ht(e.top)):ht(e)}}})),s((function(){if(Z){var e=de.slice(_e,Ae+1);Z(e,de)}}),[_e,Ae,de]);var mt=function(t,n,r,o){var u=e.useMemo((function(){return[new Map,[]]}),[t,r.id,o]),a=i(u,2),c=a[0],l=a[1];return function(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,u=c.get(e),a=c.get(i);if(void 0===u||void 0===a)for(var s=t.length,f=l.length;f<s;f+=1){var v,d=t[f],h=n(d);c.set(h,f);var m=null!==(v=r.get(h))&&void 0!==v?v:o;if(l[f]=(l[f-1]||0)+m,h===e&&(u=f),h===i&&(a=f),void 0!==u&&void 0!==a)break}return{top:l[u-1]||0,bottom:l[a]}}}(de,te,ue,C),gt=null==J?void 0:J({start:_e,end:Ae,virtual:se,offsetX:be,offsetY:je,rtl:fe,getSize:mt}),pt=function(t,n,r,o,i,u,a,c){var l=c.getKey;return t.slice(n,r+1).map((function(t,r){var c=a(t,n+r,{style:{width:o},offsetX:i}),s=l(t);return e.createElement(g,{key:s,setRef:function(e){return u(t,e)}},c)}))}(de,_e,Ae,K,be,oe,_,ke),wt=null;Y&&(wt=t(n({},I?"height":"maxHeight",Y),D),ce&&(wt.overflowY="hidden",K&&(wt.overflowX="hidden"),He&&(wt.pointerEvents="none")));var Rt={};return fe&&(Rt.dir="rtl"),e.createElement("div",r({ref:ge,style:t(t({},P),{},{position:"relative"}),className:ve},Rt,ee),e.createElement(h,{onResize:function(e){We({width:e.offsetWidth,height:e.offsetHeight})}},e.createElement(W,{className:"".concat(k,"-holder"),style:wt,ref:he,onScroll:function(e){var t=e.currentTarget.scrollTop;t!==Re&&Ne(t),null==F||F(e),it()},onMouseEnter:dt},e.createElement(m,{prefixCls:k,height:Xe,offsetX:be,offsetY:je,scrollWidth:K,onInnerResize:ie,ref:me,innerProps:G,rtl:fe,extra:gt},pt))),se&&Xe>Y&&e.createElement(L,{ref:Fe,prefixCls:k,scrollOffset:Re,scrollRange:Xe,rtl:fe,onScroll:ut,onStartMove:ze,onStopMove:De,spinSize:Ge,containerSize:Ve.height,style:null==Q?void 0:Q.verticalScrollBar,thumbStyle:null==Q?void 0:Q.verticalScrollBarThumb,showScrollBar:$}),se&&K>Ve.width&&e.createElement(L,{ref:qe,prefixCls:k,scrollOffset:be,scrollRange:K,rtl:fe,onScroll:ut,onStartMove:ze,onStopMove:De,spinSize:Ze,containerSize:Ve.width,horizontal:!0,style:null==Q?void 0:Q.horizontalScrollBar,thumbStyle:null==Q?void 0:Q.horizontalScrollBarThumb,showScrollBar:$}))}var N=e.forwardRef(k);N.displayName="List";export{N as L};
