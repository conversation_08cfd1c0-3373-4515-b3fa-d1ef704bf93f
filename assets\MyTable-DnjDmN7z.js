import{r as e,b0 as t,b5 as n}from"./index-Cak6rALw.js";import{u as l,s as r,c as i,S as s}from"./MyApp-DW5WH4Ub.js";import{u as a}from"./index-ByRdMNW-.js";import{T as o}from"./index-BzMPigdO.js";import{F as c}from"./Table-BO80-bCE.js";import{R as u}from"./row-BMfM-of4.js";import{I as d}from"./index-Bg865k-U.js";import"./DownOutlined-B-JcS-29.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./EyeOutlined-CNKPohhw.js";const h=e.forwardRef(((h,m)=>{const{ti:p,language:f}=l(),{virtual:g=!1,data:S=[],columns:x=[],size:y="middle",selectable:v=!1,searchable:j=!1,searchPlaceholder:w=p({en:"Search",vi:"Tìm kiếm"}),loading:k=!1,pageSize:b=0,keyExtractor:C=(e,t)=>t+"",onSearchRow:R,renderTitle:_,style:B,footer:T,expandable:z}=h,[D,I]=e.useState(""),[M,E]=e.useState(""),[F,H]=e.useState([]),[L,O]=e.useState(!1),[P,K]=e.useState(!1),q=a(I,500),U=e.useRef(!1),A=e=>{E(e),q(e),U.current=!0};e.useImperativeHandle(m,(()=>({getDataSelected:()=>F,setDataSelected:e=>{H(e)},hasDataSelected:()=>!!(null==F?void 0:F.length),setShowSelectedOnly:O,setSearch:A,clearFilter:({search:e=!0,dataSelected:t=!0,showSelectedOnly:n=!0}={})=>{e&&A(""),t&&H([]),n&&O(!1)}})));const W=e.useMemo((()=>{const e=new Map(x.map(((e,t)=>[e.key,t]))),t=(D?S.filter((t=>R?R(D,t):Object.entries(t).some((([n,l],i)=>{var s,a;let o=e.get(n)||-1;return!(o<0)&&(!(null==(s=x[o])?void 0:s.disableSearch)&&((null==(a=x[o])?void 0:a.onSearch)?x[o].onSearch(D,l,t,P):"string"==typeof l&&(P?null==l?void 0:l.includes(D):r(D,l))))})))):S).map(((e,t)=>({...e,key:C(e,t)})));if(L&&(null==F?void 0:F.length)){const e=new Set(F.map(C));return t.filter(((t,n)=>e.has(C(t,n))))}return t}),[S,D,P,L,F,x,C,R]),$=e.useMemo((()=>x.find((e=>e.rangeFilter))?x.map((e=>e.rangeFilter?{...e,filterDropdown:({setSelectedKeys:e,selectedKeys:l,confirm:r,clearFilters:s})=>{var a,c;return t.jsxs("div",{style:{padding:8},children:[t.jsx(i.Text,{strong:!0,style:{marginBottom:8,display:"block"},children:p({en:"Range filter",vi:"Lọc giá trị"})}),t.jsx(o,{placeholder:p({vi:"Bé nhất",en:"Min"}),value:(null==(a=l[0])?void 0:a.min)??null,onChange:t=>e([{...l[0],min:t}]),style:{marginBottom:8,display:"block",width:"100%"}}),t.jsx(o,{placeholder:p({vi:"Lớn nhất",en:"Max"}),value:(null==(c=l[0])?void 0:c.max)??null,onChange:t=>e([{...l[0],max:t}]),style:{marginBottom:8,display:"block",width:"100%"}}),t.jsx(n,{type:"primary",size:"small",onClick:r,style:{width:90,marginRight:8},children:p({en:"Apply",vi:"Áp dụng"})}),t.jsx(n,{type:"link",size:"small",onClick:s,style:{width:90},children:p({en:"Reset",vi:"Đặt lại"})})]})},onFilter:(t,n)=>{const{min:l,max:r}=t||{},i=e.rangeFilter.getValue(n);return i>=(l??-1/0)&&i<=(r??1/0)},filterIcon:e=>t.jsx("i",{className:"fa fa-filter",style:{color:e?"#1890ff":void 0}})}:e)):x),[x]);e.useEffect((()=>{if(!(null==F?void 0:F.length))return;let e=new Set(S.map(C)),t=F.filter(((t,n)=>e.has(C(t,n))));(null==t?void 0:t.length)!==(null==F?void 0:F.length)&&H(t)}),[S,F]),e.useEffect((()=>{!(null==F?void 0:F.length)&&L&&O(!1)}),[F.length,L]);const J=e.useRef([]),N=e.useRef([]),V=e.useRef(!1);e.useEffect((()=>{!V.current&&(null==F?void 0:F.length)&&N.current.push(F),V.current=!1,J.current=F}),[F]);const G=e.useRef(W),Q=e.useRef(W);e.useEffect((()=>{if(D){const e=new Set(W.map(C)),t=new Set(G.current.map(C)),n=new Set([...e].filter((e=>t.has(e))));Q.current=G.current.filter((e=>n.has(C(e))))}else Q.current=W,G.current=W}),[W,D]);const X=(e,t)=>{const n=new Set(S.map(C)),l=new Set(W.map(C)),r=new Set(J.current.map(C)),i=new Set(e),s=new Set([...n].filter((e=>!l.has(e)))),a=new Set([...e].filter((e=>!r.has(e)))),o=new Set([...r].filter((e=>!i.has(e)&&!s.has(e)))),c=[...r,...a].filter((e=>!o.has(e))).map((e=>S.find((t=>C(t)===e)))).filter(Boolean);H(c)},Y=e.useMemo((()=>{if(!v)return;const e=[{key:"undo",text:p({en:"Undo selected",vi:"Hoàn tác lựa chọn"}),onSelect:()=>{V.current=!0;const e=N.current.pop();console.log(e),H(N.current[N.current.length-1]||[])}},{key:"select_all",text:p({en:"Select all",vi:"Chọn tất cả"})+" ("+S.length+")",onSelect:()=>H([...Q.current])},{key:"invert_selection",text:p({en:"Invert selection",vi:"Đảo ngược lựa chọn"}),onSelect:()=>H(Q.current.filter((e=>!F.find((t=>C(t)===C(e))))))},{key:"unselect_all",text:p({en:"Unselect all",vi:"Bỏ chọn tất cả"}),onSelect:()=>{O(!1);const e=new Set(Q.current.map(C));H(F.filter((t=>!e.has(C(t)))))}},{key:"show_selected_only",text:(L?"✅ ":"❌ ")+p({en:"Show selected only",vi:"Chỉ hiển thị đã chọn"})+" ("+F.length+")",onSelect:()=>{A(""),O(!L)}}].filter(Boolean);return D&&e.push({key:"select_all_insearch",text:p({en:"Select all - search results",vi:"Chọn tất cả - kết quả tìm kiếm"})+" ("+W.length+")",onSelect:()=>H([...Q.current])},{key:"invert_selection_insearch",text:p({en:"Invert selection - search results",vi:"Đảo ngược lựa chọn - kết quả tìm kiếm"}),onSelect:()=>H(Q.current.filter((e=>!F.find((t=>C(t)===C(e))))))},{key:"unselect_all_insearch",text:p({en:"Unselect all - search results",vi:"Bỏ chọn tất cả - kết quả tìm kiếm"}),onSelect:()=>{O(!1);const e=new Set(Q.current.map(C));H(F.filter((t=>!e.has(C(t)))))}}),{selectedRowKeys:F.map(C),onChange:X,selections:e,type:"checkbox",getCheckboxProps:e=>({style:{minWidth:50,minHeight:50,alignItems:"center",justifyContent:"center"}})}}),[S,W,F,L,v,f,D,X]);return t.jsx(c,{sticky:{offsetHeader:0},fixedHeader:!0,size:y,loading:k,tableLayout:"auto",dataSource:W,columns:$,showSorterTooltip:!1,rowKey:C,onChange:(e,t,n,l)=>{Q.current=l.currentDataSource??[],G.current=l.currentDataSource??[]},virtual:g,scroll:g?{x:"max-content",y:500}:{x:"max-content"},rowSelection:Y,title:()=>t.jsxs(u,{justify:"space-between",style:{margin:"5px"},gutter:[8,8],align:"middle",children:["function"==typeof _&&t.jsx(u,{align:"middle",children:t.jsx(s,{wrap:!0,children:_(F)})}),j&&t.jsx(s,{children:t.jsx(d.Search,{placeholder:w,value:M,onChange:e=>A(e.target.value),style:{maxWidth:300}})})]}),pagination:!g&&(-1!=b&&{position:["bottomCenter"],showSizeChanger:!0,showTotal:(e,t)=>p({en:`Total ${e} items`,vi:`Tổng ${e} dòng`}),size:"default",defaultPageSize:b||10,style:{alignItems:"center"}}),style:B,footer:T?()=>T:void 0,expandable:z})}));export{h as default};
