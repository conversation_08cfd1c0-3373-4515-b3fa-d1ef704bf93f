import{y as n,x as l,az as e,X as i,W as o}from"./MyApp-DW5WH4Ub.js";import{extractVideoVariants as t}from"./videos-D2LbKcXH.js";import"./index-Cak6rALw.js";async function d({postID:e,comment:t,feedLocation:d="PERMALINK",groupID:u=null}){const a=await n({fb_api_req_friendly_name:"useCometUFICreateCommentMutation",variables:{feedLocation:d,feedbackSource:2,groupID:u,input:{actor_id:await o(),attachments:null,feedback_id:btoa(`feedback:${e}`),formatting_style:null,message:{ranges:[],text:t},vod_video_timestamp:null,is_tracking_encrypted:!0,feedback_source:"OBJECT"},inviteShortLinkKey:null,renderLocation:null,scale:2,useDefaultActor:!1,focusCommentID:null,__relay_internal__pv__IsWorkUserrelayprovider:!1},doc_id:"9117686168269068"}),r=l(a);console.log(r);return i(r,"comment_create.feedback.url")}var u=(n=>(n.PHOTO="Photo",n.VIDEO="Video",n.STICKER="Sticker",n.GIF="GenericAttachmentMedia",n))(u||{}),a=(n=>(n.AUTHOR="author",n.TOP_FAN="top_fan",n.RISING_FAN="rising_fan",n))(a||{});const r={author:{en:"Author",vi:"Tác giả"},top_fan:{en:"Top Fan",vi:"Fan cứng"},rising_fan:{en:"Rising Fan",vi:"Fan đang lên"}};var v=(n=>(n.RECENT_ACTIVITY="RECENT_ACTIVITY_INTENT_V1",n.RANKED_FILTERED="RANKED_FILTERED_INTENT_V1",n.ADMIN_HIDDEN="ADMIN_HIDDEN_INTENT_V1",n.CHRONOLOGICAL_UNFILTERED="CHRONOLOGICAL_UNFILTERED_INTENT_V1",n.STARS_INTENT_V1="STARS_INTENT_V1",n))(v||{});const s={CHRONOLOGICAL_UNFILTERED_INTENT_V1:{en:"All comments",vi:"Tất cả bình luận",icon:"fa-solid fa-comments",description:{vi:"Hiển thị tất cả bình luận, bao gồm cả nội dung có thể là spam.",en:"Show all comments, including potential spam."}},RECENT_ACTIVITY_INTENT_V1:{en:"Newest",vi:"Mới nhất",icon:"fa-solid fa-clock-rotate-left",description:{vi:"Hiển thị tất cả bình luận, theo thứ tự là các bình luận mới nhất trước tiên.",en:"Show all comments with the newest comments first."}},RANKED_FILTERED_INTENT_V1:{en:"Most relevant",vi:"Phù hợp nhất",icon:"fa-solid fa-thumbs-up",description:{vi:"Hiển thị bình luận của bạn bè và những bình luận có nhiều lượt tương tác nhất trước tiên.",en:"Show friends' comments and the most engaging comments first."}},ADMIN_HIDDEN_INTENT_V1:{en:"Hidden",vi:"Đã ẩn",icon:"fa-solid fa-eye-slash",description:{vi:"Hiển thị bình luận đã ẩn của bạn trong ngữ cảnh gốc của bài viết. Chỉ bạn mới nhìn thấy nội dung này.",en:"Show your hidden comments in their original context from the post. Only you can see this."}},STARS_INTENT_V1:{en:"Sent with Stars",vi:"Đã gửi kèm Sao",icon:"fa-solid fa-star",description:{vi:"Chỉ hiển thị bình luận kèm theo Sao.",en:"Show only comments that are sent with Stars."}}};async function c({postId:o,cursor:t="",type:d="RECENT_ACTIVITY_INTENT_V1"}){const u=btoa("feedback:"+o),a=t?await n({fb_api_req_friendly_name:"CommentsListComponentsPaginationQuery",variables:{commentsAfterCount:-1,commentsAfterCursor:t,commentsBeforeCount:null,commentsBeforeCursor:null,commentsIntentToken:d,feedLocation:"DEDICATED_COMMENTING_SURFACE",focusCommentID:null,scale:2,useDefaultActor:!1,id:u,__relay_internal__pv__IsWorkUserrelayprovider:!1},doc_id:"28843177298663113"}):await n({fb_api_req_friendly_name:"CommentListComponentsRootQuery",variables:{commentsIntentToken:d,feedLocation:"DEDICATED_COMMENTING_SURFACE",feedbackSource:110,focusCommentID:null,scale:2,useDefaultActor:!1,id:u,__relay_internal__pv__IsWorkUserrelayprovider:!1},doc_id:"24020614617526524"}),r=l(a,{},!0);console.log("json",r);const{edges:v=[],page_info:s={}}=e(r)||{},c=v.map((n=>_(n,s))),m={total:i(r,"comment_rendering_instance.comments.total_count")||0,comments:c};return console.log("result",m),m}function _(n,l){var e,o,t,d,u,a,r,v,s,c,_,h,p,f,g,I,y,T,b,N,E,C,A,D,k,w,R,S,L,M,V,O,F,x,H,G,U,P,q,K,B,W,j,Y,Q,$,z,J,X,Z,nn,ln,en;const on=(null==(d=null==(t=null==(o=null==(e=null==n?void 0:n.node)?void 0:e.feedback)?void 0:o.top_reactions)?void 0:t.edges)?void 0:d.map((n=>{var l;return{id:null==(l=null==n?void 0:n.node)?void 0:l.id,count:null==n?void 0:n.reaction_count}})))||[],tn=(null==(u=null==n?void 0:n.node)?void 0:u.preferred_body)||(null==(a=null==n?void 0:n.node)?void 0:a.body_renderer)||(null==(r=null==n?void 0:n.node)?void 0:r.body);return{id:null==(v=null==n?void 0:n.node)?void 0:v.id,legacy_fbid:null==(s=null==n?void 0:n.node)?void 0:s.legacy_fbid,depth:null==(c=null==n?void 0:n.node)?void 0:c.depth,url:null==(h=null==(_=null==n?void 0:n.node)?void 0:_.feedback)?void 0:h.url,created_time:1e3*((null==(p=null==n?void 0:n.node)?void 0:p.created_time)||0),text:(null==tn?void 0:tn.text)||"",tags:null==(f=null==tn?void 0:tn.ranges)?void 0:f.map((n=>{var l,e,i,o,t;return{id:null==(l=null==n?void 0:n.entity)?void 0:l.id,type:null==(e=null==n?void 0:n.entity)?void 0:e.__typename,url:null==(i=null==n?void 0:n.entity)?void 0:i.profile_url,name:null==(o=null==n?void 0:n.entity)?void 0:o.short_name,length:null==n?void 0:n.length,offset:null==n?void 0:n.offset,text:(null==(t=null==tn?void 0:tn.text)?void 0:t.slice((null==n?void 0:n.offset)||0,((null==n?void 0:n.offset)||0)+((null==n?void 0:n.length)||0)))||""}})),author:{id:null==(I=null==(g=null==n?void 0:n.node)?void 0:g.author)?void 0:I.id,avatar:i(null==(y=null==n?void 0:n.node)?void 0:y.author,"uri"),name:null==(b=null==(T=null==n?void 0:n.node)?void 0:T.author)?void 0:b.name,gender:null==(E=null==(N=null==n?void 0:n.node)?void 0:N.author)?void 0:E.gender,url:null==(A=null==(C=null==n?void 0:n.node)?void 0:C.author)?void 0:A.url,is_verified:null==(k=null==(D=null==n?void 0:n.node)?void 0:D.author)?void 0:k.is_verified,short_name:null==(R=null==(w=null==n?void 0:n.node)?void 0:w.author)?void 0:R.short_name,subscribe_status:null==(L=null==(S=null==n?void 0:n.node)?void 0:S.author)?void 0:L.subscribe_status,work_info:null==(V=null==(M=null==n?void 0:n.node)?void 0:M.author)?void 0:V.work_info},badges:null==(x=null==(F=null==(O=null==n?void 0:n.node)?void 0:O.discoverable_identity_badges_web)?void 0:F.map((n=>{const l=null==n?void 0:n.identity_badge_type;return{type:l,title:null==n?void 0:n.information_title,description:null==n?void 0:n.information_description,icon:"https://www.facebook.com"+(null==n?void 0:n.grey_badge_asset),earned:(null==n?void 0:n.is_earned)||"author"===l}})))?void 0:x.filter((n=>n.earned)),signals:null==(P=null==(U=null==(G=null==(H=null==n?void 0:n.node)?void 0:H.author_user_signals_renderer)?void 0:G.user_signals_info)?void 0:U.displayed_user_signals)?void 0:P.map((n=>{var l,e,i;return{id:null==n?void 0:n.id,type:null==n?void 0:n.signal_type_id,title:null==(l=null==n?void 0:n.title)?void 0:l.text,icon:(null==(e=null==n?void 0:n.lightModeImage)?void 0:e.uri)||(null==(i=null==n?void 0:n.darkModeImage)?void 0:i.uri)}})),reply:{count:null==(B=null==(K=null==(q=null==n?void 0:n.node)?void 0:q.feedback)?void 0:K.replies_fields)?void 0:B.count,total_count:null==(Y=null==(j=null==(W=null==n?void 0:n.node)?void 0:W.feedback)?void 0:j.replies_fields)?void 0:Y.total_count,expansion_token:null==(z=null==($=null==(Q=null==n?void 0:n.node)?void 0:Q.feedback)?void 0:$.expansion_info)?void 0:z.expansion_token,comments:[]},react:{total:on.reduce(((n,l)=>n+l.count),0)||Number((null==(Z=null==(X=null==(J=null==n?void 0:n.node)?void 0:J.feedback)?void 0:X.reactors)?void 0:Z.count_reduced)||0),each:on},attachments:m((null==(nn=null==n?void 0:n.node)?void 0:nn.attachments)||[]),parent_id:null==(en=null==(ln=null==n?void 0:n.node)?void 0:ln.comment_parent)?void 0:en.id,cursor:null==l?void 0:l.end_cursor}}function m(n){return n.map((n=>{var l,e,o,d,u,a,r,v,s,c,_,m,h,p,f,g,I,y,T,b;const N=i(n,"attachment");switch((null==(l=null==N?void 0:N.target)?void 0:l.__typename)||(null==(e=null==N?void 0:N.media)?void 0:e.__typename)){case"Photo":const n=null==N?void 0:N.media;return{id:null==(o=null==N?void 0:N.media)?void 0:o.id,type:"Photo",url:null==N?void 0:N.url,image:null==(d=null==n?void 0:n.image)?void 0:d.uri,width:null==(u=null==n?void 0:n.image)?void 0:u.width,height:null==(a=null==n?void 0:n.image)?void 0:a.height,description:null==n?void 0:n.accessibility_caption};case"Video":const l=null==N?void 0:N.media;return(null==l?void 0:l.animated_image)?{id:null==l?void 0:l.id,type:"GenericAttachmentMedia",url:null==N?void 0:N.url,image:null==(r=null==l?void 0:l.animated_image)?void 0:r.uri}:{id:null==l?void 0:l.id,type:"Video",url:null==l?void 0:l.permalink_url,image:null==(s=null==(v=null==l?void 0:l.preferred_thumbnail)?void 0:v.image)?void 0:s.uri,duration:null==l?void 0:l.length_in_second,width:null==l?void 0:l.width,height:null==l?void 0:l.height,video:{id:null==l?void 0:l.id,length:null==l?void 0:l.length_in_second,width:null==l?void 0:l.width,height:null==l?void 0:l.height,source:null==(m=null==(_=null==(c=i(l,"progressive_urls"))?void 0:c.map((n=>{var l;return{url:null==n?void 0:n.progressive_url,quality:(null==(l=null==n?void 0:n.metadata)?void 0:l.quality)||""}})).sort(((n,l)=>n.quality.localeCompare(l.quality))))?void 0:_[0])?void 0:m.url,variants:t(i(l,"dash_manifests.manifest_xml"))}};case"Sticker":const e=null==N?void 0:N.media;return{id:null==e?void 0:e.id,type:"Sticker",url:null==(h=null==e?void 0:e.sprite_image)?void 0:h.uri,image:null==(p=null==e?void 0:e.image)?void 0:p.uri,width:null==(f=null==e?void 0:e.image)?void 0:f.width,height:null==(g=null==e?void 0:e.image)?void 0:g.height,description:(null==(I=null==e?void 0:e.pack)?void 0:I.name)||(null==e?void 0:e.label)};case"GenericAttachmentMedia":const E=null==N?void 0:N.media;return{id:null==E?void 0:E.id,type:"GenericAttachmentMedia",url:null==N?void 0:N.url,image:(null==(T=null==(y=null==E?void 0:E.media)?void 0:y.animated_image)?void 0:T.uri)||(null==(b=null==E?void 0:E.image)?void 0:b.uri)}}return null})).filter(Boolean)}async function h({commentId:i,expansionToken:o,cursor:t=null}){var d;const u=atob(i),a=null==(d=null==u?void 0:u.split(":"))?void 0:d[1],[r,v]=(null==a?void 0:a.split("_"))||[],s=btoa(`feedback:${r}_${v}`),c=await n({fb_api_req_friendly_name:"Depth1CommentsListPaginationQuery",variables:{clientKey:null,expansionToken:o,feedLocation:"DEDICATED_COMMENTING_SURFACE",focusCommentID:null,repliesAfterCount:null,repliesAfterCursor:t||null,repliesBeforeCount:null,repliesBeforeCursor:null,scale:2,useDefaultActor:!1,id:s,__relay_internal__pv__IsWorkUserrelayprovider:!1},doc_id:"9847933891911243"}),m=l(c,{},!0);console.log("depth 1",m);const{edges:h=[],page_info:p={}}=e(m)||{};return h.map((n=>_(n,p)))}export{u as CommentAttachmentType,v as CommentIntentToken,s as CommentIntentTokenNames,a as IdentityBadgeType,r as IdentityBadgeTypeNames,d as createComment,m as extractCommentAttachments,_ as extractCommentData,c as getPostComments,h as getReplyComments};
