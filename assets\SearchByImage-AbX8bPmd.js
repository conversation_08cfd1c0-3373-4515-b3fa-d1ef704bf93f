import{r as e,b0 as t,b5 as i}from"./index-Cak6rALw.js";import s from"./useCacheState-CAnxkewm.js";import{u as o,d as n,S as a,c as r,t as l,A as c}from"./MyApp-DW5WH4Ub.js";import{A as p}from"./index-fI_muw4K.js";import{I as m}from"./index-Bg865k-U.js";import{I as h}from"./index-CDSnY0KE.js";import{M as d}from"./index-Bx3o6rwA.js";import{U as u}from"./index-BVNdrzmU.js";import"./PurePanel-BvHjNOhQ.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./move-ESLFEEsv.js";import"./DownOutlined-B-JcS-29.js";import"./SearchOutlined--mXbzQ68.js";import"./EyeOutlined-CNKPohhw.js";import"./addEventListener-C4tIKh7N.js";import"./useBreakpoint-CPcdMRfj.js";import"./progress-ApOaEpgr.js";const g="Google lens | https://lens.google.com/uploadbyurl?url=#t#\nGoogle image | https://www.google.com/searchbyimage?safe=off&sbisrc=1&image_url=#t#\nYandex | https://yandex.com/images/search?source=collections&rpt=imageview&url=#t#\nSauceNAO | https://saucenao.com/search.php?db=999&url=#t#\nIQDB | https://iqdb.org/?url=#t#\n3D IQDB | https://3d.iqdb.org/?url=#t#\nBaidu | https://graph.baidu.com/details?isfromtusoupc=1&tn=pc&carousel=0&promotion_name=pc_image_shituindex&extUiData%5bisLogoShow%5d=1&image=#t#\nBing | https://www.bing.com/images/search?view=detailv2&iss=sbi&form=SBIVSP&sbisrc=UrlPaste&q=imgurl:#t#\nTinEye | https://www.tineye.com/search?url=#t#\nSogou | https://pic.sogou.com/ris?query=#t#\n360 | http://st.so.com/stu?imgurl=#t#\nWhatAnime | https://trace.moe/?url=#t#\nAscii2D | https://ascii2d.net/search/url/#t#\nTrace Moe | https://trace.moe/?url=#t#\nKarmaDecay | http://karmadecay.com/#t#\nImgOps | https://imgops.com/#b#\nQRCode | https://hoothin.com/qrcode/##t#\nQR decode | https://zxing.org/w/decode?full=true&u=#t#\nQR decode 2 | https://hoothin.com/qrdecode/##t#".split("\n").map((e=>({name:e.split(" | ")[0],url:e.split(" | ")[1]})));function j(){const{ti:j}=o(),{message:x}=n(),[f,v]=e.useState(""),[w,y]=s("SearchByImage.history",[]),[b,S]=e.useState(!1);return t.jsxs(a,{wrap:!0,align:"start",size:15,children:[t.jsxs(a,{direction:"vertical",children:[t.jsx(r.Text,{children:j({en:"Enter image URL:",vi:"Nhập URL hình ảnh:"})}),t.jsx(p,{options:w.map((e=>({value:e}))),optionRender:e=>t.jsxs(a,{children:[t.jsx(h,{src:e.value,width:20,height:20,preview:!1,style:{objectFit:"contain"}}),e.value]}),value:f,onChange:e=>v(e),children:t.jsx(m,{value:f,onChange:e=>v(e.target.value),placeholder:j({en:"Enter image URL",vi:"Nhập URL hình ảnh"}),allowClear:!0})}),t.jsx(r.Text,{children:j({en:"Or upload image:",vi:"Hoặc tải lên ảnh:"})}),t.jsx(i,{icon:t.jsx("i",{className:"fa-solid fa-upload"}),onClick:()=>S(!0),children:j({en:"Upload",vi:"Tải lên"})}),t.jsx(d,{open:b,onCancel:()=>S(!1),footer:null,centered:!0,title:j({en:"Upload Image",vi:"Tải lên ảnh"}),children:t.jsxs(u.Dragger,{action:"https://tmpfiles.org/api/v1/upload",listType:"picture",maxCount:1,accept:"image/*",onChange:e=>{var t,i;if("done"===e.file.status){l("SearchByImage:Upload");const s=null==(i=null==(t=e.file.response)?void 0:t.data)?void 0:i.url;s?(v(s.replace("tmpfiles.org/","tmpfiles.org/dl/")),S(!1)):x.error(j({en:"Upload failed",vi:"Tải lên lỗi"})+" "+e.file.name)}},onRemove:e=>{v("")},style:{width:"100%"},children:[t.jsx("p",{className:"ant-upload-drag-icon",children:t.jsx("i",{className:"fa-solid fa-upload",style:{fontSize:32}})}),t.jsx("p",{className:"ant-upload-text",children:j({en:"Click or drag file to this area to upload",vi:"Nhấn hoặc kéo thả ảnh vào đây để tải lên"})})]})}),f&&t.jsx(h,{src:f,width:"100%",onLoad:()=>y([f,...w.filter((e=>e!==f))]),style:{objectFit:"contain",maxHeight:200,minHeight:100}})]}),t.jsxs(a,{direction:"vertical",style:f?{}:{pointerEvents:"none",opacity:.5},children:[!f&&t.jsx(c,{type:"warning",showIcon:!0,message:j({en:"Please enter image URL first",vi:"Vui lòng nhập URL hình ảnh trước"})}),t.jsx(r.Text,{children:j({en:"Select search engine:",vi:"Chọn công cụ tìm kiếm:"})}),g.map(((e,i)=>{const s=e.url.replace("#b#",f.replace(/https?:\/\//i,"")).replace("#t#",encodeURIComponent(f));return t.jsx("a",{onClick:t=>{t.stopPropagation(),l("SearchByImage:"+e.name),window.open(s,"_blank")},children:e.name},i)}))]})]})}export{j as default};
