/**
 * Enhanced VIP Hook
 * Extends existing useVIP functionality with new vipChecker and adminBypass features
 */

import { checkVipStatus, clearVipCache, getCacheStats } from './vipChecker.js';
import { getAllVipUsers, addVipUser, isDevelopmentMode } from './adminBypass.js';

/**
 * Enhanced VIP Hook với các tính năng mới
 * @param {string} uid - User ID
 * @returns {Object} Enhanced VIP utilities
 */
export function useVIPEnhanced(uid = null) {
    
    /**
     * Kiểm tra VIP status với forced active
     * @param {string} targetUid - UID cần check (optional, dùng uid từ hook nếu không có)
     * @param {Object} options - Options cho checkVipStatus
     * @returns {Promise<Object>} VIP status object
     */
    const checkVIP = async (targetUid = null, options = {}) => {
        const checkUid = targetUid || uid;
        if (!checkUid) {
            console.log('Cần cung cấp U<PERSON> để kiểm tra VIP');
            return null;
        }
        
        try {
            const result = await checkVipStatus(checkUid, options);
            console.log(`VIP check cho UID ${checkUid}:`, result);
            return result;
        } catch (error) {
            console.log('Lỗi khi check VIP:', error.message);
            return null;
        }
    };
    
    /**
     * Lấy danh sách tất cả VIP users (admin function)
     * @param {Object} options - Options
     * @returns {Promise<Array|null>} Danh sách VIP users
     */
    const getAllVIPs = async (options = {}) => {
        if (!isDevelopmentMode()) {
            console.log('getAllVIPs chỉ khả dụng trong development mode');
            return null;
        }
        
        try {
            const result = await getAllVipUsers(options);
            if (result) {
                console.log(`Lấy được ${result.length} VIP users`);
            }
            return result;
        } catch (error) {
            console.log('Lỗi khi lấy danh sách VIP:', error.message);
            return null;
        }
    };
    
    /**
     * Thêm VIP cho user (admin function)
     * @param {string} targetUid - UID cần thêm VIP
     * @param {string} name - Tên user
     * @param {number|Date} expireTime - Thời gian hết hạn
     * @param {Object} options - Options
     * @returns {Promise<boolean>} Success status
     */
    const addVIP = async (targetUid, name, expireTime, options = {}) => {
        if (!isDevelopmentMode()) {
            console.log('addVIP chỉ khả dụng trong development mode');
            return false;
        }
        
        // Convert Date to timestamp if needed
        const timestamp = expireTime instanceof Date ? expireTime.getTime() : expireTime;
        
        try {
            const result = await addVipUser(targetUid, name, timestamp, options);
            if (result) {
                console.log(`Đã thêm VIP cho ${targetUid} (${name})`);
            }
            return result;
        } catch (error) {
            console.log('Lỗi khi thêm VIP:', error.message);
            return false;
        }
    };
    
    /**
     * Utility functions
     */
    const utils = {
        clearCache: () => {
            clearVipCache();
            console.log('Đã xóa VIP cache');
        },
        
        getCacheInfo: () => {
            const stats = getCacheStats();
            console.log('VIP Cache stats:', stats);
            return stats;
        },
        
        isDev: isDevelopmentMode,
        
        // Preset expire times
        getExpireTime: {
            days: (days) => Date.now() + (days * 24 * 60 * 60 * 1000),
            months: (months) => Date.now() + (months * 30 * 24 * 60 * 60 * 1000),
            years: (years) => Date.now() + (years * 365 * 24 * 60 * 60 * 1000)
        }
    };
    
    return {
        // Main functions
        checkVIP,
        getAllVIPs,
        addVIP,
        
        // Utils
        utils,
        
        // Current UID
        uid,
        
        // Quick actions
        quickCheck: () => checkVIP(),
        quickAdd: (name, days = 30) => {
            if (!uid) {
                console.log('Cần set UID trước khi sử dụng quickAdd');
                return false;
            }
            return addVIP(uid, name, utils.getExpireTime.days(days));
        }
    };
}

// Export individual functions for direct use
export {
    checkVipStatus,
    getAllVipUsers,
    addVipUser,
    clearVipCache,
    getCacheStats,
    isDevelopmentMode
};

// Default export
export default useVIPEnhanced;
