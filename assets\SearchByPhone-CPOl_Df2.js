const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./MySwal-8JMBfvUJ.js","./sweetalert2.esm.all-BZxvatOx.js","./index-Cak6rALw.js","./index-DIX6R8z-.css"])))=>i.map(i=>d[i]);
import{b0 as n,bg as e,b5 as o,aN as i}from"./index-Cak6rALw.js";import{u as a,S as s,t}from"./MyApp-DW5WH4Ub.js";import r from"./sweetalert2.esm.all-BZxvatOx.js";function c(){const{ti:c}=a();return n.jsxs(s,{direction:"vertical",style:{width:"100%"},align:"center",children:[n.jsx(e,{accordion:!0,expandIconPosition:"right",items:[{key:"1",label:n.jsxs(s,{children:[n.jsx("i",{className:"fa-solid fa-phone fa-lg"}),c({en:"Find more information from phone numers",vi:"Tìm thêm thông tin về SĐT"})]}),children:n.jsxs(s,{direction:"vertical",children:[n.jsx(o,{icon:n.jsx("i",{className:"fa-brands fa-google"}),onClick:async()=>{const e=await r.fire({icon:"info",title:c({en:"Enter phone numbers",vi:"Nhập SĐT muốn tìm"}),input:"text",inputValue:"**********",showCancelButton:!0}),a=null==e?void 0:e.value;if((null==a?void 0:a.length)>0){t("SearchByPhoneNumber:Google");const e=[a,`"${a}"`,'"'+a.replace(/(\d{3})(\d{3})(\d{3})$/,"$1.$2.$3")+'"','"'+a.replace(/(\d{3})(\d{3})(\d{3})$/,"$1 $2 $3")+'"'];(await i((async()=>{const{default:n}=await import("./MySwal-8JMBfvUJ.js");return{default:n}}),__vite__mapDeps([0,1,2,3]),import.meta.url)).default.fire({icon:"success",title:c({en:"Try these links",vi:"Thử tìm theo các link này"}),html:n.jsx(s,{direction:"vertical",children:e.map((e=>n.jsx(o,{href:`https://www.google.com/search?q=${e}`,target:"_blank",children:e})))})})}},children:"Google"}),n.jsx(o,{icon:n.jsx("i",{className:"fa-solid fa-phone"}),href:"https://www.truecaller.com/",target:"_blank",onClick:()=>t("SearchByPhoneNumber:TrueCaller"),children:"True Caller"}),n.jsx(o,{icon:n.jsx("i",{className:"fa-solid fa-check"}),href:"https://www.trangtrang.com/",target:"_blank",onClick:()=>t("SearchByPhoneNumber:TrangTrang"),children:"Trang Trắng"}),n.jsx(o,{icon:n.jsx("i",{className:"fa-solid fa-z"}),onClick:()=>{r.fire({icon:"info",title:c({en:"Enter phone numbers",vi:"Nhập SĐT muốn tìm"}),input:"text",inputValue:"**********",showCancelButton:!0}).then((({value:n})=>{(null==n?void 0:n.length)>0&&(t("SearchByPhoneNumber:Zalo"),window.open(`https://zalo.me/${n}`,"_blank"))}))},children:"Zalo"})]})},{key:"2",label:n.jsxs(s,{children:[n.jsx("i",{className:"fa-brands fa-facebook fa-lg"}),c({en:"Find FB account from phone/email",vi:"Tìm nick FB từ SĐT/Email"})]}),children:n.jsxs(s,{direction:"vertical",children:[n.jsx(o,{icon:n.jsx("i",{className:"fa-solid fa-magnifying-glass"}),onClick:()=>{r.fire({icon:"warning",title:c({en:"Please aware",vi:"Cẩn thận"}),text:c({vi:"Chỉ nên dùng chức năng fbnumbers trên web, không khuyên cài extension fbnumbers vào máy tính",en:"Only use the function of fbnumbers on the web, not recommend install the fbnumbers extension on your computer"}),showDenyButton:!0,confirmButtonText:c({en:"Understand",vi:"Đã hiểu"}),denyButtonText:c({en:"Why?",vi:"Tại sao?"}),reverseButtons:!0}).then((n=>{n.isDenied?(t("SearchByPhoneNumber:FBNumbers:Why"),window.open("https://www.facebook.com/groups/fbaio/posts/****************","_blank")):n.isConfirmed&&(t("SearchByPhoneNumber:FBNumbers"),window.open("https://fbnumber.com/","_blank"))}))},children:"Fbnumbers"}),n.jsx(o,{icon:n.jsx("i",{className:"fa-solid fa-magnifying-glass"}),onClick:()=>{t("SearchByPhoneNumber:Fb1s"),window.open("https://fb1s.com/cong-cu/tim-facebook-bang-so-dien-thoai","_blank")},children:"Fb1s"}),n.jsx(o,{icon:n.jsx("i",{className:"fa-brands fa-facebook"}),onClick:async()=>{t("SearchByPhoneNumber:FBFindYourAccount");(0,(await i((async()=>{const{openURLInIncognito:n}=await import("./index-Cak6rALw.js").then((n=>n.cS));return{openURLInIncognito:n}}),__vite__mapDeps([2,3]),import.meta.url)).openURLInIncognito)("https://www.facebook.com/login/identify")},children:"Facebook (find your account)"})]})},{key:"3",label:n.jsxs(s,{children:[n.jsx("i",{className:"fa-brands fa-facebook fa-lg"}),c({en:"Find phone from FB account",vi:"Tìm SĐT từ nick FB"})]}),children:n.jsxs(s,{direction:"vertical",children:[n.jsx(o,{icon:n.jsx("i",{className:"fa-solid fa-magnifying-glass"}),href:"https://sotel.vn/cong-cu-lay-so-dien-thoai-tu-facebook-mien-phi/#phoneForm",target:"_blank",onClick:()=>t("SearchByPhoneNumber:Sotel"),children:"Sotel"}),n.jsx(o,{icon:n.jsx("i",{className:"fa-solid fa-magnifying-glass"}),onClick:()=>{t("SearchByPhoneNumber:Fb1s"),window.open("https://fb1s.com/cong-cu/tim-sdt-tu-link-facebook","_blank")},children:"Fb1s"})]})}]}),n.jsx("br",{}),n.jsx(o,{href:"https://www.facebook.com/groups/****************/?multi_permalinks=****************",target:"_blank",icon:n.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"}),onClick:()=>t("SearchByPhoneNumber:MoreInfo"),children:c({en:"Need more info?",vi:"Cần thêm thông tin?"})})]})}export{c as default};
