import{r as e,b0 as s,b5 as t}from"./index-Cak6rALw.js";import{S as n}from"./Screen-Buq7HJpK.js";import{waitWinForReady as i,runFnInWin as o}from"./window-pPC-ycGO.js";import{I as r}from"./index-Bg865k-U.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./MyApp-DW5WH4Ub.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";function a(){const[a,l]=e.useState(null),[c,p]=e.useState('alert("test")'),[j,m]=e.useState("https://www.facebook.com/messages");return s.jsxs(n,{title:"Bulk Del Fb Mess",children:[s.jsx(r,{value:j,onChange:e=>m(e.target.value)}),s.jsx(t,{disabled:!j,onClick:()=>{const e=window.open(j,"_blank");l(e)},children:"Open"}),a&&s.jsxs(s.Fragment,{children:[s.jsx(r.TextArea,{rows:10,value:c,onChange:e=>p(e.target.value)}),s.jsx(t,{disabled:!c,onClick:async()=>{await i(a),o({win:a,fnPath:"window.eval",params:[c],origin:new URL(j).origin})},children:"Run script"}),s.jsx(t,{onClick:()=>l(null),children:"Close"})]})]})}export{a as default};
