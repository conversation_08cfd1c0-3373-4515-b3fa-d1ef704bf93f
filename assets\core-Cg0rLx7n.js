import{aN as n,aM as i}from"./index-Cak6rALw.js";import{x as t}from"./MyApp-DW5WH4Ub.js";import{waitWinForReady as o,runFnInWin as e}from"./window-pPC-ycGO.js";async function l(n){var i,t,l,a,r,d;let s;try{const u=null==(i=n.match(/\/file\/d\/([^/]+)/))?void 0:i[1];if(!u)throw new Error("Video ID not found");s=window.open(n,"_blank","width=800,height=600"),await o(s),await new Promise((n=>setTimeout(n,1e3)));const c=new URL(n).origin,v=await e({win:s,fnPath:"window.gapi.client.request",params:[`https://workspacevideo-pa.clients6.google.com/v1/drive/media/${u}/playback`],origin:c}),w=null==v?void 0:v.result,f=null==(t=null==w?void 0:w.mediaStreamingData)?void 0:t.formatStreamingData,h=[...(null==f?void 0:f.adaptiveTranscodes)||[],...(null==f?void 0:f.progressiveTranscodes)||[]].map((n=>{var i,t,o,e,l,a,r,d,s,u;return{codecs:(null==(i=null==n?void 0:n.transcodeMetadata)?void 0:i.videoCodecString)||(null==(t=null==n?void 0:n.transcodeMetadata)?void 0:t.audioCodecString),bandwidth:parseInt(null==(o=null==n?void 0:n.transcodeMetadata)?void 0:o.contentLength),length:parseInt(null==(e=null==n?void 0:n.transcodeMetadata)?void 0:e.approxDuration),mimeType:null==(l=null==n?void 0:n.transcodeMetadata)?void 0:l.mimeType,width:null==(a=null==n?void 0:n.transcodeMetadata)?void 0:a.width,height:null==(r=null==n?void 0:n.transcodeMetadata)?void 0:r.height,id:null==n?void 0:n.itag,isAudio:null==(u=null==(s=null==(d=null==n?void 0:n.transcodeMetadata)?void 0:d.mimeType)?void 0:s.includes)?void 0:u.call(s,"audio"),qualityLabel:"",qualityClass:"",source:null==n?void 0:n.url}})).sort(((n,i)=>i.bandwidth-n.bandwidth)),g={source:null==(l=h.at(-1))?void 0:l.source,thumbnail:null==(d=null==(r=null==(a=null==w?void 0:w.thumbnails)?void 0:a.sort(((n,i)=>i.width-n.width)))?void 0:r[0])?void 0:d.url,variants:h};return console.log(g),g}catch(u){throw u}finally{"function"==typeof(null==s?void 0:s.close)&&s.close()}}async function a(n){var l;let a;try{if(!(null==(l=n.match(/\/file\/d\/([^/]+)/))?void 0:l[1]))throw new Error("File ID not found");a=window.open(n,"_blank","width=800,height=600"),await o(a);const r=await e({win:a,fnPath:"window.viewerData",params:[]});if(!r)throw new Error("File not found");const{config:d,itemJson:s,configJson:u}=r||{},{id:c,title:v}=d||{};let w=null==s?void 0:s.find((n=>{var i;return null==(i=null==n?void 0:n.includes)?void 0:i.call(n,"meta?ck=")}));if(!w){const n=null==s?void 0:s.find((n=>{var i;return null==(i=null==n?void 0:n.includes)?void 0:i.call(n,"ds=")}));if(!n)throw new Error("File meta & ds not found");for(const o of[!1,!0]){const e=o?"&authuser=1":"",l=await i(n+e);if(!l)continue;const a=l.split("\n"),r=t(a[1]||a[0]||"{}");console.log(r);const{img:d,meta:s,page:u,press:c,presspage:v,status:f}=r;if(s){w="https://drive.google.com/viewerng/"+s;break}}}if(console.log(r,c,v,w),!w)throw new Error("File meta info not found");for(const n of[!1,!0]){const o=n?"&authuser=1":"",e=(await i(w+o)).split("\n"),l=t(e[1]||e[0]||"{}");console.log(l);const{pages:a,maxPageWidth:r}=l;if(!a||!r)continue;const d=[];for(let n=0;n<a;n++){const i=w.replace("/meta?","/img?")+`&page=${n}&skiphighlight=true&skipwidget=false&w=${r}&webp=true`+o;d.push(i)}return console.log(d,n),{maxPageWidth:r,pages:d,title:v,id:c,isNeedAuth:n}}throw new Error("File info not found")}catch(r){throw r}finally{"function"==typeof(null==a?void 0:a.close)&&a.close()}}async function r(i,t){if(i.includes("/drive/folders/")){const i=await(await n((async()=>{const{default:n}=await import("./sweetalert2.esm.all-BZxvatOx.js");return{default:n}}),[],import.meta.url)).default.fire({title:t({en:"Folder not supported",vi:"Chưa hỗ trợ folder"}),text:t({en:"You are entering a google drive folder link. Please enter a file direct link to download (open file in new tab might help)",vi:"Bạn đang nhập link folder google drive. Vui lòng nhập link file để tải (mở file trong tab mới để lấy link đúng)"}),icon:"warning",confirmButtonText:t({en:"Enter again",vi:"Nhập lại"}),showCancelButton:!0,cancelButtonText:t({en:"Continue. I think this url is correct",vi:"Tiếp tục. Tôi nghĩ link này đúng"}),reverseButtons:!0,allowOutsideClick:!1,allowEscapeKey:!1});return!(null==i?void 0:i.isConfirmed)}return!0}export{r as checkFolderUrl,a as getGoogleDrivePDFFromURL,l as getGoogleDriveVideoFromURL};
