import{L as e,r as n,bY as t,G as o,aL as l,bU as s,b5 as a,bZ as r,b_ as i,m as c,R as p,T as m,a6 as d}from"./index-Cak6rALw.js";import{g,a as f,P as u}from"./index-PbLYNhdQ.js";const b=e("Popconfirm",(e=>(e=>{const{componentCls:n,iconCls:t,antCls:o,zIndexPopup:l,colorText:s,colorWarning:a,marginXXS:r,marginXS:i,fontSize:c,fontWeightStrong:p,colorTextHeading:m}=e;return{[n]:{zIndex:l,[`&${o}-popover`]:{fontSize:c},[`${n}-message`]:{marginBottom:i,display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${n}-message-icon ${t}`]:{color:a,fontSize:c,lineHeight:1,marginInlineEnd:i},[`${n}-title`]:{fontWeight:p,color:m,"&:only-child":{fontWeight:"normal"}},[`${n}-description`]:{marginTop:r,color:s}},[`${n}-buttons`]:{textAlign:"end",whiteSpace:"nowrap",button:{marginInlineStart:i}}}}})(e)),(e=>{const{zIndexPopupBase:n}=e;return{zIndexPopup:n+60}}),{resetStyle:!1});const y=e=>{const{prefixCls:c,okButtonProps:p,cancelButtonProps:m,title:d,description:f,cancelText:u,okText:b,okType:y="primary",icon:v=n.createElement(t,null),showCancel:O=!0,close:x,onConfirm:C,onCancel:j,onPopupClick:P}=e,{getPrefixCls:h}=n.useContext(o),[E]=l("Popconfirm",s.Popconfirm),N=g(d),S=g(f);return n.createElement("div",{className:`${c}-inner-content`,onClick:P},n.createElement("div",{className:`${c}-message`},v&&n.createElement("span",{className:`${c}-message-icon`},v),n.createElement("div",{className:`${c}-message-text`},N&&n.createElement("div",{className:`${c}-title`},N),S&&n.createElement("div",{className:`${c}-description`},S))),n.createElement("div",{className:`${c}-buttons`},O&&n.createElement(a,Object.assign({onClick:j,size:"small"},m),u||(null==E?void 0:E.cancelText)),n.createElement(r,{buttonProps:Object.assign(Object.assign({size:"small"},i(y)),p),actionFn:C,close:x,prefixCls:h("btn"),quitOnNullishReturnValue:!0,emitEvent:!0},b||(null==E?void 0:E.okText))))};const v=n.forwardRef(((e,o)=>{var l,s;const{prefixCls:a,placement:r="top",trigger:i="click",okType:g="primary",icon:f=n.createElement(t,null),children:v,overlayClassName:O,onOpenChange:x,onVisibleChange:C,overlayStyle:j,styles:P,classNames:h}=e,E=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var l=0;for(o=Object.getOwnPropertySymbols(e);l<o.length;l++)n.indexOf(o[l])<0&&Object.prototype.propertyIsEnumerable.call(e,o[l])&&(t[o[l]]=e[o[l]])}return t}(e,["prefixCls","placement","trigger","okType","icon","children","overlayClassName","onOpenChange","onVisibleChange","overlayStyle","styles","classNames"]),{getPrefixCls:N,className:S,style:$,classNames:T,styles:k}=p("popconfirm"),[w,I]=m(!1,{value:null!==(l=e.open)&&void 0!==l?l:e.visible,defaultValue:null!==(s=e.defaultOpen)&&void 0!==s?s:e.defaultVisible}),z=(e,n)=>{I(e,!0),null==C||C(e),null==x||x(e,n)},W=N("popconfirm",a),B=c(W,S,O,T.root,null==h?void 0:h.root),V=c(T.body,null==h?void 0:h.body),[R]=b(W);return R(n.createElement(u,Object.assign({},d(E,["title"]),{trigger:i,placement:r,onOpenChange:(n,t)=>{const{disabled:o=!1}=e;o||z(n,t)},open:w,ref:o,classNames:{root:B,body:V},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},k.root),$),j),null==P?void 0:P.root),body:Object.assign(Object.assign({},k.body),null==P?void 0:P.body)},content:n.createElement(y,Object.assign({okType:g,icon:f},e,{prefixCls:W,close:e=>{z(!1,e)},onConfirm:n=>{var t;return null===(t=e.onConfirm)||void 0===t?void 0:t.call(void 0,n)},onCancel:n=>{var t;z(!1,n),null===(t=e.onCancel)||void 0===t||t.call(void 0,n)}})),"data-popover-inject":!0}),v))}));v._InternalPanelDoNotUseOrYouWillBeFired=e=>{const{prefixCls:t,placement:l,className:s,style:a}=e,r=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var l=0;for(o=Object.getOwnPropertySymbols(e);l<o.length;l++)n.indexOf(o[l])<0&&Object.prototype.propertyIsEnumerable.call(e,o[l])&&(t[o[l]]=e[o[l]])}return t}(e,["prefixCls","placement","className","style"]),{getPrefixCls:i}=n.useContext(o),p=i("popconfirm",t),[m]=b(p);return m(n.createElement(f,{placement:l,className:c(p,s),style:a,content:n.createElement(y,Object.assign({prefixCls:p},r))}))};export{v as P};
