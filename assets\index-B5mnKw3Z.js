const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./MyApp-DW5WH4Ub.js","./index-Cak6rALw.js","./index-DIX6R8z-.css"])))=>i.map(i=>d[i]);
import{aN as n,r as e,bi as i,b0 as t,c8 as a,b5 as o}from"./index-Cak6rALw.js";import{aC as s,aD as r,aE as c,x as l,y as d,W as h,u as m,t as u,v as g,S as p,i as f,h as b,b as k,e as v,f as y,T as j,B as x,s as _,o as T}from"./MyApp-DW5WH4Ub.js";import{a as E,G as S}from"./gender-D7cqwEK5.js";import B from"./MyTable-DnjDmN7z.js";import I from"./useCacheState-CAnxkewm.js";import{E as w}from"./ExportButton-CbyepAf_.js";import{d as D}from"./dayjs.min-CzPn7FMI.js";import{u as N}from"./useAction-uz-MrZwJ.js";import{u as C}from"./useDevMode-yj0U7_8D.js";import{S as M}from"./Screen-Buq7HJpK.js";import{A as R}from"./index-DdM4T8-Q.js";import{I as A}from"./index-CDSnY0KE.js";import{T as P}from"./index-IxNfZAD-.js";import"./index-ByRdMNW-.js";import"./index-BzMPigdO.js";import"./DownOutlined-B-JcS-29.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./row-BMfM-of4.js";import"./useVIP-D5FAMGQQ.js";import"./sweetalert2.esm.all-BZxvatOx.js";import"./getIds-DAzc-wzf.js";import"./index-CC5caqt_.js";import"./col-CzA09p0P.js";import"./index-PbLYNhdQ.js";var L=(n=>(n.RESTRICTED_LIST="RESTRICTED_LIST",n.USER="USER",n.MESSAGE="MESSAGE",n.APP_INVITE="APP_INVITE",n.EVENT_INVITE="EVENT_INVITE",n))(L||{});const F={RESTRICTED_LIST:{icon:"fa-solid fa-ban",name:{en:"Restricted",vi:"Hạn chế"},desc:{en:"Restricted list:\nWhen you add someone's profile to your Restricted list, they won't see posts on Facebook that you share only to Friends\nThey may still see things you share to Public or on a mutual friend's profile, and posts their profile is tagged in\nFacebook doesn't notify your friends when you add them to your Restricted list.",vi:"Danh sách hạn chế:\nKhi bạn thêm trang cá nhân của ai đó vào Danh sách hạn chế thì trên Facebook, họ sẽ không nhìn thấy các bài viết mà bạn chỉ chia sẻ với Bạn bè\nHọ có thể vẫn nhìn thấy nội dung bạn chia sẻ Công khai hoặc trên trang cá nhân của một người bạn chung cũng như các bài viết có gắn thẻ trang cá nhân của họ\nFacebook không thông báo cho bạn bè của bạn khi bạn thêm họ vào Danh sách hạn chế."}},USER:{icon:"fa-solid fa-user",name:{en:"Profile/Page",vi:"Trang/Trang cá nhân"},desc:{en:"Block Profile/Page:\nOnce you block a profile or Page, you can no longer interact with each others' timelines, posts, comments or messages.\nThis doesn't include apps, games or groups you both participate in.\nIf you currently are connected with that profile or Page, blocking it will unfriend, unlike and unfollow it.",vi:"Chặn Trang/Trang cá nhân:\nSau khi bạn chặn một Trang/trang cá nhân, các bạn không thể tương tác với dòng thời gian, bài viết, bình luận hoặc tin nhắn của nhau nữa.\nĐiều này không áp dụng với ứng dụng, game hoặc nhóm mà cả 2 bạn cùng tham gia.\nNếu đang kết nối với Trang/trang cá nhân đó thì khi bạn chặn, hệ thống sẽ hủy kết bạn, bỏ thích và bỏ theo dõi Trang/trang cá nhân đó."}},MESSAGE:{icon:"fa-brands fa-facebook-messenger",name:{en:"Message",vi:"Tin nhắn"},desc:{en:"Block messages:\nIf you block someone's profile on Facebook, they won't be able to contact you in Messenger either\nUnless you block someone's Facebook profile and any others they may create, they may be able to post on your timeline, tag you, and comment on your posts or comments.",vi:"Chặn tin nhắn:\nNếu bạn chặn trang cá nhân của ai đó trên Facebook, họ cũng sẽ không thể liên hệ với bạn trong Messenger\nNếu bạn không chặn trang cá nhân Facebook của ai đó và bất kỳ trang cá nhân nào khác họ có khả năng tạo, họ sẽ có thể đăng bài lên dòng thời gian của bạn, gắn thẻ bạn và bình luận về bài viết hoặc bình luận của bạn."},confirm:{vi:"\nChặn tin nhắn và cuộc gọi\nThủy sẽ không bị chặn trên Facebook.\nBạn sẽ không nhận được tin nhắn hay cuộc gọi từ Thủy. Nếu bỏ chặn, bạn sẽ không nhận được bất kỳ nội dung nào mà người đó có thể đã gửi trong thời gian chặn.\nBạn và Thủy có thể nhìn thấy, nhắn tin và gọi cho nhau trong các nhóm chung, trừ khi bạn rời khỏi những nhóm đó."}},APP_INVITE:{icon:"fa-brands fa-google-play",name:{en:"App invite",vi:"Ứng dụng"},desc:{en:"Block app invites:\nOnce you block app invites from someone's profile, you'll automatically ignore future app requests from that person's profile.",vi:"Chặn lời mời cài đặt ứng dụng:\nSau khi chặn lời mời dùng ứng dụng từ trang cá nhân của người khác, bạn sẽ tự động bỏ qua các yêu cầu ứng dụng trong tương lai từ người đó."}},EVENT_INVITE:{icon:"fa-regular fa-calendar",name:{en:"Event invite",vi:"Sự kiện"},desc:{en:"Block event invites:\nOnce you block event invites from someone’s profile, you’ll automatically ignore future event requests from that profile.",vi:"Chặn lời mời tham gia sự kiện:\nKhi bạn chặn lời mời tham gia sự kiện từ trang cá nhân của một người, bạn sẽ tự động bỏ qua những lời mời trong tương lai từ trang cá nhân đó."}}};async function U({type:e,cursor:i}){const t=i?await d({fb_api_req_friendly_name:"BlockingSettingsBlockingListPaginationQuery",variables:{count:10,cursor:i,profile_picture_size:36,id:btoa("privacy_block_setting:"+e.toString().toLocaleLowerCase())},doc_id:"5186554984768632"}):await d({fb_api_req_friendly_name:"BlockingSettingsDialogQuery",variables:{profile_picture_size:36,settingType:e},doc_id:"5425356630905390"}),a=l(t);console.log(a);const{findDataObject:o}=await n((async()=>{const{findDataObject:n}=await import("./MyApp-DW5WH4Ub.js").then((n=>n.aX));return{findDataObject:n}}),__vite__mapDeps([0,1,2]),import.meta.url),{edges:s=[],page_info:r}=o(a);return s.map((n=>{var i,t,a,o,s;return{type:e,id:null==(i=null==n?void 0:n.node)?void 0:i.id,name:null==(t=null==n?void 0:n.node)?void 0:t.name,avatar:null==(o=null==(a=null==n?void 0:n.node)?void 0:a.profile_picture)?void 0:o.uri,gender:(null==(s=null==n?void 0:n.node)?void 0:s.gender)||E.UNKNOWN,cursor:(null==n?void 0:n.cursor)||(null==r?void 0:r.end_cursor)}}))}let O=0;function V(){const{devMode:V}=C(),{ti:Y}=m(),{onClickAction:H,onClickBulkActions:q}=N(),[G,z]=I("Blocked.type",L.RESTRICTED_LIST),[K,W]=I("Blocked.data",{}),[X,J]=I("Blocked.loading",{}),[Q,Z]=I("Blocking.noData",{}),[$,nn]=I("Blocked.blockTime",[]),en=e.useMemo((()=>i(K,(n=>{var e;for(const i in n)for(const t of n[i]){const n=null==(e=$.find((n=>n.id==t.id&&(n.isBlockMessage&&t.type==L.MESSAGE||!n.isBlockMessage&&[L.USER,L.RESTRICTED_LIST].includes(t.type)))))?void 0:e.time;n&&(t.time=n)}}))),[K,$]);e.useEffect((()=>{u("Blocked.onLoad"),async function(){var e;const i=await s(r.EAAB);if(!i)return[];const{fetchExtension:t}=await n((async()=>{const{fetchExtension:n}=await import("./index-Cak6rALw.js").then((n=>n.cS));return{fetchExtension:n}}),__vite__mapDeps([1,2]),import.meta.url),a=await t("https://graph.facebook.com/v21.0/me/blocked?fields=name,username,fbid,block_type,block_time&limit=100&access_token="+i);c(a);const o=l(a);return(null==(e=null==o?void 0:o.data)?void 0:e.map((n=>({id:n.fbid,name:n.name,time:new Date(n.block_time),isBlockMessage:"messenger"==n.block_type}))))||[]}().then(nn)}),[]);const tn=(n,e,t="")=>{J((a=>i(a,(i=>{i[n+t]=e}))))},an=(n,e)=>{W((t=>i(t,(i=>{i[n]=e}))))},on=(n,e)=>{Z((t=>i(t,(i=>{i[n]=e}))))};e.useEffect((()=>{rn(G)}),[G]);const sn=e.useRef({}),rn=async(n,e=!1)=>{var i,t;if(!e&&(Q[n]||sn.current[n]||(null==(i=en[n])?void 0:i.length)))return;e&&on(n,!1),sn.current[n]=!0,tn(n,!0);const a=[];let o="";for(;;)try{o=(null==(t=a[a.length-1])?void 0:t.cursor)||"";const e=await U({type:n,cursor:o});if(!(null==e?void 0:e.length)){a.length||on(n,!0);break}a.push(...e),console.log(a),an(n,[...a].map(((n,e)=>({...n,isBlocked:!0,index:e+1})))),await g(500)}catch(s){console.log(s)}sn.current[n]=!1,tn(n,!1)},cn=(e,t=!1)=>(tn(G,!0,e.id),H({record:e,id:e.id,key:"Blocked:onClickUnblock",actionFn:()=>V?g(1e3):async function({id:e,type:i,value:t}){const a="UNBLOCK",o=await d({fb_api_req_friendly_name:"BlockingSettingsBlockMutation",variables:{input:{block_action:a,setting:i,target_id:e,actor_id:await h(),client_mutation_id:++O},profile_picture_size:36},doc_id:"6009824239038988"}),s=l(o),{deepFind:r}=await n((async()=>{const{deepFind:n}=await import("./MyApp-DW5WH4Ub.js").then((n=>n.aX));return{deepFind:n}}),__vite__mapDeps([0,1,2]),import.meta.url);return r(s,"block_status")===a}({id:e.id,type:e.type,value:!1}),loadingText:()=>Y({en:"Unblocking...",vi:"Đang bỏ chặn..."})+" "+e.name,successText:()=>Y({en:"Unblocked",vi:"Đã bỏ chặn"})+" "+e.name,failedText:()=>Y({en:"Failed to unblock",vi:"Lỗi bỏ chặn"})+" "+e.name,onSuccess:()=>{var n,t;n=G,t=e.id,W((e=>i(e,(e=>{const i=e[n].findIndex((n=>n.id===t));return i>=0&&e[n].splice(i,1),e}))))},needConfirm:t,confirmProps:{title:Y({en:"Unblock ",vi:"Bỏ chặn "})+e.name+"?",text:Y({en:"Warning: After 48 hours, you can block again this user.",vi:"Lưu ý: Sau 48h mới có thể chặn lại người này."})}}).finally((()=>{tn(G,!1,e.id)}))),ln=(n,e)=>_(n,(null==e?void 0:e.name)+(null==e?void 0:e.id)),dn=n=>e=>t.jsxs(p,{children:[t.jsx(o,{disabled:X[n],type:"primary",icon:X[n]?t.jsx("i",{className:"fa-solid fa-rotate-right fa-spin"}):t.jsx("i",{className:"fa-solid fa-rotate-right"}),onClick:()=>rn(n,!0),children:Y({en:"Reload",vi:"Tải lại"})}),t.jsx(w,{data:(null==e?void 0:e.length)?e:en[n]||[],options:[{key:"uid",label:".txt (friend uid)",prepareData:n=>({fileName:"friends_"+D().format("YYYY-MM-DD-HHmmss")+".txt",data:n.map((n=>n.id)).join("\n")})},{key:"uid_name",label:".csv (uid+name)",prepareData:n=>({fileName:"blocked_uid_name_"+D().format("YYYY-MM-DD-HHmmss")+".csv",data:T(n.map((n=>({uid:n.id,name:n.name}))))})},{key:"json",label:".json",prepareData:n=>({fileName:"blocked"+D().format("YYYY-MM-DD-HHmmss")+".json",data:JSON.stringify(n,null,4)})},{key:"csv",label:".csv",prepareData:n=>({fileName:"blocked"+D().format("YYYY-MM-DD-HHmmss")+".csv",data:T(n)})}]}),(null==e?void 0:e.length)>0?t.jsx(o,{danger:!0,type:"default",icon:t.jsx("i",{className:"fa-regular fa-trash-can"}),onClick:()=>{return n=e,tn(G,!0),q({data:n,key:"Blocked:onClickUnblockBulk",actionFn:cn,loadingText:()=>Y({en:"Unblocking...",vi:"Đang bỏ chặn..."}),successText:()=>Y({en:"Unblocked",vi:"Đã bỏ chặn"}),successDescItem:n=>n.name,requireVIP:!1,confirmProps:{title:Y({en:"Un-Block "+n.length+" users?",vi:"Bỏ chặn "+n.length+" người?"}),text:Y({en:"Warning: After 48 hours, you can block again these users.",vi:"Lưu ý: Sau 48h mới có thể chặn lại những người này."})}}).finally((()=>{tn(G,!1)}));var n},children:Y({en:"Unblock ",vi:"Bỏ chặn "})+e.length}):null,t.jsx(j,{title:Y({en:"Coming soon",vi:"Sắp có"}),children:t.jsx(o,{disabled:!0,icon:t.jsx("i",{className:"fa-solid fa-plus"}),children:Y({en:"Import",vi:"Chặn thêm"})})})]}),hn=[{title:"#",key:"index",dataIndex:"index",width:50},{title:Y({en:"Name",vi:"Tên"}),key:"nane",dataIndex:"name",render:(n,e,i)=>t.jsxs(p,{style:{maxWidth:300},children:[t.jsx(R,{shape:"square",src:t.jsx(A,{src:e.avatar,fallback:f(e.id)}),size:50}),t.jsxs(p,{direction:"vertical",style:{marginLeft:"10px"},size:0,children:[t.jsx("a",{onClick:n=>{n.preventDefault();const i=b(e.id);e.isBlocked&&[L.USER].includes(e.type)?a(i):window.open(i,"_blank")},children:t.jsx("b",{children:e.name})}),t.jsx("span",{style:{opacity:.5},children:e.id})]})]}),width:"auto"},{title:Y({en:"Gender",vi:"Giới tính"}),key:"gender",dataIndex:"gender",render:(n,e,i)=>{var a;return t.jsx(k,{color:(null==(a=S[e.gender])?void 0:a.color)||"default",children:Y(S[e.gender])||e.gender})},filters:Object.values(E).map((n=>{var e,i;return{text:Y(S[n])+" ("+(null==(i=null==(e=en[G])?void 0:e.filter((e=>e.gender===n)))?void 0:i.length)+")",value:n}})),onFilter:(n,e)=>e.gender===n,width:120},{title:Y({en:"Time",vi:"Thời gian"}),key:"time",dataIndex:"time",render:(n,e,i)=>{const a=e.time;return a?t.jsxs(p,{direction:"vertical",size:0,children:[v(a.getTime()),t.jsx("span",{style:{opacity:.5},children:y(a)})]}):"-"},sorter:(n,e)=>n.time.getTime()-e.time.getTime(),width:170},{title:Y({en:"Status",vi:"Trạng thái"}),key:"isBlocked",dataIndex:"isBlocked",render:(n,e,i)=>e.isBlocked?t.jsx(k,{color:"red",children:Y({en:"Blocked",vi:"Đang chặn"})}):t.jsx(k,{color:"green",children:Y({en:"Unblocked",vi:"Bỏ chặn"})}),width:120},{title:Y({en:"Action",vi:"Hành động"}),render:(n,e,i)=>t.jsx(o,{loading:X[G+e.id],icon:t.jsx("i",{className:"fa-solid fa-ban"}),onClick:()=>cn(e,!0),children:Y({en:"Unblock",vi:"Bỏ chặn"})}),width:120}];return t.jsx(M,{title:Y({en:"Blocked",vi:"Đang chặn"}),titleSuffix:t.jsx(o,{type:"default",icon:t.jsx("i",{className:"fa-solid fa-up-right-from-square"}),href:"https://www.facebook.com/settings/?tab=blocking",target:"_blank",style:{marginLeft:10}}),children:t.jsx(P,{defaultActiveKey:L.RESTRICTED_LIST,activeKey:G,onChange:n=>{u("Blocked:onChangeTab:"+n),z(n)},items:Object.values(L).map((n=>{var e;return{key:n,icon:X[n]?t.jsx("i",{className:"fa fa-spinner fa-spin"}):t.jsx("i",{className:F[n].icon}),label:t.jsxs(j,{title:t.jsx(t.Fragment,{children:Y(F[n].desc).split("\n").map(((e,i)=>t.jsx("p",{children:e},n+i)))}),children:[Y(F[n].name)," ",t.jsx(x,{showZero:!0,count:Q[n]?0:(null==(e=en[n])?void 0:e.length)||"",color:Q[n]?"gray":"purple",style:{color:"white"}})]}),children:t.jsx(B,{columns:hn,data:en[n],keyExtractor:n=>null==n?void 0:n.id,searchable:!0,selectable:!0,size:"small",onSearchRow:ln,renderTitle:dn(n)})}}))})})}export{V as default};
