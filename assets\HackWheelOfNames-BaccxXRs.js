import{r as n,b0 as e,b5 as i,bg as t,aP as a}from"./index-Cak6rALw.js";import{S as o}from"./Screen-Buq7HJpK.js";import r from"./useCacheState-CAnxkewm.js";import{d as s,u as l,a as d,e as c,S as h,A as g,aM as u,c as m,b as f}from"./MyApp-DW5WH4Ub.js";import{runFnInWin as p,waitWinForReady as x}from"./window-pPC-ycGO.js";import w from"./useVIP-D5FAMGQQ.js";import{I as j}from"./index-Bg865k-U.js";import{D as b}from"./index-CC5caqt_.js";import{S as v}from"./index-Bp2s1Wws.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./sweetalert2.esm.all-BZxvatOx.js";import"./getIds-DAzc-wzf.js";import"./index-IxNfZAD-.js";import"./Dropdown-BcL-JHCf.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";function I(){const{BEVIP:I}=w(),{message:k}=s(),{ti:M}=l(),{profile:C}=d(),[S,A]=r("HackWheelOfNames.trialCount",3,!0),[P,V]=r("HackWheelOfNames.win",null),[N,T]=r("HackWheelOfNames.hackedValue","hacked"),[q,W]=r("HackWheelOfNames.reverseMode",!1);n.useEffect((()=>{if(P){const n=setInterval((()=>{P.closed&&(V(null),clearInterval(n))}),1e3);return()=>clearInterval(n)}}),[P]);const D=I.expiredTime?c(I.expiredTime):"0";return e.jsx(o,{title:"Hack WheelOfNames",mode:"center",children:e.jsxs(h,{direction:"vertical",align:"center",children:[e.jsx(g,{banner:!0,showIcon:!0,message:M({en:"Enter wanted result: ",vi:"Nhập kết quả mong muốn: "}),type:"warning"}),e.jsx(j,{value:N,placeholder:M({en:"Enter wanted result",vi:"Nhập kết quả mong muốn"}),onChange:n=>{T(n.target.value)}}),!u(N)&&e.jsxs(h,{direction:"vertical",align:"center",children:[e.jsx(b,{}),e.jsx(g,{banner:!0,showIcon:!0,message:M({en:"Select mode: ",vi:"Chọn chế độ: "}),type:"warning"}),e.jsxs(h,{children:[e.jsx(m.Text,{children:M({en:"Result will ",vi:"Kết quả sẽ "})}),e.jsx(v,{checked:q,unCheckedChildren:M({en:"Always",vi:"Luôn luôn"}),checkedChildren:M({en:"Never",vi:"Không bao giờ"}),onChange:n=>{W(n)}}),e.jsxs(m.Text,{children:[M({en:" be ",vi:" là "})," ",e.jsx(f,{color:"success",children:q?N:N.split(";").join(" -> ")})]})]}),!q&&e.jsx(g,{type:"info",showIcon:!0,message:M({en:"Can add multiple results, separated by a semicolon ;",vi:"Có thể thêm nhiều kết quả, cách nhau bằng dấu chấm phẩy ;"})})]}),e.jsx(b,{}),P&&e.jsx(g,{showIcon:!0,message:M({en:"If you change the result, please click Apply to update",vi:"Nếu bạn muốn thay đổi kết quả, hãy bấm Áp dụng để cập nhật"}),type:"info"}),P?e.jsxs(h.Compact,{children:[e.jsx(i,{icon:e.jsx("i",{className:"fa-solid fa-play"}),onClick:()=>{p({win:P,fnPath:"window.eval",params:[_(N,q)]}).then((()=>{k.success(M({en:"Applied new config",vi:"Đã áp dụng cấu hình mới"}))}))},children:M({en:"Apply new config",vi:"Áp dụng cấu hình mới"})}),e.jsx(i,{icon:e.jsx("i",{className:"fa-solid fa-xmark"}),danger:!0,onClick:()=>{P.close(),V(null)},children:M({en:"Close",vi:"Đóng"})})]}):e.jsxs(h,{direction:"vertical",align:"center",children:[e.jsx(g,{showIcon:!0,message:M({en:`Require VIP > 6 months. You have ${D} VIP left`,vi:`Yêu cầu VIP > 6 tháng. Bạn còn ${D} VIP`})}),S>0&&e.jsx(g,{showIcon:!0,type:S>0?"success":"error",message:M({en:`You have ${S} trials left`,vi:`Bạn còn ${S} lần thử`})}),e.jsx(i,{onClick:async()=>{const n=I.expiredTime>Date.now()+15552e6;if(S>0||n){S>0&&!n&&A(S-1);const e=window.open("https://wheelofnames.com/");V(e),await x(e),p({win:e,fnPath:"window.eval",params:[y(N,q)]}).then((()=>{k.success("Cheated")}))}else k.error(M({en:"You have no more trials. Please buy VIP to use this feature. 💎",vi:"Bạn đã hết lần thử. Vui lòng mua VIP để dùng chức năng. 💎"}))},icon:e.jsx("i",{className:"fa-solid fa-dharmachakra"}),disabled:!C,children:M({en:"Start Cheat",vi:"Bắt đầu Cheat"})})]}),e.jsx(b,{}),e.jsx(t,{style:{maxWidth:600},accordion:!0,items:[{key:"1",label:M({en:"What is this? 🤔",vi:"Tính năng này là gì? 🤔"}),children:e.jsx("p",{children:M({en:"This is a feature that allows you to cheat in Wheel of Names. It will always land on the your desired result.",vi:"Đây là tính năng cho phép bạn cheat trong Wheel of Names. Sẽ luôn quay ra kết quả bạn mong muốn."})})},{key:"2",label:M({en:"How to use? ❓",vi:"Cách sử dụng? ❓"}),children:e.jsx("ol",{children:M({en:e.jsxs(e.Fragment,{children:[e.jsx("li",{children:"Enter the desired outcome"}),e.jsx("li",{children:"Choose a mode: Always land on the desired result, or never land on that result."}),e.jsx("li",{children:'Click "Start Cheat"'})]}),vi:e.jsxs(e.Fragment,{children:[e.jsx("li",{children:"Nhập kết quả mong muốn"}),e.jsx("li",{children:"Chọn chế độ: Luôn quay trúng kết quả mong muốn; hoặc không bao giờ quay trúng kết quả đó."}),e.jsx("li",{children:'Bấm "Bắt đầu Cheat"'})]})})})},{key:"3",label:M({en:"Require VIP > 6 months 💎",vi:"Yêu cầu VIP > 6 tháng 💎"}),children:e.jsxs(e.Fragment,{children:[M({en:"You have 3 trials 🍀.",vi:"Bạn có 3 lần thử 🍀."}),e.jsx("br",{}),M({en:"Then you need to have VIP > 6 months to use this feature 💎. ",vi:"Sau đó bạn cần phải có VIP > 6 tháng để sử dụng tính năng này 💎. "}),e.jsx("br",{}),M({en:"If VIP is less than 6 months, you need to buy more to meet the requirement ✅. ",vi:"Nếu VIP dưới 6 tháng, bạn cần mua thêm để thoả điều kiện ✅. "}),e.jsx("br",{}),e.jsx("a",{href:"#/checkout",children:M({en:"How to get VIP?",vi:"Cách để có VIP?"})})]})},{key:"4",label:M({en:"Error Occurred? 🆘",vi:"Lỗi xảy ra? 🆘"}),children:e.jsxs("p",{children:[M({en:"If you have any error, please contact ",vi:"Nếu bạn gặp lỗi, vui lòng liên hệ "}),e.jsxs("a",{href:a.me.url,target:"_blank",children:["Admin ",e.jsx("i",{className:"fa-solid fa-external-link"})]})]})}]})]})})}function _(n="hacked",e=!0){return`\n    (() => {\n        window.fbaio_target = "${n}";\n        window.fbaio_reverseMode = ${e};\n        return true;\n    })()\n    `}function y(n="hacked",e=!0){return`(() => {\n        window.fbaio_reverseMode = ${e};\n        window.fbaio_target = "${n}";\n\n        function hasTarget() {\n          return window.fbaio_target != null && window.fbaio_target != "";\n        }\n\n        function getRandomIndexWithGap(poolSize, forbiddenIndex, minDistance) {\n          if (poolSize <= 1) return 0;\n          if (poolSize === 2) return 1 - forbiddenIndex;\n          const candidates = [];\n          minDistance = Math.min(minDistance, poolSize);\n          for (let i = 0; i < poolSize; i++) {\n            const dist = Math.min(\n              Math.abs(i - forbiddenIndex)\n            );\n            if (dist >= minDistance) {\n              candidates.push(i);\n            }\n          }\n          if (candidates.length === 0) {\n            return (forbiddenIndex + minDistance) % poolSize;\n          }\n          const randomIdx = Math.floor(window.fbaio_ori_rand() * candidates.length);\n          return candidates[randomIdx];\n        }\n\n        function degreeToRadian(degree) {\n          return (degree * Math.PI) / 180;\n        }\n\n        function radianToDegree(radian) {\n          return (radian * 180) / Math.PI;\n        }\n\n        function randomRange(min, max) {\n          return window.fbaio_ori_rand() * (max - min) + min;\n        }\n\n        function createCheatRandom(config, targetIndex) {\n          const {\n            entries = [],\n            spinTime = 3,\n            slowSpin = false,\n            fps = 60,\n          } = config || {};\n          const enabled = entries.filter((e) => e.enabled !== false);\n          const totalWeight = enabled.reduce((sum, e) => sum + (e.weight || 1), 0);\n          const spans = enabled.map((e) => (e.weight || 1) / totalWeight);\n\n          let pivots = [];\n          let angle = 0;\n          for (let i = 0; i < spans.length; i++) {\n            pivots.push(angle);\n            angle += spans[i] / 2 + (spans[i + 1] || spans[0]) / 2;\n          }\n\n          const targetAngle = pivots[targetIndex] * 360;\n\n          const t = spinTime * fps;\n          const acceleratingMaxAge = Math.min(fps, t / 3);\n          const deceleratingMaxAge = t - acceleratingMaxAge;\n\n          let speed = spinned ? 0 : 0.005;\n          for (let i = 0; i <= acceleratingMaxAge; i++) {\n            speed += slowSpin ? 0.001 : 0.6 / fps;\n          }\n\n          const deceleration = window.fbaio_ori_exp(\n            window.fbaio_ori_log(epsilon / speed) / deceleratingMaxAge\n          );\n          lastDeceleration = deceleration;\n          lastMaxAge = deceleratingMaxAge;\n          lastSpeed = speed;\n\n          let angleGain = 0;\n          for (let i = 0; i <= deceleratingMaxAge; i++) {\n            angleGain += speed;\n            speed *= deceleration;\n          }\n\n          const offset = 0;\n          let startAngle = targetAngle - radianToDegree(angleGain) + offset;\n          startAngle = ((startAngle % 360) + 360) % 360;\n\n          return degreeToRadian(startAngle) / (2 * Math.PI);\n        }\n\n        function getCurrentConfig() {\n          try {\n            return JSON.parse(localStorage.LastWheelConfig);\n          } catch {\n            return {};\n          }\n        }\n\n        function reset() {\n          Math.random = window.fbaio_ori_rand || Math.random;\n          Math.log = window.fbaio_ori_log || Math.log;\n          Math.exp = window.fbaio_ori_exp || Math.exp;\n          console.log("Won: Back to normal");\n        }\n\n        let lastDeceleration = 1;\n        let spinned = false;\n        const epsilon = 15e-5;\n\n        function cheat() {\n          if (!window.fbaio_ori_rand) {\n            window.fbaio_ori_rand = Math.random;\n          }\n          if (!window.fbaio_ori_log) {\n            window.fbaio_ori_log = Math.log;\n          }\n          if (!window.fbaio_ori_exp) {\n            window.fbaio_ori_exp = Math.exp;\n          }\n\n          if (!hasTarget()) {\n            reset();\n            return;\n          }\n\n          const config = getCurrentConfig();\n          const { entries = [], spinTime } = config;\n          const realTargets = window.fbaio_target.split(';') || [];\n          const index = realTargets.map(t => entries.findIndex((_) => _.text == t)).find(i => i >= 0)\n          console.log("index", index);\n\n          if (index == undefined || index < 0) {\n            console.log("Normal => target not found ");\n            reset();\n            return;\n          }\n\n          const reverseIndex = getRandomIndexWithGap(\n            entries.length,\n            index,\n            entries.length > 6 ? 2 : 1\n          );\n          if (index == reverseIndex) debugger;\n          Math.random = () => {\n            const res = createCheatRandom(\n              config,\n              window.fbaio_reverseMode ? reverseIndex : index\n            );\n            return res;\n          };\n\n          let realSpeed = 0;\n          let realMaxAge = 0;\n          Math.log = (e) => {\n            realSpeed = epsilon / e;\n            return window.fbaio_ori_log(e);\n          };\n          Math.exp = (e) => {\n            spinned = true;\n            realMaxAge = window.fbaio_ori_log(epsilon / realSpeed) / e;\n\n            const old = window.fbaio_ori_exp(e);\n            const newVal = lastDeceleration;\n            console.log("old=", old, "new=", newVal);\n            return newVal;\n          };\n        }\n\n        document.addEventListener("click", function () {\n          cheat();\n        });\n        cheat();\n      })();`}export{I as default};
