import{r as e,G as t,af as n,bx as r,by as o,bz as s,bA as l,m as a,bB as c,bC as i,bD as f,bE as b,bF as u,bG as p,bH as m,bI as x,bJ as y,bK as O,bL as d}from"./index-Cak6rALw.js";import{w as g}from"./PurePanel-BvHjNOhQ.js";const j=g((i=>{const{prefixCls:f,className:b,closeIcon:u,closable:p,type:m,title:x,children:y,footer:O}=i,d=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(i,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:g}=e.useContext(t),j=g(),C=f||g("modal"),P=n(j),[h,w,I]=r(C,P),E=`${C}-confirm`;let N={};return N=m?{closable:null!=p&&p,title:"",footer:"",children:e.createElement(o,Object.assign({},i,{prefixCls:C,confirmPrefixCls:E,rootPrefixCls:j,content:y}))}:{closable:null==p||p,title:x,footer:null!==O&&e.createElement(s,Object.assign({},i)),children:y},h(e.createElement(l,Object.assign({prefixCls:C,className:a(w,`${C}-pure-panel`,m&&E,m&&`${E}-${m}`,b,I,P)},d,{closeIcon:c(C,u),closable:p},N)))}));function C(e){return b(d(e))}const P=i;P.useModal=f,P.info=function(e){return b(u(e))},P.success=function(e){return b(p(e))},P.error=function(e){return b(m(e))},P.warning=C,P.warn=C,P.confirm=function(e){return b(x(e))},P.destroyAll=function(){for(;y.length;){const e=y.pop();e&&e()}},P.config=O,P._InternalPanelDoNotUseOrYouWillBeFired=j;export{P as M};
