import{y as l,x as o}from"./MyApp-DW5WH4Ub.js";async function i({groupId:i,cursor:e,search:n,statusFilter:d="ALL_STATUSES"}){var u,r;const a=await l({fb_api_req_friendly_name:"GroupsCometPeopleProfilesPaginatedListPaginationQuery",variables:{count:10,cursor:e,groupID:i,membershipType:"MEMBER",scale:1,search:n,statusStaticFilter:d,id:i},doc_id:"7804285152989966"}),t=o(a);console.log(t);const{edges:v=[],page_info:s={}}=(null==(r=null==(u=null==t?void 0:t.data)?void 0:u.node)?void 0:r.people_profiles)||{};return v.map((l=>{var o,i,e,n,d,u,r,a,t,v,p;const m=(null==(i=null==(o=null==l?void 0:l.membership)?void 0:o.meta_lines)?void 0:i.nodes)||[];return{id:null==(e=null==l?void 0:l.node)?void 0:e.id,name:null==(n=null==l?void 0:l.node)?void 0:n.name,avatar:null==(u=null==(d=null==l?void 0:l.node)?void 0:d.profile_picture)?void 0:u.uri,url:null==(r=null==l?void 0:l.node)?void 0:r.profile_url,joinedTime:null==(t=null==(a=m.find((l=>"GroupsProfileJoinedTimeMetaline"===l.__typename)))?void 0:a.text)?void 0:t.text,lastActiveTime:null==(p=null==(v=m.find((l=>"GroupsProfileLastActivityTimeMetaline"===l.__typename)))?void 0:v.text)?void 0:p.text,cursor:null==s?void 0:s.end_cursor}}))}async function e(i=""){var e,n,d,u,r,a;const t=await l({fb_api_req_friendly_name:"CometGroupMembersAdminsRootQuery",variables:{groupID:i,scale:2},doc_id:"8138328892873449"}),v=o(t);console.log(v);const s=(null==(e=null==v?void 0:v.data)?void 0:e.group)||{};return{groupId:i||"",adminCount:(null==(n=null==s?void 0:s.admin_and_moderator_profiles)?void 0:n.count)||0,memberCount:(null==(d=null==s?void 0:s.all_active_members)?void 0:d.count)||(null==(u=null==s?void 0:s.group_member_profiles)?void 0:u.count)||0,adminProfiles:null==(a=null==(r=null==s?void 0:s.group_admin_profiles)?void 0:r.edges)?void 0:a.map((l=>{var o,i,e,n,d,u;return{uid:null==(o=null==l?void 0:l.node)?void 0:o.id,name:null==(i=null==l?void 0:l.node)?void 0:i.name,avatar:null==(n=null==(e=null==l?void 0:l.node)?void 0:e.profile_picture)?void 0:n.uri,url:null==(d=null==l?void 0:l.node)?void 0:d.url,verified:null==(u=null==l?void 0:l.node)?void 0:u.is_verified}}))}}export{i as a,e as g};
