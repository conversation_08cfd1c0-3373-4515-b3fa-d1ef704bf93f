import{r as e,L as t,M as r,O as o,J as s,v as n,G as a,aw as i,af as l,m as c,t as p,Z as g}from"./index-Cak6rALw.js";import{Z as u}from"./MyApp-DW5WH4Ub.js";import{u as d,r as m}from"./useBreakpoint-CPcdMRfj.js";import{P as f}from"./index-PbLYNhdQ.js";const v=e.createContext({}),h=e=>{const{antCls:t,componentCls:r,iconCls:n,avatarBg:a,avatarColor:i,containerSize:l,containerSizeLG:c,containerSizeSM:p,textFontSize:g,textFontSizeLG:u,textFontSizeSM:d,borderRadius:m,borderRadiusLG:f,borderRadiusSM:v,lineWidth:h,lineType:b}=e,S=(e,t,o)=>({width:e,height:e,borderRadius:"50%",[`&${r}-square`]:{borderRadius:o},[`&${r}-icon`]:{fontSize:t,[`> ${n}`]:{margin:0}}});return{[r]:Object.assign(Object.assign(Object.assign(Object.assign({},o(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:i,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:a,border:`${s(h)} ${b} transparent`,"&-image":{background:"transparent"},[`${t}-image-img`]:{display:"block"}}),S(l,g,m)),{"&-lg":Object.assign({},S(c,u,f)),"&-sm":Object.assign({},S(p,d,v)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},b=e=>{const{componentCls:t,groupBorderColor:r,groupOverlapping:o,groupSpace:s}=e;return{[`${t}-group`]:{display:"inline-flex",[t]:{borderColor:r},"> *:not(:first-child)":{marginInlineStart:o}},[`${t}-group-popover`]:{[`${t} + ${t}`]:{marginInlineStart:s}}}},S=t("Avatar",(e=>{const{colorTextLightSolid:t,colorTextPlaceholder:o}=e,s=r(e,{avatarBg:o,avatarColor:t});return[h(s),b(s)]}),(e=>{const{controlHeight:t,controlHeightLG:r,controlHeightSM:o,fontSize:s,fontSizeLG:n,fontSizeXL:a,fontSizeHeading3:i,marginXS:l,marginXXS:c,colorBorderBg:p}=e;return{containerSize:t,containerSizeLG:r,containerSizeSM:o,textFontSize:Math.round((n+a)/2),textFontSizeLG:i,textFontSizeSM:s,groupSpace:c,groupOverlapping:-l,groupBorderColor:p}}));const y=e.forwardRef(((t,r)=>{const{prefixCls:o,shape:s,size:p,src:g,srcSet:f,icon:h,className:b,rootClassName:y,style:x,alt:O,draggable:z,children:j,crossOrigin:C,gap:$=4,onError:E}=t,N=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(o=Object.getOwnPropertySymbols(e);s<o.length;s++)t.indexOf(o[s])<0&&Object.prototype.propertyIsEnumerable.call(e,o[s])&&(r[o[s]]=e[o[s]])}return r}(t,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[w,k]=e.useState(1),[M,P]=e.useState(!1),[L,G]=e.useState(!0),R=e.useRef(null),B=e.useRef(null),F=n(r,R),{getPrefixCls:T,avatar:A}=e.useContext(a),H=e.useContext(v),I=()=>{if(!B.current||!R.current)return;const e=B.current.offsetWidth,t=R.current.offsetWidth;0!==e&&0!==t&&2*$<t&&k(t-2*$<e?(t-2*$)/e:1)};e.useEffect((()=>{P(!0)}),[]),e.useEffect((()=>{G(!0),k(1)}),[g]),e.useEffect(I,[$]);const W=()=>{!1!==(null==E?void 0:E())&&G(!1)},X=i((e=>{var t,r;return null!==(r=null!==(t=null!=p?p:null==H?void 0:H.size)&&void 0!==t?t:e)&&void 0!==r?r:"default"})),Z=Object.keys("object"==typeof X&&X||{}).some((e=>["xs","sm","md","lg","xl","xxl"].includes(e))),q=d(Z),J=e.useMemo((()=>{if("object"!=typeof X)return{};const e=m.find((e=>q[e])),t=X[e];return t?{width:t,height:t,fontSize:t&&(h||j)?t/2:18}:{}}),[q,X]),V=T("avatar",o),D=l(V),[K,Q,U]=S(V,D),Y=c({[`${V}-lg`]:"large"===X,[`${V}-sm`]:"small"===X}),_=e.isValidElement(g),ee=s||(null==H?void 0:H.shape)||"circle",te=c(V,Y,null==A?void 0:A.className,`${V}-${ee}`,{[`${V}-image`]:_||g&&L,[`${V}-icon`]:!!h},U,D,b,y,Q),re="number"==typeof X?{width:X,height:X,fontSize:h?X/2:18}:{};let oe;if("string"==typeof g&&L)oe=e.createElement("img",{src:g,draggable:z,srcSet:f,onError:W,alt:O,crossOrigin:C});else if(_)oe=g;else if(h)oe=h;else if(M||1!==w){const t=`scale(${w})`,r={msTransform:t,WebkitTransform:t,transform:t};oe=e.createElement(u,{onResize:I},e.createElement("span",{className:`${V}-string`,ref:B,style:Object.assign({},r)},j))}else oe=e.createElement("span",{className:`${V}-string`,style:{opacity:0},ref:B},j);return K(e.createElement("span",Object.assign({},N,{style:Object.assign(Object.assign(Object.assign(Object.assign({},re),J),null==A?void 0:A.style),x),className:te,ref:F}),oe))})),x=t=>{const{size:r,shape:o}=e.useContext(v),s=e.useMemo((()=>({size:t.size||r,shape:t.shape||o})),[t.size,t.shape,r,o]);return e.createElement(v.Provider,{value:s},t.children)},O=y;O.Group=t=>{var r,o,s,n;const{getPrefixCls:i,direction:u}=e.useContext(a),{prefixCls:d,className:m,rootClassName:v,style:h,maxCount:b,maxStyle:O,size:z,shape:j,maxPopoverPlacement:C,maxPopoverTrigger:$,children:E,max:N}=t,w=i("avatar",d),k=`${w}-group`,M=l(w),[P,L,G]=S(w,M),R=c(k,{[`${k}-rtl`]:"rtl"===u},G,M,m,v,L),B=p(E).map(((e,t)=>g(e,{key:`avatar-key-${t}`}))),F=(null==N?void 0:N.count)||b,T=B.length;if(F&&F<T){const t=B.slice(0,F),a=B.slice(F,T),i=(null==N?void 0:N.style)||O,l=(null===(r=null==N?void 0:N.popover)||void 0===r?void 0:r.trigger)||$||"hover",p=(null===(o=null==N?void 0:N.popover)||void 0===o?void 0:o.placement)||C||"top",g=Object.assign(Object.assign({content:a},null==N?void 0:N.popover),{classNames:{root:c(`${k}-popover`,null===(n=null===(s=null==N?void 0:N.popover)||void 0===s?void 0:s.classNames)||void 0===n?void 0:n.root)},placement:p,trigger:l});return t.push(e.createElement(f,Object.assign({key:"avatar-popover-key",destroyOnHidden:!0},g),e.createElement(y,{style:i},"+"+(T-F)))),P(e.createElement(x,{shape:j,size:z},e.createElement("div",{className:R,style:h},t)))}return P(e.createElement(x,{shape:j,size:z},e.createElement("div",{className:R,style:h},B)))};export{O as A};
