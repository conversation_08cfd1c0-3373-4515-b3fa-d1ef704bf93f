import{r as e,b0 as i,b5 as n,bi as t}from"./index-Cak6rALw.js";import{F as o,g as l,a as s,b as a,f as r,u as d,c}from"./follows-DFGgshWa.js";import{u as m,d as h,v as f,W as u,s as g,S as p,i as x,b as j,T as w,o as v,h as y,t as F}from"./MyApp-DW5WH4Ub.js";import k from"./MyTable-DnjDmN7z.js";import{E as b}from"./ExportButton-CbyepAf_.js";import{u as L}from"./useAction-uz-MrZwJ.js";import T from"./useCacheState-CAnxkewm.js";import N from"./WordStatisticButton-D2UxB2M4.js";import{B as O}from"./BadgeWrapper-BCbYXXfB.js";import{u as C}from"./useDevMode-yj0U7_8D.js";import{S}from"./Screen-Buq7HJpK.js";import{getAllFriends as U}from"./friends-CRlBxmhf.js";import{A as B}from"./index-DdM4T8-Q.js";import{I}from"./index-CDSnY0KE.js";import"./index-ByRdMNW-.js";import"./index-BzMPigdO.js";import"./DownOutlined-B-JcS-29.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./row-BMfM-of4.js";import"./useVIP-D5FAMGQQ.js";import"./sweetalert2.esm.all-BZxvatOx.js";import"./getIds-DAzc-wzf.js";import"./index-IxNfZAD-.js";import"./index-CC5caqt_.js";import"./index-Bp2s1Wws.js";import"./index-Bx3o6rwA.js";import"./col-CzA09p0P.js";import"./gender-D7cqwEK5.js";import"./index-PbLYNhdQ.js";function D(){const{ti:D}=m(),{message:W,notification:E}=h(),{onClickAction:_,onClickBulkActions:M}=L(),{devMode:A}=C(),[P,G]=T("Follows.friends",[]),[R,q]=T("Follows.data",[]),[V,z]=e.useState(!1),[X,H]=T("Follows.loadingFollowBulk",!1),[J,K]=T("Follows.loadingUnFollowBulk",!1),[Q,Y]=T("Follows.loadingRecord",{}),[Z,$]=T("Follows.loadingUnfollowed",!1),[ee,ie]=e.useMemo((()=>[R.filter((e=>e.status===o.FOLLOWING)),R.filter((e=>e.status===o.UNFOLLOWED))]),[R]),ne=e.useRef(null);e.useEffect((()=>{oe()}),[]);const te=e.useRef(!1),oe=async(e=!1)=>{if(te.current||V)return;te.current=!0;const i="Follows:onReload";W.loading({key:i,content:D({en:"Loading following...",vi:"Đang tải người bạn theo dõi..."}),duration:0}),z(!0);const n=e?[]:R,t=new Set(n.map((e=>e.id)));for(const[s,a]of[[l,D({en:"Loading following (option 2)... ",vi:"Đang tải người theo dõi (cách 2)... "})]]){let e="";for(;;){W.loading({key:i,content:a+n.length,duration:0});const o=await s(e);if(!(null==o?void 0:o.length))break;let l=!1;for(let e of o)t.has(e.id)||(n.push(e),t.add(e.id),l=!0);if(!l)break;q([...n.map(((e,i)=>({...e,i:i+1})))]),e=o[o.length-1].cursor,await f(300)}}W.loading({key:i,content:D({en:"Loading friends...",vi:"Đang tải danh sách bạn bè..."}),duration:0});const o=await U({myUid:await u()});G(o),W.success({key:i,content:D({en:"Finish loading follows: ",vi:"Tải xong danh sách theo dõi: "})+n.length}),z(!1),te.current=!1},le=(e,i)=>{var n,o,l,s;q((n=>t(n,(n=>{const t=n.find((i=>i.id===e.id));return t&&(t.status=i),n}))));const a=null==(o=null==(n=ne.current)?void 0:n.getDataSelected)?void 0:o.call(n);a&&(null==(s=null==(l=ne.current)?void 0:l.setDataSelected)||s.call(l,a.filter((i=>i.id!==e.id))))},se=(e,i=!1)=>(Y((i=>({...i,[e.id]:!0}))),_({record:e,id:e.id,key:"Follows:onClickFollow",actionFn:()=>A?f(1e3):r(e),loadingText:()=>D({en:"Following...",vi:"Đang theo dõi..."})+" "+e.name,successText:()=>D({en:"Followed",vi:"Đã theo dõi"})+" "+e.name,failedText:()=>D({en:"Failed to follow",vi:"Lỗi theo dõi"})+" "+e.name,onSuccess:()=>{le(e,o.FOLLOWING)},needConfirm:i,confirmProps:{title:D({en:"Follow?",vi:"Theo dõi?"}),text:e.name}}).finally((()=>{Y((i=>({...i,[e.id]:!1})))}))),ae=(e,i=!0)=>(Y((i=>({...i,[e.id]:!0}))),_({record:e,id:e.id,key:"Follows:onClickUnFollow",actionFn:()=>A?f(1e3):d(e),loadingText:()=>D({en:"Unfollowing...",vi:"Đang bỏ theo dõi..."})+" "+e.name,successText:()=>D({en:"Unfollowed",vi:"Đã bỏ theo dõi"})+" "+e.name,failedText:()=>D({en:"Failed to unfollow",vi:"Lỗi bỏ theo dõi"})+" "+e.name,onSuccess:()=>{le(e,o.UNFOLLOWED)},needConfirm:i,confirmProps:{title:D({en:"Unfollow?",vi:"Bỏ theo dõi?"}),text:e.name}}).finally((()=>{Y((i=>({...i,[e.id]:!1})))}))),re=async()=>{const e="Follows:onClickLoadUnfollowed";F(e),W.loading({key:e,content:D({en:"Loading unfollowed...",vi:"Đang tải lịch sử bỏ theo dõi..."}),duration:0}),$(!0);const n=[];let o="",l=!1;for(;!l;){const s=await c(o);if(console.log(s),!s.length)break;n.push(...s),o=s[s.length-1].cursor,W.loading({key:e,content:i.jsxs(i.Fragment,{children:[D({en:"Loading unfollowed...",vi:"Đang tải lịch sử bỏ theo dõi..."})+" "+n.length,i.jsx("br",{}),D({en:"Click to stop",vi:"Bấm để dừng"})]}),duration:0,onClick:()=>l=!0}),q((e=>t(e,(e=>{for(let i of s){const n=e.findIndex((e=>e.id===i.id));-1!==n?e[n]=i:e.unshift(i)}return e})))),await f(1e3)}W.destroy(e),E.open({type:"success",message:D({en:"Load unfollowed success",vi:"Tải xong lịch sử bỏ theo dõi"}),description:D({en:"Found "+n.length+" users you have unfollowed",vi:"Tìm thấy "+n.length+" người bạn từng bỏ theo dõi"}),duration:0}),$(!1)},de=e.useMemo((()=>{const e=new Set(P.map((e=>e.uid)));return R.filter((i=>e.has(i.id))).length}),[P,R]),ce=[{title:"#",dataIndex:"i",width:50,sorter:(e,i)=>e.i-i.i},{title:D({en:"Name",vi:"Tên"}),key:"name",dataIndex:"name",sorter:(e,i)=>(e.name||"").localeCompare(i.name||""),render:(e,n,t)=>i.jsxs(p,{align:"center",style:{maxWidth:400},children:[i.jsx(B,{shape:"square",src:i.jsx(I,{src:x(n.id),fallback:n.avatar}),size:50}),i.jsxs(p,{direction:"vertical",style:{marginLeft:"10px"},size:0,children:[i.jsx("a",{href:n.url,target:"_blank",children:i.jsx("b",{children:n.name})}),i.jsx("span",{style:{opacity:.5},children:n.id})]})]}),onSearch:(e,i,n)=>g(e,n.name+n.id),width:"auto"},{title:D({en:"Desc",vi:"Mô tả"}),key:"desc",dataIndex:"desc",render:(e,n,t)=>i.jsx("p",{style:{maxWidth:300,minWidth:50},children:e}),sorter:(e,i)=>(e.desc||"").localeCompare(i.desc||"")},{title:D({en:"Type",vi:"Loại"}),key:"type",dataIndex:"type",filters:Object.entries(s).map((([e,i])=>({text:i+" ("+R.filter((e=>e.type===i)).length+")",value:i}))),onFilter:(e,i)=>i.type===e,width:120},{title:de+" "+D({en:"Friend",vi:"Bạn bè"}),key:"friend",dataIndex:"friend",render:(e,n,t)=>new Set(P.map((e=>e.uid))).has(n.id)?i.jsx(j,{color:"success",children:D({en:"Friend",vi:"Bạn bè"})}):"-",filters:[{text:D({en:"Friend: ",vi:"Bạn bè: "})+de,value:!0},{text:D({en:"No: ",vi:"Không: "})+(R.length-de),value:!1}],onFilter:(e,i)=>new Set(P.map((e=>e.uid))).has(i.id)===e,width:120},{title:D({en:"Status",vi:"Trạng thái"}),key:"status",dataIndex:"status",render:(e,n,t)=>{const l=D(a[n.status]);return n.status===o.UNFOLLOWED?i.jsx(j,{color:"error",children:l}):l},filters:Object.entries(a).map((([e,i])=>({text:D(i)+" ("+R.filter((i=>i.status===e)).length+")",value:e}))),onFilter:(e,i)=>i.status===e,width:150},{title:D({en:"Action",vi:"Hành động"}),key:"action",dataIndex:"action",render:(e,t,l)=>t.status===o.FOLLOWING?i.jsx(w,{title:D({en:"UnFollow",vi:"Bỏ theo dõi"}),mouseEnterDelay:.5,children:i.jsx(n,{danger:!0,type:"default",icon:i.jsx("i",{className:"fa-solid fa-trash"}),onClick:()=>ae(t),loading:Q[t.id]})}):i.jsx(w,{title:D({en:"Follow",vi:"Theo dõi"}),mouseEnterDelay:.5,children:i.jsx(n,{type:"text",onClick:()=>se(t,!0),icon:i.jsx("i",{className:"fa-regular fa-square-plus fa-xl"}),loading:Q[t.id]})}),align:"right",width:100}];return i.jsx(S,{title:D({en:"Follows",vi:"Theo dõi"}),titleSuffix:i.jsx(j,{style:{marginLeft:"10px",fontWeight:"bold",color:"#888"},children:R.filter((e=>e.status===o.FOLLOWING)).length}),children:i.jsx(k,{ref:ne,data:R,columns:ce,renderTitle:e=>{const t=e.length?e.filter((e=>e.status===o.FOLLOWING)):ee,l=e.length?e.filter((e=>e.status===o.UNFOLLOWED)):ie;return i.jsxs(i.Fragment,{children:[i.jsx(n,{type:"primary",icon:V?i.jsx("i",{className:"fa-solid fa-rotate-right fa-spin"}):i.jsx("i",{className:"fa-solid fa-rotate-right"}),onClick:()=>oe(!0),children:D({en:"Reload",vi:"Tải lại"})}),i.jsx(b,{data:e.length?e:ee,options:[{key:"uid",label:".txt (uid)",prepareData:e=>({fileName:"followings_uid.txt",data:e.map((e=>e.id)).join("\n")})},{key:"uid_name",label:".csv (uid+name)",prepareData:e=>({fileName:"followings_uid_name.csv",data:v(e.map((e=>({uid:e.id,name:e.name}))))})},{key:"json",label:".json",prepareData:e=>({fileName:"followings.json",data:JSON.stringify(e,null,2)})},{key:"csv",label:".csv",prepareData:e=>({fileName:"followings.csv",data:v(e)})}]}),t.length>0&&i.jsx(w,{title:D({en:"Unfollow "+t.length+" selected",vi:"Bỏ theo dõi "+t.length+" người đang chọn"}),children:i.jsx(n,{type:"default",danger:!0,icon:i.jsx("i",{className:"fa-solid fa-trash"}),disabled:0===t.length,loading:J,onClick:()=>{return e=t,K(!0),M({data:e,key:"Follows:onClickUnFollowBulk",actionFn:e=>ae(e,!1),loadingText:e=>D({en:"Unfollowing...",vi:"Đang bỏ theo dõi..."}),successText:(e,i)=>D({en:"Unfollow finished",vi:"Đã bỏ theo dõi xong"}),successDescItem:e=>i.jsx("a",{target:"_blank",href:y(e.id),children:e.name}),confirmProps:{title:D({en:"Unfollow "+e.length+" selected?",vi:"Bỏ theo dõi "+e.length+" người?"})}}).finally((()=>{K(!1)}));var e},children:t.length})}),l.length>0&&i.jsx(w,{title:D({en:"Follow "+l.length+" selected",vi:"Theo dõi "+l.length+" người đang chọn"}),children:i.jsx(n,{type:"default",icon:i.jsx("i",{className:"fa-solid fa-plus"}),disabled:0===l.length,loading:X,onClick:()=>{return e=l,H(!0),M({data:e,key:"Follows:onClickFollowBulk",actionFn:se,loadingText:e=>D({en:"Following...",vi:"Đang theo dõi..."}),successText:(e,i)=>D({en:"Follow finished",vi:"Đã theo dõi xong"}),successDescItem:e=>i.jsx("a",{target:"_blank",href:y(e.id),children:e.name}),confirmProps:{title:D({en:"Follow "+e.length+" selected?",vi:"Theo dõi "+e.length+" người?"}),text:D({en:"Are you sure?",vi:"Bạn chắc chứ?"})}}).finally((()=>{H(!1)}));var e},children:l.length})}),i.jsx(O,{type:"new",children:i.jsx(w,{title:D({en:"Load all your unfollowed users",vi:"Tải danh sách người bạn đã bỏ theo dõi"}),children:i.jsx(n,{icon:i.jsx("i",{className:"fa-solid fa-clock"}),loading:Z,onClick:re,children:D({en:"Unfollowed",vi:"Xem lại"})})})}),i.jsx(N,{name:"Follows",text:R.map((e=>e.name)).join(" ")}),i.jsx(w,{title:D({en:"View on Facebook",vi:"Xem trên Facebook"}),children:i.jsx(n,{type:"default",icon:i.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"}),onClick:()=>window.open("https://www.facebook.com/me/following","_blank")})})]})},size:"small",searchable:!0,selectable:!0,keyExtractor:e=>e.id})})}export{D as default};
