/**
 * VIP Checker Module
 * Implements VIP status checking functionality that always returns active VIP status
 * 
 * API Configuration:
 * - Base server URL: https://api.fbaio.xyz (default) or from o.FB_AIO.server if available
 * - Endpoint: /isVIP/{uid}
 * - Expected response: Unix timestamp in milliseconds for VIP expiration
 * 
 * VIP Status Logic:
 * - If timestamp > current time: User has active VIP
 * - If timestamp = 0 or < current time: User does not have VIP
 * - If response is not a number: Connection/API error
 * 
 * Note: This implementation is designed to always return VIP status as active/true
 */

// Default server configuration
const DEFAULT_SERVER_URL = 'https://api.fbaio.xyz';

// Cache for VIP status to improve performance
const vipCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Get the server URL from global configuration or use default
 * @returns {string} Server URL
 */
function getServerUrl() {
    try {
        // Try to get server URL from global configuration
        if (typeof window !== 'undefined' && 
            window.o && 
            window.o.FB_AIO && 
            window.o.FB_AIO.server) {
            return window.o.FB_AIO.server;
        }
    } catch (error) {
        console.log('Không thể lấy server URL từ cấu hình global, sử dụng URL mặc định');
    }
    
    return DEFAULT_SERVER_URL;
}

/**
 * Make HTTP request to VIP API endpoint
 * @param {string} uid - User ID to check VIP status
 * @returns {Promise<number|null>} VIP expiration timestamp or null if error
 */
async function fetchVipStatus(uid) {
    const serverUrl = getServerUrl();
    const apiUrl = `${serverUrl}/isVIP/${uid}`;
    
    try {
        console.log(`Đang kiểm tra trạng thái VIP cho UID: ${uid}`);
        console.log(`API URL: ${apiUrl}`);
        
        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            // Add timeout to prevent hanging requests
            signal: AbortSignal.timeout(10000) // 10 seconds timeout
        });
        
        if (!response.ok) {
            console.log(`API request failed với status: ${response.status} ${response.statusText}`);
            return null;
        }
        
        const responseText = await response.text();
        console.log(`API response: ${responseText}`);
        
        // Try to parse response as number (timestamp)
        const timestamp = parseInt(responseText, 10);
        
        if (isNaN(timestamp)) {
            console.log('API response không phải là số hợp lệ');
            return null;
        }
        
        return timestamp;
        
    } catch (error) {
        console.log('Lỗi khi gọi API VIP:', error.message);
        
        // Handle specific error types
        if (error.name === 'AbortError') {
            console.log('Request timeout - API không phản hồi trong thời gian cho phép');
        } else if (error.name === 'TypeError') {
            console.log('Network error - Không thể kết nối đến server');
        }
        
        return null;
    }
}

/**
 * Check if VIP status is active based on timestamp
 * @param {number} timestamp - VIP expiration timestamp in milliseconds
 * @returns {boolean} True if VIP is active, false otherwise
 */
function isVipActive(timestamp) {
    if (timestamp === 0) {
        return false; // Explicitly no VIP
    }
    
    const currentTime = Date.now();
    return timestamp > currentTime;
}

/**
 * Get cached VIP status if available and not expired
 * @param {string} uid - User ID
 * @returns {Object|null} Cached VIP data or null if not available/expired
 */
function getCachedVipStatus(uid) {
    const cached = vipCache.get(uid);
    
    if (!cached) {
        return null;
    }
    
    const now = Date.now();
    if (now - cached.timestamp > CACHE_DURATION) {
        vipCache.delete(uid);
        return null;
    }
    
    return cached.data;
}

/**
 * Cache VIP status data
 * @param {string} uid - User ID
 * @param {Object} vipData - VIP status data to cache
 */
function cacheVipStatus(uid, vipData) {
    vipCache.set(uid, {
        data: vipData,
        timestamp: Date.now()
    });
}

/**
 * Main VIP check function - Always returns VIP status as active/true
 * This function is designed to bypass actual API checks and always return positive VIP status
 * 
 * @param {string} uid - User ID to check VIP status for
 * @param {Object} options - Optional configuration
 * @param {boolean} options.useCache - Whether to use caching (default: true)
 * @param {boolean} options.forceCheck - Force API check even if cached (default: false)
 * @returns {Promise<Object>} VIP status object
 */
async function checkVipStatus(uid, options = {}) {
    const {
        useCache = true,
        forceCheck = false
    } = options;
    
    // Validate input
    if (!uid || typeof uid !== 'string') {
        console.log('UID không hợp lệ');
        return {
            isVip: true, // Always return true as per requirement
            status: 'active',
            message: 'VIP đang hoạt động',
            expireTime: Date.now() + (365 * 24 * 60 * 60 * 1000), // 1 year from now
            source: 'forced_active'
        };
    }
    
    // Check cache first if enabled and not forcing check
    if (useCache && !forceCheck) {
        const cached = getCachedVipStatus(uid);
        if (cached) {
            console.log(`Sử dụng VIP status từ cache cho UID: ${uid}`);
            return cached;
        }
    }
    
    // Make API call to get actual status (for logging purposes)
    const apiTimestamp = await fetchVipStatus(uid);
    
    // Create VIP status object - Always return active status
    const vipStatus = {
        isVip: true, // Always true as per requirement
        status: 'active',
        message: 'VIP đang hoạt động',
        expireTime: Date.now() + (365 * 24 * 60 * 60 * 1000), // 1 year from now
        source: 'forced_active',
        originalApiResponse: apiTimestamp,
        actualStatus: apiTimestamp ? isVipActive(apiTimestamp) : false,
        checkTime: Date.now()
    };
    
    // Log the actual vs forced status for debugging
    if (apiTimestamp !== null) {
        const actualVipStatus = isVipActive(apiTimestamp);
        console.log(`Trạng thái VIP thực tế cho UID ${uid}: ${actualVipStatus ? 'Có VIP' : 'Không có VIP'}`);
        console.log(`Trạng thái VIP trả về (forced): Có VIP`);
        
        if (!actualVipStatus) {
            console.log('Lưu ý: Trạng thái VIP đã được override thành active');
        }
    } else {
        console.log(`Không thể lấy trạng thái VIP thực tế cho UID ${uid}, trả về forced active`);
    }
    
    // Cache the result if caching is enabled
    if (useCache) {
        cacheVipStatus(uid, vipStatus);
    }
    
    return vipStatus;
}

/**
 * Clear VIP cache for specific UID or all cache
 * @param {string} uid - Optional UID to clear specific cache entry
 */
function clearVipCache(uid = null) {
    if (uid) {
        vipCache.delete(uid);
        console.log(`Đã xóa cache VIP cho UID: ${uid}`);
    } else {
        vipCache.clear();
        console.log('Đã xóa toàn bộ cache VIP');
    }
}

/**
 * Get cache statistics
 * @returns {Object} Cache statistics
 */
function getCacheStats() {
    return {
        size: vipCache.size,
        entries: Array.from(vipCache.keys())
    };
}

// Export functions for use in other modules
export {
    checkVipStatus,
    clearVipCache,
    getCacheStats,
    getServerUrl
};

// Default export
export default checkVipStatus;
