import{bc as e,b3 as s,r as t,bh as n,b0 as a}from"./index-Cak6rALw.js";import{u as i,d as c}from"./MyApp-DW5WH4Ub.js";import o from"./sweetalert2.esm.all-BZxvatOx.js";function r(){const r=e(),u=s(),{ti:l}=i(),{notification:p}=c();return t.useEffect((()=>{new URLSearchParams(u.search),console.log(u);const e=l({en:"FB AIO updated",vi:"FB AIO đã được cập nhật"})+` - v${n.version}`,s=l({en:"Please turn on Autorun features again",vi:"Vui lòng bật lại các chức năng Tự động chạy"});o.fire({icon:"success",title:e,text:s,allowEscapeKey:!1,allowOutsideClick:!1,confirmButtonText:l({en:"Ok",vi:"Đã hiểu"})}),p.open({type:"success",message:e,description:s},!0),r("/autorun")}),[u]),a.jsx("div",{children:"ExtUpdated"})}export{r as default};
