const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./getIds-DAzc-wzf.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./MyApp-DW5WH4Ub.js"])))=>i.map(i=>d[i]);
import{aN as e,bj as i,aM as l}from"./index-Cak6rALw.js";import{y as o,x as n,az as t,X as a,W as r,aA as d,aB as _,aC as u,aD as s,aE as v,v as c}from"./MyApp-DW5WH4Ub.js";import{extractVideoVariants as m}from"./videos-D2LbKcXH.js";async function p(i="",l="",t=!1){if(!/^\d+$/.exec(l)){const{getPostIdFromUrl:i}=await e((async()=>{const{getPostIdFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getPostIdFromUrl:e}}),__vite__mapDeps([0,1,2,3]),import.meta.url);l=await i(l)}const a=await o({fb_api_req_friendly_name:"CometSinglePostContentQuery",variables:{feedbackSource:2,feedLocation:"PERMALINK",focusCommentID:null,privacySelectorRenderLocation:"COMET_STREAM",renderLocation:"permalink",scale:2,storyID:btoa(`S:_I${i}:${l}:${l}`),useDefaultActor:!1,__relay_internal__pv__CometImmersivePhotoCanUserDisable3DMotionrelayprovider:!1,__relay_internal__pv__IsWorkUserrelayprovider:!1,__relay_internal__pv__IsMergQAPollsrelayprovider:!1,__relay_internal__pv__CometUFIReactionsEnableShortNamerelayprovider:!1,__relay_internal__pv__CometUFIShareActionMigrationrelayprovider:!0,__relay_internal__pv__IncludeCommentWithAttachmentrelayprovider:!0,__relay_internal__pv__StoriesArmadilloReplyEnabledrelayprovider:!0,__relay_internal__pv__EventCometCardImage_prefetchEventImagerelayprovider:!1},doc_id:"8457098094310209"}),r=n(a,[],!0);return console.log(r),await y(r,!1,t)}async function y(e,i=!0,l=!1){var o,t,r,_,u,s,v,c,p,y,b,w,S;const C=i?["attached_story"]:[],I=a(e,"comment_list_renderer.feedback.comment_rendering_instance_for_feed_location",!0,!1,C),E=a(e,"comet_sections.story.message.text",!0,!1,C)||a(e,"comet_section.message_container.story.message.text",!0,!1,C),L=f(E),M=a(e,"text_format_metadata",!1,!1,C)||[],F=a(e,"comet_sections.content.story.attachments.styles.attachment",!0,!1,C),R=null==F?void 0:F.mediaset_token,k=null==F?void 0:F.media,P="Photo"===(null==k?void 0:k.__typename)||"GenericAttachmentMedia"===(null==k?void 0:k.__typename)?k:null,A=null==F?void 0:F.all_subattachments,N=(null==A?void 0:A.nodes)||[],D=((null==A?void 0:A.count)||0)>N.length,T=null==(o=null==F?void 0:F.target)?void 0:o.id,U=N.map((e=>g(e)));if(D&&T){const e=await h(T);for(let i of e){U.find((e=>e.id==i.id))||U.push(i)}}const x="Video"===(null==k?void 0:k.__typename)?k:null,O=a(x,"scrubber_preview_thumbnail_information",!0,!1,C),G=a(e,"story.to",!1,!1,C),B=a(e,"rich_message.entity_ranges",!1,!1,C)||[],W=a(e,"story.message.ranges",!1,!1,C)||[],q=Array.isArray(e)?e[0]:e;return{id:null==(r=null==(t=null==q?void 0:q.data)?void 0:t.node)?void 0:r.id,post_id:null==(u=null==(_=null==q?void 0:q.data)?void 0:_.node)?void 0:u.post_id,url:a(e,"wwwURL",!0,!1,C)||a(e,"metadata.story.url",!0,!1,C),media:{mediaset_token:R,subAttachments:U,photo:!R&&P?g(P,F):null,video:!R&&x?{id:x.id,url:x.url,thumbnail:(null==(s=x.thumbnailImage)?void 0:s.uri)||(null==(c=null==(v=x.preferred_thumbnail)?void 0:v.image)?void 0:c.uri)||(null==(p=x.image)?void 0:p.uri),duration:x.playable_duration_in_ms||0,width:x.width||0,height:x.height||0,original_width:x.original_width||0,original_height:x.original_height||0,publish_time:1e3*(x.publish_time||0),can_share:x.can_viewer_share,audio_availability:x.audio_availability,is_soundbites_video:x.is_soundbites_video,is_live_streaming:x.is_live_streaming,is_clip:x.is_clip,is_spherical:x.is_spherical,is_looping:x.is_looping,loop_count:x.loop_count,source:{sd:x.browser_native_sd_url,hd:x.browser_native_hd_url,variants:m(x.playlist)},sprite:{uris:(null==O?void 0:O.sprite_uris)||[],thumbnail_width:(null==O?void 0:O.thumbnail_width)||0,thumbnail_height:(null==O?void 0:O.thumbnail_height)||0,num_images_per_row:(null==O?void 0:O.num_images_per_row)||0,max_number_of_images_per_sprite:(null==O?void 0:O.max_number_of_images_per_sprite)||0,time_interval_between_image:(null==O?void 0:O.time_interval_between_image)||1}}:null},text:E,text_format:(null==M?void 0:M.length)?{preset_id:d(M,"preset_id"),background_color:d(M,"background_color"),background_gradient_color:d(M,"background_gradient_color"),background_gradient_direction:d(M,"background_gradient_direction"),background_image:null==(y=d(M,"background_image"))?void 0:y.uri,portrait_background_image:null==(b=d(M,"portrait_background_image"))?void 0:b.uri,color:d(M,"color"),font_weight:d(M,"font_weight"),font_style:d(M,"font_style"),text_align:d(M,"text_align")}:null,hashtags:L,metions:[...B,...W].flat().filter((e=>{var i;return"MENTION"==(null==e?void 0:e.entity_type)||(null==e?void 0:e.entity)&&"Hashtag"!=(null==(i=null==e?void 0:e.entity)?void 0:i.__typename)})).map((e=>{var i,l,o,n,t,a,r,d;const _=(null==(l=null==(i=null==e?void 0:e.entity)?void 0:i.web_link)?void 0:l.url)||(null==(o=null==e?void 0:e.entity)?void 0:o.external_url)||(null==(n=null==e?void 0:e.entity)?void 0:n.url);return{type:null==(t=null==e?void 0:e.entity)?void 0:t.__typename,id:null==(a=null==e?void 0:e.entity)?void 0:a.id,name:(null==(r=null==e?void 0:e.entity)?void 0:r.name)||(null==(d=null==e?void 0:e.entity)?void 0:d.short_name)||_,url:_,length:null==e?void 0:e.length,offset:null==e?void 0:e.offset}})),actors:a(e,"comet_sections.story.actors",!0,!1,C)||[],comments:{can_comment:a(e,"can_viewer_comment",!0,!1,C),total:(null==(w=null==I?void 0:I.comments)?void 0:w.count)||0,total_count:(null==(S=null==I?void 0:I.comments)?void 0:S.total_count)||0,sorting:{selected:null==I?void 0:I.selected_intent,available:null==I?void 0:I.selectable_intents}},reactions:{total:a(e,"comet_ufi_summary_and_actions_renderer.reaction_count.count",!0,!1,C)||0,top:(a(e,"comet_ufi_summary_and_actions_renderer.top_reactions.edges",!0,!1,C)||[]).map((e=>{var i,l;return{id:null==(i=null==e?void 0:e.node)?void 0:i.id,count:null==e?void 0:e.reaction_count,name:null==(l=null==e?void 0:e.node)?void 0:l.localized_name}}))},shares:a(e,"comet_ufi_summary_and_actions_renderer.share_count.count",!0,!1,C)||0,created_time:1e3*(a(e,"metadata.story.creation_time",!0,!1,C)||0),privacy_scope:a(e,"metadata.story.privacy_scope.description",!0,!1,C),copyright_violation_header:a(e,"copyright_violation_header",!0,!1,C),tracking_ids:n(a(e,"call_to_action.tracking",!0,!1,C)),posted_to:G?{type:a(G,"__typename",!0,!1,C),id:a(G,"id",!0,!1,C),url:a(G,"url",!0,!1,C),name:a(G,"name",!0,!1,C),avatar:a(G,"profile_picture.uri",!0,!1,C)}:null,attached_post:null,raw:l?e:void 0}}function g(e,i){var l,o,n,t,a,r,d,_,u,s,v,c,m,p,y,g;const h=(null==e?void 0:e.media)||e||{},f=(null==(l=h.viewer_image)?void 0:l.width)||(null==(o=h.image)?void 0:o.width),b=(null==(n=h.viewer_image)?void 0:n.height)||(null==(t=h.image)?void 0:t.height);return{id:h.id,url:h.url||(null==e?void 0:e.url)||(null==(d=null==(r=null==(a=null==i?void 0:i.story_attachment_link_renderer)?void 0:a.attachment)?void 0:r.web_link)?void 0:d.url)||`https://fb.com/${h.id}`,uri:(null==(_=h.viewer_image)?void 0:_.uri)||(null==(u=h.photo_image)?void 0:u.uri)||(null==(s=h.image)?void 0:s.uri)||(null==(v=h.large_share_image)?void 0:v.uri),height:(null==(c=h.photo_image)?void 0:c.height)||(null==(m=h.large_share_image)?void 0:m.height)||b,width:(null==(p=h.photo_image)?void 0:p.width)||(null==(y=h.large_share_image)?void 0:y.width)||f,accessibility_caption:h.accessibility_caption||(null==(g=null==i?void 0:i.title_with_entities)?void 0:g.text),accent_color:h.accent_color,real_width:f,real_height:b}}async function h(e){const i=await o({fb_api_req_friendly_name:"MarketplacePDPC2CMediaViewerWithImagesQuery",variables:{targetId:e},doc_id:"10059604367394414"}),l=n(i);return(a(l,"listing_photos",!0)||[]).map((e=>g(e)))}function f(e){const i=[],l=/#(\w+)/g;let o;for(;null!==(o=l.exec(e));)i.push("#"+o[1]);return i}async function b(e="",i=""){const l=await o({fb_api_req_friendly_name:"CometAlbumPhotoCollagePaginationQuery",variables:{cursor:i,count:14,renderLocation:"permalink",scale:2,id:btoa(`mediaset:${e}`)},doc_id:"7250936985006590"}),a=n(l),{edges:r=[],page_info:d={}}=t(a);return console.log("getMediasetContent",a),r.map((e=>{var i,l,o,n,t,a,r,d,_,u,s,v,c,m,p,y,g,h;return{id:null==(i=e.node)?void 0:i.id,image:null==(o=null==(l=e.node)?void 0:l.image)?void 0:o.uri,width:null==(t=null==(n=e.node)?void 0:n.image)?void 0:t.width,height:null==(r=null==(a=e.node)?void 0:a.image)?void 0:r.height,isVideo:"Video"===(null==(d=e.node)?void 0:d.__isMedia),cursor:e.cursor,created_at:1e3*((null==(u=null==(_=e.node)?void 0:_.creation_story)?void 0:u.creation_time)||0),url:null==(v=null==(s=e.node)?void 0:s.creation_story)?void 0:v.url,actors:null==(h=null==(g=null==(y=null==(p=null==(m=null==(c=e.node)?void 0:c.creation_story)?void 0:m.comet_sections)?void 0:p.actor_photo)?void 0:y.story)?void 0:g.actors)?void 0:h.map((e=>{var i;return{id:e.id,name:e.name,url:e.url,avatar:null==(i=e.profile_picture)?void 0:i.uri,type:e.__typename}}))}}))}async function w(e=100,i,o){var t,a,r,d,_;let m="https://graph.facebook.com/v14.0/me/posts?fields=id,reactions.limit(1000){id},comments.limit(1000){from{id}},created_time&limit=100&access_token="+await u(s.FBAndroid);const p=[],y=new Set;for(;;)try{const u=await l(m);v(u);const s=n(u);if(console.log(s),!Array.isArray(null==s?void 0:s.data))break;let g=!1;for(let e of s.data)y.has(e.id)||(g=!0,p.push({postId:e.id,created_time:e.created_time,reactions_uid:null==(a=null==(t=e.reactions)?void 0:t.data)?void 0:a.map((e=>e.id)),comments_uid:null==(d=null==(r=e.comments)?void 0:r.data)?void 0:d.map((e=>e.from.id))}),y.add(e.id));null==i||i(p);const h=null==(_=null==s?void 0:s.paging)?void 0:_.next;if(!g||p.length>=e||h===m||(null==o?void 0:o()))break;m=h,await c(1e3)}catch(g){console.log(g);break}return console.log(p),p}var S=(e=>(e.ALL="ALL",e.SELF="SELF",e.EVERYONE="EVERYONE",e.PUBLIC="PUBLIC",e.FRIENDS="FRIENDS",e.FRIENDS_OF_FRIENDS="FRIENDS_OF_FRIENDS",e.CUSTOM="CUSTOM",e))(S||{});const C={ALL:{en:"All",vi:"Tất cả"},EVERYONE:{en:"🌎 Everyone",vi:"🌎 Mọi người"},PUBLIC:{en:"🌎 Public",vi:"🌎 Công khai"},FRIENDS_OF_FRIENDS:{en:"👥 Friends of friends",vi:"👥 Bạn của bạn bè"},FRIENDS:{en:"🫂 Friends",vi:"🫂 Bạn bè"},SELF:{en:"🔒 Only self",vi:"🔒 Chỉ mình tôi"},CUSTOM:{en:"⚙️ Custom",vi:"⚙️ Tuỳ chỉnh"}};async function I({cursor:e=null,afterTime:i=null,beforeTime:l=null,omitPinnedPost:t=!0,privacy:a=null,taggedInOnly:d=null,uid:_="",postedByOthers:u=!1}){var s,v,c,m,p,y,g,h,f,b,w,S,C,I,E,L;const M=_||await r(),F=await o({fb_api_req_friendly_name:e?"CometManagePostsFeedRefetchQuery":"ProfileCometManagePostsTimelineRootQuery",variables:{cursor:e,afterTime:i,beforeTime:l?~~(l/1e3):null,gridMediaWidth:230,includeGroupScheduledPosts:!1,includeScheduledPosts:!1,omitPinnedPost:t,postedBy:!1===u?{group:"OWNER"}:!0===u?{group:"NON_OWNER"}:null,privacy:a&&"ALL"!==a?{exclusivity:"EXCLUSIVE",filter:a}:null,privacySelectorRenderLocation:"COMET_STREAM",scale:2,taggedInOnly:d,renderLocation:"timeline",...e?{id:M}:{userID:M},__relay_internal__pv__GHLShouldChangeSponsoredDataFieldNamerelayprovider:!0},doc_id:e?"8847372595369742":"8218825384885789"}),R=n(F,[],!0),k=[t?[]:null==(m=null==(c=null==(v=null==(s=null==R?void 0:R[0])?void 0:s.data)?void 0:v.user)?void 0:c.profile_pinned_post)?void 0:m.pinned_post_story,null==(h=null==(g=null==(y=null==(p=null==R?void 0:R[0])?void 0:p.data)?void 0:y.user)?void 0:g.timeline_manage_feed_units)?void 0:h.edges,null==(S=null==(w=null==(b=null==(f=null==R?void 0:R[0])?void 0:f.data)?void 0:b.node)?void 0:w.timeline_manage_feed_units)?void 0:S.edges,null==(L=null==(E=null==(I=null==(C=null==R?void 0:R.slice)?void 0:C.call(R,1))?void 0:I.filter)?void 0:E.call(I,(e=>{var i;return null==(i=null==e?void 0:e.path)?void 0:i.includes("edges")})))?void 0:L.map((e=>null==e?void 0:e.data))].flat().filter(Boolean).map((e=>(null==e?void 0:e.node)?e:{node:e,cursor:null,isPinned:!0}));return console.log(k),k.map((e=>{var i,l,o,n,t,a,r,d,_,u,s,v,c,m,p,y,g,h,f,b,w,S,C,I,E,L,M,F,R,k,P,A,N,D,T,U,x,O,G,B,W,q,Q,V,$,H,j,z,K,Y,X,J,Z,ee,ie,le,oe,ne,te,ae,re,de,_e,ue;const se=null==(t=null==(n=null==(o=null==(l=null==(i=null==e?void 0:e.node)?void 0:i.comet_sections)?void 0:l.actor_photo)?void 0:o.story)?void 0:n.actors)?void 0:t[0],ve=null==(u=null==(_=null==(d=null==(r=null==(a=null==e?void 0:e.node)?void 0:a.comet_sections)?void 0:r.audience)?void 0:d.story)?void 0:_.privacy_scope)?void 0:u.privacy_scope_renderer;return{id:null==(v=atob(null==(s=null==e?void 0:e.node)?void 0:s.id))?void 0:v.split(":").pop(),post_id:null==(c=null==e?void 0:e.node)?void 0:c.post_id,story_id:null==(m=null==e?void 0:e.node)?void 0:m.id,url:null==(p=null==e?void 0:e.node)?void 0:p.url,message:null==(g=null==(y=null==e?void 0:e.node)?void 0:y.message)?void 0:g.text,title:null==(f=null==(h=null==e?void 0:e.node)?void 0:h.title)?void 0:f.text,summary:null==(w=null==(b=null==e?void 0:e.node)?void 0:b.summary)?void 0:w.text,creation_time:1e3*((null==(S=null==e?void 0:e.node)?void 0:S.creation_time)||0),can_delete:null==(C=null==e?void 0:e.node)?void 0:C.can_viewer_delete,actor:{id:null==se?void 0:se.id,url:null==se?void 0:se.url,name:null==se?void 0:se.name,avatar:null==(I=null==se?void 0:se.profile_picture)?void 0:I.uri},privacy:(null==ve?void 0:ve.scope)?{id:null==(L=null==(E=null==ve?void 0:ve.scope)?void 0:E.selected_option)?void 0:L.id,scope:null==(R=null==(F=null==(M=null==ve?void 0:ve.scope)?void 0:M.selected_option)?void 0:F.privacy_row_input)?void 0:R.base_state,can_edit:null==(k=null==ve?void 0:ve.scope)?void 0:k.can_viewer_edit,deny:(null==(N=null==(A=null==(P=null==ve?void 0:ve.scope)?void 0:P.selected_option)?void 0:A.privacy_row_input)?void 0:N.deny)||[],allow:(null==(U=null==(T=null==(D=null==ve?void 0:ve.scope)?void 0:D.selected_option)?void 0:T.privacy_row_input)?void 0:U.allow)||[],label:null==(G=null==(O=null==(x=null==ve?void 0:ve.entry_point_renderer)?void 0:x.source)?void 0:O.scope)?void 0:G.label,description:null==(q=null==(W=null==(B=null==ve?void 0:ve.entry_point_renderer)?void 0:B.source)?void 0:W.scope)?void 0:q.description,write_id:null==(Q=null==ve?void 0:ve.scope)?void 0:Q.privacy_write_id}:{scope:null==(Y=null==(K=null==(z=null==(j=null==(H=null==($=null==(V=null==e?void 0:e.node)?void 0:V.comet_sections)?void 0:$.audience)?void 0:H.story)?void 0:j.privacy_scope)?void 0:z.icon_image)?void 0:K.name)?void 0:Y.toUpperCase(),description:null==(ie=null==(ee=null==(Z=null==(J=null==(X=null==e?void 0:e.node)?void 0:X.comet_sections)?void 0:J.audience)?void 0:Z.story)?void 0:ee.privacy_scope)?void 0:ie.description},content:{text:null==(re=null==(ae=null==(te=null==(ne=null==(oe=null==(le=null==e?void 0:e.node)?void 0:le.attached_story)?void 0:oe.comet_sections)?void 0:ne.message)?void 0:te.story)?void 0:ae.message)?void 0:re.text,attachments:[null==(de=null==e?void 0:e.node)?void 0:de.attachments,null==(ue=null==(_e=null==e?void 0:e.node)?void 0:_e.attached_story)?void 0:ue.attachments].filter(Boolean).flat().map((e=>{var i,l,o;const n=null==(i=null==e?void 0:e.style_type_renderer)?void 0:i.attachment;return((null==(l=null==n?void 0:n.all_subattachments)?void 0:l.nodes)||[null==(o=null==e?void 0:e.style_type_renderer)?void 0:o.attachment]).map((e=>{var i,l,o,t,a,r,d,_,u,s;return{id:null==(i=null==e?void 0:e.media)?void 0:i.id,type:null==(l=null==e?void 0:e.media)?void 0:l.__typename,title:null==e?void 0:e.title,thumbnail:(null==(t=null==(o=null==e?void 0:e.media)?void 0:o.image)?void 0:t.uri)||(null==(d=null==(r=null==(a=null==e?void 0:e.media)?void 0:a.preferred_thumbnail)?void 0:r.image)?void 0:d.uri)||(null==(u=null==(_=null==e?void 0:e.media)?void 0:_.fallback_image)?void 0:u.uri),total_count:null==(s=null==n?void 0:n.all_subattachments)?void 0:s.count}}))})).flat().filter((e=>e.thumbnail))},is_pinned:null==e?void 0:e.isPinned,cursor:null==e?void 0:e.cursor}}))}async function E({writeID:e,baseState:i}){const l=await o({fb_api_req_friendly_name:"CometPrivacySelectorSavePrivacyMutation",variables:{input:{privacy_mutation_token:null,privacy_row_input:{allow:[],base_state:i,deny:[],tag_expansion_state:"UNSPECIFIED"},privacy_write_id:e,render_location:"COMET_STORY_MENU",actor_id:await r()},privacySelectorRenderLocation:"COMET_STORY_MENU",scale:2,storyRenderLocation:"timeline",tags:null,__relay_internal__pv__CometUFIShareActionMigrationrelayprovider:!0},doc_id:"8795472927157290"}),t=n(l);return a(t,"scope.privacy_row_input.base_state")===i}async function L(e){const i=await o({fb_api_req_friendly_name:"useCometFeedStoryDeleteMutation",variables:{input:{story_id:e,story_location:"PERMALINK",actor_id:await r()},groupID:null,inviteShortLinkKey:null,renderLocation:null,scale:2},doc_id:"9052705268176005"}),l=n(i);console.log(l);return a(l,"deleted_story_id")===e}async function M(e){const i=await o({fb_api_req_friendly_name:"useCometTrashPostMutation",variables:{input:{story_id:e,story_location:"TIMELINE",actor_id:await r()}},doc_id:"5962446683854959"}),l=n(i);return a(l,"trashed_story_id")===e}async function F(e){const i=`https://www.facebook.com/profile/${e}/search?q=.&filters=${btoa(JSON.stringify({"rp_chrono_sort:0":JSON.stringify({name:"chronosort",args:""})}))}`;window.open(i,"_blank")}async function R({uid:e="",startTime:l=i,endTime:o=Date.now(),progress:n}){const t=[];for(;Math.abs(o-l)>864e5;){let i=_(l,o),a=await I({uid:e,beforeTime:i,omitPinnedPost:!0});null==n||n(i,a),(null==a?void 0:a.length)?(o=i-1,t.push(...a)):l=i+1}return t.sort(((e,i)=>e.creation_time-i.creation_time))[0]}async function k({groupId:e,cursor:i="",sorting:l="CHRONOLOGICAL"}){var t,r,d,_,u,s,v,c;const m=i?await o({fb_api_req_friendly_name:"GroupsCometFeedRegularStoriesPaginationQuery",variables:{count:3,cursor:i,feedLocation:"GROUP",feedType:"DISCUSSION",feedbackSource:0,focusCommentID:null,privacySelectorRenderLocation:"COMET_STREAM",renderLocation:"group",scale:2,sortingSetting:l,stream_initial_count:1,useDefaultActor:!1,id:e,__relay_internal__pv__GHLShouldChangeAdIdFieldNamerelayprovider:!1,__relay_internal__pv__GHLShouldChangeSponsoredDataFieldNamerelayprovider:!1,__relay_internal__pv__IsWorkUserrelayprovider:!1,__relay_internal__pv__CometFeedStoryDynamicResolutionPhotoAttachmentRenderer_experimentWidthrelayprovider:500,__relay_internal__pv__CometImmersivePhotoCanUserDisable3DMotionrelayprovider:!1,__relay_internal__pv__WorkCometIsEmployeeGKProviderrelayprovider:!1,__relay_internal__pv__IsMergQAPollsrelayprovider:!1,__relay_internal__pv__FBReelsMediaFooter_comet_enable_reels_ads_gkrelayprovider:!1,__relay_internal__pv__CometUFIReactionsEnableShortNamerelayprovider:!1,__relay_internal__pv__CometUFIShareActionMigrationrelayprovider:!0,__relay_internal__pv__StoriesArmadilloReplyEnabledrelayprovider:!0,__relay_internal__pv__EventCometCardImage_prefetchEventImagerelayprovider:!1},doc_id:"28864584756488524"}):await o({fb_api_req_friendly_name:"CometGroupDiscussionRootSuccessQuery",variables:{autoOpenChat:!1,creative_provider_id:null,feedbackSource:0,feedLocation:"GROUP",feedType:"DISCUSSION",focusCommentID:null,groupID:e,hasHoistStories:!1,hoistedSectionHeaderType:"notifications",hoistStories:[],hoistStoriesCount:0,privacySelectorRenderLocation:"COMET_STREAM",regular_stories_count:1,regular_stories_stream_initial_count:1,renderLocation:"group",scale:2,shouldDeferMainFeed:!1,sortingSetting:l,threadID:"",useDefaultActor:!1,__relay_internal__pv__GroupsCometLazyLoadFeaturedSectionrelayprovider:!1,__relay_internal__pv__EventCometCardImage_prefetchEventImagerelayprovider:!1,__relay_internal__pv__GHLShouldChangeAdIdFieldNamerelayprovider:!1,__relay_internal__pv__GHLShouldChangeSponsoredDataFieldNamerelayprovider:!1,__relay_internal__pv__IsWorkUserrelayprovider:!1,__relay_internal__pv__CometFeedStoryDynamicResolutionPhotoAttachmentRenderer_experimentWidthrelayprovider:500,__relay_internal__pv__CometImmersivePhotoCanUserDisable3DMotionrelayprovider:!1,__relay_internal__pv__WorkCometIsEmployeeGKProviderrelayprovider:!1,__relay_internal__pv__IsMergQAPollsrelayprovider:!1,__relay_internal__pv__FBReelsMediaFooter_comet_enable_reels_ads_gkrelayprovider:!1,__relay_internal__pv__CometUFIReactionsEnableShortNamerelayprovider:!1,__relay_internal__pv__CometUFIShareActionMigrationrelayprovider:!0,__relay_internal__pv__StoriesArmadilloReplyEnabledrelayprovider:!0,__relay_internal__pv__GroupsCometGroupChatLazyLoadLastMessageSnippetrelayprovider:!1},doc_id:"9165830940160555"}),p=n(m,{},!0),y=[null==(_=null==(d=null==(r=null==(t=null==p?void 0:p[0])?void 0:t.data)?void 0:r.node)?void 0:d.group_feed)?void 0:_.edges,null==(c=null==(v=null==(s=null==(u=null==p?void 0:p.slice)?void 0:u.call(p,1))?void 0:s.filter)?void 0:v.call(s,(e=>{var i;return null==(i=null==e?void 0:e.path)?void 0:i.includes("edges")})))?void 0:c.map((e=>null==e?void 0:e.data))].flat().filter(Boolean);return console.log(y),y.map((i=>{var l,o,n,t,r,d,_,u,s;const v=a(i,"story.actors",!0),c=null==(o=null==(l=(a(i,"metadata",!1)||[]).flat().find((e=>"CometFeedStoryMinimizedTimestampStrategy"===(null==e?void 0:e.__typename))))?void 0:l.story)?void 0:o.creation_time;return{id:null==(n=null==i?void 0:i.node)?void 0:n.post_id,post_id:null==(t=null==i?void 0:i.node)?void 0:t.post_id,story_id:null==(r=null==i?void 0:i.node)?void 0:r.id,actor:{id:null==(d=null==v?void 0:v[0])?void 0:d.id,name:null==(_=null==v?void 0:v[0])?void 0:_.name,url:null==(u=null==v?void 0:v[0])?void 0:u.url},creation_time:1e3*(c||0),cursor:null==i?void 0:i.cursor,url:a(i,"wwwURL")||a(i,"shareable_from_perspective_of_feed_ufi.url")||`https://www.facebook.com/${e}/posts/${null==(s=null==i?void 0:i.node)?void 0:s.post_id}`,message:a(i,"story.message.text",!0,!0)||(a(i,"rich_message")||[]).map((e=>e.text)).filter(Boolean).join("\n"),title:"",summary:"",privacy:{id:"",scope:"PUBLIC",can_edit:!1},content:{text:a(i,"attached_story.message.text",!0),attachments:(a(i,"attachments",!1)||[]).flat().filter(Boolean).map((e=>{var i,l,o,n;const t=a(e,"all_subattachments"),r=a(e,"fb_shorts_story.playback_video")||a(e,"styles.media");return{id:null==r?void 0:r.id,type:(null==r?void 0:r.__typename)||((null==r?void 0:r.length_in_seconds)?"Video":"Photo"),title:(null==r?void 0:r.title)||a(e,"attachment.web_link.url")||a(e,"styles.attachment.title"),thumbnail:a(e,"viewer_image.uri")||(null==(l=null==(i=null==r?void 0:r.preferred_thumbnail)?void 0:i.image)?void 0:l.uri)||(null==(o=null==r?void 0:r.fallback_image)?void 0:o.uri)||(null==(n=null==r?void 0:r.thumbnailImage)?void 0:n.uri)||a(e,"image.uri")||a(e,"large_share_image.uri")||a(e,"contain_fit_image.uri")||a(e,"cropped_image.uri"),total_count:(null==t?void 0:t.count)||1}})).filter((e=>null==e?void 0:e.thumbnail))}}})).filter((e=>null==e?void 0:e.id))}async function P(e){const i=await o({fb_api_req_friendly_name:"CometFeedStoryMenuQuery",variables:{feed_location:"TIMELINE",feed_menu_icon_variant:"FILLED",id:e,scale:2,serialized_frtp_identifiers:null,story_debug_info:null,__relay_internal__pv__CometFeedStoryCopyLinkMenuItem_isLinkSharingEnabledrelayprovider:!0,__relay_internal__pv__FBReels_deprecate_short_form_video_context_gkrelayprovider:!0},doc_id:"24072660595660601"});return n(i)}function A(e,i="UNTAG"){var l,o,n,t,a;return null==(a=null==(t=null==(n=null==(o=null==(l=null==e?void 0:e.data)?void 0:l.feed_unit)?void 0:o.nfx_action_menu_items)?void 0:n.find((e=>{var l;return(null==(l=null==e?void 0:e.action)?void 0:l.type)==i})))?void 0:t.action)?void 0:a.context}async function N(e){const i=await P(e),l=A(i,"UNTAG");if(console.log(i,l),!l)return!1;const t=await o({fb_api_req_friendly_name:"CometFeedStoryExecuteNFXActionMutation",variables:{input:{context:l,type:"UNTAG",actor_id:await r(),client_mutation_id:"1"},scale:2},doc_id:"29865610506387006"}),d=n(t);return console.log(d),"UNTAG"===a(d,"executed_action.type")||"UNTAG"===a(d,"action_path")}export{S as PrivacyScope,C as PrivacyScopeName,L as deletePost,A as extractContextFromFeedMenu,f as extractHashtags,y as extractPostContent,F as findAllPostsOf,R as findFirstPost,h as getAllSubAttachments,P as getFeedMenu,k as getGroupPosts,b as getMediasetContent,p as getPostContent,I as getPosts,w as getPostsInteractions,E as setPostPrivacy,M as trashPost,N as unTagPost};
