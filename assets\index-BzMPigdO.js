import{r as e,I as n,h as t,c as r,b as a,a4 as i,a5 as o,n as s,q as l,w as u,m as d,l as c,e as f,bq as p,v as g,ad as m,L as h,M as b,ah as v,O as N,J as S,ae as $,G as w,af as E,av as y,au as I,aw as x,at as R,U as k,aA as O}from"./index-Cak6rALw.js";import{R as M}from"./DownOutlined-B-JcS-29.js";import{ad as B,$ as A,a0 as j,a9 as C,a8 as D,ak as F,ao as T,ap as _,aq as G,ar as W,as as L,at as H,au as z,av as q,a5 as P,a6 as V,a7 as U}from"./MyApp-DW5WH4Ub.js";var X={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"},K=function(r,a){return e.createElement(n,t({},r,{ref:a,icon:X}))},Y=e.forwardRef(K);function J(){return"function"==typeof BigInt}function Q(e){return!e&&0!==e&&!Number.isNaN(e)||!String(e).trim()}function Z(e){var n=e.trim(),t=n.startsWith("-");t&&(n=n.slice(1)),(n=n.replace(/(\.\d*[^0])0*$/,"$1").replace(/\.0*$/,"").replace(/^0+/,"")).startsWith(".")&&(n="0".concat(n));var r=n||"0",a=r.split("."),i=a[0]||"0",o=a[1]||"0";"0"===i&&"0"===o&&(t=!1);var s=t?"-":"";return{negative:t,negativeStr:s,trimStr:r,integerStr:i,decimalStr:o,fullStr:"".concat(s).concat(r)}}function ee(e){var n=String(e);return!Number.isNaN(Number(n))&&n.includes("e")}function ne(e){var n=String(e);if(ee(e)){var t=Number(n.slice(n.indexOf("e-")+2)),r=n.match(/\.(\d+)/);return null!=r&&r[1]&&(t+=r[1].length),t}return n.includes(".")&&re(n)?n.length-n.indexOf(".")-1:0}function te(e){var n=String(e);if(ee(e)){if(e>Number.MAX_SAFE_INTEGER)return String(J()?BigInt(e).toString():Number.MAX_SAFE_INTEGER);if(e<Number.MIN_SAFE_INTEGER)return String(J()?BigInt(e).toString():Number.MIN_SAFE_INTEGER);n=e.toFixed(ne(n))}return Z(n).fullStr}function re(e){return"number"==typeof e?!Number.isNaN(e):!!e&&(/^\s*-?\d+(\.\d+)?\s*$/.test(e)||/^\s*-?\d+\.\s*$/.test(e)||/^\s*-?\.\d+\s*$/.test(e))}var ae=function(){function e(n){if(a(this,e),i(this,"origin",""),i(this,"negative",void 0),i(this,"integer",void 0),i(this,"decimal",void 0),i(this,"decimalLen",void 0),i(this,"empty",void 0),i(this,"nan",void 0),Q(n))this.empty=!0;else if(this.origin=String(n),"-"===n||Number.isNaN(n))this.nan=!0;else{var t=n;if(ee(t)&&(t=Number(t)),re(t="string"==typeof t?t:te(t))){var r=Z(t);this.negative=r.negative;var o=r.trimStr.split(".");this.integer=BigInt(o[0]);var s=o[1]||"0";this.decimal=BigInt(s),this.decimalLen=s.length}else this.nan=!0}}return r(e,[{key:"getMark",value:function(){return this.negative?"-":""}},{key:"getIntegerStr",value:function(){return this.integer.toString()}},{key:"getDecimalStr",value:function(){return this.decimal.toString().padStart(this.decimalLen,"0")}},{key:"alignDecimal",value:function(e){var n="".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(e,"0"));return BigInt(n)}},{key:"negate",value:function(){var n=new e(this.toString());return n.negative=!n.negative,n}},{key:"cal",value:function(n,t,r){var a=Math.max(this.getDecimalStr().length,n.getDecimalStr().length),i=t(this.alignDecimal(a),n.alignDecimal(a)).toString(),o=r(a),s=Z(i),l=s.negativeStr,u=s.trimStr,d="".concat(l).concat(u.padStart(o+1,"0"));return new e("".concat(d.slice(0,-o),".").concat(d.slice(-o)))}},{key:"add",value:function(n){if(this.isInvalidate())return new e(n);var t=new e(n);return t.isInvalidate()?this:this.cal(t,(function(e,n){return e+n}),(function(e){return e}))}},{key:"multi",value:function(n){var t=new e(n);return this.isInvalidate()||t.isInvalidate()?new e(NaN):this.cal(t,(function(e,n){return e*n}),(function(e){return 2*e}))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return this.nan}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(e){return this.toString()===(null==e?void 0:e.toString())}},{key:"lessEquals",value:function(e){return this.add(e.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.isNaN()?NaN:Number(this.toString())}},{key:"toString",value:function(){return!(arguments.length>0&&void 0!==arguments[0])||arguments[0]?this.isInvalidate()?"":Z("".concat(this.getMark()).concat(this.getIntegerStr(),".").concat(this.getDecimalStr())).fullStr:this.origin}}]),e}(),ie=function(){function e(n){a(this,e),i(this,"origin",""),i(this,"number",void 0),i(this,"empty",void 0),Q(n)?this.empty=!0:(this.origin=String(n),this.number=Number(n))}return r(e,[{key:"negate",value:function(){return new e(-this.toNumber())}},{key:"add",value:function(n){if(this.isInvalidate())return new e(n);var t=Number(n);if(Number.isNaN(t))return this;var r=this.number+t;if(r>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(r<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var a=Math.max(ne(this.number),ne(t));return new e(r.toFixed(a))}},{key:"multi",value:function(n){var t=Number(n);if(this.isInvalidate()||Number.isNaN(t))return new e(NaN);var r=this.number*t;if(r>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(r<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var a=Math.max(ne(this.number),ne(t));return new e(r.toFixed(a))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return Number.isNaN(this.number)}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(e){return this.toNumber()===(null==e?void 0:e.toNumber())}},{key:"lessEquals",value:function(e){return this.add(e.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.number}},{key:"toString",value:function(){return!(arguments.length>0&&void 0!==arguments[0])||arguments[0]?this.isInvalidate()?"":te(this.number):this.origin}}]),e}();function oe(e){return J()?new ae(e):new ie(e)}function se(e,n,t){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(""===e)return"";var a=Z(e),i=a.negativeStr,o=a.integerStr,s=a.decimalStr,l="".concat(n).concat(s),u="".concat(i).concat(o);if(t>=0){var d=Number(s[t]);return d>=5&&!r?se(oe(e).add("".concat(i,"0.").concat("0".repeat(t)).concat(10-d)).toString(),n,t,r):0===t?u:"".concat(u).concat(n).concat(s.padEnd(t,"0").slice(0,t))}return".0"===l?u:"".concat(u).concat(l)}function le(n){var r=n.prefixCls,a=n.upNode,o=n.downNode,c=n.upDisabled,f=n.downDisabled,p=n.onStep,g=e.useRef(),m=e.useRef([]),h=e.useRef();h.current=p;var b,v,N,S,$=function(){clearTimeout(g.current)},w=function(e,n){e.preventDefault(),$(),h.current(n),g.current=setTimeout((function e(){h.current(n),g.current=setTimeout(e,200)}),600)};if(e.useEffect((function(){return function(){$(),m.current.forEach((function(e){return u.cancel(e)}))}}),[]),b=e.useState(!1),v=s(b,2),N=v[0],S=v[1],l((function(){S(B())}),[]),N)return null;var E="".concat(r,"-handler"),y=d(E,"".concat(E,"-up"),i({},"".concat(E,"-up-disabled"),c)),I=d(E,"".concat(E,"-down"),i({},"".concat(E,"-down-disabled"),f)),x=function(){return m.current.push(u($))},R={unselectable:"on",role:"button",onMouseUp:x,onMouseLeave:x};return e.createElement("div",{className:"".concat(E,"-wrap")},e.createElement("span",t({},R,{onMouseDown:function(e){w(e,!0)},"aria-label":"Increase Value","aria-disabled":c,className:y}),a||e.createElement("span",{unselectable:"on",className:"".concat(r,"-handler-up-inner")})),e.createElement("span",t({},R,{onMouseDown:function(e){w(e,!1)},"aria-label":"Decrease Value","aria-disabled":f,className:I}),o||e.createElement("span",{unselectable:"on",className:"".concat(r,"-handler-down-inner")})))}function ue(e){var n="number"==typeof e?te(e):Z(e).fullStr;return n.includes(".")?Z(n.replace(/(\d)\.(\d)/g,"$1$2.")).fullStr:e+"0"}var de=["prefixCls","className","style","min","max","step","defaultValue","value","disabled","readOnly","upHandler","downHandler","keyboard","changeOnWheel","controls","classNames","stringMode","parser","formatter","precision","decimalSeparator","onChange","onInput","onPressEnter","onStep","changeOnBlur","domRef"],ce=["disabled","style","prefixCls","value","prefix","suffix","addonBefore","addonAfter","className","classNames"],fe=function(e,n){return e||n.isEmpty()?n.toString():n.toNumber()},pe=function(e){var n=oe(e);return n.isInvalidate()?null:n},ge=e.forwardRef((function(n,r){var a=n.prefixCls,l=n.className,m=n.style,h=n.min,b=n.max,v=n.step,N=void 0===v?1:v,S=n.defaultValue,$=n.value,w=n.disabled,E=n.readOnly,y=n.upHandler,I=n.downHandler,x=n.keyboard,R=n.changeOnWheel,k=void 0!==R&&R,O=n.controls,M=void 0===O||O;n.classNames;var B=n.stringMode,A=n.parser,j=n.formatter,C=n.precision,D=n.decimalSeparator,F=n.onChange,T=n.onInput,_=n.onPressEnter,G=n.onStep,W=n.changeOnBlur,L=void 0===W||W,H=n.domRef,z=c(n,de),q="".concat(a,"-input"),P=e.useRef(null),V=e.useState(!1),U=s(V,2),X=U[0],K=U[1],Y=e.useRef(!1),J=e.useRef(!1),Q=e.useRef(!1),Z=e.useState((function(){return oe(null!=$?$:S)})),ee=s(Z,2),ae=ee[0],ie=ee[1];var ce=e.useCallback((function(e,n){if(!n)return C>=0?C:Math.max(ne(e),ne(N))}),[C,N]),ge=e.useCallback((function(e){var n=String(e);if(A)return A(n);var t=n;return D&&(t=t.replace(D,".")),t.replace(/[^\w.-]+/g,"")}),[A,D]),me=e.useRef(""),he=e.useCallback((function(e,n){if(j)return j(e,{userTyping:n,input:String(me.current)});var t="number"==typeof e?te(e):e;if(!n){var r=ce(t,n);if(re(t)&&(D||r>=0))t=se(t,D||".",r)}return t}),[j,ce,D]),be=e.useState((function(){var e=null!=S?S:$;return ae.isInvalidate()&&["string","number"].includes(f(e))?Number.isNaN(e)?"":e:he(ae.toString(),!1)})),ve=s(be,2),Ne=ve[0],Se=ve[1];function $e(e,n){Se(he(e.isInvalidate()?e.toString(!1):e.toString(!n),n))}me.current=Ne;var we,Ee,ye,Ie,xe,Re=e.useMemo((function(){return pe(b)}),[b,C]),ke=e.useMemo((function(){return pe(h)}),[h,C]),Oe=e.useMemo((function(){return!(!Re||!ae||ae.isInvalidate())&&Re.lessEquals(ae)}),[Re,ae]),Me=e.useMemo((function(){return!(!ke||!ae||ae.isInvalidate())&&ae.lessEquals(ke)}),[ke,ae]),Be=(we=P.current,Ee=X,ye=e.useRef(null),[function(){try{var e=we.selectionStart,n=we.selectionEnd,t=we.value,r=t.substring(0,e),a=t.substring(n);ye.current={start:e,end:n,value:t,beforeTxt:r,afterTxt:a}}catch(i){}},function(){if(we&&ye.current&&Ee)try{var e=we.value,n=ye.current,t=n.beforeTxt,r=n.afterTxt,a=n.start,i=e.length;if(e.startsWith(t))i=t.length;else if(e.endsWith(r))i=e.length-ye.current.afterTxt.length;else{var s=t[a-1],l=e.indexOf(s,a-1);-1!==l&&(i=l+1)}we.setSelectionRange(i,i)}catch(u){o(!1,"Something warning of cursor restore. Please fire issue about this: ".concat(u.message))}}]),Ae=s(Be,2),je=Ae[0],Ce=Ae[1],De=function(e){return Re&&!e.lessEquals(Re)?Re:ke&&!ke.lessEquals(e)?ke:null},Fe=function(e){return!De(e)},Te=function(e,n){var t,r=e,a=Fe(r)||r.isEmpty();if(r.isEmpty()||n||(r=De(r)||r,a=!0),!E&&!w&&a){var i=r.toString(),o=ce(i,n);return o>=0&&(r=oe(se(i,".",o)),Fe(r)||(r=oe(se(i,".",o,!0)))),r.equals(ae)||(t=r,void 0===$&&ie(t),null==F||F(r.isEmpty()?null:fe(B,r)),void 0===$&&$e(r,n)),r}return ae},_e=(Ie=e.useRef(0),xe=function(){u.cancel(Ie.current)},e.useEffect((function(){return xe}),[]),function(e){xe(),Ie.current=u((function(){e()}))}),Ge=function e(n){if(je(),me.current=n,Se(n),!J.current){var t=oe(ge(n));t.isNaN()||Te(t,!0)}null==T||T(n),_e((function(){var t=n;A||(t=n.replace(/。/g,".")),t!==n&&e(t)}))},We=function(e){var n;if(!(e&&Oe||!e&&Me)){Y.current=!1;var t=oe(Q.current?ue(N):N);e||(t=t.negate());var r=(ae||oe(0)).add(t.toString()),a=Te(r,!1);null==G||G(fe(B,a),{offset:Q.current?ue(N):N,type:e?"up":"down"}),null===(n=P.current)||void 0===n||n.focus()}},Le=function(e){var n,t=oe(ge(Ne));n=t.isNaN()?Te(ae,e):Te(t,e),void 0!==$?$e(ae,!1):n.isNaN()||$e(n,!1)};e.useEffect((function(){if(k&&X){var e=function(e){We(e.deltaY<0),e.preventDefault()},n=P.current;if(n)return n.addEventListener("wheel",e,{passive:!1}),function(){return n.removeEventListener("wheel",e)}}}));return p((function(){ae.isInvalidate()||$e(ae,!1)}),[C,j]),p((function(){var e=oe($);ie(e);var n=oe(ge(Ne));e.equals(n)&&Y.current&&!j||$e(e,Y.current)}),[$]),p((function(){j&&Ce()}),[Ne]),e.createElement("div",{ref:H,className:d(a,l,i(i(i(i(i({},"".concat(a,"-focused"),X),"".concat(a,"-disabled"),w),"".concat(a,"-readonly"),E),"".concat(a,"-not-a-number"),ae.isNaN()),"".concat(a,"-out-of-range"),!ae.isInvalidate()&&!Fe(ae))),style:m,onFocus:function(){K(!0)},onBlur:function(){L&&Le(!1),K(!1),Y.current=!1},onKeyDown:function(e){var n=e.key,t=e.shiftKey;Y.current=!0,Q.current=t,"Enter"===n&&(J.current||(Y.current=!1),Le(!1),null==_||_(e)),!1!==x&&!J.current&&["Up","ArrowUp","Down","ArrowDown"].includes(n)&&(We("Up"===n||"ArrowUp"===n),e.preventDefault())},onKeyUp:function(){Y.current=!1,Q.current=!1},onCompositionStart:function(){J.current=!0},onCompositionEnd:function(){J.current=!1,Ge(P.current.value)},onBeforeInput:function(){Y.current=!0}},M&&e.createElement(le,{prefixCls:a,upNode:y,downNode:I,upDisabled:Oe,downDisabled:Me,onStep:We}),e.createElement("div",{className:"".concat(q,"-wrap")},e.createElement("input",t({autoComplete:"off",role:"spinbutton","aria-valuemin":h,"aria-valuemax":b,"aria-valuenow":ae.isInvalidate()?null:ae.toString(),step:N},z,{ref:g(P,r),className:q,value:Ne,onChange:function(e){Ge(e.target.value)},disabled:w,readOnly:E}))))})),me=e.forwardRef((function(n,r){var a=n.disabled,i=n.style,o=n.prefixCls,s=void 0===o?"rc-input-number":o,l=n.value,u=n.prefix,d=n.suffix,f=n.addonBefore,p=n.addonAfter,g=n.className,m=n.classNames,h=c(n,ce),b=e.useRef(null),v=e.useRef(null),N=e.useRef(null),S=function(e){N.current&&j(N.current,e)};return e.useImperativeHandle(r,(function(){return e=N.current,n={focus:S,nativeElement:b.current.nativeElement||v.current},"undefined"!=typeof Proxy&&e?new Proxy(e,{get:function(e,t){if(n[t])return n[t];var r=e[t];return"function"==typeof r?r.bind(e):r}}):e;var e,n})),e.createElement(A,{className:g,triggerFocus:S,prefixCls:s,value:l,disabled:a,style:i,prefix:u,suffix:d,addonAfter:p,addonBefore:f,classNames:m,components:{affixWrapper:"div",groupWrapper:"div",wrapper:"div",groupAddon:"div"},ref:b},e.createElement(ge,t({prefixCls:s,disabled:a,ref:N,domRef:v,className:null==m?void 0:m.input},h)))}));const he=({componentCls:e,borderRadiusSM:n,borderRadiusLG:t},r)=>{const a="lg"===r?t:n;return{[`&-${r}`]:{[`${e}-handler-wrap`]:{borderStartEndRadius:a,borderEndEndRadius:a},[`${e}-handler-up`]:{borderStartEndRadius:a},[`${e}-handler-down`]:{borderEndEndRadius:a}}}},be=e=>{const{componentCls:n,lineWidth:t,lineType:r,borderRadius:a,inputFontSizeSM:i,inputFontSizeLG:o,controlHeightLG:s,controlHeightSM:l,colorError:u,paddingInlineSM:d,paddingBlockSM:c,paddingBlockLG:f,paddingInlineLG:p,colorIcon:g,motionDurationMid:m,handleHoverColor:h,handleOpacity:b,paddingInline:v,paddingBlock:w,handleBg:E,handleActiveBg:y,colorTextDisabled:I,borderRadiusSM:x,borderRadiusLG:R,controlWidth:k,handleBorderColor:O,filledHandleBg:M,lineHeightLG:B,calc:A}=e;return[{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},N(e)),F(e)),{display:"inline-block",width:k,margin:0,padding:0,borderRadius:a}),T(e,{[`${n}-handler-wrap`]:{background:E,[`${n}-handler-down`]:{borderBlockStart:`${S(t)} ${r} ${O}`}}})),_(e,{[`${n}-handler-wrap`]:{background:M,[`${n}-handler-down`]:{borderBlockStart:`${S(t)} ${r} ${O}`}},"&:focus-within":{[`${n}-handler-wrap`]:{background:E}}})),G(e,{[`${n}-handler-wrap`]:{background:E,[`${n}-handler-down`]:{borderBlockStart:`${S(t)} ${r} ${O}`}}})),W(e)),{"&-rtl":{direction:"rtl",[`${n}-input`]:{direction:"rtl"}},"&-lg":{padding:0,fontSize:o,lineHeight:B,borderRadius:R,[`input${n}-input`]:{height:A(s).sub(A(t).mul(2)).equal(),padding:`${S(f)} ${S(p)}`}},"&-sm":{padding:0,fontSize:i,borderRadius:x,[`input${n}-input`]:{height:A(l).sub(A(t).mul(2)).equal(),padding:`${S(c)} ${S(d)}`}},"&-out-of-range":{[`${n}-input-wrap`]:{input:{color:u}}},"&-group":Object.assign(Object.assign(Object.assign({},N(e)),H(e)),{"&-wrapper":Object.assign(Object.assign(Object.assign({display:"inline-block",textAlign:"start",verticalAlign:"top",[`${n}-affix-wrapper`]:{width:"100%"},"&-lg":{[`${n}-group-addon`]:{borderRadius:R,fontSize:e.fontSizeLG}},"&-sm":{[`${n}-group-addon`]:{borderRadius:x}}},z(e)),q(e)),{[`&:not(${n}-compact-first-item):not(${n}-compact-last-item)${n}-compact-item`]:{[`${n}, ${n}-group-addon`]:{borderRadius:0}},[`&:not(${n}-compact-last-item)${n}-compact-first-item`]:{[`${n}, ${n}-group-addon`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&:not(${n}-compact-first-item)${n}-compact-last-item`]:{[`${n}, ${n}-group-addon`]:{borderStartStartRadius:0,borderEndStartRadius:0}}})}),[`&-disabled ${n}-input`]:{cursor:"not-allowed"},[n]:{"&-input":Object.assign(Object.assign(Object.assign(Object.assign({},N(e)),{width:"100%",padding:`${S(w)} ${S(v)}`,textAlign:"start",backgroundColor:"transparent",border:0,borderRadius:a,outline:0,transition:`all ${m} linear`,appearance:"textfield",fontSize:"inherit"}),L(e.colorTextPlaceholder)),{'&[type="number"]::-webkit-inner-spin-button, &[type="number"]::-webkit-outer-spin-button':{margin:0,appearance:"none"}})},[`&:hover ${n}-handler-wrap, &-focused ${n}-handler-wrap`]:{width:e.handleWidth,opacity:1}})},{[n]:Object.assign(Object.assign(Object.assign({[`${n}-handler-wrap`]:{position:"absolute",insetBlockStart:0,insetInlineEnd:0,width:e.handleVisibleWidth,opacity:b,height:"100%",borderStartStartRadius:0,borderStartEndRadius:a,borderEndEndRadius:a,borderEndStartRadius:0,display:"flex",flexDirection:"column",alignItems:"stretch",transition:`all ${m}`,overflow:"hidden",[`${n}-handler`]:{display:"flex",alignItems:"center",justifyContent:"center",flex:"auto",height:"40%",[`\n              ${n}-handler-up-inner,\n              ${n}-handler-down-inner\n            `]:{marginInlineEnd:0,fontSize:e.handleFontSize}}},[`${n}-handler`]:{height:"50%",overflow:"hidden",color:g,fontWeight:"bold",lineHeight:0,textAlign:"center",cursor:"pointer",borderInlineStart:`${S(t)} ${r} ${O}`,transition:`all ${m} linear`,"&:active":{background:y},"&:hover":{height:"60%",[`\n              ${n}-handler-up-inner,\n              ${n}-handler-down-inner\n            `]:{color:h}},"&-up-inner, &-down-inner":Object.assign(Object.assign({},$()),{color:g,transition:`all ${m} linear`,userSelect:"none"})},[`${n}-handler-up`]:{borderStartEndRadius:a},[`${n}-handler-down`]:{borderEndEndRadius:a}},he(e,"lg")),he(e,"sm")),{"&-disabled, &-readonly":{[`${n}-handler-wrap`]:{display:"none"},[`${n}-input`]:{color:"inherit"}},[`\n          ${n}-handler-up-disabled,\n          ${n}-handler-down-disabled\n        `]:{cursor:"not-allowed"},[`\n          ${n}-handler-up-disabled:hover &-handler-up-inner,\n          ${n}-handler-down-disabled:hover &-handler-down-inner\n        `]:{color:I}})}]},ve=e=>{const{componentCls:n,paddingBlock:t,paddingInline:r,inputAffixPadding:a,controlWidth:i,borderRadiusLG:o,borderRadiusSM:s,paddingInlineLG:l,paddingInlineSM:u,paddingBlockLG:d,paddingBlockSM:c,motionDurationMid:f}=e;return{[`${n}-affix-wrapper`]:Object.assign(Object.assign({[`input${n}-input`]:{padding:`${S(t)} 0`}},F(e)),{position:"relative",display:"inline-flex",alignItems:"center",width:i,padding:0,paddingInlineStart:r,"&-lg":{borderRadius:o,paddingInlineStart:l,[`input${n}-input`]:{padding:`${S(d)} 0`}},"&-sm":{borderRadius:s,paddingInlineStart:u,[`input${n}-input`]:{padding:`${S(c)} 0`}},[`&:not(${n}-disabled):hover`]:{zIndex:1},"&-focused, &:focus":{zIndex:1},[`&-disabled > ${n}-disabled`]:{background:"transparent"},[`> div${n}`]:{width:"100%",border:"none",outline:"none",[`&${n}-focused`]:{boxShadow:"none !important"}},"&::before":{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'},[`${n}-handler-wrap`]:{zIndex:2},[n]:{position:"static",color:"inherit","&-prefix, &-suffix":{display:"flex",flex:"none",alignItems:"center",pointerEvents:"none"},"&-prefix":{marginInlineEnd:a},"&-suffix":{insetBlockStart:0,insetInlineEnd:0,height:"100%",marginInlineEnd:r,marginInlineStart:a,transition:`margin ${f}`}},[`&:hover ${n}-handler-wrap, &-focused ${n}-handler-wrap`]:{width:e.handleWidth,opacity:1},[`&:not(${n}-affix-wrapper-without-controls):hover ${n}-suffix`]:{marginInlineEnd:e.calc(e.handleWidth).add(r).equal()}})}},Ne=h("InputNumber",(e=>{const n=b(e,D(e));return[be(n),ve(n),v(n)]}),(e=>{var n;const t=null!==(n=e.handleVisible)&&void 0!==n?n:"auto",r=e.controlHeightSM-2*e.lineWidth;return Object.assign(Object.assign({},C(e)),{controlWidth:90,handleWidth:r,handleFontSize:e.fontSize/2,handleVisible:t,handleActiveBg:e.colorFillAlter,handleBg:e.colorBgContainer,filledHandleBg:new m(e.colorFillSecondary).onBackground(e.colorBgContainer).toHexString(),handleHoverColor:e.colorPrimary,handleBorderColor:e.colorBorder,handleOpacity:!0===t?1:0,handleVisibleWidth:!0===t?r:0})}),{unitless:{handleOpacity:!0}});const Se=e.forwardRef(((n,t)=>{const{getPrefixCls:r,direction:a}=e.useContext(w),i=e.useRef(null);e.useImperativeHandle(t,(()=>i.current));const{className:o,rootClassName:s,size:l,disabled:u,prefixCls:c,addonBefore:f,addonAfter:p,prefix:g,suffix:m,bordered:h,readOnly:b,status:v,controls:N,variant:S}=n,$=function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]])}return t}(n,["className","rootClassName","size","disabled","prefixCls","addonBefore","addonAfter","prefix","suffix","bordered","readOnly","status","controls","variant"]),O=r("input-number",c),B=E(O),[A,j,C]=Ne(O,B),{compactSize:D,compactItemClassnames:F}=y(O,a);let T=e.createElement(Y,{className:`${O}-handler-up-inner`}),_=e.createElement(M,{className:`${O}-handler-down-inner`});const G="boolean"==typeof N?N:void 0;"object"==typeof N&&(T=void 0===N.upIcon?T:e.createElement("span",{className:`${O}-handler-up-inner`},N.upIcon),_=void 0===N.downIcon?_:e.createElement("span",{className:`${O}-handler-down-inner`},N.downIcon));const{hasFeedback:W,status:L,isFormItemInput:H,feedbackIcon:z}=e.useContext(I),q=U(L,v),X=x((e=>{var n;return null!==(n=null!=l?l:D)&&void 0!==n?n:e})),K=e.useContext(R),J=null!=u?u:K,[Q,Z]=P("inputNumber",S,h),ee=W&&e.createElement(e.Fragment,null,z),ne=d({[`${O}-lg`]:"large"===X,[`${O}-sm`]:"small"===X,[`${O}-rtl`]:"rtl"===a,[`${O}-in-form-item`]:H},j),te=`${O}-group`;return A(e.createElement(me,Object.assign({ref:i,disabled:J,className:d(C,B,o,s,F),upHandler:T,downHandler:_,prefixCls:O,readOnly:b,controls:G,prefix:g,suffix:ee||m,addonBefore:f&&e.createElement(k,{form:!0,space:!0},f),addonAfter:p&&e.createElement(k,{form:!0,space:!0},p),classNames:{input:ne,variant:d({[`${O}-${Q}`]:Z},V(O,q,W)),affixWrapper:d({[`${O}-affix-wrapper-sm`]:"small"===X,[`${O}-affix-wrapper-lg`]:"large"===X,[`${O}-affix-wrapper-rtl`]:"rtl"===a,[`${O}-affix-wrapper-without-controls`]:!1===N||J},j),wrapper:d({[`${te}-rtl`]:"rtl"===a},j),groupWrapper:d({[`${O}-group-wrapper-sm`]:"small"===X,[`${O}-group-wrapper-lg`]:"large"===X,[`${O}-group-wrapper-rtl`]:"rtl"===a,[`${O}-group-wrapper-${Q}`]:Z},V(`${O}-group-wrapper`,q,W),j)}},$)))})),$e=Se;$e._InternalPanelDoNotUseOrYouWillBeFired=n=>e.createElement(O,{theme:{components:{InputNumber:{handleVisible:!0}}}},e.createElement(Se,Object.assign({},n)));export{$e as T};
