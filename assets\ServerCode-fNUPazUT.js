const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./CodeBlock-BP5ddTLM.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./MyApp-DW5WH4Ub.js","./jszip.min-BQprNwoi.js","./FileSaver.min-B-NmbgGb.js"])))=>i.map(i=>d[i]);
import{r as e,b0 as n,b1 as t,b5 as s,aN as r}from"./index-Cak6rALw.js";import{u as o,d as i,S as a,c,f as l,e as d,b as p,C as m}from"./MyApp-DW5WH4Ub.js";import{C as u}from"./col-CzA09p0P.js";import{T as h}from"./index-IxNfZAD-.js";import{C as g}from"./index-D7dTP8lW.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./Dropdown-BcL-JHCf.js";const j=t((()=>r((()=>import("./CodeBlock-BP5ddTLM.js")),__vite__mapDeps([0,1,2,3]),import.meta.url)),{fallback:m}),x=1751322580206;function f(){const{ti:t}=o(),{message:m}=i(),[f,T]=e.useState(!1),b=[{name:"server.js",content:w,language:"javascript"},{name:"package.json",content:I,language:"json"},{name:"README.md",content:v,language:"javascript"}],E=async()=>{T(!0);const e=(await r((async()=>{const{default:e}=await import("./jszip.min-BQprNwoi.js").then((e=>e.j));return{default:e}}),__vite__mapDeps([4,1,2]),import.meta.url)).default,n=(await r((async()=>{const{default:e}=await import("./FileSaver.min-B-NmbgGb.js").then((e=>e.F));return{default:e}}),__vite__mapDeps([5,1,2]),import.meta.url)).default,s=new e;b.forEach((e=>{s.file(e.name,e.content)})),s.generateAsync({type:"blob"}).then((e=>{n(e,"fbaio-server.zip"),m.success(t({en:"Download success",vi:"Tải xuống thành công"}))})).catch((e=>{m.error(t({en:"Download failed",vi:"Tải xuống thất bại"})+" "+e.message)})).finally((()=>{T(!1)}))};return n.jsx(u,{children:n.jsxs(a,{direction:"vertical",style:{width:" 100%"},size:"small",children:[n.jsxs(c.Text,{type:"secondary",children:[t({en:"Last updated",vi:"Cập nhật lần cuối"}),": ",l(x)," ","(",d(new Date(x).getTime())+t({en:" ago",vi:" trước"}),")"]}),n.jsx(g,{size:"small",title:t({en:"Tutorial",vi:"Hướng dẫn"}),children:n.jsxs("ol",{children:[n.jsxs("li",{children:[t({vi:"Cài đặt",en:"Install"})," ",n.jsx("a",{href:"https://nodejs.org/en/download/",target:"_blank",children:"node.js"})," (>=18) ",t({vi:" và ",en:" and "}),n.jsx("a",{href:"https://docs.npmjs.com/downloading-and-installing-node-js-and-npm",target:"_blank",children:"npm"})]}),n.jsxs("li",{children:[t({vi:"Tải về ",en:"Download "}),n.jsx(s,{type:"link",size:"small",onClick:E,icon:n.jsx("i",{className:"fa-solid fa-download"}),loading:f,children:t({en:"Server code",vi:"Mã nguồn server"})})]}),n.jsx("li",{children:t({vi:n.jsxs(n.Fragment,{children:["Mở terminal và"," ",n.jsx(p,{children:n.jsx("code",{children:"cd"})}),"tới folder vừa tải về"]}),en:n.jsxs(n.Fragment,{children:["Open terminal and"," ",n.jsx(p,{children:n.jsx("code",{children:"cd"})}),"to the downloaded folder"]})})}),n.jsx("li",{children:t({vi:n.jsxs(n.Fragment,{children:["Chạy lệnh"," ",n.jsx(p,{children:n.jsx("code",{children:"npm install"})}),"để cài đặt"]}),en:n.jsxs(n.Fragment,{children:["Run command"," ",n.jsx(p,{children:n.jsx("code",{children:"npm install"})}),"to install packages"]})})}),n.jsx("li",{children:t({vi:n.jsxs(n.Fragment,{children:["Chạy lệnh"," ",n.jsx(p,{children:n.jsx("code",{children:"npm start"})}),"để chạy server"]}),en:n.jsxs(n.Fragment,{children:["Run command"," ",n.jsx(p,{children:n.jsx("code",{children:"npm start"})}),"to run the server"]})})}),n.jsx("li",{children:t({vi:n.jsxs(n.Fragment,{children:["Nhập link máy chủ ",n.jsx(p,{children:"http://localhost:3000"}),"vào FB AIO và Kết nối"]}),en:n.jsxs(n.Fragment,{children:["Enter server url",n.jsx(p,{children:"http://localhost:3000"}),"into FB AIO and Connect"]})})})]})}),n.jsx(h,{items:b.map((e=>({key:e.name,label:e.name,children:n.jsx(j,{code:e.content,language:e.language})})))})]})})}const v="// Require Node.js v18 or higher\nnpm install && npm start",w="const express = require('express');\nconst cors = require('cors');\nconst WebSocket = require('ws');\n\nconst app = express();\nconst port = process.env.PORT || 3000;\n\nconst clients = new Map();\nconst pendingRequests = new Map();\nconst clientHeartbeats = new Map();\n\nconst HEARTBEAT_INTERVAL = 30000; // 30 seconds\nconst REQUEST_TIMEOUT = 60000; // 1 minute\nconst MAX_RECONNECT_ATTEMPTS = 5;\n\napp.use(cors({ origin: '*' }));\napp.use(express.json());\n\nfunction generateRequestId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n\n// Heartbeat check\nsetInterval(() => {\n    const now = Date.now();\n    for (const [id, lastHeartbeat] of clientHeartbeats.entries()) {\n        if (now - lastHeartbeat > HEARTBEAT_INTERVAL * 2) {\n            const ws = clients.get(id);\n            if (ws) {\n                ws.terminate();\n                clients.delete(id);\n                clientHeartbeats.delete(id);\n                console.log('🔌 Client ' + id + ' disconnected due to heartbeat timeout');\n            }\n        }\n    }\n}, HEARTBEAT_INTERVAL);\n\napp.post('/call', (req, res) => {\n    const { id, apiname, apiparams } = req?.body || {};\n\n    console.log(req?.headers, req?.body);\n\n    if (!id || !apiname) {\n        return res.status(400).json({ error: 'Missing required parameters' });\n    }\n\n    const ws = clients.get(id);\n    if (!ws) {\n        return res.status(404).json({ error: 'Client not connected' });\n    }\n\n    const requestId = generateRequestId();\n    pendingRequests.set(requestId, res);\n\n    try {\n        ws.send(\n            JSON.stringify({\n                type: 'api_call',\n                requestId,\n                apiname,\n                apiparams\n            })\n        );\n    } catch (err) {\n        pendingRequests.delete(requestId);\n        return res.status(500).json({ error: 'Failed to send request to client' });\n    }\n\n    setTimeout(() => {\n        if (pendingRequests.has(requestId)) {\n            res.status(504).json({ error: 'Timeout waiting for response' });\n            pendingRequests.delete(requestId);\n        }\n    }, REQUEST_TIMEOUT);\n});\n\nconst server = app.listen(port, () => {\n    console.log('🌐 Server running on port ' + port);\n});\n\nconst wss = new WebSocket.Server({ server });\n\nwss.on('connection', (ws, req) => {\n    console.log('⚡ WebSocket connected');\n\n    let clientId = null;\n    let reconnectAttempts = 0;\n\n    ws.on('message', msg => {\n        try {\n            const data = JSON.parse(msg);\n\n            if (data.type === 'register') {\n                if (clientId) {\n                    console.log(\n                        '⚠️ Client ' + data.id + ' already registered, updating connection'\n                    );\n                }\n                clientId = data.id;\n                clients.set(clientId, ws);\n                clientHeartbeats.set(clientId, Date.now());\n                console.log('✅ Registered client: ' + clientId + ' (total: ' + clients.size + ')');\n                reconnectAttempts = 0;\n            } else if (data.type === 'response') {\n                const res = pendingRequests.get(data.requestId);\n                console.log('response', data);\n                if (res) {\n                    res.json({ result: data.result, error: data.error });\n                    pendingRequests.delete(data.requestId);\n                }\n            } else if (data.type === 'heartbeat') {\n                if (clientId) {\n                    clientHeartbeats.set(clientId, Date.now());\n                }\n            } else if (data.type === 'reconnect') {\n                if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {\n                    ws.send(\n                        JSON.stringify({\n                            type: 'error',\n                            error: 'Maximum reconnection attempts reached'\n                        })\n                    );\n                    ws.close();\n                    return;\n                }\n                reconnectAttempts++;\n                console.log(\n                    '🔄 Reconnection attempt ' + reconnectAttempts + ' for client ' + clientId\n                );\n            }\n        } catch (err) {\n            console.error('❌ Invalid message', err);\n        }\n    });\n\n    ws.on('close', () => {\n        if (clientId) {\n            clients.delete(clientId);\n            clientHeartbeats.delete(clientId);\n            console.log(\n                '🔌 WebSocket disconnected: ' + clientId + ' (total: ' + clients.size + ')'\n            );\n        }\n    });\n\n    ws.on('error', error => {\n        console.error('❌ WebSocket error for client ' + clientId + ':', error);\n    });\n\n    // Send initial heartbeat request\n    ws.send(JSON.stringify({ type: 'heartbeat_request' }));\n});\n\n// Error handling for the server\nserver.on('error', error => {\n    console.error('❌ Server error:', error);\n});\n\nprocess.on('uncaughtException', error => {\n    console.error('❌ Uncaught Exception:', error);\n});\n\nprocess.on('unhandledRejection', (reason, promise) => {\n    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);\n});\n",I='{\n    "name": "fbaio-apis",\n    "version": "0.1.0",\n    "description": "fbaio-apis",\n    "main": "server.js",\n    "scripts": {\n        "start": "node server.js"\n    },\n    "dependencies": {\n        "express": "^5.1.0",\n        "ws": "^8.18.2",\n        "cors": "^2.8.5"\n    },\n    "license": "MIT"\n}';export{f as default};
