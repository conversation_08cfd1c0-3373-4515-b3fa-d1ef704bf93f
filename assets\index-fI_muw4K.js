import{r as e,t as o,G as t,X as n,a6 as s,m as l}from"./index-Cak6rALw.js";import{g as r}from"./PurePanel-BvHjNOhQ.js";import{S as a}from"./index-C5gzSBcY.js";const{Option:p}=a;function i(e){return(null==e?void 0:e.type)&&(e.type.isSelectOption||e.type.isSelectOptGroup)}const d=(r,d)=>{var u,c;const{prefixCls:m,className:O,popupClassName:f,dropdownClassName:v,children:g,dataSource:C,dropdownStyle:x,dropdownRender:E,popupRender:N,onDropdownVisibleChange:y,onOpenChange:S,styles:w,classNames:h}=r,j=o(g),b=(null===(u=null==w?void 0:w.popup)||void 0===u?void 0:u.root)||x,_=(null===(c=null==h?void 0:h.popup)||void 0===c?void 0:c.root)||f||v,I=N||E,R=S||y;let D;1===j.length&&e.isValidElement(j[0])&&!i(j[0])&&([D]=j);const P=D?()=>D:void 0;let k;k=j.length&&i(j[0])?g:C?C.map((o=>{if(e.isValidElement(o))return o;switch(typeof o){case"string":return e.createElement(p,{key:o,value:o},o);case"object":{const{value:t}=o;return e.createElement(p,{key:t,value:t},o.text)}default:return}})):[];const{getPrefixCls:B}=e.useContext(t),V=B("select",m),[z]=n("SelectLike",null==b?void 0:b.zIndex);return e.createElement(a,Object.assign({ref:d,suffixIcon:null},s(r,["dataSource","dropdownClassName","popupClassName"]),{prefixCls:V,classNames:{popup:{root:_},root:null==h?void 0:h.root},styles:{popup:{root:Object.assign(Object.assign({},b),{zIndex:z})},root:null==w?void 0:w.root},className:l(`${V}-auto-complete`,O),mode:a.SECRET_COMBOBOX_MODE_DO_NOT_USE,popupRender:I,onOpenChange:R,getInputElement:P}),k)},u=e.forwardRef(d),{Option:c}=a,m=r(u,"dropdownAlign",(e=>s(e,["visible"]))),O=u;O.Option=c,O._InternalPanelDoNotUseOrYouWillBeFired=m;export{O as A};
