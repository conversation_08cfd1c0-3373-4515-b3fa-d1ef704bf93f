const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./ProfileCard-Cinxbxq-.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./MyApp-DW5WH4Ub.js","./index-D7dTP8lW.js","./index-IxNfZAD-.js","./Dropdown-BcL-JHCf.js","./index-CDSnY0KE.js","./EyeOutlined-CNKPohhw.js","./addEventListener-C4tIKh7N.js","./getIds-DAzc-wzf.js","./copy-D4Fd8-zB.js"])))=>i.map(i=>d[i]);
import{m as e,L as t,M as i,$ as n,G as o,a6 as a,l as r,a4 as l,d as c,r as s,h as d,a3 as m,J as h,aa as p,O as g,a9 as u,t as f,R as v,aw as x,as as b,aS as y,aQ as $,b0 as S,b5 as w,aN as j,b1 as A,aP as C}from"./index-Cak6rALw.js";import I from"./useCacheState-CAnxkewm.js";import{aI as k,aj as P,T,u as D,d as N,t as E,S as z,c as B,A as O,g as L,b as V,B as q,e as M,f as U,i as R,h as G,j as H,z as F,I as W}from"./MyApp-DW5WH4Ub.js";import{C as X}from"./col-CzA09p0P.js";import{R as Q}from"./row-BMfM-of4.js";import{u as K}from"./useBreakpoint-CPcdMRfj.js";import{P as Y}from"./progress-ApOaEpgr.js";import{C as Z}from"./index-D7dTP8lW.js";import{D as J}from"./index-CC5caqt_.js";import{I as _}from"./index-Bg865k-U.js";import{I as ee}from"./index-CDSnY0KE.js";import{F as te}from"./Table-BO80-bCE.js";import{A as ie}from"./index-DdM4T8-Q.js";import"./index-IxNfZAD-.js";import"./Dropdown-BcL-JHCf.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./DownOutlined-B-JcS-29.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./Pagination-2X6SDgot.js";import"./index-PbLYNhdQ.js";const ne=["wrap","nowrap","wrap-reverse"],oe=["flex-start","flex-end","start","end","center","space-between","space-around","space-evenly","stretch","normal","left","right"],ae=["center","start","end","flex-start","flex-end","self-start","self-end","baseline","normal","stretch"];function re(t,i){return e(Object.assign(Object.assign(Object.assign({},((e,t)=>{const i=!0===t.wrap?"wrap":t.wrap;return{[`${e}-wrap-${i}`]:i&&ne.includes(i)}})(t,i)),((e,t)=>{const i={};return ae.forEach((n=>{i[`${e}-align-${n}`]=t.align===n})),i[`${e}-align-stretch`]=!t.align&&!!t.vertical,i})(t,i)),((e,t)=>{const i={};return oe.forEach((n=>{i[`${e}-justify-${n}`]=t.justify===n})),i})(t,i)))}const le=e=>{const{componentCls:t}=e;return{[t]:{display:"flex",margin:0,padding:0,"&-vertical":{flexDirection:"column"},"&-rtl":{direction:"rtl"},"&:empty":{display:"none"}}}},ce=e=>{const{componentCls:t}=e;return{[t]:{"&-gap-small":{gap:e.flexGapSM},"&-gap-middle":{gap:e.flexGap},"&-gap-large":{gap:e.flexGapLG}}}},se=e=>{const{componentCls:t}=e,i={};return ne.forEach((e=>{i[`${t}-wrap-${e}`]={flexWrap:e}})),i},de=e=>{const{componentCls:t}=e,i={};return ae.forEach((e=>{i[`${t}-align-${e}`]={alignItems:e}})),i},me=e=>{const{componentCls:t}=e,i={};return oe.forEach((e=>{i[`${t}-justify-${e}`]={justifyContent:e}})),i},he=t("Flex",(e=>{const{paddingXS:t,padding:n,paddingLG:o}=e,a=i(e,{flexGapSM:t,flexGap:n,flexGapLG:o});return[le(a),ce(a),se(a),de(a),me(a)]}),(()=>({})),{resetStyle:!1});const pe=n.forwardRef(((t,i)=>{const{prefixCls:r,rootClassName:l,className:c,style:s,flex:d,gap:m,children:h,vertical:p=!1,component:g="div"}=t,u=function(e,t){var i={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(i[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(i[n[o]]=e[n[o]])}return i}(t,["prefixCls","rootClassName","className","style","flex","gap","children","vertical","component"]),{flex:f,direction:v,getPrefixCls:x}=n.useContext(o),b=x("flex",r),[y,$,S]=he(b),w=null!=p?p:null==f?void 0:f.vertical,j=e(c,l,null==f?void 0:f.className,b,$,S,re(b,t),{[`${b}-rtl`]:"rtl"===v,[`${b}-gap-${m}`]:k(m),[`${b}-vertical`]:w}),A=Object.assign(Object.assign({},null==f?void 0:f.style),s);return d&&(A.flex=d),m&&!k(m)&&(A.gap=m),y(n.createElement(g,Object.assign({ref:i,className:j,style:A},a(u,["justify","wrap","align"])),h))}));var ge=["className","prefixCls","style","active","status","iconPrefix","icon","wrapperStyle","stepNumber","disabled","description","title","subTitle","progressDot","stepIcon","tailContent","icons","stepIndex","onStepClick","onClick","render"];function ue(e){return"string"==typeof e}function fe(t){var i,n=t.className,o=t.prefixCls,a=t.style,h=t.active,p=t.status,g=t.iconPrefix,u=t.icon;t.wrapperStyle;var f=t.stepNumber,v=t.disabled,x=t.description,b=t.title,y=t.subTitle,$=t.progressDot,S=t.stepIcon,w=t.tailContent,j=t.icons,A=t.stepIndex,C=t.onStepClick,I=t.onClick,k=t.render,P=r(t,ge),T={};!!C&&!v&&(T.role="button",T.tabIndex=0,T.onClick=function(e){null==I||I(e),C(A)},T.onKeyDown=function(e){var t=e.which;t!==m.ENTER&&t!==m.SPACE||C(A)});var D,N,E,z,B=p||"wait",O=e("".concat(o,"-item"),"".concat(o,"-item-").concat(B),n,(l(i={},"".concat(o,"-item-custom"),u),l(i,"".concat(o,"-item-active"),h),l(i,"".concat(o,"-item-disabled"),!0===v),i)),L=c({},a),V=s.createElement("div",d({},P,{className:O,style:L}),s.createElement("div",d({onClick:I},T,{className:"".concat(o,"-item-container")}),s.createElement("div",{className:"".concat(o,"-item-tail")},w),s.createElement("div",{className:"".concat(o,"-item-icon")},(E=e("".concat(o,"-icon"),"".concat(g,"icon"),(l(D={},"".concat(g,"icon-").concat(u),u&&ue(u)),l(D,"".concat(g,"icon-check"),!u&&"finish"===p&&(j&&!j.finish||!j)),l(D,"".concat(g,"icon-cross"),!u&&"error"===p&&(j&&!j.error||!j)),D)),z=s.createElement("span",{className:"".concat(o,"-icon-dot")}),N=$?"function"==typeof $?s.createElement("span",{className:"".concat(o,"-icon")},$(z,{index:f-1,status:p,title:b,description:x})):s.createElement("span",{className:"".concat(o,"-icon")},z):u&&!ue(u)?s.createElement("span",{className:"".concat(o,"-icon")},u):j&&j.finish&&"finish"===p?s.createElement("span",{className:"".concat(o,"-icon")},j.finish):j&&j.error&&"error"===p?s.createElement("span",{className:"".concat(o,"-icon")},j.error):u||"finish"===p||"error"===p?s.createElement("span",{className:E}):s.createElement("span",{className:"".concat(o,"-icon")},f),S&&(N=S({index:f-1,status:p,title:b,description:x,node:N})),N)),s.createElement("div",{className:"".concat(o,"-item-content")},s.createElement("div",{className:"".concat(o,"-item-title")},b,y&&s.createElement("div",{title:"string"==typeof y?y:void 0,className:"".concat(o,"-item-subtitle")},y)),x&&s.createElement("div",{className:"".concat(o,"-item-description")},x))));return k&&(V=k(V)||null),V}var ve=["prefixCls","style","className","children","direction","type","labelPlacement","iconPrefix","status","size","current","progressDot","stepIcon","initial","icons","onChange","itemRender","items"];function xe(t){var i,o=t.prefixCls,a=void 0===o?"rc-steps":o,s=t.style,m=void 0===s?{}:s,h=t.className;t.children;var p=t.direction,g=void 0===p?"horizontal":p,u=t.type,f=void 0===u?"default":u,v=t.labelPlacement,x=void 0===v?"horizontal":v,b=t.iconPrefix,y=void 0===b?"rc":b,$=t.status,S=void 0===$?"process":$,w=t.size,j=t.current,A=void 0===j?0:j,C=t.progressDot,I=void 0!==C&&C,k=t.stepIcon,P=t.initial,T=void 0===P?0:P,D=t.icons,N=t.onChange,E=t.itemRender,z=t.items,B=void 0===z?[]:z,O=r(t,ve),L="navigation"===f,V="inline"===f,q=V||I,M=V?"horizontal":g,U=V?void 0:w,R=q?"vertical":x,G=e(a,"".concat(a,"-").concat(M),h,(l(i={},"".concat(a,"-").concat(U),U),l(i,"".concat(a,"-label-").concat(R),"horizontal"===M),l(i,"".concat(a,"-dot"),!!q),l(i,"".concat(a,"-navigation"),L),l(i,"".concat(a,"-inline"),V),i)),H=function(e){N&&A!==e&&N(e)};return n.createElement("div",d({className:G,style:m},O),B.filter((function(e){return e})).map((function(e,t){var i=c({},e),o=T+t;return"error"===S&&t===A-1&&(i.className="".concat(a,"-next-error")),i.status||(i.status=o===A?S:o<A?"finish":"wait"),V&&(i.icon=void 0,i.subTitle=void 0),!i.render&&E&&(i.render=function(e){return E(i,e)}),n.createElement(fe,d({},i,{active:o===A,stepNumber:o+1,stepIndex:o,key:o,prefixCls:a,iconPrefix:y,wrapperStyle:m,progressDot:q,stepIcon:k,icons:D,onStepClick:N&&H}))})))}xe.Step=fe;const be=e=>{const{componentCls:t,customIconTop:i,customIconSize:n,customIconFontSize:o}=e;return{[`${t}-item-custom`]:{[`> ${t}-item-container > ${t}-item-icon`]:{height:"auto",background:"none",border:0,[`> ${t}-icon`]:{top:i,width:n,height:n,fontSize:o,lineHeight:h(n)}}},[`&:not(${t}-vertical)`]:{[`${t}-item-custom`]:{[`${t}-item-icon`]:{width:"auto",background:"none"}}}}},ye=e=>{const{componentCls:t}=e,i=`${t}-item`;return{[`${t}-horizontal`]:{[`${i}-tail`]:{transform:"translateY(-50%)"}}}},$e=e=>{const{componentCls:t,inlineDotSize:i,inlineTitleColor:n,inlineTailColor:o}=e,a=e.calc(e.paddingXS).add(e.lineWidth).equal(),r={[`${t}-item-container ${t}-item-content ${t}-item-title`]:{color:n}};return{[`&${t}-inline`]:{width:"auto",display:"inline-flex",[`${t}-item`]:{flex:"none","&-container":{padding:`${h(a)} ${h(e.paddingXXS)} 0`,margin:`0 ${h(e.calc(e.marginXXS).div(2).equal())}`,borderRadius:e.borderRadiusSM,cursor:"pointer",transition:`background-color ${e.motionDurationMid}`,"&:hover":{background:e.controlItemBgHover},"&[role='button']:hover":{opacity:1}},"&-icon":{width:i,height:i,marginInlineStart:`calc(50% - ${h(e.calc(i).div(2).equal())})`,[`> ${t}-icon`]:{top:0},[`${t}-icon-dot`]:{borderRadius:e.calc(e.fontSizeSM).div(4).equal(),"&::after":{display:"none"}}},"&-content":{width:"auto",marginTop:e.calc(e.marginXS).sub(e.lineWidth).equal()},"&-title":{color:n,fontSize:e.fontSizeSM,lineHeight:e.lineHeightSM,fontWeight:"normal",marginBottom:e.calc(e.marginXXS).div(2).equal()},"&-description":{display:"none"},"&-tail":{marginInlineStart:0,top:e.calc(i).div(2).add(a).equal(),transform:"translateY(-50%)","&:after":{width:"100%",height:e.lineWidth,borderRadius:0,marginInlineStart:0,background:o}},[`&:first-child ${t}-item-tail`]:{width:"50%",marginInlineStart:"50%"},[`&:last-child ${t}-item-tail`]:{display:"block",width:"50%"},"&-wait":Object.assign({[`${t}-item-icon ${t}-icon ${t}-icon-dot`]:{backgroundColor:e.colorBorderBg,border:`${h(e.lineWidth)} ${e.lineType} ${o}`}},r),"&-finish":Object.assign({[`${t}-item-tail::after`]:{backgroundColor:o},[`${t}-item-icon ${t}-icon ${t}-icon-dot`]:{backgroundColor:o,border:`${h(e.lineWidth)} ${e.lineType} ${o}`}},r),"&-error":r,"&-active, &-process":Object.assign({[`${t}-item-icon`]:{width:i,height:i,marginInlineStart:`calc(50% - ${h(e.calc(i).div(2).equal())})`,top:0}},r),[`&:not(${t}-item-active) > ${t}-item-container[role='button']:hover`]:{[`${t}-item-title`]:{color:n}}}}}},Se=e=>{const{componentCls:t,iconSize:i,lineHeight:n,iconSizeSM:o}=e;return{[`&${t}-label-vertical`]:{[`${t}-item`]:{overflow:"visible","&-tail":{marginInlineStart:e.calc(i).div(2).add(e.controlHeightLG).equal(),padding:`0 ${h(e.paddingLG)}`},"&-content":{display:"block",width:e.calc(i).div(2).add(e.controlHeightLG).mul(2).equal(),marginTop:e.marginSM,textAlign:"center"},"&-icon":{display:"inline-block",marginInlineStart:e.controlHeightLG},"&-title":{paddingInlineEnd:0,paddingInlineStart:0,"&::after":{display:"none"}},"&-subtitle":{display:"block",marginBottom:e.marginXXS,marginInlineStart:0,lineHeight:n}},[`&${t}-small:not(${t}-dot)`]:{[`${t}-item`]:{"&-icon":{marginInlineStart:e.calc(i).sub(o).div(2).add(e.controlHeightLG).equal()}}}}}},we=e=>{const{componentCls:t,navContentMaxWidth:i,navArrowColor:n,stepsNavActiveColor:o,motionDurationSlow:a}=e;return{[`&${t}-navigation`]:{paddingTop:e.paddingSM,[`&${t}-small`]:{[`${t}-item`]:{"&-container":{marginInlineStart:e.calc(e.marginSM).mul(-1).equal()}}},[`${t}-item`]:{overflow:"visible",textAlign:"center","&-container":{display:"inline-block",height:"100%",marginInlineStart:e.calc(e.margin).mul(-1).equal(),paddingBottom:e.paddingSM,textAlign:"start",transition:`opacity ${a}`,[`${t}-item-content`]:{maxWidth:i},[`${t}-item-title`]:Object.assign(Object.assign({maxWidth:"100%",paddingInlineEnd:0},p),{"&::after":{display:"none"}})},[`&:not(${t}-item-active)`]:{[`${t}-item-container[role='button']`]:{cursor:"pointer","&:hover":{opacity:.85}}},"&:last-child":{flex:1,"&::after":{display:"none"}},"&::after":{position:"absolute",top:`calc(50% - ${h(e.calc(e.paddingSM).div(2).equal())})`,insetInlineStart:"100%",display:"inline-block",width:e.fontSizeIcon,height:e.fontSizeIcon,borderTop:`${h(e.lineWidth)} ${e.lineType} ${n}`,borderBottom:"none",borderInlineStart:"none",borderInlineEnd:`${h(e.lineWidth)} ${e.lineType} ${n}`,transform:"translateY(-50%) translateX(-50%) rotate(45deg)",content:'""'},"&::before":{position:"absolute",bottom:0,insetInlineStart:"50%",display:"inline-block",width:0,height:e.lineWidthBold,backgroundColor:o,transition:`width ${a}, inset-inline-start ${a}`,transitionTimingFunction:"ease-out",content:'""'}},[`${t}-item${t}-item-active::before`]:{insetInlineStart:0,width:"100%"}},[`&${t}-navigation${t}-vertical`]:{[`> ${t}-item`]:{marginInlineEnd:0,"&::before":{display:"none"},[`&${t}-item-active::before`]:{top:0,insetInlineEnd:0,insetInlineStart:"unset",display:"block",width:e.calc(e.lineWidth).mul(3).equal(),height:`calc(100% - ${h(e.marginLG)})`},"&::after":{position:"relative",insetInlineStart:"50%",display:"block",width:e.calc(e.controlHeight).mul(.25).equal(),height:e.calc(e.controlHeight).mul(.25).equal(),marginBottom:e.marginXS,textAlign:"center",transform:"translateY(-50%) translateX(-50%) rotate(135deg)"},"&:last-child":{"&::after":{display:"none"}},[`> ${t}-item-container > ${t}-item-tail`]:{visibility:"hidden"}}},[`&${t}-navigation${t}-horizontal`]:{[`> ${t}-item > ${t}-item-container > ${t}-item-tail`]:{visibility:"hidden"}}}},je=e=>{const{antCls:t,componentCls:i,iconSize:n,iconSizeSM:o,processIconColor:a,marginXXS:r,lineWidthBold:l,lineWidth:c,paddingXXS:s}=e,d=e.calc(n).add(e.calc(l).mul(4).equal()).equal(),m=e.calc(o).add(e.calc(e.lineWidth).mul(4).equal()).equal();return{[`&${i}-with-progress`]:{[`${i}-item`]:{paddingTop:s,[`&-process ${i}-item-container ${i}-item-icon ${i}-icon`]:{color:a}},[`&${i}-vertical > ${i}-item `]:{paddingInlineStart:s,[`> ${i}-item-container > ${i}-item-tail`]:{top:r,insetInlineStart:e.calc(n).div(2).sub(c).add(s).equal()}},[`&, &${i}-small`]:{[`&${i}-horizontal ${i}-item:first-child`]:{paddingBottom:s,paddingInlineStart:s}},[`&${i}-small${i}-vertical > ${i}-item > ${i}-item-container > ${i}-item-tail`]:{insetInlineStart:e.calc(o).div(2).sub(c).add(s).equal()},[`&${i}-label-vertical ${i}-item ${i}-item-tail`]:{top:e.calc(n).div(2).add(s).equal()},[`${i}-item-icon`]:{position:"relative",[`${t}-progress`]:{position:"absolute",insetInlineStart:"50%",top:"50%",transform:"translate(-50%, -50%)","&-inner":{width:`${h(d)} !important`,height:`${h(d)} !important`}}},[`&${i}-small`]:{[`&${i}-label-vertical ${i}-item ${i}-item-tail`]:{top:e.calc(o).div(2).add(s).equal()},[`${i}-item-icon ${t}-progress-inner`]:{width:`${h(m)} !important`,height:`${h(m)} !important`}}}}},Ae=e=>{const{componentCls:t,descriptionMaxWidth:i,lineHeight:n,dotCurrentSize:o,dotSize:a,motionDurationSlow:r}=e;return{[`&${t}-dot, &${t}-dot${t}-small`]:{[`${t}-item`]:{"&-title":{lineHeight:n},"&-tail":{top:e.calc(e.dotSize).sub(e.calc(e.lineWidth).mul(3).equal()).div(2).equal(),width:"100%",marginTop:0,marginBottom:0,marginInline:`${h(e.calc(i).div(2).equal())} 0`,padding:0,"&::after":{width:`calc(100% - ${h(e.calc(e.marginSM).mul(2).equal())})`,height:e.calc(e.lineWidth).mul(3).equal(),marginInlineStart:e.marginSM}},"&-icon":{width:a,height:a,marginInlineStart:e.calc(e.descriptionMaxWidth).sub(a).div(2).equal(),paddingInlineEnd:0,lineHeight:h(a),background:"transparent",border:0,[`${t}-icon-dot`]:{position:"relative",float:"left",width:"100%",height:"100%",borderRadius:100,transition:`all ${r}`,"&::after":{position:"absolute",top:e.calc(e.marginSM).mul(-1).equal(),insetInlineStart:e.calc(a).sub(e.calc(e.controlHeightLG).mul(1.5).equal()).div(2).equal(),width:e.calc(e.controlHeightLG).mul(1.5).equal(),height:e.controlHeight,background:"transparent",content:'""'}}},"&-content":{width:i},[`&-process ${t}-item-icon`]:{position:"relative",top:e.calc(a).sub(o).div(2).equal(),width:o,height:o,lineHeight:h(o),background:"none",marginInlineStart:e.calc(e.descriptionMaxWidth).sub(o).div(2).equal()},[`&-process ${t}-icon`]:{[`&:first-child ${t}-icon-dot`]:{insetInlineStart:0}}}},[`&${t}-vertical${t}-dot`]:{[`${t}-item-icon`]:{marginTop:e.calc(e.controlHeight).sub(a).div(2).equal(),marginInlineStart:0,background:"none"},[`${t}-item-process ${t}-item-icon`]:{marginTop:e.calc(e.controlHeight).sub(o).div(2).equal(),top:0,insetInlineStart:e.calc(a).sub(o).div(2).equal(),marginInlineStart:0},[`${t}-item > ${t}-item-container > ${t}-item-tail`]:{top:e.calc(e.controlHeight).sub(a).div(2).equal(),insetInlineStart:0,margin:0,padding:`${h(e.calc(a).add(e.paddingXS).equal())} 0 ${h(e.paddingXS)}`,"&::after":{marginInlineStart:e.calc(a).sub(e.lineWidth).div(2).equal()}},[`&${t}-small`]:{[`${t}-item-icon`]:{marginTop:e.calc(e.controlHeightSM).sub(a).div(2).equal()},[`${t}-item-process ${t}-item-icon`]:{marginTop:e.calc(e.controlHeightSM).sub(o).div(2).equal()},[`${t}-item > ${t}-item-container > ${t}-item-tail`]:{top:e.calc(e.controlHeightSM).sub(a).div(2).equal()}},[`${t}-item:first-child ${t}-icon-dot`]:{insetInlineStart:0},[`${t}-item-content`]:{width:"inherit"}}}},Ce=e=>{const{componentCls:t}=e;return{[`&${t}-rtl`]:{direction:"rtl",[`${t}-item`]:{"&-subtitle":{float:"left"}},[`&${t}-navigation`]:{[`${t}-item::after`]:{transform:"rotate(-45deg)"}},[`&${t}-vertical`]:{[`> ${t}-item`]:{"&::after":{transform:"rotate(225deg)"},[`${t}-item-icon`]:{float:"right"}}},[`&${t}-dot`]:{[`${t}-item-icon ${t}-icon-dot, &${t}-small ${t}-item-icon ${t}-icon-dot`]:{float:"right"}}}}},Ie=e=>{const{componentCls:t,iconSizeSM:i,fontSizeSM:n,fontSize:o,colorTextDescription:a}=e;return{[`&${t}-small`]:{[`&${t}-horizontal:not(${t}-label-vertical) ${t}-item`]:{paddingInlineStart:e.paddingSM,"&:first-child":{paddingInlineStart:0}},[`${t}-item-icon`]:{width:i,height:i,marginTop:0,marginBottom:0,marginInline:`0 ${h(e.marginXS)}`,fontSize:n,lineHeight:h(i),textAlign:"center",borderRadius:i},[`${t}-item-title`]:{paddingInlineEnd:e.paddingSM,fontSize:o,lineHeight:h(i),"&::after":{top:e.calc(i).div(2).equal()}},[`${t}-item-description`]:{color:a,fontSize:o},[`${t}-item-tail`]:{top:e.calc(i).div(2).sub(e.paddingXXS).equal()},[`${t}-item-custom ${t}-item-icon`]:{width:"inherit",height:"inherit",lineHeight:"inherit",background:"none",border:0,borderRadius:0,[`> ${t}-icon`]:{fontSize:i,lineHeight:h(i),transform:"none"}}}}},ke=e=>{const{componentCls:t,iconSizeSM:i,iconSize:n}=e;return{[`&${t}-vertical`]:{display:"flex",flexDirection:"column",[`> ${t}-item`]:{display:"block",flex:"1 0 auto",paddingInlineStart:0,overflow:"visible",[`${t}-item-icon`]:{float:"left",marginInlineEnd:e.margin},[`${t}-item-content`]:{display:"block",minHeight:e.calc(e.controlHeight).mul(1.5).equal(),overflow:"hidden"},[`${t}-item-title`]:{lineHeight:h(n)},[`${t}-item-description`]:{paddingBottom:e.paddingSM}},[`> ${t}-item > ${t}-item-container > ${t}-item-tail`]:{position:"absolute",top:0,insetInlineStart:e.calc(n).div(2).sub(e.lineWidth).equal(),width:e.lineWidth,height:"100%",padding:`${h(e.calc(e.marginXXS).mul(1.5).add(n).equal())} 0 ${h(e.calc(e.marginXXS).mul(1.5).equal())}`,"&::after":{width:e.lineWidth,height:"100%"}},[`> ${t}-item:not(:last-child) > ${t}-item-container > ${t}-item-tail`]:{display:"block"},[` > ${t}-item > ${t}-item-container > ${t}-item-content > ${t}-item-title`]:{"&::after":{display:"none"}},[`&${t}-small ${t}-item-container`]:{[`${t}-item-tail`]:{position:"absolute",top:0,insetInlineStart:e.calc(i).div(2).sub(e.lineWidth).equal(),padding:`${h(e.calc(e.marginXXS).mul(1.5).add(i).equal())} 0 ${h(e.calc(e.marginXXS).mul(1.5).equal())}`},[`${t}-item-title`]:{lineHeight:h(i)}}}}},Pe=(e,t)=>{const i=`${t.componentCls}-item`,n=`${e}IconColor`,o=`${e}TitleColor`,a=`${e}DescriptionColor`,r=`${e}TailColor`,l=`${e}IconBgColor`,c=`${e}IconBorderColor`,s=`${e}DotColor`;return{[`${i}-${e} ${i}-icon`]:{backgroundColor:t[l],borderColor:t[c],[`> ${t.componentCls}-icon`]:{color:t[n],[`${t.componentCls}-icon-dot`]:{background:t[s]}}},[`${i}-${e}${i}-custom ${i}-icon`]:{[`> ${t.componentCls}-icon`]:{color:t[s]}},[`${i}-${e} > ${i}-container > ${i}-content > ${i}-title`]:{color:t[o],"&::after":{backgroundColor:t[r]}},[`${i}-${e} > ${i}-container > ${i}-content > ${i}-description`]:{color:t[a]},[`${i}-${e} > ${i}-container > ${i}-tail::after`]:{backgroundColor:t[r]}}},Te=e=>{const{componentCls:t,motionDurationSlow:i}=e,n=`${t}-item`,o=`${n}-icon`;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({[n]:{position:"relative",display:"inline-block",flex:1,overflow:"hidden",verticalAlign:"top","&:last-child":{flex:"none",[`> ${n}-container > ${n}-tail, > ${n}-container >  ${n}-content > ${n}-title::after`]:{display:"none"}}},[`${n}-container`]:{outline:"none","&:focus-visible":{[o]:Object.assign({},u(e))}},[`${o}, ${n}-content`]:{display:"inline-block",verticalAlign:"top"},[o]:{width:e.iconSize,height:e.iconSize,marginTop:0,marginBottom:0,marginInlineStart:0,marginInlineEnd:e.marginXS,fontSize:e.iconFontSize,fontFamily:e.fontFamily,lineHeight:h(e.iconSize),textAlign:"center",borderRadius:e.iconSize,border:`${h(e.lineWidth)} ${e.lineType} transparent`,transition:`background-color ${i}, border-color ${i}`,[`${t}-icon`]:{position:"relative",top:e.iconTop,color:e.colorPrimary,lineHeight:1}},[`${n}-tail`]:{position:"absolute",top:e.calc(e.iconSize).div(2).equal(),insetInlineStart:0,width:"100%","&::after":{display:"inline-block",width:"100%",height:e.lineWidth,background:e.colorSplit,borderRadius:e.lineWidth,transition:`background ${i}`,content:'""'}},[`${n}-title`]:{position:"relative",display:"inline-block",paddingInlineEnd:e.padding,color:e.colorText,fontSize:e.fontSizeLG,lineHeight:h(e.titleLineHeight),"&::after":{position:"absolute",top:e.calc(e.titleLineHeight).div(2).equal(),insetInlineStart:"100%",display:"block",width:9999,height:e.lineWidth,background:e.processTailColor,content:'""'}},[`${n}-subtitle`]:{display:"inline",marginInlineStart:e.marginXS,color:e.colorTextDescription,fontWeight:"normal",fontSize:e.fontSize},[`${n}-description`]:{color:e.colorTextDescription,fontSize:e.fontSize}},Pe("wait",e)),Pe("process",e)),{[`${n}-process > ${n}-container > ${n}-title`]:{fontWeight:e.fontWeightStrong}}),Pe("finish",e)),Pe("error",e)),{[`${n}${t}-next-error > ${t}-item-title::after`]:{background:e.colorError},[`${n}-disabled`]:{cursor:"not-allowed"}})},De=e=>{const{componentCls:t,motionDurationSlow:i}=e;return{[`& ${t}-item`]:{[`&:not(${t}-item-active)`]:{[`& > ${t}-item-container[role='button']`]:{cursor:"pointer",[`${t}-item`]:{[`&-title, &-subtitle, &-description, &-icon ${t}-icon`]:{transition:`color ${i}`}},"&:hover":{[`${t}-item`]:{"&-title, &-subtitle, &-description":{color:e.colorPrimary}}}},[`&:not(${t}-item-process)`]:{[`& > ${t}-item-container[role='button']:hover`]:{[`${t}-item`]:{"&-icon":{borderColor:e.colorPrimary,[`${t}-icon`]:{color:e.colorPrimary}}}}}}},[`&${t}-horizontal:not(${t}-label-vertical)`]:{[`${t}-item`]:{paddingInlineStart:e.padding,whiteSpace:"nowrap","&:first-child":{paddingInlineStart:0},[`&:last-child ${t}-item-title`]:{paddingInlineEnd:0},"&-tail":{display:"none"},"&-description":{maxWidth:e.descriptionMaxWidth,whiteSpace:"normal"}}}}},Ne=e=>{const{componentCls:t}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},g(e)),{display:"flex",width:"100%",fontSize:0,textAlign:"initial"}),Te(e)),De(e)),be(e)),Ie(e)),ke(e)),ye(e)),Se(e)),Ae(e)),we(e)),Ce(e)),je(e)),$e(e))}},Ee=t("Steps",(e=>{const{colorTextDisabled:t,controlHeightLG:n,colorTextLightSolid:o,colorText:a,colorPrimary:r,colorTextDescription:l,colorTextQuaternary:c,colorError:s,colorBorderSecondary:d,colorSplit:m}=e,h=i(e,{processIconColor:o,processTitleColor:a,processDescriptionColor:a,processIconBgColor:r,processIconBorderColor:r,processDotColor:r,processTailColor:m,waitTitleColor:l,waitDescriptionColor:l,waitTailColor:m,waitDotColor:t,finishIconColor:r,finishTitleColor:a,finishDescriptionColor:l,finishTailColor:r,finishDotColor:r,errorIconColor:o,errorTitleColor:s,errorDescriptionColor:s,errorTailColor:m,errorIconBgColor:s,errorIconBorderColor:s,errorDotColor:s,stepsNavActiveColor:r,stepsProgressSize:n,inlineDotSize:6,inlineTitleColor:c,inlineTailColor:d});return[Ne(h)]}),(e=>({titleLineHeight:e.controlHeight,customIconSize:e.controlHeight,customIconTop:0,customIconFontSize:e.controlHeightSM,iconSize:e.controlHeight,iconTop:-.5,iconFontSize:e.fontSize,iconSizeSM:e.fontSizeHeading3,dotSize:e.controlHeight/4,dotCurrentSize:e.controlHeightLG/4,navArrowColor:e.colorTextDisabled,navContentMaxWidth:"unset",descriptionMaxWidth:140,waitIconColor:e.wireframe?e.colorTextDisabled:e.colorTextLabel,waitIconBgColor:e.wireframe?e.colorBgContainer:e.colorFillContent,waitIconBorderColor:e.wireframe?e.colorTextDisabled:"transparent",finishIconBgColor:e.wireframe?e.colorBgContainer:e.controlItemBgActive,finishIconBorderColor:e.wireframe?e.colorPrimary:e.controlItemBgActive})));const ze=t=>{const{percent:i,size:n,className:o,rootClassName:a,direction:r,items:l,responsive:c=!0,current:d=0,children:m,style:h}=t,p=function(e,t){var i={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(i[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(i[n[o]]=e[n[o]])}return i}(t,["percent","size","className","rootClassName","direction","items","responsive","current","children","style"]),{xs:g}=K(c),{getPrefixCls:u,direction:y,className:$,style:S}=v("steps"),w=s.useMemo((()=>c&&g?"vertical":r),[g,r]),j=x(n),A=u("steps",t.prefixCls),[C,I,k]=Ee(A),D="inline"===t.type,N=u("",t.iconPrefix),E=function(e,t){return e||function(e){return e.filter((e=>e))}(f(t).map((e=>{if(s.isValidElement(e)){const{props:t}=e;return Object.assign({},t)}return null})))}(l,m),z=D?void 0:i,B=Object.assign(Object.assign({},S),h),O=e($,{[`${A}-rtl`]:"rtl"===y,[`${A}-with-progress`]:void 0!==z},o,a,I,k),L={finish:s.createElement(P,{className:`${A}-finish-icon`}),error:s.createElement(b,{className:`${A}-error-icon`})};return C(s.createElement(xe,Object.assign({icons:L},p,{style:B,current:d,size:j,items:E,itemRender:D?(e,t)=>e.description?s.createElement(T,{title:e.description},t):t:void 0,stepIcon:({node:e,status:t})=>{if("process"===t&&void 0!==z){const t="small"===j?32:40;return s.createElement("div",{className:`${A}-progress-icon`},s.createElement(Y,{type:"circle",percent:z,size:t,strokeWidth:4,format:()=>null}),e)}return e},direction:w,prefixCls:A,iconPrefix:N,className:O})))};ze.Step=xe.Step;const Be=""+new URL("VPBank-B4jSWz7d.svg",import.meta.url).href,Oe=""+new URL("vcb-B3zKigWy.png",import.meta.url).href,Le=""+new URL("western_union-CFt3-5qi.png",import.meta.url).href,Ve=""+new URL("paypal-BIJoftQy.png",import.meta.url).href,qe=""+new URL("vcb-HXrFPZht.png",import.meta.url).href,Me=""+new URL("momo-BThAuDPl.png",import.meta.url).href,Ue=""+new URL("kofi-BzxZc1MB.png",import.meta.url).href,Re=""+new URL("patreon-BbtBzRnE.png",import.meta.url).href,Ge=A((()=>j((()=>import("./ProfileCard-Cinxbxq-.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9]),import.meta.url)),{fallback:W}),He={acc:"**********",name:"Trần Văn Hoàng",bank:"VPBank",logo:Be};const Fe={SupportChangeAccount:{icon:"fa-solid fa-user-check",en:"Switch account",vi:"Hỗ trợ đổi nick",tooltip:{vi:"Khi nick cũ của bạn bị khoá, bị mất, bị hack,.. liên hệ admin để chuyển VIP sang nick mới",en:"When your old account is locked, lost, hacked,.. contact the admin to transfer VIP to a new account"}},PrioritySupport:{icon:"fa-solid fa-headset",en:"Priority support",vi:"Ưu tiên hỗ trợ",tooltip:{vi:"Ưu tiên hỗ trợ bạn khi có vấn đề",en:"Priority support when you have any issues"}},FeatureRequest:{icon:"fa-solid fa-lightbulb",en:"Feature request",vi:"Yêu cầu tính năng",tooltip:{vi:"Ưu tiên làm tính năng bạn yêu cầu",en:"Priority to make the feature you request"}},MultipleAccounts:(e=1)=>({icon:"fa-solid fa-users",en:`Multiple accounts (${e})`,vi:`Nhiều tài khoản (${e})`,tooltip:{vi:`Mở VIP vĩnh viễn cho cùng lúc ${e} tài khoản. Tính tiền 1 lần, rồi liên hệ admin để nâng VIP các tài khoản còn lại.`,en:`Unlock VIP forever for up to ${e} accounts. Pay once, then contact the admin to unlock VIP for the remaining accounts.`},highlight:"red"}),Save:(e=1,t=0)=>{const i=~~(100-t/e*100);return{icon:"fa-solid fa-percent",en:`Save ${i}%`,vi:`Tiết kiệm ${i}%`}},SpecialSale:(e=34,t="30/4",i="")=>({icon:"fa-solid fa-bolt",en:`Special Sale ${t} - ${e}%`,vi:`Ưu đãi ${t} - ${e}%`,tooltip:{vi:`Giảm giá đăc biệt nhân dịp ${t}`,en:`Special sale on ${t}`}})};function We(e,t){const i=~~(e*(1-t/100));return i>1e3?1e3*~~(i/1e3):i}const Xe=[{label:{en:"2 Year",vi:"2 Năm"},expiredAt:17516484e5,oldPrice:{vi:24e5,en:120},price:{vi:We(659e3,0),en:We(35,0)},icon:"fa-solid fa-star-of-life",ribbon:{en:"Free 1 year VIP",vi:"Tặng 1 năm VIP",color:"red"},descriptions:[Fe.Save(24e5,659e3),Fe.PrioritySupport,Fe.SupportChangeAccount,Fe.FeatureRequest]},{label:{en:"3 Days",vi:"3 Ngày"},price:{vi:We(15e3,0),en:0},icon:"fa-solid fa-eye",tag:{en:"Trial",vi:"Dùng thử",color:"cyan"}},{label:{en:"1 Week",vi:"1 Tuần"},price:{vi:We(3e4,0),en:0},icon:"fa-solid fa-bolt",tag:{en:"Starter",vi:"Khởi động",color:"blue"}},{label:{en:"1 Month",vi:"1 Tháng"},oldPrice:{vi:1e5,en:5},price:{vi:We(65e3,0),en:3},icon:"fa-solid fa-piggy-bank",tag:{en:"Save",vi:"Tiết kiệm",color:"orange"},descriptions:[Fe.Save(1e5,65e3),Fe.PrioritySupport],isDefaultPlan:!0},{label:{en:"6 Months",vi:"6 Tháng"},oldPrice:{vi:6e5,en:30},price:{vi:We(369e3,0),en:We(15,0)},icon:"fa-solid fa-user",tag:{en:"Member",vi:"Thành viên",color:"yellow"},descriptions:[Fe.Save(6e5,369e3),Fe.PrioritySupport,Fe.SupportChangeAccount]},{label:{en:"1 Year",vi:"1 Năm"},oldPrice:{vi:12e5,en:60},price:{vi:We(65e4,0),en:We(30,0)},icon:"fa-solid fa-star",tag:{en:"Popular",vi:"Phổ biến",color:"green"},descriptions:[Fe.Save(12e5,65e4),Fe.PrioritySupport,Fe.SupportChangeAccount,Fe.FeatureRequest]},{label:{en:"Lifetime\n(buy 1 get 3)",vi:"Trọn đời\n(mua 1 được 3)"},oldPrice:{vi:2549e3,en:120},price:{vi:We(149e4,0),en:We(69,0)},icon:"fa-solid fa-crown",ribbon:{en:"Buy 1 get 3",vi:"Mua 1 được 3",color:"red"},tag:{en:"Forever",vi:"Vĩnh Viễn",color:"red"},descriptions:[Fe.Save(2549e3,149e4),Fe.PrioritySupport,Fe.SupportChangeAccount,Fe.FeatureRequest,Fe.MultipleAccounts(3)]}],Qe=({selectedPlan:e,setSelectedPlan:t})=>{const{ti:i}=D();return S.jsxs(z,{direction:"vertical",align:"center",style:{width:"100%"},children:[S.jsxs(B.Title,{level:4,style:{padding:10,borderRadius:10,textAlign:"center"},className:"highlight-text",children:[i({en:"Selected Plan",vi:"Gói đã chọn"}),e?": "+i(e.label)+" - "+L(e.price.vi)+" VND"+(e.price.en?" - "+L(e.price.en)+" USD":""):""]}),S.jsx(O,{type:"warning",message:i({en:S.jsxs(S.Fragment,{children:["🚨 Lifetime plan will be"," ",S.jsx(T,{title:"Those who already purchased will still retain lifetime access.",children:S.jsx(V,{color:"red",children:"stopped"})}),"soon, please buy before 1st August 2025, to get best benefits.",S.jsxs("a",{href:"https://www.facebook.com/groups/fbaio/posts/****************",target:"_blank",children:["More info ",S.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})]})]}),vi:S.jsxs(S.Fragment,{children:["🚨 Gói Trọn đời sắp"," ",S.jsx(T,{title:"Những ai đã mua sẽ vẫn được dùng VIP trọn đời.",children:S.jsx(V,{color:"red",children:"ngừng bán"})}),", vui lòng mua trước 1/8/2025, để tối ưu hoá lợi ích gói VIP."," ",S.jsxs("a",{href:"https://www.facebook.com/groups/fbaio/posts/****************",target:"_blank",children:["Xem thêm ",S.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})]})]})})}),S.jsxs(Q,{gutter:[16,16],justify:"center",children:[S.jsx(X,{children:S.jsx(Z,{style:{width:230},variant:"borderless",children:S.jsxs(z,{direction:"vertical",align:"center",style:{width:"100%"},children:[S.jsx("i",{className:"fa-solid fa-gift fa-2x"}),S.jsx(B.Title,{level:4,children:i({en:"For Free",vi:"Miễn phí"})}),S.jsx(V,{color:"green",style:{fontSize:"1em"},children:i({en:"0 cost",vi:"0 đồng"})}),S.jsx(B.Text,{strong:!0,style:{color:"orange",fontSize:"1.3em",padding:5},children:"0 VND"}),S.jsx(B.Text,{type:"secondary",children:i({en:"50+ free features",vi:"50+ tính năng miễn phí"})}),S.jsx(w,{type:"link",href:"#/vip",icon:S.jsx("i",{className:"fa-solid fa-arrow-right"}),iconPosition:"end",children:i({en:"Explore",vi:"Khám phá"})})]})})}),Xe.map(((n,o)=>{var a,r,l,c,s;const d=(null==e?void 0:e.label)===n.label;return n.expiredAt&&n.expiredAt<Date.now()?null:S.jsx(X,{children:S.jsx(q.Ribbon,{color:null==(a=null==n?void 0:n.ribbon)?void 0:a.color,text:i(null==n?void 0:n.ribbon),style:{display:(null==n?void 0:n.ribbon)?"block":"none"},children:S.jsx(Z,{hoverable:!0,style:{width:230,border:d?"2px solid #1890ff":void 0},onClick:()=>t(n),children:S.jsxs(z,{direction:"vertical",align:"center",style:{width:"100%"},children:[S.jsx("i",{className:n.icon+" fa-2x"}),S.jsxs(B.Title,{level:4,style:{textAlign:"center"},children:[i(n.label).split("\n").map(((e,t)=>S.jsxs("span",{children:[e,S.jsx("br",{})]},t))),n.expiredAt&&S.jsx(T,{title:i({en:"Promotion will end at: ",vi:"Khuyến mãi sẽ kết thúc lúc: "})+U(n.expiredAt),children:S.jsx(q,{color:"red",count:i({en:M(n.expiredAt)+" left",vi:"Còn "+M(n.expiredAt)})})})]}),n.tag&&S.jsx(V,{color:n.tag.color,style:{fontSize:"1em"},children:i(n.tag)}),S.jsxs(z,{direction:"vertical",align:"center",size:2,children:[(null==(r=n.oldPrice)?void 0:r.vi)>0&&S.jsx(B.Text,{type:"secondary",style:{textDecoration:"line-through"},children:`(${L(n.oldPrice.vi)} VND)`}),S.jsxs(B.Text,{strong:!0,style:{color:"orange",fontSize:"1.3em",padding:5},children:[L(n.price.vi)," VND"]}),S.jsx(B.Text,{type:"secondary",children:(null==(l=n.price)?void 0:l.en)>0?`(${L(n.price.en)} USD)`:i({en:"(not available in USD)",vi:"(không hỗ trợ USD)"})})]}),(null==(c=n.descriptions)?void 0:c.length)>0&&S.jsxs(S.Fragment,{children:[S.jsx(J,{}),null==(s=n.descriptions)?void 0:s.map(((e,t)=>S.jsx(T,{title:i(e.tooltip),children:S.jsxs(z,{children:[S.jsx("i",{className:e.icon}),e.highlight?S.jsx(V,{color:e.highlight,children:i(e)}):S.jsx(B.Text,{children:i(e)})]})},"desc-"+t)))]})]})})})},o)}))]})]})},Ke=({profile:e,myProfile:t,setProfile:i})=>{const{ti:n}=D(),{message:o}=N(),[a,r]=s.useState(!1),[l,c]=I("Checkout.url",""),d=s.useRef(!1),m=l&&/\d+/.test(l);s.useEffect((()=>{l&&!d.current&&h(l)}),[l]);const h=async e=>{d.current=!0,r(!0);const a="Checkout.loadProfile";o.loading({key:a,content:n({en:"Loading profile...",vi:"Đang tải profile..."}),duration:0});try{if(t){const t=(await j((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url)).getUidFromUrl,r=await t(e);if(!r)throw new Error(n({en:"Cannot get UID from URL",vi:"Không tìm thấy UID từ URL"}));const l=await H(r);if(!l)throw new Error(n({en:"Cannot get profile from UID",vi:"Không tìm thấy profile từ UID"}));if(l.type!==F.User&&l.type!==F.Page)throw new Error(n({en:"Only support Facebook user or page. Current: ",vi:"Chỉ hỗ trợ Facebook User hoặc Page. Tìm thấy: "})+l.type);i(l),E("Checkout.loadProfile:"+l.uid),o.success({key:a,content:n({en:"Load profile successfully",vi:"Tải profile thành công"})+": "+l.name})}else{if(!m)throw new Error(n({en:"Invalid UID: Must be number. Please paste whole UID, dont manual typing",vi:"UID không hợp lệ: Phải là số nguyên. Vui lòng Dán toàn bộ UID, không nhập tay từng chữ"}));i({uid:e,url:G(e),name:n({en:"Click here to verify",vi:"Bấm để kiểm tra nick"}),avatar:R(e,100)}),o.destroy(a)}}catch(l){o.error({key:a,content:l.message})}finally{d.current=!1,c(""),r(!1)}};return S.jsxs(z,{direction:"vertical",align:"center",style:{width:"100%"},children:[S.jsx(B.Title,{level:5,children:n(t?{en:"Enter URL/UID of Facebook user want to buy VIP",vi:"Nhập URL/UID nick Facebook muốn mua VIP"}:{en:"Enter Facebook UID of user want to buy VIP",vi:"Nhập UID nick Facebook muốn mua VIP"})}),S.jsx(_,{style:{maxWidth:400},value:l,disabled:a,onChange:e=>c(e.target.value),placeholder:n(t?{en:"Enter URL or UID",vi:"Nhập URL hoặc UID"}:{en:"Enter Facebook UID",vi:"Nhập UID Facebook"}),prefix:S.jsx("i",{className:"fa-solid fa-link"}),size:"large",suffix:t?S.jsx(w,{disabled:(null==t?void 0:t.uid)===(null==e?void 0:e.uid),onClick:()=>{c(""),i(t)},icon:S.jsx("i",{className:"fa-solid fa-user"}),children:n({en:"Me",vi:"Tôi"})}):null}),S.jsx(O,{type:"info",showIcon:!0,message:n({en:"If you want to buy VIP for the Page you manage, please enter the Page URL instead of User UID",vi:"Nếu muốn nâng VIP cho Page bạn quản lý, vui lòng nhập link page thay vì UID của bạn"})}),e&&S.jsxs(S.Fragment,{children:[S.jsx("br",{}),S.jsx(B.Title,{level:5,children:n({en:"Selected account",vi:"Nick đã chọn"})}),S.jsx(Ge,{about:e,showUid:!0})]})]})},Ye=({profile:e,selectedPlan:t})=>{var i,n,o,a,r;const{ti:l}=D(),[c,d]=s.useState(0),m=null==(i=null==t?void 0:t.price)?void 0:i.vi,h=(null==e?void 0:e.uid)?"VIP"+(null==e?void 0:e.uid):"",p=`https://qr.sepay.vn/img?bank=${He.bank}&acc=${He.acc}&amount=${m}&des=${h}&template=compact`;s.useEffect((()=>{const e=setInterval((()=>{1!=c&&h&&async function(e=""){const{fetchExtension:t}=await j((async()=>{const{fetchExtension:e}=await import("./index-Cak6rALw.js").then((e=>e.cS));return{fetchExtension:e}}),__vite__mapDeps([1,2]),import.meta.url),i=C.FB_AIO.server+"/check_payment_status?content="+e;for(let o of[t,fetch])try{let e=await o(i);return(null==e?void 0:e.text)&&(e=await e.text()),"true"==e?1:0}catch(n){console.log("ERROR",n)}return 0}(h).then((e=>{1===e?(async()=>{d(1),E("Checkout:paymentSuccess:"+h),(await j((async()=>{const{default:e}=await import("./sweetalert2.esm.all-BZxvatOx.js");return{default:e}}),[],import.meta.url)).default.fire({icon:"success",title:l({en:"Payment successfully",vi:"Thanh toán thành công"}),text:l({en:"Please reload page to update VIP status",vi:"Vui lòng tải lại trang web để cập nhật trạng thái VIP"}),showCancelButton:!1,allowEscapeKey:!1,allowOutsideClick:!1}).then((()=>{window.location.reload()}))})():d(0)}))}),5e3);return()=>clearInterval(e)}),[h,c]);const g=S.jsxs(S.Fragment,{children:[S.jsx(B.Title,{level:3,children:l({en:"Auto (only VND)",vi:"Tự động (chỉ hỗ trợ VND)"})}),S.jsx(O,{banner:!0,showIcon:!0,type:"info",message:l({en:"VIP will Auto Unlock after Payment Success",vi:"VIP sẽ Tự động Mở khoá sau khi Thanh toán thành công"})}),S.jsxs(pe,{wrap:!0,justify:"center",gap:"small",style:{width:"100%"},children:[S.jsx(Z,{children:S.jsxs(z,{direction:"vertical",align:"center",children:[S.jsx(B.Title,{level:5,children:l({en:"Option 1: Open bank app to scan QR code",vi:"Cách 1: Mở app ngân hàng và quét mã QR"})}),S.jsx(ee,{style:{maxWidth:350},src:p}),S.jsx(w,{href:p+"&download=true",target:"_blank",icon:S.jsx("i",{className:"fa-solid fa-download"}),children:l({en:"Download QR",vi:"Tải ảnh QR"})}),1===c?S.jsxs(B.Title,{type:"success",level:5,children:[l({en:"Status: Payment Success",vi:"Trạng thái: Thanh toán thành công"}),S.jsx("i",{className:"fa-solid fa-circle-check fa-lg"})]}):S.jsxs(B.Title,{type:"warning",level:5,children:[l({en:"Status: Waiting for payment...",vi:"Trạng thái: Chờ thanh toán..."})," ",S.jsx("i",{className:"fa-solid fa-circle-notch fa-spin fa-lg"})]})]})}),S.jsx(Z,{children:S.jsxs(z,{direction:"vertical",align:"center",children:[S.jsx(B.Title,{level:5,children:l({en:"Option 2: Transfer to bank account",vi:"Cách 2: Chuyển khoản theo thông tin"})}),S.jsx(ee,{style:{maxWidth:130},src:He.logo,preview:!1}),S.jsx(B.Title,{level:4,children:l({en:"Bank name: "+He.bank,vi:"Ngân hàng: "+He.bank})}),S.jsx(te,{pagination:!1,showHeader:!1,size:"middle",rowKey:e=>e.key,dataSource:[{key:1,name:l({en:"Account",vi:"Số TK"}),value:He.acc},{key:2,name:l({en:"Name",vi:"Chủ tài khoản"}),value:He.name},{key:3,name:l({en:"Amount",vi:"Số tiền"}),value:L(m)+" VND"},{key:4,name:l({en:"Description",vi:"Nội dung CK"}),value:h}],columns:[{title:l({en:"Name",vi:"Tên"}),dataIndex:"name",key:"name",render:e=>e+":"},{title:l({en:"Value",vi:"Giá trị"}),dataIndex:"value",key:"value",render:e=>S.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[S.jsx("b",{style:{marginRight:8},children:e}),S.jsx(w,{icon:S.jsx("i",{className:"fa-solid fa-copy"}),onClick:async()=>{const{copyWithMessage:t}=await j((async()=>{const{copyWithMessage:e}=await import("./copy-D4Fd8-zB.js");return{copyWithMessage:e}}),__vite__mapDeps([11,1,2]),import.meta.url);t(e)}})]})}]}),S.jsx(O,{showIcon:!0,style:{maxWidth:"100%"},banner:!0,message:l({vi:S.jsxs(S.Fragment,{children:["Lưu ý: Vui lòng giữ nguyên nội dung chuyển khoản",S.jsx("br",{}),S.jsx("b",{children:h}),S.jsx("br",{})," để hệ thống tự động xác nhận thanh toán"]}),en:S.jsxs(S.Fragment,{children:["Note: Please keep the transfer description ",S.jsx("br",{}),S.jsx("b",{children:h})," ",S.jsx("br",{}),"system will automatically confirm the payment"]})}),type:"warning"})]})})]})]}),u=S.jsxs(z,{direction:"vertical",align:"center",children:[S.jsx(B.Title,{level:3,children:l({en:"Manual (VND + USD)",vi:"Thủ công (VND + USD)"})}),S.jsx(O,{banner:!0,showIcon:!0,type:"info",message:l({en:S.jsxs(S.Fragment,{children:["The following methods do not yet support AUTOMATIC VIP activation ",S.jsx("br",{}),"After making the transfer, please send bill screenshot to"," ",S.jsx("a",{href:C.me.url,target:"_blank",children:"Admin"})," ","to activate VIP"]}),vi:S.jsxs(S.Fragment,{children:["Các phương thức dưới đây chưa hỗ trợ TỰ ĐỘNG mở VIP",S.jsx("br",{}),"Vui lòng gửi hình chụp hoá đơn tới"," ",S.jsx("a",{href:C.me.url,target:"_blank",children:"Admin"})," ","để được mở VIP"]})})}),S.jsx(te,{pagination:!1,dataSource:[{name:"Paypal",logo:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'%20standalone='no'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20width='800px'%20height='800px'%20viewBox='-3.5%200%2048%2048'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3ePaypal-color%3c/title%3e%3cdesc%3eCreated%20with%20Sketch.%3c/desc%3e%3cdefs%3e%3c/defs%3e%3cg%20id='Icons'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='Color-'%20transform='translate(-804.000000,%20-660.000000)'%20fill='%23022B87'%3e%3cpath%20d='M838.91167,663.619443%20C836.67088,661.085983%20832.621734,660%20827.440097,660%20L812.404732,660%20C811.344818,660%20810.443663,660.764988%20810.277343,661.801472%20L804.016136,701.193856%20C803.892151,701.970844%20804.498465,702.674333%20805.292267,702.674333%20L814.574458,702.674333%20L816.905967,688.004562%20L816.833391,688.463555%20C816.999712,687.427071%20817.894818,686.662083%20818.95322,686.662083%20L823.363735,686.662083%20C832.030541,686.662083%20838.814901,683.170138%20840.797138,673.069296%20C840.856106,672.7693%20840.951363,672.194809%20840.951363,672.194809%20C841.513828,668.456868%20840.946827,665.920407%20838.91167,663.619443%20Z%20M843.301017,674.10803%20C841.144899,684.052874%20834.27133,689.316292%20823.363735,689.316292%20L819.408334,689.316292%20L816.458414,708%20L822.873846,708%20C823.800704,708%20824.588458,707.33101%20824.733611,706.423525%20L824.809211,706.027531%20L826.284927,696.754676%20L826.380183,696.243184%20C826.523823,695.335698%20827.313089,694.666708%20828.238435,694.666708%20L829.410238,694.666708%20C836.989913,694.666708%20842.92604,691.611256%20844.660308,682.776394%20C845.35583,679.23045%20845.021677,676.257496%20843.301017,674.10803%20Z'%20id='Paypal'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/svg%3e",url:"https://paypal.me/hoangtran99",infor:["<EMAIL>","@hoangtran99","(+84) **********"],qrcode:Ve},{name:"Western Union",logo:Le,url:"https://www.westernunion.com",infor:["Mail: <EMAIL>","Name: VAN HOANG TRAN","Phone: (+84) **********","Address: Ho Chi Minh City, Vietnam"],note:"Please contact Admin to confirm before payment"},{name:"Vietcombank",logo:Oe,infor:["**********","TRAN VAN HOANG"],qrcode:qe},{name:"MoMo",logo:"data:image/png;base64,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",infor:["**********","Trần Văn Hoàng"],qrcode:Me},{name:"Kofi",logo:"data:image/svg+xml,%3csvg%20fill='none'%20height='391'%20viewBox='0%200%20391%20391'%20width='391'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m52%20102h296v199h-296z'%20fill='%23fff'/%3e%3cpath%20d='m174%201.09995c-79.2999%208.9-145.5999%2065.29995-166.59995%20141.70005-15.2%2055.6-5.8%20114%2026.10005%20161.7%2061.7%2092.3%20189.4999%20114.2%20278.7999%2047.8%2074.8-55.8%2099.8-156.6%2059.7-241-9.7-20.3001-20.4-35.6001-36.4-52.2001-27.2-27.9-61.3-46.7-99.6-54.99995-18.3-3.900005-43.3-5.1-62-3zm117.8%20102.90005c25.2%207%2043.6%2024.6%2050.2%2048%207.8%2028.1-.1%2055.6-21.5%2073.9-11.5%209.8-32.5%2017.1-49.6%2017.1h-7.6l-.8%207.2c-1.9%2019.4-11.4%2032.5-27%2037-2.9.9-25.2%201.2-80%201.2l-75.9999.1-6-2.8c-9.6-4.5-16.9-12.7-19.7-22.2-.4-1.6-.8-36.6-.8-77.8%200-80.7%200-80.6%205.2-82.7%201.4-.5%2048.5999-.9%20114.2999-.9%20109.5-.1%20112.2-.1%20119.3%201.9z'%20fill='%23579fbf'/%3e%3cpath%20d='m264%20173v34h8.8c15.5%200%2025-5.3%2031.2-17.4%203.3-6.6%203.5-7.4%203.5-17.5%200-9.6-.3-11.1-2.8-16.3-3-6.2-8.6-11.6-15.2-14.8-3.3-1.6-6.2-2-14.8-2h-10.7z'%20fill='%23579fbf'/%3e%3cpath%20d='m177.885%20147.41c-7.3%202.4-9.6%203.7-15.3%208.4l-4.7%203.9-4.3-3.5c-14.2-11.6-35-12.9-46.7-2.9-5.8%205-8.1996%2010.3-8.7996%2019.4-.5%208.1%201.2%2015.8%204.9996%2023.2%201.1%202.2%205.9%208%2010.6%2013%2014.7%2015.3%2042.4%2040.7%2044.4%2040.7%201.2%200%2012.1-10.1%2027.3-25.3%2022.9-22.8%2025.7-25.9%2028.6-32.1%204.1-8.7%205-18.8%202.5-26.4-4.9-14.6-23.4-23.4-38.6-18.4z'%20fill='%23ff5f5f'/%3e%3c/svg%3e",url:"https://ko-fi.com/99hoangtran",qrcode:Ue},{name:"Patreon",logo:"data:image/svg+xml,%3csvg%20viewBox='0%200%20512%20512'%20xmlns='http://www.w3.org/2000/svg'%20fill-rule='evenodd'%20clip-rule='evenodd'%20stroke-linejoin='round'%20stroke-miterlimit='2'%3e%3cg%20transform='matrix(.47407%200%200%20.47407%20.383%20.422)'%3e%3cclipPath%20id='prefix__a'%3e%3cpath%20d='M0%200h1080v1080H0z'/%3e%3c/clipPath%3e%3cg%20clip-path='url(%23prefix__a)'%3e%3cpath%20d='M1033.05%20324.45c-.19-137.9-107.59-250.92-233.6-291.7-156.48-50.64-362.86-43.3-512.28%2027.2-181.1%2085.46-237.99%20272.66-240.11%20459.36-1.74%20153.5%2013.58%20557.79%20241.62%20560.67%20169.44%202.15%20194.67-216.18%20273.07-321.33%2055.78-74.81%20127.6-95.94%20216.01-117.82%20151.95-37.61%20255.51-157.53%20255.29-316.38z'%20fill-rule='nonzero'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e",url:"https://patreon.com/99_hoangtran",qrcode:Re}],rowKey:e=>e.name,columns:[{title:l({en:"Provider",vi:"Tên"}),dataIndex:"name",key:"name",render:(e,t)=>S.jsxs(z,{direction:"vertical",children:[S.jsx(ee,{src:t.logo,style:{width:50},preview:!1}),S.jsx("b",{children:e})]})},{title:l({en:"Information",vi:"Thông tin"}),dataIndex:"url",key:"url",render(e,t){var i;return S.jsxs(S.Fragment,{children:[null==(i=t.infor)?void 0:i.map(((e,t)=>S.jsx("div",{children:S.jsx("b",{children:e})},t+"infor"))),S.jsx("a",{href:e,target:"_blank",children:null==e?void 0:e.replace("https://","")})]})}},{title:l({en:"QR Code",vi:"QR Code"}),dataIndex:"qrcode",key:"qrcode",render:(e,t)=>e?S.jsx(ee,{src:e,style:{maxWidth:200,maxHeight:200},preview:!t.note&&void 0}):S.jsx(B.Text,{children:t.note}),align:"center"}],style:{maxWidth:"90vw",overflow:"auto"},scroll:{x:"max-content"}})]});return S.jsxs(z,{direction:"vertical",align:"center",style:{width:"100%",justifyContent:"center"},children:[S.jsx(B.Title,{level:3,children:l({en:"Please check your information",vi:"Vui lòng kiểm tra thông tin"})}),S.jsxs(z,{align:"center",wrap:!0,style:{justifyContent:"center"},children:[S.jsxs(B.Text,{children:[l({en:"Selected Plan",vi:"Gói đã chọn"}),":"," "]}),S.jsxs(V,{color:(null==(n=null==t?void 0:t.tag)?void 0:n.color)||(null==(o=null==t?void 0:t.ribbon)?void 0:o.color),style:{fontSize:"1em"},children:[S.jsx("i",{className:(null==t?void 0:t.icon)+" fa-lg"})," ",l((null==t?void 0:t.tag)||(null==t?void 0:t.ribbon))]}),S.jsxs(V,{style:{fontSize:"1em"},children:[l(null==t?void 0:t.label)," - ",L(m)," VND",(null==(a=null==t?void 0:t.price)?void 0:a.en)>0?" - "+L(null==(r=null==t?void 0:t.price)?void 0:r.en)+" USD":""]})]}),S.jsxs(z,{align:"center",wrap:!0,style:{justifyContent:"center"},children:[S.jsxs(B.Text,{children:[l({en:"Selected User",vi:"Nick đã chọn"}),":"," "]}),e?S.jsxs(S.Fragment,{children:[S.jsxs(B.Link,{href:null==e?void 0:e.url,target:"_blank",children:[S.jsx(ie,{src:null==e?void 0:e.avatar,size:40})," ",null==e?void 0:e.name]}),S.jsxs(V,{style:{fontSize:"1em"},children:["(UID: ",null==e?void 0:e.uid,")"]})]}):S.jsxs(V,{color:"error",children:[S.jsx("i",{className:"fa-solid fa-circle-xmark"})," ",l({en:"Not selected",vi:"Chưa chọn"})]})]}),S.jsx(J,{}),(null==e?void 0:e.uid)?g:S.jsxs(S.Fragment,{children:[S.jsx(O,{showIcon:!0,type:"error",message:l({en:"You have not entered the Facebook UID. Payment will not automatically unlock VIP",vi:"Bạn chưa nhập UID nick Facebook. Thanh toán sẽ không tự động mở VIP"})}),S.jsx(O,{showIcon:!0,type:"warning",message:l({en:"After payment, please send the bill screenshot to Admin to unlock VIP",vi:"Sau khi thanh toán, vui lòng gửi hình chụp hoá đơn tới Admin để mở VIP"})})]}),S.jsx(J,{}),u]})};function Ze(){const{ti:e}=D(),{message:t}=N(),i=y($.profile),[n,o]=I("Checkout.profile",i),[a,r]=I("Checkout.currentStep",0),[l,c]=I("Checkout.selectedPlan",Xe.find((e=>e.isDefaultPlan)));s.useEffect((()=>{E("Checkout.onLoad")}),[]);const d=[{title:e({en:"Choose Plan",vi:"Chọn Gói"}),icon:S.jsx("i",{className:"fa-solid fa-gift"}),content:S.jsx(Qe,{selectedPlan:l,setSelectedPlan:i=>{c(i),t.success({key:"selected-plan",duration:3,content:e({en:"Selected plan: "+i.label.en+". Click here to Next step",vi:"Đã chọn gói: "+i.label.vi+". Bấm đây để Tiếp tục"}),onClick:()=>{r((e=>e+1)),t.destroy("selected-plan")}})}}),readyNext:!!l},{title:e({en:"Choose User",vi:"Chọn Nick"}),content:S.jsx(Ke,{profile:n,myProfile:i,setProfile:o}),readyNext:!0},{title:e({en:"Payment",vi:"Thanh Toán"}),icon:S.jsx("i",{className:"fa-solid fa-credit-card"}),content:S.jsx(Ye,{profile:n,selectedPlan:l})}];return S.jsxs(z,{style:{display:"flex",flexDirection:"column",alignItems:"center",width:"100%"},children:[S.jsx(Q,{align:"middle",style:{margin:16,marginBottom:0},children:S.jsxs(B.Title,{level:3,children:[e({en:"Buy VIP",vi:"Mua VIP"})," ",S.jsx("i",{className:"fa-solid fa-crown",style:{fontSize:30,color:"gold"}})]})}),S.jsx(O,{type:"info",showIcon:!0,message:S.jsxs(S.Fragment,{children:[e({en:"Buy VIP to Unlock VIP features",vi:"Mua VIP để mở khoá các tính năng VIP"})," ",S.jsx(w,{href:"#/vip",icon:S.jsx("i",{className:"fa-solid fa-crown"}),children:e({en:"What VIP have?",vi:"VIP có gì?"})})]}),style:{marginBottom:16,textAlign:"center"}}),S.jsx(ze,{current:a,items:d.map(((e,t)=>({title:e.title}))),style:{marginBottom:32,width:"100%"},onChange:r}),S.jsxs(z.Compact,{style:{marginBottom:32,width:"100%"},children:[a>0&&S.jsx(w,{onClick:()=>r(a-1),icon:S.jsx("i",{className:"fa-solid fa-arrow-left"}),children:e({en:"Back",vi:"Quay lại"})}),a<d.length-1&&S.jsx(w,{onClick:()=>r(a+1),icon:S.jsx("i",{className:"fa-solid fa-arrow-right"}),type:"primary",iconPosition:"end",disabled:!d[a].readyNext,children:e({en:"Next",vi:"Tiếp"})+": "+d[a+1].title})]}),d[a].content]})}export{Ze as default};
