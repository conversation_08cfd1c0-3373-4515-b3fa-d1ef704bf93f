const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./ProfileCard-Cinxbxq-.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./MyApp-DW5WH4Ub.js","./index-D7dTP8lW.js","./index-IxNfZAD-.js","./Dropdown-BcL-JHCf.js","./index-CDSnY0KE.js","./EyeOutlined-CNKPohhw.js","./addEventListener-C4tIKh7N.js","./index-GOnsP9v3.js","./posts-Fn0UXLRN.js","./videos-D2LbKcXH.js","./dayjs.min-CzPn7FMI.js","./useCacheState-CAnxkewm.js","./MyTable-DnjDmN7z.js","./index-ByRdMNW-.js","./index-BzMPigdO.js","./DownOutlined-B-JcS-29.js","./Table-BO80-bCE.js","./List-B6ZMXgC_.js","./index-D6X7PZwe.js","./index-CxAc8H5Q.js","./index-QU7lSj_a.js","./index-Dg3cFar0.js","./PurePanel-BvHjNOhQ.js","./move-ESLFEEsv.js","./index-C5gzSBcY.js","./index-SRy1SMeO.js","./SearchOutlined--mXbzQ68.js","./useBreakpoint-CPcdMRfj.js","./Pagination-2X6SDgot.js","./index-Bg865k-U.js","./row-BMfM-of4.js","./ExportButton-CbyepAf_.js","./getIds-DAzc-wzf.js","./useVIP-D5FAMGQQ.js","./sweetalert2.esm.all-BZxvatOx.js","./useAction-uz-MrZwJ.js","./index-CC5caqt_.js","./BadgeWrapper-BCbYXXfB.js","./useDevMode-yj0U7_8D.js","./Screen-Buq7HJpK.js","./col-CzA09p0P.js","./reactions-CR2nCZPF.js","./index-DdM4T8-Q.js","./index-PbLYNhdQ.js","./index-DwLJmsHL.js","./ClockCircleOutlined-BVovxNRp.js","./index-Bx3o6rwA.js","./index-aN3DIZnk.js","./About-BwZrng-C.js","./Stories-BbPLs0vR.js","./Collection-BYcChfHL.js","./react-hotkeys-hook.esm-wjq6pdfC.js","./index-jrOnBmdW.js","./icons-DFJmfgN3.js","./stories-DQ6hTlzb.js","./Albums-DAX0GD55.js","./albums-Bt4OWMRt.js","./Videos-DoKXi9RE.js","./Photos-6nUTLPjZ.js","./photos-CDfkOCs1.js","./Album-D1thQYzl.js","./Reels-BxdGi6Jf.js","./reels-Bl7il3t1.js","./LikedPages-0Yv-Pg1N.js","./pages-DvqVBMHR.js","./WordStatisticButton-D2UxB2M4.js","./index-Bp2s1Wws.js","./JoinedGroups-C9z8VG9_.js","./useForceStop-CvbJS7ei.js","./groups-D1WZyVD8.js","./CheckedIns-DazVFz2u.js","./GroupFiles-EMKN-GJ6.js","./GroupMembers-BzCrHBUQ.js","./groupMembers-T0pfmfMg.js","./Friends-DT1Yu2ZN.js","./Highlight-BwN3gU51.js","./highlights-BVXchAWb.js","./Media-Bivrw8q1.js","./download-KJKGHAGZ.js","./index-DRXkAFwH.js","./Comment-DAIfiR_T.js","./comments-BafLCvtq.js","./gender-D7cqwEK5.js","./Share-5hWzOGkn.js","./Reactions-C6nKycXe.js","./friends-CRlBxmhf.js","./follows-DFGgshWa.js","./friendRequests-DyD2rqMz.js","./Friendship-6riEOshw.js","./IGStories-DKSb0Qwr.js","./stories-CXRVmsoM.js","./core-Dw7OsFL6.js","./IGPosts-D_F0byGQ.js","./posts-6XIlY8Zu.js","./IGReels-CCSCpLIU.js","./reels-CadhT4bZ.js","./IGHighlights-B4AT3Spo.js","./highlights--x8Pz3Fi.js","./IGHighlightMedias-1WNG8mNy.js","./TiktokUserVideos-BNPu80Ip.js","./core-Bg7H-yRQ.js","./window-pPC-ycGO.js","./ThreadsPosts-BdsyMK1z.js","./posts-PKpyG5Nq.js","./core-CRQoNxLH.js"])))=>i.map(i=>d[i]);
import{r as e,bc as t,bl as a,b3 as r,aS as o,aQ as n,bk as s,b0 as i,b5 as l,aN as d,bm as m,bn as c,b1 as u,bo as g,bg as h,am as p}from"./index-Cak6rALw.js";import{u as _,d as b,t as v,s as k,S as I,i as j,z as y,j as f,B as E,A as T,c as w,C as x}from"./MyApp-DW5WH4Ub.js";import R from"./useCacheState-CAnxkewm.js";import{S as P}from"./Screen-Buq7HJpK.js";import{I as L}from"./index-CDSnY0KE.js";import{R as D}from"./row-BMfM-of4.js";import{A}from"./index-fI_muw4K.js";import{I as U}from"./index-Bg865k-U.js";import{T as V}from"./index-IxNfZAD-.js";import"./col-CzA09p0P.js";import"./EyeOutlined-CNKPohhw.js";import"./addEventListener-C4tIKh7N.js";import"./useBreakpoint-CPcdMRfj.js";import"./PurePanel-BvHjNOhQ.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./move-ESLFEEsv.js";import"./DownOutlined-B-JcS-29.js";import"./SearchOutlined--mXbzQ68.js";import"./Dropdown-BcL-JHCf.js";const C=u((()=>d((()=>import("./ProfileCard-Cinxbxq-.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9]),import.meta.url)),{fallback:i.jsx(p.Node,{active:!0})}),F={fallback:x},O=u((()=>d((()=>import("./index-GOnsP9v3.js")),__vite__mapDeps([10,1,2,11,3,12,13,14,15,16,17,18,19,9,20,21,22,23,24,6,25,26,27,28,29,30,31,32,8,33,34,35,36,37,5,38,39,40,41,42,43,44,45,46,7,4,47,48,49]),import.meta.url)),F),S=u((()=>d((()=>import("./index-aN3DIZnk.js")),__vite__mapDeps([50,1,2,14,3,5,6]),import.meta.url)),F),G=u((()=>d((()=>import("./About-BwZrng-C.js")),__vite__mapDeps([51,1,2,14,3]),import.meta.url)),F),B=u((()=>d((()=>import("./Stories-BbPLs0vR.js")),__vite__mapDeps([52,1,2,3,53,16,14,54,46,23,22,24,6,25,26,32,8,29,55,30,27,20,28,18,33,31,43,39,56,57,7,9]),import.meta.url)),F),H=u((()=>d((()=>import("./Albums-DAX0GD55.js")),__vite__mapDeps([58,1,2,3,53,16,14,54,46,23,22,24,6,25,26,32,8,29,55,30,27,20,28,18,33,31,43,39,59,40,7,9]),import.meta.url)),F),N=u((()=>d((()=>import("./Videos-DoKXi9RE.js")),__vite__mapDeps([60,1,2,3,53,16,14,54,46,23,22,24,6,25,26,32,8,29,55,30,27,20,28,18,33,31,43,39,12,7,9]),import.meta.url)),F),M=u((()=>d((()=>import("./Photos-6nUTLPjZ.js")),__vite__mapDeps([61,1,2,53,16,3,14,54,46,23,22,24,6,25,26,32,8,29,55,30,27,20,28,18,33,31,43,39,62]),import.meta.url)),F),$=u((()=>d((()=>import("./Album-D1thQYzl.js")),__vite__mapDeps([63,1,2,53,16,3,14,54,46,23,22,24,6,25,26,32,8,29,55,30,27,20,28,18,33,31,43,39,59,7,9]),import.meta.url)),F),K=u((()=>d((()=>import("./Reels-BxdGi6Jf.js")),__vite__mapDeps([64,1,2,3,53,16,14,54,46,23,22,24,6,25,26,32,8,29,55,30,27,20,28,18,33,31,43,39,65,7,9]),import.meta.url)),F),z=u((()=>d((()=>import("./LikedPages-0Yv-Pg1N.js")),__vite__mapDeps([66,1,2,3,34,24,6,25,26,53,16,14,54,46,23,22,32,8,29,55,30,27,20,28,18,33,31,43,39,67,68,40,69,49,5,17,7,9]),import.meta.url)),F),W=u((()=>d((()=>import("./JoinedGroups-C9z8VG9_.js")),__vite__mapDeps([70,1,2,3,71,15,16,17,18,19,9,20,21,22,23,24,6,25,26,27,28,29,30,31,32,8,33,34,14,72,68,40,69,49,5,7]),import.meta.url)),F),q=u((()=>d((()=>import("./CheckedIns-DazVFz2u.js")),__vite__mapDeps([73,1,2,3,71,15,16,17,18,19,9,20,21,22,23,24,6,25,26,27,28,29,30,31,32,8,33,34,14,7]),import.meta.url)),F),J=u((()=>d((()=>import("./GroupFiles-EMKN-GJ6.js")),__vite__mapDeps([74,1,2,53,16,3,14,54,46,23,22,24,6,25,26,32,8,29,55,30,27,20,28,18,33,31,43,39,7,9]),import.meta.url)),F),Q=u((()=>d((()=>import("./GroupMembers-BzCrHBUQ.js")),__vite__mapDeps([75,1,2,71,3,15,16,17,18,19,9,20,21,22,23,24,6,25,26,27,28,29,30,31,32,8,33,14,76,45,46,7]),import.meta.url)),F),X=u((()=>d((()=>import("./Friends-DT1Yu2ZN.js")),__vite__mapDeps([77,1,2,3]),import.meta.url)),F),Y=u((()=>d((()=>import("./Highlight-BwN3gU51.js")),__vite__mapDeps([78,1,2,3,79,53,16,14,54,46,23,22,24,6,25,26,32,8,29,55,30,27,20,28,18,33,31,43,39,57,7,9]),import.meta.url)),F),Z=u((()=>d((()=>import("./Media-Bivrw8q1.js").then((e=>e.M))),__vite__mapDeps([80,1,2,3,11,12,14,44,81,19,9,20,18,21,22,23,24,6,25,26,27,28,29,30,31,32,8,45,46,33,7,55,43,39,17,82]),import.meta.url)),F),ee=u((()=>d((()=>import("./Comment-DAIfiR_T.js")),__vite__mapDeps([83,1,2,84,3,12,15,16,17,18,19,9,20,21,22,23,24,6,25,26,27,28,29,30,31,32,8,33,14,85,34,44,40,45,46,7,49]),import.meta.url)),F),te=u((()=>d((()=>import("./Share-5hWzOGkn.js")),__vite__mapDeps([86,1,2]),import.meta.url)),F),ae=u((()=>d((()=>import("./Reactions-C6nKycXe.js")),__vite__mapDeps([87,1,2,3,44,15,16,17,18,19,9,20,21,22,23,24,6,25,26,27,28,29,30,31,32,8,33,34,68,40,69,49,5,14,38,36,37,35,39,41,88,85,89,90,45,46,7]),import.meta.url)),F),re=u((()=>d((()=>import("./Friendship-6riEOshw.js")),__vite__mapDeps([91,1,2,3,14,28,7,8,9]),import.meta.url)),F),oe=u((()=>d((()=>import("./IGStories-DKSb0Qwr.js")),__vite__mapDeps([92,1,2,56,53,16,3,14,54,46,23,22,24,6,25,26,32,8,29,55,30,27,20,28,18,33,31,43,39,93,94,7,9]),import.meta.url)),F),ne=u((()=>d((()=>import("./IGPosts-D_F0byGQ.js")),__vite__mapDeps([95,1,2,3,56,53,16,14,54,46,23,22,24,6,25,26,32,8,29,55,30,27,20,28,18,33,31,43,39,96,94,7,9,4,5]),import.meta.url)),F),se=u((()=>d((()=>import("./IGReels-CCSCpLIU.js")),__vite__mapDeps([97,1,2,3,53,16,14,54,46,23,22,24,6,25,26,32,8,29,55,30,27,20,28,18,33,31,43,39,98,12,94,7,9]),import.meta.url)),F),ie=u((()=>d((()=>import("./IGHighlights-B4AT3Spo.js")),__vite__mapDeps([99,1,2,3,53,16,14,54,46,23,22,24,6,25,26,32,8,29,55,30,27,20,28,18,33,31,43,39,100,94,7,9]),import.meta.url)),F),le=u((()=>d((()=>import("./IGHighlightMedias-1WNG8mNy.js")),__vite__mapDeps([101,1,2,56,53,16,3,14,54,46,23,22,24,6,25,26,32,8,29,55,30,27,20,28,18,33,31,43,39,100,94,7,9]),import.meta.url)),F),de=u((()=>d((()=>import("./TiktokUserVideos-BNPu80Ip.js")),__vite__mapDeps([102,1,2,103,3,15,16,17,18,19,9,20,21,22,23,24,6,25,26,27,28,29,30,31,32,8,33,14,68,40,69,49,5,104,34,36,37,35,7,4,55,43]),import.meta.url)),F),me=u((()=>d((()=>import("./ThreadsPosts-BdsyMK1z.js")),__vite__mapDeps([105,1,2,106,94,3,107,56,53,16,14,54,46,23,22,24,6,25,26,32,8,29,55,30,27,20,28,18,33,31,43,39,40,7,9,4,5]),import.meta.url)),F),ce=[{key:"Friendship",label:{en:"☘️ Friendship",vi:"☘️ Tình bạn"},closable:!1},{key:"Highlight",label:{en:"🌟 Highlight",vi:"🌟 Nổi bật"},closable:!1},{key:"Stories",label:{en:"❤️ Stories",vi:"❤️ Tin"},closable:!1},{key:"Tagged",label:{en:"📌 Tagged",vi:"📌 Lượt tag"},closable:!1},{key:"Posts",label:{en:"📝 Posts",vi:"📝 Bài viết"},closable:!1},{key:"Photos",label:{en:"📷 Photos",vi:"📷 Ảnh"},closable:!1},{key:"Videos",label:{en:"🎬 Videos",vi:"🎬 Video"},closable:!1},{key:"Albums",label:"🏞️ Albums",closable:!1},{key:"Reels",label:"🌈 Reels",closable:!1},{key:"Friends",label:{en:"👤 Friends",vi:"👤 Bạn bè"},closable:!1},{key:"Liked Pages",label:{en:"👍 Liked",vi:"👍 Thích"},closable:!1},{key:"Joined Groups",label:{en:"👥 Groups",vi:"👥 Nhóm"},closable:!1},{key:"Check In",label:"🌎 Check in",closable:!1},{key:"Files",label:"📁 Files",closable:!1},{key:"Members",label:{en:"👤 Members",vi:"👤 Thành viên"},closable:!1}],ue=[{key:"Post_Media",label:{en:"🎬 Media",vi:"🎬 Ảnh/Video"},closable:!1},{key:"Post_Reactions",label:{en:"👍 Reactions",vi:"👍 Cảm xúc"},closable:!1},{key:"Post_Comments",label:{en:"💬 Comments",vi:"💬 Bình luận"},closable:!1},{key:"Post_Shares",label:{en:"🔗 Shares",vi:"🔗 Chia sẻ"},closable:!1}],ge=[{key:"IG Stories",label:{en:"❤️ Stories",vi:"❤️ Tin"},closable:!1},{key:"IG Highlights",label:"🌟 Highlight",closable:!1},{key:"IG Posts",label:"📝 Posts",closable:!1},{key:"IG Reels",label:"🌈 Reels",closable:!1}],he=[{key:"Tiktok User Videos",label:"🎬 Videos",closable:!1}],pe=[{key:"Threads",label:"📝 Threads",closable:!1}],_e=[s.Facebook,s.FacebookPost,s.Instagram,s.Threads,s.Tiktok];function be(){var u,p,x;const F=t(),{ti:be}=_(),{message:ve}=b(),[ke,Ie]=a(),je=r(),ye=(null==(u=je.state)?void 0:u.targetId)||ke.get("targetId")||"",fe=(null==(p=je.state)?void 0:p.platform)||ke.get("platform")||"",Ee=o(n.platform),Te=o(n.setPlatform),we=o(n.bulkSearchHistory),xe=o(n.addBulkSearchHistory),Re=o(n.clearBulkSearchHistory),Pe=o(n.deleteBulkSearchHistory),[Le,De]=e.useState(!1),[Ae,Ue]=e.useState(fe||Ee||s.Facebook),[Ve,Ce]=R(`BulkDownloader.${Ae}.activeTab`,""),[Fe,Oe]=R(`BulkDownloader.${Ae}.tabs`,Ae===s.Facebook?ce:ge),[Se,Ge]=R(`BulkDownloader.${Ae}.about`,null),[Be,He]=R(`BulkDownloader.${Ae}.about.tiktok`,null),[Ne,Me]=R(`BulkDownloader.${Ae}.targetId`,Ae===fe?ye:""),[$e,Ke]=R(`BulkDownloader.${Ae}.${null==Se?void 0:Se.id}.postId`,""),[ze,We]=e.useState("");e.useEffect((()=>{v("BulkDownloader:onLoad")}),[]);const qe=e.useMemo((()=>{var e,t;const a=(null==(t=null==(e=null==we?void 0:we.filter((e=>e.platform===Ae&&(!ze||k(ze,e.targetId+e.name)))))?void 0:e.reverse())?void 0:t.map((e=>({value:e.targetId,label:i.jsxs(I,{style:{width:"100%",position:"relative"},children:[i.jsx(L,{src:e.avatar,width:24,style:{borderRadius:"50%"},fallback:Ae===s.Facebook?j(e.targetId,40):void 0,preview:!1}),i.jsxs("span",{children:[e.name," ",e.targetId!==e.name?` (${e.targetId})`:""]}),i.jsx(l,{onClick:t=>{t.stopPropagation(),t.preventDefault(),Pe(Ae,e.targetId)},icon:i.jsx("i",{className:"fa-solid fa-xmark"}),style:{position:"absolute",right:0,top:0,height:"100%"}})]})}))))||[];return a.length&&a.push({value:"",disabled:!0,label:i.jsx(I,{align:"center",style:{width:"100%",justifyContent:"flex-end"},children:i.jsx(l,{danger:!0,type:"text",icon:i.jsx("i",{className:"fa-solid fa-trash-can"}),onClick:()=>Re(Ae),children:be({en:"Clear",vi:"Clear"})})})}),a}),[we,Ne,Ae,ze]),Je=function(t){const a=e.useRef();return e.useEffect((()=>{a.current=t}),[t]),a.current}(Ve),Qe=(null==Se?void 0:Se.type)||y.User;e.useEffect((()=>{Te(Ae)}),[Ae,Te]),e.useEffect((()=>{ye&&Ye(ye)}),[ye]);const Xe=e.useMemo((()=>Ae===s.Facebook?{placeholder:be({en:"Enter facebook URL/ID",vi:"Nhập URL/ID facebook"}),canDownload:be({en:"Can download: Friendship / Highlight / Post / Tagged / Story / Photos / Videos / Reels / Albums / Group's Files / Joined Groups / Liked Pages / Checkin",vi:"Có thể tải: Tình bạn / Nổi bật / Bài viết / Lượt tag / Story / Ảnh / Videos / Reels / Albums / Group Files / Nhóm tham gia / Trang đã thích / Checkin"}),async onSearch(e){if(De(!0),!/^\d+$/.test(e)){const t=`BulkDownloader.onSearch.${e}`;ve.loading({key:t,duration:0,content:be({en:"Finding uid...",vi:"Đang tìm uid..."})});const{getVideoIdFromUrl:a}=await d((async()=>{const{getVideoIdFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getVideoIdFromUrl:e}}),__vite__mapDeps([35,1,2,3]),import.meta.url);if(await a(e))return ve.info({key:t,content:be({en:"Found video. Redirecting to video downloader...",vi:"Tìm thấy video. Chuyển hướng đến trình tải video..."})}),F({pathname:"/video-downloader",search:"url="+e}),void De(!1);const{getUidFromUrl:r}=await d((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([35,1,2,3]),import.meta.url),o=await r(e);if(!o)return ve.error({key:t,content:be({en:"Can not find uid: ",vi:"Không tìm thấy uid: "})+e}),void De(!1);Me(e=o),ve.success({key:t,content:be({en:"Found uid: ",vi:"Tìm thấy uid: "})+e})}f(e).then((t=>{console.log(t),Ge(t),Ke(""),Oe(ce),xe({platform:Ae,targetId:e,name:t.name,avatar:t.avatar})})).catch((e=>{ve.error(e.message)})).finally((()=>De(!1)))}}:Ae===s.Instagram?{placeholder:be({en:"Enter insta URL",vi:"Nhập URL instagram"}),canDownload:be({en:"Can download: IG Stories / IG Highlights / IG Posts / IG Reels",vi:"Có thể tải: Story / Highlight / IG Posts / IG Reels"}),async onSearch(e){if(De(!0),e.includes("instagram.com")){const{getInstaUsernameFromURL:t}=await d((async()=>{const{getInstaUsernameFromURL:e}=await import("./getIds-Dst2ZIJk.js");return{getInstaUsernameFromURL:e}}),[],import.meta.url),a=t(e);if(!a)return ve.error({content:be({en:"Can not find username: ",vi:"Không tìm thấy tên người dùng: "})+e}),void De(!1);Me(e=a)}const{getInstaUserInfo:t}=await d((async()=>{const{getInstaUserInfo:e}=await import("./core-Dw7OsFL6.js");return{getInstaUserInfo:e}}),__vite__mapDeps([94,1,2,3]),import.meta.url);t(e).then((t=>{Ge(t),Oe(ge),xe({platform:Ae,targetId:e,name:(null==t?void 0:t.name)||(null==t?void 0:t.username),avatar:null==t?void 0:t.avatar}),console.log(t)})).catch((e=>{ve.error(e.message+" - "+be({en:"Please make sure you are logged in to Instagram.",vi:"Vui lòng kiểm tra bạn đã đăng nhập Instagram."}))})).finally((()=>De(!1)))}}:Ae===s.Threads?{placeholder:be({en:"Enter threads URL",vi:"Nhập URL threads"}),canDownload:be({en:"Can download: Threads / Replies / Reposts",vi:"Có thể tải: Threads / Replies / Reposts"}),async onSearch(e){if(De(!0),e.includes("https://")){const{getThreadsUsernameFromURL:t}=await d((async()=>{const{getThreadsUsernameFromURL:e}=await import("./core-CRQoNxLH.js");return{getThreadsUsernameFromURL:e}}),__vite__mapDeps([107,1,2,3]),import.meta.url),a=t(e);if(!a)return ve.error({content:be({en:"Can not find username: ",vi:"Không tìm thấy tên người dùng: "})+e}),void De(!1);Me(e=a)}const{getThreadsUserInfo:t}=await d((async()=>{const{getThreadsUserInfo:e}=await import("./core-CRQoNxLH.js");return{getThreadsUserInfo:e}}),__vite__mapDeps([107,1,2,3]),import.meta.url);t(e).then((t=>{console.log(t),(null==t?void 0:t.id)?(Ge(t),Oe(pe),xe({platform:Ae,targetId:e,name:null==t?void 0:t.name,avatar:(null==t?void 0:t.avatarBig)||(null==t?void 0:t.avatar)})):ve.error({content:be({en:"Can not find username: ",vi:"Không tìm thấy người dùng: "})+e})})).catch((e=>{ve.error(e.message+" - "+be({en:"Please make sure you are logged in to Threads.",vi:"Vui lòng kiểm tra bạn đã đăng nhập Threads."}))})).finally((()=>De(!1)))}}:Ae===s.FacebookPost?{placeholder:be({en:"Enter facebook post URL",vi:"Nhập URL bài viết facebook"}),canDownload:be({en:"Can download: Media / Reactions / Comments / Shares",vi:"Có thể tải: Ảnh/Video / Cảm xúc / Bình luận / Chia sẻ"}),async onSearch(e){const t=`BulkDownloader.onSearch.${e}`;De(!0);try{ve.loading({key:t,duration:0,content:be({en:"Loading post id...",vi:"Đang tải id bài viết..."})});const{getPostIdFromUrl:a}=await d((async()=>{const{getPostIdFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getPostIdFromUrl:e}}),__vite__mapDeps([35,1,2,3]),import.meta.url),r=await a(e);if(!r)throw new Error(be({en:"Can not find post id",vi:"Không tìm thấy id bài viết"}));ve.loading({key:t,duration:0,content:be({en:"Loading post owner id...",vi:"Đang tải id người đăng..."})});const{getUidFromUrl:o}=await d((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([35,1,2,3]),import.meta.url),n=await o(e);if(!n)throw new Error(be({en:"Can not find owner id",vi:"Không tìm thấy id người đăng"}));ve.loading({key:t,duration:0,content:be({en:"Loading post owner info...",vi:"Đang tải thông tin người đăng..."})});const s=await f(n);if(!s)throw new Error(be({en:"Can not find owner info",vi:"Không tìm thấy thông tin người đăng"}));ve.success({key:t,content:be({en:"Load post info successfully",vi:"Tải thông tin bài viết thành công"})}),Ge(s),Me(e),Ke(r),Oe(ue),xe({platform:Ae,targetId:e,name:s.name,avatar:s.avatar})}catch(a){ve.error({key:t,content:a.message})}finally{De(!1)}}}:{placeholder:be({en:"Enter tiktok user URL",vi:"Nhập URL nguời dùng tiktok"}),canDownload:be({en:"Can download: All profile's videos",vi:"Có thể tải: Tất cả video trang cá nhân"}),async onSearch(e){De(!0);const{getTiktokUserInfo:t}=await d((async()=>{const{getTiktokUserInfo:e}=await import("./core-Bg7H-yRQ.js");return{getTiktokUserInfo:e}}),__vite__mapDeps([103,1,2,3]),import.meta.url);let a=await t(e);if(console.log(a),a){const e={id:a.id,name:a.nickname,avatar:a.avatar,username:a.secUid,url:"https://www.tiktok.com/@"+a.uniqueId,type:y.TikTokUser,raw:a};Oe(he),Me(a.uniqueId),xe({platform:Ae,targetId:a.uniqueId,name:e.name,avatar:e.avatar}),Ge(e),He(a)}else ve.error({content:be({en:"Can not find user info: ",vi:"Không tìm thấy thông tin người dùng: "})+e});De(!1)}}),[Ae,be]),Ye=async(e=Ne)=>{var t;const a=null==e?void 0:e.trim();0!=(null==a?void 0:a.length)&&(v("BulkDownloader:onSearch_"+Ae+":"+a),Me(a),null==(t=null==Xe?void 0:Xe.onSearch)||t.call(Xe,a))},Ze=e=>{const t="Album-"+e.id;v("BulkDownloader:onOpenAlbum"),Oe((a=>[...(null==a?void 0:a.filter((e=>e.key!==t)))||[],{key:t,label:"Album: "+e.name,closable:!0,props:{album:e}}])),Ce(t)},et=e=>{const t="IG Highlight-"+e.id;v("BulkDownloader:onOpenIGHighlight"),Oe((a=>[...a,{key:t,label:"IG Highlight: "+e.title,closable:!0,props:{highlight:e}}])),Ce(t)},tt=Se?Fe.map((e=>{const t=(e=>{var t,a;switch(e.key){case"Posts":return i.jsx(O,{target:Se});case"About":return Qe===y.User&&i.jsx(G,{target:Se});case"Tagged":return i.jsx(S,{target:Se});case"Stories":return i.jsx(B,{target:Se});case"Photos":return i.jsx(M,{target:Se});case"Videos":return i.jsx(N,{target:Se});case"Albums":return i.jsx(H,{target:Se,onOpenAlbum:Ze});case"Reels":return Qe!==y.Group&&i.jsx(K,{target:Se});case"Joined Groups":return Qe!==y.Group&&i.jsx(W,{target:Se});case"Liked Pages":return Qe===y.User&&i.jsx(z,{target:Se});case"Check In":return Qe===y.User&&i.jsx(q,{target:Se});case"Files":return Qe===y.Group&&i.jsx(J,{target:Se});case"Members":return Qe===y.Group&&i.jsx(Q,{target:Se});case"Friends":return Qe===y.User&&i.jsx(X,{target:Se});case"Highlight":return Qe!==y.Group&&i.jsx(Y,{target:Se});case"Friendship":return Qe!==y.Group&&i.jsx(re,{target:Se});case"Post_Media":return i.jsx(Z,{target:Se,postId:$e});case"Post_Comments":return i.jsx(ee,{target:Se,postId:$e});case"Post_Shares":return i.jsx(te,{target:Se,postId:$e});case"Post_Reactions":return i.jsx(ae,{target:Se,postId:$e});case"IG Stories":return i.jsx(oe,{target:Se});case"IG Posts":return i.jsx(ne,{target:Se});case"IG Reels":return i.jsx(se,{target:Se});case"IG Highlights":return i.jsx(ie,{target:Se,onOpenHighlight:et});case"Tiktok User Videos":return i.jsx(de,{target:Be});case"Threads":return i.jsx(me,{target:Se});default:return e.key.startsWith("Album-")?i.jsx($,{target:Se,album:null==(t=e.props)?void 0:t.album}):e.key.startsWith("IG Highlight-")?i.jsx(le,{target:Se,highlight:null==(a=e.props)?void 0:a.highlight}):null}})(e);return t?{key:e.key,label:be(e.label),closable:e.closable,children:t}:null})).filter(Boolean):[];return i.jsxs(P,{mode:"center",title:be({en:"Bulk Downloader",vi:"Tải Hàng Loạt"}),children:[i.jsxs(I,{direction:"vertical",align:"center",children:[i.jsx(D,{align:"middle",justify:"center",gutter:[5,5],children:_e.map((e=>i.jsx(E,{count:e===s.FacebookPost?be({en:"New",vi:"Mới"}):"",color:"purple",style:{color:"white",zIndex:2},children:i.jsx(l,{shape:"round",size:"middle",type:Ae===e?"primary":"default",onClick:()=>(e=>{v("BulkDownloader:onChangePlatform:"+e),Ue(e)})(e),style:{marginRight:5},icon:i.jsx("i",{className:c[e]||`fa-brands fa-${e.toLocaleLowerCase()} fa-lg`}),children:be(m[e]||e)})},e)))}),i.jsx(A,{value:Ne,options:qe,onSelect:Ye,onSearch:We,style:{width:350,maxWidth:"100%"},size:"large",children:i.jsx(U.Search,{size:"large",placeholder:Xe.placeholder,style:{width:350,maxWidth:"90vw"},onChange:e=>{var t;return Me(null==(t=e.target.value)?void 0:t.trim())},onSearch:()=>Ye(),enterButton:Le?null:i.jsx("i",{className:"fa-solid fa-magnifying-glass"}),loading:Le})}),Se?i.jsx(C,{about:Se,onClose:()=>{Ge(null),Ce(""),Me("")}}):null]}),tt.length?i.jsx(V,{defaultActiveKey:Ve,activeKey:Ve,type:"editable-card",centered:!0,hideAdd:!0,items:tt,onChange:e=>{v("BulkDownloader:onChangeTab:"+e),Ce(e)},onEdit:(e,t)=>{if("remove"===t){const t=Fe.filter((t=>t.key!==e));Oe(t),Ve==e&&Ce(Je||"")}},style:{maxWidth:"100%",marginTop:10}}):i.jsxs(I,{direction:"vertical",align:"center",style:{width:"100%",marginTop:10},children:[Xe.canDownload&&i.jsx(T,{type:"success",showIcon:!0,message:Xe.canDownload,style:{maxWidth:500,textAlign:"center"}}),(null==(x=g[Ae])?void 0:x.length)?i.jsx(h,{defaultActiveKey:["0"],items:[{label:be({en:`Supported ${Ae} URL/ID format ?`,vi:`Các định dạng ${Ae} được hỗ trợ ?`}),children:g[Ae].map((({icon:e,name:t,url:a},r)=>i.jsxs(w.Text,{children:[i.jsx("i",{className:e})," ",i.jsx("b",{children:be(t)}),Array.isArray(a)?i.jsx("ul",{children:a.map((e=>i.jsx("li",{children:e},e)))}):i.jsx(i.Fragment,{children:"u"})]},r)))}]}):null]})]})}export{be as default};
