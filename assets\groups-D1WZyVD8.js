import{y as o,x as e,az as i,W as n}from"./MyApp-DW5WH4Ub.js";import"./index-Cak6rALw.js";async function l({uid:l="",cursor:r}){const d=await o({fb_api_req_friendly_name:"ProfileCometAppCollectionGridRendererPaginationQuery",variables:{count:8,cursor:r??null,id:btoa(`app_collection:${l||await n()}:2361831622:66`)},doc_id:"5244211935648733"}),u=e(d);console.log(u);const{edges:a=[],page_info:t={}}=i(u);return a.map((o=>{var e,i,n,l,r;return{id:(null==(e=o.node.node)?void 0:e.id)||atob(o.node.id).split(":").at(-1),name:o.node.title.text,description:null==(l=null==(n=null==(i=o.node.subtitle_text)?void 0:i.text)?void 0:n.split("\n"))?void 0:l[1],url:o.node.url,visibility:o.node.node.visibility,image:o.node.image.uri,membersCount:Number(((null==(r=o.node.subtitle_text.text.split("\n"))?void 0:r[0])||"").match(/\d+/g).join("")??1),cursor:o.cursor}}))}var r=(o=>(o.NONE="NONE",o.TOP_LEVEL_GROUP="TOP_LEVEL_GROUP",o))(r||{}),d=(o=>(o.NOT_JOINED="NOT_JOINED",o.PENDING="PENDING",o.MEMBER="MEMBER",o.ADMIN="ADMIN",o))(d||{});function u(o,e="MEMBER"){var i,n,l,r,d,u,a,t,s;return{id:null==(i=null==o?void 0:o.node)?void 0:i.id,name:null==(n=null==o?void 0:o.node)?void 0:n.name,avatar:null==(r=null==(l=null==o?void 0:o.node)?void 0:l.profile_picture)?void 0:r.uri,url:null==(d=null==o?void 0:o.node)?void 0:d.url,lastVisitedTime:1e3*((null==(u=null==o?void 0:o.node)?void 0:u.viewer_last_visited_time)||(null==(a=null==o?void 0:o.node)?void 0:a.viewer_request_to_join_timestamp)||(null==(t=null==o?void 0:o.node)?void 0:t.last_post_time)||0),userType:e,cursor:null==o?void 0:o.cursor,subspaceType:(null==(s=null==o?void 0:o.node)?void 0:s.subspace_type)||"NONE"}}async function a({cursor:n="",ordering:l=["viewer_added"]}={}){var r,d,a,t;if(!n){const i=await o({fb_api_req_friendly_name:"GroupsCometJoinsRootQuery",variables:{ordering:l,scale:2},doc_id:"6985470494844388"}),n=e(i),s=[],{pending_groups:_={},all_joined_groups:c={}}=(null==(r=null==n?void 0:n.data)?void 0:r.viewer)||{};return null==(d=_.edges)||d.forEach((o=>{s.push(u(o,"PENDING"))})),null==(t=null==(a=c.tab_groups_list)?void 0:a.edges)||t.forEach((o=>{s.push(u(o,"MEMBER"))})),console.log(n),s}const s=await o({fb_api_req_friendly_name:"GroupsCometAllJoinedGroupsSectionPaginationQuery",variables:{cursor:n||null,ordering:l,count:20,scale:2},doc_id:"6009728632468556"}),_=e(s);console.log(_);const{edges:c=[],page_info:p={}}=i(_);return c.map((o=>u(o,"MEMBER")))}async function t({cursor:n=""}={}){var l;const r=await o({variables:{adminGroupsCount:1e3,memberGroupsCount:0,scale:1.5},doc_id:"4868264159917793"}),d=e(r);console.log(d);const{edges:a=[],page_info:t={}}=i(null==(l=null==d?void 0:d.data)?void 0:l.adminGroups);return a.map((o=>u(o,"ADMIN")))}async function s(i=""){var l;const r=await o({fb_api_req_friendly_name:"useGroupLeaveMutation",variables:{input:{group_id:i,readd_policy:"ALLOW_READD",source:"comet_group_page",actor_id:await n(),client_mutation_id:"1"},groupID:i},doc_id:"2018579381571489"}),d=e(r);return console.log(d),null!=(null==(l=null==d?void 0:d.data)?void 0:l.group_leave)}export{r as SubspaceType,d as UserTypeInGroup,a as getMyJoinedGroups,t as getMyManagedGroups,l as getOtherJoinedGroups,s as leaveGroup};
