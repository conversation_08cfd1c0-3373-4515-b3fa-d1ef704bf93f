import{r as n,b0 as o,bg as t,b5 as e}from"./index-Cak6rALw.js";import{S as i}from"./Screen-Buq7HJpK.js";import{u as s,A as a,S as r,ah as c}from"./MyApp-DW5WH4Ub.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";function l(){const{ti:l}=s(),[m,h]=n.useState(!0);return o.jsxs(i,{mode:"center",children:[o.jsx(a,{type:"info",showIcon:!0,message:l({vi:"Tìm kiếm hình ảnh trên Instagram tại vị trí cụ thể",en:"Search for images on Instagram at a specific location"}),style:{marginBottom:10}}),o.jsx(t,{style:{marginBottom:10},expandIconPosition:"right",className:"highlight-text",items:[{key:"1",label:l({vi:"Hướng dẫn sử dụng",en:"How to use?"}),children:o.jsxs(r,{direction:"vertical",size:10,children:[o.jsxs("ul",{children:[o.jsx("li",{children:l({vi:'🔍 Nhập tên địa điểm vào "Enter location name..."',en:'🔍 Enter the location name in "Enter location name..."'})}),o.jsx("li",{children:l({vi:"🖱️ Hoặc bấm trên bản đồ để chọn nhanh vị trí",en:"🖱️ Or click on the map to quickly select a location"})}),o.jsx("li",{children:l({vi:'🌐 Nhấn nút tìm 🔎 bên cạnh ô "Enter location name..." để mở Google Search',en:'🌐 Click the 🔎 button next to "Enter location name..." to open Google Search'})}),o.jsx("li",{children:l({vi:"🔗 Copy link địa điểm bạn muốn từ kết quả Google",en:"🔗 Copy the link of the place you want from Google results"})}),o.jsx("li",{children:l({vi:'📥 Dán link vào ô "Location URL", rồi bấm icon tìm kiếm để xem ảnh',en:'📥 Paste the link into "Location URL", then click the search icon to find photos'})})]}),o.jsx(a,{showIcon:!0,type:"info",message:l({en:"📌 Why Google? Because Instagram only support search by location name, not support precise location search.",vi:"📌 Lý do cần Google: vì Instagram chỉ tìm theo tên địa điểm, không hỗ trợ tìm vị trí chính xác."})})]})}]}),o.jsx(e,{icon:o.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"}),iconPosition:"end",style:{marginBottom:10},onClick:()=>{window.open("https://www.osintcombine.com/free-osint-tools/instagram-explorer","_blank")},children:l({en:"Open in new tab",vi:"Mở trong tab mới"})}),m&&o.jsxs("div",{children:[o.jsx(c,{})," Loading..."]}),o.jsx("iframe",{src:"https://www-osintcombine-com.filesusr.com/html/c17c29_f872a4d3a56a635adbbea278fc90bc21.html",style:{width:1200,maxWidth:"100%",height:700,borderRadius:10,border:"none"},onLoad:()=>h(!1)})]})}export{l as default};
