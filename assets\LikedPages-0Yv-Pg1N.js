import{b0 as e}from"./index-Cak6rALw.js";import{u as t,s as i,o as a,A as r,S as s,c as o}from"./MyApp-DW5WH4Ub.js";import{E as n}from"./ExportButton-CbyepAf_.js";import d from"./Collection-BYcChfHL.js";import{getOtherLikedPages as l}from"./pages-DvqVBMHR.js";import m from"./WordStatisticButton-D2UxB2M4.js";import p from"./useCacheState-CAnxkewm.js";import{L as j}from"./index-jrOnBmdW.js";import{I as c}from"./index-CDSnY0KE.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-ByRdMNW-.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-PbLYNhdQ.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CC5caqt_.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./BadgeWrapper-BCbYXXfB.js";import"./index-Bp2s1Wws.js";import"./index-Bx3o6rwA.js";import"./index-IxNfZAD-.js";import"./index-BzMPigdO.js";import"./addEventListener-C4tIKh7N.js";function g({target:g}){const{ti:h}=t();p("Pages.data",[]);return e.jsx(d,{collectionName:(null==g?void 0:g.name)+" - Liked Pages",fetchNext:async(e=[],t)=>{var i;if(!(null==g?void 0:g.id))return;return await l({uid:g.id,cursor:t||(null==(i=e[e.length-1])?void 0:i.cursor)})},renderItem:t=>e.jsx(j.Item,{children:e.jsxs(s,{direction:"vertical",style:{maxWidth:150},children:[e.jsx(c,{src:t.image,style:{width:150,height:150,objectFit:"contain",borderRadius:10}}),e.jsxs("span",{style:{margin:0},children:[e.jsxs(o.Text,{type:"secondary",children:[" ",t.id," "]}),e.jsx("br",{}),e.jsx(o.Link,{strong:!0,href:t.url,target:"_blank",children:t.name})]})]})}),getItemCursor:e=>e.cursor,rowKey:e=>e.id,showPagination:!0,all:!0,searchPlaceholder:e=>h({en:`Search in ${e.length} liked pages`,vi:`Tìm trong ${e.length} trang được thích`}),onSearch:(e,t)=>i(e,t.name+t.id),header:t=>{var i,a,s,o;const n=(null==(i=t[0])?void 0:i.total)&&(null==(a=t[0])?void 0:a.total)!==t.length;return e.jsx(r,{type:"success",showIcon:!0,message:h({en:`Liked ${t.length} public pages`+(n?` (in total ${null==(s=t[0])?void 0:s.total} likes)`:""),vi:`Đã thích ${t.length} trang công khai`+(n?` (trong tổng ${null==(o=t[0])?void 0:o.total} lượt thích)`:"")})})},headerButtons:t=>e.jsxs(e.Fragment,{children:[e.jsx(n,{data:t,options:[{key:"uid",label:".txt (pages id)",prepareData:e=>({fileName:(null==g?void 0:g.name)+"_liked_pages_id.txt",data:e.map((e=>e.id)).join("\n")})},{key:"id_name",label:".csv (page id+name)",prepareData:e=>({fileName:(null==g?void 0:g.name)+"_liked_pages_id_name.csv",data:a(e.map((e=>({id:e.id,name:e.name}))))})},{key:"json",label:".json",prepareData:e=>({fileName:(null==g?void 0:g.name)+"_liked_pages.json",data:JSON.stringify(e,null,4)})},{key:"csv",label:".csv",prepareData:e=>({fileName:(null==g?void 0:g.name)+"_liked_pages.csv",data:a(e)})}]}),e.jsx(m,{name:"LikedPages",text:t.map((e=>e.name)).join(" ")})]})})}export{g as default};
