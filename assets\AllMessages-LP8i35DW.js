import{bc as e,r as i,b0 as n,b5 as t}from"./index-Cak6rALw.js";import{d as a,u as s,a as o,t as r,g as l,c as d,b as c,T as m,S as p,h,i as g,o as u}from"./MyApp-DW5WH4Ub.js";import{E as j}from"./ExportButton-CbyepAf_.js";import x from"./useCacheState-CAnxkewm.js";import{g as v,a as f,s as y}from"./messages-wZDWdkVz.js";import{checkVIP as w}from"./useVIP-D5FAMGQQ.js";import{a as k}from"./file-download-B-gszDlL.js";import{d as b}from"./dayjs.min-CzPn7FMI.js";import{B as C}from"./BadgeWrapper-BCbYXXfB.js";import D from"./MyTable-DnjDmN7z.js";import{R as N}from"./row-BMfM-of4.js";import{D as I}from"./index-Dg3cFar0.js";import{A as T}from"./index-DdM4T8-Q.js";import{I as M}from"./index-CDSnY0KE.js";import"./sweetalert2.esm.all-BZxvatOx.js";import"./getIds-DAzc-wzf.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./index-IxNfZAD-.js";import"./Dropdown-BcL-JHCf.js";import"./index-ByRdMNW-.js";import"./index-BzMPigdO.js";import"./DownOutlined-B-JcS-29.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-C5gzSBcY.js";import"./PurePanel-BvHjNOhQ.js";import"./index-SRy1SMeO.js";import"./move-ESLFEEsv.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./index-PbLYNhdQ.js";const{Title:S}=d,L=e=>{const i=new Set;return e.map((e=>e.isGroup?e.participants.map((e=>({uid:e.id,name:e.name}))):{uid:e.id,name:e.name})).flat().forEach((({uid:e,name:n})=>{i.has(e)||i.add({uid:e,name:n})})),Array.from(i.values())};function A(e){return"-no data-"==e.name||"Người dùng Facebook"==e.name}function F(){const{message:d,notification:F}=a(),{ti:G}=s(),E=e(),{profile:H}=o(),[P,_]=x("AllMessages.data",[]),[B,O]=i.useState(!1);i.useEffect((()=>{(null==P?void 0:P.length)||q()}),[]);const q=()=>{if(B)return;const e="AllMessages:onClickReload";r(e),d.loading({key:e,duration:0,content:G({en:"Fetching messages...",vi:"Đang tải tin nhắn"})}),O(!0),v().then((i=>{if(console.log(i),!(null==i?void 0:i.length))return d.error({key:e,content:G({en:"No data to show",vi:"Không có dữ liệu"})});_(i),d.success({key:e,content:G({en:"Fetch messages completed",vi:"Tải xong tin nhắn"})})})).catch((i=>{d.error({key:e,content:G({en:"Failed to fetch messages",vi:"Lỗi tải tin nhắn"})+": "+i.message}),console.log(i)})).finally((()=>{O(!1)}))},K=e=>()=>{var i,n,t,a;r("AllMessages:onClickFirstMessages"),E("/messages/first",{state:{threadId:e.id||(null==(n=null==(i=null==e?void 0:e.participants)?void 0:i[0])?void 0:n.id),threadName:e.name||(null==(a=null==(t=null==e?void 0:e.participants)?void 0:t[0])?void 0:a.name)}})},R=async e=>{if(!(await w()))return;const i="AllMessages:onClickDownloadThread";r(i);const t=e.name,a=i+e.id;d.loading({key:a,duration:0,content:G({en:"Downloading messages...",vi:"Đang tải tin nhắn..."})+t});let s=!1;const o=await f({threadId:e.id,checkStopFn:()=>s,progressCallback:e=>{d.loading({key:a,duration:0,content:n.jsxs(n.Fragment,{children:[G({en:"Downloading messages... ",vi:"Đang tải tin nhắn... "})+t,n.jsx("br",{}),e.length,G({en:" messages",vi:" tin nhắn"}),n.jsx("br",{}),b(e[0].time).format("YYYY-MM-DD HH:mm:ss"),n.jsx("br",{}),n.jsx("i",{children:G({en:"Click to stop",vi:"Bấm để dừng"})})]}),onClick:()=>{d.loading({key:a,duration:0,content:G({en:"Stopping...",vi:"Đang dừng..."})}),s=!0}})}});d.destroy(a),F.open({type:"success",duration:0,message:G(s?{en:"Download stopped: ",vi:"Đã dừng tải: "}:{en:"Download completed: ",vi:"Tải xong: "})+t,description:o.length+G({en:" messages",vi:" tin nhắn"})});const l=y({threadId:e.id||"",threadName:t,myUid:(null==H?void 0:H.uid)||"",msgs:o});return k(l,t+".html"),s},X=[{title:"#",key:"recent",dataIndex:"recent",sorter:(e,i)=>e.recent-i.recent,render:(e,i,n)=>i.recent+1,width:70,align:"center",headerAlign:"center"},{title:G({en:"Name",vi:"Tên"}),dataIndex:"name",key:"name",sorter:(e,i)=>e.name.localeCompare(i.name),render:(e,i,t)=>{var a,s,o,r,l,d,c,m;return n.jsxs(N,{align:"middle",children:[i.isGroup?n.jsx(I,{arrow:!0,overlayStyle:{maxHeight:"350px",overflow:"auto",border:"1px dashed gray",borderRadius:"5px"},menu:{items:[{key:"-1",type:"group",label:n.jsx(S,{level:5,style:{textAlign:"center"},children:G({en:`${null==(a=i.participants)?void 0:a.length} members`,vi:`${null==(s=i.participants)?void 0:s.length} thành viên`})})},{type:"divider"},...(null==(r=null==(o=i.participants)?void 0:o.map)?void 0:r.call(o,(e=>({key:e.id,label:n.jsx("b",{children:e.name}),icon:n.jsx(T,{shape:"square",src:e.avatar}),onClick:()=>window.open(h(e.id))}))))||[]]},children:n.jsx(p,{children:i.image?n.jsx(T,{shape:"square",size:40,src:n.jsx(M,{src:i.image})}):n.jsx(T.Group,{max:{count:5},children:i.participants.filter((e=>e.id!=(null==H?void 0:H.id))).map((e=>n.jsx(T,{src:e.avatar},e.id)))})})}):n.jsx(T,{shape:"square",size:40,src:n.jsx(M,{src:i.image||g(null==(d=null==(l=i.participants)?void 0:l[0])?void 0:d.id),fallback:null==(m=null==(c=i.participants)?void 0:c[0])?void 0:m.avatar})}),n.jsx("a",{href:i.url,target:"_blank",style:{marginLeft:"10px"},children:n.jsx("b",{children:i.name})})]})},filters:[{text:G({en:"Inactive (",vi:"Không hoạt động ("})+P.filter(A).length+")",value:"inactive"}],onFilter:(e,i)=>"inactive"!=e||A(i),onSearch:(e,i,n)=>{let t=e.toLowerCase();return n.name.toLocaleLowerCase().includes(t)||n.participants.some((e=>e.name.toLocaleLowerCase().includes(t)))},width:"auto"},{title:"Id",dataIndex:"id",key:"id",sorter:(e,i)=>e.id>i.id,onSearch:(e,i,n)=>{let t=e.toLowerCase();return n.id.includes(t)||n.participants.some((e=>e.id.includes(t)))},width:150},{title:G({en:"Messages",vi:"Tin nhắn"}),dataIndex:"count",key:"count",sorter:(e,i)=>e.count-i.count,render:(e,i,n)=>l(i.count),width:100,align:"right"},{title:G({en:"Members",vi:"Nguời"}),dataIndex:"participants",key:"participants",sorter:(e,i)=>e.participants.length-i.participants.length,render:(e,i,n)=>l(i.participants.length),width:100,align:"right"},{title:G({en:"Type",vi:"Loại"}),dataIndex:"type",key:"type",render:(e,i,n)=>i.isGroup?G({en:"Group",vi:"Nhóm"}):G({en:"Personal",vi:"Cá nhân"}),filters:[{text:G({en:"Group (",vi:"Nhóm ("})+P.filter((e=>e.isGroup)).length+")",value:!0},{text:G({en:"Personal (",vi:"Cá nhân ("})+P.filter((e=>!e.isGroup)).length+")",value:!1}],onFilter:(e,i)=>i.isGroup==e,width:100,align:"right"},{title:G({en:"Action",vi:"Hành động"}),dataIndex:"action",key:"download",render:(e,i,a)=>n.jsxs(p.Compact,{children:[n.jsx(m,{title:G({en:"First messages",vi:"Tin nhắn đầu tiên"}),children:n.jsx(t,{type:"primary",icon:n.jsx("i",{className:"fa-solid fa-clock-rotate-left"}),onClick:K(i)})}),n.jsx(m,{title:G({en:"Download messages",vi:"Tải cuộc trò chuyện"}),children:n.jsx(t,{type:"primary",icon:n.jsx("i",{className:"fa-solid fa-download"}),onClick:()=>R(i)})}),n.jsx(m,{title:G({en:"Delete (Coming soon)",vi:"Xoá (Sắp có)"}),children:n.jsx(t,{danger:!0,disabled:!0,icon:n.jsx("i",{className:"fa-solid fa-trash"})})})]}),width:150,align:"right"}];return n.jsxs("div",{style:{display:"flex",flexDirection:"column",width:"100%",height:"100%"},children:[n.jsxs(N,{align:"middle",style:{margin:16},children:[n.jsx(S,{level:3,style:{margin:0},children:G({en:"Messages manager",vi:"Quản lý tin nhắn"})}),n.jsx(c,{style:{marginLeft:"10px",fontWeight:"bold",color:"#888"},children:P.length}),n.jsx(m,{title:G({en:'Can only view messages that not have "End to end encryption" (e2ee)',vi:'Chỉ có thể xem tin nhắn không bị "Mã hoá đầu cuối" (e2ee)'}),children:n.jsx(c,{color:"orange",icon:n.jsx("i",{className:"fa-solid fa-lock-open"}),children:" "+G({en:"Only none e2ee",vi:"Không bị mã hoá đầu cuối"})})})]}),n.jsx(D,{data:P,columns:X,size:"small",searchable:!0,selectable:!0,keyExtractor:e=>e.id,style:{flex:1,maxHeight:"100%"},renderTitle:e=>n.jsxs(n.Fragment,{children:[n.jsx(t,{type:"primary",icon:B?n.jsx("i",{className:"fa-solid fa-rotate-right fa-spin"}):n.jsx("i",{className:"fa-solid fa-rotate-right"}),onClick:q,children:G({en:"Reload",vi:"Tải lại"})}),n.jsx(j,{data:e.length?e:P,options:[{key:"uid",label:".txt (uid)",prepareData:e=>({fileName:"messages_uid.txt",data:L(e).map((e=>e.uid)).join("\n")})},{key:"uid_name",label:".csv (uid+name)",prepareData:e=>({fileName:"messages_uid_name.csv",data:u(L(e))})},{key:"json",label:".json",prepareData:e=>({fileName:"messages.json",data:JSON.stringify(e,null,2)})},{key:"csv",label:".csv",prepareData:e=>({fileName:"messages.csv",data:u(e.map((e=>({...e,participants:e.participants.map((e=>e.name)).join(", "),participantIds:e.participants.map((e=>e.id)).join(", ")}))))})}]}),n.jsxs(p.Compact,{children:[n.jsx(m,{title:G({en:"Delete selected (Coming soon)",vi:"Xoá lựa chọn (Sắp có)"}),children:n.jsx(t,{danger:!0,disabled:!0,icon:n.jsx("i",{className:"fa-solid fa-trash-can"}),onClick:()=>{return i=e,void console.log(i);var i},children:G({en:"Delete",vi:"Xoá"})+(e.length?" "+e.length:"")})}),n.jsx(m,{title:e.length?G({en:"Download "+e.length+" messages",vi:"Tải "+e.length+" cuộc trò chuyện"}):G({en:"Select messages to download",vi:"Chọn cuộc trò chuyện để tải"}),children:n.jsxs(t,{disabled:!(null==e?void 0:e.length),icon:n.jsx("i",{className:"fa-solid fa-download"}),onClick:()=>(async e=>{if(!(await w()))return;r("AllMessages:onClickDownloadMultipleThreads");for(let i of e)if(await R(i))break})(e),children:[G({en:"Download ",vi:"Tải "})," ",e.length||""]})})]}),n.jsx(C,{type:"new",children:n.jsx(m,{title:G({en:"How to delete all conversations",vi:"Cách xoá tất cả cuộc trò chuyện"}),children:n.jsx(t,{href:"https://www.facebook.com/groups/fbaio/posts/1617211938933488",target:"_blank",icon:n.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"}),iconPosition:"end",children:G({en:"Delete all",vi:"Xoá tất cả"})})})})]})})]})}export{F as default};
