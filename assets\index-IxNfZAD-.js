import{r as e,I as t,h as n,n as a,$ as o,w as r,d as i,bq as l,e as c,a4 as s,m as d,a3 as u,o as v,i as f,u as p,l as b,C as m,T as h,Y as g,t as $,L as y,M as k,J as w,O as x,aa as S,a9 as _,an as C,G as E,af as P,as as R,aw as I}from"./index-Cak6rALw.js";import{ab as M,ac as T,Z as L,ad as O,J as z,Q as B}from"./MyApp-DW5WH4Ub.js";import{D}from"./Dropdown-BcL-JHCf.js";var N={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"},H=function(a,o){return e.createElement(t,n({},a,{ref:o,icon:N}))},j=e.forwardRef(H);const G=e.createContext(null);var A={width:0,height:0,left:0,top:0};function W(t,n){var o=e.useRef(t),r=e.useState({}),i=a(r,2)[1];return[o.current,function(e){var t="function"==typeof e?e(o.current):e;t!==o.current&&n(t,o.current),o.current=t,i({})}]}var X=Math.pow(.995,20);function q(t){var n=e.useState(0),o=a(n,2),r=o[0],i=o[1],c=e.useRef(0),s=e.useRef();return s.current=t,l((function(){var e;null===(e=s.current)||void 0===e||e.call(s)}),[r]),function(){c.current===r&&(c.current+=1,i(c.current))}}var K={width:0,height:0,left:0,top:0,right:0};function F(e){var t;return e instanceof Map?(t={},e.forEach((function(e,n){t[n]=e}))):t=e,JSON.stringify(t)}function V(e){return String(e).replace(/"/g,"TABS_DQ")}function Y(e,t,n,a){return!(!n||a||!1===e||void 0===e&&(!1===t||null===t))}var U=e.forwardRef((function(t,n){var a=t.prefixCls,o=t.editable,r=t.locale,i=t.style;return o&&!1!==o.showAdd?e.createElement("button",{ref:n,type:"button",className:"".concat(a,"-nav-add"),style:i,"aria-label":(null==r?void 0:r.addAriaLabel)||"Add tab",onClick:function(e){o.onEdit("add",{event:e})}},o.addIcon||"+"):null})),J=e.forwardRef((function(t,n){var a,o=t.position,r=t.prefixCls,i=t.extra;if(!i)return null;var l={};return"object"!==c(i)||e.isValidElement(i)?l.right=i:l=i,"right"===o&&(a=l.right),"left"===o&&(a=l.left),a?e.createElement("div",{className:"".concat(r,"-extra-content"),ref:n},a):null})),Q=e.forwardRef((function(t,o){var r=t.prefixCls,i=t.id,l=t.tabs,c=t.locale,v=t.mobile,f=t.more,p=void 0===f?{}:f,b=t.style,m=t.className,h=t.editable,g=t.tabBarGutter,$=t.rtl,y=t.removeAriaLabel,k=t.onTabClick,w=t.getPopupContainer,x=t.popupClassName,S=e.useState(!1),_=a(S,2),C=_[0],E=_[1],P=e.useState(null),R=a(P,2),I=R[0],L=R[1],O=p.icon,z=void 0===O?"More":O,B="".concat(i,"-more-popup"),N="".concat(r,"-dropdown"),H=null!==I?"".concat(B,"-").concat(I):null,j=null==c?void 0:c.dropdownAriaLabel;var G=e.createElement(M,{onClick:function(e){var t=e.key,n=e.domEvent;k(t,n),E(!1)},prefixCls:"".concat(N,"-menu"),id:B,tabIndex:-1,role:"listbox","aria-activedescendant":H,selectedKeys:[I],"aria-label":void 0!==j?j:"expanded dropdown"},l.map((function(t){var n=t.closable,a=t.disabled,o=t.closeIcon,r=t.key,l=t.label,c=Y(n,o,h,a);return e.createElement(T,{key:r,id:"".concat(B,"-").concat(r),role:"option","aria-controls":i&&"".concat(i,"-panel-").concat(r),disabled:a},e.createElement("span",null,l),c&&e.createElement("button",{type:"button","aria-label":y||"remove",tabIndex:0,className:"".concat(N,"-menu-item-remove"),onClick:function(e){e.stopPropagation(),function(e,t){e.preventDefault(),e.stopPropagation(),h.onEdit("remove",{key:t,event:e})}(e,r)}},o||h.removeIcon||"×"))})));function A(e){for(var t=l.filter((function(e){return!e.disabled})),n=t.findIndex((function(e){return e.key===I}))||0,a=t.length,o=0;o<a;o+=1){var r=t[n=(n+e+a)%a];if(!r.disabled)return void L(r.key)}}e.useEffect((function(){var e=document.getElementById(H);e&&e.scrollIntoView&&e.scrollIntoView(!1)}),[I]),e.useEffect((function(){C||L(null)}),[C]);var W=s({},$?"marginRight":"marginLeft",g);l.length||(W.visibility="hidden",W.order=1);var X=d(s({},"".concat(N,"-rtl"),$)),q=v?null:e.createElement(D,n({prefixCls:N,overlay:G,visible:!!l.length&&C,onVisibleChange:E,overlayClassName:d(X,x),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:w},p),e.createElement("button",{type:"button",className:"".concat(r,"-nav-more"),style:W,"aria-haspopup":"listbox","aria-controls":B,id:"".concat(i,"-more"),"aria-expanded":C,onKeyDown:function(e){var t=e.which;if(C)switch(t){case u.UP:A(-1),e.preventDefault();break;case u.DOWN:A(1),e.preventDefault();break;case u.ESC:E(!1);break;case u.SPACE:case u.ENTER:null!==I&&k(I,e)}else[u.DOWN,u.SPACE,u.ENTER].includes(t)&&(E(!0),e.preventDefault())}},z));return e.createElement("div",{className:d("".concat(r,"-nav-operations"),m),style:b,ref:o},q,e.createElement(U,{prefixCls:r,locale:c,editable:h}))}));const Z=e.memo(Q,(function(e,t){return t.tabMoving}));var ee=function(t){var n=t.prefixCls,a=t.id,o=t.active,r=t.focus,i=t.tab,l=i.key,c=i.label,u=i.disabled,v=i.closeIcon,f=i.icon,p=t.closable,b=t.renderWrapper,m=t.removeAriaLabel,h=t.editable,g=t.onClick,$=t.onFocus,y=t.onBlur,k=t.onKeyDown,w=t.onMouseDown,x=t.onMouseUp,S=t.style,_=t.tabCount,C=t.currentPosition,E="".concat(n,"-tab"),P=Y(p,v,h,u);function R(e){u||g(e)}var I=e.useMemo((function(){return f&&"string"==typeof c?e.createElement("span",null,c):c}),[c,f]),M=e.useRef(null);e.useEffect((function(){r&&M.current&&M.current.focus()}),[r]);var T=e.createElement("div",{key:l,"data-node-key":V(l),className:d(E,s(s(s(s({},"".concat(E,"-with-remove"),P),"".concat(E,"-active"),o),"".concat(E,"-disabled"),u),"".concat(E,"-focus"),r)),style:S,onClick:R},e.createElement("div",{ref:M,role:"tab","aria-selected":o,id:a&&"".concat(a,"-tab-").concat(l),className:"".concat(E,"-btn"),"aria-controls":a&&"".concat(a,"-panel-").concat(l),"aria-disabled":u,tabIndex:u?null:o?0:-1,onClick:function(e){e.stopPropagation(),R(e)},onKeyDown:k,onMouseDown:w,onMouseUp:x,onFocus:$,onBlur:y},r&&e.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(C," of ").concat(_)),f&&e.createElement("span",{className:"".concat(E,"-icon")},f),c&&I),P&&e.createElement("button",{type:"button",role:"tab","aria-label":m||"remove",tabIndex:o?0:-1,className:"".concat(E,"-remove"),onClick:function(e){var t;e.stopPropagation(),(t=e).preventDefault(),t.stopPropagation(),h.onEdit("remove",{key:l,event:t})}},v||h.removeIcon||"×"));return b?b(T):T},te=function(e){var t=e.current||{},n=t.offsetWidth,a=void 0===n?0:n,o=t.offsetHeight,r=void 0===o?0:o;if(e.current){var i=e.current.getBoundingClientRect(),l=i.width,c=i.height;if(Math.abs(l-a)<1)return[l,c]}return[a,r]},ne=function(e,t){return e[t?0:1]},ae=e.forwardRef((function(t,l){var c=t.className,u=t.style,b=t.id,m=t.animated,h=t.activeKey,g=t.rtl,$=t.extra,y=t.editable,k=t.locale,w=t.tabPosition,x=t.tabBarGutter,S=t.children,_=t.onTabClick,C=t.onTabScroll,E=t.indicator,P=e.useContext(G),R=P.prefixCls,I=P.tabs,M=e.useRef(null),T=e.useRef(null),O=e.useRef(null),z=e.useRef(null),B=e.useRef(null),D=e.useRef(null),N=e.useRef(null),H="top"===w||"bottom"===w,j=W(0,(function(e,t){H&&C&&C({direction:e>t?"left":"right"})})),Q=a(j,2),ae=Q[0],oe=Q[1],re=W(0,(function(e,t){!H&&C&&C({direction:e>t?"top":"bottom"})})),ie=a(re,2),le=ie[0],ce=ie[1],se=e.useState([0,0]),de=a(se,2),ue=de[0],ve=de[1],fe=e.useState([0,0]),pe=a(fe,2),be=pe[0],me=pe[1],he=e.useState([0,0]),ge=a(he,2),$e=ge[0],ye=ge[1],ke=e.useState([0,0]),we=a(ke,2),xe=we[0],Se=we[1],_e=function(t){var n=e.useRef([]),o=e.useState({}),r=a(o,2)[1],i=e.useRef("function"==typeof t?t():t),l=q((function(){var e=i.current;n.current.forEach((function(t){e=t(e)})),n.current=[],i.current=e,r({})}));return[i.current,function(e){n.current.push(e),l()}]}(new Map),Ce=a(_e,2),Ee=Ce[0],Pe=Ce[1],Re=function(t,n,a){return e.useMemo((function(){for(var e,a=new Map,o=n.get(null===(e=t[0])||void 0===e?void 0:e.key)||A,r=o.left+o.width,l=0;l<t.length;l+=1){var c,s=t[l].key,d=n.get(s);d||(d=n.get(null===(c=t[l-1])||void 0===c?void 0:c.key)||A);var u=a.get(s)||i({},d);u.right=r-u.left-u.width,a.set(s,u)}return a}),[t.map((function(e){return e.key})).join("_"),n,a])}(I,Ee,be[0]),Ie=ne(ue,H),Me=ne(be,H),Te=ne($e,H),Le=ne(xe,H),Oe=Math.floor(Ie)<Math.floor(Me+Te),ze=Oe?Ie-Le:Ie-Te,Be="".concat(R,"-nav-operations-hidden"),De=0,Ne=0;function He(e){return e<De?De:e>Ne?Ne:e}H&&g?(De=0,Ne=Math.max(0,Me-ze)):(De=Math.min(0,ze-Me),Ne=0);var je=e.useRef(null),Ge=e.useState(),Ae=a(Ge,2),We=Ae[0],Xe=Ae[1];function qe(){Xe(Date.now())}function Ke(){je.current&&clearTimeout(je.current)}!function(t,n){var o=e.useState(),r=a(o,2),i=r[0],l=r[1],c=e.useState(0),s=a(c,2),d=s[0],u=s[1],v=e.useState(0),f=a(v,2),p=f[0],b=f[1],m=e.useState(),h=a(m,2),g=h[0],$=h[1],y=e.useRef(),k=e.useRef(),w=e.useRef(null);w.current={onTouchStart:function(e){var t=e.touches[0],n=t.screenX,a=t.screenY;l({x:n,y:a}),window.clearInterval(y.current)},onTouchMove:function(e){if(i){var t=e.touches[0],a=t.screenX,o=t.screenY;l({x:a,y:o});var r=a-i.x,c=o-i.y;n(r,c);var s=Date.now();u(s),b(s-d),$({x:r,y:c})}},onTouchEnd:function(){if(i&&(l(null),$(null),g)){var e=g.x/p,t=g.y/p,a=Math.abs(e),o=Math.abs(t);if(Math.max(a,o)<.1)return;var r=e,c=t;y.current=window.setInterval((function(){Math.abs(r)<.01&&Math.abs(c)<.01?window.clearInterval(y.current):n(20*(r*=X),20*(c*=X))}),20)}},onWheel:function(e){var t=e.deltaX,a=e.deltaY,o=0,r=Math.abs(t),i=Math.abs(a);r===i?o="x"===k.current?t:a:r>i?(o=t,k.current="x"):(o=a,k.current="y"),n(-o,-o)&&e.preventDefault()}},e.useEffect((function(){function e(e){w.current.onTouchMove(e)}function n(e){w.current.onTouchEnd(e)}return document.addEventListener("touchmove",e,{passive:!1}),document.addEventListener("touchend",n,{passive:!0}),t.current.addEventListener("touchstart",(function(e){w.current.onTouchStart(e)}),{passive:!0}),t.current.addEventListener("wheel",(function(e){w.current.onWheel(e)}),{passive:!1}),function(){document.removeEventListener("touchmove",e),document.removeEventListener("touchend",n)}}),[])}(z,(function(e,t){function n(e,t){e((function(e){return He(e+t)}))}return!!Oe&&(H?n(oe,e):n(ce,t),Ke(),qe(),!0)})),e.useEffect((function(){return Ke(),We&&(je.current=setTimeout((function(){Xe(0)}),100)),Ke}),[We]);var Fe=function(t,n,a,o,r,i,l){var c,s,d,u=l.tabs,v=l.tabPosition,f=l.rtl;return["top","bottom"].includes(v)?(c="width",s=f?"right":"left",d=Math.abs(a)):(c="height",s="top",d=-a),e.useMemo((function(){if(!u.length)return[0,0];for(var e=u.length,a=e,o=0;o<e;o+=1){var r=t.get(u[o].key)||K;if(Math.floor(r[s]+r[c])>Math.floor(d+n)){a=o-1;break}}for(var i=0,l=e-1;l>=0;l-=1)if((t.get(u[l].key)||K)[s]<d){i=l+1;break}return i>=a?[0,0]:[i,a]}),[t,n,o,r,i,d,v,u.map((function(e){return e.key})).join("_"),f])}(Re,ze,H?ae:le,Me,Te,Le,i(i({},t),{},{tabs:I})),Ve=a(Fe,2),Ye=Ve[0],Ue=Ve[1],Je=v((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:h,t=Re.get(e)||{width:0,height:0,left:0,right:0,top:0};if(H){var n=ae;g?t.right<ae?n=t.right:t.right+t.width>ae+ze&&(n=t.right+t.width-ze):t.left<-ae?n=-t.left:t.left+t.width>-ae+ze&&(n=-(t.left+t.width-ze)),ce(0),oe(He(n))}else{var a=le;t.top<-le?a=-t.top:t.top+t.height>-le+ze&&(a=-(t.top+t.height-ze)),oe(0),ce(He(a))}})),Qe=e.useState(),Ze=a(Qe,2),et=Ze[0],tt=Ze[1],nt=e.useState(!1),at=a(nt,2),ot=at[0],rt=at[1],it=I.filter((function(e){return!e.disabled})).map((function(e){return e.key})),lt=function(e){var t=it.indexOf(et||h),n=it.length,a=it[(t+e+n)%n];tt(a)},ct=function(e){var t=e.code,n=g&&H,a=it[0],o=it[it.length-1];switch(t){case"ArrowLeft":H&&lt(n?1:-1);break;case"ArrowRight":H&&lt(n?-1:1);break;case"ArrowUp":e.preventDefault(),H||lt(-1);break;case"ArrowDown":e.preventDefault(),H||lt(1);break;case"Home":e.preventDefault(),tt(a);break;case"End":e.preventDefault(),tt(o);break;case"Enter":case"Space":e.preventDefault(),_(null!=et?et:h,e);break;case"Backspace":case"Delete":var r=it.indexOf(et),i=I.find((function(e){return e.key===et}));Y(null==i?void 0:i.closable,null==i?void 0:i.closeIcon,y,null==i?void 0:i.disabled)&&(e.preventDefault(),e.stopPropagation(),y.onEdit("remove",{key:et,event:e}),r===it.length-1?lt(-1):lt(1))}},st={};H?st[g?"marginRight":"marginLeft"]=x:st.marginTop=x;var dt=I.map((function(t,n){var a=t.key;return e.createElement(ee,{id:b,prefixCls:R,key:a,tab:t,style:0===n?void 0:st,closable:t.closable,editable:y,active:a===h,focus:a===et,renderWrapper:S,removeAriaLabel:null==k?void 0:k.removeAriaLabel,tabCount:it.length,currentPosition:n+1,onClick:function(e){_(a,e)},onKeyDown:ct,onFocus:function(){ot||tt(a),Je(a),qe(),z.current&&(g||(z.current.scrollLeft=0),z.current.scrollTop=0)},onBlur:function(){tt(void 0)},onMouseDown:function(){rt(!0)},onMouseUp:function(){rt(!1)}})})),ut=function(){return Pe((function(){var e,t=new Map,n=null===(e=B.current)||void 0===e?void 0:e.getBoundingClientRect();return I.forEach((function(e){var o,r=e.key,i=null===(o=B.current)||void 0===o?void 0:o.querySelector('[data-node-key="'.concat(V(r),'"]'));if(i){var l=function(e,t){var n=e.offsetWidth,a=e.offsetHeight,o=e.offsetTop,r=e.offsetLeft,i=e.getBoundingClientRect(),l=i.width,c=i.height,s=i.left,d=i.top;return Math.abs(l-n)<1?[l,c,s-t.left,d-t.top]:[n,a,r,o]}(i,n),c=a(l,4),s=c[0],d=c[1],u=c[2],v=c[3];t.set(r,{width:s,height:d,left:u,top:v})}})),t}))};e.useEffect((function(){ut()}),[I.map((function(e){return e.key})).join("_")]);var vt=q((function(){var e=te(M),t=te(T),n=te(O);ve([e[0]-t[0]-n[0],e[1]-t[1]-n[1]]);var a=te(N);ye(a);var o=te(D);Se(o);var r=te(B);me([r[0]-a[0],r[1]-a[1]]),ut()})),ft=I.slice(0,Ye),pt=I.slice(Ue+1),bt=[].concat(f(ft),f(pt)),mt=Re.get(h),ht=function(t){var n=t.activeTabOffset,i=t.horizontal,l=t.rtl,c=t.indicator,s=void 0===c?{}:c,d=s.size,u=s.align,v=void 0===u?"center":u,f=e.useState(),p=a(f,2),b=p[0],m=p[1],h=e.useRef(),g=o.useCallback((function(e){return"function"==typeof d?d(e):"number"==typeof d?d:e}),[d]);function $(){r.cancel(h.current)}return e.useEffect((function(){var e={};if(n)if(i){e.width=g(n.width);var t=l?"right":"left";"start"===v&&(e[t]=n[t]),"center"===v&&(e[t]=n[t]+n.width/2,e.transform=l?"translateX(50%)":"translateX(-50%)"),"end"===v&&(e[t]=n[t]+n.width,e.transform="translateX(-100%)")}else e.height=g(n.height),"start"===v&&(e.top=n.top),"center"===v&&(e.top=n.top+n.height/2,e.transform="translateY(-50%)"),"end"===v&&(e.top=n.top+n.height,e.transform="translateY(-100%)");return $(),h.current=r((function(){b&&e&&Object.keys(e).every((function(t){var n=e[t],a=b[t];return"number"==typeof n&&"number"==typeof a?Math.round(n)===Math.round(a):n===a}))||m(e)})),$}),[JSON.stringify(n),i,l,v,g]),{style:b}}({activeTabOffset:mt,horizontal:H,indicator:E,rtl:g}),gt=ht.style;e.useEffect((function(){Je()}),[h,De,Ne,F(mt),F(Re),H]),e.useEffect((function(){vt()}),[g]);var $t,yt,kt,wt,xt=!!bt.length,St="".concat(R,"-nav-wrap");return H?g?(yt=ae>0,$t=ae!==Ne):($t=ae<0,yt=ae!==De):(kt=le<0,wt=le!==De),e.createElement(L,{onResize:vt},e.createElement("div",{ref:p(l,M),role:"tablist","aria-orientation":H?"horizontal":"vertical",className:d("".concat(R,"-nav"),c),style:u,onKeyDown:function(){qe()}},e.createElement(J,{ref:T,position:"left",extra:$,prefixCls:R}),e.createElement(L,{onResize:vt},e.createElement("div",{className:d(St,s(s(s(s({},"".concat(St,"-ping-left"),$t),"".concat(St,"-ping-right"),yt),"".concat(St,"-ping-top"),kt),"".concat(St,"-ping-bottom"),wt)),ref:z},e.createElement(L,{onResize:vt},e.createElement("div",{ref:B,className:"".concat(R,"-nav-list"),style:{transform:"translate(".concat(ae,"px, ").concat(le,"px)"),transition:We?"none":void 0}},dt,e.createElement(U,{ref:N,prefixCls:R,locale:k,editable:y,style:i(i({},0===dt.length?void 0:st),{},{visibility:xt?"hidden":null})}),e.createElement("div",{className:d("".concat(R,"-ink-bar"),s({},"".concat(R,"-ink-bar-animated"),m.inkBar)),style:gt}))))),e.createElement(Z,n({},t,{removeAriaLabel:null==k?void 0:k.removeAriaLabel,ref:D,prefixCls:R,tabs:bt,className:!xt&&Be,tabMoving:!!We})),e.createElement(J,{ref:O,position:"right",extra:$,prefixCls:R})))})),oe=e.forwardRef((function(t,n){var a=t.prefixCls,o=t.className,r=t.style,i=t.id,l=t.active,c=t.tabKey,s=t.children;return e.createElement("div",{id:i&&"".concat(i,"-panel-").concat(c),role:"tabpanel",tabIndex:l?0:-1,"aria-labelledby":i&&"".concat(i,"-tab-").concat(c),"aria-hidden":!l,style:r,className:d(a,l&&"".concat(a,"-active"),o),ref:n},s)})),re=["renderTabBar"],ie=["label","key"],le=function(t){var a=t.renderTabBar,o=b(t,re),r=e.useContext(G).tabs;return a?a(i(i({},o),{},{panes:r.map((function(t){var a=t.label,o=t.key,r=b(t,ie);return e.createElement(oe,n({tab:a,key:o,tabKey:o},r))}))}),ae):e.createElement(ae,o)},ce=["key","forceRender","style","className","destroyInactiveTabPane"],se=function(t){var a=t.id,o=t.activeKey,r=t.animated,l=t.tabPosition,c=t.destroyInactiveTabPane,u=e.useContext(G),v=u.prefixCls,f=u.tabs,p=r.tabPane,h="".concat(v,"-tabpane");return e.createElement("div",{className:d("".concat(v,"-content-holder"))},e.createElement("div",{className:d("".concat(v,"-content"),"".concat(v,"-content-").concat(l),s({},"".concat(v,"-content-animated"),p))},f.map((function(t){var l=t.key,s=t.forceRender,u=t.style,v=t.className,f=t.destroyInactiveTabPane,g=b(t,ce),$=l===o;return e.createElement(m,n({key:l,visible:$,forceRender:s,removeOnLeave:!(!c&&!f),leavedClassName:"".concat(h,"-hidden")},r.tabPaneMotion),(function(t,o){var r=t.style,c=t.className;return e.createElement(oe,n({},g,{prefixCls:h,id:a,tabKey:l,animated:p,active:$,style:i(i({},u),r),className:d(v,c),ref:o}))}))}))))};var de=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],ue=0,ve=e.forwardRef((function(t,o){var r=t.id,l=t.prefixCls,u=void 0===l?"rc-tabs":l,v=t.className,f=t.items,p=t.direction,m=t.activeKey,g=t.defaultActiveKey,$=t.editable,y=t.animated,k=t.tabPosition,w=void 0===k?"top":k,x=t.tabBarGutter,S=t.tabBarStyle,_=t.tabBarExtraContent,C=t.locale,E=t.more,P=t.destroyInactiveTabPane,R=t.renderTabBar,I=t.onChange,M=t.onTabClick,T=t.onTabScroll,L=t.getPopupContainer,z=t.popupClassName,B=t.indicator,D=b(t,de),N=e.useMemo((function(){return(f||[]).filter((function(e){return e&&"object"===c(e)&&"key"in e}))}),[f]),H="rtl"===p,j=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{inkBar:!0,tabPane:!1};return(e=!1===t?{inkBar:!1,tabPane:!1}:!0===t?{inkBar:!0,tabPane:!1}:i({inkBar:!0},"object"===c(t)?t:{})).tabPaneMotion&&void 0===e.tabPane&&(e.tabPane=!0),!e.tabPaneMotion&&e.tabPane&&(e.tabPane=!1),e}(y),A=e.useState(!1),W=a(A,2),X=W[0],q=W[1];e.useEffect((function(){q(O())}),[]);var K=h((function(){var e;return null===(e=N[0])||void 0===e?void 0:e.key}),{value:m,defaultValue:g}),F=a(K,2),V=F[0],Y=F[1],U=e.useState((function(){return N.findIndex((function(e){return e.key===V}))})),J=a(U,2),Q=J[0],Z=J[1];e.useEffect((function(){var e,t=N.findIndex((function(e){return e.key===V}));-1===t&&(t=Math.max(0,Math.min(Q,N.length-1)),Y(null===(e=N[t])||void 0===e?void 0:e.key));Z(t)}),[N.map((function(e){return e.key})).join("_"),V,Q]);var ee=h(null,{value:r}),te=a(ee,2),ne=te[0],ae=te[1];e.useEffect((function(){r||(ae("rc-tabs-".concat(ue)),ue+=1)}),[]);var oe={id:ne,activeKey:V,animated:j,tabPosition:w,rtl:H,mobile:X},re=i(i({},oe),{},{editable:$,locale:C,more:E,tabBarGutter:x,onTabClick:function(e,t){null==M||M(e,t);var n=e!==V;Y(e),n&&(null==I||I(e))},onTabScroll:T,extra:_,style:S,panes:null,getPopupContainer:L,popupClassName:z,indicator:B});return e.createElement(G.Provider,{value:{tabs:N,prefixCls:u}},e.createElement("div",n({ref:o,id:r,className:d(u,"".concat(u,"-").concat(w),s(s(s({},"".concat(u,"-mobile"),X),"".concat(u,"-editable"),$),"".concat(u,"-rtl"),H),v)},D),e.createElement(le,n({},re,{renderTabBar:R})),e.createElement(se,n({destroyInactiveTabPane:P},oe,{animated:j}))))}));const fe={motionAppear:!1,motionEnter:!0,motionLeave:!0};function pe(t,n){if(t)return t.map((e=>{var t;const n=null!==(t=e.destroyOnHidden)&&void 0!==t?t:e.destroyInactiveTabPane;return Object.assign(Object.assign({},e),{destroyInactiveTabPane:n})}));return function(e){return e.filter((e=>e))}($(n).map((t=>{if(e.isValidElement(t)){const{key:e,props:n}=t,a=n||{},{tab:o}=a,r=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n}(a,["tab"]);return Object.assign(Object.assign({key:String(e)},r),{label:o})}return null})))}const be=e=>{const{componentCls:t,motionDurationSlow:n}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${n}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${n}`}}}}},[z(e,"slide-up"),z(e,"slide-down")]]},me=e=>{const{componentCls:t,tabsCardPadding:n,cardBg:a,cardGutter:o,colorBorderSecondary:r,itemSelectedColor:i}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:n,background:a,border:`${w(e.lineWidth)} ${e.lineType} ${r}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:i,background:e.colorBgContainer},[`${t}-tab-focus:has(${t}-tab-btn:focus-visible)`]:_(e,-3),[`& ${t}-tab${t}-tab-focus ${t}-tab-btn:focus-visible`]:{outline:"none"},[`${t}-ink-bar`]:{visibility:"hidden"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:w(o)}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${w(e.borderRadiusLG)} ${w(e.borderRadiusLG)} 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${w(e.borderRadiusLG)} ${w(e.borderRadiusLG)}`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:w(o)}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${w(e.borderRadiusLG)} 0 0 ${w(e.borderRadiusLG)}`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${w(e.borderRadiusLG)} ${w(e.borderRadiusLG)} 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},he=e=>{const{componentCls:t,itemHoverColor:n,dropdownEdgeChildVerticalPadding:a}=e;return{[`${t}-dropdown`]:Object.assign(Object.assign({},x(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${w(a)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},S),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${w(e.paddingXXS)} ${w(e.paddingSM)}`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},ge=e=>{const{componentCls:t,margin:n,colorBorderSecondary:a,horizontalMargin:o,verticalItemPadding:r,verticalItemMargin:i,calc:l}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:o,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${w(e.lineWidth)} ${e.lineType} ${a}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},\n            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,\n        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:l(e.controlHeight).mul(1.25).equal(),[`${t}-tab`]:{padding:r,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:i},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:w(l(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${w(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:l(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${w(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},$e=e=>{const{componentCls:t,cardPaddingSM:n,cardPaddingLG:a,cardHeightSM:o,cardHeightLG:r,horizontalItemPaddingSM:i,horizontalItemPaddingLG:l}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:i,fontSize:e.titleFontSizeSM}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:l,fontSize:e.titleFontSizeLG,lineHeight:e.lineHeightLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:n},[`${t}-nav-add`]:{minWidth:o,minHeight:o}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${w(e.borderRadius)} ${w(e.borderRadius)}`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${w(e.borderRadius)} ${w(e.borderRadius)} 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${w(e.borderRadius)} ${w(e.borderRadius)} 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${w(e.borderRadius)} 0 0 ${w(e.borderRadius)}`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:a},[`${t}-nav-add`]:{minWidth:r,minHeight:r}}}}}},ye=e=>{const{componentCls:t,itemActiveColor:n,itemHoverColor:a,iconCls:o,tabsHorizontalItemMargin:r,horizontalItemPadding:i,itemSelectedColor:l,itemColor:c}=e,s=`${t}-tab`;return{[s]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:i,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:c,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:n}},"&-btn":{outline:"none",transition:`all ${e.motionDurationSlow}`,[`${s}-icon:not(:last-child)`]:{marginInlineEnd:e.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},C(e)),"&:hover":{color:a},[`&${s}-active ${s}-btn`]:{color:l,textShadow:e.tabsActiveTextShadow},[`&${s}-focus ${s}-btn:focus-visible`]:_(e),[`&${s}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${s}-disabled ${s}-btn, &${s}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${s}-remove ${o}`]:{margin:0},[`${o}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${s} + ${s}`]:{margin:{_skip_check_:!0,value:r}}}},ke=e=>{const{componentCls:t,tabsHorizontalItemMarginRTL:n,iconCls:a,cardGutter:o,calc:r}=e,i=`${t}-rtl`;return{[i]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:n},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[a]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:w(e.marginSM)}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:w(e.marginXS)},marginLeft:{_skip_check_:!0,value:w(r(e.marginXXS).mul(-1).equal())},[a]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:o},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},we=e=>{const{componentCls:t,tabsCardPadding:n,cardHeight:a,cardGutter:o,itemHoverColor:r,itemActiveColor:i,colorBorderSecondary:l}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},x(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:n,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:Object.assign({minWidth:a,minHeight:a,marginLeft:{_skip_check_:!0,value:o},background:"transparent",border:`${w(e.lineWidth)} ${e.lineType} ${l}`,borderRadius:`${w(e.borderRadiusLG)} ${w(e.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:r},"&:active, &:focus:not(:focus-visible)":{color:i}},C(e,-3))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),ye(e)),{[`${t}-content`]:{position:"relative",width:"100%"},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:Object.assign(Object.assign({},C(e)),{"&-hidden":{display:"none"}})}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping']) > ${t}-nav-list`]:{margin:"auto"}}}}}},xe=y("Tabs",(e=>{const t=k(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${w(e.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${w(e.horizontalItemGutter)}`});return[$e(t),ke(t),ge(t),he(t),me(t),we(t),be(t)]}),(e=>{const{cardHeight:t,cardHeightSM:n,cardHeightLG:a,controlHeight:o,controlHeightLG:r}=e,i=t||r,l=n||o,c=a||r+8;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:i,cardHeightSM:l,cardHeightLG:c,cardPadding:`${(i-e.fontHeight)/2-e.lineWidth}px ${e.padding}px`,cardPaddingSM:`${(l-e.fontHeight)/2-e.lineWidth}px ${e.paddingXS}px`,cardPaddingLG:`${(c-e.fontHeightLG)/2-e.lineWidth}px ${e.padding}px`,titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:`0 0 ${e.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${e.paddingSM}px 0`,horizontalItemPaddingSM:`${e.paddingXS}px 0`,horizontalItemPaddingLG:`${e.padding}px 0`,verticalItemPadding:`${e.paddingXS}px ${e.paddingLG}px`,verticalItemMargin:`${e.margin}px 0 0 0`,itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}}));const Se=t=>{var n,a,o,r,i,l,c,s,u,v,f;const{type:p,className:b,rootClassName:m,size:h,onEdit:$,hideAdd:y,centered:k,addIcon:w,removeIcon:x,moreIcon:S,more:_,popupClassName:C,children:M,items:T,animated:L,style:O,indicatorSize:z,indicator:D,destroyInactiveTabPane:N,destroyOnHidden:H}=t,G=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n}(t,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator","destroyInactiveTabPane","destroyOnHidden"]),{prefixCls:A}=G,{direction:W,tabs:X,getPrefixCls:q,getPopupContainer:K}=e.useContext(E),F=q("tabs",A),V=P(F),[Y,U,J]=xe(F,V);let Q;"editable-card"===p&&(Q={onEdit:(e,{key:t,event:n})=>{null==$||$("add"===e?n:t,e)},removeIcon:null!==(n=null!=x?x:null==X?void 0:X.removeIcon)&&void 0!==n?n:e.createElement(R,null),addIcon:(null!=w?w:null==X?void 0:X.addIcon)||e.createElement(j,null),showAdd:!0!==y});const Z=q(),ee=I(h),te=pe(T,M),ne=function(e,t={inkBar:!0,tabPane:!1}){let n;return n=!1===t?{inkBar:!1,tabPane:!1}:!0===t?{inkBar:!0,tabPane:!0}:Object.assign({inkBar:!0},"object"==typeof t?t:{}),n.tabPane&&(n.tabPaneMotion=Object.assign(Object.assign({},fe),{motionName:g(e,"switch")})),n}(F,L),ae=Object.assign(Object.assign({},null==X?void 0:X.style),O),oe={align:null!==(a=null==D?void 0:D.align)&&void 0!==a?a:null===(o=null==X?void 0:X.indicator)||void 0===o?void 0:o.align,size:null!==(c=null!==(i=null!==(r=null==D?void 0:D.size)&&void 0!==r?r:z)&&void 0!==i?i:null===(l=null==X?void 0:X.indicator)||void 0===l?void 0:l.size)&&void 0!==c?c:null==X?void 0:X.indicatorSize};return Y(e.createElement(ve,Object.assign({direction:W,getPopupContainer:K},G,{items:te,className:d({[`${F}-${ee}`]:ee,[`${F}-card`]:["card","editable-card"].includes(p),[`${F}-editable-card`]:"editable-card"===p,[`${F}-centered`]:k},null==X?void 0:X.className,b,m,U,J,V),popupClassName:d(C,U,J,V),style:ae,editable:Q,more:Object.assign({icon:null!==(f=null!==(v=null!==(u=null===(s=null==X?void 0:X.more)||void 0===s?void 0:s.icon)&&void 0!==u?u:null==X?void 0:X.moreIcon)&&void 0!==v?v:S)&&void 0!==f?f:e.createElement(B,null),transitionName:`${Z}-slide-up`},_),prefixCls:F,animated:ne,indicator:oe,destroyInactiveTabPane:null!=H?H:N})))};Se.TabPane=()=>null;export{Se as T};
