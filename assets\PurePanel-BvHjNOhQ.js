import{r as e,aA as t,T as s,G as n}from"./index-Cak6rALw.js";function r(s){return n=>e.createElement(t,{theme:{token:{motion:!1,zIndexPopupBase:0}}},e.createElement(s,Object.assign({},n)))}const a=(t,a,o,i,c)=>r((r=>{const{prefixCls:l,style:u}=r,d=e.useRef(null),[f,g]=e.useState(0),[v,p]=e.useState(0),[b,m]=s(!1,{value:r.open}),{getPrefixCls:j}=e.useContext(n),O=j(i||"select",l);e.useEffect((()=>{if(m(!0),"undefined"!=typeof ResizeObserver){const e=new ResizeObserver((e=>{const t=e[0].target;g(t.offsetHeight+8),p(t.offsetWidth)})),t=setInterval((()=>{var s;const n=c?`.${c(O)}`:`.${O}-dropdown`,r=null===(s=d.current)||void 0===s?void 0:s.querySelector(n);r&&(clearInterval(t),e.observe(r))}),10);return()=>{clearInterval(t),e.disconnect()}}}),[]);let x=Object.assign(Object.assign({},r),{style:Object.assign(Object.assign({},u),{margin:0}),open:b,visible:b,getPopupContainer:()=>d.current});o&&(x=o(x)),a&&Object.assign(x,{[a]:{overflow:{adjustX:!1,adjustY:!1}}});const y={paddingBottom:f,position:"relative",minWidth:v};return e.createElement("div",{ref:d,style:y},e.createElement(t,Object.assign({},x)))}));export{a as g,r as w};
