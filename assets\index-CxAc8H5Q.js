import{L as o,M as r,O as e,J as n,a9 as i}from"./index-Cak6rALw.js";const a=o=>{const{checkboxCls:r}=o,a=`${r}-wrapper`;return[{[`${r}-group`]:Object.assign(Object.assign({},e(o)),{display:"inline-flex",flexWrap:"wrap",columnGap:o.marginXS,[`> ${o.antCls}-row`]:{flex:1}}),[a]:Object.assign(Object.assign({},e(o)),{display:"inline-flex",alignItems:"baseline",cursor:"pointer","&:after":{display:"inline-block",width:0,overflow:"hidden",content:"'\\a0'"},[`& + ${a}`]:{marginInlineStart:0},[`&${a}-in-form-item`]:{'input[type="checkbox"]':{width:14,height:14}}}),[r]:Object.assign(Object.assign({},e(o)),{position:"relative",whiteSpace:"nowrap",lineHeight:1,cursor:"pointer",borderRadius:o.borderRadiusSM,alignSelf:"center",[`${r}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0,margin:0,[`&:focus-visible + ${r}-inner`]:Object.assign({},i(o))},[`${r}-inner`]:{boxSizing:"border-box",display:"block",width:o.checkboxSize,height:o.checkboxSize,direction:"ltr",backgroundColor:o.colorBgContainer,border:`${n(o.lineWidth)} ${o.lineType} ${o.colorBorder}`,borderRadius:o.borderRadiusSM,borderCollapse:"separate",transition:`all ${o.motionDurationSlow}`,"&:after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"25%",display:"table",width:o.calc(o.checkboxSize).div(14).mul(5).equal(),height:o.calc(o.checkboxSize).div(14).mul(8).equal(),border:`${n(o.lineWidthBold)} solid ${o.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${o.motionDurationFast} ${o.motionEaseInBack}, opacity ${o.motionDurationFast}`}},"& + span":{paddingInlineStart:o.paddingXS,paddingInlineEnd:o.paddingXS}})},{[`\n        ${a}:not(${a}-disabled),\n        ${r}:not(${r}-disabled)\n      `]:{[`&:hover ${r}-inner`]:{borderColor:o.colorPrimary}},[`${a}:not(${a}-disabled)`]:{[`&:hover ${r}-checked:not(${r}-disabled) ${r}-inner`]:{backgroundColor:o.colorPrimaryHover,borderColor:"transparent"},[`&:hover ${r}-checked:not(${r}-disabled):after`]:{borderColor:o.colorPrimaryHover}}},{[`${r}-checked`]:{[`${r}-inner`]:{backgroundColor:o.colorPrimary,borderColor:o.colorPrimary,"&:after":{opacity:1,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`all ${o.motionDurationMid} ${o.motionEaseOutBack} ${o.motionDurationFast}`}}},[`\n        ${a}-checked:not(${a}-disabled),\n        ${r}-checked:not(${r}-disabled)\n      `]:{[`&:hover ${r}-inner`]:{backgroundColor:o.colorPrimaryHover,borderColor:"transparent"}}},{[r]:{"&-indeterminate":{"&":{[`${r}-inner`]:{backgroundColor:`${o.colorBgContainer}`,borderColor:`${o.colorBorder}`,"&:after":{top:"50%",insetInlineStart:"50%",width:o.calc(o.fontSizeLG).div(2).equal(),height:o.calc(o.fontSizeLG).div(2).equal(),backgroundColor:o.colorPrimary,border:0,transform:"translate(-50%, -50%) scale(1)",opacity:1,content:'""'}},[`&:hover ${r}-inner`]:{backgroundColor:`${o.colorBgContainer}`,borderColor:`${o.colorPrimary}`}}}}},{[`${a}-disabled`]:{cursor:"not-allowed"},[`${r}-disabled`]:{[`&, ${r}-input`]:{cursor:"not-allowed",pointerEvents:"none"},[`${r}-inner`]:{background:o.colorBgContainerDisabled,borderColor:o.colorBorder,"&:after":{borderColor:o.colorTextDisabled}},"&:after":{display:"none"},"& + span":{color:o.colorTextDisabled},[`&${r}-indeterminate ${r}-inner::after`]:{background:o.colorTextDisabled}}}]};function t(o,e){const n=r(e,{checkboxCls:`.${o}`,checkboxSize:e.controlInteractiveSize});return[a(n)]}const l=o("Checkbox",((o,{prefixCls:r})=>[t(r,o)]));export{t as g,l as u};
