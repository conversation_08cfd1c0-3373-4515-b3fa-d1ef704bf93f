const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./ImageLazyPreview-D93UqUk2.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./useCacheState-CAnxkewm.js","./MyApp-DW5WH4Ub.js","./index-CDSnY0KE.js","./EyeOutlined-CNKPohhw.js","./addEventListener-C4tIKh7N.js","./VideoViewer-DSZVlhxl.js","./videos-D2LbKcXH.js","./photos-CDfkOCs1.js"])))=>i.map(i=>d[i]);
import{b0 as e,b1 as t,aN as i}from"./index-Cak6rALw.js";import{CommentAttachmentType as r}from"./comments-BafLCvtq.js";import{B as o,I as s}from"./MyApp-DW5WH4Ub.js";import{I as a}from"./index-CDSnY0KE.js";import"./videos-D2LbKcXH.js";import"./EyeOutlined-CNKPohhw.js";import"./addEventListener-C4tIKh7N.js";const m=t((()=>i((()=>import("./ImageLazyPreview-D93UqUk2.js")),__vite__mapDeps([0,1,2,3,4,5,6,7]),import.meta.url)),{fallback:s}),d=t((()=>i((()=>import("./VideoViewer-DSZVlhxl.js")),__vite__mapDeps([8,1,2,9,4]),import.meta.url)),{fallback:s});function n({attachment:t,maxWidth:s=200,maxHeight:n=200}){switch(t.type){case r.PHOTO:return e.jsx(o.Ribbon,{text:"Photo",children:e.jsx(m,{src:t.image,style:{objectFit:"cover",borderRadius:10,maxWidth:s,maxHeight:n},cacheId:"photos."+t.id,getPreview:()=>i((()=>import("./photos-CDfkOCs1.js")),__vite__mapDeps([10,4,1,2]),import.meta.url).then((e=>e.getLargestPhoto(t.id).then((e=>e.image))))})});case r.VIDEO:return e.jsx(o.Ribbon,{text:"Video",children:e.jsx(a,{src:t.image,style:{objectFit:"cover",borderRadius:10,maxWidth:s,maxHeight:n},preview:{destroyOnClose:!0,imageRender:()=>e.jsx(d,{info:t.video,style:{maxWidth:"90vw",maxHeight:"90vh"}}),toolbarRender:()=>null}})});case r.STICKER:return e.jsx(o.Ribbon,{text:"Sticker",children:e.jsx(a,{src:t.image,style:{objectFit:"cover",borderRadius:10,maxWidth:s,maxHeight:n}})});case r.GIF:return e.jsx(o.Ribbon,{text:"GIF",children:e.jsx(a,{src:t.image,style:{objectFit:"cover",borderRadius:10,maxWidth:s,maxHeight:n}})})}return null}export{n as default};
