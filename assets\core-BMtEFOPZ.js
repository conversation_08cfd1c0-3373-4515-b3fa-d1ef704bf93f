const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./window-pPC-ycGO.js","./MyApp-DW5WH4Ub.js","./index-Cak6rALw.js","./index-DIX6R8z-.css"])))=>i.map(i=>d[i]);
import{aN as n}from"./index-Cak6rALw.js";async function e(e){var o;const r=null==(o=/\/(\d+)\//.exec(e))?void 0:o[1];if(!r)throw new Error("Invalid Scribd URL: cannot find document ID");const t=`https://www.scribd.com/embeds/${r}/content`,{runFnInWin:a,waitWinForReady:s}=await n((async()=>{const{runFnInWin:n,waitWinForReady:e}=await import("./window-pPC-ycGO.js");return{runFnInWin:n,waitWinForReady:e}}),__vite__mapDeps([0,1,2,3]),import.meta.url),i=window.open(t,"_blank");await s(i),await a({win:i,fnPath:"window.eval",params:["(() => {\n        let isFirst = true;\n\n        if(window.document.readyState === 'complete') cheat();\n        else window.addEventListener('load', cheat);\n\n        async function cheat() {\n            await scrollToAllPage();\n            removeUI();\n        }\n\n        function removeUI() {\n            try {\n                ['document_scroller'].forEach(c => {\n                    document.querySelectorAll('.' + c).forEach(el => {\n                        el.classList.remove(c)\n                    })\n                });\n                ['.mobile_overlay', '.toolbar_drop'].forEach(c => {\n                    document.querySelector(c)?.remove();\n                });\n                if(isFirst) {\n                    alert('FB AIO: Done, Press Ctrl+P to print');\n                    window.print();\n                }\n            } catch (e) {\n                if(isFirst) alert('FB AIO: Error:'+ e);\n            } finally {\n                isFirst = false;\n            }\n        }\n\n        async function scrollToAllPage() {\n            await sleep(1000);\n            const scrolledPages = [];\n            const pages = Array.from(document.querySelectorAll('.newpage'));\n            while(pages.length) {\n                const page = pages.shift();\n                scrolledPages.push(page);\n                page.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'center'\n                });\n                await sleep(500);\n\n                const newPages = Array.from(document.querySelectorAll('.newpage'));\n                for(const newPage of newPages) {\n                    if(!pages.includes(newPage) && !scrolledPages.includes(newPage))\n                        pages.push(newPage);\n                }\n            }\n        }\n\n        async function sleep(ms) {\n            return new Promise(resolve => setTimeout(resolve, ms));\n        }\n    })()"]})}export{e as downloadScribdDocument};
