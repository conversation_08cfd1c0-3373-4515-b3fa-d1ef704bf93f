import{aJ as e}from"./index-Cak6rALw.js";function t(e,t){for(var o=0;o<t.length;o++){const n=t[o];if("string"!=typeof n&&!Array.isArray(n))for(const t in n)if("default"!==t&&!(t in e)){const o=Object.getOwnPropertyDescriptor(n,t);o&&Object.defineProperty(e,t,o.get?o:{enumerable:!0,get:()=>n[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var o,n;var r=n?o:(n=1,o=function(e,t,o,n){var r=new Blob(void 0!==n?[n,e]:[e],{type:o||"application/octet-stream"});if(void 0!==window.navigator.msSaveBlob)window.navigator.msSaveBlob(r,t);else{var i=window.URL&&window.URL.createObjectURL?window.URL.createObjectURL(r):window.webkitURL.createObjectURL(r),a=document.createElement("a");a.style.display="none",a.href=i,a.setAttribute("download",t),void 0===a.download&&a.setAttribute("target","_blank"),document.body.appendChild(a),a.click(),setTimeout((function(){document.body.removeChild(a),window.URL.revokeObjectURL(i)}),200)}});const i=e(r),a=t({__proto__:null,default:i},[r]);export{i as a,a as f};
