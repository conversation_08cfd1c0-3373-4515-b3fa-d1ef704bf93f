const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./VideoViewer-DSZVlhxl.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./videos-D2LbKcXH.js","./MyApp-DW5WH4Ub.js","./Media-Bivrw8q1.js","./posts-Fn0UXLRN.js","./useCacheState-CAnxkewm.js","./reactions-CR2nCZPF.js","./download-KJKGHAGZ.js","./Table-BO80-bCE.js","./addEventListener-C4tIKh7N.js","./List-B6ZMXgC_.js","./DownOutlined-B-JcS-29.js","./index-D6X7PZwe.js","./index-CxAc8H5Q.js","./index-QU7lSj_a.js","./index-Dg3cFar0.js","./Dropdown-BcL-JHCf.js","./PurePanel-BvHjNOhQ.js","./move-ESLFEEsv.js","./index-C5gzSBcY.js","./index-SRy1SMeO.js","./SearchOutlined--mXbzQ68.js","./useBreakpoint-CPcdMRfj.js","./Pagination-2X6SDgot.js","./index-Bg865k-U.js","./EyeOutlined-CNKPohhw.js","./index-DdM4T8-Q.js","./index-PbLYNhdQ.js","./row-BMfM-of4.js","./index-CDSnY0KE.js","./index-jrOnBmdW.js","./col-CzA09p0P.js","./index-CC5caqt_.js","./index-BzMPigdO.js","./index-DRXkAFwH.js"])))=>i.map(i=>d[i]);
import{bc as e,r as t,b0 as i,b1 as n,b5 as s,bk as o,bi as a,aN as l,bj as r}from"./index-Cak6rALw.js";import{PrivacyScope as c,PrivacyScopeName as d,getGroupPosts as h,getPosts as v,deletePost as u,trashPost as g,setPostPrivacy as m,unTagPost as p,findAllPostsOf as x,findFirstPost as y}from"./posts-Fn0UXLRN.js";import{d as f}from"./dayjs.min-CzPn7FMI.js";import{u as j,d as b,a as P,z as k,t as T,v as C,aG as w,S as D,i as F,h as _,s as I,B as S,c as N,l as R,e as E,f as L,b as O,g as B,T as M,j as U,W,I as A}from"./MyApp-DW5WH4Ub.js";import V from"./useCacheState-CAnxkewm.js";import q from"./MyTable-DnjDmN7z.js";import{E as G}from"./ExportButton-CbyepAf_.js";import{postIdFromBase64Id as z,getPostIdFromUrl as H,getUidFromUrl as K}from"./getIds-DAzc-wzf.js";import{checkVIP as X}from"./useVIP-D5FAMGQQ.js";import{u as $}from"./useAction-uz-MrZwJ.js";import{B as Q}from"./BadgeWrapper-BCbYXXfB.js";import{u as Y}from"./useDevMode-yj0U7_8D.js";import{S as J}from"./Screen-Buq7HJpK.js";import{R as Z,g as ee}from"./reactions-CR2nCZPF.js";import{A as te}from"./index-DdM4T8-Q.js";import{I as ie}from"./index-CDSnY0KE.js";import{C as ne}from"./index-D7dTP8lW.js";import{S as se}from"./index-C5gzSBcY.js";import{P as oe}from"./index-PbLYNhdQ.js";import{I as ae}from"./index-Bg865k-U.js";import{T as le}from"./index-BzMPigdO.js";import{D as re}from"./index-DwLJmsHL.js";import{R as ce}from"./row-BMfM-of4.js";import{M as de}from"./index-Bx3o6rwA.js";import"./videos-D2LbKcXH.js";import"./index-ByRdMNW-.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./DownOutlined-B-JcS-29.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./EyeOutlined-CNKPohhw.js";import"./sweetalert2.esm.all-BZxvatOx.js";import"./index-IxNfZAD-.js";import"./index-CC5caqt_.js";import"./col-CzA09p0P.js";import"./ClockCircleOutlined-BVovxNRp.js";const he=n((()=>l((()=>import("./VideoViewer-DSZVlhxl.js")),__vite__mapDeps([0,1,2,3,4]),import.meta.url)),{fallback:A}),ve=n((()=>l((()=>import("./Media-Bivrw8q1.js").then((e=>e.M))),__vite__mapDeps([5,1,2,4,6,3,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36]),import.meta.url)),{fallback:A}),ue=[c.EVERYONE,c.FRIENDS,c.FRIENDS_OF_FRIENDS,c.SELF],ge=new Set([c.EVERYONE,c.CUSTOM,c.FRIENDS_OF_FRIENDS]),me=Object.keys(c).filter((e=>!ge.has(e))),pe=e=>{var t,i,n,s;return R(e.title||e.summary||e.message||(null==(t=e.content)?void 0:t.text)||(null==(s=null==(n=null==(i=e.content)?void 0:i.attachments)?void 0:n.find((e=>e.title)))?void 0:s.title),30)||L(e.creation_time)},xe={uid:"",privacy:c.ALL,omitPinnedPost:!1,beforeTime:null,taggedInOnly:!1,postedByOthers:null};function ye({target:n}){var l,A;const{ti:ge}=j(),ye=e(),{message:fe,notification:je}=b(),{devMode:be}=Y(),{onClickAction:Pe,onClickBulkActions:ke}=$(),[Te,Ce]=V("Posts.showFilters",!1),{profile:we}=P(),De=(null==n?void 0:n.id)||"",[Fe,_e]=V("Posts.postFilters",xe),[Ie,Se]=V("Posts.autoLoadReactions",!1),[Ne,Re]=V("Posts.maxPosts."+De,9),[Ee,Le]=V("Posts.uid."+De,De),[Oe,Be]=V("Posts.profile."+De,n),[Me,Ue]=V("Posts.posts."+De,[]),[We,Ae]=V("Posts.reactions."+De,{}),[Ve,qe]=V("Posts.loading."+De,!1),[Ge,ze]=V("Posts.loadingFirstPost."+De,!1),[He,Ke]=V("Posts.loadingReactions."+De,!1),[Xe,$e]=t.useState(null),Qe=(null==Oe?void 0:Oe.type)===k.Group,Ye=t.useMemo((()=>Me.filter(Boolean).map(((e,t)=>{var i,n;return{...e,recent:t,reactions:We[e.id]||null,totalReact:(null==(n=null==(i=We[e.id])?void 0:i.reduce)?void 0:n.call(i,((e,t)=>e+t.count),0))||0}}))),[Me,We]);t.useEffect((()=>{T("Posts:onLoad")}),[]),t.useEffect((()=>{n&&n.id!==Ee&&(Le(n.id),setTimeout((()=>{Ze(n.id)}),1e3))}),[n,Ee]);const Je=async(e="",t="")=>{const i=e||Ee||Fe.uid;return Qe?await h({groupId:i,cursor:t}):await v({...Fe,uid:i,cursor:t})},Ze=async(e,t=!1)=>{var n,s;const o=t?"Posts:onClickLoadMore":"Posts:onClickReload";T(o),qe(!0),fe.loading({key:o,content:ge({en:"Fetching posts...",vi:"Đang tải bài viết..."}),duration:0});const a=t?[...Me]:[];let l=(null==(n=a[a.length-1])?void 0:n.cursor)||"",r=!1,c=0;for(;c<Ne&&!r;){const t=await Je(e,l);if(!(null==t?void 0:t.length))break;c+=t.length,a.push(...t),console.log(a),fe.loading({key:o,content:i.jsxs(i.Fragment,{children:[ge({en:"Fetching posts...",vi:"Đang tải bài viết..."})+c+"/"+Ne,i.jsx("br",{}),ge({en:"Click to stop",vi:"Bấm để dừng"})]}),onClick:()=>r=!0,duration:0}),Ue([...a]),l=null==(s=a[a.length-1])?void 0:s.cursor,Ie&&await it(t,!1,!1),await C(1e3)}0===a.length?fe.info({key:o,content:ge({en:"Posts not found",vi:"Không tìm thấy bài viết nào"})}):fe.success({key:o,content:t?ge({en:"Load more posts success: ",vi:"Tải thêm thành công: "})+c:ge({en:"Fetch posts success: ",vi:"Tải bài viết thành công: "})+a.length}),qe(!1)},et=async()=>{if(!(await X()))return;const e="Posts:onClickFindFirstPost";T(e),ze(!0),fe.loading({key:e,content:ge({en:"Finding first post...",vi:"Tìm bài viết đầu tiên..."}),duration:0});let t=r,i=Date.now();Ue([]);const n=await y({uid:Ee||Fe.uid||"",startTime:t,endTime:i,progress:(t,i)=>{console.log(t,i),fe.loading({key:e,content:ge({en:"Finding first post...",vi:"Tìm bài viết đầu tiên..."})+L(t),duration:0}),(null==i?void 0:i.length)&&Ue((e=>{const t=new Set(e.map((e=>e.id)));return[...i.filter((e=>!t.has(e.id))),...e].sort(((e,t)=>t.creation_time-e.creation_time))}))}});n?(fe.destroy(e),je.open({key:e,type:"success",message:ge({en:"Find first post done: ",vi:"Tìm bài viết đầu tiên xong: "})+L(n.creation_time),description:n.url})):fe.info({key:e,content:ge({en:"First post not found",vi:"Không tìm thấy bài viết đầu tiên"})}),ze(!1),console.log(n)},tt=async(e,t=!1)=>{const i=pe(e);return Pe({key:"Posts:onLoadReactions",id:e.id,record:e,actionFn:async()=>{try{const t=e.post_id||z(e.id)||await H(e.url),i=await ee(t);return Ae((t=>({...t,[e.id]:i}))),!0}catch(t){return console.error(t),!1}},loadingText:()=>ge({en:"Loading reactions..",vi:"Đang tải lượt thích.."})+i,successText:()=>ge({en:"Reactions loaded: ",vi:"Tải lượt thích thành công: "})+i,failedText:()=>ge({en:"Reactions load failed: ",vi:"Không tải được lượt thích: "})+i,onSuccess:()=>{},needConfirm:t,confirmProps:{title:ge({en:"Load reactions?",vi:"Tải lượt thích?"}),text:i}})},it=async(e,t=!0,i=!0)=>(Ke(!0),ke({data:e,key:"Posts:onLoadReactionsBulk",actionFn:tt,loadingText:e=>ge({en:"Loading reactions...",vi:"Đang tải lượt thích..."}),successText:e=>ge({en:"Loaded reactions",vi:"Hoàn tất tải lượt thích"}),successDescItem:e=>pe(e),needConfirm:t,showSuccessNoti:i,confirmProps:{title:ge({en:"Load reactions of "+e.length+" posts?",vi:"Tải lượt thích của "+e.length+" bài viết?"}),text:ge({en:"Warning: This action will call api for each post. Please only select posts you want to load reactions. To reduce the chance of being banned by fb, please spread the posts to load reactions.",vi:"Lưu ý: Sẽ gọi api cho từng bài viết. Vui lòng chỉ chọn bài muốn tải lượt thích. Để giảm tỷ lệ bị fb ban"})}}).finally((()=>{Ke(!1)}))),nt=async(e,t=!1)=>{const i=pe(e);return Pe({key:"Posts:onDeletePost",id:e.id,record:e,actionFn:()=>be?C(1e3):u(e.story_id),loadingText:()=>ge({en:"Deleting post..",vi:"Đang xoá bài viết.."})+i,successText:()=>ge({en:"Post deleted: ",vi:"Xoá bài viết thành công: "})+i,failedText:()=>ge({en:"Post not deleted: ",vi:"Không xoá được bài viết: "})+i,onSuccess:()=>{Ue((t=>a(t,(t=>{const i=t.find((t=>t.story_id===e.story_id));i&&(i.deleted=!0)}))))},needConfirm:t,confirmProps:{title:ge({en:"Delete post?",vi:"Xoá bài viết?"}),text:i}})},st=async(e,t=!1)=>{const i=pe(e);return Pe({key:"Posts:onTrashPost",id:e.id,record:e,actionFn:()=>be?C(1e3):g(e.story_id),loadingText:()=>ge({en:"Moving post to trash..",vi:"Đang chuyển bài viết vào thùng rác.."})+i,successText:()=>ge({en:"Post trashed: ",vi:"Chuyển bài viết vào thùng rác thành công: "})+i,failedText:()=>ge({en:"Post not trashed: ",vi:"Không chuyển được bài viết vào thùng rác: "})+i,onSuccess:()=>{Ue((t=>a(t,(t=>{const i=t.find((t=>t.story_id===e.story_id));i&&(i.trashed=!0)}))))},needConfirm:t,confirmProps:{title:ge({en:"Move post to trash? (Delete after 30 days)",vi:"Chuyển vào thùng rác? (Bị xoá sau 30 ngày)"}),text:i}})},ot=async(e,t,i=!1)=>{const n=pe(e),s=ge(d[t]);return Pe({key:"Posts:onChangePostPrivacy",id:e.id,record:e,actionFn:()=>be?C(1e3):m({writeID:e.privacy.write_id,baseState:t}),loadingText:()=>ge({en:"Changing post privacy to "+s+"..",vi:"Đang sửa quyền riêng tư sang "+s+".."})+n,successText:()=>ge({en:"Change post privacy success "+s+": ",vi:"Sửa quyền riêng tư thành công "+s+": "})+n,failedText:()=>ge({en:"Change post privacy failed "+s+": ",vi:"Sửa quyền riêng tư lỗi "+s+": "})+n,onSuccess:()=>{Ue((i=>a(i,(i=>{const n=i.find((t=>t.story_id===e.story_id));n&&(n.privacy.scope=t)}))))},needConfirm:i,confirmProps:{title:ge({en:"Change post privacy? ",vi:"Sửa quyền riêng tư? "}),text:s+"; "+n}})},at=async(e,t=!1)=>{const i=pe(e);return Pe({key:"Posts:onUnTagPost",id:e.id,record:e,actionFn:()=>be?C(1e3):p(e.story_id),loadingText:()=>ge({en:"Untagging post..",vi:"Đang gỡ thẻ bài viết.."})+i,successText:()=>ge({en:"Post untagged: ",vi:"Gỡ thẻ bài viết thành công: "})+i,failedText:()=>ge({en:"Post not untagged: ",vi:"Không gỡ được thẻ bài viết: "})+i,onSuccess:()=>{Ue((t=>a(t,(t=>{const i=t.find((t=>t.story_id===e.story_id));i&&(i.untagged=!0)}))))},needConfirm:t,confirmProps:{title:ge({en:"Untag post?",vi:"Gỡ thẻ bài viết?"}),text:i}})},lt=[{title:"#",key:"recent",dataIndex:"recent",sorter:(e,t)=>e.recent-t.recent,render:(e,t,i)=>t.recent+1,width:70,align:"center"},{title:ge({en:"Actor",vi:"Người đăng"}),key:"actor",dataIndex:"actor",onSearch:(e,t,i)=>{var n,s;return I(e,(null==(n=null==i?void 0:i.actor)?void 0:n.name)+(null==(s=null==i?void 0:i.actor)?void 0:s.id))},render:(e,t,n)=>{var s,o,a,l,r,c;return i.jsxs(D,{style:{maxWidth:300},children:[i.jsx(te,{shape:"square",src:i.jsx(ie,{src:F(null==(s=t.actor)?void 0:s.id),fallback:null==(o=t.actor)?void 0:o.avatar}),size:50}),i.jsxs(D,{direction:"vertical",size:0,children:[i.jsx("a",{href:(null==(a=t.actor)?void 0:a.url)||_(null==(l=t.actor)?void 0:l.id),target:"_blank",children:i.jsx("b",{children:null==(r=t.actor)?void 0:r.name})}),i.jsx("span",{style:{opacity:.5},children:null==(c=t.actor)?void 0:c.id})]})]})},filters:w(Ye.map((e=>e.actor)),"name"),onFilter:(e,t)=>{var i;return(null==(i=t.actor)?void 0:i.name)==e},width:250},{title:ge({en:"Media",vi:"Ảnh/Video"}),key:"media",dataIndex:"media",render:(e,t,n)=>{var s,o;const a=null==(s=t.content)?void 0:s.attachments;if((null==a?void 0:a.length)>0){const e=(null==(o=null==a?void 0:a[0])?void 0:o.total_count)||(null==a?void 0:a.length),n=1===e,s=1===e&&"Video"===a[0].type;return i.jsx(S.Ribbon,{text:s?i.jsx("i",{className:"fa-solid fa-video"}):e>1?e:null,style:{display:s||e>1?"block":"none"},children:i.jsx(ie,{src:a[0].thumbnail,style:{objectFit:"cover",height:150,width:210,borderRadius:10},preview:!(!n||s)||{destroyOnClose:!0,imageRender:()=>s?i.jsx(he,{info:{id:a[0].id}}):i.jsx(ne,{style:{maxWidth:"90vw",maxHeight:"90vh",overflowY:"auto",overflowX:"hidden",borderRadius:10},children:i.jsx(ve,{target:t.actor,postId:t.id})}),toolbarRender:()=>null}})})}return"-"},filters:[{text:ge({en:"Video",vi:"Video"}),value:"video"},{text:ge({en:"Has media",vi:"Có ảnh hoặc video"}),value:"has-media"},{text:ge({en:"Multi media",vi:"Nhiều ảnh/video"}),value:"multi-media"}],onFilter:(e,t)=>{var i,n,s,o,a,l,r,c,d;return"video"===e?1===(null==(n=null==(i=t.content)?void 0:i.attachments)?void 0:n.length)&&"Video"===(null==(a=null==(o=null==(s=t.content)?void 0:s.attachments)?void 0:o[0])?void 0:a.type):"has-media"===e?(null==(r=null==(l=t.content)?void 0:l.attachments)?void 0:r.length)>0:"multi-media"===e?(null==(d=null==(c=t.content)?void 0:c.attachments)?void 0:d.length)>1:void 0},width:200,align:"right"},{title:ge({en:"Content",vi:"Nội dung"}),key:"content",dataIndex:"content",onSearch:(e,t,i)=>{var n;return[null==(n=i.content)?void 0:n.text,i.message,i.title,i.summary].filter(Boolean).some((t=>I(e,t)))},render:(e,t,n)=>{var s,o,a,l,r,c;const d=(null==(s=t.content)?void 0:s.text)||(null==(l=null==(a=null==(o=t.content)?void 0:o.attachments)?void 0:a.find((e=>e.title)))?void 0:l.title);return i.jsxs(D,{direction:"vertical",style:{maxWidth:400},children:[t.is_pinned&&i.jsx(S,{count:ge({en:"Pinned post",vi:"Bài viết Đã ghim"}),color:"green"}),(null==(r=t.summary)?void 0:r.length)>0&&i.jsx(N.Text,{style:{fontStyle:"italic"},type:"secondary",children:R(t.summary,150)}),(null==(c=t.message)?void 0:c.length)>0&&i.jsx(N.Text,{children:R(t.message,150)}),i.jsx(N.Text,{type:"secondary",children:R(d,150)})]})},width:"auto"},{title:ge({en:"Posted time",vi:"Đăng lúc"}),key:"date",dataIndex:"date",sorter:(e,t)=>e.creation_time-t.creation_time,render:(e,t,n)=>i.jsxs(D,{direction:"vertical",size:0,children:[i.jsxs(N.Text,{children:[E(t.creation_time)," ",ge({en:" ago",vi:"trước"})]}),i.jsx(N.Text,{type:"secondary",children:L(t.creation_time)})]})},{title:ge({en:"Privacy",vi:"Quyền riêng tư"}),key:"privacy",dataIndex:"privacy",render:(e,t,n)=>{var s;if(t.deleted)return i.jsx(O,{color:"red",children:ge({en:"Deleted",vi:"Đã xoá"})});const{scope:o,description:a,allow:l,deny:r}=t.privacy||{};return i.jsxs(D,{direction:"vertical",size:0,children:[(null==(s=t.privacy)?void 0:s.can_edit)?i.jsx(se,{value:o,options:ue.map((e=>({value:e,label:ge(d[e])}))),onSelect:e=>{console.log(e),ot(t,e,!0)},popupMatchSelectWidth:!1}):i.jsx(N.Text,{children:ge(d[o])}),a&&((null==l?void 0:l.length)>0||(null==r?void 0:r.length)>0)&&i.jsx(N.Text,{type:"secondary",children:a})]})},filters:Object.keys(c).map((e=>({text:ge(d[e])+" ("+Ye.filter((t=>t.privacy.scope===e)).length+")",value:e}))),onFilter:(e,t)=>t.privacy.scope===e,width:150},{title:ge({en:"React",vi:"Thích"}),key:"reactions",dataIndex:"reactions",render:(e,t,n)=>{const s=t.reactions;if(!s)return"-";const o=s.reduce(((e,t)=>e+t.count),0);return i.jsxs(D,{size:3,direction:"vertical",children:[i.jsx(O,{color:o>0?"success":"default",children:B(o)}),s.map((e=>i.jsxs(D,{size:2,children:[i.jsx(ie,{preview:!1,src:Z[e.reactionId].icon,width:20,height:20})," ",B(e.count)]},e.reactionId)))]})},rangeFilter:{getValue:e=>e.totalReact},sorter:(e,t)=>e.totalReact-t.totalReact,width:100},{title:ge({en:"Action",vi:"Hành động"}),key:"action",dataIndex:"action",render:(e,t,n)=>{var a;return i.jsxs(D,{size:3,wrap:!0,style:{maxWidth:200},children:[!t.deleted&&i.jsx(M,{title:ge({en:"View detail",vi:"Xem chi tiết"}),children:i.jsx(s,{type:"primary",onClick:()=>{$e(t)},icon:i.jsx("i",{className:"fa-solid fa-info"})})}),i.jsx(M,{title:ge({en:"Load reactions",vi:"Tải lượt thích"}),children:i.jsx(s,{icon:i.jsx("i",{className:"fa-solid fa-thumbs-up"}),onClick:()=>tt(t,!0)})}),t.can_delete&&!t.deleted&&i.jsxs(i.Fragment,{children:[i.jsx(M,{title:ge({en:"Delete permanently",vi:"Xoá vĩnh viễn"}),children:i.jsx(s,{danger:!0,icon:i.jsx("i",{className:"fa-solid fa-trash"}),onClick:()=>nt(t,!0)})}),!t.trashed&&i.jsx(M,{title:ge({en:"Move to trash",vi:"Chuyển vào thùng rác"}),children:i.jsx(s,{icon:i.jsx("i",{className:"fa-solid fa-eye-slash"}),onClick:()=>st(t,!0)})})]}),!t.can_delete&&(null==we?void 0:we.id)==Ee&&Ee!==(null==(a=t.actor)?void 0:a.id)&&i.jsx(M,{title:t.untagged?ge({en:"Tag removed",vi:"Đã gỡ thẻ"}):ge({en:"Remove tag",vi:"Gỡ thẻ"}),children:i.jsx(s,{danger:!t.untagged,disabled:t.untagged,icon:i.jsx("i",{className:"fa-solid fa-user-tag"}),onClick:()=>at(t,!0)})}),!t.deleted&&i.jsx(M,{title:ge({en:"Open in Bulk downloader",vi:"Mở trong Tải hàng loạt"}),children:i.jsx(s,{onClick:()=>{ye("/bulk-downloader",{state:{targetId:t.url,platform:o.FacebookPost}})},icon:i.jsx("i",{className:"fa-solid fa-download"})})}),!t.deleted&&i.jsx(M,{title:ge({en:"View on Facebook",vi:"Xem bài viết"}),children:i.jsx(s,{href:t.trashed?"https://www.facebook.com/me/allactivity/?category_key=TRASH":t.url,target:"_blank",icon:i.jsx("i",{className:"fa-solid fa-up-right-from-square"})})})]})},width:150}],rt=i.jsx(M,{mouseEnterDelay:.5,title:ge({en:"Load older posts. Use filters above",vi:"Tải thêm bài viết cũ hơn. Sử dụng các bộ lọc trên"}),children:i.jsx(s,{loading:Ve,icon:i.jsx("i",{className:"fa-solid fa-play"}),onClick:()=>Ze(null,!0),children:ge(Ve?{en:"Loading..",vi:"Đang tải.."}:{en:"Load more",vi:"Tải thêm"})})}),ct=t.useMemo((()=>Object.entries(Fe).filter((([e,t])=>t!==xe[e]))),[Fe]);return i.jsxs(J,{title:ge({en:"Posts manager",vi:"Quản lý bài viết"}),titleSuffix:i.jsx(O,{color:"error",style:{marginLeft:10},children:ge({en:"🚧 Work in progress",vi:"🚧 Đang phát triển"})}),children:[i.jsxs(D,{direction:"vertical",size:0,children:[i.jsxs(D.Compact,{style:{maxWidth:"90vw",marginBottom:10,overflow:"auto"},children:[i.jsx(oe,{title:(null==(A=null==(l=null==Oe?void 0:Oe.type)?void 0:l.toUpperCase)?void 0:A.call(l))||"User/Page/Group",content:Oe?i.jsxs(D,{children:[i.jsx(te,{shape:"square",src:i.jsx(ie,{src:Oe.avatar}),size:50}),i.jsx("a",{href:Oe.url,target:"_blank",children:i.jsx("b",{children:Oe.name})})]}):null,children:i.jsx(ae,{placeholder:ge({en:"UID/URL",vi:"UID/URL"}),value:Ee,onChange:async e=>{const t=e.target.value;if(Le(t),""==t.trim())return;T("Posts:onChangeUid:"+t);const i="Posts:onChangeUid";fe.loading({key:i,content:ge({en:"Finding UID..",vi:"Đang tìm UID.."}),duration:0});const n=/^\d+$/.test(t)?t:await K(t);fe.loading({key:i,content:ge({en:"Finding Profile..",vi:"Đang tìm Profile.."}),duration:0});const s=await U(n);Le(n),Be(s),n?fe.success({key:i,content:ge({en:"UID found: ",vi:"Tìm thấy UID: "})+n+" - "+(null==s?void 0:s.name)}):fe.error({key:i,content:ge({en:"UID not found",vi:"Không tìm thấy UID"})})},style:{minWidth:100,maxWidth:150},prefix:i.jsx("i",{className:"fa-solid fa-user"})})}),i.jsx(s,{onClick:()=>Ce((e=>!e)),icon:i.jsx("i",{className:"fa-solid fa-filter"}),children:i.jsxs(D,{size:3,align:"center",children:[ge({en:"Filters",vi:"Bộ lọc"})," ",i.jsx(O,{color:ct.length>0?"blue":"default",style:{margin:0},children:ct.length}),i.jsx("i",{style:{opacity:.5},className:"fa-solid "+(Te?"fa-chevron-up":"fa-chevron-down")})]})}),i.jsx(M,{mouseEnterDelay:.3,title:ge({en:"Auto load reactions for each post",vi:"Tự động tải lượt thích cho từng bài viết"}),children:i.jsx(s,{icon:i.jsx("i",{className:"fa-solid fa-thumbs-up"}),type:Ie?"primary":"default",onClick:async()=>{(Ie||await X())&&Se((e=>!e))},children:ge({en:"Reactions",vi:"Lượt thích"})})}),i.jsx(M,{title:ge({en:"Max posts to load",vi:"Số bài viết tối đa muốn tải"}),children:i.jsx(le,{placeholder:ge({en:"Max posts",vi:"Số bài viết tối đa"}),value:Ne,onChange:e=>Re(Math.max(e||0,1)),style:{minWidth:50,maxWidth:100}})}),i.jsx(s,{type:"primary",onClick:()=>Ze(),icon:i.jsx("i",{className:"fa-solid fa-rotate-right"}),loading:Ve,children:ge({en:"Load posts",vi:"Tải bài viết"})})]}),Te&&i.jsxs(D.Compact,{style:{maxWidth:"90vw",marginBottom:10,overflow:"auto"},children:[i.jsx(M,{title:ge({en:"Posted before time",vi:"Đăng trước ngày"}),children:i.jsx(re,{value:Fe.beforeTime?f(Fe.beforeTime):null,onChange:(e,t)=>{T("Posts:onChangeTime"),_e((e=>a(e,(e=>{if(!t)return void(e.beforeTime=null);const i=Array.isArray(t)?t[0]:t;e.beforeTime=new Date(i).getTime(),console.log(t,e.beforeTime)}))))},maxDate:f(new Date),disabled:Qe,style:{minWidth:100,maxWidth:150},placeholder:ge({en:"Before time",vi:"Trước ngày"})})}),i.jsx(M,{title:ge({en:"Posted by",vi:"Đăng bởi"}),children:i.jsxs(se,{style:{minWidth:120},value:Fe.postedByOthers,onChange:e=>{T("Posts:onChangePostedBy:"+e),_e((t=>a(t,(t=>{t.postedByOthers=e}))))},disabled:Qe,popupMatchSelectWidth:!1,children:[i.jsx(se.Option,{value:null,children:ge({en:"Anyone",vi:"Bất kỳ ai"})}),i.jsxs(se.Option,{value:!1,children:["📌 ",ge({en:"Owner",vi:"Chính chủ"})]}),i.jsxs(se.Option,{value:!0,children:["👤 ",ge({en:"Others",vi:"Người khác"})]})]})}),i.jsx(M,{title:ge({en:"Privacy",vi:"Quyền riêng tư"}),children:i.jsx(se,{style:{minWidth:120},value:Fe.privacy,onChange:e=>{T("Posts:onChangePrivacy:"+e),_e((t=>a(t,(t=>{t.privacy=e}))))},disabled:Qe,popupMatchSelectWidth:!1,children:me.map((e=>i.jsx(se.Option,{value:e,children:ge(d[e])},e)))})}),i.jsx(M,{title:ge({en:"Tagged only",vi:"Chỉ bài viết được gắn thẻ"}),children:i.jsx(s,{icon:i.jsx("i",{className:"fa-solid fa-user-tag"}),type:Fe.taggedInOnly?"primary":"default",onClick:()=>{T("Posts:onToggleTaggedOnly"),_e((e=>a(e,(e=>{e.taggedInOnly=!e.taggedInOnly}))))},disabled:Qe,children:ge({en:"Tagged only",vi:"Gắn thẻ"})})}),i.jsx(M,{title:ge({en:"Ignore pinned post",vi:"Bỏ qua bài viết đã ghim"}),children:i.jsx(s,{icon:i.jsx("i",{className:"fa-solid fa-thumbtack"}),type:Fe.omitPinnedPost?"primary":"default",onClick:()=>{T("Posts:onTogglePinnedPost"),_e((e=>a(e,(e=>{e.omitPinnedPost=!e.omitPinnedPost}))))},disabled:Qe,children:ge({en:"Pinned",vi:"Đã ghim"})})})]})]}),i.jsx(q,{data:Ye,columns:lt,searchable:!0,selectable:!0,keyExtractor:e=>e.id,renderTitle:e=>{var t;const n=(null==e?void 0:e.length)?e:Ye,o=n.filter((e=>e.can_delete&&!e.deleted)),a=n.filter((e=>e.can_delete&&!e.trashed&&!e.deleted)),l=n.filter((e=>{var t;return(null==(t=e.privacy)?void 0:t.can_edit)&&!e.deleted})),r=n.filter((e=>!We[e.id])),c=n.filter((e=>{var t;return!e.can_delete&&(null==we?void 0:we.id)==Ee&&Ee!==(null==(t=e.actor)?void 0:t.id)&&!e.untagged}));return i.jsxs(i.Fragment,{children:[i.jsxs(D.Compact,{children:[i.jsx(G,{data:e.length?e:Ye,options:[{key:"json",label:".json",prepareData:e=>({fileName:"posts.json",data:JSON.stringify(e,null,4)})}]}),rt]}),i.jsxs(D.Compact,{children:[i.jsx(Q,{type:"hot",children:i.jsx(M,{mouseEnterDelay:.4,title:ge({en:"Find first posts",vi:"Tìm bài viết đầu tiên"}),children:i.jsx(s,{loading:Ge,icon:i.jsx("i",{className:"fa-solid fa-clock-rotate-left"}),onClick:et,disabled:Qe,children:ge({en:"First posts",vi:"Bài đầu tiên"})})})}),i.jsx(M,{mouseEnterDelay:.4,title:ge({en:"Search posts on Facebook (Fanpage/Group/User)",vi:"Tìm tất cả bài viết trên Facebook (Fanpage/Group/User)"}),children:i.jsx(s,{icon:i.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"}),onClick:async()=>{T("Posts:FindAllPosts"),x(Ee||Fe.uid||await W()||"")},children:ge({en:"All posts",vi:"Tìm bài viết"})})})]}),i.jsxs(D.Compact,{children:[r.length>0&&i.jsx(Q,{type:"new",children:i.jsx(M,{mouseEnterDelay:.3,title:ge({en:"Load post reactions of "+r.length+" posts",vi:"Tải lượt thích của "+r.length+" bài viết"}),children:i.jsxs(s,{loading:He,icon:i.jsx("i",{className:"fa-solid fa-thumbs-up"}),onClick:()=>it(r),children:[ge({en:"Reactions ",vi:"Thích "}),r.length]})})}),o.length>0&&i.jsx(M,{mouseEnterDelay:.3,title:ge({en:`Delete permanently ${o.length} posts`,vi:`Xoá vĩnh viễn ${o.length} bài viết`}),children:i.jsxs(s,{danger:!0,icon:i.jsx("i",{className:"fa-solid fa-trash"}),onClick:()=>(async e=>ke({data:e,key:"Posts:onDeleteBulk",actionFn:nt,loadingText:e=>ge({en:"Deleting post...",vi:"Đang xoá bài viết..."}),successText:e=>ge({en:"Deleted posts",vi:"Hoàn tất xoá bài viết"}),successDescItem:e=>pe(e),confirmProps:{title:ge({en:"Delete "+e.length+" posts?",vi:"Xoá "+e.length+" bài viết?"}),text:ge({en:"Warning: Cannot undo",vi:"Lưu ý: Không thể hoàn tác."})}}))(o),children:[ge({en:"Delete",vi:"Xoá"})," ",o.length]})}),a.length>0&&i.jsx(M,{mouseEnterDelay:.3,title:ge({en:`Move to trash ${a.length} posts`,vi:`Chuyển vào thùng rác ${a.length} bài viết`}),children:i.jsxs(s,{icon:i.jsx("i",{className:"fa-solid fa-eye-slash"}),onClick:()=>(async e=>ke({data:e,key:"Posts:onTrashBulk",actionFn:st,loadingText:e=>ge({en:"Moving to trash...",vi:"Đang chuyển vào thùng rác..."}),successText:e=>ge({en:"Trashed posts",vi:"Hoàn tất chuyển vào thùng rác"}),successDescItem:e=>pe(e),confirmProps:{title:ge({en:"Move to trash "+e.length+" posts?",vi:"Chuyển vào thùng rác "+e.length+" bài viết?"}),text:ge({en:"Warning: Will be deleted after 30 days",vi:"Lưu ý: Sẽ bị xoá sau 30 ngày"})}}))(a),children:[ge({en:"Trash",vi:"Rác"})," ",a.length]})}),l.length>0&&i.jsx(M,{mouseEnterDelay:.3,title:ge({en:"Change privacy "+l.length+" posts",vi:"Sửa quyền riêng tư "+l.length+" bài viết"}),children:i.jsx(se,{value:null==(t=l[0].privacy)?void 0:t.scope,options:ue.map((e=>({value:e,label:ge(d[e])}))),onSelect:e=>{(async(e,t)=>{const i=ge(d[t]);ke({data:e,key:"Posts:onChangePrivacyBulk",actionFn:e=>t===e.privacy.scope||ot(e,t),loadingText:e=>ge({en:"Changing post privacy...",vi:"Đang sửa quyền riêng tư..."})+i,successText:e=>ge({en:"Chaged posts privacy: ",vi:"Hoàn tất sửa quyền riêng tư: "})+i,successDescItem:e=>pe(e)+": "+_(e.id),confirmProps:{title:ge({en:"Changed privacy "+e.length+" posts?",vi:"Sửa quyền riêng tư "+e.length+" bài viết?"}),text:i}})})(l,e)},popupMatchSelectWidth:!1})}),c.length>0&&i.jsx(M,{mouseEnterDelay:.3,title:ge({en:"Untag "+c.length+" posts",vi:"Gỡ thẻ "+c.length+" bài viết"}),children:i.jsxs(s,{danger:!0,icon:i.jsx("i",{className:"fa-solid fa-user-tag"}),onClick:()=>(async e=>ke({data:e,key:"Posts:onUntagBulk",actionFn:at,loadingText:e=>ge({en:"Untagging posts...",vi:"Đang gỡ thẻ bài viết..."}),successText:e=>ge({en:"Untagged posts",vi:"Hoàn tất gỡ thẻ bài viết"}),successDescItem:e=>pe(e),confirmProps:{title:ge({en:"Untag "+e.length+" posts?",vi:"Gỡ thẻ "+e.length+" bài viết?"}),text:ge({en:"Warning: Cannot undo",vi:"Lưu ý: Không thể hoàn tác."})}}))(c),children:[ge({en:"Untag",vi:"Gỡ thẻ"})," ",c.length]})})]})]})},footer:i.jsx(ce,{align:"middle",justify:"center",style:{width:"100%"},children:rt})}),i.jsx(de,{open:!!Xe,onCancel:()=>$e(null),footer:null,width:"90vw",children:Xe&&i.jsx(ve,{target:Xe.actor,postId:Xe.id})})]})}export{ye as default};
