import{cv as e,cw as n,cx as s,az as t,$ as o,aA as r,aB as a,r as c,G as i,aC as l,cy as u,i as f}from"./index-Cak6rALw.js";let g=null,p=e=>e(),d=[],y={};function m(){const{getContainer:e,duration:n,rtl:s,maxCount:t,top:o}=y,r=(null==e?void 0:e())||document.body;return{getContainer:()=>r,duration:n,rtl:s,maxCount:t,top:o}}const C=o.forwardRef(((e,n)=>{const{messageConfig:s,sync:t}=e,{getPrefixCls:r}=c.useContext(i),a=y.prefixCls||r("message"),f=c.useContext(l),[g,p]=u(Object.assign(Object.assign(Object.assign({},s),{prefixCls:a}),f.message));return o.useImperativeHandle(n,(()=>{const e=Object.assign({},g);return Object.keys(e).forEach((n=>{e[n]=(...e)=>(t(),g[n].apply(g,e))})),{instance:e,sync:t}})),p})),h=o.forwardRef(((e,n)=>{const[s,t]=o.useState(m),c=()=>{t(m)};o.useEffect(c,[]);const i=a(),l=i.getRootPrefixCls(),u=i.getIconPrefixCls(),f=i.getTheme(),g=o.createElement(C,{ref:n,sync:c,messageConfig:s});return o.createElement(r,{prefixCls:l,iconPrefixCls:u,theme:f},i.holderRender?i.holderRender(g):g)}));function x(){if(!g){const e=document.createDocumentFragment(),n={fragment:e};return g=n,void p((()=>{t()(o.createElement(h,{ref:e=>{const{instance:s,sync:t}=e||{};Promise.resolve().then((()=>{!n.instance&&s&&(n.instance=s,n.sync=t,x())}))}}),e)}))}g.instance&&(d.forEach((e=>{const{type:n,skipped:s}=e;if(!s)switch(n){case"open":p((()=>{const n=g.instance.open(Object.assign(Object.assign({},y),e.config));null==n||n.then(e.resolve),e.setCloseFn(n)}));break;case"destroy":p((()=>{null==g||g.instance.destroy(e.key)}));break;default:p((()=>{var s;const t=(s=g.instance)[n].apply(s,f(e.args));null==t||t.then(e.resolve),e.setCloseFn(t)}))}})),d=[])}const v={open:function(e){const n=s((n=>{let s;const t={type:"open",config:e,resolve:n,setCloseFn:e=>{s=e}};return d.push(t),()=>{s?p((()=>{s()})):t.skipped=!0}}));return x(),n},destroy:e=>{d.push({type:"destroy",key:e}),x()},config:function(e){y=Object.assign(Object.assign({},y),e),p((()=>{var e;null===(e=null==g?void 0:g.sync)||void 0===e||e.call(g)}))},useMessage:n,_InternalPanelDoNotUseOrYouWillBeFired:e};["success","info","warning","error","loading"].forEach((e=>{v[e]=(...n)=>function(e,n){const t=s((s=>{let t;const o={type:e,args:n,resolve:s,setCloseFn:e=>{t=e}};return d.push(o),()=>{t?p((()=>{t()})):o.skipped=!0}}));return x(),t}(e,n)}));export{v as default};
