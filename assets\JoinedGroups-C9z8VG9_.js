import{aT as e,bc as n,r as i,b0 as o,b5 as t,bk as a}from"./index-Cak6rALw.js";import{u as r,d as s,v as l,S as d,c as m,g as c,T as p,A as u,o as g,t as h}from"./MyApp-DW5WH4Ub.js";import{u as j}from"./useForceStop-CvbJS7ei.js";import v from"./MyTable-DnjDmN7z.js";import{E as f}from"./ExportButton-CbyepAf_.js";import x,{getCacheByKey as y}from"./useCacheState-CAnxkewm.js";import{getOtherJoinedGroups as b,getMyManagedGroups as k,UserTypeInGroup as w,getMyJoinedGroups as C}from"./groups-D1WZyVD8.js";import _ from"./WordStatisticButton-D2UxB2M4.js";import{B as G}from"./BadgeWrapper-BCbYXXfB.js";import{I as N}from"./index-CDSnY0KE.js";import"./index-ByRdMNW-.js";import"./index-BzMPigdO.js";import"./DownOutlined-B-JcS-29.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./row-BMfM-of4.js";import"./index-Bp2s1Wws.js";import"./index-Bx3o6rwA.js";import"./index-IxNfZAD-.js";function T({target:T}){const{message:S}=e.useApp(),D=n(),E=j(),{ti:F}=r(),{notification:I}=s(),[J,B]=i.useState(!1),[M,L]=x("JoinedGroups.data."+(null==T?void 0:T.id),[]),[A,O]=x("JoinedGroups.myGroupLoaded",!1),[P,$]=x("Groups.data",[]),[H,R]=i.useState(!1),W=i.useMemo((()=>{const e=new Set(P.map((e=>e.id)));return M.map((n=>({...n,isCommonGroup:e.has(n.id)})))}),[M,P]);i.useEffect((()=>{var e;(null==T?void 0:T.id)&&V(!(null==(e=y("JoinedGroups.data."+(null==T?void 0:T.id)))?void 0:e.length))}),[null==T?void 0:T.id]);const V=async(e=!1)=>{var n;if(!(null==T?void 0:T.id))return;const i=E.start();let o=[...e?[]:y("JoinedGroups.data."+(null==T?void 0:T.id))||[]],t=(null==(n=o[o.length-1])?void 0:n.cursor)||"";B(!0);try{for(;!i.value();){const e=await b({uid:T.id,cursor:t});if(!(null==e?void 0:e.length))break;t=e[e.length-1].cursor,o=[...o,...e].map(((e,n)=>({...e,recent:n}))),L(o),await l(500)}S.success({content:F({en:"Load groups success ",vi:"Tải nhóm xong "})+o.length})}catch(a){S.error({content:F({en:"Error: ",vi:"Lỗi: "})+a.message})}finally{L(o),B(!1)}},z=async()=>{var e;const n="JoinedGroups.onClickFindCommonGroups";h(n),R(!0);const i=[],o=new Set;for(const[t,a]of[[k,w.ADMIN],[C,w.MEMBER]]){let a="";for(;;){S.loading({key:n,content:F({en:"Finding common groups...",vi:"Đang tìm nhóm chung..."})+i.length,duration:0});const r=await t({cursor:a}),s=null==r?void 0:r.filter((e=>!o.has(e.id)));if(r.forEach((e=>o.add(e.id))),!(null==s?void 0:s.length))break;i.push(...s),a=null==(e=null==i?void 0:i[(null==i?void 0:i.length)-1])?void 0:e.cursor,$([...i]),await l(500)}}R(!1),O(!0),S.destroy(n),I.open({type:"success",message:F({en:"Find common groups success",vi:"Hoàn tất tìm nhóm chung"}),description:F({en:'Please use filter in column "Common groups"',vi:'Vui lòng sử dụng bộ lọc ở cột "Nhóm chung"'}),duration:0})},K=[{title:"#",dataIndex:"recent",key:"recent",render:(e,n,i)=>(n.recent||0)+1,sorter:(e,n)=>e.recent-n.recent,width:60},{title:F({en:"Cover",vi:"Ảnh"}),key:"cover",dataIndex:"image",render:(e,n,i)=>o.jsx(N,{src:n.image,style:{width:150,height:100,objectFit:"contain"}}),width:150},{title:F({en:"Name",vi:"Tên"}),key:"name",dataIndex:"name",sorter:(e,n)=>e.name.localeCompare(n.name),render:(e,n,i)=>o.jsxs(d,{direction:"vertical",style:{maxWidth:250},children:[o.jsx(m.Link,{strong:!0,href:n.url,target:"_blank",children:n.name}),o.jsx(m.Text,{type:"secondary",children:n.id})]})},{title:F({en:"Description",vi:"Mô tả"}),key:"description",dataIndex:"description",render:(e,n,i)=>o.jsx("p",{style:{width:250},children:e}),sorter:(e,n)=>(e.description||"").localeCompare(n.description||"")},{title:F({en:"Members",vi:"Thành viên"}),key:"membersCount",dataIndex:"membersCount",sorter:(e,n)=>e.membersCount-n.membersCount,render:(e,n,i)=>c(e),align:"end",width:120},...P.length||A?[{title:W.filter((e=>e.isCommonGroup)).length+F({en:" Common groups",vi:" Nhóm chung"}),key:"common",dataIndex:"common",render:(e,n,i)=>n.isCommonGroup?"✅":"❌",filters:[{text:`✅ (${W.filter((e=>e.isCommonGroup)).length})`,value:!0},{text:`❌ (${W.filter((e=>!e.isCommonGroup)).length})`,value:!1}],onFilter:(e,n)=>n.isCommonGroup==e,width:150,align:"right"}]:[],{title:F({en:"Actions",vi:"Hành động"}),key:"actions",render:(e,n,i)=>o.jsx(d.Compact,{children:o.jsx(p,{title:F({en:"Bulk Downloader",vi:"Tải hàng loạt"}),children:o.jsx(t,{type:"default",onClick:()=>{return e=n.id,void D("/bulk-downloader",{state:{targetId:e,platform:a.Facebook}});var e},icon:o.jsx("i",{className:"fa-solid fa-download"})})})}),width:100,align:"end"}];return o.jsxs("div",{style:{display:"flex",flexDirection:"column",width:"100%",height:"100%"},children:[o.jsx(u,{type:"success",showIcon:!0,message:F({en:`Joined ${W.length} public groups`,vi:`Đã tham gia ${W.length} nhóm công khai`}),style:{alignSelf:"center",marginBottom:10}}),o.jsx(v,{columns:K,data:W,keyExtractor:e=>null==e?void 0:e.id,searchable:!0,selectable:!0,pageSize:5,renderTitle:e=>o.jsxs(o.Fragment,{children:[o.jsx(t,{disabled:J,type:"primary",icon:J?o.jsx("i",{className:"fa-solid fa-rotate-right fa-spin"}):o.jsx("i",{className:"fa-solid fa-rotate-right"}),onClick:()=>V(!0),children:F({en:"Reload",vi:"Tải lại"})}),o.jsx(f,{data:e.length?e:W,options:[{key:"uid",label:".txt (id)",prepareData:e=>({fileName:(null==T?void 0:T.name)+"_joined_groups_id.txt",data:e.map((e=>e.id)).join("\n")})},{key:"id_name",label:".csv (id+name)",prepareData:e=>({fileName:(null==T?void 0:T.name)+"_joined_groups_id_name.csv",data:g(e.map((e=>({uid:e.id,name:e.name}))))})},{key:"name_desc",label:".csv (name+desc)",prepareData:e=>({fileName:(null==T?void 0:T.name)+"_joined_groups.csv",data:g(e.map((e=>({name:e.name,desc:e.description}))))})},{key:"json",label:".json",prepareData:e=>({fileName:(null==T?void 0:T.name)+"_joined_groups.json",data:JSON.stringify(e,null,4)})},{key:"csv",label:".csv",prepareData:e=>({fileName:(null==T?void 0:T.name)+"_joined_groups.csv",data:g(e)})}]}),o.jsx(G,{type:"new",children:o.jsx(p,{title:F({en:"Find common groups of "+(null==T?void 0:T.name)+" with you",vi:"Tìm nhóm chung của "+(null==T?void 0:T.name)+" với bạn"}),children:o.jsx(t,{loading:H,icon:o.jsx("i",{className:"fa-solid fa-magnifying-glass"}),onClick:z,children:F({en:"Find common groups",vi:"Tìm nhóm chung"})})})}),o.jsx(_,{name:"JoinedGroups",text:W.map((e=>{var n,i;return[e.name,null==(i=null==(n=e.description)?void 0:n.replace)?void 0:i.call(n,e.name,"")]})).flat().filter(Boolean).join("\n")})]})})]})}export{T as default};
