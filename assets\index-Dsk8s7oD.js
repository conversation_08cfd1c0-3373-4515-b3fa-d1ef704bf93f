import{aJ as e,cz as t,r as n,$ as o}from"./index-Cak6rALw.js";var r,a,u,i,h,s,p,l,f,v,c,y,g,w,d,m,x,E;function k(){if(s)return h;s=1;var e=function(){if(i)return u;function e(e,o,r){e instanceof RegExp&&(e=t(e,r)),o instanceof RegExp&&(o=t(o,r));var a=n(e,o,r);return a&&{start:a[0],end:a[1],pre:r.slice(0,a[0]),body:r.slice(a[0]+e.length,a[1]),post:r.slice(a[1]+o.length)}}function t(e,t){var n=t.match(e);return n?n[0]:null}function n(e,t,n){var o,r,a,u,i,h=n.indexOf(e),s=n.indexOf(t,h+1),p=h;if(h>=0&&s>0){if(e===t)return[h,s];for(o=[],a=n.length;p>=0&&!i;)p==h?(o.push(p),h=n.indexOf(e,p+1)):1==o.length?i=[o.pop(),s]:((r=o.pop())<a&&(a=r,u=s),s=n.indexOf(t,p+1)),p=h<s&&h>=0?h:s;o.length&&(i=[a,u])}return i}return i=1,u=e,e.range=n,u}();function t(n,o,r){var a=n;return function(t,n){var o=[],r="string"==typeof n?new RegExp("\\b("+n+")\\("):n;do{var a=r.exec(t);if(!a)return o;if(void 0===a[1])throw new Error("Missing the first couple of parenthesis to get the function identifier in "+n);var u=a[1],i=a.index,h=e("(",")",t.substring(i));if(!h||h.start!==a[0].length-1)throw new SyntaxError(u+"(): missing closing ')' in the value '"+t+"'");o.push({matches:h,functionIdentifier:u}),t=h.post}while(r.test(t));return o}(n,o).reduce((function(e,n){return e.replace(n.functionIdentifier+"("+n.matches.body+")",function(e,n,o,r,a){return o(t(e,a,o),n,r)}(n.matches.body,n.functionIdentifier,r,a,o))}),n)}return h=t}function M(){if(v)return f;v=1;var e=function(){if(l)return p;l=1;var e=function(e){this.value=e};return e.math={isDegree:!0,acos:function(t){return e.math.isDegree?180/Math.PI*Math.acos(t):Math.acos(t)},add:function(e,t){return e+t},asin:function(t){return e.math.isDegree?180/Math.PI*Math.asin(t):Math.asin(t)},atan:function(t){return e.math.isDegree?180/Math.PI*Math.atan(t):Math.atan(t)},acosh:function(e){return Math.log(e+Math.sqrt(e*e-1))},asinh:function(e){return Math.log(e+Math.sqrt(e*e+1))},atanh:function(e){return Math.log((1+e)/(1-e))},C:function(t,n){var o=1,r=t-n,a=n;a<r&&(a=r,r=n);for(var u=a+1;u<=t;u++)o*=u;return o/e.math.fact(r)},changeSign:function(e){return-e},cos:function(t){return e.math.isDegree&&(t=e.math.toRadian(t)),Math.cos(t)},cosh:function(e){return(Math.pow(Math.E,e)+Math.pow(Math.E,-1*e))/2},div:function(e,t){return e/t},fact:function(e){if(e%1!=0)return"NaN";for(var t=1,n=2;n<=e;n++)t*=n;return t},inverse:function(e){return 1/e},log:function(e){return Math.log(e)/Math.log(10)},mod:function(e,t){return e%t},mul:function(e,t){return e*t},P:function(e,t){for(var n=1,o=Math.floor(e)-Math.floor(t)+1;o<=Math.floor(e);o++)n*=o;return n},Pi:function(e,t,n){for(var o=1,r=e;r<=t;r++)o*=Number(n.postfixEval({n:r}));return o},pow10x:function(e){for(var t=1;e--;)t*=10;return t},sigma:function(e,t,n){for(var o=0,r=e;r<=t;r++)o+=Number(n.postfixEval({n:r}));return o},sin:function(t){return e.math.isDegree&&(t=e.math.toRadian(t)),Math.sin(t)},sinh:function(e){return(Math.pow(Math.E,e)-Math.pow(Math.E,-1*e))/2},sub:function(e,t){return e-t},tan:function(t){return e.math.isDegree&&(t=e.math.toRadian(t)),Math.tan(t)},tanh:function(t){return e.sinha(t)/e.cosha(t)},toRadian:function(e){return e*Math.PI/180},and:function(e,t){return e&t}},e.Exception=function(e){this.message=e},p=e}();function t(e,t){for(var n=0;n<e.length;n++)e[n]+=t;return e}for(var n=[{token:"sin",show:"sin",type:0,value:e.math.sin},{token:"cos",show:"cos",type:0,value:e.math.cos},{token:"tan",show:"tan",type:0,value:e.math.tan},{token:"pi",show:"&pi;",type:3,value:"PI"},{token:"(",show:"(",type:4,value:"("},{token:")",show:")",type:5,value:")"},{token:"P",show:"P",type:10,value:e.math.P},{token:"C",show:"C",type:10,value:e.math.C},{token:" ",show:" ",type:14,value:" ".anchor},{token:"asin",show:"asin",type:0,value:e.math.asin},{token:"acos",show:"acos",type:0,value:e.math.acos},{token:"atan",show:"atan",type:0,value:e.math.atan},{token:"7",show:"7",type:1,value:"7"},{token:"8",show:"8",type:1,value:"8"},{token:"9",show:"9",type:1,value:"9"},{token:"int",show:"Int",type:0,value:Math.floor},{token:"cosh",show:"cosh",type:0,value:e.math.cosh},{token:"acosh",show:"acosh",type:0,value:e.math.acosh},{token:"ln",show:" ln",type:0,value:Math.log},{token:"^",show:"^",type:10,value:Math.pow},{token:"root",show:"root",type:0,value:Math.sqrt},{token:"4",show:"4",type:1,value:"4"},{token:"5",show:"5",type:1,value:"5"},{token:"6",show:"6",type:1,value:"6"},{token:"/",show:"&divide;",type:2,value:e.math.div},{token:"!",show:"!",type:7,value:e.math.fact},{token:"tanh",show:"tanh",type:0,value:e.math.tanh},{token:"atanh",show:"atanh",type:0,value:e.math.atanh},{token:"Mod",show:" Mod ",type:2,value:e.math.mod},{token:"1",show:"1",type:1,value:"1"},{token:"2",show:"2",type:1,value:"2"},{token:"3",show:"3",type:1,value:"3"},{token:"*",show:"&times;",type:2,value:e.math.mul},{token:"sinh",show:"sinh",type:0,value:e.math.sinh},{token:"asinh",show:"asinh",type:0,value:e.math.asinh},{token:"e",show:"e",type:3,value:"E"},{token:"log",show:" log",type:0,value:e.math.log},{token:"0",show:"0",type:1,value:"0"},{token:".",show:".",type:6,value:"."},{token:"+",show:"+",type:9,value:e.math.add},{token:"-",show:"-",type:9,value:e.math.sub},{token:",",show:",",type:11,value:","},{token:"Sigma",show:"&Sigma;",type:12,value:e.math.sigma},{token:"n",show:"n",type:13,value:"n"},{token:"Pi",show:"&Pi;",type:12,value:e.math.Pi},{token:"pow",show:"pow",type:8,value:Math.pow,numberOfArguments:2},{token:"&",show:"&",type:9,value:e.math.and}],o={0:11,1:0,2:3,3:0,4:0,5:0,6:0,7:11,8:11,9:1,10:10,11:0,12:11,13:0,14:-1,15:11},r=0;r<n.length;r++)n[r].precedence=o[n[r].type];var a={0:!0,1:!0,3:!0,4:!0,6:!0,8:!0,9:!0,12:!0,13:!0,14:!0,15:!0},u={0:!0,1:!0,2:!0,3:!0,4:!0,5:!0,6:!0,7:!0,8:!0,9:!0,10:!0,11:!0,12:!0,13:!0,15:!0},i={0:!0,3:!0,4:!0,8:!0,12:!0,13:!0,15:!0},h={},s={0:!0,1:!0,3:!0,4:!0,6:!0,8:!0,12:!0,13:!0,15:!0},c={1:!0},y=[[],["1","2","3","7","8","9","4","5","6","+","-","*","/","(",")","^","!","P","C","e","0",".",",","n"," ","&"],["pi","ln","Pi"],["sin","cos","tan","Del","int","Mod","log","pow"],["asin","acos","atan","cosh","root","tanh","sinh"],["acosh","atanh","asinh","Sigma"]];function g(e,t,n,o){for(var r=0;r<o;r++)if(e[n+r]!==t[r])return!1;return!0}function w(e,t){for(var n=0;n<t.length;n++)if(t[n].token===e)return n;return-1}e.tokenTypes={FUNCTION_WITH_ONE_ARG:0,NUMBER:1,BINARY_OPERATOR_HIGH_PRECENDENCE:2,CONSTANT:3,OPENING_PARENTHESIS:4,CLOSING_PARENTHESIS:5,DECIMAL:6,POSTFIX_FUNCTION_WITH_ONE_ARG:7,FUNCTION_WITH_N_ARGS:8,BINARY_OPERATOR_LOW_PRECENDENCE:9,BINARY_OPERATOR_PERMUTATION:10,COMMA:11,EVALUATED_FUNCTION:12,EVALUATED_FUNCTION_PARAMETER:13,SPACE:14},e.addToken=function(t){for(var r=0;r<t.length;r++){var a=t[r].token.length,u=-1;t[r].type===e.tokenTypes.FUNCTION_WITH_N_ARGS&&void 0===t[r].numberOfArguments&&(t[r].numberOfArguments=2),y[a]=y[a]||[];for(var i=0;i<y[a].length;i++)if(t[r].token===y[a][i]){u=w(y[a][i],n);break}-1===u?(n.push(t[r]),t[r].precedence=o[t[r].type],y.length<=t[r].token.length&&(y[t[r].token.length]=[]),y[t[r].token.length].push(t[r].token)):(n[u]=t[r],t[r].precedence=o[t[r].type])}};var d={value:e.math.changeSign,type:0,pre:21,show:"-"},m={value:")",show:")",type:5,pre:0},x={value:"(",type:4,pre:0,show:"("};return e.lex=function(o,r){var p,l=[x],f=[],v=o,E=a,k=0,M=h,O="";void 0!==r&&e.addToken(r);var b={},A=function(t){for(var o,r,a,u=[],i=t.length,h=0;h<i;h++)if(!(h<i-1&&" "===t[h]&&" "===t[h+1])){for(o="",r=t.length-h>y.length-2?y.length-1:t.length-h;r>0;r--)if(void 0!==y[r])for(a=0;a<y[r].length;a++)g(t,y[r][a],h,r)&&(o=y[r][a],a=y[r].length,r=0);if(h+=o.length-1,""===o)throw new e.Exception("Can't understand after "+t.slice(h));u.push(n[w(o,n)])}return u}(v);for(p=0;p<A.length;p++){var N=A[p];if(14!==N.type){var P,T=N.token,R=N.type,I=N.value,_=N.precedence,C=N.show,S=l[l.length-1];for(P=f.length;P--&&0===f[P];)if(-1!==[0,2,3,4,5,9,11,12,13].indexOf(R)){if(!0!==E[R])throw new e.Exception(T+" is not allowed after "+O);l.push(m),E=u,M=s,f.pop()}if(!0!==E[R])throw new e.Exception(T+" is not allowed after "+O);if(!0===M[R]&&(R=2,I=e.math.mul,C="&times;",_=3,p-=1),b={value:I,type:R,pre:_,show:C,numberOfArguments:N.numberOfArguments},0===R)E=a,M=h,t(f,2),l.push(b),4!==A[p+1].type&&(l.push(x),f.push(2));else if(1===R)1===S.type?(S.value+=I,t(f,1)):l.push(b),E=u,M=i;else if(2===R)E=a,M=h,t(f,2),l.push(b);else if(3===R)l.push(b),E=u,M=s;else if(4===R)t(f,1),k++,E=a,M=h,l.push(b);else if(5===R){if(!k)throw new e.Exception("Closing parenthesis are more than opening one, wait What!!!");k--,E=u,M=s,l.push(b),t(f,1)}else if(6===R){if(S.hasDec)throw new e.Exception("Two decimals are not allowed in one number");1!==S.type&&(S={value:0,type:1,pre:0},l.push(S)),E=c,t(f,1),M=h,S.value+=I,S.hasDec=!0}else 7===R&&(E=u,M=s,t(f,1),l.push(b));8===R?(E=a,M=h,t(f,N.numberOfArguments+2),l.push(b),4!==A[p+1].type&&(l.push(x),f.push(N.numberOfArguments+2))):9===R?(9===S.type?S.value===e.math.add?(S.value=I,S.show=C,t(f,1)):S.value===e.math.sub&&"-"===C&&(S.value=e.math.add,S.show="+",t(f,1)):5!==S.type&&7!==S.type&&1!==S.type&&3!==S.type&&13!==S.type?"-"===T&&(E=a,M=h,t(f,2).push(2),l.push(d),l.push(x)):(l.push(b),t(f,2)),E=a,M=h):10===R?(E=a,M=h,t(f,2),l.push(b)):11===R?(E=a,M=h,l.push(b)):12===R?(E=a,M=h,t(f,6),l.push(b),4!==A[p+1].type&&(l.push(x),f.push(6))):13===R&&(E=u,M=s,l.push(b)),t(f,-1),O=T}else if(p>0&&p<A.length-1&&1===A[p+1].type&&(1===A[p-1].type||6===A[p-1].type))throw new e.Exception("Unexpected Space")}for(P=f.length;P--;)l.push(m);if(!0!==E[5])throw new e.Exception("complete the expression");for(;k--;)l.push(m);return l.push(m),new e(l)},f=e}function O(){if(w)return g;w=1;var e=function(){if(y)return c;y=1;var e=M();return e.prototype.toPostfix=function(){for(var t,n,o,r,a,u=[],i=[{value:"(",type:4,pre:0}],h=this.value,s=1;s<h.length;s++)if(1===h[s].type||3===h[s].type||13===h[s].type)1===h[s].type&&(h[s].value=Number(h[s].value)),u.push(h[s]);else if(4===h[s].type)i.push(h[s]);else if(5===h[s].type)for(;4!==(n=i.pop()).type;)u.push(n);else if(11===h[s].type){for(;4!==(n=i.pop()).type;)u.push(n);i.push(n)}else{r=(t=h[s]).pre,o=(a=i[i.length-1]).pre;var p="Math.pow"==a.value&&"Math.pow"==t.value;if(r>o)i.push(t);else{for(;o>=r&&!p||p&&r<o;)n=i.pop(),a=i[i.length-1],u.push(n),o=a.pre,p="Math.pow"==t.value&&"Math.pow"==a.value;i.push(t)}}return new e(u)},c=e}();return e.prototype.postfixEval=function(t){(t=t||{}).PI=Math.PI,t.E=Math.E;for(var n,o,r,a=[],u=this.value,i=void 0!==t.n,h=0;h<u.length;h++)if(1===u[h].type)a.push({value:u[h].value,type:1});else if(3===u[h].type)a.push({value:t[u[h].value],type:1});else if(0===u[h].type)void 0===a[a.length-1].type?a[a.length-1].value.push(u[h]):a[a.length-1].value=u[h].value(a[a.length-1].value);else if(7===u[h].type)void 0===a[a.length-1].type?a[a.length-1].value.push(u[h]):a[a.length-1].value=u[h].value(a[a.length-1].value);else if(8===u[h].type){for(var s=[],p=0;p<u[h].numberOfArguments;p++)s.push(a.pop().value);a.push({type:1,value:u[h].value.apply(u[h],s.reverse())})}else 10===u[h].type?(n=a.pop(),void 0===(o=a.pop()).type?(o.value=o.concat(n),o.value.push(u[h]),a.push(o)):void 0===n.type?(n.unshift(o),n.push(u[h]),a.push(n)):a.push({type:1,value:u[h].value(o.value,n.value)})):2===u[h].type||9===u[h].type?(n=a.pop(),void 0===(o=a.pop()).type?((o=o.concat(n)).push(u[h]),a.push(o)):void 0===n.type?(n.unshift(o),n.push(u[h]),a.push(n)):a.push({type:1,value:u[h].value(o.value,n.value)})):12===u[h].type?(void 0!==(n=a.pop()).type&&(n=[n]),o=a.pop(),r=a.pop(),a.push({type:1,value:u[h].value(r.value,o.value,new e(n))})):13===u[h].type&&(i?a.push({value:t[u[h].value],type:3}):a.push([u[h]]));if(a.length>1)throw new e.Exception("Uncaught Syntax error");return a[0].value>1e15?"Infinity":parseFloat(a[0].value.toFixed(15))},e.eval=function(e,t,n){return void 0===t?this.lex(e).toPostfix().postfixEval():void 0===n?void 0!==t.length?this.lex(e,t).toPostfix().postfixEval():this.lex(e).toPostfix().postfixEval(t):this.lex(e,t).toPostfix().postfixEval(n)},g=e}const b=e(function(){if(E)return x;E=1;var e,t=function(){if(a)return r;function e(e,o,r){e instanceof RegExp&&(e=t(e,r)),o instanceof RegExp&&(o=t(o,r));var a=n(e,o,r);return a&&{start:a[0],end:a[1],pre:r.slice(0,a[0]),body:r.slice(a[0]+e.length,a[1]),post:r.slice(a[1]+o.length)}}function t(e,t){var n=t.match(e);return n?n[0]:null}function n(e,t,n){var o,r,a,u,i,h=n.indexOf(e),s=n.indexOf(t,h+1),p=h;if(h>=0&&s>0){for(o=[],a=n.length;p>=0&&!i;)p==h?(o.push(p),h=n.indexOf(e,p+1)):1==o.length?i=[o.pop(),s]:((r=o.pop())<a&&(a=r,u=s),s=n.indexOf(t,p+1)),p=h<s&&h>=0?h:s;o.length&&(i=[a,u])}return i}return a=1,r=e,e.range=n,r}(),n=k(),o=function(){if(m)return d;m=1;var e=O();return e.prototype.formulaEval=function(){for(var e,t,n,o=[],r=this.value,a=0;a<r.length;a++)1===r[a].type||3===r[a].type?o.push({value:3===r[a].type?r[a].show:r[a].value,type:1}):13===r[a].type?o.push({value:r[a].show,type:1}):0===r[a].type?o[o.length-1]={value:r[a].show+("-"!=r[a].show?"(":"")+o[o.length-1].value+("-"!=r[a].show?")":""),type:0}:7===r[a].type?o[o.length-1]={value:(1!=o[o.length-1].type?"(":"")+o[o.length-1].value+(1!=o[o.length-1].type?")":"")+r[a].show,type:7}:10===r[a].type?(e=o.pop(),t=o.pop(),"P"===r[a].show||"C"===r[a].show?o.push({value:"<sup>"+t.value+"</sup>"+r[a].show+"<sub>"+e.value+"</sub>",type:10}):o.push({value:(1!=t.type?"(":"")+t.value+(1!=t.type?")":"")+"<sup>"+e.value+"</sup>",type:1})):2===r[a].type||9===r[a].type?(e=o.pop(),t=o.pop(),o.push({value:(1!=t.type?"(":"")+t.value+(1!=t.type?")":"")+r[a].show+(1!=e.type?"(":"")+e.value+(1!=e.type?")":""),type:r[a].type})):12===r[a].type&&(e=o.pop(),t=o.pop(),n=o.pop(),o.push({value:r[a].show+"("+n.value+","+t.value+","+e.value+")",type:12}));return o[0].value},d=e}(),u=/(\+|\-|\*|\\|[^a-z]|)(\s*)(\()/g;return x=function(r,a){function i(n,r,h){if(e++>100)throw e=0,new Error("Call stack overflow for "+h);if(""===n)throw new Error(r+"(): '"+h+"' must contain a non-whitespace string");n=function(e,n){e=e.replace(/((?:\-[a-z]+\-)?calc)/g,"");var o,r="",a=e;for(;o=u.exec(a);){o[0].index>0&&(r+=a.substring(0,o[0].index));var h=t("(",")",a.substring([0].index));if(""===h.body)throw new Error("'"+e+"' must contain a non-whitespace string");var s=i(h.body,"",n);r+=h.pre+s,a=h.post}return r+a}(n,h);var s=function(e){var t=[],n=[],o=/[\.0-9]([%a-z]+)/gi,r=o.exec(e);for(;r;)r&&r[1]&&(-1===n.indexOf(r[1].toLowerCase())&&(t.push(r[1]),n.push(r[1].toLowerCase())),r=o.exec(e));return t}(n);if(s.length>1||n.indexOf("var(")>-1)return r+"("+n+")";var p=s[0]||"";"%"===p&&(n=n.replace(/\b[0-9\.]+%/g,(function(e){return.01*parseFloat(e.slice(0,-1))})));var l,f=n.replace(new RegExp(p,"gi"),"");try{l=o.eval(f)}catch(v){return r+"("+n+")"}return"%"===p&&(l*=100),(r.length||"%"===p)&&(l=Math.round(l*a)/a),l+=p}return e=0,a=Math.pow(10,void 0===a?5:a),r=r.replace(/\n+/g," "),n(r,/((?:\-[a-z]+\-)?calc)\(/,i)}}());var A,N;var P=function(){if(N)return A;N=1;var e=t();function n(t,o){if("function"!=typeof t||null!=o&&"function"!=typeof o)throw new TypeError("Expected a function");var r=function(){var e=arguments,n=o?o.apply(this,e):e[0],a=r.cache;if(a.has(n))return a.get(n);var u=t.apply(this,e);return r.cache=a.set(n,u)||a,u};return r.cache=new(n.Cache||e),r}return n.Cache=e,A=n}();var T="__react_svg_text_measurement_id";const R=e(P)((function(e,t){try{var n=document.getElementById(T);if(!n){var o=document.createElementNS("http://www.w3.org/2000/svg","svg");o.setAttribute("aria-hidden","true"),o.style.width="0",o.style.height="0",o.style.position="absolute",o.style.top="-100%",o.style.left="-100%",(n=document.createElementNS("http://www.w3.org/2000/svg","text")).setAttribute("id",T),o.appendChild(n),document.body.appendChild(o)}return Object.assign(n.style,t),n.textContent=e,n.getComputedTextLength()}catch(r){return null}}),(function(e,t){return e+"_"+JSON.stringify(t)}));var I=["verticalAnchor","scaleToFit","angle","width","lineHeight","capHeight","children","style"];function _(e){return"number"==typeof e}function C(e){return"number"==typeof e&&Number.isFinite(e)||"string"==typeof e}function S(e){var t=e.verticalAnchor,o=void 0===t?"end":t,r=e.scaleToFit,a=void 0!==r&&r,u=e.angle,i=e.width,h=e.lineHeight,s=void 0===h?"1em":h,p=e.capHeight,l=void 0===p?"0.71em":p,f=e.children,v=e.style,c=function(e,t){if(null==e)return{};var n,o,r={},a=Object.keys(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,I),y=c.x,g=void 0===y?0:y,w=c.y,d=void 0===w?0:w,m=!C(g)||!C(d),x=n.useMemo((function(){return{wordsWithWidth:(null==f?[]:f.toString().split(/(?:(?!\u00A0+)\s+)/)).map((function(e){return{word:e,wordWidth:R(e,v)||0}})),spaceWidth:R(" ",v)||0}}),[f,v]),E=x.wordsWithWidth,k=x.spaceWidth,M=n.useMemo((function(){return m?[]:i||a?E.reduce((function(e,t){var n=t.word,o=t.wordWidth,r=e[e.length-1];if(r&&(null==i||a||(r.width||0)+o+k<i))r.words.push(n),r.width=r.width||0,r.width+=o+k;else{var u={words:[n],width:o};e.push(u)}return e}),[]):[{words:null==f?[]:f.toString().split(/(?:(?!\u00A0+)\s+)/)}]}),[m,i,a,f,E,k]),O=n.useMemo((function(){return m?"":b("start"===o?"calc("+l+")":"middle"===o?"calc("+(M.length-1)/2+" * -"+s+" + ("+l+" / 2))":"calc("+(M.length-1)+" * -"+s+")")}),[m,o,l,M.length,s]),A=n.useMemo((function(){var e=[];if(m)return"";if(_(g)&&_(d)&&_(i)&&a&&M.length>0){var t=M[0].width||1,n="shrink-only"===a?Math.min(i/t,1):i/t,o=n,r=g-n*g,h=d-o*d;e.push("matrix("+n+", 0, 0, "+o+", "+r+", "+h+")")}return u&&e.push("rotate("+u+", "+g+", "+d+")"),e.length>0?e.join(" "):""}),[m,g,d,i,a,M,u]);return{wordsByLines:M,startDy:O,transform:A}}var D=["dx","dy","textAnchor","innerRef","innerTextRef","verticalAnchor","angle","lineHeight","scaleToFit","capHeight","width"];function H(){return H=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},H.apply(this,arguments)}var F={overflow:"visible"};function W(e){var t=e.dx,n=void 0===t?0:t,r=e.dy,a=void 0===r?0:r,u=e.textAnchor,i=void 0===u?"start":u,h=e.innerRef,s=e.innerTextRef;e.verticalAnchor,e.angle;var p=e.lineHeight,l=void 0===p?"1em":p;e.scaleToFit,e.capHeight,e.width;var f=function(e,t){if(null==e)return{};var n,o,r={},a=Object.keys(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,D),v=f.x,c=void 0===v?0:v,y=f.fontSize,g=S(e),w=g.wordsByLines,d=g.startDy,m=g.transform;return o.createElement("svg",{ref:h,x:n,y:a,fontSize:y,style:F},w.length>0?o.createElement("text",H({ref:s,transform:m},f,{textAnchor:i}),w.map((function(e,t){return o.createElement("tspan",{key:t,x:c,dy:0===t?d:l},e.words.join(" "))}))):null)}export{W as Text,R as getStringWidth,S as useText};
