import{r as e,b0 as n,bg as t,b5 as i}from"./index-Cak6rALw.js";import{u as s,d as r,t as h,c as o,S as a,b as l}from"./MyApp-DW5WH4Ub.js";import c from"./useCacheState-CAnxkewm.js";import{copyWithMessage as d}from"./copy-D4Fd8-zB.js";import{S as g}from"./Screen-Buq7HJpK.js";import{P as m}from"./index-PbLYNhdQ.js";import{a as u}from"./index-QU7lSj_a.js";import{I as v}from"./index-Bg865k-U.js";import{D as f}from"./index-CC5caqt_.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CxAc8H5Q.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";const p=["󠁢","󠁣","󠁤","󠁥","󠁦","󠁧","󠁨","󠁩","󠁪","󠁫","󠁬","󠁭","󠁮","󠁯","󠁰","󠁱","󠁲","󠁳","󠁴","󠁵","󠁶","󠁷","󠁸","󠁹","󠁺","󠁿"],x=/>(.+?)</g,b=new RegExp(`‌([${p.join("")}]+?)󠁡`),j=p.reduce(((e,n,t)=>(e[n]=t,e)),{}),w=p.length,k=((e,n)=>{let t=0,i=1;for(;i<n;)i*=e,t++;return t})(w,1114112),y=(e,n="")=>{let t="";for(let i=0;i<e.length;i++)t+=String.fromCharCode(e.charCodeAt(i)^n.charCodeAt(i%n.length));return t},T="!FBAIO!",C=e=>{let n=e.codePointAt(0),t=[];for(;n>0;)t.push(n%w),n=Math.floor(n/w);for(;t.length<k;)t.push(0);return t.reverse()},I=e=>e.reduce(((e,n)=>e+p[n]),""),E=(e,n="")=>{const t=y(T+e,n);let i=[];for(let s of t)i.push(C(s));return"‌"+i.map(I).join("")+"󠁡"},N=e=>{e=e.reverse();let n=1,t=0;for(let i of e)t+=i*n,n*=w;return String.fromCodePoint(t)},S=(e,n="")=>{const t=b.exec(e);if(!t)throw new Error("No encoded content found.");e=t[1];let i=[],s="";for(let h of e)i.push(j[h]),i.length>=k&&(s+=N(i),i=[]);const r=y(s,n);if(!r.startsWith(T))throw new Error("Invalid password!");return r.slice(7)},M=e=>b.test(e);function A(){const{ti:i}=s(),{message:d}=r(),[p,j]=c("InvisibleText.message",""),[w,k]=c("InvisibleText.output",""),[y,T]=c("InvisibleText.partial",!1),[C,I]=c("InvisibleText.mode",0),[N,A]=c("InvisibleText.pass",""),[P,z]=c("InvisibleText.err",""),R=e.useRef(null),B=e.useRef(null);e.useEffect((()=>{h("InvisibleText.onLoad")}),[]),e.useEffect((()=>{if(0===C)try{k(((e,n="",t=!1)=>{if(!e)return"";if(!t)return E(e,n);let i=x.exec(e);return i?e.replace(x,E(i[1],n)):e})(p,N,y)),z("")}catch(e){z(e.message)}}),[p,C,y,N]),e.useEffect((()=>{if(1===C)try{j(((e,n="",t=!1)=>e?t?M(e)?e.replace(b,`>${S(e,n)}<`):e:S(e,n):"")(w,N,y)),z("")}catch(e){z(e.message),j("")}}),[w,C,y,N]),e.useEffect((()=>{var e,n;0===C?(null==(e=R.current)||e.focus(),z("")):1===C&&(null==(n=B.current)||n.focus())}),[C]);const L=[{label:i({en:"❓ What is an invisible message?",vi:"❓ Tin nhắn tàng hình là gì?"}),children:i({en:"An invisible message is a piece of text that uses zero-width characters to hide content within messages, texts, or posts that are not visible to the naked eye.",vi:"Tin nhắn tàng hình là một đoạn văn bản sử dụng ký tự vô hình (zero-width characters) để ẩn nội dung trong các tin nhắn, văn bản hoặc bài đăng mà mắt thường không thể thấy được."})},{label:i({en:"🎯 What is this feature used for?",vi:"🎯 Chức năng này dùng để làm gì?"}),children:n.jsxs(n.Fragment,{children:[i({en:"It helps you send secret messages, mark hidden content, or create an invisible signature in text without anyone noticing.",vi:"Nó giúp bạn gửi tin nhắn bí mật, đánh dấu nội dung ẩn, hoặc tạo dấu hiệu nhận biết trong văn bản mà không ai nhận ra."})," ",n.jsxs(o.Link,{href:"https://www.facebook.com/groups/fbaio/posts/1578525616135454",target:"_blank",children:[i({en:"Learn more",vi:"Xem thêm"})," ",n.jsx("i",{className:"fa-solid fa-external-link"})]})]})},{label:i({en:"🔐 How do I CREATE invisible messages?",vi:"🔐 Làm thế nào để TẠO tin nhắn tàng hình?"}),children:i({en:"Switch to Encode mode, paste the message into the first input box, enter password (if needed). The invisible result will be shown at the third input box. Copy and send to the recipient.",vi:"Ở Chế độ Mã hoá, nhập tin nhắn bạn muốn ẩn vào ô thứ 1, thêm mật khẩu (nếu muốn), kết quả tàng hình sẽ hiện ở ô thứ 3. Sao chép và gửi cho người nhận."})},{label:i({en:"🔍 How can I REVEAL an invisible message?",vi:"🔍 Làm thế nào để ĐỌC tin nhắn tàng hình?"}),children:i({en:"Switch to Decode mode, paste the message into the third input box, enter password (if needed). The original message will be shown at the first input box.",vi:"Chuyển sang Chế độ Giải mã, nhập tin nhắn tàng hình vào ô thứ 3, nhập mật khẩu (nếu cần), tin nhắn gốc sẽ hiện ở ô thứ 1."})},{label:i({en:"🧩 What is partially invisible?",vi:"🧩 Tàng hình 1 phần là gì?"}),children:i({en:"When you want to hide a small part of a message, you can enable `Partially invisible`. Messages that are between >< will be hidden, but the remaining characters will be visible.",vi:"Nếu bạn muốn tàng hình 1 đoạn nhỏ trong tin nhắn, hãy bật `Tàng hình 1 phần`, những tin nhắn được bọc bằng dấu >< sẽ được tàng hình, các ký tự còn lại được giữ nguyên."})},{label:i({en:"🛡️ Is an invisible message safe?",vi:"🛡️ Tin nhắn tàng hình có an toàn không?"}),children:i({en:"Basically, it cannot be detected by the naked eye. However, some tools or software can detect hidden characters.",vi:"Về cơ bản, nó không thể bị phát hiện bằng mắt thường. Tuy nhiên, một số công cụ hoặc phần mềm kiểm tra ký tự ẩn có thể nhận diện được."})},{label:i({en:"📱 Does an invisible message work on all platforms?",vi:"📱 Tin nhắn tàng hình có hoạt động trên mọi nền tảng không?"}),children:i({en:"Most text platforms like Facebook, Twitter, Messenger, Zalo, and Telegram support zero-width characters, but some may automatically remove them.",vi:"Hầu hết các nền tảng văn bản như Facebook, Twitter, Messenger, Zalo, Telegram đều hỗ trợ ký tự zero-width, nhưng một số có thể tự động loại bỏ chúng."})},{label:i({en:"📝 What else can I use an invisible message for?",vi:"📝 Tôi có thể dùng tin nhắn tàng hình để làm gì khác?"}),children:i({en:"You can use it to insert a watermark into text, send secret codes, or mark important messages.",vi:"Bạn có thể dùng nó để chèn watermark (dấu bản quyền) vào văn bản, gửi mật mã bí mật, hoặc đánh dấu tin nhắn quan trọng."})}],O=n.jsx("div",{style:{display:"flex",justifyContent:"center"},children:n.jsx("i",{className:"fa-solid fa-arrow-"+(0===C?"down":"up")})});return n.jsx(g,{mode:"center",title:i({en:"Invisible Text",vi:"Tin nhắn tàng hình"}),children:n.jsxs(a,{style:{maxWidth:400},direction:"vertical",children:[n.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[n.jsx(m,{title:i({en:"Partially invisible",vi:"Tàng hình 1 phần"}),content:n.jsxs(n.Fragment,{children:[n.jsxs("ul",{children:[n.jsx("li",{children:i({vi:"BÂT: Chỉ tàng hình những ký tự nằm trong cặp dấu ><",en:"ON: Make invisible only characters between ><"})}),n.jsx("li",{children:i({vi:"TẮT: Tàng hình tất cả ký tự",en:"OFF: Make every character invisible"})})]}),i({en:n.jsxs(n.Fragment,{children:["eg ON: ",n.jsx(l,{children:"Password is >123< 456"}),n.jsx("i",{className:"fa-solid fa-arrow-right"})," ",n.jsx(l,{children:"Password is 456"})]}),vi:n.jsxs(n.Fragment,{children:["ví dụ khi BẬT: ",n.jsx(l,{children:"Mật khẩu là >123< 456"}),n.jsx("i",{className:"fa-solid fa-arrow-right"})," ",n.jsx(l,{children:"Mật khẩu là 456"})]})})]}),children:n.jsxs(u,{checked:y,onChange:e=>T(e.target.checked),children:[n.jsx("i",{className:"fa-solid fa-puzzle-piece"})," ",i({en:"Partial mode",vi:"Tàng hình 1 phần"})]})}),n.jsx(m,{title:n.jsx("i",{children:i({en:"Click to switch mode",vi:"Click để đổi chế độ"})}),content:i(0===C?{en:"Encode mode: Generate invisible text from original text",vi:"Chế độ mã hoá: Tạo tin nhắn tàng hình từ tin nhắn gốc"}:{en:"Decode mode: Reveal original text from invisible text",vi:"Chế độ giải mã: Hiển thị tin nhắn gốc từ tin nhắn tàng hình"}),children:n.jsxs(l,{color:0===C?"success":"warning",style:{cursor:"pointer"},onClick:()=>I(0===C?1:0),children:[0===C?n.jsx("i",{className:"fas fa-lock"}):n.jsx("i",{className:"fas fa-lock-open"})," ",i(0===C?{en:"Encode mode",vi:"Chế độ Mã hoá"}:{en:"Decode mode",vi:"Chế độ Giải mã"})]})})]}),n.jsx(v.TextArea,{ref:R,value:p,onChange:e=>j(e.target.value),rows:10,autoSize:{minRows:3},placeholder:i(0===C?{en:"Enter the message you want to encode",vi:"Nhập tin nhắn bạn muốn tàng hình"}:{en:"> Decode result goes here...",vi:"> Kết quả giải mã sẽ hiện ở đây..."}),onFocus:()=>I(0)}),O,n.jsx(v.Password,{placeholder:i(0===C?{en:"Add password",vi:"Thêm mật khẩu"}:{en:"Password to decode",vi:"Nhập mật khẩu giải mã"}),value:N,onChange:e=>A(e.target.value),status:P.length>0?"error":void 0,prefix:n.jsx("i",{className:"fa-solid fa-lock"})}),P.length>0&&n.jsx(l,{color:"error",children:P}),O,n.jsx(v.TextArea,{ref:B,value:w,onChange:e=>k(e.target.value),rows:10,autoSize:{minRows:3},placeholder:i(1===C?{en:"Enter the message you want to decode",vi:"Nhập tin nhắn bạn muốn giải mã"}:{en:"> Invisible result goes here...",vi:"> Kết quả tàng hình sẽ hiện ở đây..."}),onFocus:()=>I(1)}),w.length>0&&n.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[n.jsx(F,{text:w,message:d,ti:i}),n.jsxs(l,{children:[w.length,i({en:" characters",vi:" ký tự"})]})]}),n.jsx(f,{}),n.jsxs(a,{align:"center",direction:"vertical",children:[n.jsx(o.Title,{level:3,style:{margin:0,alignSelf:"center"},children:"FAQ"}),n.jsx(t,{items:L,accordion:!0,expandIconPosition:"end",style:{marginBottom:200}})]})]})})}function F({text:e,message:t,ti:s}){return 0===e.length?null:n.jsx(i,{onClick:()=>{h("InvisibleText:Copy"),d(e)},icon:n.jsx("i",{className:"fa-solid fa-copy"}),children:s({en:"Copy",vi:"Sao chép"})})}export{A as default};
