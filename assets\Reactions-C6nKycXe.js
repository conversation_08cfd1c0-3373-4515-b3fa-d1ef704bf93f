import{r as e,b0 as n,b5 as i,bi as t}from"./index-Cak6rALw.js";import{u as o,d as s,t as l,T as a,S as r,g as c,aF as d,v as u,c as m,b as h,s as g,o as f}from"./MyApp-DW5WH4Ub.js";import{g as x,a as v,b as j,R as p,c as k,d as y,F as b,e as C,S as F}from"./reactions-CR2nCZPF.js";import S from"./MyTable-DnjDmN7z.js";import{E as R}from"./ExportButton-CbyepAf_.js";import w from"./WordStatisticButton-D2UxB2M4.js";import B from"./useCacheState-CAnxkewm.js";import{u as T}from"./useAction-uz-MrZwJ.js";import{u as I}from"./useDevMode-yj0U7_8D.js";import{unfriend as A,addFriend as N}from"./friends-CRlBxmhf.js";import{u as $,a as U,f as E}from"./follows-DFGgshWa.js";import{c as q}from"./friendRequests-DyD2rqMz.js";import{A as L}from"./index-DdM4T8-Q.js";import{R as _}from"./row-BMfM-of4.js";import{T as P}from"./index-IxNfZAD-.js";import{I as D}from"./index-CDSnY0KE.js";import{T as M}from"./index-BzMPigdO.js";import"./index-ByRdMNW-.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./DownOutlined-B-JcS-29.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./BadgeWrapper-BCbYXXfB.js";import"./index-Bp2s1Wws.js";import"./index-Bx3o6rwA.js";import"./useVIP-D5FAMGQQ.js";import"./sweetalert2.esm.all-BZxvatOx.js";import"./getIds-DAzc-wzf.js";import"./index-CC5caqt_.js";import"./gender-D7cqwEK5.js";import"./index-PbLYNhdQ.js";function O({target:t,postId:d}){const{ti:u}=o(),{message:m}=s(),[h,g]=B(`Reactions.${d}.activeTab`,""),[f,k]=B(`Reactions.${d}.reactions`,[]),[y,b]=e.useState(!1);e.useEffect((()=>{l("Reactions:onLoad")}),[]),e.useEffect((()=>{!f.length&&d&&(g(""),C())}),[d,f]);const C=async()=>{if(!d)return;b(!0);const e="Reactions:loadReactions";l(e),m.loading({key:e,content:u({en:"Loading reactions...",vi:"Đang tải cảm xúc..."})});try{let n=await x(d);(null==n?void 0:n.length)||(n=await v(d));const i=[{reactionId:j.ALL,count:n.reduce(((e,n)=>e+n.count),0),users:[]},...n];k(i),m.success({key:e,content:u({en:"Reactions loaded successfully: ",vi:"Tải cảm xúc thành công: "})+i[0].count})}catch(n){m.error({key:e,content:u({en:"Failed to load reactions: ",vi:"Lỗi tải cảm xúc: "})+((null==n?void 0:n.message)||"")})}finally{b(!1)}},F=e.useMemo((()=>f.map((e=>{const i=p[e.reactionId];return{key:d+"-"+(e.reactionId||"all"),label:n.jsx(a,{title:u(null==i?void 0:i.display_name),children:n.jsxs(r,{direction:"vertical",align:"center",size:3,style:{minWidth:60},children:[e.icon&&n.jsx(L,{src:e.icon,size:"small"})||(null==i?void 0:i.emoji)||n.jsx("i",{className:"fa-solid fa-list"}),c(e.count)]})}),children:n.jsx(G,{reaction:e,postId:d})}}))),[f,d,u]);return n.jsxs(_,{gutter:[10,10],justify:"center",children:[n.jsxs(r.Compact,{children:[n.jsx(i,{icon:n.jsx("i",{className:"fa-solid fa-rotate-right"}),onClick:C,loading:y,children:u({en:"Reload",vi:"Làm mới"})}),n.jsx(i,{icon:n.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"}),onClick:()=>{window.open(`https://www.facebook.com/${d}`,"_blank")},children:u({en:"View Post",vi:"Xem bài viết"})})]}),n.jsx(P,{items:F,activeKey:h,onChange:e=>g(e),centered:!0,style:{width:"100%"},tabBarGutter:0,type:"line"})]})}const V=(e,n="")=>(null==e?void 0:e.uid)+"-"+n;function G({reaction:c,postId:x}){const{devMode:v}=I(),{ti:P}=o(),{message:O}=s(),{onClickAction:G,onClickBulkActions:Q}=T(),[z,H]=B(`Reactions.${x}.${c.reactionId}.users`,[]),[X,W]=e.useState(10),[J,Y]=e.useState(!1),[Z,ee]=e.useState({}),ne=e.useMemo((()=>z.map(((e,n)=>({index:n+1,...e})))),[z]);e.useEffect((()=>{z.length||ie(!0)}),[x,c,z]);const ie=async(e=!1)=>{var i,t;if(!x)return;Y(!0);const o="Reactions:loadUsers",s=o+":sleep";l(o),O.loading({key:o,duration:0,content:P({en:"Loading users like post...",vi:"Đang tải người like bài viết..."})});try{const l=e?[]:z;let a=0,r=!1,m=e?10:Math.min(c.count-z.length,X);for(;!r&&a<m;){const e=await k({postId:x,reactionId:c.reactionId,cursor:(null==(i=l[l.length-1])?void 0:i.cursor)||null});if(console.log("res",e),0===e.length)break;if(l.push(...e),H([...l]),a+=e.length,O.loading({key:o,duration:0,content:n.jsxs(n.Fragment,{children:[P({en:"Loading users like post...",vi:"Đang tải người like bài viết..."})+(null==(t=p[c.reactionId])?void 0:t.emoji)+" "+a+"/"+m,n.jsx("br",{}),P({en:"Click to stop",vi:"Bấm để dừng"})]}),onClick:()=>{r=!0}}),l.length>=c.count||a>=m)break;const h=d(2e3,6e3);await u(h,(e=>(O.loading({key:s,duration:0,content:P({en:"Waiting...",vi:"Đang chờ..."})+" "+~~(e/1e3)+"s"}),r)))}O.success({key:o,content:P({en:"Load liked users successfully: ",vi:"Tải người like thành công: "})+a})}catch(a){O.error({key:o,content:P({en:"Failed to load users like post: ",vi:"Lỗi tải người like bài viết: "})+((null==a?void 0:a.message)||"")})}finally{O.destroy(s),Y(!1)}},te=()=>{const e=Math.min(1,c.count-z.length),t=c.count-z.length,o=t<=0;return n.jsxs(r.Compact,{children:[n.jsx(i,{icon:n.jsx("i",{className:"fa-solid fa-play"}),onClick:()=>ie(!1),loading:J,disabled:o,children:P(o?{en:"No more",vi:"Không còn"}:{en:"Load more",vi:"Tải thêm"})}),n.jsx(a,{title:P({en:"Number of reactions to load more",vi:"Số lượng cảm xúc muốn tải thêm"})+` min: ${e}, max: ${t}`,children:n.jsx(M,{min:1,max:c.count-z.length,value:Math.min(X,t),onChange:e=>W(e),disabled:o})})]})},oe=(e,n,i)=>{ee((o=>t(o,(t=>{t[V(n,e)]=i}))))},se=(e,n=!1)=>(oe("onAddFriend",e,!0),G({record:e,id:e.uid,key:"Reactions:onClickAddFriend",actionFn:()=>v?u(1e3):N({targetUid:e.uid}),loadingText:()=>P({en:"Adding friend...",vi:"Đang kết bạn..."})+" "+e.name,successText:()=>P({en:"Added friend",vi:"Đã kết bạn"})+" "+e.name,failedText:()=>P({en:"Failed to add friend",vi:"Lỗi kết bạn"})+" "+e.name,onSuccess:()=>{H((n=>n.map((n=>n.uid===e.uid?{...n,friendshipStatus:b.OUTGOING_REQUEST}:n))))},finallyFn(){oe("onAddFriend",e,!1)},needConfirm:n,confirmProps:{title:P({en:"Add Friend ",vi:"Kết bạn "})+e.name+"?",text:P({en:`Are you sure to add ${e.name} as your friend?`,vi:`Bạn có chắc chắn muốn kết bạn với ${e.name} không?`})}})),le=(e,n=!1)=>(oe("onCancelRequest",e,!0),G({record:e,id:e.uid,key:"Reactions:onClickCancelRequest",actionFn:()=>v?u(1e3):q(e.uid),loadingText:()=>P({en:"Cancelling request...",vi:"Đang thu hồi yêu cầu..."})+" "+e.name,successText:()=>P({en:"Cancelled request",vi:"Đã thu hồi yêu cầu"})+" "+e.name,failedText:()=>P({en:"Failed to cancel request",vi:"Lỗi thu hồi yêu cầu"})+" "+e.name,onSuccess:()=>{H((n=>n.map((n=>n.uid===e.uid?{...n,friendshipStatus:b.CAN_REQUEST}:n))))},finallyFn(){oe("onCancelRequest",e,!1)},needConfirm:n,confirmProps:{title:P({en:"Cancel friend request?",vi:"Thu hồi yêu cầu kết bạn?"}),text:e.name}})),ae=(e,n=!1)=>(oe("onRemoveFriend",e,!0),G({record:e,id:e.uid,key:"Reactions:onClickRemoveFriend",actionFn:()=>v?u(1e3):A({targetUid:e.uid}),loadingText:()=>P({en:"Removing friend...",vi:"Đang xóa bạn bè..."})+" "+e.name,successText:()=>P({en:"Removed friend",vi:"Đã xóa bạn bè"})+" "+e.name,failedText:()=>P({en:"Failed to remove friend",vi:"Lỗi xóa bạn bè"})+" "+e.name,onSuccess:()=>{H((n=>n.map((n=>n.uid===e.uid?{...n,friendshipStatus:b.CAN_REQUEST}:n))))},finallyFn(){oe("onRemoveFriend",e,!1)},needConfirm:n,confirmProps:{title:P({en:"Remove Friend ",vi:"Xóa bạn bè "})+e.name+"?",text:P({en:`Are you sure to remove ${e.name} as your friend?`,vi:`Bạn có chắc chắn muốn xóa bạn bè với ${e.name} không?`})}})),re=(e,n=!1)=>(oe("onFollow",e,!0),G({record:e,id:e.uid,key:"Reactions:onClickFollow",actionFn:()=>v?u(1e3):E({id:e.uid,type:U.USER}),loadingText:()=>P({en:"Following...",vi:"Đang theo dõi..."})+" "+e.name,successText:()=>P({en:"Followed",vi:"Đã theo dõi"})+" "+e.name,failedText:()=>P({en:"Failed to follow",vi:"Lỗi theo dõi"})+" "+e.name,onSuccess:()=>{H((n=>n.map((n=>n.uid===e.uid?{...n,subscribeStatus:F.IS_SUBSCRIBED}:n))))},finallyFn(){oe("onFollow",e,!1)},needConfirm:n,confirmProps:{title:P({en:"Follow ",vi:"Theo dõi "})+e.name+"?",text:P({en:`Are you sure to follow ${e.name}?`,vi:`Bạn có chắc chắn muốn theo dõi ${e.name} không?`})}})),ce=(e,n=!1)=>(oe("onUnfollow",e,!0),G({record:e,id:e.uid,key:"Reactions:onClickUnfollow",actionFn:()=>v?u(1e3):$({id:e.uid,type:U.USER}),loadingText:()=>P({en:"Unfollowing...",vi:"Đang bỏ theo dõi..."})+" "+e.name,successText:()=>P({en:"Unfollowed",vi:"Đã bỏ theo dõi"})+" "+e.name,failedText:()=>P({en:"Failed to unfollow",vi:"Lỗi bỏ theo dõi"})+" "+e.name,onSuccess:()=>{H((n=>n.map((n=>n.uid===e.uid?{...n,subscribeStatus:F.CAN_SUBSCRIBE}:n))))},finallyFn(){oe("onUnfollow",e,!1)},needConfirm:n,confirmProps:{title:P({en:"Unfollow ",vi:"Bỏ theo dõi "})+e.name+"?",text:P({en:`Are you sure to unfollow ${e.name}?`,vi:`Bạn có chắc chắn muốn bỏ theo dõi ${e.name} không?`})}})),de=[{title:"#",dataIndex:"index",key:"index",render:(e,n)=>n.index,width:50},{title:P({en:"User",vi:"Người dùng"}),dataIndex:"name",key:"name",render:(e,i)=>n.jsxs(r,{children:[n.jsx(D,{src:i.avatar,style:{width:50,height:50,borderRadius:5}}),n.jsxs(r,{direction:"vertical",size:0,children:[n.jsx(m.Link,{strong:!0,href:i.url,target:"_blank",children:i.name}),n.jsx(m.Text,{type:"secondary",children:i.uid})]})]})},{title:P({en:"Reaction",vi:"Cảm xúc"}),dataIndex:"reaction",key:"reaction",render:(e,i)=>n.jsx(L,{src:i.reaction.icon,size:"small"}),filters:y.filter((e=>e!==j.ALL)).map((e=>{const n=p[e];return{text:`${z.filter((n=>n.reaction.id===e)).length} ${null==n?void 0:n.emoji} ${P(null==n?void 0:n.display_name)}`,value:e}})),onFilter:(e,n)=>n.reaction.id===e,width:100},{title:n.jsx(a,{title:P({en:"Mutual Friends",vi:"Bạn chung"}),children:P({en:"Mutual",vi:"Chung"})}),dataIndex:"mutualFriendsCount",key:"mutualFriendsCount",render:(e,n)=>n.mutualFriendsCount,rangeFilter:{getValue:e=>e.mutualFriendsCount},sorter:(e,n)=>e.mutualFriendsCount-n.mutualFriendsCount,width:100},{title:n.jsx(a,{title:P({en:"Friend status",vi:"Trạng thái bạn bè"}),children:P({en:"Friend",vi:"Bạn"})}),dataIndex:"friendshipStatus",key:"friendshipStatus",render:(e,i)=>{const t=C[i.friendshipStatus],o=n.jsx("i",{className:"fa-solid fa-minus",style:{color:"gray"}});if(t){const e=t.icon?n.jsx("i",{className:t.icon}):o;return n.jsx(a,{title:P(t),children:t.color?n.jsx(h,{color:t.color||"default",children:e}):e})}return o},filters:Object.values(b).map((e=>({text:n.jsxs(n.Fragment,{children:[n.jsx(h,{color:C[e].color||"default",children:n.jsx("i",{className:C[e].icon})}),P(C[e])+" ("+z.filter((n=>n.friendshipStatus===e)).length+")"]}),value:e}))),onFilter:(e,n)=>n.friendshipStatus===e,width:80},K({ti:P,key:"subscribeStatus",data:z,title:P({en:"Follow",vi:"Follow"}),tooltip:P({en:"Your following",vi:"Bạn đang theo dõi"}),getBoolean:e=>e.subscribeStatus===F.IS_SUBSCRIBED}),K({ti:P,key:"hasStory",data:z,title:P({en:"Story",vi:"Story"}),tooltip:P({en:"Has Story",vi:"Có story"})}),K({ti:P,key:"canMessage",data:z,title:P({en:"Chat",vi:"Chat"}),tooltip:P({en:"Can Message",vi:"Có thể nhắn tin"})}),K({ti:P,key:"isSecureThread",data:z,title:P({en:"e2ee",vi:"e2ee"}),tooltip:P({en:"End-to-end encryption chat",vi:"Tin nhắn Mã hoá đầu cuối"})}),{title:P({en:"Action",vi:"Hành động"}),dataIndex:"action",key:"action",render:(e,t)=>n.jsxs(r,{children:[n.jsxs(r.Compact,{children:[t.friendshipStatus===b.ARE_FRIENDS&&n.jsx(a,{title:P({en:"Remove Friend",vi:"Xóa bạn"}),children:n.jsx(i,{danger:!0,icon:n.jsx("i",{className:"fa-solid fa-user-minus"}),onClick:()=>ae(t,!0),loading:Z[V(t,"onRemoveFriend")]})}),t.friendshipStatus===b.OUTGOING_REQUEST&&n.jsx(a,{title:P({en:"Cancel friend request",vi:"Thu hồi yêu cầu kết bạn"}),children:n.jsx(i,{danger:!0,icon:n.jsx("i",{className:"fa-solid fa-person-circle-xmark"}),onClick:()=>le(t,!0),loading:Z[V(t,"onCancelRequest")]})}),t.subscribeStatus===F.IS_SUBSCRIBED&&n.jsx(a,{title:P({en:"Unfollow",vi:"Bỏ theo dõi"}),children:n.jsx(i,{danger:!0,icon:n.jsx("i",{className:"fa-solid fa-minus"}),onClick:()=>ce(t,!0),loading:Z[V(t,"onUnfollow")]})})]}),n.jsxs(r.Compact,{children:[t.friendshipStatus===b.CAN_REQUEST&&n.jsx(a,{title:P({en:"Add Friend",vi:"Kết bạn"}),children:n.jsx(i,{icon:n.jsx("i",{className:"fa-solid fa-user-plus"}),onClick:()=>se(t,!0),loading:Z[V(t,"onAddFriend")]})}),t.subscribeStatus===F.CAN_SUBSCRIBE&&n.jsx(a,{title:P({en:"Follow",vi:"Theo dõi"}),children:n.jsx(i,{icon:n.jsx("i",{className:"fa-solid fa-plus"}),onClick:()=>re(t,!0),loading:Z[V(t,"onFollow")]})})]})]})}];return n.jsx(S,{data:ne,columns:de,keyExtractor:e=>null==e?void 0:e.uid,size:"small",searchable:!0,searchPlaceholder:P({en:"Search name or uid",vi:"Tìm tên hoặc uid"}),selectable:!0,renderTitle:e=>{const t=e.length?e:ne,o=t.filter((e=>e.friendshipStatus===b.CAN_REQUEST)),s=t.filter((e=>e.friendshipStatus===b.ARE_FRIENDS)),l=t.filter((e=>e.friendshipStatus===b.OUTGOING_REQUEST)),d=t.filter((e=>e.subscribeStatus===F.CAN_SUBSCRIBE)),u=t.filter((e=>e.subscribeStatus===F.IS_SUBSCRIBED));return n.jsxs(r,{wrap:!0,children:[n.jsxs(r.Compact,{children:[n.jsx(i,{icon:n.jsx("i",{className:"fa-solid fa-rotate-right"}),onClick:()=>ie(!0),loading:J,children:P({en:"Reload",vi:"Làm mới"})}),n.jsx(R,{data:t.map((e=>{var n;return{...e,reaction:null==(n=p[e.reaction.id])?void 0:n.name,reactionIcon:e.reaction.icon,reactionId:e.reaction.id}})),options:[{key:"json",label:P({en:".json",vi:".json"}),prepareData:e=>{var n;return{data:JSON.stringify(e,null,2),fileName:`Reactions:${null==(n=p[c.reactionId])?void 0:n.name}-postId:${x}.json`}}},{key:"csv",label:".csv",prepareData:e=>{var n;return{data:f(e),fileName:`Reactions:${null==(n=p[c.reactionId])?void 0:n.name}-postId:${x}.csv`}}}]})]}),te(),n.jsxs(r.Compact,{children:[o.length>0&&n.jsx(a,{title:P({en:`Add Friend ${o.length} selected users`,vi:`Kết bạn ${o.length} người đang chọn`}),children:n.jsx(i,{icon:n.jsx("i",{className:"fa-solid fa-user-plus"}),onClick:()=>{return e=o,oe("onAddFriendBulk",null,!0),Q({data:e,key:"Reactions:onClickAddFriendBulk",actionFn:se,loadingText:e=>P({en:"Adding friends...",vi:"Đang kết bạn..."})+e.name,successText:()=>P({en:"Added friends",vi:"Đã kết bạn"}),successDescItem:e=>e.name,requireVIP:!0,confirmProps:{title:P({en:"Add "+e.length+" friends?",vi:"Kết bạn "+e.length+" người?"}),text:P({en:`Are you sure to add ${e.length} friends?`,vi:`Bạn có chắc chắn muốn kết bạn với ${e.length} người?`})}}).finally((()=>{oe("onAddFriendBulk",null,!1)}));var e},loading:Z.onAddFriendBulk,children:o.length})}),d.length>0&&n.jsx(a,{title:P({en:`Follow ${d.length} selected users`,vi:`Theo dõi ${d.length} người đang chọn`}),children:n.jsx(i,{icon:n.jsx("i",{className:"fa-solid fa-plus"}),onClick:()=>{return e=d,oe("onFollowBulk",null,!0),Q({data:e,key:"Reactions:onClickFollowBulk",actionFn:re,loadingText:e=>P({en:"Following...",vi:"Đang theo dõi..."})+e.name,successText:()=>P({en:"Followed",vi:"Đã theo dõi"}),successDescItem:e=>e.name,requireVIP:!0,confirmProps:{title:P({en:"Follow "+e.length+" users?",vi:"Theo dõi "+e.length+" người?"}),text:P({en:`Are you sure to follow ${e.length} users?`,vi:`Bạn có chắc chắn muốn theo dõi ${e.length} người?`})}}).finally((()=>{oe("onFollowBulk",null,!1)}));var e},loading:Z.onFollowBulk,children:d.length})}),s.length>0&&n.jsx(a,{title:P({en:`Remove Friend ${s.length} selected users`,vi:`Huỷ kết bạn ${s.length} người đang chọn`}),children:n.jsx(i,{danger:!0,icon:n.jsx("i",{className:"fa-solid fa-user-minus"}),onClick:()=>{return e=s,oe("onRemoveFriendBulk",null,!0),Q({data:e,key:"Reactions:onClickRemoveFriendBulk",actionFn:ae,loadingText:e=>P({en:"Removing friends...",vi:"Đang xóa bạn bè..."})+e.name,successText:()=>P({en:"Removed friends",vi:"Đã xóa bạn bè"}),successDescItem:e=>e.name,requireVIP:!0,confirmProps:{title:P({en:"Remove "+e.length+" friends?",vi:"Xóa bạn bè "+e.length+" người?"}),text:P({en:`Are you sure to remove ${e.length} friends?`,vi:`Bạn có chắc chắn muốn xóa bạn bè với ${e.length} người?`})}}).finally((()=>{oe("onRemoveFriendBulk",null,!1)}));var e},loading:Z.onRemoveFriendBulk,children:s.length})}),l.length>0&&n.jsx(a,{title:P({en:`Cancel friend request ${l.length} selected users`,vi:`Thu hồi yêu cầu kết bạn ${l.length} người đang chọn`}),children:n.jsx(i,{danger:!0,icon:n.jsx("i",{className:"fa-solid fa-person-circle-xmark"}),onClick:()=>{return e=l,oe("onCancelRequestBulk",null,!0),Q({data:e,key:"Reactions:onClickCancelRequestBulk",actionFn:le,loadingText:e=>P({en:"Cancelling requests...",vi:"Đang thu hồi yêu cầu..."})+e.name,successText:()=>P({en:"Cancelled requests",vi:"Đã thu hồi yêu cầu"}),successDescItem:e=>e.name,requireVIP:!0,confirmProps:{title:P({en:"Cancel "+e.length+" requests?",vi:"Thu hồi "+e.length+" yêu cầu?"}),text:P({en:`Are you sure to cancel ${e.length} requests?`,vi:`Bạn có chắc chắn muốn thu hồi ${e.length} yêu cầu?`})}}).finally((()=>{oe("onCancelRequestBulk",null,!1)}));var e},loading:Z.onCancelRequestBulk,children:l.length})}),u.length>0&&n.jsx(a,{title:P({en:`Unfollow ${u.length} selected users`,vi:`Huỷ theo dõi ${u.length} người đang chọn`}),children:n.jsx(i,{danger:!0,icon:n.jsx("i",{className:"fa-solid fa-minus"}),onClick:()=>{return e=u,oe("onUnfollowBulk",null,!0),Q({data:e,key:"Reactions:onClickUnfollowBulk",actionFn:ce,loadingText:e=>P({en:"Unfollowing...",vi:"Đang bỏ theo dõi..."})+" "+e.name,successText:()=>P({en:"Unfollowed",vi:"Đã bỏ theo dõi"}),successDescItem:e=>e.name,requireVIP:!0,confirmProps:{title:P({en:"Unfollow "+e.length+" users?",vi:"Bỏ theo dõi "+e.length+" người?"}),text:P({en:`Are you sure to unfollow ${e.length} users?`,vi:`Bạn có chắc chắn muốn bỏ theo dõi ${e.length} người?`})}}).finally((()=>{oe("onUnfollowBulk",null,!1)}));var e},loading:Z.onUnfollowBulk,children:u.length})})]}),n.jsx(w,{text:t.map((e=>e.name)).join(" "),name:"users_reaction",title:P({en:"User Reaction",vi:"Người thả cảm xúc"})})]})},footer:n.jsx(_,{justify:"center",children:te()}),onSearchRow:(e,n)=>g(e,n.name+" "+n.uid)})}function K({ti:e,key:i,data:t,title:o,tooltip:s,getBoolean:l=null}){const r=l||(e=>e[i]);return{title:n.jsx(a,{title:e(s),children:e(o)}),dataIndex:i,key:i,render:(e,i)=>r(i)?n.jsx(h,{color:"green",children:n.jsx("i",{className:"fa-solid fa-check"})}):n.jsx("i",{className:"fa-solid fa-minus",style:{color:"gray"}}),width:70,filters:[{text:n.jsxs(n.Fragment,{children:[n.jsx(h,{color:"green",children:n.jsx("i",{className:"fa-solid fa-check"})}),e({en:"Yes",vi:"Có"})+" ("+t.filter((e=>r(e))).length+")"]}),value:!0},{text:n.jsxs(n.Fragment,{children:[n.jsx(h,{color:"transparent",children:n.jsx("i",{className:"fa-solid fa-minus"})}),e({en:"No",vi:"Không"})+" ("+t.filter((e=>!r(e))).length+")"]}),value:!1}],onFilter:(e,n)=>r(n)===e}}export{O as default};
