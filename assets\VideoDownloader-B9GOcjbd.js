const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./VideoViewer-DSZVlhxl.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./videos-D2LbKcXH.js","./MyApp-DW5WH4Ub.js","./core-B0p8eH3S.js","./core-Bg7H-yRQ.js","./core-Cg0rLx7n.js","./window-pPC-ycGO.js","./core--w8KKccJ.js","./getIds-DAzc-wzf.js"])))=>i.map(i=>d[i]);
import{bl as e,r as o,aN as i,b0 as t,bk as r,b1 as a,b5 as n,bg as s,cQ as l,bn as c}from"./index-Cak6rALw.js";import{u as d,d as m,t as u,S as p,c as g,T as f,I as w}from"./MyApp-DW5WH4Ub.js";import h from"./useCacheState-CAnxkewm.js";import{B as j}from"./BadgeWrapper-BCbYXXfB.js";import{S as v}from"./Screen-Buq7HJpK.js";import{checkFolderUrl as _}from"./core-Cg0rLx7n.js";import{I as y}from"./index-Bg865k-U.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./window-pPC-ycGO.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";const V=a((()=>i((()=>import("./VideoViewer-DSZVlhxl.js")),__vite__mapDeps([0,1,2,3,4]),import.meta.url)),{fallback:w}),b={[r.Bilibili]:"https://www.facebook.com/groups/fbaio/posts/1576991539622195",[r.GGDrive]:"https://www.facebook.com/groups/fbaio/posts/1576580342996648",[r.Tiktok]:"https://www.facebook.com/groups/fbaio/posts/1571823256805690",[r.Facebook]:"https://www.facebook.com/groups/fbaio/posts/1499830124005004",[r.Douyin]:"https://www.facebook.com/groups/fbaio/posts/1585404348780914"};function x(){const{ti:r}=d(),{message:a}=m(),[w,x]=e(),[k,D]=h("videoDownloader.url",""),[E,L]=h("videoDownloader.videoInfo",null),[I,R]=o.useState(!1);o.useEffect((()=>{u("VideoDownloader:onLoad")}),[]),o.useEffect((()=>{const e=w.get("url");if(console.log("url ne",e,w),e){const o=setTimeout((()=>{D(e),T(e),w.delete("url"),x(w)}),200);return()=>clearTimeout(o)}}),[w]);const T=async(e=k)=>{const o="VideoDownloader:onSearch";u(o),R(!0),a.loading({key:o,content:r({en:"Finding data...",vi:"Đang tìm dữ liệu..."}),duration:0});try{let t;if(e.includes("douyin.com")){const{extractRealDouyinUrl:o,getDouyinVideoDetail:r}=await i((async()=>{const{extractRealDouyinUrl:e,getDouyinVideoDetail:o}=await import("./core-B0p8eH3S.js");return{extractRealDouyinUrl:e,getDouyinVideoDetail:o}}),__vite__mapDeps([5,1,2]),import.meta.url),a=await o(e);a!=e&&D(a),t=await r(a)}else if(e.includes("tiktok.com")){const{getTiktokVideoFromURL:o}=await i((async()=>{const{getTiktokVideoFromURL:e}=await import("./core-Bg7H-yRQ.js");return{getTiktokVideoFromURL:e}}),__vite__mapDeps([6,1,2,4]),import.meta.url);t=await o(e)}else if(e.includes("drive.google.com")){if(!(await _(e,r)))throw new Error(r({en:"Folder not supported",vi:"Chưa hỗ trợ folder"}));const{getGoogleDriveVideoFromURL:o}=await i((async()=>{const{getGoogleDriveVideoFromURL:e}=await import("./core-Cg0rLx7n.js");return{getGoogleDriveVideoFromURL:e}}),__vite__mapDeps([7,1,2,4,8]),import.meta.url);t=await o(e)}else if(e.includes("bilibili.tv")){const{getBilibiliVideoFromURL:o}=await i((async()=>{const{getBilibiliVideoFromURL:e}=await import("./core--w8KKccJ.js");return{getBilibiliVideoFromURL:e}}),__vite__mapDeps([9,1,2]),import.meta.url);t=await o(e)}else{const{getVideoIdFromUrl:n}=await i((async()=>{const{getVideoIdFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getVideoIdFromUrl:e}}),__vite__mapDeps([10,1,2,4]),import.meta.url),s=await n(e);if(!s)throw R(!1),new Error(r({en:"Video not found",vi:"Không tìm thấy video"}));a.success({content:r({en:"Found video id: ",vi:"Tìm thấy ID video: "})+s}),a.loading({key:o,content:r({en:"Fetching video info...",vi:"Đang tải thông tin video..."}),duration:0});const{getVideoInfo:l}=await i((async()=>{const{getVideoInfo:e}=await import("./videos-D2LbKcXH.js");return{getVideoInfo:e}}),__vite__mapDeps([3,1,2,4]),import.meta.url);t=await l(s)}if(console.log(t),!t)throw R(!1),new Error(r({en:"Cannot fetch file info",vi:"Không tải được file"}));a.success({key:o,content:r({en:"Fetched file info",vi:"Tải thông tin file thành công"})}),L(t)}catch(t){a.error({key:o,content:r({en:"Error",vi:"Lỗi"})+": "+t.message})}finally{R(!1)}};return t.jsx(v,{mode:"center",title:r({en:"Video downloader",vi:"Tải Video"})+" 🎬",children:t.jsxs(p,{align:"center",direction:"vertical",style:{width:"100%"},children:[t.jsx(y.Search,{placeholder:r({en:"Enter video/reel URL",vi:"Nhập LINK video/reel"}),size:"large",style:{width:350,maxWidth:"90vw"},value:k,onChange:e=>{var o;return D(null==(o=e.target.value)?void 0:o.trim())},onSearch:()=>T(),enterButton:I?null:t.jsx("i",{className:"fa-solid fa-magnifying-glass"}),loading:I}),E?t.jsxs("div",{style:{position:"relative"},children:[t.jsx(V,{buttonTop:!0,info:E}),t.jsx(n,{type:"text",icon:t.jsx("i",{className:"fa-solid fa-xmark"}),onClick:()=>{D(""),L(null)},style:{position:"absolute",right:5,top:5,zIndex:2}})]}):t.jsx(j,{type:"new",children:t.jsx(s,{style:{maxWidth:"90vw"},defaultActiveKey:[0],items:[{label:r({en:"Supported URL formats ?",vi:"Các định dạng LINK được hỗ trợ ?"}),children:Object.entries(l).map((([e,o])=>t.jsxs("div",{children:[t.jsx("i",{className:c[e]})," ",t.jsx(g.Text,{strong:!0,children:b[e]?t.jsx(f,{title:r({en:"Tutorial",vi:"Hướng dẫn"}),children:t.jsxs(g.Link,{href:b[e],target:"_blank",children:[e," ",t.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})]})}):e}),t.jsx("ul",{children:o.map((e=>t.jsx("li",{children:e},e)))})]},e)))}]})})]})})}export{b as TutorialPostURLs,x as default};
