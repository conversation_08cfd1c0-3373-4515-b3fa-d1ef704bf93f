/*!
* sweetalert2 v11.22.0
* Released under the MIT License.
*/
function e(e,t,o){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:o;throw new TypeError("Private element is not present on this object")}function t(t,o){return t.get(e(t,o))}function o(e,t,o){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,o)}const n={},a=e=>new Promise((t=>{if(!e)return t();const o=window.scrollX,a=window.scrollY;n.restoreFocusTimeout=setTimeout((()=>{n.previousActiveElement instanceof HTMLElement?(n.previousActiveElement.focus(),n.previousActiveElement=null):document.body&&document.body.focus(),t()}),100),window.scrollTo(o,a)})),s="swal2-",r=["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error","draggable","dragging"].reduce(((e,t)=>(e[t]=s+t,e)),{}),i=["success","warning","info","question","error"].reduce(((e,t)=>(e[t]=s+t,e)),{}),l="SweetAlert2:",c=e=>e.charAt(0).toUpperCase()+e.slice(1),d=e=>{console.warn(`${l} ${"object"==typeof e?e.join(" "):e}`)},u=e=>{console.error(`${l} ${e}`)},w=[],m=(e,t=null)=>{var o;o=`"${e}" is deprecated and will be removed in the next major release.${t?` Use "${t}" instead.`:""}`,w.includes(o)||(w.push(o),d(o))},p=e=>"function"==typeof e?e():e,h=e=>e&&"function"==typeof e.toPromise,g=e=>h(e)?e.toPromise():Promise.resolve(e),b=e=>e&&Promise.resolve(e)===e,f=()=>document.body.querySelector(`.${r.container}`),v=e=>{const t=f();return t?t.querySelector(e):null},y=e=>v(`.${e}`),k=()=>y(r.popup),x=()=>y(r.icon),C=()=>y(r.title),A=()=>y(r["html-container"]),E=()=>y(r.image),$=()=>y(r["progress-steps"]),B=()=>y(r["validation-message"]),L=()=>v(`.${r.actions} .${r.confirm}`),P=()=>v(`.${r.actions} .${r.cancel}`),T=()=>v(`.${r.actions} .${r.deny}`),S=()=>v(`.${r.loader}`),O=()=>y(r.actions),j=()=>y(r.footer),M=()=>y(r["timer-progress-bar"]),z=()=>y(r.close),H=()=>{const e=k();if(!e)return[];const t=e.querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])'),o=Array.from(t).sort(((e,t)=>{const o=parseInt(e.getAttribute("tabindex")||"0"),n=parseInt(t.getAttribute("tabindex")||"0");return o>n?1:o<n?-1:0})),n=e.querySelectorAll('\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex="0"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n'),a=Array.from(n).filter((e=>"-1"!==e.getAttribute("tabindex")));return[...new Set(o.concat(a))].filter((e=>ee(e)))},I=()=>V(document.body,r.shown)&&!V(document.body,r["toast-shown"])&&!V(document.body,r["no-backdrop"]),q=()=>{const e=k();return!!e&&V(e,r.toast)},D=(e,t)=>{if(e.textContent="",t){const o=(new DOMParser).parseFromString(t,"text/html"),n=o.querySelector("head");n&&Array.from(n.childNodes).forEach((t=>{e.appendChild(t)}));const a=o.querySelector("body");a&&Array.from(a.childNodes).forEach((t=>{t instanceof HTMLVideoElement||t instanceof HTMLAudioElement?e.appendChild(t.cloneNode(!0)):e.appendChild(t)}))}},V=(e,t)=>{if(!t)return!1;const o=t.split(/\s+/);for(let n=0;n<o.length;n++)if(!e.classList.contains(o[n]))return!1;return!0},N=(e,t,o)=>{if(((e,t)=>{Array.from(e.classList).forEach((o=>{Object.values(r).includes(o)||Object.values(i).includes(o)||Object.values(t.showClass||{}).includes(o)||e.classList.remove(o)}))})(e,t),!t.customClass)return;const n=t.customClass[o];n&&("string"==typeof n||n.forEach?U(e,n):d(`Invalid type of customClass.${o}! Expected string or iterable object, got "${typeof n}"`))},_=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(`.${r.popup} > .${r[t]}`);case"checkbox":return e.querySelector(`.${r.popup} > .${r.checkbox} input`);case"radio":return e.querySelector(`.${r.popup} > .${r.radio} input:checked`)||e.querySelector(`.${r.popup} > .${r.radio} input:first-child`);case"range":return e.querySelector(`.${r.popup} > .${r.range} input`);default:return e.querySelector(`.${r.popup} > .${r.input}`)}},F=e=>{if(e.focus(),"file"!==e.type){const t=e.value;e.value="",e.value=t}},R=(e,t,o)=>{e&&t&&("string"==typeof t&&(t=t.split(/\s+/).filter(Boolean)),t.forEach((t=>{Array.isArray(e)?e.forEach((e=>{o?e.classList.add(t):e.classList.remove(t)})):o?e.classList.add(t):e.classList.remove(t)})))},U=(e,t)=>{R(e,t,!0)},Y=(e,t)=>{R(e,t,!1)},W=(e,t)=>{const o=Array.from(e.children);for(let n=0;n<o.length;n++){const e=o[n];if(e instanceof HTMLElement&&V(e,t))return e}},Z=(e,t,o)=>{o===`${parseInt(o)}`&&(o=parseInt(o)),o||0===parseInt(o)?e.style.setProperty(t,"number"==typeof o?`${o}px`:o):e.style.removeProperty(t)},K=(e,t="flex")=>{e&&(e.style.display=t)},X=e=>{e&&(e.style.display="none")},J=(e,t="block")=>{e&&new MutationObserver((()=>{Q(e,e.innerHTML,t)})).observe(e,{childList:!0,subtree:!0})},G=(e,t,o,n)=>{const a=e.querySelector(t);a&&a.style.setProperty(o,n)},Q=(e,t,o="flex")=>{t?K(e,o):X(e)},ee=e=>!(!e||!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),te=e=>!!(e.scrollHeight>e.clientHeight),oe=e=>{const t=window.getComputedStyle(e),o=parseFloat(t.getPropertyValue("animation-duration")||"0"),n=parseFloat(t.getPropertyValue("transition-duration")||"0");return o>0||n>0},ne=(e,t=!1)=>{const o=M();o&&ee(o)&&(t&&(o.style.transition="none",o.style.width="100%"),setTimeout((()=>{o.style.transition=`width ${e/1e3}s linear`,o.style.width="0%"}),10))},ae=`\n <div aria-labelledby="${r.title}" aria-describedby="${r["html-container"]}" class="${r.popup}" tabindex="-1">\n   <button type="button" class="${r.close}"></button>\n   <ul class="${r["progress-steps"]}"></ul>\n   <div class="${r.icon}"></div>\n   <img class="${r.image}" />\n   <h2 class="${r.title}" id="${r.title}"></h2>\n   <div class="${r["html-container"]}" id="${r["html-container"]}"></div>\n   <input class="${r.input}" id="${r.input}" />\n   <input type="file" class="${r.file}" />\n   <div class="${r.range}">\n     <input type="range" />\n     <output></output>\n   </div>\n   <select class="${r.select}" id="${r.select}"></select>\n   <div class="${r.radio}"></div>\n   <label class="${r.checkbox}">\n     <input type="checkbox" id="${r.checkbox}" />\n     <span class="${r.label}"></span>\n   </label>\n   <textarea class="${r.textarea}" id="${r.textarea}"></textarea>\n   <div class="${r["validation-message"]}" id="${r["validation-message"]}"></div>\n   <div class="${r.actions}">\n     <div class="${r.loader}"></div>\n     <button type="button" class="${r.confirm}"></button>\n     <button type="button" class="${r.deny}"></button>\n     <button type="button" class="${r.cancel}"></button>\n   </div>\n   <div class="${r.footer}"></div>\n   <div class="${r["timer-progress-bar-container"]}">\n     <div class="${r["timer-progress-bar"]}"></div>\n   </div>\n </div>\n`.replace(/(^|\n)\s*/g,""),se=()=>{n.currentInstance.resetValidationMessage()},re=e=>{const t=(()=>{const e=f();return!!e&&(e.remove(),Y([document.documentElement,document.body],[r["no-backdrop"],r["toast-shown"],r["has-column"]]),!0)})();if("undefined"==typeof window||"undefined"==typeof document)return void u("SweetAlert2 requires document to initialize");const o=document.createElement("div");o.className=r.container,t&&U(o,r["no-transition"]),D(o,ae),o.dataset.swal2Theme=e.theme;const n="string"==typeof(a=e.target)?document.querySelector(a):a;var a;n.appendChild(o),e.topLayer&&(o.setAttribute("popover",""),o.showPopover()),(e=>{const t=k();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")})(e),(e=>{"rtl"===window.getComputedStyle(e).direction&&U(f(),r.rtl)})(n),(()=>{const e=k(),t=W(e,r.input),o=W(e,r.file),n=e.querySelector(`.${r.range} input`),a=e.querySelector(`.${r.range} output`),s=W(e,r.select),i=e.querySelector(`.${r.checkbox} input`),l=W(e,r.textarea);t.oninput=se,o.onchange=se,s.onchange=se,i.onchange=se,l.oninput=se,n.oninput=()=>{se(),a.value=n.value},n.onchange=()=>{se(),a.value=n.value}})()},ie=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):"object"==typeof e?le(e,t):e&&D(t,e)},le=(e,t)=>{e.jquery?ce(t,e):D(t,e.toString())},ce=(e,t)=>{if(e.textContent="",0 in t)for(let o=0;o in t;o++)e.appendChild(t[o].cloneNode(!0));else e.appendChild(t.cloneNode(!0))},de=(e,t)=>{const o=O(),n=S();o&&n&&(t.showConfirmButton||t.showDenyButton||t.showCancelButton?K(o):X(o),N(o,t,"actions"),function(e,t,o){const n=L(),a=T(),s=P();if(!n||!a||!s)return;we(n,"confirm",o),we(a,"deny",o),we(s,"cancel",o),function(e,t,o,n){if(!n.buttonsStyling)return void Y([e,t,o],r.styled);U([e,t,o],r.styled),n.confirmButtonColor&&e.style.setProperty("--swal2-confirm-button-background-color",n.confirmButtonColor);n.denyButtonColor&&t.style.setProperty("--swal2-deny-button-background-color",n.denyButtonColor);n.cancelButtonColor&&o.style.setProperty("--swal2-cancel-button-background-color",n.cancelButtonColor);ue(e),ue(t),ue(o)}(n,a,s,o),o.reverseButtons&&(o.toast?(e.insertBefore(s,n),e.insertBefore(a,n)):(e.insertBefore(s,t),e.insertBefore(a,t),e.insertBefore(n,t)))}(o,n,t),D(n,t.loaderHtml||""),N(n,t,"loader"))};function ue(e){const t=window.getComputedStyle(e);if(t.getPropertyValue("--swal2-action-button-focus-box-shadow"))return;const o=t.backgroundColor.replace(/rgba?\((\d+), (\d+), (\d+).*/,"rgba($1, $2, $3, 0.5)");e.style.setProperty("--swal2-action-button-focus-box-shadow",t.getPropertyValue("--swal2-outline").replace(/ rgba\(.*/,` ${o}`))}function we(e,t,o){const n=c(t);Q(e,o[`show${n}Button`],"inline-block"),D(e,o[`${t}ButtonText`]||""),e.setAttribute("aria-label",o[`${t}ButtonAriaLabel`]||""),e.className=r[t],N(e,o,`${t}Button`)}const me=(e,t)=>{const o=f();o&&(!function(e,t){"string"==typeof t?e.style.background=t:t||U([document.documentElement,document.body],r["no-backdrop"])}(o,t.backdrop),function(e,t){if(!t)return;t in r?U(e,r[t]):(d('The "position" parameter is not valid, defaulting to "center"'),U(e,r.center))}(o,t.position),function(e,t){if(!t)return;U(e,r[`grow-${t}`])}(o,t.grow),N(o,t,"container"))};var pe={innerParams:new WeakMap,domCache:new WeakMap};const he=["input","file","range","select","radio","checkbox","textarea"],ge=e=>{if(!e.input)return;if(!Ce[e.input])return void u(`Unexpected type of input! Expected ${Object.keys(Ce).join(" | ")}, got "${e.input}"`);const t=ke(e.input);if(!t)return;const o=Ce[e.input](t,e);K(t),e.inputAutoFocus&&setTimeout((()=>{F(o)}))},be=(e,t)=>{const o=k();if(!o)return;const n=_(o,e);if(n){(e=>{for(let t=0;t<e.attributes.length;t++){const o=e.attributes[t].name;["id","type","value","style"].includes(o)||e.removeAttribute(o)}})(n);for(const e in t)n.setAttribute(e,t[e])}},fe=e=>{if(!e.input)return;const t=ke(e.input);t&&N(t,e,"input")},ve=(e,t)=>{!e.placeholder&&t.inputPlaceholder&&(e.placeholder=t.inputPlaceholder)},ye=(e,t,o)=>{if(o.inputLabel){const n=document.createElement("label"),a=r["input-label"];n.setAttribute("for",e.id),n.className=a,"object"==typeof o.customClass&&U(n,o.customClass.inputLabel),n.innerText=o.inputLabel,t.insertAdjacentElement("beforebegin",n)}},ke=e=>{const t=k();if(t)return W(t,r[e]||r.input)},xe=(e,t)=>{["string","number"].includes(typeof t)?e.value=`${t}`:b(t)||d(`Unexpected type of inputValue! Expected "string", "number" or "Promise", got "${typeof t}"`)},Ce={};Ce.text=Ce.email=Ce.password=Ce.number=Ce.tel=Ce.url=Ce.search=Ce.date=Ce["datetime-local"]=Ce.time=Ce.week=Ce.month=(e,t)=>(xe(e,t.inputValue),ye(e,e,t),ve(e,t),e.type=t.input,e),Ce.file=(e,t)=>(ye(e,e,t),ve(e,t),e),Ce.range=(e,t)=>{const o=e.querySelector("input"),n=e.querySelector("output");return xe(o,t.inputValue),o.type=t.input,xe(n,t.inputValue),ye(o,e,t),e},Ce.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const o=document.createElement("option");D(o,t.inputPlaceholder),o.value="",o.disabled=!0,o.selected=!0,e.appendChild(o)}return ye(e,e,t),e},Ce.radio=e=>(e.textContent="",e),Ce.checkbox=(e,t)=>{const o=_(k(),"checkbox");o.value="1",o.checked=Boolean(t.inputValue);const n=e.querySelector("span");return D(n,t.inputPlaceholder||t.inputLabel),o},Ce.textarea=(e,t)=>{xe(e,t.inputValue),ve(e,t),ye(e,e,t);return setTimeout((()=>{if("MutationObserver"in window){const o=parseInt(window.getComputedStyle(k()).width);new MutationObserver((()=>{if(!document.body.contains(e))return;const n=e.offsetWidth+(a=e,parseInt(window.getComputedStyle(a).marginLeft)+parseInt(window.getComputedStyle(a).marginRight));var a;n>o?k().style.width=`${n}px`:Z(k(),"width",t.width)})).observe(e,{attributes:!0,attributeFilter:["style"]})}})),e};const Ae=(e,t)=>{const o=A();o&&(J(o),N(o,t,"htmlContainer"),t.html?(ie(t.html,o),K(o,"block")):t.text?(o.textContent=t.text,K(o,"block")):X(o),((e,t)=>{const o=k();if(!o)return;const n=pe.innerParams.get(e),a=!n||t.input!==n.input;he.forEach((e=>{const n=W(o,r[e]);n&&(be(e,t.inputAttributes),n.className=r[e],a&&X(n))})),t.input&&(a&&ge(t),fe(t))})(e,t))},Ee=(e,t)=>{for(const[o,n]of Object.entries(i))t.icon!==o&&Y(e,n);U(e,t.icon&&i[t.icon]),Le(e,t),$e(),N(e,t,"icon")},$e=()=>{const e=k();if(!e)return;const t=window.getComputedStyle(e).getPropertyValue("background-color"),o=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let n=0;n<o.length;n++)o[n].style.backgroundColor=t},Be=(e,t)=>{if(!t.icon&&!t.iconHtml)return;let o=e.innerHTML,n="";if(t.iconHtml)n=Pe(t.iconHtml);else if("success"===t.icon)n='\n  <div class="swal2-success-circular-line-left"></div>\n  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n  <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n  <div class="swal2-success-circular-line-right"></div>\n',o=o.replace(/ style=".*?"/g,"");else if("error"===t.icon)n='\n  <span class="swal2-x-mark">\n    <span class="swal2-x-mark-line-left"></span>\n    <span class="swal2-x-mark-line-right"></span>\n  </span>\n';else if(t.icon){n=Pe({question:"?",warning:"!",info:"i"}[t.icon])}o.trim()!==n.trim()&&D(e,n)},Le=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const o of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])G(e,o,"background-color",t.iconColor);G(e,".swal2-success-ring","border-color",t.iconColor)}},Pe=e=>`<div class="${r["icon-content"]}">${e}</div>`;let Te=!1,Se=0,Oe=0,je=0,Me=0;const ze=e=>{const t=k();if(e.target===t||x().contains(e.target)){Te=!0;const o=qe(e);Se=o.clientX,Oe=o.clientY,je=parseInt(t.style.insetInlineStart)||0,Me=parseInt(t.style.insetBlockStart)||0,U(t,"swal2-dragging")}},He=e=>{const t=k();if(Te){let{clientX:o,clientY:n}=qe(e);t.style.insetInlineStart=`${je+(o-Se)}px`,t.style.insetBlockStart=`${Me+(n-Oe)}px`}},Ie=()=>{const e=k();Te=!1,Y(e,"swal2-dragging")},qe=e=>{let t=0,o=0;return e.type.startsWith("mouse")?(t=e.clientX,o=e.clientY):e.type.startsWith("touch")&&(t=e.touches[0].clientX,o=e.touches[0].clientY),{clientX:t,clientY:o}},De=(e,t)=>{const o=f(),n=k();if(o&&n){if(t.toast){Z(o,"width",t.width),n.style.width="100%";const e=S();e&&n.insertBefore(e,x())}else Z(n,"width",t.width);Z(n,"padding",t.padding),t.color&&(n.style.color=t.color),t.background&&(n.style.background=t.background),X(B()),Ve(n,t),t.draggable&&!t.toast?(U(n,r.draggable),(e=>{e.addEventListener("mousedown",ze),document.body.addEventListener("mousemove",He),e.addEventListener("mouseup",Ie),e.addEventListener("touchstart",ze),document.body.addEventListener("touchmove",He),e.addEventListener("touchend",Ie)})(n)):(Y(n,r.draggable),(e=>{e.removeEventListener("mousedown",ze),document.body.removeEventListener("mousemove",He),e.removeEventListener("mouseup",Ie),e.removeEventListener("touchstart",ze),document.body.removeEventListener("touchmove",He),e.removeEventListener("touchend",Ie)})(n))}},Ve=(e,t)=>{const o=t.showClass||{};e.className=`${r.popup} ${ee(e)?o.popup:""}`,t.toast?(U([document.documentElement,document.body],r["toast-shown"]),U(e,r.toast)):U(e,r.modal),N(e,t,"popup"),"string"==typeof t.customClass&&U(e,t.customClass),t.icon&&U(e,r[`icon-${t.icon}`])},Ne=e=>{const t=document.createElement("li");return U(t,r["progress-step"]),D(t,e),t},_e=e=>{const t=document.createElement("li");return U(t,r["progress-step-line"]),e.progressStepsDistance&&Z(t,"width",e.progressStepsDistance),t},Fe=(e,t)=>{De(0,t),me(0,t),((e,t)=>{const o=$();if(!o)return;const{progressSteps:n,currentProgressStep:a}=t;n&&0!==n.length&&void 0!==a?(K(o),o.textContent="",a>=n.length&&d("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),n.forEach(((e,s)=>{const i=Ne(e);if(o.appendChild(i),s===a&&U(i,r["active-progress-step"]),s!==n.length-1){const e=_e(t);o.appendChild(e)}}))):X(o)})(0,t),((e,t)=>{const o=pe.innerParams.get(e),n=x();if(!n)return;if(o&&t.icon===o.icon)return Be(n,t),void Ee(n,t);if(!t.icon&&!t.iconHtml)return void X(n);if(t.icon&&-1===Object.keys(i).indexOf(t.icon))return u(`Unknown icon! Expected "success", "error", "warning", "info" or "question", got "${t.icon}"`),void X(n);K(n),Be(n,t),Ee(n,t),U(n,t.showClass&&t.showClass.icon),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",$e)})(e,t),((e,t)=>{const o=E();o&&(t.imageUrl?(K(o,""),o.setAttribute("src",t.imageUrl),o.setAttribute("alt",t.imageAlt||""),Z(o,"width",t.imageWidth),Z(o,"height",t.imageHeight),o.className=r.image,N(o,t,"image")):X(o))})(0,t),((e,t)=>{const o=C();o&&(J(o),Q(o,t.title||t.titleText,"block"),t.title&&ie(t.title,o),t.titleText&&(o.innerText=t.titleText),N(o,t,"title"))})(0,t),((e,t)=>{const o=z();o&&(D(o,t.closeButtonHtml||""),N(o,t,"closeButton"),Q(o,t.showCloseButton),o.setAttribute("aria-label",t.closeButtonAriaLabel||""))})(0,t),Ae(e,t),de(0,t),((e,t)=>{const o=j();o&&(J(o),Q(o,t.footer,"block"),t.footer&&ie(t.footer,o),N(o,t,"footer"))})(0,t);const o=k();"function"==typeof t.didRender&&o&&t.didRender(o),n.eventEmitter.emit("didRender",o)},Re=()=>{var e;return null===(e=L())||void 0===e?void 0:e.click()},Ue=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),Ye=e=>{e.keydownTarget&&e.keydownHandlerAdded&&(e.keydownTarget.removeEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!1)},We=(e,t)=>{var o;const n=H();if(n.length)return-2===(e+=t)&&(e=n.length-1),e===n.length?e=0:-1===e&&(e=n.length-1),void n[e].focus();null===(o=k())||void 0===o||o.focus()},Ze=["ArrowRight","ArrowDown"],Ke=["ArrowLeft","ArrowUp"],Xe=(e,t,o)=>{e&&(t.isComposing||229===t.keyCode||(e.stopKeydownPropagation&&t.stopPropagation(),"Enter"===t.key?Je(t,e):"Tab"===t.key?Ge(t):[...Ze,...Ke].includes(t.key)?Qe(t.key):"Escape"===t.key&&et(t,e,o)))},Je=(e,t)=>{if(!p(t.allowEnterKey))return;const o=_(k(),t.input);if(e.target&&o&&e.target instanceof HTMLElement&&e.target.outerHTML===o.outerHTML){if(["textarea","file"].includes(t.input))return;Re(),e.preventDefault()}},Ge=e=>{const t=e.target,o=H();let n=-1;for(let a=0;a<o.length;a++)if(t===o[a]){n=a;break}e.shiftKey?We(n,-1):We(n,1),e.stopPropagation(),e.preventDefault()},Qe=e=>{const t=O(),o=L(),n=T(),a=P();if(!(t&&o&&n&&a))return;const s=[o,n,a];if(document.activeElement instanceof HTMLElement&&!s.includes(document.activeElement))return;const r=Ze.includes(e)?"nextElementSibling":"previousElementSibling";let i=document.activeElement;if(i){for(let e=0;e<t.children.length;e++){if(i=i[r],!i)return;if(i instanceof HTMLButtonElement&&ee(i))break}i instanceof HTMLButtonElement&&i.focus()}},et=(e,t,o)=>{p(t.allowEscapeKey)&&(e.preventDefault(),o(Ue.esc))};var tt={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};const ot=()=>{Array.from(document.body.children).forEach((e=>{e.hasAttribute("data-previous-aria-hidden")?(e.setAttribute("aria-hidden",e.getAttribute("data-previous-aria-hidden")||""),e.removeAttribute("data-previous-aria-hidden")):e.removeAttribute("aria-hidden")}))},nt="undefined"!=typeof window&&!!window.GestureEvent,at=()=>{const e=f();if(!e)return;let t;e.ontouchstart=e=>{t=st(e)},e.ontouchmove=e=>{t&&(e.preventDefault(),e.stopPropagation())}},st=e=>{const t=e.target,o=f(),n=A();return!(!o||!n)&&(!rt(e)&&!it(e)&&(t===o||!(te(o)||!(t instanceof HTMLElement)||((e,t)=>{let o=e;for(;o&&o!==t;){if(te(o))return!0;o=o.parentElement}return!1})(t,n)||"INPUT"===t.tagName||"TEXTAREA"===t.tagName||te(n)&&n.contains(t))))},rt=e=>e.touches&&e.touches.length&&"stylus"===e.touches[0].touchType,it=e=>e.touches&&e.touches.length>1;let lt=null;const ct=e=>{null===lt&&(document.body.scrollHeight>window.innerHeight||"scroll"===e)&&(lt=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=`${lt+(()=>{const e=document.createElement("div");e.className=r["scrollbar-measure"],document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t})()}px`)};function dt(e,t,o,s){q()?ft(e,s):(a(o).then((()=>ft(e,s))),Ye(n)),nt?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),I()&&(null!==lt&&(document.body.style.paddingRight=`${lt}px`,lt=null),(()=>{if(V(document.body,r.iosfix)){const e=parseInt(document.body.style.top,10);Y(document.body,r.iosfix),document.body.style.top="",document.body.scrollTop=-1*e}})(),ot()),Y([document.documentElement,document.body],[r.shown,r["height-auto"],r["no-backdrop"],r["toast-shown"]])}function ut(e){e=ht(e);const t=tt.swalPromiseResolve.get(this),o=wt(this);this.isAwaitingPromise?e.isDismissed||(pt(this),t(e)):o&&t(e)}const wt=e=>{const t=k();if(!t)return!1;const o=pe.innerParams.get(e);if(!o||V(t,o.hideClass.popup))return!1;Y(t,o.showClass.popup),U(t,o.hideClass.popup);const n=f();return Y(n,o.showClass.backdrop),U(n,o.hideClass.backdrop),gt(e,t,o),!0};function mt(e){const t=tt.swalPromiseReject.get(this);pt(this),t&&t(e)}const pt=e=>{e.isAwaitingPromise&&(delete e.isAwaitingPromise,pe.innerParams.get(e)||e._destroy())},ht=e=>void 0===e?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},e),gt=(e,t,o)=>{var a;const s=f(),r=oe(t);"function"==typeof o.willClose&&o.willClose(t),null===(a=n.eventEmitter)||void 0===a||a.emit("willClose",t),r?bt(e,t,s,o.returnFocus,o.didClose):dt(e,s,o.returnFocus,o.didClose)},bt=(e,t,o,a,s)=>{n.swalCloseEventFinishedCallback=dt.bind(null,e,o,a,s);const r=function(e){var o;e.target===t&&(null===(o=n.swalCloseEventFinishedCallback)||void 0===o||o.call(n),delete n.swalCloseEventFinishedCallback,t.removeEventListener("animationend",r),t.removeEventListener("transitionend",r))};t.addEventListener("animationend",r),t.addEventListener("transitionend",r)},ft=(e,t)=>{setTimeout((()=>{var o;"function"==typeof t&&t.bind(e.params)(),null===(o=n.eventEmitter)||void 0===o||o.emit("didClose"),e._destroy&&e._destroy()}))},vt=e=>{let t=k();if(t||new Qo,t=k(),!t)return;const o=S();q()?X(x()):yt(t,e),K(o),t.setAttribute("data-loading","true"),t.setAttribute("aria-busy","true"),t.focus()},yt=(e,t)=>{const o=O(),n=S();o&&n&&(!t&&ee(L())&&(t=L()),K(o),t&&(X(t),n.setAttribute("data-button-to-replace",t.className),o.insertBefore(n,t)),U([e,o],r.loading))},kt=e=>e.checked?1:0,xt=e=>e.checked?e.value:null,Ct=e=>e.files&&e.files.length?null!==e.getAttribute("multiple")?e.files:e.files[0]:null,At=(e,t)=>{const o=k();if(!o)return;const n=e=>{"select"===t.input?function(e,t,o){const n=W(e,r.select);if(!n)return;const a=(e,t,n)=>{const a=document.createElement("option");a.value=n,D(a,t),a.selected=Bt(n,o.inputValue),e.appendChild(a)};t.forEach((e=>{const t=e[0],o=e[1];if(Array.isArray(o)){const e=document.createElement("optgroup");e.label=t,e.disabled=!1,n.appendChild(e),o.forEach((t=>a(e,t[1],t[0])))}else a(n,o,t)})),n.focus()}(o,$t(e),t):"radio"===t.input&&function(e,t,o){const n=W(e,r.radio);if(!n)return;t.forEach((e=>{const t=e[0],a=e[1],s=document.createElement("input"),i=document.createElement("label");s.type="radio",s.name=r.radio,s.value=t,Bt(t,o.inputValue)&&(s.checked=!0);const l=document.createElement("span");D(l,a),l.className=r.label,i.appendChild(s),i.appendChild(l),n.appendChild(i)}));const a=n.querySelectorAll("input");a.length&&a[0].focus()}(o,$t(e),t)};h(t.inputOptions)||b(t.inputOptions)?(vt(L()),g(t.inputOptions).then((t=>{e.hideLoading(),n(t)}))):"object"==typeof t.inputOptions?n(t.inputOptions):u("Unexpected type of inputOptions! Expected object, Map or Promise, got "+typeof t.inputOptions)},Et=(e,t)=>{const o=e.getInput();o&&(X(o),g(t.inputValue).then((n=>{o.value="number"===t.input?`${parseFloat(n)||0}`:`${n}`,K(o),o.focus(),e.hideLoading()})).catch((t=>{u(`Error in inputValue promise: ${t}`),o.value="",K(o),o.focus(),e.hideLoading()})))};const $t=e=>{const t=[];return e instanceof Map?e.forEach(((e,o)=>{let n=e;"object"==typeof n&&(n=$t(n)),t.push([o,n])})):Object.keys(e).forEach((o=>{let n=e[o];"object"==typeof n&&(n=$t(n)),t.push([o,n])})),t},Bt=(e,t)=>!!t&&t.toString()===e.toString(),Lt=(e,t)=>{const o=pe.innerParams.get(e);if(!o.input)return void u(`The "input" parameter is needed to be set when using returnInputValueOn${c(t)}`);const n=e.getInput(),a=((e,t)=>{const o=e.getInput();if(!o)return null;switch(t.input){case"checkbox":return kt(o);case"radio":return xt(o);case"file":return Ct(o);default:return t.inputAutoTrim?o.value.trim():o.value}})(e,o);o.inputValidator?Pt(e,a,t):n&&!n.checkValidity()?(e.enableButtons(),e.showValidationMessage(o.validationMessage||n.validationMessage)):"deny"===t?Tt(e,a):jt(e,a)},Pt=(e,t,o)=>{const n=pe.innerParams.get(e);e.disableInput();Promise.resolve().then((()=>g(n.inputValidator(t,n.validationMessage)))).then((n=>{e.enableButtons(),e.enableInput(),n?e.showValidationMessage(n):"deny"===o?Tt(e,t):jt(e,t)}))},Tt=(e,t)=>{const o=pe.innerParams.get(e||void 0);if(o.showLoaderOnDeny&&vt(T()),o.preDeny){e.isAwaitingPromise=!0;Promise.resolve().then((()=>g(o.preDeny(t,o.validationMessage)))).then((o=>{!1===o?(e.hideLoading(),pt(e)):e.close({isDenied:!0,value:void 0===o?t:o})})).catch((t=>Ot(e||void 0,t)))}else e.close({isDenied:!0,value:t})},St=(e,t)=>{e.close({isConfirmed:!0,value:t})},Ot=(e,t)=>{e.rejectPromise(t)},jt=(e,t)=>{const o=pe.innerParams.get(e||void 0);if(o.showLoaderOnConfirm&&vt(),o.preConfirm){e.resetValidationMessage(),e.isAwaitingPromise=!0;Promise.resolve().then((()=>g(o.preConfirm(t,o.validationMessage)))).then((o=>{ee(B())||!1===o?(e.hideLoading(),pt(e)):St(e,void 0===o?t:o)})).catch((t=>Ot(e||void 0,t)))}else St(e,t)};function Mt(){const e=pe.innerParams.get(this);if(!e)return;const t=pe.domCache.get(this);X(t.loader),q()?e.icon&&K(x()):zt(t),Y([t.popup,t.actions],r.loading),t.popup.removeAttribute("aria-busy"),t.popup.removeAttribute("data-loading"),t.confirmButton.disabled=!1,t.denyButton.disabled=!1,t.cancelButton.disabled=!1}const zt=e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?K(t[0],"inline-block"):ee(L())||ee(T())||ee(P())||X(e.actions)};function Ht(){const e=pe.innerParams.get(this),t=pe.domCache.get(this);return t?_(t.popup,e.input):null}function It(e,t,o){const n=pe.domCache.get(e);t.forEach((e=>{n[e].disabled=o}))}function qt(e,t){const o=k();if(o&&e)if("radio"===e.type){const e=o.querySelectorAll(`[name="${r.radio}"]`);for(let o=0;o<e.length;o++)e[o].disabled=t}else e.disabled=t}function Dt(){It(this,["confirmButton","denyButton","cancelButton"],!1)}function Vt(){It(this,["confirmButton","denyButton","cancelButton"],!0)}function Nt(){qt(this.getInput(),!1)}function _t(){qt(this.getInput(),!0)}function Ft(e){const t=pe.domCache.get(this),o=pe.innerParams.get(this);D(t.validationMessage,e),t.validationMessage.className=r["validation-message"],o.customClass&&o.customClass.validationMessage&&U(t.validationMessage,o.customClass.validationMessage),K(t.validationMessage);const n=this.getInput();n&&(n.setAttribute("aria-invalid","true"),n.setAttribute("aria-describedby",r["validation-message"]),F(n),U(n,r.inputerror))}function Rt(){const e=pe.domCache.get(this);e.validationMessage&&X(e.validationMessage);const t=this.getInput();t&&(t.removeAttribute("aria-invalid"),t.removeAttribute("aria-describedby"),Y(t,r.inputerror))}const Ut={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,draggable:!1,animation:!0,theme:"light",showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0,topLayer:!1},Yt=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","draggable","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","theme","willClose"],Wt={allowEnterKey:void 0},Zt=["allowOutsideClick","allowEnterKey","backdrop","draggable","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],Kt=e=>Object.prototype.hasOwnProperty.call(Ut,e),Xt=e=>-1!==Yt.indexOf(e),Jt=e=>Wt[e],Gt=e=>{Kt(e)||d(`Unknown parameter "${e}"`)},Qt=e=>{Zt.includes(e)&&d(`The parameter "${e}" is incompatible with toasts`)},eo=e=>{const t=Jt(e);t&&m(e,t)},to=e=>{!1===e.backdrop&&e.allowOutsideClick&&d('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),e.theme&&!["light","dark","auto","minimal","borderless","embed-iframe","bulma","bulma-light","bulma-dark"].includes(e.theme)&&d(`Invalid theme "${e.theme}"`);for(const t in e)Gt(t),e.toast&&Qt(t),eo(t)};function oo(e){const t=f(),o=k(),n=pe.innerParams.get(this);if(!o||V(o,n.hideClass.popup))return void d("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");const a=no(e),s=Object.assign({},n,a);to(s),t.dataset.swal2Theme=s.theme,Fe(this,s),pe.innerParams.set(this,s),Object.defineProperties(this,{params:{value:Object.assign({},this.params,e),writable:!1,enumerable:!0}})}const no=e=>{const t={};return Object.keys(e).forEach((o=>{Xt(o)?t[o]=e[o]:d(`Invalid parameter to update: ${o}`)})),t};function ao(){const e=pe.domCache.get(this),t=pe.innerParams.get(this);t?(e.popup&&n.swalCloseEventFinishedCallback&&(n.swalCloseEventFinishedCallback(),delete n.swalCloseEventFinishedCallback),"function"==typeof t.didDestroy&&t.didDestroy(),n.eventEmitter.emit("didDestroy"),so(this)):ro(this)}const so=e=>{ro(e),delete e.params,delete n.keydownHandler,delete n.keydownTarget,delete n.currentInstance},ro=e=>{e.isAwaitingPromise?(io(pe,e),e.isAwaitingPromise=!0):(io(tt,e),io(pe,e),delete e.isAwaitingPromise,delete e.disableButtons,delete e.enableButtons,delete e.getInput,delete e.disableInput,delete e.enableInput,delete e.hideLoading,delete e.disableLoading,delete e.showValidationMessage,delete e.resetValidationMessage,delete e.close,delete e.closePopup,delete e.closeModal,delete e.closeToast,delete e.rejectPromise,delete e.update,delete e._destroy)},io=(e,t)=>{for(const o in e)e[o].delete(t)};var lo=Object.freeze({__proto__:null,_destroy:ao,close:ut,closeModal:ut,closePopup:ut,closeToast:ut,disableButtons:Vt,disableInput:_t,disableLoading:Mt,enableButtons:Dt,enableInput:Nt,getInput:Ht,handleAwaitingPromise:pt,hideLoading:Mt,rejectPromise:mt,resetValidationMessage:Rt,showValidationMessage:Ft,update:oo});const co=(e,t,o)=>{t.popup.onclick=()=>{e&&(uo(e)||e.timer||e.input)||o(Ue.close)}},uo=e=>!!(e.showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton);let wo=!1;const mo=e=>{e.popup.onmousedown=()=>{e.container.onmouseup=function(t){e.container.onmouseup=()=>{},t.target===e.container&&(wo=!0)}}},po=e=>{e.container.onmousedown=t=>{t.target===e.container&&t.preventDefault(),e.popup.onmouseup=function(t){e.popup.onmouseup=()=>{},(t.target===e.popup||t.target instanceof HTMLElement&&e.popup.contains(t.target))&&(wo=!0)}}},ho=(e,t,o)=>{t.container.onclick=n=>{wo?wo=!1:n.target===t.container&&p(e.allowOutsideClick)&&o(Ue.backdrop)}},go=e=>e instanceof Element||(e=>"object"==typeof e&&e.jquery)(e);const bo=()=>{if(n.timeout)return(()=>{const e=M();if(!e)return;const t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";const o=t/parseInt(window.getComputedStyle(e).width)*100;e.style.width=`${o}%`})(),n.timeout.stop()},fo=()=>{if(n.timeout){const e=n.timeout.start();return ne(e),e}};let vo=!1;const yo={};const ko=e=>{for(let t=e.target;t&&t!==document;t=t.parentNode)for(const e in yo){const o=t.getAttribute(e);if(o)return void yo[e].fire({template:o})}};n.eventEmitter=new class{constructor(){this.events={}}_getHandlersByEventName(e){return void 0===this.events[e]&&(this.events[e]=[]),this.events[e]}on(e,t){const o=this._getHandlersByEventName(e);o.includes(t)||o.push(t)}once(e,t){const o=(...n)=>{this.removeListener(e,o),t.apply(this,n)};this.on(e,o)}emit(e,...t){this._getHandlersByEventName(e).forEach((e=>{try{e.apply(this,t)}catch(o){console.error(o)}}))}removeListener(e,t){const o=this._getHandlersByEventName(e),n=o.indexOf(t);n>-1&&o.splice(n,1)}removeAllListeners(e){void 0!==this.events[e]&&(this.events[e].length=0)}reset(){this.events={}}};var xo=Object.freeze({__proto__:null,argsToParams:e=>{const t={};return"object"!=typeof e[0]||go(e[0])?["title","html","icon"].forEach(((o,n)=>{const a=e[n];"string"==typeof a||go(a)?t[o]=a:void 0!==a&&u(`Unexpected type of ${o}! Expected "string" or "Element", got ${typeof a}`)})):Object.assign(t,e[0]),t},bindClickHandler:function(e="data-swal-template"){yo[e]=this,vo||(document.body.addEventListener("click",ko),vo=!0)},clickCancel:()=>{var e;return null===(e=P())||void 0===e?void 0:e.click()},clickConfirm:Re,clickDeny:()=>{var e;return null===(e=T())||void 0===e?void 0:e.click()},enableLoading:vt,fire:function(...e){return new this(...e)},getActions:O,getCancelButton:P,getCloseButton:z,getConfirmButton:L,getContainer:f,getDenyButton:T,getFocusableElements:H,getFooter:j,getHtmlContainer:A,getIcon:x,getIconContent:()=>y(r["icon-content"]),getImage:E,getInputLabel:()=>y(r["input-label"]),getLoader:S,getPopup:k,getProgressSteps:$,getTimerLeft:()=>n.timeout&&n.timeout.getTimerLeft(),getTimerProgressBar:M,getTitle:C,getValidationMessage:B,increaseTimer:e=>{if(n.timeout){const t=n.timeout.increase(e);return ne(t,!0),t}},isDeprecatedParameter:Jt,isLoading:()=>{const e=k();return!!e&&e.hasAttribute("data-loading")},isTimerRunning:()=>!(!n.timeout||!n.timeout.isRunning()),isUpdatableParameter:Xt,isValidParameter:Kt,isVisible:()=>ee(k()),mixin:function(e){return class extends(this){_main(t,o){return super._main(t,Object.assign({},e,o))}}},off:(e,t)=>{e?t?n.eventEmitter.removeListener(e,t):n.eventEmitter.removeAllListeners(e):n.eventEmitter.reset()},on:(e,t)=>{n.eventEmitter.on(e,t)},once:(e,t)=>{n.eventEmitter.once(e,t)},resumeTimer:fo,showLoading:vt,stopTimer:bo,toggleTimer:()=>{const e=n.timeout;return e&&(e.running?bo():fo())}});class Co{constructor(e,t){this.callback=e,this.remaining=t,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.started&&this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=(new Date).getTime()-this.started.getTime()),this.remaining}increase(e){const t=this.running;return t&&this.stop(),this.remaining+=e,t&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const Ao=["swal-title","swal-html","swal-footer"],Eo=e=>{const t={};return Array.from(e.querySelectorAll("swal-param")).forEach((e=>{jo(e,["name","value"]);const o=e.getAttribute("name"),n=e.getAttribute("value");o&&n&&(t[o]="boolean"==typeof Ut[o]?"false"!==n:"object"==typeof Ut[o]?JSON.parse(n):n)})),t},$o=e=>{const t={};return Array.from(e.querySelectorAll("swal-function-param")).forEach((e=>{const o=e.getAttribute("name"),n=e.getAttribute("value");o&&n&&(t[o]=new Function(`return ${n}`)())})),t},Bo=e=>{const t={};return Array.from(e.querySelectorAll("swal-button")).forEach((e=>{jo(e,["type","color","aria-label"]);const o=e.getAttribute("type");o&&["confirm","cancel","deny"].includes(o)&&(t[`${o}ButtonText`]=e.innerHTML,t[`show${c(o)}Button`]=!0,e.hasAttribute("color")&&(t[`${o}ButtonColor`]=e.getAttribute("color")),e.hasAttribute("aria-label")&&(t[`${o}ButtonAriaLabel`]=e.getAttribute("aria-label")))})),t},Lo=e=>{const t={},o=e.querySelector("swal-image");return o&&(jo(o,["src","width","height","alt"]),o.hasAttribute("src")&&(t.imageUrl=o.getAttribute("src")||void 0),o.hasAttribute("width")&&(t.imageWidth=o.getAttribute("width")||void 0),o.hasAttribute("height")&&(t.imageHeight=o.getAttribute("height")||void 0),o.hasAttribute("alt")&&(t.imageAlt=o.getAttribute("alt")||void 0)),t},Po=e=>{const t={},o=e.querySelector("swal-icon");return o&&(jo(o,["type","color"]),o.hasAttribute("type")&&(t.icon=o.getAttribute("type")),o.hasAttribute("color")&&(t.iconColor=o.getAttribute("color")),t.iconHtml=o.innerHTML),t},To=e=>{const t={},o=e.querySelector("swal-input");o&&(jo(o,["type","label","placeholder","value"]),t.input=o.getAttribute("type")||"text",o.hasAttribute("label")&&(t.inputLabel=o.getAttribute("label")),o.hasAttribute("placeholder")&&(t.inputPlaceholder=o.getAttribute("placeholder")),o.hasAttribute("value")&&(t.inputValue=o.getAttribute("value")));const n=Array.from(e.querySelectorAll("swal-input-option"));return n.length&&(t.inputOptions={},n.forEach((e=>{jo(e,["value"]);const o=e.getAttribute("value");if(!o)return;const n=e.innerHTML;t.inputOptions[o]=n}))),t},So=(e,t)=>{const o={};for(const n in t){const a=t[n],s=e.querySelector(a);s&&(jo(s,[]),o[a.replace(/^swal-/,"")]=s.innerHTML.trim())}return o},Oo=e=>{const t=Ao.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(e.children).forEach((e=>{const o=e.tagName.toLowerCase();t.includes(o)||d(`Unrecognized element <${o}>`)}))},jo=(e,t)=>{Array.from(e.attributes).forEach((o=>{-1===t.indexOf(o.name)&&d([`Unrecognized attribute "${o.name}" on <${e.tagName.toLowerCase()}>.`,""+(t.length?`Allowed attributes are: ${t.join(", ")}`:"To set the value, use HTML within the element.")])}))},Mo=e=>{const t=f(),o=k();"function"==typeof e.willOpen&&e.willOpen(o),n.eventEmitter.emit("willOpen",o);const a=window.getComputedStyle(document.body).overflowY;qo(t,o,e),setTimeout((()=>{Ho(t,o)}),10),I()&&(Io(t,e.scrollbarPadding,a),(()=>{const e=f();Array.from(document.body.children).forEach((t=>{t.contains(e)||(t.hasAttribute("aria-hidden")&&t.setAttribute("data-previous-aria-hidden",t.getAttribute("aria-hidden")||""),t.setAttribute("aria-hidden","true"))}))})()),q()||n.previousActiveElement||(n.previousActiveElement=document.activeElement),"function"==typeof e.didOpen&&setTimeout((()=>e.didOpen(o))),n.eventEmitter.emit("didOpen",o),Y(t,r["no-transition"])},zo=e=>{const t=k();if(e.target!==t)return;const o=f();t.removeEventListener("animationend",zo),t.removeEventListener("transitionend",zo),o.style.overflowY="auto"},Ho=(e,t)=>{oe(t)?(e.style.overflowY="hidden",t.addEventListener("animationend",zo),t.addEventListener("transitionend",zo)):e.style.overflowY="auto"},Io=(e,t,o)=>{(()=>{if(nt&&!V(document.body,r.iosfix)){const e=document.body.scrollTop;document.body.style.top=-1*e+"px",U(document.body,r.iosfix),at()}})(),t&&"hidden"!==o&&ct(o),setTimeout((()=>{e.scrollTop=0}))},qo=(e,t,o)=>{U(e,o.showClass.backdrop),o.animation?(t.style.setProperty("opacity","0","important"),K(t,"grid"),setTimeout((()=>{U(t,o.showClass.popup),t.style.removeProperty("opacity")}),10)):K(t,"grid"),U([document.documentElement,document.body],r.shown),o.heightAuto&&o.backdrop&&!o.toast&&U([document.documentElement,document.body],r["height-auto"])};var Do=(e,t)=>/^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]+$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),Vo=(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL");function No(e){!function(e){e.inputValidator||("email"===e.input&&(e.inputValidator=Do),"url"===e.input&&(e.inputValidator=Vo))}(e),e.showLoaderOnConfirm&&!e.preConfirm&&d("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),function(e){(!e.target||"string"==typeof e.target&&!document.querySelector(e.target)||"string"!=typeof e.target&&!e.target.appendChild)&&(d('Target parameter is not valid, defaulting to "body"'),e.target="body")}(e),"string"==typeof e.title&&(e.title=e.title.split("\n").join("<br />")),re(e)}let _o;var Fo=new WeakMap;class Ro{constructor(...t){if(o(this,Fo,void 0),"undefined"==typeof window)return;_o=this;const n=Object.freeze(this.constructor.argsToParams(t));var a,s,r;this.params=n,this.isAwaitingPromise=!1,a=Fo,s=this,r=this._main(_o.params),a.set(e(a,s),r)}_main(e,t={}){if(to(Object.assign({},t,e)),n.currentInstance){const e=tt.swalPromiseResolve.get(n.currentInstance),{isAwaitingPromise:t}=n.currentInstance;n.currentInstance._destroy(),t||e({isDismissed:!0}),I()&&ot()}n.currentInstance=_o;const o=Yo(e,t);No(o),Object.freeze(o),n.timeout&&(n.timeout.stop(),delete n.timeout),clearTimeout(n.restoreFocusTimeout);const a=Wo(_o);return Fe(_o,o),pe.innerParams.set(_o,o),Uo(_o,a,o)}then(e){return t(Fo,this).then(e)}finally(e){return t(Fo,this).finally(e)}}const Uo=(e,t,o)=>new Promise(((a,s)=>{const r=t=>{e.close({isDismissed:!0,dismiss:t})};tt.swalPromiseResolve.set(e,a),tt.swalPromiseReject.set(e,s),t.confirmButton.onclick=()=>{(e=>{const t=pe.innerParams.get(e);e.disableButtons(),t.input?Lt(e,"confirm"):jt(e,!0)})(e)},t.denyButton.onclick=()=>{(e=>{const t=pe.innerParams.get(e);e.disableButtons(),t.returnInputValueOnDeny?Lt(e,"deny"):Tt(e,!1)})(e)},t.cancelButton.onclick=()=>{((e,t)=>{e.disableButtons(),t(Ue.cancel)})(e,r)},t.closeButton.onclick=()=>{r(Ue.close)},((e,t,o)=>{e.toast?co(e,t,o):(mo(t),po(t),ho(e,t,o))})(o,t,r),((e,t,o)=>{Ye(e),t.toast||(e.keydownHandler=e=>Xe(t,e,o),e.keydownTarget=t.keydownListenerCapture?window:k(),e.keydownListenerCapture=t.keydownListenerCapture,e.keydownTarget.addEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!0)})(n,o,r),((e,t)=>{"select"===t.input||"radio"===t.input?At(e,t):["text","email","number","tel","textarea"].some((e=>e===t.input))&&(h(t.inputValue)||b(t.inputValue))&&(vt(L()),Et(e,t))})(e,o),Mo(o),Zo(n,o,r),Ko(t,o),setTimeout((()=>{t.container.scrollTop=0}))})),Yo=(e,t)=>{const o=(e=>{const t="string"==typeof e.template?document.querySelector(e.template):e.template;if(!t)return{};const o=t.content;return Oo(o),Object.assign(Eo(o),$o(o),Bo(o),Lo(o),Po(o),To(o),So(o,Ao))})(e),n=Object.assign({},Ut,t,o,e);return n.showClass=Object.assign({},Ut.showClass,n.showClass),n.hideClass=Object.assign({},Ut.hideClass,n.hideClass),!1===n.animation&&(n.showClass={backdrop:"swal2-noanimation"},n.hideClass={}),n},Wo=e=>{const t={popup:k(),container:f(),actions:O(),confirmButton:L(),denyButton:T(),cancelButton:P(),loader:S(),closeButton:z(),validationMessage:B(),progressSteps:$()};return pe.domCache.set(e,t),t},Zo=(e,t,o)=>{const n=M();X(n),t.timer&&(e.timeout=new Co((()=>{o("timer"),delete e.timeout}),t.timer),t.timerProgressBar&&(K(n),N(n,t,"timerProgressBar"),setTimeout((()=>{e.timeout&&e.timeout.running&&ne(t.timer)}))))},Ko=(e,t)=>{if(!t.toast)return p(t.allowEnterKey)?void(Xo(e)||Jo(e,t)||We(-1,1)):(m("allowEnterKey"),void Go())},Xo=e=>{const t=Array.from(e.popup.querySelectorAll("[autofocus]"));for(const o of t)if(o instanceof HTMLElement&&ee(o))return o.focus(),!0;return!1},Jo=(e,t)=>t.focusDeny&&ee(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&ee(e.cancelButton)?(e.cancelButton.focus(),!0):!(!t.focusConfirm||!ee(e.confirmButton))&&(e.confirmButton.focus(),!0),Go=()=>{document.activeElement instanceof HTMLElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()};if("undefined"!=typeof window&&/^ru\b/.test(navigator.language)&&location.host.match(/\.(ru|su|by|xn--p1ai)$/)){const e=new Date,t=localStorage.getItem("swal-initiation");t?(e.getTime()-Date.parse(t))/864e5>3&&setTimeout((()=>{document.body.style.pointerEvents="none";const e=document.createElement("audio");e.src="https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3",e.loop=!0,document.body.appendChild(e),setTimeout((()=>{e.play().catch((()=>{}))}),2500)}),500):localStorage.setItem("swal-initiation",`${e}`)}Ro.prototype.disableButtons=Vt,Ro.prototype.enableButtons=Dt,Ro.prototype.getInput=Ht,Ro.prototype.disableInput=_t,Ro.prototype.enableInput=Nt,Ro.prototype.hideLoading=Mt,Ro.prototype.disableLoading=Mt,Ro.prototype.showValidationMessage=Ft,Ro.prototype.resetValidationMessage=Rt,Ro.prototype.close=ut,Ro.prototype.closePopup=ut,Ro.prototype.closeModal=ut,Ro.prototype.closeToast=ut,Ro.prototype.rejectPromise=mt,Ro.prototype.update=oo,Ro.prototype._destroy=ao,Object.assign(Ro,xo),Object.keys(lo).forEach((e=>{Ro[e]=function(...t){return _o&&_o[e]?_o[e](...t):null}})),Ro.DismissReason=Ue,Ro.version="11.22.0";const Qo=Ro;Qo.default=Qo,"undefined"!=typeof document&&function(e,t){var o=e.createElement("style");if(e.getElementsByTagName("head")[0].appendChild(o),o.styleSheet)o.styleSheet.disabled||(o.styleSheet.cssText=t);else try{o.innerHTML=t}catch(n){o.innerText=t}}(document,':root{--swal2-outline: 0 0 0 3px rgba(100, 150, 200, 0.5);--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-backdrop-transition: background-color 0.1s;--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-icon-zoom: 1;--swal2-icon-animations: true;--swal2-title-padding: 0.8em 1em 0;--swal2-html-container-padding: 1em 1.6em 0.3em;--swal2-input-border: 1px solid #d9d9d9;--swal2-input-border-radius: 0.1875em;--swal2-input-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-background: transparent;--swal2-input-transition: border-color 0.2s, box-shadow 0.2s;--swal2-input-hover-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-focus-border: 1px solid #b4dbed;--swal2-input-focus-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px $swal2-outline-color;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-footer-border-color: #eee;--swal2-footer-background: transparent;--swal2-footer-color: inherit;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc;--swal2-close-button-transition: color 0.2s, box-shadow 0.2s;--swal2-close-button-outline: initial;--swal2-close-button-box-shadow: inset 0 0 0 3px transparent;--swal2-close-button-focus-box-shadow: inset var(--swal2-outline);--swal2-close-button-hover-transform: none;--swal2-actions-justify-content: center;--swal2-actions-width: auto;--swal2-actions-margin: 1.25em auto 0;--swal2-actions-padding: 0;--swal2-actions-border-radius: 0;--swal2-actions-background: transparent;--swal2-action-button-transition: background-color 0.2s, box-shadow 0.2s;--swal2-action-button-hover: black 10%;--swal2-action-button-active: black 10%;--swal2-confirm-button-box-shadow: none;--swal2-confirm-button-border-radius: 0.25em;--swal2-confirm-button-background-color: #7066e0;--swal2-confirm-button-color: #fff;--swal2-deny-button-box-shadow: none;--swal2-deny-button-border-radius: 0.25em;--swal2-deny-button-background-color: #dc3741;--swal2-deny-button-color: #fff;--swal2-cancel-button-box-shadow: none;--swal2-cancel-button-border-radius: 0.25em;--swal2-cancel-button-background-color: #6e7881;--swal2-cancel-button-color: #fff;--swal2-toast-show-animation: swal2-toast-show 0.5s;--swal2-toast-hide-animation: swal2-toast-hide 0.1s forwards;--swal2-toast-border: none;--swal2-toast-box-shadow: 0 0 1px hsl(0deg 0% 0% / 0.075), 0 1px 2px hsl(0deg 0% 0% / 0.075), 1px 2px 4px hsl(0deg 0% 0% / 0.075), 1px 3px 8px hsl(0deg 0% 0% / 0.075), 2px 4px 16px hsl(0deg 0% 0% / 0.075)}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:"top-start     top            top-end" "center-start  center         center-end" "bottom-start  bottom-center  bottom-end";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:var(--swal2-backdrop-transition);-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container)[popover]{width:auto;border:0}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem;container-name:swal2-popup}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:var(--swal2-title-padding);color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:var(--swal2-actions-justify-content);width:var(--swal2-actions-width);margin:var(--swal2-actions-margin);padding:var(--swal2-actions-padding);border-radius:var(--swal2-actions-border-radius);background:var(--swal2-actions-background)}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:var(--swal2-action-button-transition);border:none;box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border-radius:var(--swal2-confirm-button-border-radius);background:initial;background-color:var(--swal2-confirm-button-background-color);box-shadow:var(--swal2-confirm-button-box-shadow);color:var(--swal2-confirm-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):hover{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):active{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border-radius:var(--swal2-deny-button-border-radius);background:initial;background-color:var(--swal2-deny-button-background-color);box-shadow:var(--swal2-deny-button-box-shadow);color:var(--swal2-deny-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):hover{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):active{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border-radius:var(--swal2-cancel-button-border-radius);background:initial;background-color:var(--swal2-cancel-button-background-color);box-shadow:var(--swal2-cancel-button-box-shadow);color:var(--swal2-cancel-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):hover{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):active{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none;box-shadow:var(--swal2-action-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-styled)[disabled]:not(.swal2-loading){opacity:.4}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);background:var(--swal2-footer-background);color:var(--swal2-footer-color);font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:var(--swal2-close-button-transition);border:none;border-radius:var(--swal2-border-radius);outline:var(--swal2-close-button-outline);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:var(--swal2-close-button-hover-transform);background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:var(--swal2-close-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:var(--swal2-html-container-padding);overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:var(--swal2-input-transition);border:var(--swal2-input-border);border-radius:var(--swal2-input-border-radius);background:var(--swal2-input-background);box-shadow:var(--swal2-input-box-shadow);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):hover,div:where(.swal2-container) input:where(.swal2-file):hover,div:where(.swal2-container) textarea:where(.swal2-textarea):hover{box-shadow:var(--swal2-input-hover-box-shadow)}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:var(--swal2-input-focus-border);outline:none;box-shadow:var(--swal2-input-focus-box-shadow)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:"!";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;zoom:var(--swal2-icon-zoom);border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;border:var(--swal2-toast-border);background:var(--swal2-background);box-shadow:var(--swal2-toast-box-shadow);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}@container swal2-popup style(--swal2-icon-animations:true){.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}}.swal2-toast.swal2-show{animation:var(--swal2-toast-show-animation)}.swal2-toast.swal2-hide{animation:var(--swal2-toast-hide-animation)}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}');export{Qo as default};
