import{bc as e,aS as n,aQ as i,r as t,b0 as s,b5 as l,bk as r,bi as a,aP as o}from"./index-Cak6rALw.js";import{d}from"./dayjs.min-CzPn7FMI.js";import c from"./MyTable-DnjDmN7z.js";import{E as u}from"./ExportButton-CbyepAf_.js";import{u as h,d as g,a as m,k as f,n as v,t as k,i as p,S as y,c as x,h as b,s as j,b as F,e as w,f as S,p as C,q as T,T as N,v as D,o as $,w as E,x as q}from"./MyApp-DW5WH4Ub.js";import A from"./useCacheState-CAnxkewm.js";import{u as I}from"./useForceStop-CvbJS7ei.js";import{QuickFilterType as B,getAllFriends as U,QuickFilterLang as R,unfriend as H,pokeFriend as Q,addFriend as L,findBuddy as P,getProfileFriendsSection as M,FriendSection as _,getFriendBirthdays as Y,getAddFriendTime as O,getUnfriended as W,getFollowers as K}from"./friends-CRlBxmhf.js";import{getPostsInteractions as G}from"./posts-Fn0UXLRN.js";import{d as X}from"./messages-wZDWdkVz.js";import{checkVIP as V}from"./useVIP-D5FAMGQQ.js";import z from"./WordStatisticButton-D2UxB2M4.js";import{u as J}from"./useAction-uz-MrZwJ.js";import Z from"./UploadModal-iOZ41qZ5.js";import{B as ee}from"./BadgeWrapper-BCbYXXfB.js";import{u as ne}from"./useDevMode-yj0U7_8D.js";import{S as ie}from"./Screen-Buq7HJpK.js";import{G as te}from"./gender-D7cqwEK5.js";import{R as se}from"./row-BMfM-of4.js";import{A as le}from"./index-DdM4T8-Q.js";import{I as re}from"./index-CDSnY0KE.js";import{D as ae}from"./index-Dg3cFar0.js";import{P as oe}from"./index-PbLYNhdQ.js";import{P as de}from"./index-DP_qolD8.js";import"./index-ByRdMNW-.js";import"./index-BzMPigdO.js";import"./DownOutlined-B-JcS-29.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-C5gzSBcY.js";import"./PurePanel-BvHjNOhQ.js";import"./index-SRy1SMeO.js";import"./move-ESLFEEsv.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./videos-D2LbKcXH.js";import"./sweetalert2.esm.all-BZxvatOx.js";import"./getIds-DAzc-wzf.js";import"./index-IxNfZAD-.js";import"./Dropdown-BcL-JHCf.js";import"./index-Bp2s1Wws.js";import"./index-Bx3o6rwA.js";import"./index-CC5caqt_.js";import"./index-D3qvoGmV.js";import"./index-BVNdrzmU.js";import"./progress-ApOaEpgr.js";import"./col-CzA09p0P.js";const ce={FRIEND:"Friend",POKED:"Poked",UNFRIENDED:"Unfriended",REQUESTED:"Requested friend",NEW:"New friend",FOLLOWER:"Follower"},ue={[ce.POKED]:"green",[ce.UNFRIENDED]:"red",[ce.NEW]:"blue",[ce.REQUESTED]:"pink",[ce.FOLLOWER]:"orange"},he={[ce.FRIEND]:{en:"Friend",vi:"Bạn bè"},[ce.POKED]:{en:"Poked",vi:"Đã chọc"},[ce.UNFRIENDED]:{en:"Unfriended",vi:"Đã huỷ kết bạn"},[ce.REQUESTED]:{en:"Requested friend",vi:"Đã gửi kết bạn"},[ce.NEW]:{en:"New friend",vi:"Chưa kết bạn"},[ce.FOLLOWER]:{en:"Follower",vi:"Người theo dõi"}},ge=e=>{const n=(null==e?void 0:e.statuses)||[];return!n.includes(ce.REQUESTED)&&(n.includes(ce.NEW)||n.includes(ce.UNFRIENDED)||n.includes(ce.FOLLOWER))},me=e=>{const n=(null==e?void 0:e.statuses)||[];return!n.includes(ce.NEW)&&!n.includes(ce.UNFRIENDED)};function fe(){const fe=e(),ve=I(),{ti:ke}=h(),{message:pe,notification:ye}=g(),{onClickAction:xe,onClickBulkActions:be}=J(),{devMode:je}=ne(),{profile:Fe}=m(),we=n(i.friendsByUid),Se=n(i.setFriendsByUid),[Ce,Te]=A("AllFriends.data",[]),[Ne,De]=A("AllFriends.interactions",[]),[$e,Ee]=A("AllFriends.ranking",[]),qe=t.useMemo((()=>{var e,n;const i=new Map,t=new Map;for(let s of Ne)null==(e=s.reactions_uid)||e.forEach((e=>i.set(e,(i.get(e)||0)+1))),null==(n=s.comments_uid)||n.forEach((e=>t.set(e,(t.get(e)||0)+1)));return{reactions:i,comments:t}}),[Ne]),Ae=t.useMemo((()=>(null==Ce?void 0:Ce.map(((e,n)=>{var i,t,s;return{...e,recent:n,comments:(null==(i=null==qe?void 0:qe.comments)?void 0:i.get(e.uid))||0,reactions:(null==(t=null==qe?void 0:qe.reactions)?void 0:t.get(e.uid))||0,ranking:null==$e?void 0:$e.indexOf(e.uid),quickFilters:Array.from(new Set([...e.quickFilters||[],(null==(s=e.uid)?void 0:s.startsWith("1000"))?null:B.StrangeUid,f(e.avatar)?B.NoAvatar:null,/\d/.test(e.name)?B.NameContainsNumbers:null,v(e.name)?B.NameContainsSpecialCharacters:null].filter(Boolean)))}})))||[]),[Ce,qe,$e]),[Ie,Be]=t.useState(!1),[Ue,Re]=t.useState(0),[He,Qe]=t.useState(!1),[Le,Pe]=t.useState(!1),[Me,_e]=t.useState(!1),[Ye,Oe]=t.useState(!1),[We,Ke]=t.useState(!1),[Ge,Xe]=t.useState(!1),[Ve,ze]=t.useState(!1),[Je,Ze]=t.useState(!1),[en,nn]=t.useState(!1),[tn,sn]=t.useState(!1),ln=t.useRef(null);t.useEffect((()=>{(null==Fe?void 0:Fe.uid)&&!(null==Ce?void 0:Ce.length)&&an()}),[]);const rn=(e,n,i=!1,t=!1)=>{Te((s=>a(s,(s=>{Array.isArray(e)||(e=[e]);let l=[];for(let r of e){let e=s.findIndex((e=>e.uid==((null==r?void 0:r.uid)||r))),a=s[e];a?(a.statuses||(a.statuses=[]),i||a.statuses.includes(n)?(Array.isArray(n)||(n=[n]),a.statuses=n):a.statuses.push(n),t&&l.push({index:e,data:a})):t?s.unshift({...r,statuses:[n]}):s.push({...r,statuses:[n]})}for(let{index:e,data:n}of l)s.splice(e,1),s.unshift(n);return s}))))},an=()=>{if(Ie)return;k("AllFriends:onClickReload");const e="onClickReload";pe.loading({key:e,content:ke({en:"Fetching friends...",vi:"Đang tải bạn bè..."}),duration:0}),Be(!0),U({myUid:null==Fe?void 0:Fe.uid}).then((n=>{var i;if(((e=Ce)=>{let n=!1;const i=(null==we?void 0:we[null==Fe?void 0:Fe.uid])||[],t=e.map((e=>e.uid)),l=i.map((e=>e.uid)),r=new Set(t),a=new Set(l),o=t.filter((e=>!a.has(e))),d=l.filter((e=>!r.has(e)));o.length&&(n=!0,console.log("New friends: ",o),rn(o,ce.NEW,!0),(null==i?void 0:i.length)?ye.open({type:"success",duration:0,message:ke({en:`Find ${o.length} new friends`,vi:`Tìm thấy ${o.length} bạn mới`}),description:s.jsx("div",{style:{maxHeight:300,overflowY:"auto",overflowX:"hidden"},children:s.jsx("ol",{children:o.map((n=>{const i=e.find((e=>e.uid==n));return s.jsx("li",{children:s.jsx("a",{href:b(n),target:"_blank",children:(null==i?void 0:i.name)||n})},n)}))})})}):ye.open({type:"success",duration:0,message:ke({en:`Saved friends to cache: ${o.length}`,vi:`Đã lưu danh sách bạn bè: ${o.length}`}),description:ke({en:"Will notify if you have new friends / someone unfriended to you in future",vi:"Sẽ thông báo nếu có bạn mới / người huỷ kết bạn với bạn trong tương lai"})})),d.length&&(n=!0,console.log("Deleted friends: ",d),ye.open({type:"info",duration:0,message:ke({en:`Find ${d.length} unfriended`,vi:`Có ${d.length} người huỷ kết bạn`}),description:s.jsx("div",{style:{maxHeight:300,overflowY:"auto",overflowX:"hidden"},children:s.jsx("ol",{children:d.map((e=>{const n=i.find((n=>n.uid==e));return s.jsx("li",{children:s.jsx("a",{href:b(e),target:"_blank",children:(null==n?void 0:n.name)||e})},e)}))})})})),n&&Se(null==Fe?void 0:Fe.uid,e.map((e=>({uid:e.uid,name:e.name}))))})(n),!(null==n?void 0:n.length))return pe.error({key:e,content:ke({en:"No data to show",vi:"Không có dữ liệu"})});Te(n),console.log(n),pe.success({key:e,content:ke({en:`Found ${n.length} friends`,vi:`Tải xong ${n.length} bạn bè`})}),null==(i=ln.current)||i.clearFilter()})).catch((n=>{pe.error({key:e,content:ke({en:"Failed to fetch friends",vi:"Lỗi tải bạn bè"})+": "+n.message}),console.log(n)})).finally((()=>{Be(!1)}))},on=(e,n=!1)=>xe({key:"AllFriends:onClickUnfriendOne",id:e.uid,record:e,loadingText:e=>ke({en:"Unfriending...",vi:"Đang huỷ kết bạn..."})+" "+e.name,successText:e=>ke({en:"Unfriend completed",vi:"Đã huỷ kết bạn"})+": "+e.name,actionFn:e=>je?D(1e3):H({myUid:null==Fe?void 0:Fe.uid,targetUid:null==e?void 0:e.uid}),onSuccess:()=>rn(e,ce.UNFRIENDED),failedText:e=>ke({en:"Failed to unfriend",vi:"Lỗi huỷ kết bạn"})+": "+e.name,needConfirm:n,confirmProps:{title:ke({en:"Unfriend 🫂",vi:"Huỷ kết bạn 🫂"}),text:ke({en:"Unfriend with ",vi:"Huỷ kết bạn với "})+e.name+"?"}}),dn=async(e,n=!1)=>xe({key:"AllFriends:onClickPokeFriend",id:e.uid,record:e,loadingText:e=>ke({en:"Poking...",vi:"Đang chọc..."})+" "+e.name,successText:e=>ke({en:"Poke completed",vi:"Chọc thành công"})+": "+e.name,actionFn:e=>je?D(1e3):Q({myUid:null==Fe?void 0:Fe.uid,targetUid:null==e?void 0:e.uid}),onSuccess:()=>rn(e,ce.POKED),failedText:e=>ke({en:"Failed to poke",vi:"Chọc thất bại"})+": "+e.name,needConfirm:n,confirmProps:{title:ke({en:"Poke friend 👉",vi:"Chọc bạn bè 👉"}),text:ke({en:"Poke ",vi:"Chọc "})+e.name+"?"}}),cn=async(e,n=!1)=>xe({key:"AllFriends:onClickAddFriend",id:e.uid,record:e,loadingText:e=>ke({en:"Sending friend request...",vi:"Đang gửi kết bạn..."})+" "+e.name,successText:e=>ke({en:"Send friend request success",vi:"Gửi kết bạn thành công"})+": "+e.name,actionFn:e=>je?D(1e3):L({myUid:null==Fe?void 0:Fe.uid,targetUid:null==e?void 0:e.uid}),onSuccess:()=>rn(e,ce.REQUESTED),failedText:e=>ke({en:"Failed to send friend request",vi:"Lỗi gửi kết bạn"})+": "+e.name,needConfirm:n,confirmProps:{title:ke({en:"Send friend request 🙏",vi:"Gửi kết bạn 🙏"}),text:ke({en:"Send friend request to ",vi:"Gửi kết bạn tới "})+e.name+"?"}}),un=async e=>{var n;if(!e)return pe.error(ke({en:"File empty",vi:"File rỗng"}));k("AllFriends:onUploadFriendsFile");try{const i=q(e,[]);if(!Array.isArray(i)||!(null==i?void 0:i.length))return pe.error(ke({en:"No data",vi:"Không có dữ liệu"}));const t=i.map((e=>e.uid)).filter(Boolean);if(!t.length)return pe.error(ke({en:"No friends data in file",vi:"Không có dữ liệu bạn bè trong file"}));const l=new Set(t),r=[];for(let e of Ce)l.has(e.uid)||r.push(e);const a=new Set(Ce.map((e=>e.uid))),o=[];for(let e of i)a.has(e.uid)||o.push(e);rn(r,ce.NEW,!1,!0),rn(o,ce.UNFRIENDED,!1,!0),[{title:ke({en:`Found ${r.length} new friends`,vi:`Tìm thấy ${r.length} bạn mới`}),text:s.jsx("div",{style:{maxHeight:300,overflowY:"auto",overflowX:"hidden"},children:s.jsx("ol",{children:r.map((e=>s.jsx("li",{children:s.jsx("a",{href:b(e.uid),target:"_blank",children:e.name})},e.uid)))})})},{title:ke({en:`Found ${o.length} unfriended`,vi:`Tìm thấy ${o.length} người huỷ kết bạn`}),text:s.jsx("div",{style:{maxHeight:300,overflowY:"auto",overflowX:"hidden"},children:s.jsx("ol",{children:o.map((e=>s.jsx("li",{children:s.jsx("a",{href:b(e.uid),target:"_blank",children:e.name})},e.uid)))})})}].forEach((({title:e,text:n})=>ye.open({type:"success",message:e,description:n,duration:0}))),(o.length||r.length)&&(null==(n=ln.current)||n.setDataSelected([...o,...r]))}catch(i){pe.error(i.message)}},hn=async()=>{if(!(await V())||Le)return;Pe(!0);const e=ve.start(),n="AllFriends:onClickScanInteractions";k(n),pe.loading({key:n,duration:0,content:ke({en:"Loading posts...",vi:"Đang tải bài viết..."})});let i=!1;const t=await G(1/0,(e=>{var t;De([...e]);const l=e.reduce(((e,n)=>{var i;return e+((null==(i=n.comments_uid)?void 0:i.length)||0)}),0),r=e.reduce(((e,n)=>{var i;return e+((null==(i=n.reactions_uid)?void 0:i.length)||0)}),0),a=new Date((null==(t=e[e.length-1])?void 0:t.created_time)||0),o=E(a);pe.loading({key:n,duration:0,content:s.jsxs(s.Fragment,{children:[ke({en:`Loading ${o}: ${e.length} posts, ${r} reactions, ${l} comments...`,vi:`Đang tải ${o}: ${e.length} bài viết, ${r} lượt thích, ${l} bình luận...`}),s.jsx("br",{}),s.jsx("i",{children:ke({en:"Click to stop",vi:"Bấm để dừng"})})]}),onClick:()=>i=!0})}),(()=>i||e.value())),l=t.reduce(((e,n)=>{var i;return e+((null==(i=n.comments_uid)?void 0:i.length)||0)}),0),r=t.reduce(((e,n)=>{var i;return e+((null==(i=n.reactions_uid)?void 0:i.length)||0)}),0);pe.destroy(n);const a=t[t.length-1],o=E(new Date((null==a?void 0:a.created_time)||0));ye.open({type:"success",duration:0,message:ke({en:"Scan interactions finished",vi:"Quét tương tác xong"}),description:s.jsxs(s.Fragment,{children:[ke({en:`Scanned ${t.length} posts, ${r} reactions, ${l} comments, last post ${o} `,vi:`Quét được ${t.length} bài viết, ${r} lượt thích, ${l} bình luận, bài viết cuối ${o} `}),s.jsx("a",{href:b(null==a?void 0:a.postId),target:"_blank",children:b(null==a?void 0:a.postId)})]})}),De(t),Pe(!1)},gn=async()=>{const e="AllFriends:onClickFindBuddy";k(e),pe.loading({key:e,duration:0,content:ke({en:"Scanning ranking friends...",vi:"Đang tải xếp hạng bạn bè..."})});const n=await P(Ce.length||100);console.log(n),pe.destroy(e),ye.open({type:"success",duration:0,message:ke({en:`Scan ranking completed: ${n.length} friends`,vi:`Quét xếp hạng bạn bè xong: ${n.length} bạn`}),description:s.jsxs(s.Fragment,{children:["Top 20:",s.jsx("br",{}),s.jsx("ol",{children:n.slice(0,20).map(((e,n)=>s.jsxs("div",{children:[s.jsx("a",{href:b(e.uid),target:"_blank",children:n+1+": "+e.name}),s.jsx("br",{})]},e.uid)))})]})}),Ee(n.map((e=>e.uid)));const i=new Set(Ce.map((e=>e.uid))),t=n.filter((e=>!i.has(e.uid)));t.length&&rn(t,ce.NEW,!0)},mn=async()=>{if(!(await V()))return;Qe(!0);const e="AllFriends:onClickDeepScan";k(e),pe.loading({key:e,content:ke({en:"Deep Scanning...",vi:"Đang quét chuyên sâu..."}),duration:0});const n=[];let i=null,t=!1;for(;!t;){const l=await M({cursor:i,section:_.friends});if(!l.length)break;Te((e=>a(e,(e=>(l.forEach((n=>{if(!n.uid)return;const i=e.find((e=>e.uid===n.uid));i?(i.mutualFriendCount=n.mutualFriendCount,i.gender=n.gender,i.quickFilters||(i.quickFilters=[]),n.isLocked&&!i.quickFilters.includes(B.Locked)&&i.quickFilters.push(B.Locked)):n.isLocked&&e.push({...n,quickFilters:[B.Locked]})})),e))))),n.push(...l),i=l[l.length-1].cursor,pe.loading({key:e,content:s.jsxs(s.Fragment,{children:[ke({en:"Deep Scanning...",vi:"Đang quét chuyên sâu..."})+`${~~(n.length/Ce.length*100)}% (${n.length}/${Ce.length})`,s.jsx("br",{}),ke({en:"Click to stop",vi:"Bấm để dừng"})]}),onClick:()=>t=!0,duration:0}),await D(200)}const l=n.filter((e=>e.isLocked));pe.destroy(e),ye.open({type:"success",message:ke({en:"Deep Scanning completed",vi:"Hoàn tất Quét chuyên sâu"})+` (${n.length}/${Ce.length})`,description:ke({en:`Found ${l.length} locked friends.\nPlease see in "Quick filters" column`,vi:`Tìm thấy ${l.length} bạn bị khoá.\nVui lòng xem cột "Bộ lọc"`})}),Qe(!1)},fn=async()=>{var e;if(!(await V()))return;_e(!0);const n="AllFriends:onClickScanRemovedFriends";k(n),pe.loading({key:n,content:ke({en:"Scanning removed friends...",vi:"Đang quét bạn cũ..."}),duration:0});const i=[];let t="",l=!1;for(;!l;){const r=await W(t);if(!(null==r?void 0:r.length))break;i.push(...r),t=(null==(e=r[r.length-1])?void 0:e.cursor)||"",Te((e=>a(e,(e=>(r.forEach((n=>{if(!n.uid)return;const i=e.find((e=>e.uid===n.uid));i?(i.statuses||(i.statuses=[]),i.statuses.includes(ce.UNFRIENDED)||i.statuses.push(ce.UNFRIENDED),i.time=n.time,i.desc=n.desc,i.cursor=n.cursor):e.push({...n,statuses:[ce.UNFRIENDED]})})),e))))),pe.loading({key:n,content:s.jsxs(s.Fragment,{children:[ke({en:`Scanning removed friends... ${i.length}`,vi:`Đang quét bạn cũ... ${i.length}`}),s.jsx("br",{}),ke({en:"Click to stop",vi:"Bấm để dừng"})]}),duration:0,onClick:()=>{l=!0}}),await D(1e3)}pe.destroy(n),ye.open({type:"success",message:ke({en:"Scan removed friends completed",vi:"Hoàn tất Quét bạn cũ"}),description:ke({en:`Found ${i.length} removed friends.\nPlease filter in "Status" column`,vi:`Tìm thấy ${i.length} bạn cũ.\nVui lòng xem cột "Trạng thái"`})}),_e(!1)},vn=async()=>{var e;Oe(!0);const n="AllFriends:onClickScanAddedTime";k(n),pe.loading({key:n,content:ke({en:"Scanning added time...",vi:"Đang quét thời gian..."}),duration:0});const i=[];let t="",l=!1;for(;!l;){const r=await O(t);if(!(null==r?void 0:r.length))break;i.push(...r),t=(null==(e=r[r.length-1])?void 0:e.cursor)||"",Te((e=>a(e,(e=>(r.forEach((n=>{if(!n.uid)return;const i=e.find((e=>e.uid===n.uid));i?(i.time=n.time,i.cursor=n.cursor):e.push(n)})),e))))),pe.loading({key:n,content:s.jsxs(s.Fragment,{children:[ke({en:`Scanning added time... ${i.length}`,vi:`Đang quét thời gian... ${i.length}`}),s.jsx("br",{}),ke({en:"Click to stop",vi:"Bấm để dừng"})]}),duration:0,onClick:()=>{l=!0}}),await D(1e3)}pe.destroy(n),ye.open({type:"success",message:ke({en:"Scan added time completed",vi:"Hoàn tất Quét thời gian"}),description:ke({en:`Scanned ${i.length} friends.`,vi:`Đã quét ${i.length} bạn.`})}),Oe(!1)},kn=async()=>{var e;if(!(await V()))return;Ke(!0);const n="AllFriends:onClickScanBirthday";k(n),pe.loading({key:n,content:ke({en:"Scanning birthday...",vi:"Đang quét ngày sinh..."})});let i="1";const t=[];for(;;){const s=await Y(i);if(console.log(s),!(null==s?void 0:s.length))break;t.push(...s),i=null==(e=s[s.length-1])?void 0:e.cursor,pe.loading({key:n,content:ke({en:`Scanning birthday... ${t.length}`,vi:`Đang quét ngày sinh... ${t.length}`}),duration:0}),Te((e=>a(e,(e=>(t.forEach((n=>{const i=e.find((e=>e.uid===n.uid));i?i.birthday=n.birthday:console.log("new friend birthday",n)})),e))))),await D(500)}pe.destroy(n),ye.open({type:"success",message:ke({en:"🎂 Scan birthday completed",vi:"Hoàn tất 🎂 Quét sinh nhật"}),description:ke({en:`Found ${t.length} friend birthdays.`,vi:`Tìm được ${t.length} sinh nhật.`}),duration:0}),Ke(!1)},pn=async({section:e,key:n,quickFilterType:i,setLoading:t,getFriendsFn:l,addIfNotExists:r=!0})=>{k(n),t(!0);const o=ke(R[i]);pe.loading({key:n,content:ke({en:`Scanning ${o}...`,vi:`Đang quét bạn ${o}...`})});const d=[];let c=null,u=!1;for(;!u;){const t=l?await l(c):await M({cursor:c,section:e});if(!t.length)break;console.log(t),Te((e=>a(e,(e=>(t.forEach((n=>{if(!n.uid)return;const t=e.find((e=>e.uid===n.uid));t?(t.quickFilters||(t.quickFilters=[]),t.quickFilters.includes(i)||t.quickFilters.push(i)):r&&e.push({...n,quickFilters:[i],statuses:[ce.NEW]})})),e))))),d.push(...t),c=t[t.length-1].cursor,pe.loading({key:n,content:s.jsxs(s.Fragment,{children:[ke({en:`Scanning ${o}...`,vi:`Đang quét ${o}...`})," ",d.length,s.jsx("br",{}),ke({en:"Click to stop",vi:"Bấm để dừng"})]}),onClick:()=>u=!0,duration:0}),await D(200)}pe.destroy(n),ye.open({type:"success",message:ke({en:`Scan ${o} completed`,vi:`Hoàn tất quét ${o}`}),description:ke({en:`Found ${d.length} friends.\nPlease see in "Quick filters" column`,vi:`Tìm thấy ${d.length} bạn bè.\nVui lòng xem cột "Bộ lọc"`}),duration:0}),t(!1)},yn=()=>{pn({section:_.current_city,key:"AllFriends:onClickScanCurrentCity",quickFilterType:B.CurrentCity,setLoading:Xe})},xn=()=>{pn({section:_.hometown,key:"AllFriends:onClickScanHomeTown",quickFilterType:B.Hometown,setLoading:ze})},bn=()=>{pn({section:_.high_school,key:"AllFriends:onClickScanHighSchool",quickFilterType:B.Highschool,setLoading:Ze})},jn=()=>{pn({section:_.works,key:"AllFriends:onClickScanWork",quickFilterType:B.Work,setLoading:nn})},Fn=()=>{pn({key:"AllFriends:onClickScanFollowers",quickFilterType:B.Follower,setLoading:sn,getFriendsFn:K})},wn=new Date,Sn=[{title:"#",key:"recent",dataIndex:"recent",sorter:(e,n)=>e.recent-n.recent,render:(e,n,i)=>n.recent+1,width:70,align:"center"},{title:ke({en:"Name",vi:"Tên"}),dataIndex:"name",key:"name",sorter:(e,n)=>e.name.localeCompare(n.name),onSearch:(e,n,i)=>{const t=(null==i?void 0:i.name)+(null==i?void 0:i.uid);return j(e,t)},render:(e,n,i)=>s.jsxs(se,{align:"middle",justify:"start",style:{flex:1},children:[s.jsx(le,{shape:"square",src:s.jsx(re,{src:p(n.uid),fallback:n.avatar}),size:50}),s.jsxs(y,{direction:"vertical",size:0,style:{marginLeft:10},children:[s.jsx(x.Link,{href:n.url||b(n.uid),target:"_blank",strong:!0,children:n.name}),s.jsx(x.Text,{type:"secondary",children:n.uid}),!!n.desc&&s.jsx(x.Text,{type:"secondary",children:n.desc})]})]})},{title:ke({en:"Quick Filters",vi:"Bộ lọc"}),dataIndex:"quickFilters",key:"quickFilters",render:(e,n,i)=>{var t;return(null==(t=n.quickFilters)?void 0:t.map((e=>{var n;return s.jsx(F,{color:(null==(n=R[e])?void 0:n.color)||"default",children:ke(R[e])},e)})))||""},filters:Object.entries(R).map((([e,n])=>({text:ke(n)+" ("+Ae.filter((n=>{var i;return null==(i=n.quickFilters)?void 0:i.includes(e)})).length+")",value:e}))),onFilter:(e,n)=>{var i;return null==(i=n.quickFilters)?void 0:i.includes(e)},width:150},...Ce.find((e=>e.gender))?[{title:ke({en:"Gender",vi:"Giới tính"}),key:"gender",dataIndex:"gender",render:(e,n,i)=>{var t;const l=ke(te[n.gender])||"-";return s.jsx(F,{color:(null==(t=te[n.gender])?void 0:t.color)||"default",children:l})},filters:Object.entries(te).map((([e,n])=>({text:ke(n)+" ("+Ce.filter((n=>n.gender===e)).length+")",value:e}))),onFilter:(e,n)=>n.gender==e,width:120}]:[],...Ce.find((e=>e.mutualFriendCount))?[{title:ke({en:"Mutual",vi:"Bạn chung"}),key:"mutualFriendCount",dataIndex:"mutualFriendCount",sorter:(e,n)=>(e.mutualFriendCount||0)-(n.mutualFriendCount||0),width:120,align:"right",rangeFilter:{getValue:e=>e.mutualFriendCount}}]:[],...$e.length?[{title:ke({en:"Ranking",vi:"Xếp hạng"}),key:"ranking",dataIndex:"ranking",render:(e,n,i)=>-1==e?"_":e+1,sorter:(e,n)=>(-1==e.ranking?1/0:e.ranking)-(-1==n.ranking?1/0:n.ranking),width:120,align:"right",rangeFilter:{getValue:e=>e.ranking}}]:[],...(null==Ne?void 0:Ne.length)?[{title:ke({en:"Reactions",vi:"Thích"}),key:"reactions",dataIndex:"reactions",sorter:(e,n)=>e.reactions-n.reactions,width:100,align:"right"},{title:ke({en:"Comments",vi:"Bình luận"}),key:"comments",dataIndex:"comments",sorter:(e,n)=>e.comments-n.comments,width:100,align:"right"}]:[],...Ce.find((e=>e.time))?[{title:ke({en:"Time",vi:"Thời gian"}),key:"time",dataIndex:"time",render:(e,n,i)=>{if(!n.time)return"";const t=new Date(n.time);return s.jsxs(y,{direction:"vertical",size:0,children:[s.jsx(x.Text,{children:w(t.getTime())}),s.jsx(x.Text,{type:"secondary",children:S(t)})]})},sorter:(e,n)=>(e.time||0)-(n.time||0),width:"16ch"}]:[],...Ce.find((e=>e.birthday))?[{title:ke({en:"Birthday",vi:"Sinh nhật"}),key:"birthday",dataIndex:"birthday",render:(e,n,i)=>{const{year:t,month:l,day:r}=n.birthday||{};if(!l)return"-";const a=new Date,o=l===a.getMonth()+1&&r===a.getDate();a.setFullYear(a.getFullYear(),l-1,r);const d=a.getTime()-Date.now(),c=d>0,u=C(Math.abs(d)),h=Math.floor(d/864e5);return s.jsxs(y,{direction:"vertical",size:0,children:[t&&s.jsxs(x.Text,{strong:!0,children:[T(t,l,r)," ",ke({en:"years",vi:"tuổi"})]}),s.jsxs(x.Text,{type:"secondary",children:[r,"/",l,t?"/"+t:""]}),s.jsx(F,{color:o?"red":c?c&&h<10?"orange":"green":"blue",children:o?ke({en:"Today",vi:"Hôm nay"}):s.jsxs(s.Fragment,{children:[u," ",ke(d<0?{en:"ago",vi:"trước"}:{en:"left",vi:"sau"})]})})]})},sorter:(e,n)=>{const{year:i=0,month:t=0,day:s=0}=e.birthday||{},{year:l=0,month:r=0,day:a=0}=n.birthday||{};return 1e4*i+100*t+s-1e4*l-100*r-a},filters:[{text:ke({en:"Has birthday",vi:"Có ngày sinh"})+` (${Ce.filter((e=>e.birthday)).length})`,value:"yes"},{text:ke({en:"No birthday",vi:"Không ngày sinh"})+` (${Ce.filter((e=>!e.birthday)).length})`,value:"no"},{text:ke({en:"Know ages",vi:"Biết tuổi"})+` (${Ce.filter((e=>{var n;return null==(n=e.birthday)?void 0:n.year})).length})`,value:"year"},{text:ke({en:"Today",vi:"Hôm nay"})+` (${Ce.filter((e=>{const{month:n,day:i}=e.birthday||{};return i==wn.getDate()&&n==wn.getMonth()+1})).length})`,value:"today"},...Array.from({length:12}).map(((e,n)=>({text:ke({en:"Month ",vi:"Tháng "})+(n+1)+` (${Ce.filter((e=>{var i;return(null==(i=e.birthday)?void 0:i.month)===n+1})).length})`,value:n+1})))],onFilter:(e,n)=>{const{year:i,month:t,day:s}=n.birthday||{};return"year"==e?!!i:"yes"==e?!!n.birthday:"no"==e?!n.birthday:"today"==e?s==wn.getDate()&&t==wn.getMonth()+1:t==e},width:120}]:[],{title:ke({en:"Friend status",vi:"Trạng thái"}),dataIndex:"status",key:"status",width:150,filters:Object.entries(ce).map((([e,n])=>({text:ke(he[n]||n)+" ("+Ce.filter((e=>{var i,t;return(null==(i=e.statuses)?void 0:i.includes(n))||n===ce.FRIEND&&!(null==(t=e.statuses)?void 0:t.length)})).length+")",value:n}))),onFilter:(e,n)=>{var i,t;return(null==(i=n.statuses)?void 0:i.includes(e))||e==ce.FRIEND&&!(null==(t=n.statuses)?void 0:t.length)},render:(e,n,i)=>{var t;return(null==(t=null==n?void 0:n.statuses)?void 0:t.length)?n.statuses.map((e=>({key:e,value:ke(he[e]||e)}))).map((({key:e,value:n})=>s.jsx(F,{color:ue[e],children:n},e))):ke({en:"Friend",vi:"Bạn bè"})},align:"right"},{title:ke({en:"Action",vi:"Hành động"}),dataIndex:"action",key:"download",render:(e,n,i)=>{var t,a;return s.jsxs(y.Compact,{children:[s.jsx(N,{title:ke({en:"Friendship",vi:"Xem tình bạn"}),children:s.jsx(l,{type:"default",onClick:()=>(e=>{k("AllFriends:onClickViewFriendship");const n=`https://www.facebook.com/friendship/${null==Fe?void 0:Fe.uid}/${e.uid}`;window.open(n,"_blank")})(n),icon:s.jsx("i",{className:"fa-solid fa-eye"})})}),s.jsx(N,{title:ke({en:"Bulk Downloader",vi:"Tải hàng loạt"}),children:s.jsx(l,{type:"default",onClick:()=>{return e=n.uid,void fe("/bulk-downloader",{state:{targetId:e,platform:r.Facebook}});var e},icon:s.jsx("i",{className:"fa-solid fa-download"})})}),s.jsx(N,{title:ke({en:"Poke",vi:"Chọc"}),children:s.jsx(l,{type:"default",onClick:()=>dn(n,!0),icon:s.jsx("i",{className:"fa-regular fa-hand-point-right"})})}),(null==(t=n.statuses)?void 0:t.includes(ce.UNFRIENDED))||(null==(a=n.statuses)?void 0:a.includes(ce.NEW))?s.jsx(N,{title:ke({en:"Request friend",vi:"Gửi kết bạn"}),children:s.jsx(l,{type:"default",onClick:()=>cn(n,!0),icon:s.jsx("i",{className:"fa-solid fa-user-plus"})})}):s.jsx(N,{title:ke({en:"Unfriend",vi:"Huỷ kết bạn"}),children:s.jsx(l,{danger:!0,onClick:()=>on(n,!0),icon:s.jsx("i",{className:"fa-solid fa-trash-can"})})})]})},width:150,align:"right"}],Cn=e=>[{type:"group",label:ke({en:"Interaction",vi:"Tương tác"}),children:[{key:"ranking",label:s.jsx(oe,{placement:"right",title:ke({en:"Ranking",vi:"Xếp hạng"}),content:s.jsx("ul",{children:ke({en:"📊 Rank who has interacted with you the most recently\n                                ❓ Also called Recent interactions",vi:"📊 Xem ai tương tác với bạn nhiều nhất thời gian gần đây\n                                ❓ Còn gọi là Tương tác gần đây"}).split("\n").map(((e,n)=>s.jsx("li",{children:e.trim()},n)))}),children:s.jsx(l,{onClick:gn,icon:s.jsx("i",{className:"fa-solid fa-ranking-star"}),children:ke({en:"Ranking",vi:"Xếp hạng"})})})},{key:"deep-scan",label:s.jsx(ee,{type:"hot",children:s.jsx(oe,{placement:"right",title:ke({en:"Deep Scan",vi:"Quét Chuyên Sâu"}),content:s.jsxs(s.Fragment,{children:[ke({en:"🔎 Scan all friends to filter:",vi:"🔎 Quét tất cả bạn bè để lọc:"}),s.jsx("ul",{children:ke({en:"🎯 gender\n                                                        🔒 locked friends\n                                                        🫂 mutual friends",vi:"🎯 giới tính\n                                                        🔒 bạn bè bị khoá\n                                                        🫂 bạn chung"}).split("\n").map(((e,n)=>s.jsx("li",{children:e.trim()},n)))})]}),children:s.jsx(l,{loading:!!He,onClick:mn,icon:s.jsx("i",{className:"fa-solid fa-magnifying-glass"}),children:ke({en:"Deep scan",vi:"Quét chuyên sâu"})})})})},{key:"scan-interactions",label:s.jsx(ee,{type:"hot",children:s.jsx(oe,{placement:"right",title:ke({en:"Interaction Scan",vi:"Quét Tương Tác"}),content:s.jsx("ul",{children:ke({en:"👍💬 Count friend's reactions and comments on all of your profile's posts\n                                ❓ Also called Total interactions",vi:"👍💬 Đếm like và comment của bạn bè trên tất cả bài viết của bạn\n                                ❓ Còn gọi là Tương tác tổng"}).split("\n").map(((e,n)=>s.jsx("li",{children:e.trim()},n)))}),children:s.jsx(l,{loading:Le,icon:s.jsx("i",{className:"fa-solid fa-thumbs-up"}),onClick:hn,children:ke({en:"Scan interactions",vi:"Quét tương tác"})})})})},{key:"detect-unfriend",label:s.jsx(Z,{accept:".json",title:ke({en:"Upload friends file",vi:"Tải lên file bạn bè đã lưu"}),text:ke({en:"Click or drag file to this area to upload",vi:"Click chọn hoặc kéo thả file vào đây"}),hint:ke({en:"Support only .json backup file",vi:"Chỉ hỗ trợ file .json được xuất từ trang này"}),onSubmit:un,renderButton:({showModal:e})=>s.jsx(oe,{placement:"right",title:ke({en:"Check who unfriend you",vi:"Kiểm tra ai huỷ kết bạn với bạn"}),content:s.jsx("ul",{children:ke({en:"❓ How it work: Export friends data to file, then compare it later\n                                    ✅ FB AIO can automatically detect who unfriend you\n                                    🚨 Only use this feature if you want manual check",vi:"❓ Cách hoạt động: Lưu danh sách bạn bè ra file trước, sau đó so sánh\n                                    ✅ FB AIO sẽ tự động thông báo khi có người huỷ kết bạn\n                                    🚨 Chỉ sử dụng chức năng này nếu bạn muốn chủ động kiểm tra bằng tay"}).split("\n").map(((e,n)=>s.jsx("li",{children:e.trim()},n)))}),children:s.jsx(l,{onClick:e,icon:s.jsx("i",{className:"fa-solid fa-user-slash"}),children:ke({en:"Detect unfriend",vi:"Quét huỷ kết bạn"})})})})},{key:"detect-block-msg",label:s.jsx(oe,{placement:"right",title:ke({en:"Check who is blocking your messages",vi:"Xem ai đang chặn tin nhắn của bạn"}),content:s.jsx("ul",{children:ke({en:"✅ You still can message them\n                                ✅ They can still read your messages\n                                ❌ But facebook will never mark as read",vi:"✅ Bạn vẫn nhắn được cho họ\n                                ✅ Họ vẫn đọc được tin nhắn\n                                ❌ Nhưng facebook sẽ không cập nhật trạng thái đã xem"}).split("\n").map(((e,n)=>s.jsx("li",{children:e.trim()},n)))}),children:s.jsx(de,{title:ke(ke({en:"Check who is blocking your messages",vi:"Xem ai đang chặn tin nhắn của bạn"})),description:s.jsxs("ul",{children:[s.jsx("li",{children:ke({en:`Are you sure to check ${(null==e?void 0:e.length)||Ae.length} friends?.\n `,vi:`Bạn có chắc muốn kiểm tra ${(null==e?void 0:e.length)||Ae.length} bạn bè?.`})}),s.jsx("li",{children:ke({en:"You can select friends to check, instead of check all, to reduce load time.",vi:"Bạn có thể chọn bạn bè để kiểm tra, thay vì kiểm tra tất cả, để giảm thời gian xử lý."})})]}),onConfirm:()=>{(async()=>{var e,n;if(Ue)return;const i="AllFriends:onClickFindBlockedMessages";k(i);const t=null==(e=ln.current)?void 0:e.getDataSelected(),l=(null==t?void 0:t.length)?t:Ce;let r=0;const o=[];let d=!1;for(let u=0;u<l.length&&!d;u++){Re(!0),pe.loading({key:i,duration:0,content:s.jsxs(s.Fragment,{children:[ke({en:"Finding blocked messages...",vi:"Đang tìm bạn bè chặn tin nhắn..."})+`${o.length}/${u+1} - ${~~(u/l.length*100)}%`,s.jsx("br",{}),s.jsx("i",{children:ke({en:"Click to stop",vi:"Bấm để dừng"})})]}),onClick:()=>d=!0});const e=l[u];await X(e.uid)||(o.push(e),null==(n=ln.current)||n.setDataSelected(o.map((e=>Ce.find((n=>n.uid===e.uid))||e))),Te((n=>a(n,(n=>{var i;const t=n.find((n=>n.uid===e.uid));t&&!(null==(i=t.quickFilters)?void 0:i.includes(B.BlockedMessages))&&(t.quickFilters||(t.quickFilters=[]),t.quickFilters.push(B.BlockedMessages))})))),pe.success({key:i,content:ke({en:"Found",vi:"Tìm thấy"})+" "+e.name+" ("+o.length+")"})),r++}Re(!1);const c=o.length?s.jsxs(s.Fragment,{children:[ke({en:`Found ${o.length} friends blocking your messages`,vi:`Tìm thấy ${o.length} bạn bè chặn tin nhắn`}),s.jsx("br",{}),s.jsx("ul",{children:o.map((e=>s.jsx("li",{children:e.name},e.uid)))})]}):ke({en:"No one block messages you",vi:"Không ai chặn tin nhắn của bạn"});pe.success({key:i,content:c}),ye.open({type:"success",message:ke({en:"Scan block message success "+r+" users",vi:"Hoàn tất Quét chặn tin nhắn "+r+" người"}),description:c,duration:0})})()},okText:ke({en:"Yes",vi:"Kiểm tra"}),cancelText:ke({en:"No",vi:"Huỷ"}),children:s.jsx(l,{loading:!!Ue,icon:s.jsx("i",{className:"fa-solid fa-comment-slash"}),children:ke({en:"Block messages",vi:"Chặn tin nhắn"})})})})}]},{type:"group",label:ke({en:"Similarity",vi:"Điểm chung"}),children:[{key:"birthday",label:s.jsx(oe,{placement:"right",title:ke({en:"Scan Birthday",vi:"Quét Sinh nhật"}),content:ke({en:"🎂 Scan all friend's birthday",vi:"🎂 Quét sinh nhật của tất cả bạn bè"}),children:s.jsx(l,{loading:We,onClick:kn,icon:s.jsx("i",{className:"fa-solid fa-birthday-cake"}),children:ke({en:"Scan Birthday",vi:"Quét sinh nhật"})})})},{key:"current-city",label:s.jsx(oe,{placement:"right",title:ke({en:"Current city",vi:"Cùng tỉnh thành"}),content:ke({en:"🏙️ Scan all friends that live in the same city as you",vi:"🏙️ Quét tất cả bạn bè đang sống cùng tỉnh thành với bạn"}),children:s.jsx(l,{loading:Ge,onClick:yn,icon:s.jsx("i",{className:"fa-solid fa-location"}),children:ke({en:"Current city",vi:"Cùng tỉnh thành"})})})},{key:"hometown",label:s.jsx(oe,{placement:"right",title:ke({en:"Hometown",vi:"Cùng quê"}),content:ke({en:"🏡 Scan all friends that has same hometown as you",vi:"🏡 Quét tất cả bạn bè cùng quê quán với bạn"}),children:s.jsx(l,{loading:Ve,onClick:xn,icon:s.jsx("i",{className:"fa-solid fa-home"}),children:ke({en:"Hometown",vi:"Cùng quê"})})})},{key:"high-school",label:s.jsx(oe,{placement:"right",title:ke({en:"High school",vi:"Cùng trung học"}),content:ke({en:"📚 Scan all friends that has same high school as you",vi:"📚 Quét tất cả bạn bè cùng trường trung học với bạn"}),children:s.jsx(l,{loading:Je,onClick:bn,icon:s.jsx("i",{className:"fa-solid fa-graduation-cap"}),children:ke({en:"High school",vi:"Cùng trung học"})})})},{key:"work",label:s.jsx(oe,{placement:"right",title:ke({en:"Scan work",vi:"Cùng công việc"}),content:ke({en:"💼 Scan all friends that work at your company",vi:"💼 Quét tất cả bạn bè cùng công việc (công ty) với bạn"}),children:s.jsx(l,{loading:en,onClick:jn,icon:s.jsx("i",{className:"fa-solid fa-building"}),children:ke({en:"Scan work",vi:"Cùng công việc"})})})},{key:"follower",label:s.jsx(ee,{type:"new",children:s.jsx(oe,{placement:"right",title:ke({en:"Scan followers",vi:"Người theo dõi"}),content:ke({en:"👉 Scan all your followers",vi:"👉 Quét tất người theo dõi của bạn"}),children:s.jsx(l,{loading:tn,onClick:Fn,icon:s.jsx("i",{className:"fa-solid fa-person-walking-arrow-right"}),children:ke({en:"Scan followers",vi:"Người theo dõi"})})})})}]},{type:"group",label:ke({en:"History",vi:"Lịch sử"}),children:[{key:"scan-added-time",label:s.jsx(oe,{placement:"right",title:ke({en:"Scan Added Time",vi:"Quét Thời Gian"}),content:ke({en:"🕓 Scan the time you make friend with all your friends",vi:"🕓 Quét thời gian mà bạn kết bạn với tất cả bạn bè"}),children:s.jsx(l,{loading:Ye,onClick:vn,icon:s.jsx("i",{className:"fa-solid fa-clock"}),children:ke({en:"Scan time",vi:"Quét thời gian"})})})},{key:"scan-removed",label:s.jsx(oe,{placement:"right",title:ke({en:"Scan Removed Friends",vi:"Quét Bạn Cũ"}),content:ke({en:"🗑️ Scan to show all your removed friends",vi:"🗑️ Quét tất cả bạn bè bạn đã huỷ kết bạn trong quá khứ"}),children:s.jsx(l,{loading:Me,onClick:fn,icon:s.jsx("i",{className:"fa-solid fa-user-minus"}),children:ke({en:"Scan removed",vi:"Quét bạn cũ"})})})}]},{key:"topid",label:s.jsx(oe,{placement:"right",title:ke({en:"TopID - Auto make friends using AI",vi:"TopID - Tự động kết bạn bằng AI"}),content:s.jsx("ul",{children:ke({vi:"👫 Kết bạn theo tiêu chí: bạn chung, giới tính\n                    🚫 Bỏ qua nếu không thỏa tiêu chí\n                    👀 Kết bạn với danh sách bạn của người khác\n                    🫶 Kết bạn với danh sách gợi ý bạn bè của Facebook\n                    🎯 Tận dụng bộ lọc có sẵn: cùng tỉnh, cùng quê, mới thêm gần đây, ...\n                    ⏳ Thêm thời gian chờ: tự động bấm thủ công trên giao diện để tránh bị ban",en:"👫 Add friends based on criteria: mutual friends, gender\n                    🚫 Skip if criteria not met\n                    👀 Add friends of others\n                    🫶 Add friends from Facebook's suggested list\n                    🎯 Filter options: same province, hometown, recently added, etc.\n                    ⏳ Include wait time: manually click to avoid getting banned"}).split("\n").map(((e,n)=>s.jsx("li",{children:e.trim()},n)))}),children:s.jsx(l,{target:"_blank",href:o.TopID.webstore,icon:s.jsx("i",{className:"fa-solid fa-robot"}),children:ke({en:"TopID",vi:"TopID"})})})}];return s.jsx(ie,{title:ke({en:"Friends manager",vi:"Quản lý bạn bè"}),titleSuffix:s.jsx(F,{style:{marginLeft:"10px",fontWeight:"bold",color:"#888"},children:Ae.length}),children:s.jsx(c,{ref:ln,data:Ae,columns:Sn,size:"small",searchable:!0,selectable:!0,keyExtractor:e=>e.uid,renderTitle:e=>{var n,i;const t=null==(n=null==e?void 0:e.filter)?void 0:n.call(e,ge),r=null==(i=null==e?void 0:e.filter)?void 0:i.call(e,me);return s.jsxs(s.Fragment,{children:[s.jsx(l,{type:"primary",icon:Ie?s.jsx("i",{className:"fa-solid fa-rotate-right fa-spin"}):s.jsx("i",{className:"fa-solid fa-rotate-right"}),onClick:an,children:ke({en:"Reload",vi:"Tải lại"})}),s.jsx(u,{data:e.length?e:Ce,options:[{key:"uid",label:".txt (friend uid)",prepareData:e=>({fileName:"friends_"+d().format("YYYY-MM-DD-HHmmss")+".txt",data:e.map((e=>e.uid)).join("\n")})},{key:"uid_name",label:".csv (friend uid+name)",prepareData:e=>({fileName:"friends_uid_name_"+d().format("YYYY-MM-DD-HHmmss")+".csv",data:$(e.map((e=>({uid:e.uid,name:e.name}))))})},{key:"json",label:".json",prepareData:e=>({fileName:"friends"+d().format("YYYY-MM-DD-HHmmss")+".json",data:JSON.stringify(e,null,4)})},{key:"csv",label:".csv",prepareData:e=>({fileName:"friends"+d().format("YYYY-MM-DD-HHmmss")+".csv",data:$(e)})}]}),s.jsx(ee,{type:"new",children:s.jsx(ae,{menu:{items:Cn(e)},arrow:!0,children:s.jsx(l,{icon:s.jsx("i",{className:"fa-solid fa-toolbox"}),children:ke({en:"Tools",vi:"Công cụ"})})})}),s.jsx(z,{name:"AllFriends",text:Ce.map((e=>e.name)).join(" ")}),s.jsx(y.Compact,{children:(null==e?void 0:e.length)?s.jsxs(s.Fragment,{children:[s.jsx(N,{title:ke({en:`Poke ${e.length} selected friends`,vi:`Chọc ${e.length} người được chọn`}),children:s.jsx(l,{type:"default",icon:s.jsx("i",{className:"fa-solid fa-hand-point-right"}),onClick:()=>(async e=>be({key:"AllFriends:onClickPokeSelected",data:e,loadingText:e=>ke({en:"Poking...",vi:"Đang chọc..."}),successText:(e,n)=>ke({en:"Poke friends completed",vi:"Chọc bạn bè xong"}),actionFn:dn,waitTime:[500,1e3],successDescItem:e=>e.name,confirmProps:{title:ke({en:"Poke "+e.length+" users?",vi:"Chọc "+e.length+" người?"})}}))(e),children:e.length?" "+e.length:""})}),(null==t?void 0:t.length)>0?s.jsx(N,{title:ke({en:`Request friend with ${t.length} selected users`,vi:`Gửi kết bạn với ${t.length} người được chọn`}),children:s.jsx(l,{icon:s.jsx("i",{className:"fa-solid fa-user-plus"}),onClick:()=>(async e=>be({key:"AllFriends:onClickAddFriendSelected",data:e,loadingText:e=>ke({en:"Sending friend request...",vi:"Đang gửi kết bạn..."}),successText:(e,n)=>ke({en:"Send friend requests completed",vi:"Gửi kết bạn xong"}),actionFn:cn,waitTime:[500,1e3],successDescItem:e=>e.name,confirmProps:{title:ke({en:"Send friend request to "+e.length+" users?",vi:"Gứi kết bạn tới "+e.length+" người?"})}}))(t),children:t.length})}):null,(null==r?void 0:r.length)>0?s.jsx(N,{title:ke({en:`Unfriend ${r.length} selected friends`,vi:`Huỷ kết bạn ${r.length} người được chọn`}),children:s.jsx(l,{danger:!0,icon:s.jsx("i",{className:"fa-solid fa-trash-can"}),onClick:()=>(async e=>be({key:"AllFriends:onClickUnfriendSelected",data:e,loadingText:e=>ke({en:"Unfriending...",vi:"Đang huỷ kết bạn..."}),successText:(e,n)=>ke({en:"Unfriend completed",vi:"Huỷ kết bạn xong"}),actionFn:on,waitTime:[500,1e3],successDescItem:e=>e.name,confirmProps:{title:ke({en:"Unfriend "+e.length+" users?",vi:"Huỷ kết bạn "+e.length+" người?"})}}))(r),children:r.length})}):null]}):null})]})}})})}export{fe as default};
