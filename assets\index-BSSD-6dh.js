const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./WebTimerChart-DCvi2W1a.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./dayjs.min-CzPn7FMI.js","./MyApp-DW5WH4Ub.js","./Table-BO80-bCE.js","./addEventListener-C4tIKh7N.js","./List-B6ZMXgC_.js","./DownOutlined-B-JcS-29.js","./index-D6X7PZwe.js","./index-CxAc8H5Q.js","./index-QU7lSj_a.js","./index-Dg3cFar0.js","./Dropdown-BcL-JHCf.js","./PurePanel-BvHjNOhQ.js","./move-ESLFEEsv.js","./index-C5gzSBcY.js","./index-SRy1SMeO.js","./SearchOutlined--mXbzQ68.js","./useBreakpoint-CPcdMRfj.js","./Pagination-2X6SDgot.js","./index-Bg865k-U.js","./EyeOutlined-CNKPohhw.js","./index-DwLJmsHL.js","./ClockCircleOutlined-BVovxNRp.js","./UploadModal-iOZ41qZ5.js","./index-D3qvoGmV.js","./index-Bx3o6rwA.js","./index-BVNdrzmU.js","./progress-ApOaEpgr.js"])))=>i.map(i=>d[i]);
import{cj as t,ck as e,cl as r,cm as n,cn as i,co as o,a_ as a,aY as u,cp as c,cq as s,cr as f,aW as l,aX as d,aZ as m,cs as v,a$ as p,ct as h,cu as j,aJ as b,r as x,c7 as y,b0 as g,b9 as w,bb as _,b1 as T,b5 as O,aN as k}from"./index-Cak6rALw.js";import{isScriptEnabled as W}from"./AutoRun-C_QIEDH2.js";import{S as C}from"./Screen-Buq7HJpK.js";import{u as S,d as B,S as A,A as D,c as E,T as I,x as P,aN as N,I as L}from"./MyApp-DW5WH4Ub.js";import{r as F}from"./_copyArray-_bDJu6nu.js";import H from"./useCacheState-CAnxkewm.js";import{D as V}from"./index-CC5caqt_.js";import{S as M}from"./index-Bp2s1Wws.js";import{E as R}from"./index-SRy1SMeO.js";import"./BadgeWrapper-BCbYXXfB.js";import"./adsLinkList-C2Suxade.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-jrOnBmdW.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./DownOutlined-B-JcS-29.js";import"./SearchOutlined--mXbzQ68.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";var q,U,X,$,G,J,K,Q,Y,Z;function z(){if(U)return q;U=1;var e=t(),r=function(){try{var t=e(Object,"defineProperty");return t({},"",{}),t}catch(r){}}();return q=r}function tt(){if($)return X;$=1;var t=z();return X=function(e,r,n){"__proto__"==r&&t?t(e,r,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[r]=n}}function et(){if(J)return G;J=1;var t=tt(),r=e();return G=function(e,n,i){(void 0!==i&&!r(e[n],i)||void 0===i&&!(n in e))&&t(e,n,i)}}function rt(){if(Z)return Y;Z=1;var t=(Q?K:(Q=1,K=function(t){return function(e,r,n){for(var i=-1,o=Object(e),a=n(e),u=a.length;u--;){var c=a[t?u:++i];if(!1===r(o[c],c,o))break}return e}}))();return Y=t}var nt,it,ot,at,ut,ct,st,ft,lt,dt,mt,vt,pt,ht,jt,bt,xt,yt,gt,wt,_t,Tt,Ot,kt,Wt,Ct,St,Bt,At,Dt,Et,It,Pt,Nt,Lt,Ft,Ht,Vt,Mt,Rt,qt,Ut,Xt,$t,Gt,Jt,Kt,Qt,Yt,Zt,zt,te,ee,re,ne,ie={exports:{}};function oe(){if(ut)return at;ut=1;var t=function(){if(ot)return it;ot=1;var t=n();return it=function(e){var r=new e.constructor(e.byteLength);return new t(r).set(new t(e)),r}}();return at=function(e,r){var n=r?t(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}}function ae(){if(lt)return ft;lt=1;var t=o()(Object.getPrototypeOf,Object);return ft=t}function ue(){if(mt)return dt;mt=1;var t=function(){if(st)return ct;st=1;var t=i(),e=Object.create;return ct=function(){function r(){}return function(n){if(!t(n))return{};if(e)return e(n);r.prototype=n;var i=new r;return r.prototype=void 0,i}}()}(),e=ae(),r=a();return dt=function(n){return"function"!=typeof n.constructor||r(n)?{}:t(e(n))}}function ce(){if(xt)return bt;return xt=1,bt=function(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]}}function se(){if(_t)return wt;_t=1;var t=function(){if(gt)return yt;gt=1;var t=tt(),r=e(),n=Object.prototype.hasOwnProperty;return yt=function(e,i,o){var a=e[i];n.call(e,i)&&r(a,o)&&(void 0!==o||i in e)||t(e,i,o)}}(),r=tt();return wt=function(e,n,i,o){var a=!i;i||(i={});for(var u=-1,c=n.length;++u<c;){var s=n[u],f=o?o(i[s],e[s],s,i,e):void 0;void 0===f&&(f=e[s]),a?r(i,s,f):t(i,s,f)}return i}}function fe(){if(Wt)return kt;Wt=1;var t=i(),e=a(),r=Ot?Tt:(Ot=1,Tt=function(t){var e=[];if(null!=t)for(var r in Object(t))e.push(r);return e}),n=Object.prototype.hasOwnProperty;return kt=function(i){if(!t(i))return r(i);var o=e(i),a=[];for(var u in i)("constructor"!=u||!o&&n.call(i,u))&&a.push(u);return a}}function le(){if(St)return Ct;St=1;var t=f(),e=fe(),r=u();return Ct=function(n){return r(n)?t(n,!0):e(n)}}function de(){if(Et)return Dt;Et=1;var t,e,n,o,a,f,h,j=et(),b=(nt||(nt=1,t=ie,e=ie.exports,n=r(),o=e&&!e.nodeType&&e,a=o&&t&&!t.nodeType&&t,f=a&&a.exports===o?n.Buffer:void 0,h=f?f.allocUnsafe:void 0,t.exports=function(t,e){if(e)return t.slice();var r=t.length,n=h?h(r):new t.constructor(r);return t.copy(n),n}),ie.exports),x=oe(),y=F(),g=ue(),w=l(),_=d(),T=function(){if(pt)return vt;pt=1;var t=u(),e=c();return vt=function(r){return e(r)&&t(r)}}(),O=m(),k=v(),W=i(),C=function(){if(jt)return ht;jt=1;var t=s(),e=ae(),r=c(),n=Function.prototype,i=Object.prototype,o=n.toString,a=i.hasOwnProperty,u=o.call(Object);return ht=function(n){if(!r(n)||"[object Object]"!=t(n))return!1;var i=e(n);if(null===i)return!0;var c=a.call(i,"constructor")&&i.constructor;return"function"==typeof c&&c instanceof c&&o.call(c)==u}}(),S=p(),B=ce(),A=function(){if(At)return Bt;At=1;var t=se(),e=le();return Bt=function(r){return t(r,e(r))}}();return Dt=function(t,e,r,n,i,o,a){var u=B(t,r),c=B(e,r),s=a.get(c);if(s)j(t,r,s);else{var f=o?o(u,c,r+"",t,e,a):void 0,l=void 0===f;if(l){var d=_(c),m=!d&&O(c),v=!d&&!m&&S(c);f=c,d||m||v?_(u)?f=u:T(u)?f=y(u):m?(l=!1,f=b(c,!0)):v?(l=!1,f=x(c,!0)):f=[]:C(c)||w(c)?(f=u,w(u)?f=A(u):W(u)&&!k(u)||(f=g(c))):l=!1}l&&(a.set(c,f),i(f,c,n,o,a),a.delete(c)),j(t,r,f)}}}function me(){if(Lt)return Nt;return Lt=1,Nt=function(t){return t}}function ve(){if(Mt)return Vt;Mt=1;var t=Ht?Ft:(Ht=1,Ft=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}),e=Math.max;return Vt=function(r,n,i){return n=e(void 0===n?r.length-1:n,0),function(){for(var o=arguments,a=-1,u=e(o.length-n,0),c=Array(u);++a<u;)c[a]=o[n+a];a=-1;for(var s=Array(n+1);++a<n;)s[a]=o[a];return s[n]=i(c),t(r,this,s)}},Vt}function pe(){if(Xt)return Ut;Xt=1;var t=qt?Rt:(qt=1,Rt=function(t){return function(){return t}}),e=z(),r=me();return Ut=e?function(r,n){return e(r,"toString",{configurable:!0,enumerable:!1,value:t(n),writable:!0})}:r}function he(){if(Kt)return Jt;Kt=1;var t=pe(),e=function(){if(Gt)return $t;Gt=1;var t=Date.now;return $t=function(e){var r=0,n=0;return function(){var i=t(),o=16-(i-n);if(n=i,o>0){if(++r>=800)return arguments[0]}else r=0;return e.apply(void 0,arguments)}},$t}(),r=e(t);return Jt=r}function je(){if(ee)return te;ee=1;var t=function(){if(Yt)return Qt;Yt=1;var t=me(),e=ve(),r=he();return Qt=function(n,i){return r(e(n,i,t),n+"")}}(),r=function(){if(zt)return Zt;zt=1;var t=e(),r=u(),n=j(),o=i();return Zt=function(e,i,a){if(!o(a))return!1;var u=typeof i;return!!("number"==u?r(a)&&n(i,a.length):"string"==u&&i in a)&&t(a[i],e)}}();return te=function(e){return t((function(t,n){var i=-1,o=n.length,a=o>1?n[o-1]:void 0,u=o>2?n[2]:void 0;for(a=e.length>3&&"function"==typeof a?(o--,a):void 0,u&&r(n[0],n[1],u)&&(a=o<3?void 0:a,o=1),t=Object(t);++i<o;){var c=n[i];c&&e(t,c,i,a)}return t}))}}const be=b(function(){if(ne)return re;ne=1;var t=function(){if(Pt)return It;Pt=1;var t=h(),e=et(),r=rt(),n=de(),o=i(),a=le(),u=ce();return It=function i(c,s,f,l,d){c!==s&&r(s,(function(r,a){if(d||(d=new t),o(r))n(c,s,a,f,i,l,d);else{var m=l?l(u(c,a),r,a+"",c,s,d):void 0;void 0===m&&(m=r),e(c,a,m)}}),a)},It}(),e=je()((function(e,r,n){t(e,r,n)}));return re=e}()),xe=T((()=>k((()=>import("./WebTimerChart-DCvi2W1a.js").then((t=>t.W))),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24]),import.meta.url)),{fallback:L}),ye=T((()=>k((()=>import("./UploadModal-iOZ41qZ5.js")),__vite__mapDeps([25,1,2,26,27,14,28,19,4,22,29]),import.meta.url)),{fallback:N}),ge="web_timer",we="web_timer_show_time_on_title";function _e(){const{ti:t}=S(),{message:e}=B(),[r,n]=H("WebTimer.data",{}),[i,o]=H("WebTimer.showTimerOnTitle",!1),[a,u]=H("WebTimer.enabled",!1);x.useEffect((()=>{W("web_timer").then(u)}),[]),x.useEffect((()=>{y(ge).then((t=>{console.log("webtimer ne",t),n(t||{})})),y(we).then(o)}),[]);return g.jsx(C,{title:"⏰ WebTimer",mode:"center",children:g.jsxs(A,{direction:"vertical",align:"center",children:[g.jsx(D,{type:"info",showIcon:!0,message:g.jsxs(g.Fragment,{children:[t({vi:"Đếm thời gian truy cập internet",en:"Count time you spend on internet"})," ",g.jsxs("a",{href:"https://www.facebook.com/groups/fbaio/posts/1661830854471596/",target:"_blank",children:[t({vi:"Tìm hiểu thêm",en:"Learn more"})," ",g.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})]})]})}),a?null:g.jsx(D,{type:"error",showIcon:!0,message:t({vi:g.jsx(g.Fragment,{children:g.jsxs(E.Text,{children:["WebTimer chưa được kích hoạt. Vui lòng kích hoạt tại"," ",g.jsx(w,{to:"/autorun",children:"Tự chạy"})]})}),en:g.jsx(g.Fragment,{children:g.jsxs(E.Text,{children:["WebTimer is not enabled. Please enable at"," ",g.jsx(w,{to:"/autorun",children:"AutoRun"})]})})})}),g.jsx(V,{}),g.jsxs(A,{direction:"vertical",align:"center",children:[g.jsxs(A,{children:[g.jsx(E.Text,{children:t({vi:"Hiển thị thời gian trên tiêu đề trang web",en:"Show timer on website title"})}),g.jsx(M,{checked:i,onChange:t=>{o(t),_(we,t)},checkedChildren:t({vi:"Hiện",en:"Show"}),unCheckedChildren:t({vi:"Ẩn",en:"Hide"})})]}),g.jsxs(A.Compact,{children:[g.jsx(ye,{accept:".json",title:t({vi:"Nhập dữ liệu WebTimer",en:"Import WebTimer data"}),text:t({vi:"Click chọn hoặc kéo thả file .json vào đây",en:"Click or drag .json file to this area to upload"}),hint:t({vi:"Hỗ trợ file .json xuất từ FB AIO hoặc Useful Script",en:"Support .json file exported from FB AIO or Useful Script"}),renderButton:({showModal:e})=>g.jsx(I,{title:t({vi:"Nhập dữ liệu từ file, để khôi phục",en:"Import data from file, to restore"}),placement:"bottom",children:g.jsx(O,{icon:g.jsx("i",{className:"fa-solid fa-file-import"}),onClick:e,children:t({vi:"Nhập",en:"Import"})})}),onSubmit:async r=>{var i;const o=P(r),a=(null==(i=null==o?void 0:o.chromeStorage)?void 0:i.web_timer)||o||{},u=Object.keys(a).length;if(u){const r=(await k((async()=>{const{default:t}=await import("./sweetalert2.esm.all-BZxvatOx.js");return{default:t}}),[],import.meta.url)).default,i=await r.fire({icon:"question",title:t({vi:"Nhập dữ liệu WebTimer",en:"Import WebTimer data"}),text:t({vi:`Tìm thấy ${u} ngày. Vui lòng chọn chế độ`,en:`Found ${u} days. Please select mode`}),showCancelButton:!0,showDenyButton:!0,showConfirmButton:!0,confirmButtonText:t({vi:"Ghi đè",en:"Override"}),cancelButtonText:t({vi:"Hủy",en:"Cancel"}),denyButtonText:t({vi:"Hợp nhất",en:"Merge"})});if(i.isConfirmed){(await r.fire({icon:"question",title:t({vi:"Xác nhận ghi đè",en:"Confirm override"}),text:t({vi:"Dữ liệu cũ sẽ bị xoá. Bạn có chắc chắn muốn tiếp tục?",en:"The old data will be deleted. Are you sure you want to continue?"}),showCancelButton:!0,confirmButtonText:t({vi:"Ghi đè",en:"Override"}),cancelButtonText:t({vi:"Hủy",en:"Cancel"})})).isConfirmed&&(n(a),_(ge,a),e.success(t({vi:"Đã ghi đè dữ liệu WebTimer",en:"WebTimer data overridden"})+" "+Object.keys(a).length+" "+t({vi:"ngày",en:"days"})))}else i.isDenied&&n((r=>{const n=be({},r,a);return _(ge,n),e.success(t({vi:"Đã hợp nhất dữ liệu WebTimer",en:"WebTimer data merged"})+" "+Object.keys(n).length+" "+t({vi:"ngày",en:"days"})),n}))}else e.error(t({vi:"Không tìm thấy dữ liệu WebTimer",en:"WebTimer data not found"}))}}),g.jsx(I,{title:t({vi:"Xuất dữ liệu ra file, để lưu trữ",en:"Export data to file, to save"}),placement:"bottom",children:g.jsx(O,{icon:g.jsx("i",{className:"fa-solid fa-download"}),onClick:async()=>{const t=await y(ge),{downloadData:e}=await k((async()=>{const{downloadData:t}=await import("./download-KJKGHAGZ.js");return{downloadData:t}}),[],import.meta.url);e(JSON.stringify(t),"web_timer_"+(new Date).toISOString()+".json")},children:t({vi:"Xuất",en:"Export"})})})]})]}),g.jsx(V,{}),Object.keys(r).length>0?g.jsx(xe,{data:r}):g.jsx(R,{description:t({vi:"Không có dữ liệu",en:"No data"})})]})})}export{_e as default};
