const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./StoryViewers-B7IqR_CO.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./MyApp-DW5WH4Ub.js","./Collection-BYcChfHL.js","./index-ByRdMNW-.js","./useCacheState-CAnxkewm.js","./react-hotkeys-hook.esm-wjq6pdfC.js","./index-PbLYNhdQ.js","./index-QU7lSj_a.js","./index-CxAc8H5Q.js","./index-Dg3cFar0.js","./Dropdown-BcL-JHCf.js","./PurePanel-BvHjNOhQ.js","./move-ESLFEEsv.js","./index-Bg865k-U.js","./EyeOutlined-CNKPohhw.js","./SearchOutlined--mXbzQ68.js","./index-jrOnBmdW.js","./useBreakpoint-CPcdMRfj.js","./index-C5gzSBcY.js","./List-B6ZMXgC_.js","./index-SRy1SMeO.js","./DownOutlined-B-JcS-29.js","./row-BMfM-of4.js","./Pagination-2X6SDgot.js","./col-CzA09p0P.js","./index-CC5caqt_.js","./stories-DQ6hTlzb.js","./index-DdM4T8-Q.js","./VideoWithMuted-CYlwayKv.js"])))=>i.map(i=>d[i]);
import{aT as e,aS as i,aQ as t,r as s,b0 as r,b1 as a,b5 as o,aN as n}from"./index-Cak6rALw.js";import{u as l,S as c,c as d,f as x,h,i as m,I as j}from"./MyApp-DW5WH4Ub.js";import u from"./useCacheState-CAnxkewm.js";import{getArchivedStoriesContent as f}from"./stories-DQ6hTlzb.js";import{I as p}from"./index-CDSnY0KE.js";import{A as y}from"./index-DdM4T8-Q.js";import{T as g}from"./index-IxNfZAD-.js";import"./EyeOutlined-CNKPohhw.js";import"./addEventListener-C4tIKh7N.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-PbLYNhdQ.js";import"./Dropdown-BcL-JHCf.js";const v=a((()=>n((()=>import("./StoryViewers-B7IqR_CO.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29]),import.meta.url)),{fallback:j}),b=a((()=>n((()=>import("./VideoWithMuted-CYlwayKv.js")),__vite__mapDeps([30,1,2]),import.meta.url)),{fallback:j});function w({story:a}){const{ti:n}=l(),{message:j}=e.useApp(),w=i(t.darkMode),[_,k]=u("ArchivedStoryViewer.stories."+a.creation_time,[]),[T,A]=u("ArchivedStoryViewer.viewingIndex."+a.id,0),[N,R]=s.useState(!1),V=_[T]||_[0],E=V?Object.values(V.viewer_count).reduce(((e,i)=>e+i),0):0,I=V?V.reactions.reduce(((e,i)=>e+i.count),0):0,S=T<_.length-1,L=T>0,D=0===_.length&&!N,O=s.useRef(!1);s.useEffect((()=>{(null==_?void 0:_.length)||!a.creation_time||O.current||(R(!0),O.current=!0,f({creationTime:a.creation_time}).then((e=>{if(e){const i=e.findIndex((e=>e.id===a.id));k(e),A(i)}})).catch((e=>j.error(n({en:"Error: ",vi:"Lỗi: "}))+e.message)).finally((()=>{R(!1),O.current=!1})))}),[]);const C={height:"70vh",width:"calc(70vh * 9 / 16)"},F=s.useRef(null),$=V?[{key:"story",label:r.jsxs(d.Text,{children:[r.jsx("i",{className:"fa fa-play"})," Play"]}),children:r.jsxs(c,{direction:"vertical",style:{position:"relative"},children:[V.video?r.jsx(b,{src:V.video,style:{...C,borderRadius:10,objectFit:"contain"}}):V.image_background?r.jsxs(c,{direction:"vertical",style:{position:"relative"},children:[r.jsx(p,{src:V.image_background,preview:!1,style:{...C,borderRadius:10,objectFit:"contain"}}),r.jsx(p,{src:V.image,style:{...C,borderRadius:10,objectFit:"contain",position:"absolute",bottom:0,left:0,transform:"translate(-50%, 0)",opacity:.5}})]}):r.jsx(p,{src:V.image,style:{...C,borderRadius:10,objectFit:"contain"}}),r.jsx(c,{direction:"vertical",align:"start",style:{background:"linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%)",position:"absolute",top:0,left:0,padding:10,width:"100%",paddingBottom:"50px",borderRadius:10},children:[V.comment_count?{icon:r.jsx("i",{className:"fa-solid fa-message"}),text:V.comment_count}:null,V.music?{icon:r.jsx("i",{className:"fa-solid fa-music"}),text:V.music}:null].filter(Boolean).map(((e,i)=>e&&r.jsxs(d.Text,{style:{color:"#eee",textAlign:"left"},children:[e.icon," ",e.text]},"story-statistic-"+i)))})]})},{key:"viewer",label:r.jsxs(d.Text,{children:[r.jsx("i",{className:"fa fa-eye"})," ",E," "]}),children:r.jsxs(c,{direction:"vertical",style:{...C,width:450,overflowY:"auto",overflowX:"hidden",textAlign:"left",position:"relative"},children:[r.jsxs(d.Text,{children:[n({en:"Views",vi:"Lượt xem"}),":"]}),r.jsx("ul",{children:Object.entries(V.viewer_count).map((([e,i])=>r.jsx("li",{children:r.jsxs(d.Text,{style:{color:"#eee"},children:[e,": ",i]})},"story-viewer-"+e)))}),r.jsx(c,{direction:"horizontal",style:{display:"flex",padding:10,alignItems:"center"},children:r.jsxs(d.Text,{style:{textDecoration:"underline"},children:[n({en:"Created date",vi:"Ngày đăng"}),r.jsx(d.Text,{style:{position:"absolute",right:10},children:x(new Date(V.created_at))})]})}),r.jsx(v,{storyId:V.id,initialViewers:V.viewers})]})},{key:"reactions",label:r.jsxs(d.Text,{children:[r.jsx("i",{className:"fa fa-heart"})," ",I," "]}),children:r.jsxs(c,{direction:"vertical",style:{overflowY:"auto",...C},align:"start",children:[r.jsxs(d.Text,{children:[n({en:"Reactions",vi:"Lượt thích"}),":"]}),V.reactions.map(((e,i)=>r.jsxs(d.Link,{href:h(e.uid),style:{padding:"0 5px 5px",display:"block"},target:"_blank",children:[r.jsx(y,{src:m(e.uid,50),size:35}),r.jsx(d.Text,{style:{color:"#eee",textAlign:"left"},children:` ${e.count} ${e.reactions}`},"story-reaction-"+i)]},"story-reaction-"+i)))]})}]:[];return r.jsxs(c,{direction:"vertical",style:{maxHeight:"100vh",overflowY:"auto",overflowX:"hidden",minWidth:C.width,minHeight:C.height,background:w?"#222":"#eee",color:w?"white":"#111",padding:10,borderRadius:10},ref:F,children:[r.jsxs(d.Text,{children:["Archived Story Viewer"," ",N?r.jsx("i",{className:"fa fa-spinner fa-spin"}):`(${T+1}/${_.length})`]}),D&&r.jsx("div",{children:"No data"}),V&&r.jsxs(c,{children:[r.jsx(o,{disabled:!L,onClick:()=>{A(T-1)},children:r.jsx("i",{className:"fa fa-chevron-left"})}),r.jsx(g,{defaultActiveKey:"story",items:$}),r.jsx(o,{disabled:!S,onClick:()=>{A(T+1)},children:r.jsx("i",{className:"fa fa-chevron-right"})})]})]})}export{w as default};
