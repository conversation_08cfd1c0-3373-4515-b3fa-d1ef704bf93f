import{r as a,b0 as s,b5 as e}from"./index-Cak6rALw.js";import{z as r,S as i}from"./MyApp-DW5WH4Ub.js";import{C as n}from"./index-D7dTP8lW.js";import{I as A}from"./index-CDSnY0KE.js";import"./index-IxNfZAD-.js";import"./Dropdown-BcL-JHCf.js";import"./EyeOutlined-CNKPohhw.js";import"./addEventListener-C4tIKh7N.js";function o({about:o,onClose:l,showUid:t=!1}){const m=(null==o?void 0:o.type)||r.User,f=a.useMemo((()=>{switch(m){case r.User:return{icon:s.jsx("i",{className:"fa-solid fa-user"}),name:"User"};case r.Group:return{icon:s.jsx("i",{className:"fa-solid fa-people-group"}),name:"Group"};case r.Page:return{icon:s.jsx("i",{className:"fa-solid fa-pager"}),name:"Page"};case r.IGUser:return{icon:s.jsx("i",{className:"fa-brands fa-instagram"}),name:"Instagram"};case r.TikTokUser:return{icon:s.jsx("i",{className:"fa-brands fa-tiktok"}),name:"TikTok"};case r.ThreadsUser:return{icon:s.jsx("i",{className:"fa-solid fa-at"}),name:"Threads"};default:return{icon:s.jsx("i",{className:"fa-solid fa-question"}),name:"Unknow"}}}),[m]);return o?s.jsxs(n,{style:{maxWidth:"min(90vw, 500px)",paddingRight:40},actions:[],children:[l&&s.jsx(e,{type:"text",onClick:l,icon:s.jsx("i",{className:"fa fa-times"}),style:{position:"absolute",top:5,right:5}}),s.jsx(n.Meta,{avatar:s.jsx(A,{src:null==o?void 0:o.avatarBig,style:{width:60,height:60,borderRadius:"50%",objectFit:"cover"},alt:null==o?void 0:o.name,fallback:(null==o?void 0:o.type)===r.IGUser?"data:image/png;base64,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":null==o?void 0:o.avatar}),title:s.jsx("a",{href:null==o?void 0:o.url,target:"_blank",children:(null==o?void 0:o.name)||(null==o?void 0:o.username)}),description:t?(null==o?void 0:o.uid)||(null==o?void 0:o.id):s.jsxs(i,{children:[f.icon," ",f.name]})})]}):null}export{o as default};
