import{r as e,n as t,k as r,e as o,d as n,l as i,h as s,m as a,c4 as c,L as l,M as u,O as d,J as p,K as g,ad as m,G as f,a6 as h,ai as y,as as b,c5 as $}from"./index-Cak6rALw.js";import{T as k,aj as v}from"./MyApp-DW5WH4Ub.js";var x={percent:0,prefixCls:"rc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1,gapPosition:"bottom"},C=0,S=r();const w=function(r){var o=e.useState(),n=t(o,2),i=n[0],s=n[1];return e.useEffect((function(){var e;s("rc_progress_".concat((S?(e=C,C+=1):e="TEST_OR_SSR",e)))}),[]),r||i};var E=function(t){var r=t.bg,o=t.children;return e.createElement("div",{style:{width:"100%",height:"100%",background:r}},o)};function j(e,t){return Object.keys(e).map((function(r){var o=parseFloat(r),n="".concat(Math.floor(o*t),"%");return"".concat(e[r]," ").concat(n)}))}var O=e.forwardRef((function(t,r){var n=t.prefixCls,i=t.color,s=t.gradientId,a=t.radius,c=t.style,l=t.ptg,u=t.strokeLinecap,d=t.strokeWidth,p=t.size,g=t.gapDegree,m=i&&"object"===o(i),f=m?"#FFF":void 0,h=p/2,y=e.createElement("circle",{className:"".concat(n,"-circle-path"),r:a,cx:h,cy:h,stroke:f,strokeLinecap:u,strokeWidth:d,opacity:0===l?0:1,style:c,ref:r});if(!m)return y;var b="".concat(s,"-conic"),$=g?"".concat(180+g/2,"deg"):"0deg",k=j(i,(360-g)/360),v=j(i,1),x="conic-gradient(from ".concat($,", ").concat(k.join(", "),")"),C="linear-gradient(to ".concat(g?"bottom":"top",", ").concat(v.join(", "),")");return e.createElement(e.Fragment,null,e.createElement("mask",{id:b},y),e.createElement("foreignObject",{x:0,y:0,width:p,height:p,mask:"url(#".concat(b,")")},e.createElement(E,{bg:C},e.createElement(E,{bg:x}))))})),I=100,N=function(e,t,r,o,n,i,s,a,c,l){var u=arguments.length>10&&void 0!==arguments[10]?arguments[10]:0,d=r/100*360*((360-i)/360),p=0===i?0:{bottom:0,top:180,left:90,right:-90}[s],g=(100-o)/100*t;"round"===c&&100!==o&&(g+=l/2)>=t&&(g=t-.01);return{stroke:"string"==typeof a?a:void 0,strokeDasharray:"".concat(t,"px ").concat(e),strokeDashoffset:g+u,transform:"rotate(".concat(n+d+p,"deg)"),transformOrigin:"".concat(50,"px ").concat(50,"px"),transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s",fillOpacity:0}},P=["id","prefixCls","steps","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","style","className","strokeColor","percent"];function W(e){var t=null!=e?e:[];return Array.isArray(t)?t:[t]}var A=function(t){var r,c,l,u,d,p=n(n({},x),t),g=p.id,m=p.prefixCls,f=p.steps,h=p.strokeWidth,y=p.trailWidth,b=p.gapDegree,$=void 0===b?0:b,k=p.gapPosition,v=p.trailColor,C=p.strokeLinecap,S=p.style,E=p.className,j=p.strokeColor,A=p.percent,D=i(p,P),z=w(g),M="".concat(z,"-gradient"),R=50-h/2,L=2*Math.PI*R,X=$>0?90+$/2:-90,T=L*((360-$)/360),F="object"===o(f)?f:{count:f,gap:2},B=F.count,_=F.gap,H=W(A),q=W(j),G=q.find((function(e){return e&&"object"===o(e)})),J=G&&"object"===o(G)?"butt":C,K=N(L,T,0,100,X,$,k,v,J,h),Q=(r=e.useRef([]),c=e.useRef(null),e.useEffect((function(){var e=Date.now(),t=!1;r.current.forEach((function(r){if(r){t=!0;var o=r.style;o.transitionDuration=".3s, .3s, .3s, .06s",c.current&&e-c.current<100&&(o.transitionDuration="0s, 0s")}})),t&&(c.current=Date.now())})),r.current);return e.createElement("svg",s({className:a("".concat(m,"-circle"),E),viewBox:"0 0 ".concat(I," ").concat(I),style:S,id:g,role:"presentation"},D),!B&&e.createElement("circle",{className:"".concat(m,"-circle-trail"),r:R,cx:50,cy:50,stroke:v,strokeLinecap:J,strokeWidth:y||h,style:K}),B?(l=Math.round(B*(H[0]/100)),u=100/B,d=0,new Array(B).fill(null).map((function(t,r){var n=r<=l-1?q[0]:v,i=n&&"object"===o(n)?"url(#".concat(M,")"):void 0,s=N(L,T,d,u,X,$,k,n,"butt",h,_);return d+=100*(T-s.strokeDashoffset+_)/T,e.createElement("circle",{key:r,className:"".concat(m,"-circle-path"),r:R,cx:50,cy:50,stroke:i,strokeWidth:h,opacity:1,style:s,ref:function(e){Q[r]=e}})}))):function(){var t=0;return H.map((function(r,o){var n=q[o]||q[q.length-1],i=N(L,T,t,r,X,$,k,n,J,h);return t+=r,e.createElement(O,{key:o,color:n,ptg:r,radius:R,prefixCls:m,gradientId:M,style:i,strokeLinecap:J,strokeWidth:h,gapDegree:$,ref:function(e){Q[o]=e},size:I})})).reverse()}())};function D(e){return!e||e<0?0:e>100?100:e}function z({success:e,successPercent:t}){let r=t;return e&&"progress"in e&&(r=e.progress),e&&"percent"in e&&(r=e.percent),r}const M=(e,t,r)=>{var o,n,i,s;let a=-1,c=-1;if("step"===t){const t=r.steps,o=r.strokeWidth;"string"==typeof e||void 0===e?(a="small"===e?2:14,c=null!=o?o:8):"number"==typeof e?[a,c]=[e,e]:[a=14,c=8]=Array.isArray(e)?e:[e.width,e.height],a*=t}else if("line"===t){const t=null==r?void 0:r.strokeWidth;"string"==typeof e||void 0===e?c=t||("small"===e?6:8):"number"==typeof e?[a,c]=[e,e]:[a=-1,c=8]=Array.isArray(e)?e:[e.width,e.height]}else"circle"!==t&&"dashboard"!==t||("string"==typeof e||void 0===e?[a,c]="small"===e?[60,60]:[120,120]:"number"==typeof e?[a,c]=[e,e]:Array.isArray(e)&&(a=null!==(n=null!==(o=e[0])&&void 0!==o?o:e[1])&&void 0!==n?n:120,c=null!==(s=null!==(i=e[0])&&void 0!==i?i:e[1])&&void 0!==s?s:120));return[a,c]},R=t=>{const{prefixCls:r,trailColor:o=null,strokeLinecap:n="round",gapPosition:i,gapDegree:s,width:l=120,type:u,children:d,success:p,size:g=l,steps:m}=t,[f,h]=M(g,"circle");let{strokeWidth:y}=t;void 0===y&&(y=Math.max((e=>3/e*100)(f),6));const b={width:f,height:h,fontSize:.15*f+6},$=e.useMemo((()=>s||0===s?s:"dashboard"===u?75:void 0),[s,u]),v=(({percent:e,success:t,successPercent:r})=>{const o=D(z({success:t,successPercent:r}));return[o,D(D(e)-o)]})(t),x=i||"dashboard"===u&&"bottom"||void 0,C="[object Object]"===Object.prototype.toString.call(t.strokeColor),S=(({success:e={},strokeColor:t})=>{const{strokeColor:r}=e;return[r||c.green,t||null]})({success:p,strokeColor:t.strokeColor}),w=a(`${r}-inner`,{[`${r}-circle-gradient`]:C}),E=e.createElement(A,{steps:m,percent:m?v[1]:v,strokeWidth:y,trailWidth:y,strokeColor:m?S[1]:S,strokeLinecap:n,trailColor:o,prefixCls:r,gapDegree:$,gapPosition:x}),j=f<=20,O=e.createElement("div",{className:w,style:b},E,!j&&d);return j?e.createElement(k,{title:d},O):O},L="--progress-line-stroke-color",X="--progress-percent",T=e=>{const t=e?"100%":"-100%";return new g(`antProgress${e?"RTL":"LTR"}Active`,{"0%":{transform:`translateX(${t}) scaleX(0)`,opacity:.1},"20%":{transform:`translateX(${t}) scaleX(0)`,opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}})},F=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:Object.assign(Object.assign({},d(e)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:e.fontSize},[`${t}-outer`]:{display:"inline-flex",alignItems:"center",width:"100%"},[`${t}-inner`]:{position:"relative",display:"inline-block",width:"100%",flex:1,overflow:"hidden",verticalAlign:"middle",backgroundColor:e.remainingColor,borderRadius:e.lineBorderRadius},[`${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.defaultColor}},[`${t}-success-bg, ${t}-bg`]:{position:"relative",background:e.defaultColor,borderRadius:e.lineBorderRadius,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`},[`${t}-layout-bottom`]:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",[`${t}-text`]:{width:"max-content",marginInlineStart:0,marginTop:e.marginXXS}},[`${t}-bg`]:{overflow:"hidden","&::after":{content:'""',background:{_multi_value_:!0,value:["inherit",`var(${L})`]},height:"100%",width:`calc(1 / var(${X}) * 100%)`,display:"block"},[`&${t}-bg-inner`]:{minWidth:"max-content","&::after":{content:"none"},[`${t}-text-inner`]:{color:e.colorWhite,[`&${t}-text-bright`]:{color:"rgba(0, 0, 0, 0.45)"}}}},[`${t}-success-bg`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:e.colorSuccess},[`${t}-text`]:{display:"inline-block",marginInlineStart:e.marginXS,color:e.colorText,lineHeight:1,width:"2em",whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[r]:{fontSize:e.fontSize},[`&${t}-text-outer`]:{width:"max-content"},[`&${t}-text-outer${t}-text-start`]:{width:"max-content",marginInlineStart:0,marginInlineEnd:e.marginXS}},[`${t}-text-inner`]:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",marginInlineStart:0,padding:`0 ${p(e.paddingXXS)}`,[`&${t}-text-start`]:{justifyContent:"start"},[`&${t}-text-end`]:{justifyContent:"end"}},[`&${t}-status-active`]:{[`${t}-bg::before`]:{position:"absolute",inset:0,backgroundColor:e.colorBgContainer,borderRadius:e.lineBorderRadius,opacity:0,animationName:T(),animationDuration:e.progressActiveMotionDuration,animationTimingFunction:e.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},[`&${t}-rtl${t}-status-active`]:{[`${t}-bg::before`]:{animationName:T(!0)}},[`&${t}-status-exception`]:{[`${t}-bg`]:{backgroundColor:e.colorError},[`${t}-text`]:{color:e.colorError}},[`&${t}-status-exception ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorError}},[`&${t}-status-success`]:{[`${t}-bg`]:{backgroundColor:e.colorSuccess},[`${t}-text`]:{color:e.colorSuccess}},[`&${t}-status-success ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorSuccess}}})}},B=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-circle-trail`]:{stroke:e.remainingColor},[`&${t}-circle ${t}-inner`]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},[`&${t}-circle ${t}-text`]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:e.circleTextColor,fontSize:e.circleTextFontSize,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[r]:{fontSize:e.circleIconFontSize}},[`${t}-circle&-status-exception`]:{[`${t}-text`]:{color:e.colorError}},[`${t}-circle&-status-success`]:{[`${t}-text`]:{color:e.colorSuccess}}},[`${t}-inline-circle`]:{lineHeight:1,[`${t}-inner`]:{verticalAlign:"bottom"}}}},_=e=>{const{componentCls:t}=e;return{[t]:{[`${t}-steps`]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:e.progressStepMinWidth,marginInlineEnd:e.progressStepMarginInlineEnd,backgroundColor:e.remainingColor,transition:`all ${e.motionDurationSlow}`,"&-active":{backgroundColor:e.defaultColor}}}}}},H=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-small&-line, ${t}-small&-line ${t}-text ${r}`]:{fontSize:e.fontSizeSM}}}},q=l("Progress",(e=>{const t=e.calc(e.marginXXS).div(2).equal(),r=u(e,{progressStepMarginInlineEnd:t,progressStepMinWidth:t,progressActiveMotionDuration:"2.4s"});return[F(r),B(r),_(r),H(r)]}),(e=>({circleTextColor:e.colorText,defaultColor:e.colorInfo,remainingColor:e.colorFillSecondary,lineBorderRadius:100,circleTextFontSize:"1em",circleIconFontSize:e.fontSize/e.fontSizeSM+"em"})));const G=(e,t)=>{const{from:r=c.blue,to:o=c.blue,direction:n=("rtl"===t?"to left":"to right")}=e,i=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]])}return r}(e,["from","to","direction"]);if(0!==Object.keys(i).length){const e=`linear-gradient(${n}, ${(e=>{let t=[];return Object.keys(e).forEach((r=>{const o=parseFloat(r.replace(/%/g,""));Number.isNaN(o)||t.push({key:o,value:e[r]})})),t=t.sort(((e,t)=>e.key-t.key)),t.map((({key:e,value:t})=>`${t} ${e}%`)).join(", ")})(i)})`;return{background:e,[L]:e}}const s=`linear-gradient(${n}, ${r}, ${o})`;return{background:s,[L]:s}},J=t=>{const{prefixCls:r,direction:o,percent:n,size:i,strokeWidth:s,strokeColor:c,strokeLinecap:l="round",children:u,trailColor:d=null,percentPosition:p,success:g}=t,{align:m,type:f}=p,h=c&&"string"!=typeof c?G(c,o):{[L]:c,background:c},y="square"===l||"butt"===l?0:void 0,b=null!=i?i:[-1,s||("small"===i?6:8)],[$,k]=M(b,"line",{strokeWidth:s}),v={backgroundColor:d||void 0,borderRadius:y},x=Object.assign(Object.assign({width:`${D(n)}%`,height:k,borderRadius:y},h),{[X]:D(n)/100}),C=z(t),S={width:`${D(C)}%`,height:k,borderRadius:y,backgroundColor:null==g?void 0:g.strokeColor},w={width:$<0?"100%":$},E=e.createElement("div",{className:`${r}-inner`,style:v},e.createElement("div",{className:a(`${r}-bg`,`${r}-bg-${f}`),style:x},"inner"===f&&u),void 0!==C&&e.createElement("div",{className:`${r}-success-bg`,style:S})),j="outer"===f&&"start"===m,O="outer"===f&&"end"===m;return"outer"===f&&"center"===m?e.createElement("div",{className:`${r}-layout-bottom`},E,u):e.createElement("div",{className:`${r}-outer`,style:w},j&&u,E,O&&u)},K=t=>{const{size:r,steps:o,rounding:n=Math.round,percent:i=0,strokeWidth:s=8,strokeColor:c,trailColor:l=null,prefixCls:u,children:d}=t,p=n(o*(i/100)),g=null!=r?r:["small"===r?2:14,s],[m,f]=M(g,"step",{steps:o,strokeWidth:s}),h=m/o,y=Array.from({length:o});for(let b=0;b<o;b++){const t=Array.isArray(c)?c[b]:c;y[b]=e.createElement("div",{key:b,className:a(`${u}-steps-item`,{[`${u}-steps-item-active`]:b<=p-1}),style:{backgroundColor:b<=p-1?t:l,width:h,height:f}})}return e.createElement("div",{className:`${u}-steps-outer`},y,d)};const Q=["normal","exception","active","success"],Y=e.forwardRef(((t,r)=>{const{prefixCls:o,className:n,rootClassName:i,steps:s,strokeColor:c,percent:l=0,size:u="default",showInfo:d=!0,type:p="line",status:g,format:k,style:x,percentPosition:C={}}=t,S=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]])}return r}(t,["prefixCls","className","rootClassName","steps","strokeColor","percent","size","showInfo","type","status","format","style","percentPosition"]),{align:w="end",type:E="outer"}=C,j=Array.isArray(c)?c[0]:c,O="string"==typeof c||Array.isArray(c)?c:void 0,I=e.useMemo((()=>{if(j){const e="string"==typeof j?j:Object.values(j)[0];return new m(e).isLight()}return!1}),[c]),N=e.useMemo((()=>{var e,r;const o=z(t);return parseInt(void 0!==o?null===(e=null!=o?o:0)||void 0===e?void 0:e.toString():null===(r=null!=l?l:0)||void 0===r?void 0:r.toString(),10)}),[l,t.success,t.successPercent]),P=e.useMemo((()=>!Q.includes(g)&&N>=100?"success":g||"normal"),[g,N]),{getPrefixCls:W,direction:A,progress:L}=e.useContext(f),X=W("progress",o),[T,F,B]=q(X),_="line"===p,H=_&&!s,G=e.useMemo((()=>{if(!d)return null;const r=z(t);let o;const n=_&&I&&"inner"===E;return"inner"===E||k||"exception"!==P&&"success"!==P?o=(k||(e=>`${e}%`))(D(l),D(r)):"exception"===P?o=_?e.createElement(y,null):e.createElement(b,null):"success"===P&&(o=_?e.createElement($,null):e.createElement(v,null)),e.createElement("span",{className:a(`${X}-text`,{[`${X}-text-bright`]:n,[`${X}-text-${w}`]:H,[`${X}-text-${E}`]:H}),title:"string"==typeof o?o:void 0},o)}),[d,l,N,P,p,X,k]);let Y;"line"===p?Y=s?e.createElement(K,Object.assign({},t,{strokeColor:O,prefixCls:X,steps:"object"==typeof s?s.count:s}),G):e.createElement(J,Object.assign({},t,{strokeColor:j,prefixCls:X,direction:A,percentPosition:{align:w,type:E}}),G):"circle"!==p&&"dashboard"!==p||(Y=e.createElement(R,Object.assign({},t,{strokeColor:j,prefixCls:X,progressStatus:P}),G));const U=a(X,`${X}-status-${P}`,{[`${X}-${"dashboard"===p?"circle":p}`]:"line"!==p,[`${X}-inline-circle`]:"circle"===p&&M(u,"circle")[0]<=20,[`${X}-line`]:H,[`${X}-line-align-${w}`]:H,[`${X}-line-position-${E}`]:H,[`${X}-steps`]:s,[`${X}-show-info`]:d,[`${X}-${u}`]:"string"==typeof u,[`${X}-rtl`]:"rtl"===A},null==L?void 0:L.className,n,i,F,B);return T(e.createElement("div",Object.assign({ref:r,style:Object.assign(Object.assign({},null==L?void 0:L.style),x),className:U,role:"progressbar","aria-valuenow":N,"aria-valuemin":0,"aria-valuemax":100},h(S,["trailColor","strokeWidth","width","gapDegree","gapPosition","strokeLinecap","success","successPercent"])),Y))}));export{Y as P};
