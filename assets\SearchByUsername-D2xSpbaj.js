import{b0 as e,b5 as n}from"./index-Cak6rALw.js";import{u as s,S as t,A as o,t as r}from"./MyApp-DW5WH4Ub.js";function i(){const{ti:i}=s();return e.jsxs(t,{direction:"vertical",style:{width:"100%",alignItems:"center"},children:[e.jsx(o,{message:i({en:"Find accounts by username on multiple platforms",vi:"Tìm tài khoản bằng username trên nhiều nền tảng"}),type:"info",showIcon:!0}),e.jsxs(n,{type:"default",href:"https://www.facebook.com/groups/fbaio/posts/****************",target:"_blank",onClick:()=>r("SearchByUsername:NeedMore"),icon:e.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"}),iconPosition:"end",children:["🧐 ",i({en:"Need more?",vi:"Cần nhiều hơn?"})]}),e.jsx("iframe",{src:"https://instantusername.com/",style:{maxWidth:"100%",width:600,height:600,border:"none",borderRadius:10,backgroundColor:"#fff"}})]})}export{i as default};
