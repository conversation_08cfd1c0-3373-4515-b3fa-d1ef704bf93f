import{r as e,m as n,$ as o,e as t,n as r,a3 as i,d as l,l as a,bs as c,v as s,aj as u,a4 as d,h as p,a5 as m,bt as f,i as v,u as g,q as h,T as b,a1 as C,a6 as S,k as w,t as $,G as I,O as y,aa as E,M as x,ae as O,J as R,L as N,ah as M,ai as D,as as P,aK as H,R as B,Q as T,av as z,af as j,au as k,aw as W,at as A,X as L,Y as F}from"./index-Cak6rALw.js";import{ai as V,U as _,ad as G,J as K,D as q,E as X,F as U,G as Y,aj as Q,a5 as J,a6 as Z,a7 as ee}from"./MyApp-DW5WH4Ub.js";import{L as ne}from"./List-B6ZMXgC_.js";import{g as oe}from"./PurePanel-BvHjNOhQ.js";import{E as te}from"./index-SRy1SMeO.js";import{i as re}from"./move-ESLFEEsv.js";import{R as ie}from"./DownOutlined-B-JcS-29.js";import{R as le}from"./SearchOutlined--mXbzQ68.js";var ae=function(o){var t=o.className,r=o.customizeIcon,i=o.customizeIconProps,l=o.children,a=o.onMouseDown,c=o.onClick,s="function"==typeof r?r(i):r;return e.createElement("span",{className:t,onMouseDown:function(e){e.preventDefault(),null==a||a(e)},style:{userSelect:"none",WebkitUserSelect:"none"},unselectable:"on",onClick:c,"aria-hidden":!0},void 0!==s?s:e.createElement("span",{className:n(t.split(/\s+/).map((function(e){return"".concat(e,"-icon")})))},l))},ce=e.createContext(null);function se(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:250,o=e.useRef(null),t=e.useRef(null);return e.useEffect((function(){return function(){window.clearTimeout(t.current)}}),[]),[function(){return o.current},function(e){(e||null===o.current)&&(o.current=e),window.clearTimeout(t.current),t.current=window.setTimeout((function(){o.current=null}),n)}]}var ue=["prefixCls","id","inputElement","autoFocus","autoComplete","editable","activeDescendantId","value","open","attrs"],de=function(o,t){var r=o.prefixCls,i=o.id,u=o.inputElement,d=o.autoFocus,p=o.autoComplete,m=o.editable,f=o.activeDescendantId,v=o.value,g=o.open,h=o.attrs,b=a(o,ue),C=u||e.createElement("input",null),S=C,w=S.ref,$=S.props;return c(!("maxLength"in C.props)),C=e.cloneElement(C,l(l(l({type:"search"},function(e,n){var o=l(l({},e),n);return Object.keys(n).forEach((function(t){var r=n[t];"function"==typeof r&&(o[t]=function(){for(var n,o=arguments.length,i=new Array(o),l=0;l<o;l++)i[l]=arguments[l];return r.apply(void 0,i),null===(n=e[t])||void 0===n?void 0:n.call.apply(n,[e].concat(i))})})),o}(b,$)),{},{id:i,ref:s(t,w),autoComplete:p||"off",autoFocus:d,className:n("".concat(r,"-selection-search-input"),null==$?void 0:$.className),role:"combobox","aria-expanded":g||!1,"aria-haspopup":"listbox","aria-owns":"".concat(i,"_list"),"aria-autocomplete":"list","aria-controls":"".concat(i,"_list"),"aria-activedescendant":g?f:void 0},h),{},{value:m?v:"",readOnly:!m,unselectable:m?null:"on",style:l(l({},$.style),{},{opacity:m?null:0})})),C},pe=e.forwardRef(de);function me(e){return Array.isArray(e)?e:void 0!==e?[e]:[]}var fe="undefined"!=typeof window&&window.document&&window.document.documentElement;function ve(e){return["string","number"].includes(t(e))}function ge(e){var n=void 0;return e&&(ve(e.title)?n=e.title.toString():ve(e.label)&&(n=e.label.toString())),n}function he(e){var n;return null!==(n=e.key)&&void 0!==n?n:e.value}var be=function(e){e.preventDefault(),e.stopPropagation()},Ce=function(o){var t,i,l=o.id,a=o.prefixCls,c=o.values,s=o.open,p=o.searchValue,m=o.autoClearSearchValue,f=o.inputRef,v=o.placeholder,g=o.disabled,h=o.mode,b=o.showSearch,C=o.autoFocus,S=o.autoComplete,w=o.activeDescendantId,$=o.tabIndex,I=o.removeIcon,y=o.maxTagCount,E=o.maxTagTextLength,x=o.maxTagPlaceholder,O=void 0===x?function(e){return"+ ".concat(e.length," ...")}:x,R=o.tagRender,N=o.onToggleOpen,M=o.onRemove,D=o.onInputChange,P=o.onInputPaste,H=o.onInputKeyDown,B=o.onInputMouseDown,T=o.onInputCompositionStart,z=o.onInputCompositionEnd,j=o.onInputBlur,k=e.useRef(null),W=e.useState(0),A=r(W,2),L=A[0],F=A[1],_=e.useState(!1),G=r(_,2),K=G[0],q=G[1],X="".concat(a,"-selection"),U=s||"multiple"===h&&!1===m||"tags"===h?p:"",Y="tags"===h||"multiple"===h&&!1===m||b&&(s||K);t=function(){F(k.current.scrollWidth)},i=[U],fe?e.useLayoutEffect(t,i):e.useEffect(t,i);var Q=function(o,t,r,i,l){return e.createElement("span",{title:ge(o),className:n("".concat(X,"-item"),d({},"".concat(X,"-item-disabled"),r))},e.createElement("span",{className:"".concat(X,"-item-content")},t),i&&e.createElement(ae,{className:"".concat(X,"-item-remove"),onMouseDown:be,onClick:l,customizeIcon:I},"×"))},J=function(n,o,t,r,i,l){return e.createElement("span",{onMouseDown:function(e){be(e),N(!s)}},R({label:o,value:n,disabled:t,closable:r,onClose:i,isMaxTag:!!l}))},Z=e.createElement("div",{className:"".concat(X,"-search"),style:{width:L},onFocus:function(){q(!0)},onBlur:function(){q(!1)}},e.createElement(pe,{ref:f,open:s,prefixCls:a,id:l,inputElement:null,disabled:g,autoFocus:C,autoComplete:S,editable:Y,activeDescendantId:w,value:U,onKeyDown:H,onMouseDown:B,onChange:D,onPaste:P,onCompositionStart:T,onCompositionEnd:z,onBlur:j,tabIndex:$,attrs:u(o,!0)}),e.createElement("span",{ref:k,className:"".concat(X,"-search-mirror"),"aria-hidden":!0},U," ")),ee=e.createElement(V,{prefixCls:"".concat(X,"-overflow"),data:c,renderItem:function(e){var n=e.disabled,o=e.label,t=e.value,r=!g&&!n,i=o;if("number"==typeof E&&("string"==typeof o||"number"==typeof o)){var l=String(i);l.length>E&&(i="".concat(l.slice(0,E),"..."))}var a=function(n){n&&n.stopPropagation(),M(e)};return"function"==typeof R?J(t,i,n,r,a):Q(e,i,n,r,a)},renderRest:function(e){if(!c.length)return null;var n="function"==typeof O?O(e):O;return"function"==typeof R?J(void 0,n,!1,!1,void 0,!0):Q({title:n},n,!1)},suffix:Z,itemKey:he,maxCount:y});return e.createElement("span",{className:"".concat(X,"-wrap")},ee,!c.length&&!U&&e.createElement("span",{className:"".concat(X,"-placeholder")},v))},Se=function(n){var o=n.inputElement,t=n.prefixCls,i=n.id,l=n.inputRef,a=n.disabled,c=n.autoFocus,s=n.autoComplete,d=n.activeDescendantId,p=n.mode,m=n.open,f=n.values,v=n.placeholder,g=n.tabIndex,h=n.showSearch,b=n.searchValue,C=n.activeValue,S=n.maxLength,w=n.onInputKeyDown,$=n.onInputMouseDown,I=n.onInputChange,y=n.onInputPaste,E=n.onInputCompositionStart,x=n.onInputCompositionEnd,O=n.onInputBlur,R=n.title,N=e.useState(!1),M=r(N,2),D=M[0],P=M[1],H="combobox"===p,B=H||h,T=f[0],z=b||"";H&&C&&!D&&(z=C),e.useEffect((function(){H&&P(!1)}),[H,C]);var j=!("combobox"!==p&&!m&&!h)&&!!z,k=void 0===R?ge(T):R,W=e.useMemo((function(){return T?null:e.createElement("span",{className:"".concat(t,"-selection-placeholder"),style:j?{visibility:"hidden"}:void 0},v)}),[T,j,v,t]);return e.createElement("span",{className:"".concat(t,"-selection-wrap")},e.createElement("span",{className:"".concat(t,"-selection-search")},e.createElement(pe,{ref:l,prefixCls:t,id:i,open:m,inputElement:o,disabled:a,autoFocus:c,autoComplete:s,editable:B,activeDescendantId:d,value:z,onKeyDown:w,onMouseDown:$,onChange:function(e){P(!0),I(e)},onPaste:y,onCompositionStart:E,onCompositionEnd:x,onBlur:O,tabIndex:g,attrs:u(n,!0),maxLength:H?S:void 0})),!H&&T?e.createElement("span",{className:"".concat(t,"-selection-item"),title:k,style:j?{visibility:"hidden"}:void 0},T.label):null,W)},we=function(n,o){var t=e.useRef(null),l=e.useRef(!1),a=n.prefixCls,c=n.open,s=n.mode,u=n.showSearch,d=n.tokenWithEnter,m=n.disabled,f=n.prefix,v=n.autoClearSearchValue,g=n.onSearch,h=n.onSearchSubmit,b=n.onToggleOpen,C=n.onInputKeyDown,S=n.onInputBlur,w=n.domRef;e.useImperativeHandle(o,(function(){return{focus:function(e){t.current.focus(e)},blur:function(){t.current.blur()}}}));var $=se(0),I=r($,2),y=I[0],E=I[1],x=e.useRef(null),O=function(e){!1!==g(e,!0,l.current)&&b(!0)},R={inputRef:t,onInputKeyDown:function(e){var n,o=e.which,r=t.current instanceof HTMLTextAreaElement;(r||!c||o!==i.UP&&o!==i.DOWN||e.preventDefault(),C&&C(e),o!==i.ENTER||"tags"!==s||l.current||c||null==h||h(e.target.value),r&&!c&&~[i.UP,i.DOWN,i.LEFT,i.RIGHT].indexOf(o))||(n=o)&&![i.ESC,i.SHIFT,i.BACKSPACE,i.TAB,i.WIN_KEY,i.ALT,i.META,i.WIN_KEY_RIGHT,i.CTRL,i.SEMICOLON,i.EQUALS,i.CAPS_LOCK,i.CONTEXT_MENU,i.F1,i.F2,i.F3,i.F4,i.F5,i.F6,i.F7,i.F8,i.F9,i.F10,i.F11,i.F12].includes(n)&&b(!0)},onInputMouseDown:function(){E(!0)},onInputChange:function(e){var n=e.target.value;if(d&&x.current&&/[\r\n]/.test(x.current)){var o=x.current.replace(/[\r\n]+$/,"").replace(/\r\n/g," ").replace(/[\r\n]/g," ");n=n.replace(o,x.current)}x.current=null,O(n)},onInputPaste:function(e){var n=e.clipboardData,o=null==n?void 0:n.getData("text");x.current=o||""},onInputCompositionStart:function(){l.current=!0},onInputCompositionEnd:function(e){l.current=!1,"combobox"!==s&&O(e.target.value)},onInputBlur:S},N="multiple"===s||"tags"===s?e.createElement(Ce,p({},n,R)):e.createElement(Se,p({},n,R));return e.createElement("div",{ref:w,className:"".concat(a,"-selector"),onClick:function(e){e.target!==t.current&&(void 0!==document.body.style.msTouchAction?setTimeout((function(){t.current.focus()})):t.current.focus())},onMouseDown:function(e){var n=y();e.target===t.current||n||"combobox"===s&&m||e.preventDefault(),("combobox"===s||u&&n)&&c||(c&&!1!==v&&g("",!0,!1),b())}},f&&e.createElement("div",{className:"".concat(a,"-prefix")},f),N)},$e=e.forwardRef(we),Ie=["prefixCls","disabled","visible","children","popupElement","animation","transitionName","dropdownStyle","dropdownClassName","direction","placement","builtinPlacements","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","getPopupContainer","empty","getTriggerDOMNode","onPopupVisibleChange","onPopupMouseEnter"],ye=function(o,t){var r=o.prefixCls;o.disabled;var i=o.visible,c=o.children,s=o.popupElement,u=o.animation,m=o.transitionName,f=o.dropdownStyle,v=o.dropdownClassName,g=o.direction,h=void 0===g?"ltr":g,b=o.placement,C=o.builtinPlacements,S=o.dropdownMatchSelectWidth,w=o.dropdownRender,$=o.dropdownAlign,I=o.getPopupContainer,y=o.empty,E=o.getTriggerDOMNode,x=o.onPopupVisibleChange,O=o.onPopupMouseEnter,R=a(o,Ie),N="".concat(r,"-dropdown"),M=s;w&&(M=w(s));var D=e.useMemo((function(){return C||function(e){var n=!0===e?0:1;return{bottomLeft:{points:["tl","bl"],offset:[0,4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},bottomRight:{points:["tr","br"],offset:[0,4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},topLeft:{points:["bl","tl"],offset:[0,-4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},topRight:{points:["br","tr"],offset:[0,-4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"}}}(S)}),[C,S]),P=u?"".concat(N,"-").concat(u):m,H="number"==typeof S,B=e.useMemo((function(){return H?null:!1===S?"minWidth":"width"}),[S,H]),T=f;H&&(T=l(l({},T),{},{width:S}));var z=e.useRef(null);return e.useImperativeHandle(t,(function(){return{getPopupElement:function(){var e;return null===(e=z.current)||void 0===e?void 0:e.popupElement}}})),e.createElement(_,p({},R,{showAction:x?["click"]:[],hideAction:x?["click"]:[],popupPlacement:b||("rtl"===h?"bottomRight":"bottomLeft"),builtinPlacements:D,prefixCls:N,popupTransitionName:P,popup:e.createElement("div",{onMouseEnter:O},M),ref:z,stretch:B,popupAlign:$,popupVisible:i,getPopupContainer:I,popupClassName:n(v,d({},"".concat(N,"-empty"),y)),popupStyle:T,getTriggerDOMNode:E,onPopupVisibleChange:x}),c)},Ee=e.forwardRef(ye);function xe(e,n){var o,t=e.key;return"value"in e&&(o=e.value),null!=t?t:void 0!==o?o:"rc-index-key-".concat(n)}function Oe(e){return void 0!==e&&!Number.isNaN(e)}function Re(e,n){var o=e||{},t=o.label||(n?"children":"label");return{label:t,value:o.value||"value",options:o.options||"options",groupLabel:o.groupLabel||t}}function Ne(e){var n=l({},e);return"props"in n||Object.defineProperty(n,"props",{get:function(){return m(!1,"Return type is option instead of Option instance. Please read value directly instead of reading from `props`."),n}}),n}var Me=e.createContext(null);function De(n){var o=n.visible,r=n.values;if(!o)return null;return e.createElement("span",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"".concat(r.slice(0,50).map((function(e){var n=e.label,o=e.value;return["number","string"].includes(t(n))?n:o})).join(", ")),r.length>50?", ...":null)}var Pe=["id","prefixCls","className","showSearch","tagRender","direction","omitDomProps","displayValues","onDisplayValuesChange","emptyOptions","notFoundContent","onClear","mode","disabled","loading","getInputElement","getRawInputElement","open","defaultOpen","onDropdownVisibleChange","activeValue","onActiveValueChange","activeDescendantId","searchValue","autoClearSearchValue","onSearch","onSearchSplit","tokenSeparators","allowClear","prefix","suffixIcon","clearIcon","OptionList","animation","transitionName","dropdownStyle","dropdownClassName","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","placement","builtinPlacements","getPopupContainer","showAction","onFocus","onBlur","onKeyUp","onKeyDown","onMouseDown"],He=["value","onChange","removeIcon","placeholder","autoFocus","maxTagCount","maxTagTextLength","maxTagPlaceholder","choiceTransitionName","onInputKeyDown","onPopupScroll","tabIndex"],Be=function(e){return"tags"===e||"multiple"===e},Te=e.forwardRef((function(i,c){var s,u=i.id,m=i.prefixCls,C=i.className,S=i.showSearch,w=i.tagRender,$=i.direction,I=i.omitDomProps,y=i.displayValues,E=i.onDisplayValuesChange,x=i.emptyOptions,O=i.notFoundContent,R=void 0===O?"Not Found":O,N=i.onClear,M=i.mode,D=i.disabled,P=i.loading,H=i.getInputElement,B=i.getRawInputElement,T=i.open,z=i.defaultOpen,j=i.onDropdownVisibleChange,k=i.activeValue,W=i.onActiveValueChange,A=i.activeDescendantId,L=i.searchValue,F=i.autoClearSearchValue,V=i.onSearch,_=i.onSearchSplit,K=i.tokenSeparators,q=i.allowClear,X=i.prefix,U=i.suffixIcon,Y=i.clearIcon,Q=i.OptionList,J=i.animation,Z=i.transitionName,ee=i.dropdownStyle,ne=i.dropdownClassName,oe=i.dropdownMatchSelectWidth,te=i.dropdownRender,re=i.dropdownAlign,ie=i.placement,le=i.builtinPlacements,ue=i.getPopupContainer,de=i.showAction,pe=void 0===de?[]:de,me=i.onFocus,fe=i.onBlur,ve=i.onKeyUp,ge=i.onKeyDown,he=i.onMouseDown,be=a(i,Pe),Ce=Be(M),Se=(void 0!==S?S:Ce)||"combobox"===M,we=l({},be);He.forEach((function(e){delete we[e]})),null==I||I.forEach((function(e){delete we[e]}));var Ie=e.useState(!1),ye=r(Ie,2),xe=ye[0],Re=ye[1];e.useEffect((function(){Re(G())}),[]);var Ne=e.useRef(null),Te=e.useRef(null),ze=e.useRef(null),je=e.useRef(null),ke=e.useRef(null),We=e.useRef(!1),Ae=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,o=e.useState(!1),t=r(o,2),i=t[0],l=t[1],a=e.useRef(null),c=function(){window.clearTimeout(a.current)};return e.useEffect((function(){return c}),[]),[i,function(e,o){c(),a.current=window.setTimeout((function(){l(e),o&&o()}),n)},c]}(),Le=r(Ae,3),Fe=Le[0],Ve=Le[1],_e=Le[2];e.useImperativeHandle(c,(function(){var e,n;return{focus:null===(e=je.current)||void 0===e?void 0:e.focus,blur:null===(n=je.current)||void 0===n?void 0:n.blur,scrollTo:function(e){var n;return null===(n=ke.current)||void 0===n?void 0:n.scrollTo(e)},nativeElement:Ne.current||Te.current}}));var Ge=e.useMemo((function(){var e;if("combobox"!==M)return L;var n=null===(e=y[0])||void 0===e?void 0:e.value;return"string"==typeof n||"number"==typeof n?String(n):""}),[L,M,y]),Ke="combobox"===M&&"function"==typeof H&&H()||null,qe="function"==typeof B&&B(),Xe=g(Te,null==qe||null===(s=qe.props)||void 0===s?void 0:s.ref),Ue=e.useState(!1),Ye=r(Ue,2),Qe=Ye[0],Je=Ye[1];h((function(){Je(!0)}),[]);var Ze=b(!1,{defaultValue:z,value:T}),en=r(Ze,2),nn=en[0],on=en[1],tn=!!Qe&&nn,rn=!R&&x;(D||rn&&tn&&"combobox"===M)&&(tn=!1);var ln=!rn&&tn,an=e.useCallback((function(e){var n=void 0!==e?e:!tn;D||(on(n),tn!==n&&(null==j||j(n)))}),[D,tn,on,j]),cn=e.useMemo((function(){return(K||[]).some((function(e){return["\n","\r\n"].includes(e)}))}),[K]),sn=e.useContext(Me)||{},un=sn.maxCount,dn=sn.rawValues,pn=function(e,n,o){if(!(Ce&&Oe(un)&&(null==dn?void 0:dn.size)>=un)){var t=!0,r=e;null==W||W(null);var i=function(e,n,o){if(!n||!n.length)return null;var t=!1,r=function e(n,o){var r=f(o),i=r[0],l=r.slice(1);if(!i)return[n];var a=n.split(i);return t=t||a.length>1,a.reduce((function(n,o){return[].concat(v(n),v(e(o,l)))}),[]).filter(Boolean)}(e,n);return t?void 0!==o?r.slice(0,o):r:null}(e,K,Oe(un)?un-dn.size:void 0),l=o?null:i;return"combobox"!==M&&l&&(r="",null==_||_(l),an(!1),t=!1),V&&Ge!==r&&V(r,{source:n?"typing":"effect"}),t}};e.useEffect((function(){tn||Ce||"combobox"===M||pn("",!1,!1)}),[tn]),e.useEffect((function(){nn&&D&&on(!1),D&&!We.current&&Ve(!1)}),[D]);var mn=se(),fn=r(mn,2),vn=fn[0],gn=fn[1],hn=e.useRef(!1),bn=e.useRef(!1),Cn=[];e.useEffect((function(){return function(){Cn.forEach((function(e){return clearTimeout(e)})),Cn.splice(0,Cn.length)}}),[]);var Sn,wn=e.useState({}),$n=r(wn,2)[1];qe&&(Sn=function(e){an(e)}),function(n,o,t,r){var i=e.useRef(null);i.current={open:o,triggerOpen:t,customizedTrigger:r},e.useEffect((function(){function e(e){var o;if(null===(o=i.current)||void 0===o||!o.customizedTrigger){var t=e.target;t.shadowRoot&&e.composed&&(t=e.composedPath()[0]||t),i.current.open&&n().filter((function(e){return e})).every((function(e){return!e.contains(t)&&e!==t}))&&i.current.triggerOpen(!1)}}return window.addEventListener("mousedown",e),function(){return window.removeEventListener("mousedown",e)}}),[])}((function(){var e;return[Ne.current,null===(e=ze.current)||void 0===e?void 0:e.getPopupElement()]}),ln,an,!!qe);var In,yn=e.useMemo((function(){return l(l({},i),{},{notFoundContent:R,open:tn,triggerOpen:ln,id:u,showSearch:Se,multiple:Ce,toggleOpen:an})}),[i,R,ln,tn,u,Se,Ce,an]),En=!!U||P;En&&(In=e.createElement(ae,{className:n("".concat(m,"-arrow"),d({},"".concat(m,"-arrow-loading"),P)),customizeIcon:U,customizeIconProps:{loading:P,searchValue:Ge,open:tn,focused:Fe,showSearch:Se}}));var xn,On=function(e,n,r,i,l){var a=arguments.length>5&&void 0!==arguments[5]&&arguments[5],c=arguments.length>6?arguments[6]:void 0,s=arguments.length>7?arguments[7]:void 0,u=o.useMemo((function(){return"object"===t(i)?i.clearIcon:l||void 0}),[i,l]);return{allowClear:o.useMemo((function(){return!(a||!i||!r.length&&!c||"combobox"===s&&""===c)}),[i,a,r.length,c,s]),clearIcon:o.createElement(ae,{className:"".concat(e,"-clear"),onMouseDown:n,customizeIcon:u},"×")}}(m,(function(){var e;null==N||N(),null===(e=je.current)||void 0===e||e.focus(),E([],{type:"clear",values:y}),pn("",!1,!1)}),y,q,Y,D,Ge,M),Rn=On.allowClear,Nn=On.clearIcon,Mn=e.createElement(Q,{ref:ke}),Dn=n(m,C,d(d(d(d(d(d(d(d(d(d({},"".concat(m,"-focused"),Fe),"".concat(m,"-multiple"),Ce),"".concat(m,"-single"),!Ce),"".concat(m,"-allow-clear"),q),"".concat(m,"-show-arrow"),En),"".concat(m,"-disabled"),D),"".concat(m,"-loading"),P),"".concat(m,"-open"),tn),"".concat(m,"-customize-input"),Ke),"".concat(m,"-show-search"),Se)),Pn=e.createElement(Ee,{ref:ze,disabled:D,prefixCls:m,visible:ln,popupElement:Mn,animation:J,transitionName:Z,dropdownStyle:ee,dropdownClassName:ne,direction:$,dropdownMatchSelectWidth:oe,dropdownRender:te,dropdownAlign:re,placement:ie,builtinPlacements:le,getPopupContainer:ue,empty:x,getTriggerDOMNode:function(e){return Te.current||e},onPopupVisibleChange:Sn,onPopupMouseEnter:function(){$n({})}},qe?e.cloneElement(qe,{ref:Xe}):e.createElement($e,p({},i,{domRef:Te,prefixCls:m,inputElement:Ke,ref:je,id:u,prefix:X,showSearch:Se,autoClearSearchValue:F,mode:M,activeDescendantId:A,tagRender:w,values:y,open:tn,onToggleOpen:an,activeValue:k,searchValue:Ge,onSearch:pn,onSearchSubmit:function(e){e&&e.trim()&&V(e,{source:"submit"})},onRemove:function(e){var n=y.filter((function(n){return n!==e}));E(n,{type:"remove",values:[e]})},tokenWithEnter:cn,onInputBlur:function(){hn.current=!1}})));return xn=qe?Pn:e.createElement("div",p({className:Dn},we,{ref:Ne,onMouseDown:function(e){var n,o=e.target,t=null===(n=ze.current)||void 0===n?void 0:n.getPopupElement();if(t&&t.contains(o)){var r=setTimeout((function(){var e,n=Cn.indexOf(r);-1!==n&&Cn.splice(n,1),_e(),xe||t.contains(document.activeElement)||null===(e=je.current)||void 0===e||e.focus()}));Cn.push(r)}for(var i=arguments.length,l=new Array(i>1?i-1:0),a=1;a<i;a++)l[a-1]=arguments[a];null==he||he.apply(void 0,[e].concat(l))},onKeyDown:function(e){var n,o=vn(),t=e.key,r="Enter"===t;if(r&&("combobox"!==M&&e.preventDefault(),tn||an(!0)),gn(!!Ge),"Backspace"===t&&!o&&Ce&&!Ge&&y.length){for(var i=v(y),l=null,a=i.length-1;a>=0;a-=1){var c=i[a];if(!c.disabled){i.splice(a,1),l=c;break}}l&&E(i,{type:"remove",values:[l]})}for(var s=arguments.length,u=new Array(s>1?s-1:0),d=1;d<s;d++)u[d-1]=arguments[d];!tn||r&&hn.current||(r&&(hn.current=!0),null===(n=ke.current)||void 0===n||n.onKeyDown.apply(n,[e].concat(u))),null==ge||ge.apply(void 0,[e].concat(u))},onKeyUp:function(e){for(var n=arguments.length,o=new Array(n>1?n-1:0),t=1;t<n;t++)o[t-1]=arguments[t];var r;tn&&(null===(r=ke.current)||void 0===r||r.onKeyUp.apply(r,[e].concat(o))),"Enter"===e.key&&(hn.current=!1),null==ve||ve.apply(void 0,[e].concat(o))},onFocus:function(){Ve(!0),D||(me&&!bn.current&&me.apply(void 0,arguments),pe.includes("focus")&&an(!0)),bn.current=!0},onBlur:function(){We.current=!0,Ve(!1,(function(){bn.current=!1,We.current=!1,an(!1)})),D||(Ge&&("tags"===M?V(Ge,{source:"submit"}):"multiple"===M&&V("",{source:"blur"})),fe&&fe.apply(void 0,arguments))}}),e.createElement(De,{visible:Fe&&!tn,values:y}),Pn,In,Rn&&Nn),e.createElement(ce.Provider,{value:yn},xn)})),ze=function(){return null};ze.isSelectOptGroup=!0;var je=function(){return null};je.isSelectOption=!0;var ke=["disabled","title","children","style","className"];function We(e){return"string"==typeof e||"number"==typeof e}var Ae=function(o,t){var l=e.useContext(ce),c=l.prefixCls,s=l.id,m=l.open,f=l.multiple,g=l.mode,h=l.searchValue,b=l.toggleOpen,w=l.notFoundContent,$=l.onPopupScroll,I=e.useContext(Me),y=I.maxCount,E=I.flattenOptions,x=I.onActiveValue,O=I.defaultActiveFirstOption,R=I.onSelect,N=I.menuItemSelectedIcon,M=I.rawValues,D=I.fieldNames,P=I.virtual,H=I.direction,B=I.listHeight,T=I.listItemHeight,z=I.optionRender,j="".concat(c,"-item"),k=C((function(){return E}),[m,E],(function(e,n){return n[0]&&e[1]!==n[1]})),W=e.useRef(null),A=e.useMemo((function(){return f&&Oe(y)&&(null==M?void 0:M.size)>=y}),[f,y,null==M?void 0:M.size]),L=function(e){e.preventDefault()},F=function(e){var n;null===(n=W.current)||void 0===n||n.scrollTo("number"==typeof e?{index:e}:e)},V=e.useCallback((function(e){return"combobox"!==g&&M.has(e)}),[g,v(M).toString(),M.size]),_=function(e){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,o=k.length,t=0;t<o;t+=1){var r=(e+t*n+o)%o,i=k[r]||{},l=i.group,a=i.data;if(!l&&(null==a||!a.disabled)&&(V(a.value)||!A))return r}return-1},G=e.useState((function(){return _(0)})),K=r(G,2),q=K[0],X=K[1],U=function(e){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];X(e);var o={source:n?"keyboard":"mouse"},t=k[e];t?x(t.value,e,o):x(null,-1,o)};e.useEffect((function(){U(!1!==O?_(0):-1)}),[k.length,h]);var Y=e.useCallback((function(e){return"combobox"===g?String(e).toLowerCase()===h.toLowerCase():M.has(e)}),[g,h,v(M).toString(),M.size]);e.useEffect((function(){var e,n=setTimeout((function(){if(!f&&m&&1===M.size){var e=Array.from(M)[0],n=k.findIndex((function(n){var o=n.data;return h?String(o.value).startsWith(h):o.value===e}));-1!==n&&(U(n),F(n))}}));m&&(null===(e=W.current)||void 0===e||e.scrollTo(void 0));return function(){return clearTimeout(n)}}),[m,h]);var Q=function(e){void 0!==e&&R(e,{selected:!M.has(e)}),f||b(!1)};if(e.useImperativeHandle(t,(function(){return{onKeyDown:function(e){var n=e.which,o=e.ctrlKey;switch(n){case i.N:case i.P:case i.UP:case i.DOWN:var t=0;if(n===i.UP?t=-1:n===i.DOWN?t=1:/(mac\sos|macintosh)/i.test(navigator.appVersion)&&o&&(n===i.N?t=1:n===i.P&&(t=-1)),0!==t){var r=_(q+t,t);F(r),U(r,!0)}break;case i.TAB:case i.ENTER:var l,a=k[q];!a||null!=a&&null!==(l=a.data)&&void 0!==l&&l.disabled||A?Q(void 0):Q(a.value),m&&e.preventDefault();break;case i.ESC:b(!1),m&&e.stopPropagation()}},onKeyUp:function(){},scrollTo:function(e){F(e)}}})),0===k.length)return e.createElement("div",{role:"listbox",id:"".concat(s,"_list"),className:"".concat(j,"-empty"),onMouseDown:L},w);var J=Object.keys(D).map((function(e){return D[e]})),Z=function(e){return e.label};function ee(e,n){return{role:e.group?"presentation":"option",id:"".concat(s,"_list_").concat(n)}}var oe=function(n){var o=k[n];if(!o)return null;var t=o.data||{},r=t.value,i=o.group,l=u(t,!0),a=Z(o);return o?e.createElement("div",p({"aria-label":"string"!=typeof a||i?null:a},l,{key:n},ee(o,n),{"aria-selected":Y(r)}),r):null},te={role:"listbox",id:"".concat(s,"_list")};return e.createElement(e.Fragment,null,P&&e.createElement("div",p({},te,{style:{height:0,width:0,overflow:"hidden"}}),oe(q-1),oe(q),oe(q+1)),e.createElement(ne,{itemKey:"key",ref:W,data:k,height:B,itemHeight:T,fullHeight:!1,onMouseDown:L,onScroll:$,virtual:P,direction:H,innerProps:P?null:te},(function(o,t){var r=o.group,i=o.groupOption,l=o.data,c=o.label,s=o.value,m=l.key;if(r){var f,v=null!==(f=l.title)&&void 0!==f?f:We(c)?c.toString():void 0;return e.createElement("div",{className:n(j,"".concat(j,"-group"),l.className),title:v},void 0!==c?c:m)}var g=l.disabled,h=l.title;l.children;var b=l.style,C=l.className,w=a(l,ke),$=S(w,J),I=V(s),y=g||!I&&A,E="".concat(j,"-option"),x=n(j,E,C,d(d(d(d({},"".concat(E,"-grouped"),i),"".concat(E,"-active"),q===t&&!y),"".concat(E,"-disabled"),y),"".concat(E,"-selected"),I)),O=Z(o),R=!N||"function"==typeof N||I,M="number"==typeof O?O:O||s,D=We(M)?M.toString():void 0;return void 0!==h&&(D=h),e.createElement("div",p({},u($),P?{}:ee(o,t),{"aria-selected":Y(s),className:x,title:D,onMouseMove:function(){q===t||y||U(t)},onClick:function(){y||Q(s)},style:b}),e.createElement("div",{className:"".concat(E,"-content")},"function"==typeof z?z(o,{index:t}):M),e.isValidElement(N)||I,R&&e.createElement(ae,{className:"".concat(j,"-option-state"),customizeIcon:N,customizeIconProps:{value:s,disabled:y,isSelected:I}},I?"✓":null))})))},Le=e.forwardRef(Ae);function Fe(e,n){return me(e).join("").toUpperCase().includes(n)}var Ve=0,_e=w();function Ge(n){var o=e.useState(),t=r(o,2),i=t[0],l=t[1];return e.useEffect((function(){var e;l("rc_select_".concat((_e?(e=Ve,Ve+=1):e="TEST_OR_SSR",e)))}),[]),n||i}var Ke=["children","value"],qe=["children"];function Xe(n){var o=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return $(n).map((function(n,t){if(!e.isValidElement(n)||!n.type)return null;var r=n,i=r.type.isSelectOptGroup,c=r.key,s=r.props,u=s.children,d=a(s,qe);return o||!i?function(e){var n=e,o=n.key,t=n.props,r=t.children,i=t.value,c=a(t,Ke);return l({key:o,value:void 0!==i?i:o,children:r},c)}(n):l(l({key:"__RC_SELECT_GRP__".concat(null===c?t:c,"__"),label:c},d),{},{options:Xe(u)})})).filter((function(e){return e}))}function Ue(n){var o=e.useRef();o.current=n;var t=e.useCallback((function(){return o.current.apply(o,arguments)}),[]);return t}var Ye=["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","optionRender","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","direction","listHeight","listItemHeight","labelRender","value","defaultValue","labelInValue","onChange","maxCount"],Qe=["inputValue"];var Je=e.forwardRef((function(n,o){var i=n.id,c=n.mode,s=n.prefixCls,u=void 0===s?"rc-select":s,m=n.backfill,f=n.fieldNames,g=n.inputValue,h=n.searchValue,C=n.onSearch,S=n.autoClearSearchValue,w=void 0===S||S,$=n.onSelect,I=n.onDeselect,y=n.dropdownMatchSelectWidth,E=void 0===y||y,x=n.filterOption,O=n.filterSort,R=n.optionFilterProp,N=n.optionLabelProp,M=n.options,D=n.optionRender,P=n.children,H=n.defaultActiveFirstOption,B=n.menuItemSelectedIcon,T=n.virtual,z=n.direction,j=n.listHeight,k=void 0===j?200:j,W=n.listItemHeight,A=void 0===W?20:W,L=n.labelRender,F=n.value,V=n.defaultValue,_=n.labelInValue,G=n.onChange,K=n.maxCount,q=a(n,Ye),X=Ge(i),U=Be(c),Y=!(M||!P),Q=e.useMemo((function(){return(void 0!==x||"combobox"!==c)&&x}),[x,c]),J=e.useMemo((function(){return Re(f,Y)}),[JSON.stringify(f),Y]),Z=b("",{value:void 0!==h?h:g,postState:function(e){return e||""}}),ee=r(Z,2),ne=ee[0],oe=ee[1],te=function(n,o,t,r,i){return e.useMemo((function(){var e=n;!n&&(e=Xe(o));var l=new Map,a=new Map,c=function(e,n,o){o&&"string"==typeof o&&e.set(n[o],n)};return function e(n){for(var o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=0;s<n.length;s+=1){var u=n[s];!u[t.options]||o?(l.set(u[t.value],u),c(a,u,t.label),c(a,u,r),c(a,u,i)):e(u[t.options],!0)}}(e),{options:e,valueOptions:l,labelOptions:a}}),[n,o,t,r,i])}(M,P,J,R,N),re=te.valueOptions,ie=te.labelOptions,le=te.options,ae=e.useCallback((function(e){return me(e).map((function(e){var n,o,r,i,l,a;(function(e){return!e||"object"!==t(e)})(e)?n=e:(r=e.key,o=e.label,n=null!==(a=e.value)&&void 0!==a?a:r);var c,s=re.get(n);s&&(void 0===o&&(o=null==s?void 0:s[N||J.label]),void 0===r&&(r=null!==(c=null==s?void 0:s.key)&&void 0!==c?c:n),i=null==s?void 0:s.disabled,l=null==s?void 0:s.title);return{label:o,value:n,key:r,disabled:i,title:l}}))}),[J,N,re]),ce=b(V,{value:F}),se=r(ce,2),ue=se[0],de=se[1],pe=e.useMemo((function(){var e,n=ae(U&&null===ue?[]:ue);return"combobox"===c&&function(e){return!e&&0!==e}(null===(e=n[0])||void 0===e?void 0:e.value)?[]:n}),[ue,ae,c,U]),fe=function(n,o){var t=e.useRef({values:new Map,options:new Map});return[e.useMemo((function(){var e=t.current,r=e.values,i=e.options,a=n.map((function(e){var n;return void 0===e.label?l(l({},e),{},{label:null===(n=r.get(e.value))||void 0===n?void 0:n.label}):e})),c=new Map,s=new Map;return a.forEach((function(e){c.set(e.value,e),s.set(e.value,o.get(e.value)||i.get(e.value))})),t.current.values=c,t.current.options=s,a}),[n,o]),e.useCallback((function(e){return o.get(e)||t.current.options.get(e)}),[o])]}(pe,re),ve=r(fe,2),ge=ve[0],he=ve[1],be=e.useMemo((function(){if(!c&&1===ge.length){var e=ge[0];if(null===e.value&&(null===e.label||void 0===e.label))return[]}return ge.map((function(e){var n;return l(l({},e),{},{label:null!==(n="function"==typeof L?L(e):e.label)&&void 0!==n?n:e.value})}))}),[c,ge,L]),Ce=e.useMemo((function(){return new Set(ge.map((function(e){return e.value})))}),[ge]);e.useEffect((function(){if("combobox"===c){var e,n=null===(e=ge[0])||void 0===e?void 0:e.value;oe(function(e){return null!=e}(n)?String(n):"")}}),[ge]);var Se=Ue((function(e,n){var o=null!=n?n:e;return d(d({},J.value,e),J.label,o)})),we=function(n,o,t,r,i){return e.useMemo((function(){if(!t||!1===r)return n;var e=o.options,a=o.label,c=o.value,s=[],u="function"==typeof r,p=t.toUpperCase(),m=u?r:function(n,o){return i?Fe(o[i],p):o[e]?Fe(o["children"!==a?a:"label"],p):Fe(o[c],p)},f=u?function(e){return Ne(e)}:function(e){return e};return n.forEach((function(n){if(n[e])if(m(t,f(n)))s.push(n);else{var o=n[e].filter((function(e){return m(t,f(e))}));o.length&&s.push(l(l({},n),{},d({},e,o)))}else m(t,f(n))&&s.push(n)})),s}),[n,r,i,t,o])}(e.useMemo((function(){if("tags"!==c)return le;var e=v(le);return v(ge).sort((function(e,n){return e.value<n.value?-1:1})).forEach((function(n){var o=n.value;(function(e){return re.has(e)})(o)||e.push(Se(o,n.label))})),e}),[Se,le,re,ge,c]),J,ne,Q,R),$e=e.useMemo((function(){return"tags"!==c||!ne||we.some((function(e){return e[R||"value"]===ne}))||we.some((function(e){return e[J.value]===ne}))?we:[Se(ne)].concat(v(we))}),[Se,R,c,we,ne,J]),Ie=function e(n){return v(n).sort((function(e,n){return O(e,n,{searchValue:ne})})).map((function(n){return Array.isArray(n.options)?l(l({},n),{},{options:n.options.length>0?e(n.options):n.options}):n}))},ye=e.useMemo((function(){return O?Ie($e):$e}),[$e,O,ne]),Ee=e.useMemo((function(){return function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=n.fieldNames,t=n.childrenAsData,r=[],i=Re(o,!1),l=i.label,a=i.value,c=i.options,s=i.groupLabel;return function e(n,o){Array.isArray(n)&&n.forEach((function(n){if(o||!(c in n)){var i=n[a];r.push({key:xe(n,r.length),groupOption:o,data:n,label:n[l],value:i})}else{var u=n[s];void 0===u&&t&&(u=n.label),r.push({key:xe(n,r.length),group:!0,data:n,label:u}),e(n[c],!0)}}))}(e,!1),r}(ye,{fieldNames:J,childrenAsData:Y})}),[ye,J,Y]),Oe=function(e){var n=ae(e);if(de(n),G&&(n.length!==ge.length||n.some((function(e,n){var o;return(null===(o=ge[n])||void 0===o?void 0:o.value)!==(null==e?void 0:e.value)})))){var o=_?n:n.map((function(e){return e.value})),t=n.map((function(e){return Ne(he(e.value))}));G(U?o:o[0],U?t:t[0])}},De=e.useState(null),Pe=r(De,2),He=Pe[0],ze=Pe[1],je=e.useState(0),ke=r(je,2),We=ke[0],Ae=ke[1],Ve=void 0!==H?H:"combobox"!==c,_e=e.useCallback((function(e,n){var o=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).source,t=void 0===o?"keyboard":o;Ae(n),m&&"combobox"===c&&null!==e&&"keyboard"===t&&ze(String(e))}),[m,c]),Ke=function(e,n,o){var t=function(){var n,o=he(e);return[_?{label:null==o?void 0:o[J.label],value:e,key:null!==(n=null==o?void 0:o.key)&&void 0!==n?n:e}:e,Ne(o)]};if(n&&$){var i=t(),l=r(i,2),a=l[0],c=l[1];$(a,c)}else if(!n&&I&&"clear"!==o){var s=t(),u=r(s,2),d=u[0],p=u[1];I(d,p)}},qe=Ue((function(e,n){var o,t=!U||n.selected;o=t?U?[].concat(v(ge),[e]):[e]:ge.filter((function(n){return n.value!==e})),Oe(o),Ke(e,t),"combobox"===c?ze(""):Be&&!w||(oe(""),ze(""))})),Je=e.useMemo((function(){var e=!1!==T&&!1!==E;return l(l({},te),{},{flattenOptions:Ee,onActiveValue:_e,defaultActiveFirstOption:Ve,onSelect:qe,menuItemSelectedIcon:B,rawValues:Ce,fieldNames:J,virtual:e,direction:z,listHeight:k,listItemHeight:A,childrenAsData:Y,maxCount:K,optionRender:D})}),[K,te,Ee,_e,Ve,qe,B,Ce,J,T,E,z,k,A,Y,D]);return e.createElement(Me.Provider,{value:Je},e.createElement(Te,p({},q,{id:X,prefixCls:u,ref:o,omitDomProps:Qe,mode:c,displayValues:be,onDisplayValuesChange:function(e,n){Oe(e);var o=n.type,t=n.values;"remove"!==o&&"clear"!==o||t.forEach((function(e){Ke(e.value,!1,o)}))},direction:z,searchValue:ne,onSearch:function(e,n){if(oe(e),ze(null),"submit"!==n.source)"blur"!==n.source&&("combobox"===c&&Oe(e),null==C||C(e));else{var o=(e||"").trim();if(o){var t=Array.from(new Set([].concat(v(Ce),[o])));Oe(t),Ke(o,!0),oe("")}}},autoClearSearchValue:w,onSearchSplit:function(e){var n=e;"tags"!==c&&(n=e.map((function(e){var n=ie.get(e);return null==n?void 0:n.value})).filter((function(e){return void 0!==e})));var o=Array.from(new Set([].concat(v(Ce),v(n))));Oe(o),o.forEach((function(e){Ke(e,!0)}))},dropdownMatchSelectWidth:E,OptionList:Le,emptyOptions:!Ee.length,activeValue:He,activeDescendantId:"".concat(X,"_list_").concat(We)})))}));Je.Option=je,Je.OptGroup=ze;const Ze=n=>{const{componentName:t}=n,{getPrefixCls:r}=e.useContext(I),i=r("empty");switch(t){case"Table":case"List":return o.createElement(te,{image:te.PRESENTED_IMAGE_SIMPLE});case"Select":case"TreeSelect":case"Cascader":case"Transfer":case"Mentions":return o.createElement(te,{image:te.PRESENTED_IMAGE_SIMPLE,className:`${i}-small`});case"Table.filter":return null;default:return o.createElement(te,null)}};function en(e,n){return e||(e=>{const n={overflow:{adjustX:!0,adjustY:!0,shiftY:!0},htmlRegion:"scroll"===e?"scroll":"visible",dynamicInset:!0};return{bottomLeft:Object.assign(Object.assign({},n),{points:["tl","bl"],offset:[0,4]}),bottomRight:Object.assign(Object.assign({},n),{points:["tr","br"],offset:[0,4]}),topLeft:Object.assign(Object.assign({},n),{points:["bl","tl"],offset:[0,-4]}),topRight:Object.assign(Object.assign({},n),{points:["br","tr"],offset:[0,-4]})}})(n)}const nn=e=>{const{optionHeight:n,optionFontSize:o,optionLineHeight:t,optionPadding:r}=e;return{position:"relative",display:"block",minHeight:n,padding:r,color:e.colorText,fontWeight:"normal",fontSize:o,lineHeight:t,boxSizing:"border-box"}},on=e=>{const{antCls:n,componentCls:o}=e,t=`${o}-item`,r=`&${n}-slide-up-enter${n}-slide-up-enter-active`,i=`&${n}-slide-up-appear${n}-slide-up-appear-active`,l=`&${n}-slide-up-leave${n}-slide-up-leave-active`,a=`${o}-dropdown-placement-`,c=`${t}-option-selected`;return[{[`${o}-dropdown`]:Object.assign(Object.assign({},y(e)),{position:"absolute",top:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",padding:e.paddingXXS,overflow:"hidden",fontSize:e.fontSize,fontVariant:"initial",backgroundColor:e.colorBgElevated,borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,[`\n          ${r}${a}bottomLeft,\n          ${i}${a}bottomLeft\n        `]:{animationName:Y},[`\n          ${r}${a}topLeft,\n          ${i}${a}topLeft,\n          ${r}${a}topRight,\n          ${i}${a}topRight\n        `]:{animationName:U},[`${l}${a}bottomLeft`]:{animationName:X},[`\n          ${l}${a}topLeft,\n          ${l}${a}topRight\n        `]:{animationName:q},"&-hidden":{display:"none"},[t]:Object.assign(Object.assign({},nn(e)),{cursor:"pointer",transition:`background ${e.motionDurationSlow} ease`,borderRadius:e.borderRadiusSM,"&-group":{color:e.colorTextDescription,fontSize:e.fontSizeSM,cursor:"default"},"&-option":{display:"flex","&-content":Object.assign({flex:"auto"},E),"&-state":{flex:"none",display:"flex",alignItems:"center"},[`&-active:not(${t}-option-disabled)`]:{backgroundColor:e.optionActiveBg},[`&-selected:not(${t}-option-disabled)`]:{color:e.optionSelectedColor,fontWeight:e.optionSelectedFontWeight,backgroundColor:e.optionSelectedBg,[`${t}-option-state`]:{color:e.colorPrimary}},"&-disabled":{[`&${t}-option-selected`]:{backgroundColor:e.colorBgContainerDisabled},color:e.colorTextDisabled,cursor:"not-allowed"},"&-grouped":{paddingInlineStart:e.calc(e.controlPaddingHorizontal).mul(2).equal()}},"&-empty":Object.assign(Object.assign({},nn(e)),{color:e.colorTextDisabled})}),[`${c}:has(+ ${c})`]:{borderEndStartRadius:0,borderEndEndRadius:0,[`& + ${c}`]:{borderStartStartRadius:0,borderStartEndRadius:0}},"&-rtl":{direction:"rtl"}})},K(e,"slide-up"),K(e,"slide-down"),re(e,"move-up"),re(e,"move-down")]},tn=e=>{const{multipleSelectItemHeight:n,paddingXXS:o,lineWidth:t,INTERNAL_FIXED_ITEM_MARGIN:r}=e,i=e.max(e.calc(o).sub(t).equal(),0);return{basePadding:i,containerPadding:e.max(e.calc(i).sub(r).equal(),0),itemHeight:R(n),itemLineHeight:R(e.calc(n).sub(e.calc(e.lineWidth).mul(2)).equal())}},rn=e=>{const{componentCls:n,iconCls:o,borderRadiusSM:t,motionDurationSlow:r,paddingXS:i,multipleItemColorDisabled:l,multipleItemBorderColorDisabled:a,colorIcon:c,colorIconHover:s,INTERNAL_FIXED_ITEM_MARGIN:u}=e,d=`${n}-selection-overflow`;return{[d]:{position:"relative",display:"flex",flex:"auto",flexWrap:"wrap",maxWidth:"100%","&-item":{flex:"none",alignSelf:"center",maxWidth:"100%",display:"inline-flex"},[`${n}-selection-item`]:{display:"flex",alignSelf:"center",flex:"none",boxSizing:"border-box",maxWidth:"100%",marginBlock:u,borderRadius:t,cursor:"default",transition:`font-size ${r}, line-height ${r}, height ${r}`,marginInlineEnd:e.calc(u).mul(2).equal(),paddingInlineStart:i,paddingInlineEnd:e.calc(i).div(2).equal(),[`${n}-disabled&`]:{color:l,borderColor:a,cursor:"not-allowed"},"&-content":{display:"inline-block",marginInlineEnd:e.calc(i).div(2).equal(),overflow:"hidden",whiteSpace:"pre",textOverflow:"ellipsis"},"&-remove":Object.assign(Object.assign({},O()),{display:"inline-flex",alignItems:"center",color:c,fontWeight:"bold",fontSize:10,lineHeight:"inherit",cursor:"pointer",[`> ${o}`]:{verticalAlign:"-0.2em"},"&:hover":{color:s}})}}}},ln=(e,n)=>{const{componentCls:o,INTERNAL_FIXED_ITEM_MARGIN:t}=e,r=`${o}-selection-overflow`,i=e.multipleSelectItemHeight,l=(e=>{const{multipleSelectItemHeight:n,selectHeight:o,lineWidth:t}=e;return e.calc(o).sub(n).div(2).sub(t).equal()})(e),a=n?`${o}-${n}`:"",c=tn(e);return{[`${o}-multiple${a}`]:Object.assign(Object.assign({},rn(e)),{[`${o}-selector`]:{display:"flex",alignItems:"center",width:"100%",height:"100%",paddingInline:c.basePadding,paddingBlock:c.containerPadding,borderRadius:e.borderRadius,[`${o}-disabled&`]:{background:e.multipleSelectorBgDisabled,cursor:"not-allowed"},"&:after":{display:"inline-block",width:0,margin:`${R(t)} 0`,lineHeight:R(i),visibility:"hidden",content:'"\\a0"'}},[`${o}-selection-item`]:{height:c.itemHeight,lineHeight:R(c.itemLineHeight)},[`${o}-selection-wrap`]:{alignSelf:"flex-start","&:after":{lineHeight:R(i),marginBlock:t}},[`${o}-prefix`]:{marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(c.basePadding).equal()},[`${r}-item + ${r}-item,\n        ${o}-prefix + ${o}-selection-wrap\n      `]:{[`${o}-selection-search`]:{marginInlineStart:0},[`${o}-selection-placeholder`]:{insetInlineStart:0}},[`${r}-item-suffix`]:{minHeight:c.itemHeight,marginBlock:t},[`${o}-selection-search`]:{display:"inline-flex",position:"relative",maxWidth:"100%",marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(l).equal(),"\n          &-input,\n          &-mirror\n        ":{height:i,fontFamily:e.fontFamily,lineHeight:R(i),transition:`all ${e.motionDurationSlow}`},"&-input":{width:"100%",minWidth:4.1},"&-mirror":{position:"absolute",top:0,insetInlineStart:0,insetInlineEnd:"auto",zIndex:999,whiteSpace:"pre",visibility:"hidden"}},[`${o}-selection-placeholder`]:{position:"absolute",top:"50%",insetInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(c.basePadding).equal(),insetInlineEnd:e.inputPaddingHorizontalBase,transform:"translateY(-50%)",transition:`all ${e.motionDurationSlow}`}})}};function an(e,n){const{componentCls:o}=e,t=n?`${o}-${n}`:"",r={[`${o}-multiple${t}`]:{fontSize:e.fontSize,[`${o}-selector`]:{[`${o}-show-search&`]:{cursor:"text"}},[`\n        &${o}-show-arrow ${o}-selector,\n        &${o}-allow-clear ${o}-selector\n      `]:{paddingInlineEnd:e.calc(e.fontSizeIcon).add(e.controlPaddingHorizontal).equal()}}};return[ln(e,n),r]}const cn=e=>{const{componentCls:n}=e,o=x(e,{selectHeight:e.controlHeightSM,multipleSelectItemHeight:e.multipleItemHeightSM,borderRadius:e.borderRadiusSM,borderRadiusSM:e.borderRadiusXS}),t=x(e,{fontSize:e.fontSizeLG,selectHeight:e.controlHeightLG,multipleSelectItemHeight:e.multipleItemHeightLG,borderRadius:e.borderRadiusLG,borderRadiusSM:e.borderRadius});return[an(e),an(o,"sm"),{[`${n}-multiple${n}-sm`]:{[`${n}-selection-placeholder`]:{insetInline:e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal()},[`${n}-selection-search`]:{marginInlineStart:2}}},an(t,"lg")]};function sn(e,n){const{componentCls:o,inputPaddingHorizontalBase:t,borderRadius:r}=e,i=e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),l=n?`${o}-${n}`:"";return{[`${o}-single${l}`]:{fontSize:e.fontSize,height:e.controlHeight,[`${o}-selector`]:Object.assign(Object.assign({},y(e,!0)),{display:"flex",borderRadius:r,flex:"1 1 auto",[`${o}-selection-wrap:after`]:{lineHeight:R(i)},[`${o}-selection-search`]:{position:"absolute",inset:0,width:"100%","&-input":{width:"100%",WebkitAppearance:"textfield"}},[`\n          ${o}-selection-item,\n          ${o}-selection-placeholder\n        `]:{display:"block",padding:0,lineHeight:R(i),transition:`all ${e.motionDurationSlow}, visibility 0s`,alignSelf:"center"},[`${o}-selection-placeholder`]:{transition:"none",pointerEvents:"none"},[["&:after",`${o}-selection-item:empty:after`,`${o}-selection-placeholder:empty:after`].join(",")]:{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'}}),[`\n        &${o}-show-arrow ${o}-selection-item,\n        &${o}-show-arrow ${o}-selection-search,\n        &${o}-show-arrow ${o}-selection-placeholder\n      `]:{paddingInlineEnd:e.showArrowPaddingInlineEnd},[`&${o}-open ${o}-selection-item`]:{color:e.colorTextPlaceholder},[`&:not(${o}-customize-input)`]:{[`${o}-selector`]:{width:"100%",height:"100%",alignItems:"center",padding:`0 ${R(t)}`,[`${o}-selection-search-input`]:{height:i,fontSize:e.fontSize},"&:after":{lineHeight:R(i)}}},[`&${o}-customize-input`]:{[`${o}-selector`]:{"&:after":{display:"none"},[`${o}-selection-search`]:{position:"static",width:"100%"},[`${o}-selection-placeholder`]:{position:"absolute",insetInlineStart:0,insetInlineEnd:0,padding:`0 ${R(t)}`,"&:after":{display:"none"}}}}}}}function un(e){const{componentCls:n}=e,o=e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal();return[sn(e),sn(x(e,{controlHeight:e.controlHeightSM,borderRadius:e.borderRadiusSM}),"sm"),{[`${n}-single${n}-sm`]:{[`&:not(${n}-customize-input)`]:{[`${n}-selector`]:{padding:`0 ${R(o)}`},[`&${n}-show-arrow ${n}-selection-search`]:{insetInlineEnd:e.calc(o).add(e.calc(e.fontSize).mul(1.5)).equal()},[`\n            &${n}-show-arrow ${n}-selection-item,\n            &${n}-show-arrow ${n}-selection-placeholder\n          `]:{paddingInlineEnd:e.calc(e.fontSize).mul(1.5).equal()}}}},sn(x(e,{controlHeight:e.singleItemHeightLG,fontSize:e.fontSizeLG,borderRadius:e.borderRadiusLG}),"lg")]}const dn=(e,n)=>{const{componentCls:o,antCls:t,controlOutlineWidth:r}=e;return{[`&:not(${o}-customize-input) ${o}-selector`]:{border:`${R(e.lineWidth)} ${e.lineType} ${n.borderColor}`,background:e.selectorBg},[`&:not(${o}-disabled):not(${o}-customize-input):not(${t}-pagination-size-changer)`]:{[`&:hover ${o}-selector`]:{borderColor:n.hoverBorderHover},[`${o}-focused& ${o}-selector`]:{borderColor:n.activeBorderColor,boxShadow:`0 0 0 ${R(r)} ${n.activeOutlineColor}`,outline:0},[`${o}-prefix`]:{color:n.color}}}},pn=(e,n)=>({[`&${e.componentCls}-status-${n.status}`]:Object.assign({},dn(e,n))}),mn=e=>({"&-outlined":Object.assign(Object.assign(Object.assign(Object.assign({},dn(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),pn(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),pn(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${R(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),fn=(e,n)=>{const{componentCls:o,antCls:t}=e;return{[`&:not(${o}-customize-input) ${o}-selector`]:{background:n.bg,border:`${R(e.lineWidth)} ${e.lineType} transparent`,color:n.color},[`&:not(${o}-disabled):not(${o}-customize-input):not(${t}-pagination-size-changer)`]:{[`&:hover ${o}-selector`]:{background:n.hoverBg},[`${o}-focused& ${o}-selector`]:{background:e.selectorBg,borderColor:n.activeBorderColor,outline:0}}}},vn=(e,n)=>({[`&${e.componentCls}-status-${n.status}`]:Object.assign({},fn(e,n))}),gn=e=>({"&-filled":Object.assign(Object.assign(Object.assign(Object.assign({},fn(e,{bg:e.colorFillTertiary,hoverBg:e.colorFillSecondary,activeBorderColor:e.activeBorderColor,color:e.colorText})),vn(e,{status:"error",bg:e.colorErrorBg,hoverBg:e.colorErrorBgHover,activeBorderColor:e.colorError,color:e.colorError})),vn(e,{status:"warning",bg:e.colorWarningBg,hoverBg:e.colorWarningBgHover,activeBorderColor:e.colorWarning,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{borderColor:e.colorBorder,background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.colorBgContainer,border:`${R(e.lineWidth)} ${e.lineType} ${e.colorSplit}`}})}),hn=e=>({"&-borderless":{[`${e.componentCls}-selector`]:{background:"transparent",border:`${R(e.lineWidth)} ${e.lineType} transparent`},[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${R(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`},[`&${e.componentCls}-status-error`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorError}},[`&${e.componentCls}-status-warning`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorWarning}}}}),bn=(e,n)=>{const{componentCls:o,antCls:t}=e;return{[`&:not(${o}-customize-input) ${o}-selector`]:{borderWidth:`0 0 ${R(e.lineWidth)} 0`,borderStyle:`none none ${e.lineType} none`,borderColor:n.borderColor,background:e.selectorBg,borderRadius:0},[`&:not(${o}-disabled):not(${o}-customize-input):not(${t}-pagination-size-changer)`]:{[`&:hover ${o}-selector`]:{borderColor:n.hoverBorderHover},[`${o}-focused& ${o}-selector`]:{borderColor:n.activeBorderColor,outline:0},[`${o}-prefix`]:{color:n.color}}}},Cn=(e,n)=>({[`&${e.componentCls}-status-${n.status}`]:Object.assign({},bn(e,n))}),Sn=e=>({"&-underlined":Object.assign(Object.assign(Object.assign(Object.assign({},bn(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),Cn(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),Cn(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${R(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),wn=e=>({[e.componentCls]:Object.assign(Object.assign(Object.assign(Object.assign({},mn(e)),gn(e)),hn(e)),Sn(e))}),$n=e=>{const{componentCls:n}=e;return{position:"relative",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,input:{cursor:"pointer"},[`${n}-show-search&`]:{cursor:"text",input:{cursor:"auto",color:"inherit",height:"100%"}},[`${n}-disabled&`]:{cursor:"not-allowed",input:{cursor:"not-allowed"}}}},In=e=>{const{componentCls:n}=e;return{[`${n}-selection-search-input`]:{margin:0,padding:0,background:"transparent",border:"none",outline:"none",appearance:"none",fontFamily:"inherit","&::-webkit-search-cancel-button":{display:"none",appearance:"none"}}}},yn=e=>{const{antCls:n,componentCls:o,inputPaddingHorizontalBase:t,iconCls:r}=e,i={[`${o}-clear`]:{opacity:1,background:e.colorBgBase,borderRadius:"50%"}};return{[o]:Object.assign(Object.assign({},y(e)),{position:"relative",display:"inline-flex",cursor:"pointer",[`&:not(${o}-customize-input) ${o}-selector`]:Object.assign(Object.assign({},$n(e)),In(e)),[`${o}-selection-item`]:Object.assign(Object.assign({flex:1,fontWeight:"normal",position:"relative",userSelect:"none"},E),{[`> ${n}-typography`]:{display:"inline"}}),[`${o}-selection-placeholder`]:Object.assign(Object.assign({},E),{flex:1,color:e.colorTextPlaceholder,pointerEvents:"none"}),[`${o}-arrow`]:Object.assign(Object.assign({},O()),{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:t,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,lineHeight:1,textAlign:"center",pointerEvents:"none",display:"flex",alignItems:"center",transition:`opacity ${e.motionDurationSlow} ease`,[r]:{verticalAlign:"top",transition:`transform ${e.motionDurationSlow}`,"> svg":{verticalAlign:"top"},[`&:not(${o}-suffix)`]:{pointerEvents:"auto"}},[`${o}-disabled &`]:{cursor:"not-allowed"},"> *:not(:last-child)":{marginInlineEnd:8}}),[`${o}-selection-wrap`]:{display:"flex",width:"100%",position:"relative",minWidth:0,"&:after":{content:'"\\a0"',width:0,overflow:"hidden"}},[`${o}-prefix`]:{flex:"none",marginInlineEnd:e.selectAffixPadding},[`${o}-clear`]:{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:t,zIndex:1,display:"inline-block",width:e.fontSizeIcon,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",cursor:"pointer",opacity:0,transition:`color ${e.motionDurationMid} ease, opacity ${e.motionDurationSlow} ease`,textRendering:"auto","&:before":{display:"block"},"&:hover":{color:e.colorIcon}},"@media(hover:none)":i,"&:hover":i}),[`${o}-status`]:{"&-error, &-warning, &-success, &-validating":{[`&${o}-has-feedback`]:{[`${o}-clear`]:{insetInlineEnd:e.calc(t).add(e.fontSize).add(e.paddingXS).equal()}}}}}},En=e=>{const{componentCls:n}=e;return[{[n]:{[`&${n}-in-form-item`]:{width:"100%"}}},yn(e),un(e),cn(e),on(e),{[`${n}-rtl`]:{direction:"rtl"}},M(e,{borderElCls:`${n}-selector`,focusElCls:`${n}-focused`})]},xn=N("Select",((e,{rootPrefixCls:n})=>{const o=x(e,{rootPrefixCls:n,inputPaddingHorizontalBase:e.calc(e.paddingSM).sub(1).equal(),multipleSelectItemHeight:e.multipleItemHeight,selectHeight:e.controlHeight});return[En(o),wn(o)]}),(e=>{const{fontSize:n,lineHeight:o,lineWidth:t,controlHeight:r,controlHeightSM:i,controlHeightLG:l,paddingXXS:a,controlPaddingHorizontal:c,zIndexPopupBase:s,colorText:u,fontWeightStrong:d,controlItemBgActive:p,controlItemBgHover:m,colorBgContainer:f,colorFillSecondary:v,colorBgContainerDisabled:g,colorTextDisabled:h,colorPrimaryHover:b,colorPrimary:C,controlOutline:S}=e,w=2*a,$=2*t,I=Math.min(r-w,r-$),y=Math.min(i-w,i-$),E=Math.min(l-w,l-$);return{INTERNAL_FIXED_ITEM_MARGIN:Math.floor(a/2),zIndexPopup:s+50,optionSelectedColor:u,optionSelectedFontWeight:d,optionSelectedBg:p,optionActiveBg:m,optionPadding:`${(r-n*o)/2}px ${c}px`,optionFontSize:n,optionLineHeight:o,optionHeight:r,selectorBg:f,clearBg:f,singleItemHeightLG:l,multipleItemBg:v,multipleItemBorderColor:"transparent",multipleItemHeight:I,multipleItemHeightSM:y,multipleItemHeightLG:E,multipleSelectorBgDisabled:g,multipleItemColorDisabled:h,multipleItemBorderColorDisabled:"transparent",showArrowPaddingInlineEnd:Math.ceil(1.25*e.fontSize),hoverBorderColor:b,activeBorderColor:C,activeOutlineColor:S,selectAffixPadding:a}}),{unitless:{optionLineHeight:!0,optionSelectedFontWeight:!0}});function On({suffixIcon:n,clearIcon:o,menuItemSelectedIcon:t,removeIcon:r,loading:i,multiple:l,hasFeedback:a,prefixCls:c,showSuffixIcon:s,feedbackIcon:u,showArrow:d,componentName:p}){const m=null!=o?o:e.createElement(D,null),f=o=>null!==n||a||d?e.createElement(e.Fragment,null,!1!==s&&o,a&&u):null;let v=null;if(void 0!==n)v=f(n);else if(i)v=f(e.createElement(H,{spin:!0}));else{const n=`${c}-suffix`;v=({open:o,showSearch:t})=>f(o&&t?e.createElement(le,{className:n}):e.createElement(ie,{className:n}))}let g=null;g=void 0!==t?t:l?e.createElement(Q,null):null;let h=null;return h=void 0!==r?r:e.createElement(P,null),{clearIcon:m,suffixIcon:v,itemIcon:g,removeIcon:h}}const Rn="SECRET_COMBOBOX_MODE_DO_NOT_USE",Nn=(o,t)=>{var r,i,l,a,c;const{prefixCls:s,bordered:u,className:d,rootClassName:p,getPopupContainer:m,popupClassName:f,dropdownClassName:v,listHeight:g=256,placement:h,listItemHeight:b,size:C,disabled:w,notFoundContent:$,status:y,builtinPlacements:E,dropdownMatchSelectWidth:x,popupMatchSelectWidth:O,direction:R,style:N,allowClear:M,variant:D,dropdownStyle:P,transitionName:H,tagRender:V,maxCount:_,prefix:G,dropdownRender:K,popupRender:q,onDropdownVisibleChange:X,onOpenChange:U,styles:Y,classNames:Q}=o,ne=function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(o[t[r]]=e[t[r]])}return o}(o,["prefixCls","bordered","className","rootClassName","getPopupContainer","popupClassName","dropdownClassName","listHeight","placement","listItemHeight","size","disabled","notFoundContent","status","builtinPlacements","dropdownMatchSelectWidth","popupMatchSelectWidth","direction","style","allowClear","variant","dropdownStyle","transitionName","tagRender","maxCount","prefix","dropdownRender","popupRender","onDropdownVisibleChange","onOpenChange","styles","classNames"]),{getPopupContainer:oe,getPrefixCls:te,renderEmpty:re,direction:ie,virtual:le,popupMatchSelectWidth:ae,popupOverflow:ce}=e.useContext(I),{showSearch:se,style:ue,styles:de,className:pe,classNames:me}=B("select"),[,fe]=T(),ve=null!=b?b:null==fe?void 0:fe.controlHeight,ge=te("select",s),he=te(),be=null!=R?R:ie,{compactSize:Ce,compactItemClassnames:Se}=z(ge,be),[we,$e]=J("select",D,u),Ie=j(ge),[ye,Ee,xe]=xn(ge,Ie),Oe=e.useMemo((()=>{const{mode:e}=o;if("combobox"!==e)return e===Rn?"combobox":e}),[o.mode]),Re="multiple"===Oe||"tags"===Oe,Ne=function(e,n){return void 0!==n?n:null!==e}(o.suffixIcon,o.showArrow),Me=null!==(r=null!=O?O:x)&&void 0!==r?r:ae,De=(null===(i=null==Y?void 0:Y.popup)||void 0===i?void 0:i.root)||(null===(l=de.popup)||void 0===l?void 0:l.root)||P,Pe=q||K,He=U||X,{status:Be,hasFeedback:Te,isFormItemInput:ze,feedbackIcon:je}=e.useContext(k),ke=ee(Be,y);let We;We=void 0!==$?$:"combobox"===Oe?null:(null==re?void 0:re("Select"))||e.createElement(Ze,{componentName:"Select"});const{suffixIcon:Ae,itemIcon:Le,removeIcon:Fe,clearIcon:Ve}=On(Object.assign(Object.assign({},ne),{multiple:Re,hasFeedback:Te,feedbackIcon:je,showSuffixIcon:Ne,prefixCls:ge,componentName:"Select"})),_e=!0===M?{clearIcon:Ve}:M,Ge=S(ne,["suffixIcon","itemIcon"]),Ke=n((null===(a=null==Q?void 0:Q.popup)||void 0===a?void 0:a.root)||(null===(c=null==me?void 0:me.popup)||void 0===c?void 0:c.root)||f||v,{[`${ge}-dropdown-${be}`]:"rtl"===be},p,me.root,null==Q?void 0:Q.root,xe,Ie,Ee),qe=W((e=>{var n;return null!==(n=null!=C?C:Ce)&&void 0!==n?n:e})),Xe=e.useContext(A),Ue=null!=w?w:Xe,Ye=n({[`${ge}-lg`]:"large"===qe,[`${ge}-sm`]:"small"===qe,[`${ge}-rtl`]:"rtl"===be,[`${ge}-${we}`]:$e,[`${ge}-in-form-item`]:ze},Z(ge,ke,Te),Se,pe,d,me.root,null==Q?void 0:Q.root,p,xe,Ie,Ee),Qe=e.useMemo((()=>void 0!==h?h:"rtl"===be?"bottomRight":"bottomLeft"),[h,be]),[nn]=L("SelectLike",null==De?void 0:De.zIndex);return ye(e.createElement(Je,Object.assign({ref:t,virtual:le,showSearch:se},Ge,{style:Object.assign(Object.assign(Object.assign(Object.assign({},de.root),null==Y?void 0:Y.root),ue),N),dropdownMatchSelectWidth:Me,transitionName:F(he,"slide-up",H),builtinPlacements:en(E,ce),listHeight:g,listItemHeight:ve,mode:Oe,prefixCls:ge,placement:Qe,direction:be,prefix:G,suffixIcon:Ae,menuItemSelectedIcon:Le,removeIcon:Fe,allowClear:_e,notFoundContent:We,className:Ye,getPopupContainer:m||oe,dropdownClassName:Ke,disabled:Ue,dropdownStyle:Object.assign(Object.assign({},De),{zIndex:nn}),maxCount:Re?_:void 0,tagRender:Re?V:void 0,dropdownRender:Pe,onDropdownVisibleChange:He})))},Mn=e.forwardRef(Nn),Dn=oe(Mn,"dropdownAlign");Mn.SECRET_COMBOBOX_MODE_DO_NOT_USE=Rn,Mn.Option=je,Mn.OptGroup=ze,Mn._InternalPanelDoNotUseOrYouWillBeFired=Dn;export{Ze as D,Mn as S,tn as a,rn as g,On as u};
