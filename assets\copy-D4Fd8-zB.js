const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./index-CMwI1v5w.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./MyApp-DW5WH4Ub.js"])))=>i.map(i=>d[i]);
import{aN as t}from"./index-Cak6rALw.js";async function a(t){try{return await navigator.clipboard.writeText(t),!0}catch(a){return console.log(a),!1}}async function n(n){const r=await a(n),e=(await t((async()=>{const{default:t}=await import("./index-CMwI1v5w.js");return{default:t}}),__vite__mapDeps([0,1,2]),import.meta.url)).default,{trans:o}=await t((async()=>{const{trans:t}=await import("./MyApp-DW5WH4Ub.js").then((t=>t.aW));return{trans:t}}),__vite__mapDeps([3,1,2]),import.meta.url);r?e.success({content:o({en:"Copied: ",vi:"Đã sao chép: "})+n}):e.error({content:o({en:"Failed to copy. Please use Ctrl+C",vi:"Lỗi sao chép. Vui lòng dùng Ctrl+C"})})}export{a as copy,n as copyWithMessage};
