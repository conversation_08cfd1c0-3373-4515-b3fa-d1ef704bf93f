import{r as e,G as t,m as a,L as n,M as r,J as o,O as i,ac as l,aa as d,aw as s,a6 as c,am as g}from"./index-Cak6rALw.js";import{T as b}from"./index-IxNfZAD-.js";import{a5 as p}from"./MyApp-DW5WH4Ub.js";const m=n=>{var{prefixCls:r,className:o,hoverable:i=!0}=n,l=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]])}return a}(n,["prefixCls","className","hoverable"]);const{getPrefixCls:d}=e.useContext(t),s=d("card",r),c=a(`${s}-grid`,o,{[`${s}-grid-hoverable`]:i});return e.createElement("div",Object.assign({},l,{className:c}))},$=e=>{const{antCls:t,componentCls:a,headerHeight:n,headerPadding:r,tabsMarginBottom:i}=e;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:n,marginBottom:-1,padding:`0 ${o(r)}`,color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.headerFontSize,background:e.headerBg,borderBottom:`${o(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`,borderRadius:`${o(e.borderRadiusLG)} ${o(e.borderRadiusLG)} 0 0`},l()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},d),{[`\n          > ${a}-typography,\n          > ${a}-typography-edit-content\n        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${t}-tabs-top`]:{clear:"both",marginBottom:i,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,"&-bar":{borderBottom:`${o(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`}}})},h=e=>{const{cardPaddingBase:t,colorBorderSecondary:a,cardShadow:n,lineWidth:r}=e;return{width:"33.33%",padding:t,border:0,borderRadius:0,boxShadow:`\n      ${o(r)} 0 0 0 ${a},\n      0 ${o(r)} 0 0 ${a},\n      ${o(r)} ${o(r)} 0 0 ${a},\n      ${o(r)} 0 0 0 ${a} inset,\n      0 ${o(r)} 0 0 ${a} inset;\n    `,transition:`all ${e.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:n}}},u=e=>{const{componentCls:t,iconCls:a,actionsLiMargin:n,cardActionsIconSize:r,colorBorderSecondary:i,actionsBg:d}=e;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:d,borderTop:`${o(e.lineWidth)} ${e.lineType} ${i}`,display:"flex",borderRadius:`0 0 ${o(e.borderRadiusLG)} ${o(e.borderRadiusLG)}`},l()),{"& > li":{margin:n,color:e.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:e.calc(e.cardActionsIconSize).mul(2).equal(),fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimary,transition:`color ${e.motionDurationMid}`},[`a:not(${t}-btn), > ${a}`]:{display:"inline-block",width:"100%",color:e.colorIcon,lineHeight:o(e.fontHeight),transition:`color ${e.motionDurationMid}`,"&:hover":{color:e.colorPrimary}},[`> ${a}`]:{fontSize:r,lineHeight:o(e.calc(r).mul(e.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:`${o(e.lineWidth)} ${e.lineType} ${i}`}}})},y=e=>Object.assign(Object.assign({margin:`${o(e.calc(e.marginXXS).mul(-1).equal())} 0`,display:"flex"},l()),{"&-avatar":{paddingInlineEnd:e.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:e.marginXS}},"&-title":Object.assign({color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG},d),"&-description":{color:e.colorTextDescription}}),f=e=>{const{componentCls:t,colorFillAlter:a,headerPadding:n,bodyPadding:r}=e;return{[`${t}-head`]:{padding:`0 ${o(n)}`,background:a,"&-title":{fontSize:e.fontSize}},[`${t}-body`]:{padding:`${o(e.padding)} ${o(r)}`}}},v=e=>{const{componentCls:t}=e;return{overflow:"hidden",[`${t}-body`]:{userSelect:"none"}}},S=e=>{const{componentCls:t,cardShadow:a,cardHeadPadding:n,colorBorderSecondary:r,boxShadowTertiary:d,bodyPadding:s,extraColor:c}=e;return{[t]:Object.assign(Object.assign({},i(e)),{position:"relative",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,[`&:not(${t}-bordered)`]:{boxShadow:d},[`${t}-head`]:$(e),[`${t}-extra`]:{marginInlineStart:"auto",color:c,fontWeight:"normal",fontSize:e.fontSize},[`${t}-body`]:Object.assign({padding:s,borderRadius:`0 0 ${o(e.borderRadiusLG)} ${o(e.borderRadiusLG)}`},l()),[`${t}-grid`]:h(e),[`${t}-cover`]:{"> *":{display:"block",width:"100%",borderRadius:`${o(e.borderRadiusLG)} ${o(e.borderRadiusLG)} 0 0`}},[`${t}-actions`]:u(e),[`${t}-meta`]:y(e)}),[`${t}-bordered`]:{border:`${o(e.lineWidth)} ${e.lineType} ${r}`,[`${t}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${t}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${e.motionDurationMid}, border-color ${e.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:a}},[`${t}-contain-grid`]:{borderRadius:`${o(e.borderRadiusLG)} ${o(e.borderRadiusLG)} 0 0 `,[`${t}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${t}-loading) ${t}-body`]:{marginBlockStart:e.calc(e.lineWidth).mul(-1).equal(),marginInlineStart:e.calc(e.lineWidth).mul(-1).equal(),padding:0}},[`${t}-contain-tabs`]:{[`> div${t}-head`]:{minHeight:0,[`${t}-head-title, ${t}-extra`]:{paddingTop:n}}},[`${t}-type-inner`]:f(e),[`${t}-loading`]:v(e),[`${t}-rtl`]:{direction:"rtl"}}},x=e=>{const{componentCls:t,bodyPaddingSM:a,headerPaddingSM:n,headerHeightSM:r,headerFontSizeSM:i}=e;return{[`${t}-small`]:{[`> ${t}-head`]:{minHeight:r,padding:`0 ${o(n)}`,fontSize:i,[`> ${t}-head-wrapper`]:{[`> ${t}-extra`]:{fontSize:e.fontSize}}},[`> ${t}-body`]:{padding:a}},[`${t}-small${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{paddingTop:0,display:"flex",alignItems:"center"}}}}},O=n("Card",(e=>{const t=r(e,{cardShadow:e.boxShadowCard,cardHeadPadding:e.padding,cardPaddingBase:e.paddingLG,cardActionsIconSize:e.fontSize});return[S(t),x(t)]}),(e=>{var t,a;return{headerBg:"transparent",headerFontSize:e.fontSizeLG,headerFontSizeSM:e.fontSize,headerHeight:e.fontSizeLG*e.lineHeightLG+2*e.padding,headerHeightSM:e.fontSize*e.lineHeight+2*e.paddingXS,actionsBg:e.colorBgContainer,actionsLiMargin:`${e.paddingSM}px 0`,tabsMarginBottom:-e.padding-e.lineWidth,extraColor:e.colorText,bodyPaddingSM:12,headerPaddingSM:12,bodyPadding:null!==(t=e.bodyPadding)&&void 0!==t?t:e.paddingLG,headerPadding:null!==(a=e.headerPadding)&&void 0!==a?a:e.paddingLG}}));var j=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]])}return a};const C=t=>{const{actionClasses:a,actions:n=[],actionStyle:r}=t;return e.createElement("ul",{className:a,style:r},n.map(((t,a)=>{const r=`action-${a}`;return e.createElement("li",{style:{width:100/n.length+"%"},key:r},e.createElement("span",null,t))})))};const w=e.forwardRef(((n,r)=>{const{prefixCls:o,className:i,rootClassName:l,style:d,extra:$,headStyle:h={},bodyStyle:u={},title:y,loading:f,bordered:v,variant:S,size:x,type:w,cover:P,actions:z,tabList:E,children:N,activeTabKey:B,defaultActiveTabKey:T,tabBarExtraContent:L,hoverable:M,tabProps:G={},classNames:H,styles:R}=n,I=j(n,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","variant","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:W,direction:k,card:A}=e.useContext(t),[D]=p("card",S,v),K=e=>{var t;return a(null===(t=null==A?void 0:A.classNames)||void 0===t?void 0:t[e],null==H?void 0:H[e])},q=e=>{var t;return Object.assign(Object.assign({},null===(t=null==A?void 0:A.styles)||void 0===t?void 0:t[e]),null==R?void 0:R[e])},F=e.useMemo((()=>{let t=!1;return e.Children.forEach(N,(e=>{(null==e?void 0:e.type)===m&&(t=!0)})),t}),[N]),X=W("card",o),[J,Q,U]=O(X),V=e.createElement(g,{loading:!0,active:!0,paragraph:{rows:4},title:!1},N),Y=void 0!==B,Z=Object.assign(Object.assign({},G),{[Y?"activeKey":"defaultActiveKey"]:Y?B:T,tabBarExtraContent:L});let _;const ee=s(x),te=ee&&"default"!==ee?ee:"large",ae=E?e.createElement(b,Object.assign({size:te},Z,{className:`${X}-head-tabs`,onChange:e=>{var t;null===(t=n.onTabChange)||void 0===t||t.call(n,e)},items:E.map((e=>{var{tab:t}=e,a=j(e,["tab"]);return Object.assign({label:t},a)}))})):null;if(y||$||ae){const t=a(`${X}-head`,K("header")),n=a(`${X}-head-title`,K("title")),r=a(`${X}-extra`,K("extra")),o=Object.assign(Object.assign({},h),q("header"));_=e.createElement("div",{className:t,style:o},e.createElement("div",{className:`${X}-head-wrapper`},y&&e.createElement("div",{className:n,style:q("title")},y),$&&e.createElement("div",{className:r,style:q("extra")},$)),ae)}const ne=a(`${X}-cover`,K("cover")),re=P?e.createElement("div",{className:ne,style:q("cover")},P):null,oe=a(`${X}-body`,K("body")),ie=Object.assign(Object.assign({},u),q("body")),le=e.createElement("div",{className:oe,style:ie},f?V:N),de=a(`${X}-actions`,K("actions")),se=(null==z?void 0:z.length)?e.createElement(C,{actionClasses:de,actionStyle:q("actions"),actions:z}):null,ce=c(I,["onTabChange"]),ge=a(X,null==A?void 0:A.className,{[`${X}-loading`]:f,[`${X}-bordered`]:"borderless"!==D,[`${X}-hoverable`]:M,[`${X}-contain-grid`]:F,[`${X}-contain-tabs`]:null==E?void 0:E.length,[`${X}-${ee}`]:ee,[`${X}-type-${w}`]:!!w,[`${X}-rtl`]:"rtl"===k},i,l,Q,U),be=Object.assign(Object.assign({},null==A?void 0:A.style),d);return J(e.createElement("div",Object.assign({ref:r},ce,{className:ge,style:be}),_,re,le,se))}));w.Grid=m,w.Meta=n=>{const{prefixCls:r,className:o,avatar:i,title:l,description:d}=n,s=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]])}return a}(n,["prefixCls","className","avatar","title","description"]),{getPrefixCls:c}=e.useContext(t),g=c("card",r),b=a(`${g}-meta`,o),p=i?e.createElement("div",{className:`${g}-meta-avatar`},i):null,m=l?e.createElement("div",{className:`${g}-meta-title`},l):null,$=d?e.createElement("div",{className:`${g}-meta-description`},d):null,h=m||$?e.createElement("div",{className:`${g}-meta-detail`},m,$):null;return e.createElement("div",Object.assign({},s,{className:b}),p,h)};export{w as C};
