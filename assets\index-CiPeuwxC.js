import{b0 as n}from"./index-Cak6rALw.js";function e(){return n.jsxs("div",{className:"fb-old-container",children:[n.jsx("div",{className:"fb-old-header",children:n.jsx("span",{className:"fb-old-title",children:"News Feed"})}),n.jsxs("div",{className:"fb-old-status-bar",children:[n.jsx("img",{className:"fb-old-avatar",src:"https://randomuser.me/api/portraits/men/1.jpg",alt:"avatar"}),n.jsx("input",{className:"fb-old-status-input",placeholder:"Status",disabled:!0}),n.jsx("button",{className:"fb-old-icon-btn",children:n.jsx("span",{role:"img","aria-label":"camera",children:"📷"})}),n.jsx("button",{className:"fb-old-icon-btn",children:n.jsx("span",{role:"img","aria-label":"plus",children:"➕"})}),n.jsx("button",{className:"fb-old-icon-btn",children:n.jsx("span",{role:"img","aria-label":"globe",children:"🌐"})})]}),n.jsxs("div",{className:"fb-old-grid",children:[n.jsx("div",{className:"fb-old-grid-item selected",children:n.jsx("span",{role:"img","aria-label":"news",children:"📰"})}),n.jsx("div",{className:"fb-old-grid-item",children:n.jsx("span",{role:"img","aria-label":"chat",children:"💬"})}),n.jsx("div",{className:"fb-old-grid-item",children:n.jsxs("span",{role:"img","aria-label":"add-friend",children:["👤",n.jsx("span",{className:"fb-old-plus",children:"+"})]})}),n.jsxs("div",{className:"fb-old-grid-item globe",children:[n.jsx("span",{role:"img","aria-label":"globe",children:"🌍"}),n.jsx("span",{className:"fb-old-badge",children:"3"})]}),n.jsx("div",{className:"fb-old-grid-item",children:n.jsx("span",{role:"img","aria-label":"calendar",children:"📅"})}),n.jsx("div",{className:"fb-old-grid-item",children:n.jsx("span",{role:"img","aria-label":"photos",children:"🖼️"})}),n.jsx("div",{className:"fb-old-grid-item",children:n.jsx("span",{role:"img","aria-label":"friends",children:"👥"})}),n.jsx("div",{className:"fb-old-grid-item",children:n.jsx("span",{role:"img","aria-label":"user",children:"👤"})}),n.jsx("div",{className:"fb-old-grid-item",children:n.jsxs("span",{role:"img","aria-label":"add-group",children:["👥",n.jsx("span",{className:"fb-old-plus",children:"+"})]})}),n.jsx("div",{className:"fb-old-grid-item",children:n.jsx("span",{role:"img","aria-label":"search",children:"🔍"})}),n.jsx("div",{className:"fb-old-grid-item",children:n.jsx("span",{role:"img","aria-label":"like",children:"👍"})}),n.jsx("div",{className:"fb-old-grid-item",children:n.jsx("span",{role:"img","aria-label":"contacts",children:"👥"})}),n.jsx("div",{className:"fb-old-grid-item",children:n.jsx("span",{role:"img","aria-label":"settings",children:"⚙️"})})]}),n.jsx("div",{className:"fb-old-menu-bar",children:"Menu"}),n.jsx("style",{children:"\n                .fb-old-container {\n                    width: 260px;\n                    margin: 20px auto;\n                    background: #f0f2f5;\n                    border: 1px solid #b0b0b0;\n                    border-radius: 8px;\n                    font-family: Arial, sans-serif;\n                    box-shadow: 0 2px 8px rgba(0,0,0,0.08);\n                    overflow: hidden;\n                }\n                .fb-old-header {\n                    background: #3b5998;\n                    color: #fff;\n                    text-align: center;\n                    padding: 6px 0;\n                    font-size: 18px;\n                    font-weight: bold;\n                    border-bottom: 1px solid #29487d;\n                }\n                .fb-old-status-bar {\n                    display: flex;\n                    align-items: center;\n                    padding: 6px 8px;\n                    background: #fff;\n                    border-bottom: 1px solid #e0e0e0;\n                }\n                .fb-old-avatar {\n                    width: 28px;\n                    height: 28px;\n                    border-radius: 50%;\n                    margin-right: 6px;\n                }\n                .fb-old-status-input {\n                    flex: 1;\n                    border: 1px solid #ccc;\n                    border-radius: 4px;\n                    padding: 4px 8px;\n                    font-size: 14px;\n                    margin-right: 4px;\n                    background: #f5f6fa;\n                }\n                .fb-old-icon-btn {\n                    background: none;\n                    border: none;\n                    margin: 0 2px;\n                    font-size: 18px;\n                    cursor: pointer;\n                }\n                .fb-old-grid {\n                    display: grid;\n                    grid-template-columns: repeat(3, 1fr);\n                    gap: 12px 0;\n                    padding: 16px 0 8px 0;\n                    background: #f0f2f5;\n                }\n                .fb-old-grid-item {\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n                    font-size: 28px;\n                    height: 48px;\n                    position: relative;\n                }\n                .fb-old-grid-item.selected {\n                    background: #fffbe6;\n                    border-left: 4px solid #f7c948;\n                    border-radius: 0 8px 8px 0;\n                }\n                .fb-old-plus {\n                    color: #4caf50;\n                    font-size: 16px;\n                    margin-left: -4px;\n                }\n                .fb-old-badge {\n                    position: absolute;\n                    top: 6px;\n                    right: 22px;\n                    background: #e53935;\n                    color: #fff;\n                    font-size: 12px;\n                    border-radius: 10px;\n                    padding: 0 5px;\n                    font-weight: bold;\n                }\n                .fb-old-menu-bar {\n                    background: #3b5998;\n                    color: #fff;\n                    text-align: left;\n                    padding: 8px 12px;\n                    font-size: 16px;\n                    border-top: 1px solid #29487d;\n                }\n                @media (max-width: 320px) {\n                    .fb-old-container { width: 100%; }\n                }\n            "})]})}export{e as default};
