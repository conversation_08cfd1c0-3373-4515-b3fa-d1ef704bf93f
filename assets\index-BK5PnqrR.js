import{aJ as t}from"./index-Cak6rALw.js";var e,a={};function n(){return e||(e=1,function(t){var e="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;function a(t,e){return Object.prototype.hasOwnProperty.call(t,e)}t.assign=function(t){for(var e=Array.prototype.slice.call(arguments,1);e.length;){var n=e.shift();if(n){if("object"!=typeof n)throw new TypeError(n+"must be non-object");for(var i in n)a(n,i)&&(t[i]=n[i])}}return t},t.shrinkBuf=function(t,e){return t.length===e?t:t.subarray?t.subarray(0,e):(t.length=e,t)};var n={arraySet:function(t,e,a,n,i){if(e.subarray&&t.subarray)t.set(e.subarray(a,a+n),i);else for(var r=0;r<n;r++)t[i+r]=e[a+r]},flattenChunks:function(t){var e,a,n,i,r,s;for(n=0,e=0,a=t.length;e<a;e++)n+=t[e].length;for(s=new Uint8Array(n),i=0,e=0,a=t.length;e<a;e++)r=t[e],s.set(r,i),i+=r.length;return s}},i={arraySet:function(t,e,a,n,i){for(var r=0;r<n;r++)t[i+r]=e[a+r]},flattenChunks:function(t){return[].concat.apply([],t)}};t.setTyped=function(e){e?(t.Buf8=Uint8Array,t.Buf16=Uint16Array,t.Buf32=Int32Array,t.assign(t,n)):(t.Buf8=Array,t.Buf16=Array,t.Buf32=Array,t.assign(t,i))},t.setTyped(e)}(a)),a}var i,r,s,o,h,l,d,_,f={},u={},c={};function w(){if(i)return c;i=1;var t=n();function e(t){for(var e=t.length;--e>=0;)t[e]=0}var a=256,r=286,s=30,o=15,h=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],l=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],d=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],_=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],f=new Array(576);e(f);var u=new Array(60);e(u);var w=new Array(512);e(w);var g=new Array(256);e(g);var b=new Array(29);e(b);var m,p,v,k=new Array(s);function y(t,e,a,n,i){this.static_tree=t,this.extra_bits=e,this.extra_base=a,this.elems=n,this.max_length=i,this.has_stree=t&&t.length}function x(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}function z(t){return t<256?w[t]:w[256+(t>>>7)]}function B(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255}function S(t,e,a){t.bi_valid>16-a?(t.bi_buf|=e<<t.bi_valid&65535,B(t,t.bi_buf),t.bi_buf=e>>16-t.bi_valid,t.bi_valid+=a-16):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=a)}function E(t,e,a){S(t,a[2*e],a[2*e+1])}function A(t,e){var a=0;do{a|=1&t,t>>>=1,a<<=1}while(--e>0);return a>>>1}function Z(t,e,a){var n,i,r=new Array(16),s=0;for(n=1;n<=o;n++)r[n]=s=s+a[n-1]<<1;for(i=0;i<=e;i++){var h=t[2*i+1];0!==h&&(t[2*i]=A(r[h]++,h))}}function R(t){var e;for(e=0;e<r;e++)t.dyn_ltree[2*e]=0;for(e=0;e<s;e++)t.dyn_dtree[2*e]=0;for(e=0;e<19;e++)t.bl_tree[2*e]=0;t.dyn_ltree[512]=1,t.opt_len=t.static_len=0,t.last_lit=t.matches=0}function C(t){t.bi_valid>8?B(t,t.bi_buf):t.bi_valid>0&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0}function N(t,e,a,n){var i=2*e,r=2*a;return t[i]<t[r]||t[i]===t[r]&&n[e]<=n[a]}function I(t,e,a){for(var n=t.heap[a],i=a<<1;i<=t.heap_len&&(i<t.heap_len&&N(e,t.heap[i+1],t.heap[i],t.depth)&&i++,!N(e,n,t.heap[i],t.depth));)t.heap[a]=t.heap[i],a=i,i<<=1;t.heap[a]=n}function O(t,e,n){var i,r,s,o,d=0;if(0!==t.last_lit)do{i=t.pending_buf[t.d_buf+2*d]<<8|t.pending_buf[t.d_buf+2*d+1],r=t.pending_buf[t.l_buf+d],d++,0===i?E(t,r,e):(E(t,(s=g[r])+a+1,e),0!==(o=h[s])&&S(t,r-=b[s],o),E(t,s=z(--i),n),0!==(o=l[s])&&S(t,i-=k[s],o))}while(d<t.last_lit);E(t,256,e)}function D(t,e){var a,n,i,r=e.dyn_tree,s=e.stat_desc.static_tree,h=e.stat_desc.has_stree,l=e.stat_desc.elems,d=-1;for(t.heap_len=0,t.heap_max=573,a=0;a<l;a++)0!==r[2*a]?(t.heap[++t.heap_len]=d=a,t.depth[a]=0):r[2*a+1]=0;for(;t.heap_len<2;)r[2*(i=t.heap[++t.heap_len]=d<2?++d:0)]=1,t.depth[i]=0,t.opt_len--,h&&(t.static_len-=s[2*i+1]);for(e.max_code=d,a=t.heap_len>>1;a>=1;a--)I(t,r,a);i=l;do{a=t.heap[1],t.heap[1]=t.heap[t.heap_len--],I(t,r,1),n=t.heap[1],t.heap[--t.heap_max]=a,t.heap[--t.heap_max]=n,r[2*i]=r[2*a]+r[2*n],t.depth[i]=(t.depth[a]>=t.depth[n]?t.depth[a]:t.depth[n])+1,r[2*a+1]=r[2*n+1]=i,t.heap[1]=i++,I(t,r,1)}while(t.heap_len>=2);t.heap[--t.heap_max]=t.heap[1],function(t,e){var a,n,i,r,s,h,l=e.dyn_tree,d=e.max_code,_=e.stat_desc.static_tree,f=e.stat_desc.has_stree,u=e.stat_desc.extra_bits,c=e.stat_desc.extra_base,w=e.stat_desc.max_length,g=0;for(r=0;r<=o;r++)t.bl_count[r]=0;for(l[2*t.heap[t.heap_max]+1]=0,a=t.heap_max+1;a<573;a++)(r=l[2*l[2*(n=t.heap[a])+1]+1]+1)>w&&(r=w,g++),l[2*n+1]=r,n>d||(t.bl_count[r]++,s=0,n>=c&&(s=u[n-c]),h=l[2*n],t.opt_len+=h*(r+s),f&&(t.static_len+=h*(_[2*n+1]+s)));if(0!==g){do{for(r=w-1;0===t.bl_count[r];)r--;t.bl_count[r]--,t.bl_count[r+1]+=2,t.bl_count[w]--,g-=2}while(g>0);for(r=w;0!==r;r--)for(n=t.bl_count[r];0!==n;)(i=t.heap[--a])>d||(l[2*i+1]!==r&&(t.opt_len+=(r-l[2*i+1])*l[2*i],l[2*i+1]=r),n--)}}(t,e),Z(r,d,t.bl_count)}function U(t,e,a){var n,i,r=-1,s=e[1],o=0,h=7,l=4;for(0===s&&(h=138,l=3),e[2*(a+1)+1]=65535,n=0;n<=a;n++)i=s,s=e[2*(n+1)+1],++o<h&&i===s||(o<l?t.bl_tree[2*i]+=o:0!==i?(i!==r&&t.bl_tree[2*i]++,t.bl_tree[32]++):o<=10?t.bl_tree[34]++:t.bl_tree[36]++,o=0,r=i,0===s?(h=138,l=3):i===s?(h=6,l=3):(h=7,l=4))}function T(t,e,a){var n,i,r=-1,s=e[1],o=0,h=7,l=4;for(0===s&&(h=138,l=3),n=0;n<=a;n++)if(i=s,s=e[2*(n+1)+1],!(++o<h&&i===s)){if(o<l)do{E(t,i,t.bl_tree)}while(0!==--o);else 0!==i?(i!==r&&(E(t,i,t.bl_tree),o--),E(t,16,t.bl_tree),S(t,o-3,2)):o<=10?(E(t,17,t.bl_tree),S(t,o-3,3)):(E(t,18,t.bl_tree),S(t,o-11,7));o=0,r=i,0===s?(h=138,l=3):i===s?(h=6,l=3):(h=7,l=4)}}e(k);var F=!1;function L(e,a,n,i){S(e,0+(i?1:0),3),function(e,a,n){C(e),B(e,n),B(e,~n),t.arraySet(e.pending_buf,e.window,a,n,e.pending),e.pending+=n}(e,a,n)}return c._tr_init=function(t){F||(!function(){var t,e,a,n,i,_=new Array(16);for(a=0,n=0;n<28;n++)for(b[n]=a,t=0;t<1<<h[n];t++)g[a++]=n;for(g[a-1]=n,i=0,n=0;n<16;n++)for(k[n]=i,t=0;t<1<<l[n];t++)w[i++]=n;for(i>>=7;n<s;n++)for(k[n]=i<<7,t=0;t<1<<l[n]-7;t++)w[256+i++]=n;for(e=0;e<=o;e++)_[e]=0;for(t=0;t<=143;)f[2*t+1]=8,t++,_[8]++;for(;t<=255;)f[2*t+1]=9,t++,_[9]++;for(;t<=279;)f[2*t+1]=7,t++,_[7]++;for(;t<=287;)f[2*t+1]=8,t++,_[8]++;for(Z(f,287,_),t=0;t<s;t++)u[2*t+1]=5,u[2*t]=A(t,5);m=new y(f,h,257,r,o),p=new y(u,l,0,s,o),v=new y(new Array(0),d,0,19,7)}(),F=!0),t.l_desc=new x(t.dyn_ltree,m),t.d_desc=new x(t.dyn_dtree,p),t.bl_desc=new x(t.bl_tree,v),t.bi_buf=0,t.bi_valid=0,R(t)},c._tr_stored_block=L,c._tr_flush_block=function(t,e,n,i){var r,s,o=0;t.level>0?(2===t.strm.data_type&&(t.strm.data_type=function(t){var e,n=4093624447;for(e=0;e<=31;e++,n>>>=1)if(1&n&&0!==t.dyn_ltree[2*e])return 0;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return 1;for(e=32;e<a;e++)if(0!==t.dyn_ltree[2*e])return 1;return 0}(t)),D(t,t.l_desc),D(t,t.d_desc),o=function(t){var e;for(U(t,t.dyn_ltree,t.l_desc.max_code),U(t,t.dyn_dtree,t.d_desc.max_code),D(t,t.bl_desc),e=18;e>=3&&0===t.bl_tree[2*_[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e}(t),r=t.opt_len+3+7>>>3,(s=t.static_len+3+7>>>3)<=r&&(r=s)):r=s=n+5,n+4<=r&&-1!==e?L(t,e,n,i):4===t.strategy||s===r?(S(t,2+(i?1:0),3),O(t,f,u)):(S(t,4+(i?1:0),3),function(t,e,a,n){var i;for(S(t,e-257,5),S(t,a-1,5),S(t,n-4,4),i=0;i<n;i++)S(t,t.bl_tree[2*_[i]+1],3);T(t,t.dyn_ltree,e-1),T(t,t.dyn_dtree,a-1)}(t,t.l_desc.max_code+1,t.d_desc.max_code+1,o+1),O(t,t.dyn_ltree,t.dyn_dtree)),R(t),i&&C(t)},c._tr_tally=function(t,e,n){return t.pending_buf[t.d_buf+2*t.last_lit]=e>>>8&255,t.pending_buf[t.d_buf+2*t.last_lit+1]=255&e,t.pending_buf[t.l_buf+t.last_lit]=255&n,t.last_lit++,0===e?t.dyn_ltree[2*n]++:(t.matches++,e--,t.dyn_ltree[2*(g[n]+a+1)]++,t.dyn_dtree[2*z(e)]++),t.last_lit===t.lit_bufsize-1},c._tr_align=function(t){S(t,2,3),E(t,256,f),function(t){16===t.bi_valid?(B(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):t.bi_valid>=8&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}(t)},c}function g(){if(s)return r;return s=1,r=function(t,e,a,n){for(var i=65535&t,r=t>>>16&65535,s=0;0!==a;){a-=s=a>2e3?2e3:a;do{r=r+(i=i+e[n++]|0)|0}while(--s);i%=65521,r%=65521}return i|r<<16}}function b(){if(h)return o;h=1;var t=function(){for(var t,e=[],a=0;a<256;a++){t=a;for(var n=0;n<8;n++)t=1&t?3988292384^t>>>1:t>>>1;e[a]=t}return e}();return o=function(e,a,n,i){var r=t,s=i+n;e^=-1;for(var o=i;o<s;o++)e=e>>>8^r[255&(e^a[o])];return-1^e}}function m(){return d?l:(d=1,l={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"})}function p(){if(_)return u;_=1;var t,e=n(),a=w(),i=g(),r=b(),s=m(),o=-2,h=258,l=262,d=103,f=113,c=666;function p(t,e){return t.msg=s[e],e}function v(t){return(t<<1)-(t>4?9:0)}function k(t){for(var e=t.length;--e>=0;)t[e]=0}function y(t){var a=t.state,n=a.pending;n>t.avail_out&&(n=t.avail_out),0!==n&&(e.arraySet(t.output,a.pending_buf,a.pending_out,n,t.next_out),t.next_out+=n,a.pending_out+=n,t.total_out+=n,t.avail_out-=n,a.pending-=n,0===a.pending&&(a.pending_out=0))}function x(t,e){a._tr_flush_block(t,t.block_start>=0?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,y(t.strm)}function z(t,e){t.pending_buf[t.pending++]=e}function B(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e}function S(t,e){var a,n,i=t.max_chain_length,r=t.strstart,s=t.prev_length,o=t.nice_match,d=t.strstart>t.w_size-l?t.strstart-(t.w_size-l):0,_=t.window,f=t.w_mask,u=t.prev,c=t.strstart+h,w=_[r+s-1],g=_[r+s];t.prev_length>=t.good_match&&(i>>=2),o>t.lookahead&&(o=t.lookahead);do{if(_[(a=e)+s]===g&&_[a+s-1]===w&&_[a]===_[r]&&_[++a]===_[r+1]){r+=2,a++;do{}while(_[++r]===_[++a]&&_[++r]===_[++a]&&_[++r]===_[++a]&&_[++r]===_[++a]&&_[++r]===_[++a]&&_[++r]===_[++a]&&_[++r]===_[++a]&&_[++r]===_[++a]&&r<c);if(n=h-(c-r),r=c-h,n>s){if(t.match_start=e,s=n,n>=o)break;w=_[r+s-1],g=_[r+s]}}}while((e=u[e&f])>d&&0!==--i);return s<=t.lookahead?s:t.lookahead}function E(t){var a,n,s,o,h,d,_,f,u,c,w=t.w_size;do{if(o=t.window_size-t.lookahead-t.strstart,t.strstart>=w+(w-l)){e.arraySet(t.window,t.window,w,w,0),t.match_start-=w,t.strstart-=w,t.block_start-=w,a=n=t.hash_size;do{s=t.head[--a],t.head[a]=s>=w?s-w:0}while(--n);a=n=w;do{s=t.prev[--a],t.prev[a]=s>=w?s-w:0}while(--n);o+=w}if(0===t.strm.avail_in)break;if(d=t.strm,_=t.window,f=t.strstart+t.lookahead,u=o,c=void 0,(c=d.avail_in)>u&&(c=u),n=0===c?0:(d.avail_in-=c,e.arraySet(_,d.input,d.next_in,c,f),1===d.state.wrap?d.adler=i(d.adler,_,c,f):2===d.state.wrap&&(d.adler=r(d.adler,_,c,f)),d.next_in+=c,d.total_in+=c,c),t.lookahead+=n,t.lookahead+t.insert>=3)for(h=t.strstart-t.insert,t.ins_h=t.window[h],t.ins_h=(t.ins_h<<t.hash_shift^t.window[h+1])&t.hash_mask;t.insert&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[h+3-1])&t.hash_mask,t.prev[h&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=h,h++,t.insert--,!(t.lookahead+t.insert<3)););}while(t.lookahead<l&&0!==t.strm.avail_in)}function A(t,e){for(var n,i;;){if(t.lookahead<l){if(E(t),t.lookahead<l&&0===e)return 1;if(0===t.lookahead)break}if(n=0,t.lookahead>=3&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==n&&t.strstart-n<=t.w_size-l&&(t.match_length=S(t,n)),t.match_length>=3)if(i=a._tr_tally(t,t.strstart-t.match_start,t.match_length-3),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=3){t.match_length--;do{t.strstart++,t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart}while(0!==--t.match_length);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+1])&t.hash_mask;else i=a._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(i&&(x(t,!1),0===t.strm.avail_out))return 1}return t.insert=t.strstart<2?t.strstart:2,4===e?(x(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(x(t,!1),0===t.strm.avail_out)?1:2}function Z(t,e){for(var n,i,r;;){if(t.lookahead<l){if(E(t),t.lookahead<l&&0===e)return 1;if(0===t.lookahead)break}if(n=0,t.lookahead>=3&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=2,0!==n&&t.prev_length<t.max_lazy_match&&t.strstart-n<=t.w_size-l&&(t.match_length=S(t,n),t.match_length<=5&&(1===t.strategy||3===t.match_length&&t.strstart-t.match_start>4096)&&(t.match_length=2)),t.prev_length>=3&&t.match_length<=t.prev_length){r=t.strstart+t.lookahead-3,i=a._tr_tally(t,t.strstart-1-t.prev_match,t.prev_length-3),t.lookahead-=t.prev_length-1,t.prev_length-=2;do{++t.strstart<=r&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart)}while(0!==--t.prev_length);if(t.match_available=0,t.match_length=2,t.strstart++,i&&(x(t,!1),0===t.strm.avail_out))return 1}else if(t.match_available){if((i=a._tr_tally(t,0,t.window[t.strstart-1]))&&x(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return 1}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(i=a._tr_tally(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<2?t.strstart:2,4===e?(x(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(x(t,!1),0===t.strm.avail_out)?1:2}function R(t,e,a,n,i){this.good_length=t,this.max_lazy=e,this.nice_length=a,this.max_chain=n,this.func=i}function C(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=8,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new e.Buf16(1146),this.dyn_dtree=new e.Buf16(122),this.bl_tree=new e.Buf16(78),k(this.dyn_ltree),k(this.dyn_dtree),k(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new e.Buf16(16),this.heap=new e.Buf16(573),k(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new e.Buf16(573),k(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function N(t){var e;return t&&t.state?(t.total_in=t.total_out=0,t.data_type=2,(e=t.state).pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=e.wrap?42:f,t.adler=2===e.wrap?0:1,e.last_flush=0,a._tr_init(e),0):p(t,o)}function I(e){var a,n=N(e);return 0===n&&((a=e.state).window_size=2*a.w_size,k(a.head),a.max_lazy_match=t[a.level].max_lazy,a.good_match=t[a.level].good_length,a.nice_match=t[a.level].nice_length,a.max_chain_length=t[a.level].max_chain,a.strstart=0,a.block_start=0,a.lookahead=0,a.insert=0,a.match_length=a.prev_length=2,a.match_available=0,a.ins_h=0),n}function O(t,a,n,i,r,s){if(!t)return o;var h=1;if(-1===a&&(a=6),i<0?(h=0,i=-i):i>15&&(h=2,i-=16),r<1||r>9||8!==n||i<8||i>15||a<0||a>9||s<0||s>4)return p(t,o);8===i&&(i=9);var l=new C;return t.state=l,l.strm=t,l.wrap=h,l.gzhead=null,l.w_bits=i,l.w_size=1<<l.w_bits,l.w_mask=l.w_size-1,l.hash_bits=r+7,l.hash_size=1<<l.hash_bits,l.hash_mask=l.hash_size-1,l.hash_shift=~~((l.hash_bits+3-1)/3),l.window=new e.Buf8(2*l.w_size),l.head=new e.Buf16(l.hash_size),l.prev=new e.Buf16(l.w_size),l.lit_bufsize=1<<r+6,l.pending_buf_size=4*l.lit_bufsize,l.pending_buf=new e.Buf8(l.pending_buf_size),l.d_buf=1*l.lit_bufsize,l.l_buf=3*l.lit_bufsize,l.level=a,l.strategy=s,l.method=n,I(t)}return t=[new R(0,0,0,0,(function(t,e){var a=65535;for(a>t.pending_buf_size-5&&(a=t.pending_buf_size-5);;){if(t.lookahead<=1){if(E(t),0===t.lookahead&&0===e)return 1;if(0===t.lookahead)break}t.strstart+=t.lookahead,t.lookahead=0;var n=t.block_start+a;if((0===t.strstart||t.strstart>=n)&&(t.lookahead=t.strstart-n,t.strstart=n,x(t,!1),0===t.strm.avail_out))return 1;if(t.strstart-t.block_start>=t.w_size-l&&(x(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(x(t,!0),0===t.strm.avail_out?3:4):(t.strstart>t.block_start&&(x(t,!1),t.strm.avail_out),1)})),new R(4,4,8,4,A),new R(4,5,16,8,A),new R(4,6,32,32,A),new R(4,4,16,16,Z),new R(8,16,32,32,Z),new R(8,16,128,128,Z),new R(8,32,128,256,Z),new R(32,128,258,1024,Z),new R(32,258,258,4096,Z)],u.deflateInit=function(t,e){return O(t,e,8,15,8,0)},u.deflateInit2=O,u.deflateReset=I,u.deflateResetKeep=N,u.deflateSetHeader=function(t,e){return t&&t.state?2!==t.state.wrap?o:(t.state.gzhead=e,0):o},u.deflate=function(e,n){var i,s,l,_;if(!e||!e.state||n>5||n<0)return e?p(e,o):o;if(s=e.state,!e.output||!e.input&&0!==e.avail_in||s.status===c&&4!==n)return p(e,0===e.avail_out?-5:o);if(s.strm=e,i=s.last_flush,s.last_flush=n,42===s.status)if(2===s.wrap)e.adler=0,z(s,31),z(s,139),z(s,8),s.gzhead?(z(s,(s.gzhead.text?1:0)+(s.gzhead.hcrc?2:0)+(s.gzhead.extra?4:0)+(s.gzhead.name?8:0)+(s.gzhead.comment?16:0)),z(s,255&s.gzhead.time),z(s,s.gzhead.time>>8&255),z(s,s.gzhead.time>>16&255),z(s,s.gzhead.time>>24&255),z(s,9===s.level?2:s.strategy>=2||s.level<2?4:0),z(s,255&s.gzhead.os),s.gzhead.extra&&s.gzhead.extra.length&&(z(s,255&s.gzhead.extra.length),z(s,s.gzhead.extra.length>>8&255)),s.gzhead.hcrc&&(e.adler=r(e.adler,s.pending_buf,s.pending,0)),s.gzindex=0,s.status=69):(z(s,0),z(s,0),z(s,0),z(s,0),z(s,0),z(s,9===s.level?2:s.strategy>=2||s.level<2?4:0),z(s,3),s.status=f);else{var u=8+(s.w_bits-8<<4)<<8;u|=(s.strategy>=2||s.level<2?0:s.level<6?1:6===s.level?2:3)<<6,0!==s.strstart&&(u|=32),u+=31-u%31,s.status=f,B(s,u),0!==s.strstart&&(B(s,e.adler>>>16),B(s,65535&e.adler)),e.adler=1}if(69===s.status)if(s.gzhead.extra){for(l=s.pending;s.gzindex<(65535&s.gzhead.extra.length)&&(s.pending!==s.pending_buf_size||(s.gzhead.hcrc&&s.pending>l&&(e.adler=r(e.adler,s.pending_buf,s.pending-l,l)),y(e),l=s.pending,s.pending!==s.pending_buf_size));)z(s,255&s.gzhead.extra[s.gzindex]),s.gzindex++;s.gzhead.hcrc&&s.pending>l&&(e.adler=r(e.adler,s.pending_buf,s.pending-l,l)),s.gzindex===s.gzhead.extra.length&&(s.gzindex=0,s.status=73)}else s.status=73;if(73===s.status)if(s.gzhead.name){l=s.pending;do{if(s.pending===s.pending_buf_size&&(s.gzhead.hcrc&&s.pending>l&&(e.adler=r(e.adler,s.pending_buf,s.pending-l,l)),y(e),l=s.pending,s.pending===s.pending_buf_size)){_=1;break}_=s.gzindex<s.gzhead.name.length?255&s.gzhead.name.charCodeAt(s.gzindex++):0,z(s,_)}while(0!==_);s.gzhead.hcrc&&s.pending>l&&(e.adler=r(e.adler,s.pending_buf,s.pending-l,l)),0===_&&(s.gzindex=0,s.status=91)}else s.status=91;if(91===s.status)if(s.gzhead.comment){l=s.pending;do{if(s.pending===s.pending_buf_size&&(s.gzhead.hcrc&&s.pending>l&&(e.adler=r(e.adler,s.pending_buf,s.pending-l,l)),y(e),l=s.pending,s.pending===s.pending_buf_size)){_=1;break}_=s.gzindex<s.gzhead.comment.length?255&s.gzhead.comment.charCodeAt(s.gzindex++):0,z(s,_)}while(0!==_);s.gzhead.hcrc&&s.pending>l&&(e.adler=r(e.adler,s.pending_buf,s.pending-l,l)),0===_&&(s.status=d)}else s.status=d;if(s.status===d&&(s.gzhead.hcrc?(s.pending+2>s.pending_buf_size&&y(e),s.pending+2<=s.pending_buf_size&&(z(s,255&e.adler),z(s,e.adler>>8&255),e.adler=0,s.status=f)):s.status=f),0!==s.pending){if(y(e),0===e.avail_out)return s.last_flush=-1,0}else if(0===e.avail_in&&v(n)<=v(i)&&4!==n)return p(e,-5);if(s.status===c&&0!==e.avail_in)return p(e,-5);if(0!==e.avail_in||0!==s.lookahead||0!==n&&s.status!==c){var w=2===s.strategy?function(t,e){for(var n;;){if(0===t.lookahead&&(E(t),0===t.lookahead)){if(0===e)return 1;break}if(t.match_length=0,n=a._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,n&&(x(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(x(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(x(t,!1),0===t.strm.avail_out)?1:2}(s,n):3===s.strategy?function(t,e){for(var n,i,r,s,o=t.window;;){if(t.lookahead<=h){if(E(t),t.lookahead<=h&&0===e)return 1;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=3&&t.strstart>0&&(i=o[r=t.strstart-1])===o[++r]&&i===o[++r]&&i===o[++r]){s=t.strstart+h;do{}while(i===o[++r]&&i===o[++r]&&i===o[++r]&&i===o[++r]&&i===o[++r]&&i===o[++r]&&i===o[++r]&&i===o[++r]&&r<s);t.match_length=h-(s-r),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=3?(n=a._tr_tally(t,1,t.match_length-3),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(n=a._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),n&&(x(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(x(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(x(t,!1),0===t.strm.avail_out)?1:2}(s,n):t[s.level].func(s,n);if(3!==w&&4!==w||(s.status=c),1===w||3===w)return 0===e.avail_out&&(s.last_flush=-1),0;if(2===w&&(1===n?a._tr_align(s):5!==n&&(a._tr_stored_block(s,0,0,!1),3===n&&(k(s.head),0===s.lookahead&&(s.strstart=0,s.block_start=0,s.insert=0))),y(e),0===e.avail_out))return s.last_flush=-1,0}return 4!==n?0:s.wrap<=0?1:(2===s.wrap?(z(s,255&e.adler),z(s,e.adler>>8&255),z(s,e.adler>>16&255),z(s,e.adler>>24&255),z(s,255&e.total_in),z(s,e.total_in>>8&255),z(s,e.total_in>>16&255),z(s,e.total_in>>24&255)):(B(s,e.adler>>>16),B(s,65535&e.adler)),y(e),s.wrap>0&&(s.wrap=-s.wrap),0!==s.pending?0:1)},u.deflateEnd=function(t){var e;return t&&t.state?42!==(e=t.state.status)&&69!==e&&73!==e&&91!==e&&e!==d&&e!==f&&e!==c?p(t,o):(t.state=null,e===f?p(t,-3):0):o},u.deflateSetDictionary=function(t,a){var n,r,s,h,l,d,_,f,u=a.length;if(!t||!t.state)return o;if(2===(h=(n=t.state).wrap)||1===h&&42!==n.status||n.lookahead)return o;for(1===h&&(t.adler=i(t.adler,a,u,0)),n.wrap=0,u>=n.w_size&&(0===h&&(k(n.head),n.strstart=0,n.block_start=0,n.insert=0),f=new e.Buf8(n.w_size),e.arraySet(f,a,u-n.w_size,n.w_size,0),a=f,u=n.w_size),l=t.avail_in,d=t.next_in,_=t.input,t.avail_in=u,t.next_in=0,t.input=a,E(n);n.lookahead>=3;){r=n.strstart,s=n.lookahead-2;do{n.ins_h=(n.ins_h<<n.hash_shift^n.window[r+3-1])&n.hash_mask,n.prev[r&n.w_mask]=n.head[n.ins_h],n.head[n.ins_h]=r,r++}while(--s);n.strstart=r,n.lookahead=2,E(n)}return n.strstart+=n.lookahead,n.block_start=n.strstart,n.insert=n.lookahead,n.lookahead=0,n.match_length=n.prev_length=2,n.match_available=0,t.next_in=d,t.input=_,t.avail_in=l,n.wrap=h,0},u.deflateInfo="pako deflate (from Nodeca project)",u}var v,k,y,x,z={};function B(){if(v)return z;v=1;var t=n(),e=!0,a=!0;try{String.fromCharCode.apply(null,[0])}catch(o){e=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(o){a=!1}for(var i=new t.Buf8(256),r=0;r<256;r++)i[r]=r>=252?6:r>=248?5:r>=240?4:r>=224?3:r>=192?2:1;function s(n,i){if(i<65534&&(n.subarray&&a||!n.subarray&&e))return String.fromCharCode.apply(null,t.shrinkBuf(n,i));for(var r="",s=0;s<i;s++)r+=String.fromCharCode(n[s]);return r}return i[254]=i[254]=1,z.string2buf=function(e){var a,n,i,r,s,o=e.length,h=0;for(r=0;r<o;r++)55296==(64512&(n=e.charCodeAt(r)))&&r+1<o&&56320==(64512&(i=e.charCodeAt(r+1)))&&(n=65536+(n-55296<<10)+(i-56320),r++),h+=n<128?1:n<2048?2:n<65536?3:4;for(a=new t.Buf8(h),s=0,r=0;s<h;r++)55296==(64512&(n=e.charCodeAt(r)))&&r+1<o&&56320==(64512&(i=e.charCodeAt(r+1)))&&(n=65536+(n-55296<<10)+(i-56320),r++),n<128?a[s++]=n:n<2048?(a[s++]=192|n>>>6,a[s++]=128|63&n):n<65536?(a[s++]=224|n>>>12,a[s++]=128|n>>>6&63,a[s++]=128|63&n):(a[s++]=240|n>>>18,a[s++]=128|n>>>12&63,a[s++]=128|n>>>6&63,a[s++]=128|63&n);return a},z.buf2binstring=function(t){return s(t,t.length)},z.binstring2buf=function(e){for(var a=new t.Buf8(e.length),n=0,i=a.length;n<i;n++)a[n]=e.charCodeAt(n);return a},z.buf2string=function(t,e){var a,n,r,o,h=e||t.length,l=new Array(2*h);for(n=0,a=0;a<h;)if((r=t[a++])<128)l[n++]=r;else if((o=i[r])>4)l[n++]=65533,a+=o-1;else{for(r&=2===o?31:3===o?15:7;o>1&&a<h;)r=r<<6|63&t[a++],o--;o>1?l[n++]=65533:r<65536?l[n++]=r:(r-=65536,l[n++]=55296|r>>10&1023,l[n++]=56320|1023&r)}return s(l,n)},z.utf8border=function(t,e){var a;for((e=e||t.length)>t.length&&(e=t.length),a=e-1;a>=0&&128==(192&t[a]);)a--;return a<0||0===a?e:a+i[t[a]]>e?a:e},z}function S(){if(y)return k;return y=1,k=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}}var E,A,Z,R,C,N,I,O,D,U,T,F,L={},H={};function j(){if(C)return H;C=1;var t=n(),e=g(),a=b(),i=A?E:(A=1,E=function(t,e){var a,n,i,r,s,o,h,l,d,_,f,u,c,w,g,b,m,p,v,k,y,x,z,B,S;a=t.state,n=t.next_in,B=t.input,i=n+(t.avail_in-5),r=t.next_out,S=t.output,s=r-(e-t.avail_out),o=r+(t.avail_out-257),h=a.dmax,l=a.wsize,d=a.whave,_=a.wnext,f=a.window,u=a.hold,c=a.bits,w=a.lencode,g=a.distcode,b=(1<<a.lenbits)-1,m=(1<<a.distbits)-1;t:do{c<15&&(u+=B[n++]<<c,c+=8,u+=B[n++]<<c,c+=8),p=w[u&b];e:for(;;){if(u>>>=v=p>>>24,c-=v,0==(v=p>>>16&255))S[r++]=65535&p;else{if(!(16&v)){if(64&v){if(32&v){a.mode=12;break t}t.msg="invalid literal/length code",a.mode=30;break t}p=w[(65535&p)+(u&(1<<v)-1)];continue e}for(k=65535&p,(v&=15)&&(c<v&&(u+=B[n++]<<c,c+=8),k+=u&(1<<v)-1,u>>>=v,c-=v),c<15&&(u+=B[n++]<<c,c+=8,u+=B[n++]<<c,c+=8),p=g[u&m];;){if(u>>>=v=p>>>24,c-=v,16&(v=p>>>16&255)){if(y=65535&p,c<(v&=15)&&(u+=B[n++]<<c,(c+=8)<v&&(u+=B[n++]<<c,c+=8)),(y+=u&(1<<v)-1)>h){t.msg="invalid distance too far back",a.mode=30;break t}if(u>>>=v,c-=v,y>(v=r-s)){if((v=y-v)>d&&a.sane){t.msg="invalid distance too far back",a.mode=30;break t}if(x=0,z=f,0===_){if(x+=l-v,v<k){k-=v;do{S[r++]=f[x++]}while(--v);x=r-y,z=S}}else if(_<v){if(x+=l+_-v,(v-=_)<k){k-=v;do{S[r++]=f[x++]}while(--v);if(x=0,_<k){k-=v=_;do{S[r++]=f[x++]}while(--v);x=r-y,z=S}}}else if(x+=_-v,v<k){k-=v;do{S[r++]=f[x++]}while(--v);x=r-y,z=S}for(;k>2;)S[r++]=z[x++],S[r++]=z[x++],S[r++]=z[x++],k-=3;k&&(S[r++]=z[x++],k>1&&(S[r++]=z[x++]))}else{x=r-y;do{S[r++]=S[x++],S[r++]=S[x++],S[r++]=S[x++],k-=3}while(k>2);k&&(S[r++]=S[x++],k>1&&(S[r++]=S[x++]))}break}if(64&v){t.msg="invalid distance code",a.mode=30;break t}p=g[(65535&p)+(u&(1<<v)-1)]}}break}}while(n<i&&r<o);n-=k=c>>3,u&=(1<<(c-=k<<3))-1,t.next_in=n,t.next_out=r,t.avail_in=n<i?i-n+5:5-(n-i),t.avail_out=r<o?o-r+257:257-(r-o),a.hold=u,a.bits=c}),r=function(){if(R)return Z;R=1;var t=n(),e=15,a=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],i=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],r=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],s=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];return Z=function(n,o,h,l,d,_,f,u){var c,w,g,b,m,p,v,k,y,x=u.bits,z=0,B=0,S=0,E=0,A=0,Z=0,R=0,C=0,N=0,I=0,O=null,D=0,U=new t.Buf16(16),T=new t.Buf16(16),F=null,L=0;for(z=0;z<=e;z++)U[z]=0;for(B=0;B<l;B++)U[o[h+B]]++;for(A=x,E=e;E>=1&&0===U[E];E--);if(A>E&&(A=E),0===E)return d[_++]=20971520,d[_++]=20971520,u.bits=1,0;for(S=1;S<E&&0===U[S];S++);for(A<S&&(A=S),C=1,z=1;z<=e;z++)if(C<<=1,(C-=U[z])<0)return-1;if(C>0&&(0===n||1!==E))return-1;for(T[1]=0,z=1;z<e;z++)T[z+1]=T[z]+U[z];for(B=0;B<l;B++)0!==o[h+B]&&(f[T[o[h+B]]++]=B);if(0===n?(O=F=f,p=19):1===n?(O=a,D-=257,F=i,L-=257,p=256):(O=r,F=s,p=-1),I=0,B=0,z=S,m=_,Z=A,R=0,g=-1,b=(N=1<<A)-1,1===n&&N>852||2===n&&N>592)return 1;for(;;){v=z-R,f[B]<p?(k=0,y=f[B]):f[B]>p?(k=F[L+f[B]],y=O[D+f[B]]):(k=96,y=0),c=1<<z-R,S=w=1<<Z;do{d[m+(I>>R)+(w-=c)]=v<<24|k<<16|y}while(0!==w);for(c=1<<z-1;I&c;)c>>=1;if(0!==c?(I&=c-1,I+=c):I=0,B++,0===--U[z]){if(z===E)break;z=o[h+f[B]]}if(z>A&&(I&b)!==g){for(0===R&&(R=A),m+=S,C=1<<(Z=z-R);Z+R<E&&!((C-=U[Z+R])<=0);)Z++,C<<=1;if(N+=1<<Z,1===n&&N>852||2===n&&N>592)return 1;d[g=I&b]=A<<24|Z<<16|m-_}}return 0!==I&&(d[m+I]=z-R<<24|64<<16),u.bits=A,0}}(),s=-2,o=12,h=30;function l(t){return(t>>>24&255)+(t>>>8&65280)+((65280&t)<<8)+((255&t)<<24)}function d(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new t.Buf16(320),this.work=new t.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function _(e){var a;return e&&e.state?(a=e.state,e.total_in=e.total_out=a.total=0,e.msg="",a.wrap&&(e.adler=1&a.wrap),a.mode=1,a.last=0,a.havedict=0,a.dmax=32768,a.head=null,a.hold=0,a.bits=0,a.lencode=a.lendyn=new t.Buf32(852),a.distcode=a.distdyn=new t.Buf32(592),a.sane=1,a.back=-1,0):s}function f(t){var e;return t&&t.state?((e=t.state).wsize=0,e.whave=0,e.wnext=0,_(t)):s}function u(t,e){var a,n;return t&&t.state?(n=t.state,e<0?(a=0,e=-e):(a=1+(e>>4),e<48&&(e&=15)),e&&(e<8||e>15)?s:(null!==n.window&&n.wbits!==e&&(n.window=null),n.wrap=a,n.wbits=e,f(t))):s}function c(t,e){var a,n;return t?(n=new d,t.state=n,n.window=null,0!==(a=u(t,e))&&(t.state=null),a):s}var w,m,p=!0;function v(e){if(p){var a;for(w=new t.Buf32(512),m=new t.Buf32(32),a=0;a<144;)e.lens[a++]=8;for(;a<256;)e.lens[a++]=9;for(;a<280;)e.lens[a++]=7;for(;a<288;)e.lens[a++]=8;for(r(1,e.lens,0,288,w,0,e.work,{bits:9}),a=0;a<32;)e.lens[a++]=5;r(2,e.lens,0,32,m,0,e.work,{bits:5}),p=!1}e.lencode=w,e.lenbits=9,e.distcode=m,e.distbits=5}function k(e,a,n,i){var r,s=e.state;return null===s.window&&(s.wsize=1<<s.wbits,s.wnext=0,s.whave=0,s.window=new t.Buf8(s.wsize)),i>=s.wsize?(t.arraySet(s.window,a,n-s.wsize,s.wsize,0),s.wnext=0,s.whave=s.wsize):((r=s.wsize-s.wnext)>i&&(r=i),t.arraySet(s.window,a,n-i,r,s.wnext),(i-=r)?(t.arraySet(s.window,a,n-i,i,0),s.wnext=i,s.whave=s.wsize):(s.wnext+=r,s.wnext===s.wsize&&(s.wnext=0),s.whave<s.wsize&&(s.whave+=r))),0}return H.inflateReset=f,H.inflateReset2=u,H.inflateResetKeep=_,H.inflateInit=function(t){return c(t,15)},H.inflateInit2=c,H.inflate=function(n,d){var _,f,u,c,w,g,b,m,p,y,x,z,B,S,E,A,Z,R,C,N,I,O,D,U,T=0,F=new t.Buf8(4),L=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!n||!n.state||!n.output||!n.input&&0!==n.avail_in)return s;(_=n.state).mode===o&&(_.mode=13),w=n.next_out,u=n.output,b=n.avail_out,c=n.next_in,f=n.input,g=n.avail_in,m=_.hold,p=_.bits,y=g,x=b,O=0;t:for(;;)switch(_.mode){case 1:if(0===_.wrap){_.mode=13;break}for(;p<16;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}if(2&_.wrap&&35615===m){_.check=0,F[0]=255&m,F[1]=m>>>8&255,_.check=a(_.check,F,2,0),m=0,p=0,_.mode=2;break}if(_.flags=0,_.head&&(_.head.done=!1),!(1&_.wrap)||(((255&m)<<8)+(m>>8))%31){n.msg="incorrect header check",_.mode=h;break}if(8!=(15&m)){n.msg="unknown compression method",_.mode=h;break}if(p-=4,I=8+(15&(m>>>=4)),0===_.wbits)_.wbits=I;else if(I>_.wbits){n.msg="invalid window size",_.mode=h;break}_.dmax=1<<I,n.adler=_.check=1,_.mode=512&m?10:o,m=0,p=0;break;case 2:for(;p<16;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}if(_.flags=m,8!=(255&_.flags)){n.msg="unknown compression method",_.mode=h;break}if(57344&_.flags){n.msg="unknown header flags set",_.mode=h;break}_.head&&(_.head.text=m>>8&1),512&_.flags&&(F[0]=255&m,F[1]=m>>>8&255,_.check=a(_.check,F,2,0)),m=0,p=0,_.mode=3;case 3:for(;p<32;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}_.head&&(_.head.time=m),512&_.flags&&(F[0]=255&m,F[1]=m>>>8&255,F[2]=m>>>16&255,F[3]=m>>>24&255,_.check=a(_.check,F,4,0)),m=0,p=0,_.mode=4;case 4:for(;p<16;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}_.head&&(_.head.xflags=255&m,_.head.os=m>>8),512&_.flags&&(F[0]=255&m,F[1]=m>>>8&255,_.check=a(_.check,F,2,0)),m=0,p=0,_.mode=5;case 5:if(1024&_.flags){for(;p<16;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}_.length=m,_.head&&(_.head.extra_len=m),512&_.flags&&(F[0]=255&m,F[1]=m>>>8&255,_.check=a(_.check,F,2,0)),m=0,p=0}else _.head&&(_.head.extra=null);_.mode=6;case 6:if(1024&_.flags&&((z=_.length)>g&&(z=g),z&&(_.head&&(I=_.head.extra_len-_.length,_.head.extra||(_.head.extra=new Array(_.head.extra_len)),t.arraySet(_.head.extra,f,c,z,I)),512&_.flags&&(_.check=a(_.check,f,z,c)),g-=z,c+=z,_.length-=z),_.length))break t;_.length=0,_.mode=7;case 7:if(2048&_.flags){if(0===g)break t;z=0;do{I=f[c+z++],_.head&&I&&_.length<65536&&(_.head.name+=String.fromCharCode(I))}while(I&&z<g);if(512&_.flags&&(_.check=a(_.check,f,z,c)),g-=z,c+=z,I)break t}else _.head&&(_.head.name=null);_.length=0,_.mode=8;case 8:if(4096&_.flags){if(0===g)break t;z=0;do{I=f[c+z++],_.head&&I&&_.length<65536&&(_.head.comment+=String.fromCharCode(I))}while(I&&z<g);if(512&_.flags&&(_.check=a(_.check,f,z,c)),g-=z,c+=z,I)break t}else _.head&&(_.head.comment=null);_.mode=9;case 9:if(512&_.flags){for(;p<16;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}if(m!==(65535&_.check)){n.msg="header crc mismatch",_.mode=h;break}m=0,p=0}_.head&&(_.head.hcrc=_.flags>>9&1,_.head.done=!0),n.adler=_.check=0,_.mode=o;break;case 10:for(;p<32;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}n.adler=_.check=l(m),m=0,p=0,_.mode=11;case 11:if(0===_.havedict)return n.next_out=w,n.avail_out=b,n.next_in=c,n.avail_in=g,_.hold=m,_.bits=p,2;n.adler=_.check=1,_.mode=o;case o:if(5===d||6===d)break t;case 13:if(_.last){m>>>=7&p,p-=7&p,_.mode=27;break}for(;p<3;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}switch(_.last=1&m,p-=1,3&(m>>>=1)){case 0:_.mode=14;break;case 1:if(v(_),_.mode=20,6===d){m>>>=2,p-=2;break t}break;case 2:_.mode=17;break;case 3:n.msg="invalid block type",_.mode=h}m>>>=2,p-=2;break;case 14:for(m>>>=7&p,p-=7&p;p<32;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}if((65535&m)!=(m>>>16^65535)){n.msg="invalid stored block lengths",_.mode=h;break}if(_.length=65535&m,m=0,p=0,_.mode=15,6===d)break t;case 15:_.mode=16;case 16:if(z=_.length){if(z>g&&(z=g),z>b&&(z=b),0===z)break t;t.arraySet(u,f,c,z,w),g-=z,c+=z,b-=z,w+=z,_.length-=z;break}_.mode=o;break;case 17:for(;p<14;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}if(_.nlen=257+(31&m),m>>>=5,p-=5,_.ndist=1+(31&m),m>>>=5,p-=5,_.ncode=4+(15&m),m>>>=4,p-=4,_.nlen>286||_.ndist>30){n.msg="too many length or distance symbols",_.mode=h;break}_.have=0,_.mode=18;case 18:for(;_.have<_.ncode;){for(;p<3;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}_.lens[L[_.have++]]=7&m,m>>>=3,p-=3}for(;_.have<19;)_.lens[L[_.have++]]=0;if(_.lencode=_.lendyn,_.lenbits=7,D={bits:_.lenbits},O=r(0,_.lens,0,19,_.lencode,0,_.work,D),_.lenbits=D.bits,O){n.msg="invalid code lengths set",_.mode=h;break}_.have=0,_.mode=19;case 19:for(;_.have<_.nlen+_.ndist;){for(;A=(T=_.lencode[m&(1<<_.lenbits)-1])>>>16&255,Z=65535&T,!((E=T>>>24)<=p);){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}if(Z<16)m>>>=E,p-=E,_.lens[_.have++]=Z;else{if(16===Z){for(U=E+2;p<U;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}if(m>>>=E,p-=E,0===_.have){n.msg="invalid bit length repeat",_.mode=h;break}I=_.lens[_.have-1],z=3+(3&m),m>>>=2,p-=2}else if(17===Z){for(U=E+3;p<U;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}p-=E,I=0,z=3+(7&(m>>>=E)),m>>>=3,p-=3}else{for(U=E+7;p<U;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}p-=E,I=0,z=11+(127&(m>>>=E)),m>>>=7,p-=7}if(_.have+z>_.nlen+_.ndist){n.msg="invalid bit length repeat",_.mode=h;break}for(;z--;)_.lens[_.have++]=I}}if(_.mode===h)break;if(0===_.lens[256]){n.msg="invalid code -- missing end-of-block",_.mode=h;break}if(_.lenbits=9,D={bits:_.lenbits},O=r(1,_.lens,0,_.nlen,_.lencode,0,_.work,D),_.lenbits=D.bits,O){n.msg="invalid literal/lengths set",_.mode=h;break}if(_.distbits=6,_.distcode=_.distdyn,D={bits:_.distbits},O=r(2,_.lens,_.nlen,_.ndist,_.distcode,0,_.work,D),_.distbits=D.bits,O){n.msg="invalid distances set",_.mode=h;break}if(_.mode=20,6===d)break t;case 20:_.mode=21;case 21:if(g>=6&&b>=258){n.next_out=w,n.avail_out=b,n.next_in=c,n.avail_in=g,_.hold=m,_.bits=p,i(n,x),w=n.next_out,u=n.output,b=n.avail_out,c=n.next_in,f=n.input,g=n.avail_in,m=_.hold,p=_.bits,_.mode===o&&(_.back=-1);break}for(_.back=0;A=(T=_.lencode[m&(1<<_.lenbits)-1])>>>16&255,Z=65535&T,!((E=T>>>24)<=p);){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}if(A&&!(240&A)){for(R=E,C=A,N=Z;A=(T=_.lencode[N+((m&(1<<R+C)-1)>>R)])>>>16&255,Z=65535&T,!(R+(E=T>>>24)<=p);){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}m>>>=R,p-=R,_.back+=R}if(m>>>=E,p-=E,_.back+=E,_.length=Z,0===A){_.mode=26;break}if(32&A){_.back=-1,_.mode=o;break}if(64&A){n.msg="invalid literal/length code",_.mode=h;break}_.extra=15&A,_.mode=22;case 22:if(_.extra){for(U=_.extra;p<U;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}_.length+=m&(1<<_.extra)-1,m>>>=_.extra,p-=_.extra,_.back+=_.extra}_.was=_.length,_.mode=23;case 23:for(;A=(T=_.distcode[m&(1<<_.distbits)-1])>>>16&255,Z=65535&T,!((E=T>>>24)<=p);){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}if(!(240&A)){for(R=E,C=A,N=Z;A=(T=_.distcode[N+((m&(1<<R+C)-1)>>R)])>>>16&255,Z=65535&T,!(R+(E=T>>>24)<=p);){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}m>>>=R,p-=R,_.back+=R}if(m>>>=E,p-=E,_.back+=E,64&A){n.msg="invalid distance code",_.mode=h;break}_.offset=Z,_.extra=15&A,_.mode=24;case 24:if(_.extra){for(U=_.extra;p<U;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}_.offset+=m&(1<<_.extra)-1,m>>>=_.extra,p-=_.extra,_.back+=_.extra}if(_.offset>_.dmax){n.msg="invalid distance too far back",_.mode=h;break}_.mode=25;case 25:if(0===b)break t;if(z=x-b,_.offset>z){if((z=_.offset-z)>_.whave&&_.sane){n.msg="invalid distance too far back",_.mode=h;break}z>_.wnext?(z-=_.wnext,B=_.wsize-z):B=_.wnext-z,z>_.length&&(z=_.length),S=_.window}else S=u,B=w-_.offset,z=_.length;z>b&&(z=b),b-=z,_.length-=z;do{u[w++]=S[B++]}while(--z);0===_.length&&(_.mode=21);break;case 26:if(0===b)break t;u[w++]=_.length,b--,_.mode=21;break;case 27:if(_.wrap){for(;p<32;){if(0===g)break t;g--,m|=f[c++]<<p,p+=8}if(x-=b,n.total_out+=x,_.total+=x,x&&(n.adler=_.check=_.flags?a(_.check,u,x,w-x):e(_.check,u,x,w-x)),x=b,(_.flags?m:l(m))!==_.check){n.msg="incorrect data check",_.mode=h;break}m=0,p=0}_.mode=28;case 28:if(_.wrap&&_.flags){for(;p<32;){if(0===g)break t;g--,m+=f[c++]<<p,p+=8}if(m!==(4294967295&_.total)){n.msg="incorrect length check",_.mode=h;break}m=0,p=0}_.mode=29;case 29:O=1;break t;case h:O=-3;break t;case 31:return-4;default:return s}return n.next_out=w,n.avail_out=b,n.next_in=c,n.avail_in=g,_.hold=m,_.bits=p,(_.wsize||x!==n.avail_out&&_.mode<h&&(_.mode<27||4!==d))&&k(n,n.output,n.next_out,x-n.avail_out),y-=n.avail_in,x-=n.avail_out,n.total_in+=y,n.total_out+=x,_.total+=x,_.wrap&&x&&(n.adler=_.check=_.flags?a(_.check,u,x,n.next_out-x):e(_.check,u,x,n.next_out-x)),n.data_type=_.bits+(_.last?64:0)+(_.mode===o?128:0)+(20===_.mode||15===_.mode?256:0),(0===y&&0===x||4===d)&&0===O&&(O=-5),O},H.inflateEnd=function(t){if(!t||!t.state)return s;var e=t.state;return e.window&&(e.window=null),t.state=null,0},H.inflateGetHeader=function(t,e){var a;return t&&t.state&&2&(a=t.state).wrap?(a.head=e,e.done=!1,0):s},H.inflateSetDictionary=function(t,a){var n,i=a.length;return t&&t.state?0!==(n=t.state).wrap&&11!==n.mode?s:11===n.mode&&e(1,a,i,0)!==n.check?-3:k(t,a,i,i)?(n.mode=31,-4):(n.havedict=1,0):s},H.inflateInfo="pako inflate (from Nodeca project)",H}function K(){return I?N:(I=1,N={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8})}function M(){if(U)return L;U=1;var t=j(),e=n(),a=B(),i=K(),r=m(),s=S(),o=D?O:(D=1,O=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}),h=Object.prototype.toString;function l(n){if(!(this instanceof l))return new l(n);this.options=e.assign({chunkSize:16384,windowBits:0,to:""},n||{});var d=this.options;d.raw&&d.windowBits>=0&&d.windowBits<16&&(d.windowBits=-d.windowBits,0===d.windowBits&&(d.windowBits=-15)),!(d.windowBits>=0&&d.windowBits<16)||n&&n.windowBits||(d.windowBits+=32),d.windowBits>15&&d.windowBits<48&&(15&d.windowBits||(d.windowBits|=15)),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new s,this.strm.avail_out=0;var _=t.inflateInit2(this.strm,d.windowBits);if(_!==i.Z_OK)throw new Error(r[_]);if(this.header=new o,t.inflateGetHeader(this.strm,this.header),d.dictionary&&("string"==typeof d.dictionary?d.dictionary=a.string2buf(d.dictionary):"[object ArrayBuffer]"===h.call(d.dictionary)&&(d.dictionary=new Uint8Array(d.dictionary)),d.raw&&(_=t.inflateSetDictionary(this.strm,d.dictionary))!==i.Z_OK))throw new Error(r[_])}function d(t,e){var a=new l(e);if(a.push(t,!0),a.err)throw a.msg||r[a.err];return a.result}return l.prototype.push=function(n,r){var s,o,l,d,_,f=this.strm,u=this.options.chunkSize,c=this.options.dictionary,w=!1;if(this.ended)return!1;o=r===~~r?r:!0===r?i.Z_FINISH:i.Z_NO_FLUSH,"string"==typeof n?f.input=a.binstring2buf(n):"[object ArrayBuffer]"===h.call(n)?f.input=new Uint8Array(n):f.input=n,f.next_in=0,f.avail_in=f.input.length;do{if(0===f.avail_out&&(f.output=new e.Buf8(u),f.next_out=0,f.avail_out=u),(s=t.inflate(f,i.Z_NO_FLUSH))===i.Z_NEED_DICT&&c&&(s=t.inflateSetDictionary(this.strm,c)),s===i.Z_BUF_ERROR&&!0===w&&(s=i.Z_OK,w=!1),s!==i.Z_STREAM_END&&s!==i.Z_OK)return this.onEnd(s),this.ended=!0,!1;f.next_out&&(0!==f.avail_out&&s!==i.Z_STREAM_END&&(0!==f.avail_in||o!==i.Z_FINISH&&o!==i.Z_SYNC_FLUSH)||("string"===this.options.to?(l=a.utf8border(f.output,f.next_out),d=f.next_out-l,_=a.buf2string(f.output,l),f.next_out=d,f.avail_out=u-d,d&&e.arraySet(f.output,f.output,l,d,0),this.onData(_)):this.onData(e.shrinkBuf(f.output,f.next_out)))),0===f.avail_in&&0===f.avail_out&&(w=!0)}while((f.avail_in>0||0===f.avail_out)&&s!==i.Z_STREAM_END);return s===i.Z_STREAM_END&&(o=i.Z_FINISH),o===i.Z_FINISH?(s=t.inflateEnd(this.strm),this.onEnd(s),this.ended=!0,s===i.Z_OK):o!==i.Z_SYNC_FLUSH||(this.onEnd(i.Z_OK),f.avail_out=0,!0)},l.prototype.onData=function(t){this.chunks.push(t)},l.prototype.onEnd=function(t){t===i.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=e.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},L.Inflate=l,L.inflate=d,L.inflateRaw=function(t,e){return(e=e||{}).raw=!0,d(t,e)},L.ungzip=d,L}const P=t(function(){if(F)return T;F=1;var t={};return(0,n().assign)(t,function(){if(x)return f;x=1;var t=p(),e=n(),a=B(),i=m(),r=S(),s=Object.prototype.toString;function o(n){if(!(this instanceof o))return new o(n);this.options=e.assign({level:-1,method:8,chunkSize:16384,windowBits:15,memLevel:8,strategy:0,to:""},n||{});var h=this.options;h.raw&&h.windowBits>0?h.windowBits=-h.windowBits:h.gzip&&h.windowBits>0&&h.windowBits<16&&(h.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new r,this.strm.avail_out=0;var l=t.deflateInit2(this.strm,h.level,h.method,h.windowBits,h.memLevel,h.strategy);if(0!==l)throw new Error(i[l]);if(h.header&&t.deflateSetHeader(this.strm,h.header),h.dictionary){var d;if(d="string"==typeof h.dictionary?a.string2buf(h.dictionary):"[object ArrayBuffer]"===s.call(h.dictionary)?new Uint8Array(h.dictionary):h.dictionary,0!==(l=t.deflateSetDictionary(this.strm,d)))throw new Error(i[l]);this._dict_set=!0}}function h(t,e){var a=new o(e);if(a.push(t,!0),a.err)throw a.msg||i[a.err];return a.result}return o.prototype.push=function(n,i){var r,o,h=this.strm,l=this.options.chunkSize;if(this.ended)return!1;o=i===~~i?i:!0===i?4:0,"string"==typeof n?h.input=a.string2buf(n):"[object ArrayBuffer]"===s.call(n)?h.input=new Uint8Array(n):h.input=n,h.next_in=0,h.avail_in=h.input.length;do{if(0===h.avail_out&&(h.output=new e.Buf8(l),h.next_out=0,h.avail_out=l),1!==(r=t.deflate(h,o))&&0!==r)return this.onEnd(r),this.ended=!0,!1;0!==h.avail_out&&(0!==h.avail_in||4!==o&&2!==o)||("string"===this.options.to?this.onData(a.buf2binstring(e.shrinkBuf(h.output,h.next_out))):this.onData(e.shrinkBuf(h.output,h.next_out)))}while((h.avail_in>0||0===h.avail_out)&&1!==r);return 4===o?(r=t.deflateEnd(this.strm),this.onEnd(r),this.ended=!0,0===r):2!==o||(this.onEnd(0),h.avail_out=0,!0)},o.prototype.onData=function(t){this.chunks.push(t)},o.prototype.onEnd=function(t){0===t&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=e.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},f.Deflate=o,f.deflate=h,f.deflateRaw=function(t,e){return(e=e||{}).raw=!0,h(t,e)},f.gzip=function(t,e){return(e=e||{}).gzip=!0,h(t,e)},f}(),M(),K()),T=t}());export{P as p};
