const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./GoogleTranslator-tjINXBj0.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./MyApp-DW5WH4Ub.js","./MySwal-8JMBfvUJ.js","./sweetalert2.esm.all-BZxvatOx.js"])))=>i.map(i=>d[i]);
import{aT as A,r as e,b0 as o,b5 as l,aR as d,aN as a,b1 as s}from"./index-Cak6rALw.js";import{u as n,S as t,t as i,C as H}from"./MyApp-DW5WH4Ub.js";import{D as c}from"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";const r=s((()=>a((()=>import("./GoogleTranslator-tjINXBj0.js")),__vite__mapDeps([0,1,2,3]),import.meta.url)),{fallback:H}),j={[d.vi]:"Tiếng Việt",[d.en]:"English"},E={[d.vi]:"data:image/png;base64,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",[d.en]:"data:image/png;base64,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"};function g(){const{notification:s}=A.useApp(),{language:H,setLanguage:g}=n();e.useEffect((()=>{if(!H){const A="fb_aio_select_language";s.warning({key:A,message:"Select language - Chọn ngôn ngữ",description:o.jsx(t.Compact,{children:Object.entries(j).map((([e,d])=>o.jsxs(l,{onClick:()=>{s.destroy(A),g(e),i("LanguagePicker:handleInitLanguage:"+e)},children:[o.jsx("img",{src:E[e],alt:e,style:{width:20,height:20}}),d]},e)))}),placement:"topRight",duration:0})}}),[H]);return o.jsx(c,{menu:{selectable:!0,items:[...Object.entries(j).map((([A,e])=>({key:A,label:e,icon:o.jsx("img",{src:E[A],alt:A,style:{width:20,height:20}})}))),{key:"other",label:"🌏 Other..."}],defaultSelectedKeys:[H||d.vi],onClick:async A=>{if(A.key!==H){if("other"===A.key){(await a((async()=>{const{default:A}=await import("./MySwal-8JMBfvUJ.js");return{default:A}}),__vite__mapDeps([4,5,1,2]),import.meta.url)).default.fire({title:"Select language",html:o.jsx(r,{})})}else g(A.key);i("LanguagePicker:handleChangeLanguage:"+A.key)}}},arrow:!0,placement:"bottom",children:o.jsx(l,{icon:o.jsx("i",{className:"fa-solid fa-earth-americas"})})})}export{g as default};
