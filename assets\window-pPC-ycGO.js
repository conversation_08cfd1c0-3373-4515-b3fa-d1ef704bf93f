import{v as t,aH as e}from"./MyApp-DW5WH4Ub.js";import"./index-Cak6rALw.js";function a({win:t=window,fnPath:a="",params:n=[],origin:o="",timeout:r=1e5,checkAbort:i=()=>!1}){return new Promise(((l,s)=>{const d=e(),c=new AbortController;let u=r;const w=setInterval((()=>{u-=1e3,(u<=0||(null==i?void 0:i()))&&(c.abort(),clearInterval(w),s(new Error("timeout")))}),1e3);window.addEventListener("message",(t=>{var e,a;(null==(e=t.data)?void 0:e.uuid)===d&&(clearInterval(w),l(null==(a=t.data)?void 0:a.data),c.abort())}),{signal:c.signal}),t.postMessage({from:"fbaio",uuid:d,origin:o,fnPath:a,params:n},"*")}))}async function n(e,n=!0,o=""){for(await t(500);;){let i;try{i=await a({win:e,fnPath:"window.document.readyState",timeout:1e3,origin:o})}catch(r){console.log(r)}if("complete"===i||"interactive"===i)return!0;if(!e||n&&e.closed)return!1;await t(1e3)}}export{a as runFnInWin,n as waitWinForReady};
