const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./VideoWithMuted-CYlwayKv.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./AudioWithMuted-Cry1ElD_.js"])))=>i.map(i=>d[i]);
import{c9 as e,ca as s,cb as i,cc as o,aJ as t,r,aN as n,bi as a,b0 as l,cd as m,b5 as d,bg as _,b1 as u}from"./index-Cak6rALw.js";import{S as j}from"./Screen-Buq7HJpK.js";import c from"./useCacheState-CAnxkewm.js";import{s as p}from"./shuffle-BAGnj1ei.js";import{d as h,u as g,t as E,S as f,ah as x,B as v,c as T,b as P,I as y}from"./MyApp-DW5WH4Ub.js";import L from"./Collection-BYcChfHL.js";import{S as I}from"./index-C5gzSBcY.js";import{D as O}from"./index-CC5caqt_.js";import{E as A}from"./index-SRy1SMeO.js";import{I as D}from"./index-CDSnY0KE.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./_copyArray-_bDJu6nu.js";import"./index-ByRdMNW-.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-PbLYNhdQ.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./index-jrOnBmdW.js";import"./Pagination-2X6SDgot.js";import"./List-B6ZMXgC_.js";import"./DownOutlined-B-JcS-29.js";import"./addEventListener-C4tIKh7N.js";var R,V,k,b,w,M,N,S,C,F,H,z,W,$,q,B,G,K,J,U;function Q(){if(S)return N;S=1;var e=V?R:(V=1,R=function(e,s,i,o){for(var t=e.length,r=i+(o?1:-1);o?r--:++r<t;)if(s(e[r],r,e))return r;return-1}),s=b?k:(b=1,k=function(e){return e!=e}),i=M?w:(M=1,w=function(e,s,i){for(var o=i-1,t=e.length;++o<t;)if(e[o]===s)return o;return-1});return N=function(o,t,r){return t==t?i(o,t,r):e(o,s,r)}}function X(){if(B)return q;B=1;var i=e(),o=$?W:($=1,W=function(){}),t=s(),r=i&&1/t(new i([,-0]))[1]==1/0?function(e){return new i(e)}:o;return q=r}function Y(){if(K)return G;K=1;var e=i(),t=function(){if(F)return C;F=1;var e=Q();return C=function(s,i){return!(null==s||!s.length)&&e(s,i,0)>-1}}(),r=z?H:(z=1,H=function(e,s,i){for(var o=-1,t=null==e?0:e.length;++o<t;)if(i(s,e[o]))return!0;return!1}),n=o(),a=X(),l=s();return G=function(s,i,o){var m=-1,d=t,_=s.length,u=!0,j=[],c=j;if(o)u=!1,d=r;else if(_>=200){var p=i?null:a(s);if(p)return l(p);u=!1,d=n,c=new e}else c=i?[]:j;e:for(;++m<_;){var h=s[m],g=i?i(h):h;if(h=o||0!==h?h:0,u&&g==g){for(var E=c.length;E--;)if(c[E]===g)continue e;i&&c.push(g),j.push(h)}else d(c,g,o)||(c!==j&&c.push(g),j.push(h))}return j}}const Z=t(function(){if(U)return J;U=1;var e=Y();return J=function(s){return s&&s.length?e(s):[]}}()),ee=u((()=>n((()=>import("./VideoWithMuted-CYlwayKv.js")),__vite__mapDeps([0,1,2]),import.meta.url)),{fallback:y}),se=u((()=>n((()=>import("./AudioWithMuted-Cry1ElD_.js")),__vite__mapDeps([3,1,2]),import.meta.url)),{fallback:y}),ie=["artstation","girl","girls","girlSexy","anime","meme","cosplay","waifu","lolii","phongcanh","wibu","ausand","ig","gaiIG","naughty","gentle","du","donate","vdchill","xoa","spar","hug","creepy","vddoraemon","puch","kiss","slap","vdtet","domixi","cosplaygenshin","itachi","jimmy","vdgai","linhngocdam","autosend","jack3cu","gura","videochill","APIsKem","saumui","couple","khanhhuyen","lebong","videogaixinh","vdanime","ngoctrinh","man","chitanda","nhacmp3"];function oe(){var e,s;const{message:i}=h(),{ti:o}=g(),[t,u]=c("TinderNew.category",ie[0],!0),[y,D]=c("TinderNew.mediaStacks",{}),[R,V]=c("Tinder.count",{skip:0,like:0},!0),[k,b]=c("Tinder.lovedUrls",[],!0),[w,M]=r.useState(!1),[N,S]=r.useState(new Set);r.useEffect((()=>{E("Tinder.onLoad")}),[]),r.useEffect((()=>{y[t]||(M(!0),((e,s,i)=>{const o=e[s];return o?"function"==typeof o?o():Promise.resolve(o):new Promise(((e,o)=>{("function"==typeof queueMicrotask?queueMicrotask:setTimeout)(o.bind(null,new Error("Unknown variable dynamic import: "+s+(s.split("/").length!==i?". Note that variables only represent file names one level deep.":""))))}))})(Object.assign({"../../assets/json/img/APIsKem.json":()=>n((()=>import("./APIsKem-DFAVi_5O.js")),[],import.meta.url),"../../assets/json/img/anime.json":()=>n((()=>import("./anime-DM8wUqSD.js")),[],import.meta.url),"../../assets/json/img/artstation.json":()=>n((()=>import("./artstation-MjJCVLdZ.js")),[],import.meta.url),"../../assets/json/img/ausand.json":()=>n((()=>import("./ausand-BnVZRE9R.js")),[],import.meta.url),"../../assets/json/img/autosend.json":()=>n((()=>import("./autosend-C98BDuJg.js")),[],import.meta.url),"../../assets/json/img/chitanda.json":()=>n((()=>import("./chitanda-C4VBzwhJ.js")),[],import.meta.url),"../../assets/json/img/cosplay.json":()=>n((()=>import("./cosplay-DujLTsCz.js")),[],import.meta.url),"../../assets/json/img/cosplaygenshin.json":()=>n((()=>import("./cosplaygenshin-Ca4p8Ake.js")),[],import.meta.url),"../../assets/json/img/couple.json":()=>n((()=>import("./couple-BxOM_kSh.js")),[],import.meta.url),"../../assets/json/img/creepy.json":()=>n((()=>import("./creepy-Ds19T0ez.js")),[],import.meta.url),"../../assets/json/img/domixi.json":()=>n((()=>import("./domixi-CDmSaTWS.js")),[],import.meta.url),"../../assets/json/img/donate.json":()=>n((()=>import("./donate-DHyNNudV.js")),[],import.meta.url),"../../assets/json/img/du.json":()=>n((()=>import("./du-DUAy_v35.js")),[],import.meta.url),"../../assets/json/img/gaiIG.json":()=>n((()=>import("./gaiIG-CeCsJgRZ.js")),[],import.meta.url),"../../assets/json/img/gentle.json":()=>n((()=>import("./gentle-R-s-WnGS.js")),[],import.meta.url),"../../assets/json/img/girl.json":()=>n((()=>import("./girl-AEcjtsCD.js")),[],import.meta.url),"../../assets/json/img/girlSexy.json":()=>n((()=>import("./girlSexy-DFAjvpkD.js")),[],import.meta.url),"../../assets/json/img/girls.json":()=>n((()=>import("./girls-2INL9jyL.js")),[],import.meta.url),"../../assets/json/img/gura.json":()=>n((()=>import("./gura-DWWyYfb2.js")),[],import.meta.url),"../../assets/json/img/help.json":()=>n((()=>import("./help-C1oz5beS.js")),[],import.meta.url),"../../assets/json/img/hug.json":()=>n((()=>import("./hug-CD6hRunE.js")),[],import.meta.url),"../../assets/json/img/ig.json":()=>n((()=>import("./ig-CeCsJgRZ.js")),[],import.meta.url),"../../assets/json/img/itachi.json":()=>n((()=>import("./itachi-SaAfTYc6.js")),[],import.meta.url),"../../assets/json/img/jack3cu.json":()=>n((()=>import("./jack3cu-D8WUrHoa.js")),[],import.meta.url),"../../assets/json/img/jimmy.json":()=>n((()=>import("./jimmy-fBcePqWQ.js")),[],import.meta.url),"../../assets/json/img/khanhhuyen.json":()=>n((()=>import("./khanhhuyen-CloNkM9d.js")),[],import.meta.url),"../../assets/json/img/kiss.json":()=>n((()=>import("./kiss-DRx_mTbM.js")),[],import.meta.url),"../../assets/json/img/lebong.json":()=>n((()=>import("./lebong-Bd_6j0b3.js")),[],import.meta.url),"../../assets/json/img/linhngocdam.json":()=>n((()=>import("./linhngocdam-DCMMjih6.js")),[],import.meta.url),"../../assets/json/img/lolii.json":()=>n((()=>import("./lolii-tiadadMi.js")),[],import.meta.url),"../../assets/json/img/man.json":()=>n((()=>import("./man-DDM6aXCQ.js")),[],import.meta.url),"../../assets/json/img/meme.json":()=>n((()=>import("./meme-BYrSPw85.js")),[],import.meta.url),"../../assets/json/img/naughty.json":()=>n((()=>import("./naughty-D8pkLkiX.js")),[],import.meta.url),"../../assets/json/img/ngoctrinh.json":()=>n((()=>import("./ngoctrinh-BgYDOZu0.js")),[],import.meta.url),"../../assets/json/img/nhacmp3.json":()=>n((()=>import("./nhacmp3-BQvD_Mro.js")),[],import.meta.url),"../../assets/json/img/nude.json":()=>n((()=>import("./nude-CBT8PlrJ.js")),[],import.meta.url),"../../assets/json/img/phongcanh.json":()=>n((()=>import("./phongcanh-BqlBA_Kk.js")),[],import.meta.url),"../../assets/json/img/puch.json":()=>n((()=>import("./puch-CDPKKJC1.js")),[],import.meta.url),"../../assets/json/img/saumui.json":()=>n((()=>import("./saumui-CE5CRqF-.js")),[],import.meta.url),"../../assets/json/img/slap.json":()=>n((()=>import("./slap-DecAk_eL.js")),[],import.meta.url),"../../assets/json/img/spar.json":()=>n((()=>import("./spar-DGQJfWSM.js")),[],import.meta.url),"../../assets/json/img/vdanime.json":()=>n((()=>import("./vdanime-BR_0z3OY.js")),[],import.meta.url),"../../assets/json/img/vdchill.json":()=>n((()=>import("./vdchill-iZ3k91tm.js")),[],import.meta.url),"../../assets/json/img/vddoraemon.json":()=>n((()=>import("./vddoraemon-DfbLDw3h.js")),[],import.meta.url),"../../assets/json/img/vdgai.json":()=>n((()=>import("./vdgai-DebIhX-o.js")),[],import.meta.url),"../../assets/json/img/vdtet.json":()=>n((()=>import("./vdtet-C0-eAh7Q.js")),[],import.meta.url),"../../assets/json/img/videochill.json":()=>n((()=>import("./videochill-DFAVi_5O.js")),[],import.meta.url),"../../assets/json/img/videogaixinh.json":()=>n((()=>import("./videogaixinh-BqS_oyz-.js")),[],import.meta.url),"../../assets/json/img/waifu.json":()=>n((()=>import("./waifu-CTGaoqkO.js")),[],import.meta.url),"../../assets/json/img/wibu.json":()=>n((()=>import("./wibu-CaLuXU3x.js")),[],import.meta.url),"../../assets/json/img/xoa.json":()=>n((()=>import("./xoa-njJXdVtL.js")),[],import.meta.url)}),`../../assets/json/img/${t}.json`,6).then((e=>{E("Tinder.loadCategory:"+t);const s=Z(p(e.default));D(a((e=>{e[t]={urls:s,loadedMedia:[],index:0}})))})).catch((e=>{console.error("Failed to load category:",t,e),i.error(`Failed to load category: ${t}`)})).finally((()=>M(!1))))}),[t,y]);const C=r.useRef({});r.useEffect((()=>{const e=y[t];if(!e)return;const s=e.loadedMedia.length,i=Math.min(e.index+5,e.urls.length);for(let o=s;o<i;o++){const s=e.urls[o];!s||C.current[s]||e.loadedMedia.includes(s)||(C.current[s]=!0,S((e=>(e.add(s),e))),ne(s).then((e=>{e||console.error("Failed to preload media:",s),D(a((i=>(e?i[t].loadedMedia.push(s):i[t].urls=i[t].urls.filter((e=>e!==s)),i))))})).finally((()=>{C.current[s]=!1,S((e=>(e.delete(s),e)))})))}}),[t,y]);const F=()=>{D(a((e=>{const s=e[t].urls.length,i=e[t].index;e[t].index=(i+1)%s})))},H=(e,s)=>l.jsx(f,{wrap:!0,children:s.loadedMedia.map(((o,t)=>{const r=t===s.index,n=k.includes(o);return l.jsxs("div",{style:{border:r?"2px solid red":"none",borderRadius:10,padding:2,position:"relative"},className:"show-on-hover-trigger",children:[l.jsx(ae,{url:o,onLoad:()=>{},onError:()=>{i.error(`Failed to load media: ${o}`)},maxWidth:100,maxHeight:100,autoPlay:!1}),l.jsxs(f,{size:3,style:{position:"absolute",bottom:5,right:5},children:[l.jsx(d,{danger:!0,icon:l.jsx("i",{className:"fa-solid fa-trash"}),className:"show-on-hover-item",onClick:()=>((e,s)=>{D(a((i=>{i[e].urls=i[e].urls.filter((e=>e!==s)),i[e].loadedMedia=i[e].loadedMedia.filter((e=>e!==s))})))})(e,o)}),l.jsx(d,{danger:n,icon:l.jsx("i",{className:"fa-solid fa-heart"}),onClick:()=>(e=>{E("Tinder.toggleLoved"),b((s=>s.includes(e)?s.filter((s=>s!==e)):[...s,e]))})(o),className:n?"":"show-on-hover-item"})]})]},o)}))},e),z=y[t],W=(null==(e=null==z?void 0:z.loadedMedia)?void 0:e.length)>0,$=null==(s=null==z?void 0:z.loadedMedia)?void 0:s[null==z?void 0:z.index],q=!$||N.has($);return l.jsx(j,{title:l.jsxs(l.Fragment,{children:["Tinder mini ",l.jsx("i",{className:"fa-solid fa-heart",style:{color:"red"}})]}),mode:"center",children:l.jsxs(f,{direction:"vertical",align:"center",children:[l.jsx(I,{options:ie.map((e=>{var s,i;return{label:e+(y[e]?" ("+(null==(i=null==(s=y[e])?void 0:s.urls)?void 0:i.length)+")":""),value:e}})),value:t,onChange:u,showSearch:!0,popupMatchSelectWidth:!1}),W&&!q?l.jsx(ae,{url:$,maxWidth:"95vw",maxHeight:"75vh",resizeMode:"contain",fixedSize:!1}):l.jsxs(l.Fragment,{children:[l.jsx(m,{active:!0}),l.jsx(x,{size:"default"})]}),l.jsxs(f,{size:"middle",style:{justifyContent:"center"},children:[l.jsx(v,{count:R.skip,color:"gray",children:l.jsx(d,{icon:l.jsx("i",{className:"fa-solid fa-xmark fa-lg"}),size:"large",onClick:()=>{E("Tinder.skip"),F(),V(a((e=>{e.skip=e.skip+1})))},disabled:w,children:o({en:"Skip",vi:"Bỏ qua"})})}),l.jsx(v,{count:R.like,color:"pink",children:l.jsx(d,{type:"primary",danger:!0,icon:l.jsx("i",{className:"fa-solid fa-heart fa-lg"}),size:"large",onClick:()=>{E("Tinder.love"),F(),b((e=>[...e.filter((e=>e!==$)),$])),V(a((e=>{e.like=e.like+1})))},disabled:w||q,children:o({en:"Love",vi:"Thích"})})})]}),(R.skip>0||R.like>0)&&l.jsx(d,{type:"text",onClick:()=>{E("Tinder.reset"),V({skip:0,like:0})},icon:l.jsx("i",{className:"fa-solid fa-rotate-left"}),children:o({en:"Reset count",vi:"Đếm lại"})}),l.jsx(O,{}),l.jsxs(_,{bordered:!1,style:{minWidth:300},destroyOnHidden:!0,children:[l.jsx(_.Panel,{header:l.jsxs(T.Text,{children:[o({en:"Your Love",vi:"Đã thích"})," ",l.jsx(P,{color:"pink",children:null==k?void 0:k.length})]}),children:(null==k?void 0:k.length)>0?l.jsx(L,{collectionName:"MiniTinder",renderItem:e=>l.jsxs("div",{style:{position:"relative",padding:5},className:"show-on-hover-trigger",children:[l.jsx(ae,{url:e,maxWidth:120,maxHeight:120},e),l.jsx(d,{className:"show-on-hover-item",icon:l.jsx("i",{className:"fa-solid fa-close"}),onClick:()=>{b((s=>s.filter((s=>s!==e))))},style:{position:"absolute",bottom:5,right:5}})]}),initialData:k,rowKey:e=>e,stop:!0,downloadItem:(e,s)=>({url:e,name:`TinderSaved-${s}.${re(e)}`}),headerButtons:()=>[l.jsx(d,{danger:!0,icon:l.jsx("i",{className:"fa-solid fa-trash"}),onClick:()=>b([]),children:o({en:"Clear",vi:"Xóa"})},"clear")]}):l.jsx(A,{description:"No love yet"})},"loved"),l.jsx(_.Panel,{header:l.jsxs(T.Text,{children:[o({en:"History",vi:"Lịch sử"})," ",l.jsx(P,{color:"blue",children:Object.keys(y).reduce(((e,s)=>{var i,o;return e+((null==(o=null==(i=null==y?void 0:y[s])?void 0:i.loadedMedia)?void 0:o.length)??0)}),0)})]}),children:l.jsx(_,{bordered:!1,destroyOnHidden:!0,children:Object.keys(y).length>0?Object.entries(y).map((([e,s])=>l.jsx(_.Panel,{header:l.jsxs(f,{align:"center",style:{justifyContent:"space-between",width:"100%"},children:[l.jsxs(T.Text,{children:[e," - ",s.loadedMedia.length,"/",s.urls.length," "]}),l.jsx(d,{icon:l.jsx("i",{className:"fa-solid fa-close"}),onClick:()=>{D(a((s=>(delete s[e],s))))}})]}),children:H(e,s)},e))):l.jsx(A,{description:"No history yet"})})},"a")]})]})})}function te(e){const s=re(e);return"mp4"===s||"webm"===s?"video":"mp3"===s||"wav"===s?"audio":"image"}function re(e){var s;return(null==(s=e.split(".").pop())?void 0:s.toLowerCase())??".jpg"}function ne(e){return new Promise(((s,i)=>{switch(te(e)){case"image":{const i=new Image;i.onload=()=>{s(!0)},i.onerror=()=>{console.error("Failed to preload image:",e),s(!1)},i.src=e;break}case"video":{const i=document.createElement("video");i.onloadeddata=()=>{s(!0)},i.onerror=()=>{console.error("Failed to preload video:",e),s(!1)},i.src=e;break}case"audio":{const i=new Audio;i.oncanplaythrough=()=>{s(!0)},i.onerror=()=>{console.error("Failed to preload audio:",e),s(!1)},i.src=e;break}default:s(!1)}}))}function ae({url:e,onLoad:s=()=>{},onError:i=()=>{},maxWidth:o=500,maxHeight:t=500,autoPlay:r=!0,resizeMode:n="cover",fixedSize:a=!0}){switch(te(e)){case"video":return l.jsx(ee,{src:e,controls:!0,style:{maxWidth:o,maxHeight:t,borderRadius:10},onLoadedData:s,onError:i,autoPlay:r});case"audio":return l.jsx("div",{style:{width:o,padding:20,background:"#f0f0f0",borderRadius:10},children:l.jsx(se,{src:e,controls:!0,style:{width:"100%"},onLoadedData:s,onError:i,autoPlay:r})});default:return l.jsx(D,{src:e,style:{...a?{width:o,height:t}:{maxWidth:o,maxHeight:t},objectFit:n,borderRadius:10,cursor:"pointer"},onLoad:s,onError:i,preview:{mask:null}})}}export{oe as default};
