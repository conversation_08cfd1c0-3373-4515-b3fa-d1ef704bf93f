const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./MySwal-8JMBfvUJ.js","./sweetalert2.esm.all-BZxvatOx.js","./index-Cak6rALw.js","./index-DIX6R8z-.css"])))=>i.map(i=>d[i]);
import{r as e,b0 as s,b5 as n,aN as t}from"./index-Cak6rALw.js";import{u as o,d as c,a,S as i,A as r,c as l,aD as h,t as k,aC as d}from"./MyApp-DW5WH4Ub.js";import{S as m}from"./Screen-Buq7HJpK.js";import{D as u}from"./index-CC5caqt_.js";import{I as g}from"./index-Bg865k-U.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";function p(){const{ti:p}=o(),{message:j,notification:x}=c(),{profile:b}=a(),y=e.useRef("");return s.jsxs(m,{title:p({en:"Get access token",vi:"Lấy access token"}),mode:"center",children:[s.jsxs(i,{direction:"vertical",align:"center",children:[s.jsx(r,{showIcon:!0,type:"info",message:s.jsxs(s.Fragment,{children:[p({en:"Access token is a secret key, that allows you to access Facebook APIs",vi:"Access token là một khóa bí mật, cho phép bạn truy cập vào các API của Facebook"})," ",s.jsxs("a",{href:"https://developers.facebook.com/docs/facebook-login/guides/access-tokens",target:"_blank",children:[p({en:"Learn more",vi:"Tìm hiểu thêm"})," ",s.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})]})]})}),s.jsx(r,{type:"success",message:s.jsx(l.Text,{children:p({en:s.jsxs(s.Fragment,{children:["🔒 FB AIO helps you get your Facebook access token"," ",s.jsx("u",{children:"securely"}),".",s.jsx("br",{}),"📌 Your token is stored ",s.jsx("u",{children:"only on your device"}),", never sent anywhere.",s.jsx("br",{}),"✅ No server, no tracking, no data collection — 100% private."]}),vi:s.jsxs(s.Fragment,{children:["🔒 FB AIO giúp bạn lấy token Facebook một cách"," ",s.jsx("u",{children:"an toàn"}),".",s.jsx("br",{}),"📌 Token của bạn chỉ được ",s.jsx("u",{children:"lưu trên thiết bị"}),", không gửi đi đâu cả.",s.jsx("br",{}),"✅ Không server, không theo dõi, không thu thập dữ liệu — ",s.jsx("u",{children:"bảo mật tuyệt đối"}),"."]})})})}),s.jsx(r,{showIcon:!0,type:"warning",message:p({en:"Don't share your token with anyone. It can lead to account loss.",vi:"Đừng chia sẻ token của bạn với bất kỳ ai. Có thể dẫn tới mất tài khoản."})}),!b&&s.jsx(r,{showIcon:!0,type:"error",message:p({en:"You are not logged in Facebook. Please login to get your access token.",vi:"Bạn chưa đăng nhập Facebook. Vui lòng đăng nhập để lấy token."})})]}),s.jsx(u,{}),s.jsx(i,{direction:"vertical",children:Object.entries(h).map((([e,s])=>({key:s,label:"🔑 "+s}))).map((e=>s.jsx(n,{onClick:()=>(async e=>{const s="GetAccessToken:"+e;k(s),j.loading({key:s,content:`Getting access token ${e}...`,duration:0});const n=await d(e,!0,!0);n?(j.destroy(s),x.open({type:"success",message:`Access token ${e}`,description:n})):j.error({key:s,content:`Cannot get access token ${e}`})})(e.key),disabled:!b,children:e.label},e.key)))}),s.jsx(u,{}),s.jsxs(n,{onClick:async()=>{k("GetAccessToken:checkToken");const e=(await t((async()=>{const{default:e}=await import("./MySwal-8JMBfvUJ.js");return{default:e}}),__vite__mapDeps([0,1,2,3]),import.meta.url)).default;(await e.fire({title:p({en:"Check access token",vi:"Kiểm tra access token"}),showCancelButton:!0,html:s.jsxs(i,{direction:"vertical",size:16,children:[s.jsxs("ul",{style:{textAlign:"left"},children:[s.jsxs("li",{children:["✅"," ",p({en:"Check token validity",vi:"Kiểm tra token hợp lệ"})]}),s.jsxs("li",{children:["📝"," ",p({en:"Check token permissions",vi:"Kiểm tra quyền token"})]}),s.jsxs("li",{children:["⏰"," ",p({en:"Check token expiration time",vi:"Kiểm tra thời gian hết hạn token"})]}),s.jsxs("li",{children:["🛡️"," ",p({en:s.jsxs(s.Fragment,{children:["Using"," ",s.jsx("a",{href:"https://developers.facebook.com/tools/debug/accesstoken",target:"_blank",children:"Facebook Debugger"}),", your token is safe."]}),vi:s.jsxs(s.Fragment,{children:["Sử dụng"," ",s.jsx("a",{href:"https://developers.facebook.com/tools/debug/accesstoken",target:"_blank",children:"Trình gỡ lỗi Facebook"}),", không lộ token của bạn ra ngoài internet."]})})]})]}),s.jsx(g,{placeholder:p({en:"Enter access token to check",vi:"Nhập access token cần kiểm tra"}),onChange:e=>y.current=e.target.value,required:!0,size:"large"})]})})).isConfirmed&&y.current&&(k("GetAccessToken:checkPermission"),window.open("https://developers.facebook.com/tools/debug/accesstoken/?access_token="+y.current))},children:["🔎 ",p({en:"Check token",vi:"Kiểm tra token"})]})]})}export{p as default};
