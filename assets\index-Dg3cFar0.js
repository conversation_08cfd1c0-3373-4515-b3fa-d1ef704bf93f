import{L as e,M as n,N as o,O as t,an as a,J as i,r as l,G as r,S as s,af as d,Q as p,Z as c,m as u,T as m,o as g,X as b,a6 as $,a0 as v,a7 as y,av as w,b5 as f}from"./index-Cak6rALw.js";import{D as C,E as h,F as S,G as O,H as x,J as P,K as E,M as j,N,O as z,P as R,R as I,Q as D,S as k}from"./MyApp-DW5WH4Ub.js";import{D as H}from"./Dropdown-BcL-JHCf.js";import{g as L}from"./PurePanel-BvHjNOhQ.js";import{i as A}from"./move-ESLFEEsv.js";const T=e=>{const{componentCls:n,menuCls:o,colorError:t,colorTextLightSolid:a}=e,i=`${o}-item`;return{[`${n}, ${n}-menu-submenu`]:{[`${o} ${i}`]:{[`&${i}-danger:not(${i}-disabled)`]:{color:t,"&:hover":{color:a,backgroundColor:t}}}}}},X=e=>{const{componentCls:n,menuCls:l,zIndexPopup:r,dropdownArrowDistance:s,sizePopupArrow:d,antCls:p,iconCls:c,motionDurationMid:u,paddingBlock:m,fontSize:g,dropdownEdgeChildPadding:b,colorTextDisabled:$,fontSizeIcon:v,controlPaddingHorizontal:y,colorBgElevated:w}=e;return[{[n]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:r,display:"block","&::before":{position:"absolute",insetBlock:e.calc(d).div(2).sub(s).equal(),zIndex:-9999,opacity:1e-4,content:'""'},"&-menu-vertical":{maxHeight:"100vh",overflowY:"auto"},[`&-trigger${p}-btn`]:{[`& > ${c}-down, & > ${p}-btn-icon > ${c}-down`]:{fontSize:v}},[`${n}-wrap`]:{position:"relative",[`${p}-btn > ${c}-down`]:{fontSize:v},[`${c}-down::before`]:{transition:`transform ${u}`}},[`${n}-wrap-open`]:{[`${c}-down::before`]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},[`&${p}-slide-down-enter${p}-slide-down-enter-active${n}-placement-bottomLeft,\n          &${p}-slide-down-appear${p}-slide-down-appear-active${n}-placement-bottomLeft,\n          &${p}-slide-down-enter${p}-slide-down-enter-active${n}-placement-bottom,\n          &${p}-slide-down-appear${p}-slide-down-appear-active${n}-placement-bottom,\n          &${p}-slide-down-enter${p}-slide-down-enter-active${n}-placement-bottomRight,\n          &${p}-slide-down-appear${p}-slide-down-appear-active${n}-placement-bottomRight`]:{animationName:O},[`&${p}-slide-up-enter${p}-slide-up-enter-active${n}-placement-topLeft,\n          &${p}-slide-up-appear${p}-slide-up-appear-active${n}-placement-topLeft,\n          &${p}-slide-up-enter${p}-slide-up-enter-active${n}-placement-top,\n          &${p}-slide-up-appear${p}-slide-up-appear-active${n}-placement-top,\n          &${p}-slide-up-enter${p}-slide-up-enter-active${n}-placement-topRight,\n          &${p}-slide-up-appear${p}-slide-up-appear-active${n}-placement-topRight`]:{animationName:S},[`&${p}-slide-down-leave${p}-slide-down-leave-active${n}-placement-bottomLeft,\n          &${p}-slide-down-leave${p}-slide-down-leave-active${n}-placement-bottom,\n          &${p}-slide-down-leave${p}-slide-down-leave-active${n}-placement-bottomRight`]:{animationName:h},[`&${p}-slide-up-leave${p}-slide-up-leave-active${n}-placement-topLeft,\n          &${p}-slide-up-leave${p}-slide-up-leave-active${n}-placement-top,\n          &${p}-slide-up-leave${p}-slide-up-leave-active${n}-placement-topRight`]:{animationName:C}}},x(e,w,{arrowPlacement:{top:!0,bottom:!0}}),{[`${n} ${l}`]:{position:"relative",margin:0},[`${l}-submenu-popup`]:{position:"absolute",zIndex:r,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},[`${n}, ${n}-menu-submenu`]:Object.assign(Object.assign({},t(e)),{[l]:Object.assign(Object.assign({padding:b,listStyleType:"none",backgroundColor:w,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},a(e)),{"&:empty":{padding:0,boxShadow:"none"},[`${l}-item-group-title`]:{padding:`${i(m)} ${i(y)}`,color:e.colorTextDescription,transition:`all ${u}`},[`${l}-item`]:{position:"relative",display:"flex",alignItems:"center"},[`${l}-item-icon`]:{minWidth:g,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${l}-title-content`]:{flex:"auto","&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},"> a":{color:"inherit",transition:`all ${u}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}},[`${l}-item-extra`]:{paddingInlineStart:e.padding,marginInlineStart:"auto",fontSize:e.fontSizeSM,color:e.colorTextDescription}},[`${l}-item, ${l}-submenu-title`]:Object.assign(Object.assign({display:"flex",margin:0,padding:`${i(m)} ${i(y)}`,color:e.colorText,fontWeight:"normal",fontSize:g,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${u}`,borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},a(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:$,cursor:"not-allowed","&:hover":{color:$,backgroundColor:w,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${i(e.marginXXS)} 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${n}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${n}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorIcon,fontSize:v,fontStyle:"normal"}}}),[`${l}-item-group-list`]:{margin:`0 ${i(e.marginXS)}`,padding:0,listStyle:"none"},[`${l}-submenu-title`]:{paddingInlineEnd:e.calc(y).add(e.fontSizeSM).equal()},[`${l}-submenu-vertical`]:{position:"relative"},[`${l}-submenu${l}-submenu-disabled ${n}-menu-submenu-title`]:{[`&, ${n}-menu-submenu-arrow-icon`]:{color:$,backgroundColor:w,cursor:"not-allowed"}},[`${l}-submenu-selected ${n}-menu-submenu-title`]:{color:e.colorPrimary}})})},[P(e,"slide-up"),P(e,"slide-down"),A(e,"move-up"),A(e,"move-down"),o(e,"zoom-big")]]},B=e("Dropdown",(e=>{const{marginXXS:o,sizePopupArrow:t,paddingXXS:a,componentCls:i}=e,l=n(e,{menuCls:`${i}-menu`,dropdownArrowDistance:e.calc(t).div(2).add(o).equal(),dropdownEdgeChildPadding:a});return[X(l),T(l)]}),(e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},E({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),j(e))),{resetStyle:!1}),M=e=>{var n;const{menu:o,arrow:t,prefixCls:a,children:i,trigger:w,disabled:f,dropdownRender:C,popupRender:h,getPopupContainer:S,overlayClassName:O,rootClassName:x,overlayStyle:P,open:E,onOpenChange:j,visible:D,onVisibleChange:k,mouseEnterDelay:L=.15,mouseLeaveDelay:A=.1,autoAdjustOverflow:T=!0,placement:X="",overlay:M,transitionName:_,destroyOnHidden:F,destroyPopupOnHide:G}=e,{getPopupContainer:W,getPrefixCls:q,direction:V,dropdown:J}=l.useContext(r),Q=h||C;s();const U=l.useMemo((()=>{const e=q();return void 0!==_?_:X.includes("top")?`${e}-slide-down`:`${e}-slide-up`}),[q,X,_]),Y=l.useMemo((()=>X?X.includes("Center")?X.slice(0,X.indexOf("Center")):X:"rtl"===V?"bottomRight":"bottomLeft"),[X,V]),K=q("dropdown",a),Z=d(K),[ee,ne,oe]=B(K,Z),[,te]=p(),ae=l.Children.only("object"!=typeof(ie=i)&&"function"!=typeof ie||null===ie?l.createElement("span",null,i):i);var ie;const le=c(ae,{className:u(`${K}-trigger`,{[`${K}-rtl`]:"rtl"===V},ae.props.className),disabled:null!==(n=ae.props.disabled)&&void 0!==n?n:f}),re=f?[]:w,se=!!(null==re?void 0:re.includes("contextMenu")),[de,pe]=m(!1,{value:null!=E?E:D}),ce=g((e=>{null==j||j(e,{source:"trigger"}),null==k||k(e),pe(e)})),ue=u(O,x,ne,oe,Z,null==J?void 0:J.className,{[`${K}-rtl`]:"rtl"===V}),me=N({arrowPointAtCenter:"object"==typeof t&&t.pointAtCenter,autoAdjustOverflow:T,offset:te.marginXXS,arrowWidth:t?te.sizePopupArrow:0,borderRadius:te.borderRadius}),ge=l.useCallback((()=>{(null==o?void 0:o.selectable)&&(null==o?void 0:o.multiple)||(null==j||j(!1,{source:"menu"}),pe(!1))}),[null==o?void 0:o.selectable,null==o?void 0:o.multiple]),[be,$e]=b("Dropdown",null==P?void 0:P.zIndex);let ve=l.createElement(H,Object.assign({alignPoint:se},$(e,["rootClassName"]),{mouseEnterDelay:L,mouseLeaveDelay:A,visible:de,builtinPlacements:me,arrow:!!t,overlayClassName:ue,prefixCls:K,getPopupContainer:S||W,transitionName:U,trigger:re,overlay:()=>{let e;return e=(null==o?void 0:o.items)?l.createElement(z,Object.assign({},o)):"function"==typeof M?M():M,Q&&(e=Q(e)),e=l.Children.only("string"==typeof e?l.createElement("span",null,e):e),l.createElement(R,{prefixCls:`${K}-menu`,rootClassName:u(oe,Z),expandIcon:l.createElement("span",{className:`${K}-menu-submenu-arrow`},"rtl"===V?l.createElement(I,{className:`${K}-menu-submenu-arrow-icon`}):l.createElement(y,{className:`${K}-menu-submenu-arrow-icon`})),mode:"vertical",selectable:!1,onClick:ge,validator:({mode:e})=>{}},e)},placement:Y,onVisibleChange:ce,overlayStyle:Object.assign(Object.assign(Object.assign({},null==J?void 0:J.style),P),{zIndex:be}),autoDestroy:null!=F?F:G}),le);return be&&(ve=l.createElement(v.Provider,{value:$e},ve)),ee(ve)},_=L(M,"align",void 0,"dropdown",(e=>e));M._InternalPanelDoNotUseOrYouWillBeFired=e=>l.createElement(_,Object.assign({},e),l.createElement("span",null));const F=e=>{const{getPopupContainer:n,getPrefixCls:o,direction:t}=l.useContext(r),{prefixCls:a,type:i="default",danger:s,disabled:d,loading:p,onClick:c,htmlType:m,children:g,className:b,menu:$,arrow:v,autoFocus:y,overlay:C,trigger:h,align:S,open:O,onOpenChange:x,placement:P,getPopupContainer:E,href:j,icon:N=l.createElement(D,null),title:z,buttonsRender:R=e=>e,mouseEnterDelay:I,mouseLeaveDelay:H,overlayClassName:L,overlayStyle:A,destroyOnHidden:T,destroyPopupOnHide:X,dropdownRender:B,popupRender:_}=e,F=function(e,n){var o={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(o[t]=e[t]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(t=Object.getOwnPropertySymbols(e);a<t.length;a++)n.indexOf(t[a])<0&&Object.prototype.propertyIsEnumerable.call(e,t[a])&&(o[t[a]]=e[t[a]])}return o}(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyOnHidden","destroyPopupOnHide","dropdownRender","popupRender"]),G=o("dropdown",a),W=`${G}-button`,q={menu:$,arrow:v,autoFocus:y,align:S,disabled:d,trigger:d?[]:h,onOpenChange:x,getPopupContainer:E||n,mouseEnterDelay:I,mouseLeaveDelay:H,overlayClassName:L,overlayStyle:A,destroyOnHidden:T,popupRender:_||B},{compactSize:V,compactItemClassnames:J}=w(G,t),Q=u(W,J,b);"destroyPopupOnHide"in e&&(q.destroyPopupOnHide=X),"overlay"in e&&(q.overlay=C),"open"in e&&(q.open=O),q.placement="placement"in e?P:"rtl"===t?"bottomLeft":"bottomRight";const U=l.createElement(f,{type:i,danger:s,disabled:d,loading:p,onClick:c,htmlType:m,href:j,title:z},g),Y=l.createElement(f,{type:i,danger:s,icon:N}),[K,Z]=R([U,Y]);return l.createElement(k.Compact,Object.assign({className:Q,size:V,block:!0},F),K,l.createElement(M,Object.assign({},q),Z))};F.__ANT_BUTTON=!0;const G=M;G.Button=F;export{G as D};
