import{r as e}from"./index-Cak6rALw.js";function n(){return n=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var o in t)({}).hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},n.apply(null,arguments)}var t=["shift","alt","meta","mod","ctrl"],o={esc:"escape",return:"enter",".":"period",",":"comma","-":"slash"," ":"space","`":"backquote","#":"backslash","+":"bracketright",ShiftLeft:"shift",ShiftRight:"shift",AltLeft:"alt",AltRight:"alt",MetaLeft:"meta",MetaRight:"meta",OSLeft:"meta",OSRight:"meta",ControlLeft:"ctrl",ControlRight:"ctrl"};function r(e){return(e&&o[e]||e||"").trim().toLowerCase().replace(/key|digit|numpad|arrow/,"")}function i(e,n){return void 0===n&&(n=","),e.split(n)}function u(e,o,i){void 0===o&&(o="+");var u=e.toLocaleLowerCase().split(o).map((function(e){return r(e)}));return n({},{alt:u.includes("alt"),ctrl:u.includes("ctrl")||u.includes("control"),shift:u.includes("shift"),meta:u.includes("meta"),mod:u.includes("mod")},{keys:u.filter((function(e){return!t.includes(e)})),description:i,hotkey:e})}"undefined"!=typeof document&&(document.addEventListener("keydown",(function(e){void 0!==e.key&&c([r(e.key),r(e.code)])})),document.addEventListener("keyup",(function(e){void 0!==e.key&&s([r(e.key),r(e.code)])}))),"undefined"!=typeof window&&window.addEventListener("blur",(function(){a.clear()}));var a=new Set;function l(e){return Array.isArray(e)}function c(e){var n=Array.isArray(e)?e:[e];a.has("meta")&&a.forEach((function(e){return!function(e){return t.includes(e)}(e)&&a.delete(e.toLowerCase())})),n.forEach((function(e){return a.add(e.toLowerCase())}))}function s(e){var n=Array.isArray(e)?e:[e];"meta"===e?a.clear():n.forEach((function(e){return a.delete(e.toLowerCase())}))}function d(e,n){void 0===n&&(n=!1);var t,o=e.target,r=e.composed,i=null;return i=(t=o).tagName&&!t.tagName.startsWith("-")&&t.tagName.includes("-")&&r?e.composedPath()[0]&&e.composedPath()[0].tagName:o&&o.tagName,l(n)?Boolean(i&&n&&n.some((function(e){var n;return e.toLowerCase()===(null==(n=i)?void 0:n.toLowerCase())}))):Boolean(i&&n&&n)}var f=function(e,n,t){void 0===t&&(t=!1);var o,i,u=n.alt,c=n.meta,s=n.mod,d=n.shift,f=n.ctrl,v=n.keys,y=e.key,p=e.code,m=e.ctrlKey,h=e.metaKey,k=e.shiftKey,g=e.altKey,w=r(p),b=y.toLowerCase();if(!(null!=v&&v.includes(w)||null!=v&&v.includes(b)||["ctrl","control","unknown","meta","alt","shift","os"].includes(w)))return!1;if(!t){if(u===!g&&"alt"!==b)return!1;if(d===!k&&"shift"!==b)return!1;if(s){if(!h&&!m)return!1}else{if(c===!h&&"meta"!==b&&"os"!==b)return!1;if(f===!m&&"ctrl"!==b&&"control"!==b)return!1}}return!(!v||1!==v.length||!v.includes(b)&&!v.includes(w))||(v?(void 0===i&&(i=","),(l(o=v)?o:o.split(i)).every((function(e){return a.has(e.trim().toLowerCase())}))):!v)},v=e.createContext(void 0);function y(e,n){return e&&n&&"object"==typeof e&&"object"==typeof n?Object.keys(e).length===Object.keys(n).length&&Object.keys(e).reduce((function(t,o){return t&&y(e[o],n[o])}),!0):e===n}var p=e.createContext({hotkeys:[],enabledScopes:[],toggleScope:function(){},enableScope:function(){},disableScope:function(){}});function m(n){var t=e.useRef(void 0);return y(t.current,n)||(t.current=n),t.current}var h=function(e){e.stopPropagation(),e.preventDefault(),e.stopImmediatePropagation()},k="undefined"!=typeof window?e.useLayoutEffect:e.useEffect;function g(n,t,o,a){var y=e.useState(null),g=y[0],w=y[1],b=e.useRef(!1),L=o instanceof Array?a instanceof Array?void 0:a:o,E=l(n)?n.join(null==L?void 0:L.splitKey):n,C=o instanceof Array?o:a instanceof Array?a:void 0,O=e.useCallback(t,null!=C?C:[]),A=e.useRef(O);A.current=C?O:t;var S=m(L),K=e.useContext(p).enabledScopes,R=e.useContext(v);return k((function(){if(!1!==(null==S?void 0:S.enabled)&&(e=K,n=null==S?void 0:S.scopes,0===e.length&&n?(console.warn('A hotkey has the "scopes" option set, however no active scopes were found. If you want to use the global scopes feature, you need to wrap your app in a <HotkeysProvider>'),1):!n||e.some((function(e){return n.includes(e)}))||e.includes("*"))){var e,n,t=function(e,n){var t;if(void 0===n&&(n=!1),!d(e,["input","textarea","select"])||d(e,null==S?void 0:S.enableOnFormTags)){if(null!==g){var o=g.getRootNode();if((o instanceof Document||o instanceof ShadowRoot)&&o.activeElement!==g&&!g.contains(o.activeElement))return void h(e)}(null==(t=e.target)||!t.isContentEditable||null!=S&&S.enableOnContentEditable)&&i(E,null==S?void 0:S.splitKey).forEach((function(t){var o,r=u(t,null==S?void 0:S.combinationKey);if(f(e,r,null==S?void 0:S.ignoreModifiers)||null!=(o=r.keys)&&o.includes("*")){if(null!=S&&null!=S.ignoreEventWhen&&S.ignoreEventWhen(e))return;if(n&&b.current)return;if(function(e,n,t){("function"==typeof t&&t(e,n)||!0===t)&&e.preventDefault()}(e,r,null==S?void 0:S.preventDefault),!function(e,n,t){return"function"==typeof t?t(e,n):!0===t||void 0===t}(e,r,null==S?void 0:S.enabled))return void h(e);A.current(e,r),n||(b.current=!0)}}))}},o=function(e){void 0!==e.key&&(c(r(e.code)),(void 0===(null==S?void 0:S.keydown)&&!0!==(null==S?void 0:S.keyup)||null!=S&&S.keydown)&&t(e))},a=function(e){void 0!==e.key&&(s(r(e.code)),b.current=!1,null!=S&&S.keyup&&t(e,!0))},l=g||(null==L?void 0:L.document)||document;return l.addEventListener("keyup",a,null==L?void 0:L.eventListenerOptions),l.addEventListener("keydown",o,null==L?void 0:L.eventListenerOptions),R&&i(E,null==S?void 0:S.splitKey).forEach((function(e){return R.addHotkey(u(e,null==S?void 0:S.combinationKey,null==S?void 0:S.description))})),function(){l.removeEventListener("keyup",a,null==L?void 0:L.eventListenerOptions),l.removeEventListener("keydown",o,null==L?void 0:L.eventListenerOptions),R&&i(E,null==S?void 0:S.splitKey).forEach((function(e){return R.removeHotkey(u(e,null==S?void 0:S.combinationKey,null==S?void 0:S.description))}))}}}),[g,E,S,K]),w}export{g as u};
