import{x as e,az as i}from"./MyApp-DW5WH4Ub.js";import{fetchInstaGraphQl as o,getBiggestUrl as l}from"./core-Dw7OsFL6.js";import"./index-Cak6rALw.js";async function n(n="",a=""){let r;if(a){const i=await o({fb_api_req_friendly_name:"PolarisProfilePostsTabContentQuery_connection",variables:{after:a,before:null,data:{count:12,include_reel_media_seen_timestamp:!0,include_relationship_info:!0,latest_besties_reel_media:!0,latest_reel_media:!0},first:12,last:null,username:n,__relay_internal__pv__PolarisIsLoggedInrelayprovider:!0,__relay_internal__pv__PolarisShareSheetV3relayprovider:!0},doc_id:"9066001566837493"});r=e(i),console.log("cursor fetch",r)}else{const i=await o({fb_api_req_friendly_name:"PolarisProfilePostsQuery",variables:{data:{count:12,include_reel_media_seen_timestamp:!0,include_relationship_info:!0,latest_besties_reel_media:!0,latest_reel_media:!0},username:n,__relay_internal__pv__PolarisIsLoggedInrelayprovider:!0,__relay_internal__pv__PolarisShareSheetV3relayprovider:!0},doc_id:"9582275171810021"});r=e(i),console.log("first fetch",r)}const{edges:_=[],page_info:t={}}=i(r);return console.log(r),_.map((e=>{var i,o,n,a;const r=(null==(i=null==e?void 0:e.node)?void 0:i.media)||(null==e?void 0:e.node);return{id:null==r?void 0:r.id,post_id:null==r?void 0:r.code,caption:(null==(o=null==r?void 0:r.caption)?void 0:o.text)||"",video:l(null==r?void 0:r.video_versions),image:l(null==(n=null==r?void 0:r.image_versions2)?void 0:n.candidates),carousel:null==(a=null==r?void 0:r.carousel_media)?void 0:a.map(((e,i)=>{var o;return{id:e.id,post_id:null==r?void 0:r.code,index:i+1,video:l(e.video_versions),image:l(null==(o=e.image_versions2)?void 0:o.candidates)}})),created_at:1e3*(null==r?void 0:r.taken_at),like_count:null==r?void 0:r.like_count,comment_count:null==r?void 0:r.comment_count,cursor:(null==e?void 0:e.cursor)||(null==t?void 0:t.end_cursor)}})).filter((e=>e.image||e.video))}export{n as getInstaPosts};
