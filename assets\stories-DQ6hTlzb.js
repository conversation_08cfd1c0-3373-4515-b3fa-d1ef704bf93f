import{y as l,x as o,W as n,az as i,w as e}from"./MyApp-DW5WH4Ub.js";import"./index-Cak6rALw.js";async function d(e=""){const d=await l({fb_api_req_friendly_name:"StoriesTrayRectangularQuery",variables:{blur:10,bucketsToFetch:5,cursor:e,scale:1,id:await n()},doc_id:"24539171939059864"}),u=o(d);console.log(u);const{edges:r=[],page_info:t={}}=i(u);return r.map((l=>{var o,n,i,e,d,u,r,a,v,s,_,c,m;const y=(null==(o=null==l?void 0:l.node)?void 0:o.story_bucket_owner)||{};return{bucket_id:null==(n=null==l?void 0:l.node)?void 0:n.id,thumbnail:null==(u=null==(d=null==(e=null==(i=null==l?void 0:l.node)?void 0:i.thumbnail_story_to_show)?void 0:e.story_card_info)?void 0:d.story_thumbnail)?void 0:u.uri,owner:{id:y.id,name:y.name,avatar:(null==(r=y.coverImage)?void 0:r.uri)||(null==(a=y.profilePic)?void 0:a.uri)||(null==(v=y.profile_picture)?void 0:v.uri)},stories:null==(c=null==(_=null==(s=null==l?void 0:l.node)?void 0:s.unified_stories)?void 0:_.edges)?void 0:c.map((l=>{var o,n;return{id:null==(o=null==l?void 0:l.node)?void 0:o.id,expiration_time:null==(n=null==l?void 0:l.node)?void 0:n.expiration_time}})),seen:null==(m=null==l?void 0:l.node)?void 0:m.is_bucket_seen_by_viewer,cursor:(null==l?void 0:l.cursor)||t.end_cursor}})).filter((l=>l.stories.length>0))}async function u(n=""){var i,e,d,u,r;const t=await l({fb_api_req_friendly_name:"StoriesSuspenseContentPaneRootWithEntryPointQuery",variables:{blur:10,bucketID:n,feedbackSource:65,feedLocation:"COMET_MEDIA_VIEWER",focusCommentID:null,initialBucketID:n,initialLoad:!1,scale:1,shouldDeferLoad:!1,shouldEnableArmadilloStoryReply:!0,shouldEnableLiveInStories:!0,__relay_internal__pv__StoriesIsShareToStoryEnabledrelayprovider:!1,__relay_internal__pv__StoriesLWRVariantrelayprovider:"www_new_reactions"},doc_id:"8237681579610775"}),a=o((null==t?void 0:t.split("\n")[0])||"{}");console.log(a);const v=(null==(e=null==(i=null==a?void 0:a.data)?void 0:i.nodes)?void 0:e[0])||(null==(d=null==a?void 0:a.data)?void 0:d.bucket);return null==(r=null==(u=null==v?void 0:v.unified_stories)?void 0:u.edges)?void 0:r.map((l=>{var o,i,e,d,u,r,t,a,v,s,_,c,m,y,f,g,p,b,w,h,I,S,k,C,E,D,T,L,A,M,V,x,R,q,P,W,Q,j,G,O,F;const H=(null==(d=null==(e=null==(i=null==(o=null==l?void 0:l.node)?void 0:o.attachments)?void 0:i.sort(((l,o)=>{var n,i,e,d,u,r,t,a;return((null==(i=null==(n=null==o?void 0:o.media)?void 0:n.image)?void 0:i.width)||0)*((null==(d=null==(e=null==o?void 0:o.media)?void 0:e.image)?void 0:d.height)||0)-((null==(r=null==(u=null==l?void 0:l.media)?void 0:u.image)?void 0:r.width)||0)*((null==(a=null==(t=null==l?void 0:l.media)?void 0:t.image)?void 0:a.height)||0)})))?void 0:e[0])?void 0:d.media)||{},z=(null==(r=null==(u=null==l?void 0:l.node)?void 0:u.story_card_info)?void 0:r.feedback_summary)||{},B=[];for(let n of(null==(v=null==(a=null==(t=null==l?void 0:l.node)?void 0:t.story_card_info)?void 0:a.viewerList_viewers)?void 0:v.edges)||[])for(let l of(null==(s=null==n?void 0:n.feedback)?void 0:s.lightweight_reactions)||[]){let o=null==(c=null==(_=null==l?void 0:l.id)?void 0:_.split(":"))?void 0:c[0],n="";for(let i of(null==(m=null==l?void 0:l.last5)?void 0:m.edges)||[])n+=null==(y=null==i?void 0:i.node)?void 0:y.reaction;B.push({uid:o,reaction:n})}return{bucket_id:n,id:null==(f=null==l?void 0:l.node)?void 0:f.id,caption:(null==(p=null==(g=null==l?void 0:l.node)?void 0:g.message)?void 0:p.text)||(null==(b=null==H?void 0:H.message)?void 0:b.text)||(null==(w=null==H?void 0:H.title)?void 0:w.text),thumbnail:(null==(S=null==(I=null==(h=null==l?void 0:l.node)?void 0:h.story_card_info)?void 0:I.story_video_thumbnail)?void 0:S.uri)||(null==(E=null==(C=null==(k=null==l?void 0:l.node)?void 0:k.story_card_info)?void 0:C.story_thumbnail)?void 0:E.uri)||(null==(D=null==H?void 0:H.previewImage)?void 0:D.uri),url:null==(L=null==(T=null==l?void 0:l.node)?void 0:T.shareable)?void 0:L.url,video:(null==H?void 0:H.playable_url_quality_hd)||(null==H?void 0:H.browser_native_hd_url)||(null==H?void 0:H.playable_url)||(null==H?void 0:H.browser_native_sd_url),image:(null==(A=null==H?void 0:H.image)?void 0:A.uri)||(null==(M=null==H?void 0:H.previewImage)?void 0:M.uri),reaction:null==(x=null==(V=null==z?void 0:z.reaction_summary)?void 0:V.map((l=>l.reaction_unicode)))?void 0:x.join(""),react_count:null==z?void 0:z.total_reaction_count,view_count:(null==(P=null==(q=null==(R=null==l?void 0:l.node)?void 0:R.story_card_info)?void 0:q.story_viewers)?void 0:P.count)||(null==(j=null==(Q=null==(W=null==l?void 0:l.node)?void 0:W.story_card_info)?void 0:Q.viewers)?void 0:j.count),creation_time:1e3*((null==(G=null==l?void 0:l.node)?void 0:G.creation_time)||0),seen:null==(F=null==(O=null==l?void 0:l.node)?void 0:O.story_card_seen_state)?void 0:F.is_seen_by_viewer,reactions:B}}))}async function r(e=""){const d=await l({doc_id:"2474284976008135",variables:{count:8,cursor:e,scale:1.5,id:btoa("app_collection:"+await n()+":235134840312703:175")}}),u=o(d);console.log(u);const{edges:r=[],page_info:t={}}=i(u);return r.map((l=>{var o,n,i,e,d,u;const r=(null==(o=null==l?void 0:l.node)?void 0:o.node)||(null==l?void 0:l.node);return{id:null==r?void 0:r.id,creation_time:null==(n=null==r?void 0:r.story_card_info)?void 0:n.local_creation_time,thumbnail:null==(e=null==(i=null==r?void 0:r.story_card_info)?void 0:i.story_thumbnail)?void 0:e.uri,seen_count:null==(u=null==(d=null==r?void 0:r.story_card_seen_state)?void 0:d.seen_count)?void 0:u.count,cursor:(null==l?void 0:l.cursor)||t.end_cursor}}))}async function t({afterTime:n="",beforeTime:i="",creationTime:d=""}={}){var u,r,t,v;if(d){const l=new Date(d),o=new Date(l.getTime()+864e5);n=e(l),i=e(o)}console.log(n,i);const s=await l({fb_api_req_friendly_name:"StoriesArchiveContentPaneRootQuery",variables:{lower_bound_time:n,upper_bound_time:i,scale:1,blur:null,shouldEnableLiveInStories:!0,feedbackSource:65,useDefaultActor:!1,feedLocation:"COMET_MEDIA_VIEWER",focusCommentID:null,isStoriesArchive:!0,__relay_internal__pv__StoriesIsShareToStoryEnabledrelayprovider:!1},doc_id:"8266171723403755"}),_=o(s.split("\n")[0]||"{}");console.log(_);const{edges:c=[],page_info:m={}}=(null==(v=null==(t=null==(r=null==(u=null==_?void 0:_.data)?void 0:u.viewer)?void 0:r.stories_data)?void 0:t.story_archive)?void 0:v.cards)||{};return c.map((l=>{var o,n,i,e,d,u,r,t,v,s,_,c,m,y,f,g,p,b,w,h,I,S;const k=(null==(o=null==l?void 0:l.node)?void 0:o.viewerCount)||{},C=null==(i=null==(n=null==l?void 0:l.node)?void 0:n.media_attribution_elements)?void 0:i.find((l=>"MUSIC"===l.attribution_type));function E(o){var n,i;return null==(i=null==(n=null==l?void 0:l.node)?void 0:n.attachments)?void 0:i.find((l=>{var n;return(null==(n=null==l?void 0:l.media)?void 0:n.__typename)===o}))}function D(l){return["image","previewImage","blurred_image"].reduce(((o,n)=>{var i,e;return o||(null==(e=null==(i=null==l?void 0:l.media)?void 0:i[n])?void 0:e.uri)}),"")}let T="",L=E("GenericAttachmentMedia");return L?T=E("Photo"):L=E("Photo"),{id:null==(e=null==l?void 0:l.node)?void 0:e.id,created_at:1e3*((null==(d=null==l?void 0:l.node)?void 0:d.creation_time)||0),last_seen_at:1e3*((null==(r=null==(u=null==l?void 0:l.node)?void 0:u.story_card_seen_state)?void 0:r.seen_receipts_last_seen_time)||0),comment_count:null==(v=null==(t=null==l?void 0:l.node)?void 0:t.feedback)?void 0:v.story_comment_count,viewer_count:{friend:null==(s=null==k?void 0:k.friendViewerCount)?void 0:s.count,connection:null==(_=null==k?void 0:k.connectionViewerCount)?void 0:_.count,follower:null==(c=null==k?void 0:k.followerViewerCount)?void 0:c.count,new_friend:null==(m=null==k?void 0:k.newFriendViewerCount)?void 0:m.count,new_connection:null==(y=null==k?void 0:k.newConnectionViewerCount)?void 0:y.count},image:D(L),image_background:D(T),video:["browser_native_hd_url","browser_native_sd_url"].reduce(((o,n)=>{var i,e,d,u;return o||(null==(u=null==(d=null==(e=null==(i=null==l?void 0:l.node)?void 0:i.attachments)?void 0:e.find((l=>{var o;return null==(o=null==l?void 0:l.media)?void 0:o[n]})))?void 0:d.media)?void 0:u[n])}),""),music:C?(null==C?void 0:C.song_title)+" - "+(null==C?void 0:C.artist_name):"",reactions:null==(b=null==(p=null==(g=null==(f=null==l?void 0:l.node)?void 0:f.story_card_info)?void 0:g.story_card_reactions)?void 0:p.edges)?void 0:b.map((l=>{var o,n,i,e,d,u,r;return{uid:null==(n=null==(o=null==l?void 0:l.node)?void 0:o.messaging_actor)?void 0:n.id,reactions:null==(d=null==(e=null==(i=null==l?void 0:l.node)?void 0:i.messaging_actions)?void 0:e.edges)?void 0:d.reduce(((l,o)=>{var n;return l+(null==(n=null==o?void 0:o.node)?void 0:n.reaction)}),""),count:null==(r=null==(u=null==l?void 0:l.node)?void 0:u.messaging_actions)?void 0:r.count}})),viewers:null==(S=null==(I=null==(h=null==(w=null==l?void 0:l.node)?void 0:w.story_card_info)?void 0:h.viewerList_viewers)?void 0:I.edges)?void 0:S.map(a)}}))}function a(l){var o,n,i,e,d,u,r,t;return{uid:null==(o=null==l?void 0:l.node)?void 0:o.id,avatar:null==(i=null==(n=null==l?void 0:l.node)?void 0:n.profile_picture)?void 0:i.uri,name:null==(e=null==l?void 0:l.node)?void 0:e.name,reactions:null==(u=null==(d=null==l?void 0:l.feedback)?void 0:d.lightweight_reactions)?void 0:u.reduce(((l,o)=>{var n,i;const e=(null==(n=null==o?void 0:o.last5)?void 0:n.edges)||(null==(i=null==o?void 0:o.messaging_actions)?void 0:i.edges);return l+(null==e?void 0:e.reduce(((l,o)=>{var n;return l+(null==(n=null==o?void 0:o.node)?void 0:n.reaction)}),""))}),""),count:null==(t=null==(r=null==l?void 0:l.feedback)?void 0:r.lightweight_reactions)?void 0:t.reduce(((l,o)=>{var n;return l+(null==(n=null==o?void 0:o.messaging_actions)?void 0:n.count)}),0),seen_time:1e3*((null==l?void 0:l.seen_time)||0),cursor:null==l?void 0:l.cursor}}async function v({storyId:n="",cursor:e=""}={}){const d=await l({fb_api_req_friendly_name:"StoriesViewerSheetViewerListContentQuery",variables:{cursor:e,id:n,viewerCount:8},doc_id:"3934951263269875"}),u=o(d.split("\n")[0]||"{}");console.log(u);const{edges:r=[],page_info:t={}}=i(u);return r.map(a)}async function s(n=""){var i,e,d,u,r;const t=await l({variables:{scale:1,userID:n,useIncrementalDelivery:!0},doc_id:"4192155837522535"}),a=o(t.split("\n")[0]||"{}"),v=null==(u=null==(d=null==(e=null==(i=null==a?void 0:a.data)?void 0:i.user)?void 0:e.story_bucket)?void 0:d.nodes)?void 0:u[0];return console.log(a),{bucketId:null==v?void 0:v.id,firstStoryId:null==(r=null==v?void 0:v.first_story_to_show)?void 0:r.id}}async function _(n="",i=""){var e,d,u,r,t,a;const v=await l({fb_api_req_friendly_name:"useStoriesSendReplyMutation",variables:{input:{lightweight_reaction_actions:{offsets:[0],reaction:i},story_id:n,story_reply_type:"LIGHT_WEIGHT",actor_id:"me"}},doc_id:"3769885849805751"}),s=o(v);return console.log(s),null==(a=null==(t=null==(r=null==(u=null==(d=null==(e=null==s?void 0:s.data)?void 0:e.direct_message_reply)?void 0:d.story)?void 0:u.story_card_info)?void 0:r.story_card_reactions)?void 0:t.edges)?void 0:a.length}async function c(i=""){var e,d;const u=await l({fb_api_req_friendly_name:"StoriesArchiveDeleteCardOptionMenuItem_StoriesArchiveDeleteMutation",variables:{input:{archived_story_card_id:i,actor_id:await n()}},doc_id:8763985503642972}),r=o(u);return console.log(r),(null==(d=null==(e=null==r?void 0:r.data)?void 0:e.archived_story_card_delete)?void 0:d.deleted_archived_story_card_id)===i}export{c as deleteArchivedStory,r as getArchivedStories,t as getArchivedStoriesContent,s as getFirstStoryBucketIdToShow,d as getRecentStories,u as getStoriesBucketsData,v as getStoryViewers,_ as reactStory};
