const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./useVIP-D5FAMGQQ.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./MyApp-DW5WH4Ub.js","./sweetalert2.esm.all-BZxvatOx.js","./getIds-DAzc-wzf.js","./index-Bg865k-U.js","./EyeOutlined-CNKPohhw.js","./SearchOutlined--mXbzQ68.js","./index-IxNfZAD-.js","./Dropdown-BcL-JHCf.js","./file-download-B-gszDlL.js"])))=>i.map(i=>d[i]);
import{r as e,b0 as n,b5 as t,aN as i,cf as o,cg as l,ch as s}from"./index-Cak6rALw.js";import{a,b as r}from"./index-ByRdMNW-.js";import{u as d,d as c,t as h,aJ as u,S as m,c as f,b as g,T as x,ah as p,aK as j,aL as v}from"./MyApp-DW5WH4Ub.js";import w,{getCacheByKey as y}from"./useCacheState-CAnxkewm.js";import{u as b}from"./react-hotkeys-hook.esm-wjq6pdfC.js";import{P as k}from"./index-PbLYNhdQ.js";import{a as C}from"./index-QU7lSj_a.js";import{D}from"./index-Dg3cFar0.js";import{I as T}from"./index-Bg865k-U.js";import{L as $}from"./index-jrOnBmdW.js";import{R as I}from"./row-BMfM-of4.js";import{D as S}from"./index-CC5caqt_.js";import"./index-CxAc8H5Q.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";var _=(e=>(e.File="file",e.Link="link",e.JSON="json",e))(_||{}),P=(e=>(e.Grid="grid",e.Vertical="vertical",e))(P||{});function N({collectionName:P,renderItem:N,fetchNext:B,rowKey:L,downloadItem:E,getItemCursor:A,initialData:F=[],downloadThreads:O=5,downloadOptions:V=[],displayType:M="grid",headerButtons:R=()=>null,header:H=()=>null,once:K=!1,all:q=!1,stop:z=!1,showPagination:W=!1,hideLoadMore:J=!1,pageSize:G=20,searchPlaceholder:X=()=>({en:"Search",vi:"Tìm kiếm"}),onSearch:Q,centerItems:U=!0}){const{ti:Y}=d(),{message:Z,notification:ee}=c(),{isIntersecting:ne,ref:te}=a(),[ie,oe]=e.useState(!1),[le,se]=w(`Collection.${P}.hasMore`,!0),[ae,re]=w(`Collection.${P}.data`,F),[de,ce]=w(`Collection.${P}.pageIndex`,1),[he,ue]=w(`Collection.${P}.search`,""),[me,fe]=w(`Collection.${P}.selected`,[]),[ge,xe]=w(`Collection.${P}.selectMode`,!1),[pe,je]=w(`Collection.${P}.downloaded`,{});b(["shift"],(()=>{E&&xe((e=>!e))}));const ve=e.useMemo((()=>he&&Q?ae.filter((e=>Q(he,e))):ae),[ae,he,Q]);e.useEffect((()=>{let e=ae.length,n=Math.ceil(e/20);de>n&&ce(n)}),[ae,de]),e.useEffect((()=>{ae.length||ye(!1)}),[P]),e.useEffect((()=>{F&&!B&&re(F)}),[F,B]);const we=(!ae.length||ne||q)&&le&&!he&&!z&&B;r((()=>{we&&ke()}),1e3),e.useEffect((()=>{we&&ke()}),[we]);const ye=(e=!1)=>{e&&h("Collection:reload");const n=e?[]:y("Collection.data."+P)||[];re(n),fe([]),ke(n)},be=e.useRef(!1),ke=async(e=ae)=>{if(be.current)return;be.current=!0,oe(!0);const n=await(null==B?void 0:B(e));console.log(P,n);let t=!1;if(null==n?void 0:n.length){const i=u(e,n,L);if(se(!K&&i.length>0),i.length){const n=[...e,...i];re(n),t=!0}}else 0===(null==n?void 0:n.length)&&se(!1);t||e.length||Z.info(Y({en:"No data found",vi:"Không có dữ liệu"})+": "+P),be.current=!1,oe(!1)},Ce=e.useCallback(((e,i)=>{const o=me.indexOf(e),l=o>=0,s=pe[L(e)],a={position:"absolute",top:0,left:0,right:0,bottom:0,width:"100%",height:"100%",background:l||s?"#000a":"#0002"};return n.jsxs("div",{style:{position:"relative",flexGrow:1,height:"100%"},children:[i<ae.length-1?N(e,i,re):n.jsx(m,{ref:te,children:N(e,i,re)}),ge?n.jsx(t,{type:"primary",style:a,className:"scale-on-hover",onClick:()=>{fe(l?n=>n.filter((n=>n!==e)):n=>[...n,e])},children:l?n.jsx("i",{className:"fa-solid fa-5x",children:o+1}):s?n.jsx("i",{className:"fa-solid fa-check fa-5x"}):n.jsx("i",{className:"fa-solid fa-square  fa-5x",style:{color:"#fff7"}})}):s?n.jsx("div",{style:{...a,display:"flex",justifyContent:"center",alignItems:"center",pointerEvents:"none",background:"#0006"},children:n.jsx("i",{className:"fa-solid fa-check fa-5x",style:{color:"white"}})}):null]},"select_container_"+L(e))}),[ae,me,pe,L,ge,N,te]),De=async({fromCursor:e,downloadType:a,startIndex:r=0}={})=>{var d;if(!E)return;const c=(await i((async()=>{const{default:e}=await import("./sweetalert2.esm.all-BZxvatOx.js");return{default:e}}),[],import.meta.url)).default;if(!("showDirectoryPicker"in window)&&"file"===a)return c.fire({icon:"error",title:Y({en:"Browser not supported",vi:"Trình duyệt không hỗ trợ"}),text:Y({en:"File saver API not supported in this browser. Please use newest version of Edge or Chrome. (window.showDirectoryPicker)",vi:"Trình duyệt này không hỗ trợ API lưu file (window.showDirectoryPicker). Vui lòng sử dụng Edge hoặc Chrome bản mới nhất"})});let f;if(!e&&!ge&&(f=await c.fire({icon:"question",title:Y({en:"Download",vi:"Tải xuống"})+"?",text:P,showDenyButton:!0,showCancelButton:!1,confirmButtonColor:"#d33",denyButtonColor:"#1668dc",confirmButtonText:Y({en:"Download from cursor",vi:"Tải từ vị trí"}),denyButtonText:Y({en:"Download all",vi:"Tải từ đầu"})}),f.isDismissed))return;if(null==f?void 0:f.isConfirmed){const n=await c.fire({icon:"info",title:Y({en:"Download from cursor",vi:"Tải từ vị trí"}),html:`\n                <label for="from-cursor">\n                ${Y({en:"Last cursor",vi:"Con trỏ cuối"})}: (${Y({en:"leave empty to re-download all",vi:"bỏ trống để tải từ đầu"})})\n                </label><br/>\n                <input\n                    id="from-cursor"\n                    class="swal2-input"\n                    style="margin: 5px"\n                    value="${e||localStorage.getItem(P+"_fromCursor")||""}">\n                <br/>\n                <label for="start-index">\n                ${Y({en:"Last index",vi:"Vị trí cuối"})}: (${Y({en:"for auto generate file name",vi:"để tự động tạo tên file"})})\n                </label><br/>\n                <input\n                    id="start-index"\n                    class="swal2-input"\n                    style="margin: 5px"\n                    value="${r||localStorage.getItem(P+"_startIndex")||0}">\n            `,preConfirm:()=>{var e,n;return[null==(e=document.getElementById("from-cursor"))?void 0:e.value,null==(n=document.getElementById("start-index"))?void 0:n.value]},showCancelButton:!0,confirmButtonText:Y({en:"Start download",vi:"Bắt đầu tải"})});if(n.isDismissed||n.isDenied)return;e=n.value[0],r=parseInt((null==(d=n.value)?void 0:d[1])||0)}if(!a){const e=await c.fire({icon:"question",title:Y({en:"Data type",vi:"Loại dữ liệu"}),html:`\n                    <label for="download-type">\n                        ${Y({en:"Which data type you want to download?",vi:"Bạn muốn tải xuống loại dữ liệu nào?"})}\n                    </label><br/>\n                    <select\n                        id="download-type"\n                        class="swal2-select"\n                        style="margin: 5px">\n                        ${Object.values(_).map((e=>`<option value="${e}">${e}</option>`)).join("")}\n                    </select>\n                `,preConfirm:()=>{var e;return null==(e=document.getElementById("download-type"))?void 0:e.value},showCancelButton:!0,reverseButtons:!0});if(e.isDismissed)return;a=e.value}const g=(await i((async()=>{const{checkVIP:e}=await import("./useVIP-D5FAMGQQ.js");return{checkVIP:e}}),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]),import.meta.url)).checkVIP;if(!(await g()))return;let x;if(h("downloadCollection:"+a+":"+(ge?`selected:${me.length}:`:"all:")+P),"file"===a){const e=await window.showDirectoryPicker({mode:"readwrite"});await e.requestPermission({writable:!0}),x=await e.getDirectoryHandle(j(P),{create:!0})}const p=await o(),w="downloading_collection_"+P,y="stopping_downloading_collection_"+P;Z.loading({key:w,content:Y({en:"Downloading",vi:"Đang tải"})+"...",duration:0});const b=e?[]:ge?[...me]:[...ae];let k=0,C=0,D=0,T=0,$=0,I=!0,N=!1,F=!1,V=!0,M=[],R=[];for(;!N&&V;){if(!ge&&$>=b.length-O){const n=await(null==B?void 0:B(b,I?e:void 0)),t=u(b,n,L);!(null==t?void 0:t.length)&&I&&e&&ee.open({type:"error",message:Y({en:"No data at from your cursor",vi:"Không có dữ liệu tại vị trí bạn nhập"})+e+" ("+P+")",description:Y({en:"Will download from start",vi:"Sẽ tải từ đầu"}),duration:0}),I=!1,console.log(t),(null==t?void 0:t.length)?b.push(...t):V=!1}const t=b.slice($,$+O);if(!t.length)break;const i=Math.min(O,t.length),o=Array.from({length:i},(()=>""));await Promise.all(t.map((async(e,t)=>{try{let s=await E(e,k);s||(s=[]),Array.isArray(s)||(s=[s]);let d=!1;for(let e=0;e<s.length&&!N;e++){const{url:c,name:h}=s[e];if(!c)continue;const u=r+$+t+"_"+e+"_"+h;if(s[e].name=u,o[t]="thread "+(t+1)+": item "+(k+t+1)+(s.length>1?` (${e+1}/${s.length})`:""),N||F||Z.loading({key:w,content:n.jsxs("span",{children:[`${Y({en:"Downloading",vi:"Đang tải"})}... ${C}`,D?n.jsxs(n.Fragment,{children:[Y({en:"Failed",vi:"Lỗi"}),": $",D," ",n.jsx("br",{})]}):"",n.jsx("br",{}),P,n.jsx("br",{}),n.jsx("i",{children:Y({en:"Click to stop",vi:"Bấm để dừng"})}),n.jsx("br",{}),n.jsx(S,{}),o.map(((e,t)=>n.jsxs("span",{style:{width:"100%",textAlign:"left",display:"block"},children:[e,n.jsx("br",{})]},t))),n.jsx("br",{})]}),duration:0,onClick:()=>{N=!0,Z.loading({key:y,content:Y({en:"Stopping...",vi:"Đang dừng..."}),duration:0})}}),R.push(c),"file"===a)try{const e=await v({url:c,checkAbortFn:()=>N}),n=await e.blob(),t=await x.getFileHandle(u,{create:!0}),i=await t.createWritable();await i.write(n),await i.close()}catch(i){console.log(i),N||(p?(await l({url:c,conflictAction:"overwrite",filename:P+"/"+u}),d=!0):(D++,ee.open({type:"error",message:Y({en:"Download failed",vi:"Lỗi tải"}),description:c})))}C++}d&&T++,k++,M.push({data:s,raw:e}),je((n=>({...n,[L(e)]:!0}))),fe((n=>n.filter((n=>L(n)!==L(e)))))}catch(i){D++,Z.error({content:Y({en:"Download failed",vi:"Lỗi tải"})+": "+i.message})}}))),N||($+=t.length)}if(F=!0,"json"===a||"link"===a){const e=P+("json"===a?".json":".txt"),n="json"===a?JSON.stringify(M,null,4):R.join("\n");i((()=>import("./file-download-B-gszDlL.js").then((e=>e.f))),__vite__mapDeps([11,1,2]),import.meta.url).then((t=>{t.default(n,e)}))}let H="";try{H=(null==A?void 0:A(b[$]||b[$-1]))||""}catch(K){console.error(K)}Z.destroy(w),Z.destroy(y),T>0&&ee.open({type:"info",message:Y({en:"In Download/ folder",vi:"Trong folder Download/"})+": "+T,description:Y({en:"Files that cannot be normal downloaded, will be force download into default Download folder of your browser",vi:"Những file không thể tải bình thường, sẽ được tải vào trong folder Download/ mặc định của trình duyệt"}),duration:0,btn:n.jsx(t,{onClick:s,children:Y({en:"Show Download/ folder",vi:"Mở folder Download/"})})}),ee.open({type:"success",message:Y(N?{en:"Download stopped",vi:"Đã dừng tải"}:{en:"Download finished",vi:"Tải xong"}),description:n.jsxs("ul",{children:[n.jsx("li",{children:Y({en:"Folder name",vi:"Tên folder"})+": "+P}),n.jsx("li",{children:Y({en:"Downloaded",vi:"Đã tải"})+": "+C}),T>0&&n.jsx("li",{children:Y({en:"In Download/ folder",vi:"Trong folder Download/"})+": "+T}),n.jsx("li",{children:Y({en:"Last index",vi:"Vị trí cuối"})+": "+($+r)}),n.jsx("li",{children:Y({en:"Last cursor",vi:"Con trỏ cuối"})+": "+H})]}),duration:0,btn:n.jsx(m,{direction:"horizontal",children:N&&!ge?n.jsx(t,{onClick:()=>De({downloadType:a,fromCursor:H,startIndex:$+r}),children:Y({en:"Continue download",vi:"Tiếp tục tải"})}):null})}),localStorage.setItem(P+"_fromCursor",H+""),localStorage.setItem(P+"_startIndex",$+r+"")},Te=ge&&(null==me?void 0:me.length)>0?me:ae,$e=ge&&me.length?me.length:Y({en:"all",vi:"tất cả"}),Ie=ge&&me.length?n.jsx(f.Text,{type:"secondary",children:Y({en:`Download ${$e} selected items`,vi:`Tải ${$e} mục đang chọn`})}):n.jsx(f.Text,{type:"secondary",children:Y({en:"Auto load more until nothing left",vi:"Tự động tải thêm cho đến khi không còn"})}),Se={items:[{key:"selectMode",label:n.jsxs(m,{align:"center",children:[n.jsxs(k,{placement:"left",title:Y({en:"Select mode",vi:"Chọn để tải"}),content:Y({en:n.jsxs("ul",{children:[n.jsxs("li",{children:[n.jsxs(g,{color:"success",children:[n.jsx("i",{className:"fa-solid fa-check"})," ON"]}),"Download only selected items"]}),n.jsxs("li",{children:[n.jsxs(g,{color:"warning",children:[n.jsx("i",{className:"fa-solid fa-xmark"})," OFF"]}),"Download all items (auto load more)"]}),n.jsxs("li",{children:["Shortcut:"," ",n.jsx(g,{style:{borderRadius:"9999px"},children:"Shift"})]})]}),vi:n.jsxs("ul",{children:[n.jsxs("li",{children:[n.jsxs(g,{color:"success",children:[n.jsx("i",{className:"fa-solid fa-check"})," BẬT"]}),"Chỉ tải những mục đã chọn"]}),n.jsxs("li",{children:[n.jsxs(g,{color:"warning",children:[n.jsx("i",{className:"fa-solid fa-xmark"})," TẮT"]}),"Tải tất cả (tự động tải thêm)"]}),n.jsxs("li",{children:["Phím tắt:"," ",n.jsx(g,{style:{borderRadius:"9999px"},children:"Shift"})]})]})}),children:[n.jsx(C,{checked:ge})," ",Y({en:"Select mode",vi:"Chọn để tải"})]}),n.jsxs(m.Compact,{children:[me.length!==ae.length&&n.jsx(x,{title:Y({en:"Select all",vi:"Chọn tất cả"}),children:n.jsx(t,{onClick:e=>{e.preventDefault(),e.stopPropagation(),fe(ae),xe(!0)},icon:n.jsx("i",{className:"fa-solid fa-check"}),children:ae.length})}),(null==me?void 0:me.length)>0&&n.jsx(x,{title:Y({en:"Clear selected",vi:"Bỏ chọn"}),children:n.jsx(t,{onClick:e=>{e.preventDefault(),e.stopPropagation(),fe([])},icon:n.jsx("i",{className:"fa-solid fa-trash"}),children:null==me?void 0:me.length})})]})]})},{type:"divider"},{key:"file",label:n.jsxs(m,{direction:"vertical",children:[n.jsx(f.Text,{strong:!0,children:"📁 "+Y({en:`Download ${$e} files (photo/video)`,vi:`Tải ${$e} files (ảnh/video)`})}),Ie]})},{key:"link",label:n.jsxs(m,{direction:"vertical",children:[n.jsx(f.Text,{strong:!0,children:"🔗 "+Y({en:`Export ${$e} links`,vi:`Xuất ${$e} links`})}),Ie]})},{key:"json",label:n.jsxs(m,{direction:"vertical",children:[n.jsx(f.Text,{strong:!0,children:"📄 "+Y({en:`Export ${$e} to .json`,vi:`Xuất ${$e} ra .json`})}),Ie]})},{type:"divider"},...V.map((({key:e,label:n,labelFn:t})=>({key:e,label:(null==t?void 0:t(Te))||n})))],onClick:e=>{var n;if(V.length&&V.some((n=>n.key===e.key))){const t=null==(n=V.find((n=>n.key===e.key)))?void 0:n.onClick;t&&t(Te,re)}else"selectMode"===e.key?xe(!ge):!ge||(null==me?void 0:me.length)>0?De({downloadType:e.key}):Z.info(Y({en:"Please select media to download",vi:"Vui lòng chọn ảnh/video muốn tải"}))}};return n.jsxs(m,{direction:"vertical",align:"center",style:{width:"100%"},className:U?"collection-center":"",children:[null==H?void 0:H(ae),n.jsxs(m.Compact,{style:{justifyContent:"center"},children:[B&&n.jsx(t,{onClick:()=>ye(!0),icon:n.jsx("i",{className:"fa-solid fa-rotate-right"}),disabled:ie,loading:ie,children:Y(ie?{en:"Loading..",vi:"Đang tải.."}:{en:"Refresh",vi:"Làm mới"})}),E&&n.jsx(D,{disabled:!ae.length,menu:Se,placement:"bottomCenter",children:n.jsx(t,{type:"primary",icon:n.jsx("i",{className:"fa-solid fa-download"}),children:ge?me.length?Y({en:"Download",vi:"Tải xuống"})+` (${me.length})`:Y({en:"Select to download...",vi:"Chọn để tải..."}):Y({en:"Download all",vi:"Tải xuống"})})}),null==R?void 0:R(ae,ie)]}),Q&&n.jsx(T.Search,{value:he,onChange:e=>ue(e.target.value+""),placeholder:Y(X(ae)),allowClear:!0,style:{minWidth:300}}),n.jsx($,{grid:{gutter:10,column:"vertical"===M?1:void 0},pagination:!!W&&{showTotal:(e,n)=>Y({en:`Showing ${n[0]}-${n[1]} / Total ${e}`,vi:`Hiển thị ${n[0]}-${n[1]} / Tổng ${e}`}),defaultPageSize:G,showSizeChanger:!0,position:"bottom",align:"center"},dataSource:ve,renderItem:Ce,rowKey:L,loadMore:W||J?null:n.jsx(I,{justify:"center",children:n.jsxs(m,{direction:"vertical",align:"center",children:[n.jsxs(g,{color:"success",children:[Y({en:"Total",vi:"Tổng"}),": ",ae.length]}),we&&!W?n.jsx("div",{style:{height:500},children:n.jsx(p,{tip:Y({en:"Loading",vi:"Đang tải"}),children:n.jsx("div",{style:{padding:50,background:"rgba(0, 0, 0, 0.05)",borderRadius:4}})})}):n.jsx(x,{placement:"bottom",title:Y({en:n.jsxs(n.Fragment,{children:["🤔 If FB AIO does not load more automatically",n.jsx("br",{}),"👀 But you think there are more data",n.jsx("br",{}),"👆 Please click this button"]}),vi:n.jsxs(n.Fragment,{children:["🤔 Nếu FB AIO không tự động tải thêm nữa",n.jsx("br",{}),"👀 Nhưng bạn tin vẫn còn dữ liệu",n.jsx("br",{}),"👆 Hãy bấm nút này"]})}),mouseEnterDelay:.5,children:n.jsx(t,{icon:n.jsx("i",{className:"fa-solid fa-play"}),onClick:()=>ke(),loading:ie,children:Y({en:"Load more",vi:"Tải thêm"})})})]})})})]})}export{P as DisplayType,_ as DownloadType,N as default};
