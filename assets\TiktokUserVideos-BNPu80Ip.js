const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./VideoViewer-DSZVlhxl.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./videos-D2LbKcXH.js","./MyApp-DW5WH4Ub.js"])))=>i.map(i=>d[i]);
import{r as e,bh as t,b0 as i,b1 as o,b5 as a,aN as s,bp as n}from"./index-Cak6rALw.js";import{genParams as r,extractItemList as l}from"./core-Bg7H-yRQ.js";import{u as d,d as c,aG as m,B as u,c as h,S as g,e as p,f as v,aS as x,aQ as j,T as f,A as w,s as k,I as y,t as b,x as I,v as T,aK as C,l as N,aL as F,aO as $}from"./MyApp-DW5WH4Ub.js";import q from"./MyTable-DnjDmN7z.js";import R from"./useCacheState-CAnxkewm.js";import S from"./WordStatisticButton-D2UxB2M4.js";import{waitWinForReady as _,runFnInWin as B}from"./window-pPC-ycGO.js";import{E as V}from"./ExportButton-CbyepAf_.js";import{checkVIP as D}from"./useVIP-D5FAMGQQ.js";import{I as P}from"./index-CDSnY0KE.js";import{C as L}from"./index-D7dTP8lW.js";import{L as O}from"./index-jrOnBmdW.js";import{R as A}from"./row-BMfM-of4.js";import"./index-ByRdMNW-.js";import"./index-BzMPigdO.js";import"./DownOutlined-B-JcS-29.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./BadgeWrapper-BCbYXXfB.js";import"./index-Bp2s1Wws.js";import"./index-Bx3o6rwA.js";import"./index-IxNfZAD-.js";import"./sweetalert2.esm.all-BZxvatOx.js";import"./getIds-DAzc-wzf.js";import"./col-CzA09p0P.js";const E=o((()=>s((()=>import("./VideoViewer-DSZVlhxl.js")),__vite__mapDeps([0,1,2,3,4]),import.meta.url)),{fallback:y}),M="1.8",U=e=>!!e.image,W=e=>!U(e)&&!!e.video;function H({target:o}){const{ti:s}=d(),{message:y,notification:H}=c(),[z,J]=R("TiktokUserVideos."+o.uniqueId,[]),[K,G]=R("TiktokUserVideos.loading",!1),Q=e.useRef(null),X=t.version>=M,Y=async()=>{const e="TiktokUserVideos.onReload";b(e),G(!0),J([]),y.loading({key:e,content:s({en:"Loading...",vi:"Đang tải..."}),duration:0});try{Q.current&&Q.current.close();const t=window.open("https://www.tiktok.com/@"+o.uniqueId,"_blank","width=800,height=600");t.blur(),window.focus(),Q.current=t,y.loading({key:e,content:s({en:"Waiting tiktok...",vi:"Đang chờ tiktok..."}),duration:0}),await _(t);let a="",n=0,d=!1;for(;!d&&!t.closed;){y.loading({key:e,content:i.jsxs(i.Fragment,{children:[s({en:"Fetching videos...",vi:"Đang tải videos..."})+n,i.jsx("br",{}),i.jsx("i",{children:s({en:"Click to stop",vi:"Bấm để dừng"})})]}),duration:0,onClick:()=>{d=!0}});const c=r({secUid:o.secUid,count:35,cursor:a,coverFormat:"2"}),m="https://www.tiktok.com/api/post/item_list/?"+Object.entries(c).map((e=>e.join("="))).join("&"),u=await B({win:t,fnPath:"fetch",params:[m,{credentials:"include"}],checkAbort:()=>d}),h=I(u);console.log(h);const g=l(null==h?void 0:h.itemList);if(!g.length)break;if(n+=g.length,J((e=>[...e,...g])),!(null==h?void 0:h.hasMore))break;a=null==h?void 0:h.cursor,await T(500)}t.closed?y.warning({content:s({en:"Tiktok window closed before done",vi:"Tab tiktok bị tắt trước khi chạy xong"})}):t.close(),H.open({type:"success",message:s({en:"Fetch tiktok videos completed",vi:"Tìm videos tiktok xong"}),description:o.uniqueId+" - "+n+" videos",duration:0})}catch(t){console.log(t),H.open({type:"error",message:s({en:"Failed to fetch tiktok videos",vi:"Lỗi tìm videos tiktok"}),description:o.uniqueId+" - "+t.message,duration:0})}finally{y.destroy(e),G(!1)}},Z=e.useMemo((()=>{const e=z.filter(U).length;return[{title:"#",dataIndex:"index",key:"index",width:50},{title:`${z.filter(W).length} Video + ${e} Photo`,key:"video",render(e,t,o){var a;return i.jsx(u.Ribbon,{text:"Photo",color:"geekblue-inverse",style:{opacity:t.image?1:0},children:i.jsx(P,{src:(null==(a=t.image)?void 0:a.cover)||t.video.cover,fallback:t.video.cover,style:{width:180,height:200,objectFit:"cover",borderRadius:10},preview:{destroyOnClose:!0,imageRender:t.image?()=>{var e;return i.jsx(L,{style:{maxWidth:"90vw",maxHeight:"90vh",alignItems:"center",overflowY:"auto",overflowX:"hidden",padding:10,borderRadius:10},children:i.jsx(O,{grid:{gutter:10},style:{width:"100%"},dataSource:null==(e=t.image)?void 0:e.images,renderItem:(e,t)=>i.jsx(O.Item,{children:i.jsx(P,{src:e,style:{objectFit:"cover",width:250,height:250,borderRadius:10}})}),rowKey:e=>e})})}:()=>i.jsx(E,{info:t.video,defaultVariantTitle:{en:"Best",vi:"Tốt nhất"},style:{maxWidth:"90vw",maxHeight:"90vh"}}),toolbarRender:()=>null}})})},filters:[{text:"Video",value:"video"},{text:"Photo",value:"photo"}].map((e=>({text:e.text+" ("+z.filter((t=>"photo"===e.value&&U(t)||"video"===e.value&&W(t))).length+")",value:e.value}))),onFilter:(e,t)=>"photo"===e&&U(t)||"video"===e&&W(t),align:"center",width:180},{title:s({en:"Desc",vi:"Nội dung"}),dataIndex:"desc",key:"desc",render:(e,t,o)=>i.jsx(h.Text,{style:{maxWidth:250},children:t.desc}),width:250},{title:new Set(z.map((e=>{var t;return null==(t=e.music)?void 0:t.title}))).size+s({en:" Music",vi:" Nhạc"}),dataIndex:"music",key:"music",render(e,t,o){const{id:a,author:s,cover:n,original:r,playUrl:l,title:d}=t.music||{};return a?i.jsxs(g,{direction:"vertical",size:3,children:[i.jsx(P,{src:n,style:{width:50,height:50,borderRadius:10}}),i.jsxs(h.Text,{children:[s," ",d," ",r," ",!!l&&i.jsx(h.Link,{href:l,target:"_blank",children:i.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})})]})]}):"-"},filters:m(z.map((e=>e.music)),"title"),onFilter:(e,t)=>{var i;return(null==(i=t.music)?void 0:i.title)===e},filterSearch:!0,width:150},{title:s({en:"Created",vi:"Ngày đăng"}),dataIndex:"createTime",key:"createTime",render:(e,t,o)=>i.jsxs(g,{direction:"vertical",size:3,children:[i.jsx(h.Text,{children:p(t.createTime)}),i.jsx(h.Text,{type:"secondary",children:v(t.createTime)})]}),sorter:(e,t)=>e.createTime-t.createTime,width:120},{title:i.jsx("i",{className:"fa-solid fa-play fa-lg"}),dataIndex:"views",key:"views",render:(e,t,i)=>x(t.stats.playCount),sorter:(e,t)=>e.stats.playCount-t.stats.playCount,width:70},{title:i.jsx("i",{className:"fa-solid fa-heart fa-lg"}),dataIndex:"heart",key:"heart",render:(e,t,i)=>x(t.stats.diggCount),sorter:(e,t)=>e.stats.diggCount-t.stats.diggCount,width:70},{title:i.jsx("i",{className:"fa-solid fa-comment fa-lg"}),dataIndex:"comments",key:"comments",render:(e,t,i)=>x(t.stats.commentCount),sorter:(e,t)=>e.stats.commentCount-t.stats.commentCount,width:70},{title:i.jsx("i",{className:"fa-solid fa-bookmark fa-lg"}),dataIndex:"collect",key:"collect",render:(e,t,i)=>x(t.stats.collectCount),sorter:(e,t)=>e.stats.collectCount-t.stats.collectCount,width:70},{title:i.jsx("i",{className:"fa-solid fa-share fa-lg"}),dataIndex:"shares",key:"shares",render:(e,t,i)=>x(t.stats.shareCount),sorter:(e,t)=>e.stats.shareCount-t.stats.shareCount,width:70},{title:i.jsx("i",{className:"fa-solid fa-clock fa-lg"}),dataIndex:"length",key:"length",render:(e,t,i)=>j(t.video.duration),sorter:(e,t)=>e.video.duration-t.video.duration,width:70},{title:s({en:"Action",vi:"Hành động"}),dataIndex:"action",key:"action",render:(e,t,o)=>i.jsx(g.Compact,{children:i.jsx(f,{title:s({en:"Open on tiktok",vi:"Mở trong tiktok"}),children:i.jsx(a,{icon:i.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"}),onClick:()=>{var e;window.open(`https://www.tiktok.com/@${null==(e=t.author)?void 0:e.uniqueId}/video/${t.video.id}`,"_blank")}})})}),width:90}]}),[z]);return i.jsx("div",{style:{display:"flex",flexDirection:"column",width:"100%",height:"100%"},children:X?location.hostname.includes(".github.io")?i.jsx(w,{message:i.jsxs(i.Fragment,{children:[i.jsx(h.Text,{children:s({en:i.jsxs(i.Fragment,{children:["🚧 Please open"," ",i.jsxs("a",{href:"https://fb-aio.web.app/#/bulk-downloader",target:"_blank",children:[" ","This website"," ",i.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})]})," ","to download bulk Tiktok."]}),vi:i.jsxs(i.Fragment,{children:["🚧 Vui lòng mở"," ",i.jsxs("a",{href:"https://fb-aio.web.app/#/bulk-downloader",target:"_blank",children:[" ","Trang web này"," ",i.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})]})," ","để tải hàng loạt Tiktok."]})})})," ",i.jsxs("a",{href:"https://www.facebook.com/groups/fbaio/posts/1583865772268105",target:"_blank",children:[s({en:"Reason?",vi:"Lý do?"})," ",i.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})]})]}),type:"error",style:{alignSelf:"center",marginBottom:10}}):X||(null==z?void 0:z.length)?i.jsxs(A,{justify:"center",gutter:[10,10],children:[i.jsx(w,{type:"info",showIcon:!0,message:s({en:'Click "Reload" button to start find videos',vi:'Bấm nút "Tải lại" để bắt đầu tìm videos'})}),i.jsx(q,{searchable:!0,selectable:!0,data:z.map(((e,t)=>({...e,index:t+1}))),columns:Z,keyExtractor:e=>e.id,onSearchRow:(e,t)=>{var i,o;return[t.id,t.desc,null==(i=t.author)?void 0:i.nickname,null==(o=t.author)?void 0:o.uniqueId].find((t=>!!t&&k(e,t)))},renderTitle:e=>{const t=(null==e?void 0:e.length)?e:z,r=t.filter((e=>e.video&&!e.image));return i.jsxs(i.Fragment,{children:[i.jsx(a,{type:"primary",icon:i.jsx("i",{className:"fa-solid fa-rotate-right"}),onClick:Y,loading:K,children:s({en:"Reload",vi:"Tải lại"})}),i.jsx(V,{title:s({en:"Download",vi:"Tải xuống"}),data:t,options:[{key:"videos",label:r.length+" videos",onClick:e=>{(async e=>{if(!(await D()))return;const t="TiktokUserVideos:downloadVideos";b(t);const a=e.map(((e,t)=>({name:t+"_"+C(e.desc||e.video.id||e.id)+".mp4",urls:(e.video.variants||[]).map((e=>[e.source,e.urls].flat().filter(Boolean))).flat().filter(Boolean)}))).filter((e=>e.urls.length));if(!a.length)return!1;const r=await window.showDirectoryPicker({mode:"readwrite"});await r.requestPermission({writable:!0});const l=await r.getDirectoryHandle(C(null==o?void 0:o.uniqueId),{create:!0}),d=t;y.loading({key:d,content:s({en:"Downloading...",vi:"Đang tải..."}),duration:0});let c=!1,m=0,u=0;const h=Math.min(5,a.length),g=Array.from({length:h},(()=>"")),p=a.map(((e,t)=>async(t,i)=>{for(let a of e.urls)try{g[i]=N(e.name,30);const t=await n(a,!0);a!==t&&console.log("redirected",a,t);const o=await F({url:t,checkAbortFn:()=>c}),s=o.headers.get("content-type");if("video/mp4"!==s)throw new Error("Invalid content type: "+s);const r=await o.blob(),d=await l.getFileHandle(e.name,{create:!0}),u=await d.createWritable();return await u.write(r),await u.close(),m++,!0}catch(o){console.log(o,e)}return u++,!1})),{start:v,stop:x}=$(h,p,1e3),j=setInterval((()=>{y.loading({key:d,content:i.jsxs(i.Fragment,{children:[s({en:`Downloading... ${m}/${a.length}. Failed: ${u}`,vi:`Đang tải... ${m}/${a.length}. Lỗi: ${u}`}),i.jsx("br",{}),i.jsx("i",{children:s({en:"Click to stop",vi:"Bấm để dừng"})}),i.jsx("br",{}),g.map(((e,t)=>i.jsxs("div",{style:{textAlign:"left"},children:[e,i.jsx("br",{})]},t)))]}),duration:0,onClick:()=>c=!0}),c&&x()}),500);await v(),clearInterval(j),y.destroy(d),H.open({type:"success",message:s(c?{en:"Stopped download tiktok videos",vi:"Đã dừng tải video tiktok"}:{en:"Downloaded tiktok videos",vi:"Tải xong video tiktok"}),description:`${null==o?void 0:o.uniqueId} (${m}/${a.length})`,duration:0})})(e)}},{key:"formatted json",label:".json (formatted)",prepareData:e=>({fileName:(null==o?void 0:o.uniqueId)+".json",data:JSON.stringify(e.map((e=>({...e,raw:void 0,video:{...e.video,raw:void 0}}))),null,4)})},{key:"raw json",label:".json (raw)",prepareData:e=>({fileName:(null==o?void 0:o.uniqueId)+".raw.json",data:JSON.stringify(e.map((e=>e.raw)),null,4)})}]}),i.jsx(S,{text:z.map((e=>e.desc)).join("\n")})]})}})]}):null:i.jsx(w,{message:s({vi:`Sắp có trong phiên bản v${M} (Bạn đang dùng v${t.version})`,en:`Coming soon in version v${M} (Current v${t.version})`}),type:"warning",showIcon:!0,style:{alignSelf:"center",marginBottom:10}})})}export{H as default};
