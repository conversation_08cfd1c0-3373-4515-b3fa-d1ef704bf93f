import{r as i,b0 as t,b5 as e}from"./index-Cak6rALw.js";import{l as r}from"./MyApp-DW5WH4Ub.js";import o from"./Collection-BYcChfHL.js";import{g as s,a}from"./highlights--x8Pz3Fi.js";import{getInstaUrlFromId as n}from"./core-Dw7OsFL6.js";import{L as l}from"./index-jrOnBmdW.js";import{I as m}from"./index-CDSnY0KE.js";import"./index-ByRdMNW-.js";import"./useCacheState-CAnxkewm.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-PbLYNhdQ.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CC5caqt_.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./addEventListener-C4tIKh7N.js";function p({target:p,onOpenHighlight:d}){const j=i.useCallback((async(i=[])=>{if(!(null==p?void 0:p.id))return;if(i.length)return[];return await s(p.id)}),[p]),h=i.useCallback((i=>{var o,s;return t.jsxs(l.Item,{className:"show-on-hover-trigger",children:[t.jsx(m,{src:i.cover,width:100,height:100,style:{objectFit:"cover",borderRadius:"50%",cursor:"pointer"},preview:!1,onClick:()=>{null==d||d(i)}}),t.jsx("a",{href:"https://www.instagram.com/stories/highlights/"+i.id.split(":")[1],target:"_blank",style:{display:"block",wordWrap:"break-word",maxWidth:100,padding:5,fontWeight:"bold",textAlign:"center",fontSize:"1em"},title:i.title,children:r(i.title,30)}),t.jsx(e,{type:"default",icon:t.jsx("i",{className:"fa-solid fa-up-right-from-square"}),style:{position:"absolute",top:0,right:0},className:"show-on-hover-item",target:"_blank",href:n("stories/highlights/"+(null==(s=null==(o=i.id)?void 0:o.split(":"))?void 0:s[1]))})]})}),[]),c=i.useCallback((async(i,t)=>(await a(i.id)).map((i=>{const t=!!i.video;return{url:t?i.video:i.image,name:i.id+(t?".mp4":".jpg")}}))),[]);return t.jsx(o,{collectionName:(null==p?void 0:p.username)+" - IG Highlights",fetchNext:j,renderItem:h,downloadItem:c,rowKey:i=>i.id})}export{p as default};
