import{r as e,b0 as n,b5 as t}from"./index-Cak6rALw.js";import{waitWinForReady as r,runFnInWin as s}from"./window-pPC-ycGO.js";import a from"./useCacheState-CAnxkewm.js";import{u as i,d as o,t as l,c,S as u,ah as d,b as f}from"./MyApp-DW5WH4Ub.js";import{S as m}from"./Screen-Buq7HJpK.js";import{D as h}from"./index-CC5caqt_.js";import{R as p}from"./row-BMfM-of4.js";import{I as g}from"./index-CDSnY0KE.js";import{I as w}from"./index-Bg865k-U.js";import"./col-CzA09p0P.js";import"./useBreakpoint-CPcdMRfj.js";import"./EyeOutlined-CNKPohhw.js";import"./addEventListener-C4tIKh7N.js";import"./SearchOutlined--mXbzQ68.js";const k=[{id:"Duck Race",url:"https://www.online-stopwatch.com/duck-race/",img:""+new URL("duck--6fNLnXb.png",import.meta.url).href},{id:"Emoji Race",url:"https://www.online-stopwatch.com/emoji-race-timer/",img:""+new URL("emoji-Dr6CgfhJ.png",import.meta.url).href},{id:"Wheel Name Picker",url:"https://www.online-stopwatch.com/random-name-pickers/name-picker-wheel/",img:""+new URL("wheel-lFKZS5xZ.png",import.meta.url).href}];function j(){const{ti:j}=i(),{message:R}=o(),[b,D]=e.useState(!1),[S,_]=e.useState(null),[E,C]=a("Duckrace.win",null),[I,N]=a("Duckrace.tags",[]),[H,q]=e.useState(!1),[A,L]=e.useState(""),[T,P]=e.useState(-1),[M,O]=e.useState(""),z=e.useRef(null),B=e.useRef(null);e.useEffect((()=>{if(E&&S){const e="Duckrace:processHack"+Date.now();R.loading({key:e,content:"Waiting for load...",duration:0}),r(E).then((()=>{R.loading({key:e,content:"Hacking...",duration:0}),s({win:E,fnPath:"window.eval",params:[v(I)]}).then((()=>{R.success({key:e,content:"Hacked duckrace: "+((null==I?void 0:I.length)?I:"random")})}))})).catch((n=>{R.error({key:e,content:"Hack failed: "+n.message})}))}}),[I,E,S]),e.useEffect((()=>{var e;H&&(null==(e=z.current)||e.focus())}),[H]),e.useEffect((()=>{var e;null==(e=B.current)||e.focus()}),[M]),e.useEffect((()=>{if(!E)return;const e=setInterval((()=>{E&&E.closed&&C(null)}),1e3);return()=>{clearInterval(e)}}),[E]),e.useEffect((()=>{l("HackDuckRace:onLoad")}),[]);const F=()=>{q(!0)},J=e=>{L(e.target.value)},U=()=>{if(A){const e=A.split(",").map((e=>e.trim())).filter((e=>e.length>0&&!I.includes(e)));e.length&&N([...I,...e])}q(!1),L("")},W=e=>{O(e.target.value)},K=()=>{const e=[...I];e[T]=M,N(e),P(-1),O("")};return n.jsxs(m,{mode:"center",title:j({en:"Hack DuckRace",vi:"Hack DuckRace"}),children:[n.jsxs(c.Text,{children:[j({en:"Cheat the random of Duckrace result",vi:"Kết quả Duckrace nằm trong tay bạn"})," ",n.jsx(c.Link,{href:"https://www.facebook.com/groups/fbaio/posts/1582618815726134",target:"_blank",children:n.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})})]}),E?n.jsxs(u,{direction:"vertical",align:"center",children:[n.jsxs(n.Fragment,{children:[n.jsxs(c.Text,{children:[j({en:"Enter result you want (name or number)",vi:"Nhập kết quả mong muốn (tên hoặc số thứ tự)"}),":"]}),n.jsxs(u,{size:0,children:[I.map(((e,t)=>T===t?n.jsx(w,{ref:B,size:"small",style:y,value:M,onChange:W,onBlur:K,onPressEnter:K},e):n.jsx(f,{closable:!0,style:{userSelect:"none"},onClose:()=>(e=>{const n=I.filter((n=>n!==e));console.log(n),N(n)})(e),children:n.jsx("span",{onDoubleClick:n=>{0!==t&&(P(t),O(e),n.preventDefault())},children:e})},e))),H?n.jsx(w,{ref:z,type:"text",size:"small",style:y,value:A,onChange:J,onBlur:U,onPressEnter:U}):n.jsx(f,{style:x,icon:n.jsx("i",{className:"fa-solid fa-plus"}),onClick:F,children:j({en:"Add result",vi:"Thêm kết quả"})})]})]}),n.jsx(h,{}),n.jsx(t,{danger:!0,icon:n.jsx("i",{className:"fa-solid fa-close"}),iconPosition:"end",onClick:()=>{E.close(),C(null),l("HackDuckRace:closeDuckRace")},children:"Close duckrace"})]}):b?n.jsxs(u,{children:[n.jsx(c.Text,{children:j({en:"Openning Duckrace...",vi:"Đang mở Duckrace..."})}),n.jsx(d,{})]}):n.jsxs(n.Fragment,{children:[n.jsx(c.Text,{children:j({en:"Select game to start",vi:"Chọn trò bạn muốn chơi"})}),n.jsx(p,{align:"middle",justify:"center",children:k.map((e=>n.jsxs(u,{direction:"vertical",align:"center",style:{margin:8},children:[n.jsxs("div",{className:"show-on-hover-trigger",style:{position:"relative"},children:[n.jsx(g,{src:e.img,onClick:async()=>{var n;E&&(null==(n=null==E?void 0:E.close)||n.call(E)),D(!0);const t=window.open(e.url,"_blank");await r(t),_(e),C(t),D(!1),l("HackDuckRace:openDuckRace")},preview:!1,width:150,height:150,style:{borderRadius:10,cursor:"pointer"}},e.id),n.jsx("div",{className:"show-on-hover-item",style:{position:"absolute",top:0,left:0,right:0,bottom:0,background:"rgba(0, 0, 0, 0.5)",display:"flex",justifyContent:"center",alignItems:"center",pointerEvents:"none"},children:n.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square "})})]}),n.jsx(c.Text,{children:e.id})]},e.id)))})]})]})}const x={height:22,borderStyle:"dashed"},y={width:64,height:22,marginInlineEnd:8,verticalAlign:"top"};function v(e=[]){return`(() => {\n        let targets = ${JSON.stringify(e)};\n\n        let wins = Array.from(document.querySelectorAll("iframe")).map(\n          (_) => _?.contentWindow\n        );\n\n        [window, ...wins]\n          .filter((_) => _)\n          .forEach((win) => {\n            if (win.location.href.includes('html5/random-name-pickers') && win.Math.specialRandom) {\n              if(!win.fbaio_duckRace_specialRandom) win.fbaio_duckRace_specialRandom = win.Math.specialRandom;\n\n              win.Math.specialRandom = function () {\n                let result = win.fbaio_duckRace_specialRandom.apply(this, arguments);\n                if (win.holder_array?.length > 0) {\n                  for (let target of targets) {\n                    let found = win.holder_array.findIndex((i) => i == target);\n                    if (found >= 0) result = found;\n                  }\n                }\n                console.log("specialRandom", this, result);\n                return result;\n              };\n            } else {\n                if (!win.fbaio_duckRace_originalShuffle) {\n                    win.fbaio_duckRace_originalShuffle = win.Array.prototype.shuffle;\n                }\n                win.Array.prototype.shuffle = function () {\n                    const result = win.fbaio_duckRace_originalShuffle.apply(\n                    this,\n                    arguments\n                    );\n                    console.log(result, win);\n                    if (result?.[0]?.instance) {\n                    for (let target of targets) {\n                        let targetIndex = result.findIndex(\n                        (i) => i?.name === target || i?.number == target\n                        );\n                        if (targetIndex >= 0) {\n                        let temp = result[0];\n                        result[0] = result[targetIndex];\n                        result[targetIndex] = temp;\n                        break;\n                        }\n                    }\n                    }\n                    console.log("shuffle", this, result, result[0]);\n                    return result;\n                };\n            }\n          });\n      })();\n      `}export{j as default};
