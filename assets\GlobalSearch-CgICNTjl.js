import{bc as e,r as i,aP as l,b0 as n,b5 as t}from"./index-Cak6rALw.js";import{u as s}from"./react-hotkeys-hook.esm-wjq6pdfC.js";import{u as a,s as r,t as o,S as c,b as d,c as u,T as f,l as p}from"./MyApp-DW5WH4Ub.js";import{f as m}from"./features-BdMou7IV.js";import{M as h}from"./index-Bx3o6rwA.js";import{I as j}from"./index-Bg865k-U.js";import{D as v}from"./index-CC5caqt_.js";import"./PurePanel-BvHjNOhQ.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";const x=[{title:{vi:"Tất cả tính năng",en:"List all features"},icon:"fa-solid fa-location-arrow",children:[{title:{vi:"Xem tất cả tính năng",en:"View all features"},description:{vi:"Xem tất cả tính năng",en:"View all features"},icon:"fa-solid fa-list",link:"/vip",newTab:!1,requireVIP:!1},...m.map((e=>e.header?{divider:!0,title:e.name}:{title:e.name,description:e.description,icon:e.icon,link:e.path,newTab:e.newTab,requireVIP:e.requireVIP}))]},{title:{vi:"Lịch sử cập nhật",en:"Update logs"},icon:"fa-solid fa-circle-up",link:"/updates"},{title:{vi:"Mua VIP",en:"Buy VIP"},icon:"fa-solid fa-crown",link:"/checkout"},{title:{vi:"FAQ: Câu hỏi thường gặp",en:"FAQs"},icon:"fa-solid fa-question",link:"/FAQ"},{title:{vi:"Trợ giúp",en:"Help"},icon:"fa-solid fa-life-ring",link:l.GroupFB,newTab:!0}];function g({style:l={},compact:m=!1}){const g=e(),{ti:k}=a(),[y,b]=i.useState(""),[w,T]=i.useState(!1),[P,S]=i.useState([]),V=i.useRef(null),B=i.useMemo((()=>{var e;let i=x;for(let l of P){let n=null==(e=null==i?void 0:i[l])?void 0:e.children;if(!n)break;i=n}return y?i.filter((e=>{var i,l,n,t;return e.divider||r(y,null==(i=e.title)?void 0:i.en)||r(y,null==(l=e.title)?void 0:l.vi)||r(y,null==(n=e.description)?void 0:n.en)||r(y,null==(t=e.description)?void 0:t.vi)})):i}),[y,k,P]);s(["ctrl+k","meta+k"],(()=>{o("GlobalSearch:Shortcut"),T((e=>!e))})),i.useEffect((()=>{w&&(o("GlobalSearch:Open"),setTimeout((()=>{var e;return null==(e=V.current)?void 0:e.focus()}),500))}),[w]);return n.jsxs(c,{style:l,children:[n.jsx(t,{type:"default",icon:n.jsx("i",{className:"fa-solid fa-magnifying-glass"}),onClick:()=>T(!0),style:m?{}:{borderRadius:"9999px",padding:18},children:m?null:n.jsxs(n.Fragment,{children:[k({en:"Search",vi:"Tìm"})," ",n.jsx(d,{style:{borderRadius:"9999px"},children:"⌘ K"})]})}),n.jsxs(h,{open:w,onCancel:()=>T(!1),footer:null,closable:!1,children:[n.jsx(j,{ref:V,placeholder:k({en:"Global search",vi:"Tìm kiếm toàn cục"}),prefix:n.jsx("i",{className:"fa-solid fa-magnifying-glass"}),style:{marginBottom:10},value:y,onChange:e=>b(e.target.value)}),P.length>0&&n.jsx(t,{icon:n.jsx("i",{className:"fa-solid fa-arrow-left"}),onClick:()=>{b(""),S((e=>e.slice(0,e.length-1)))},style:{marginBottom:10},children:k({en:"Back",vi:"Quay lại"})}),B.map(((e,i)=>e.divider?n.jsxs("div",{children:[n.jsx(v,{style:{marginTop:20,marginBottom:0,paddingBottom:0}}),n.jsx(u.Title,{level:5,children:k(e.title)})]},"header"+i):n.jsx(f,{title:e.description?k(e.description):null,placement:"right",children:n.jsxs(t,{type:"text",style:{width:"100%",justifyContent:"space-between"},onClick:()=>{e.children?S((e=>[...e,i])):(e.keepOpen||T(!1),e.link&&(e.newTab?window.open(e.link,"_blank"):g(e.link)))},children:[n.jsxs(c,{children:[n.jsx("i",{className:e.icon}),k(e.title)]}),n.jsxs("div",{children:[e.requireVIP&&n.jsx("i",{className:"fa-solid fa-crown",style:{color:"gold",opacity:.5}}),e.children?n.jsx("i",{className:"fa-solid fa-chevron-right"}):n.jsx(d,{children:p(e.link,30)})]})]},i)},i)))]})]})}export{g as default};
