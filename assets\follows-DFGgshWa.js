import{W as e,y as o,x as i,az as l,X as n}from"./MyApp-DW5WH4Ub.js";var a=(e=>(e.USER="User",e.PAGE="Page",e.GROUP="Group",e))(a||{}),r=(e=>(e.FOLLOWING="Following",e.UNFOLLOWED="UnFollowed",e))(r||{});const d={Following:{en:"Following",vi:"Đang theo dõi"},UnFollowed:{en:"Unfollowed",vi:"Đã bỏ theo dõi"}};async function t(e=""){const n=e?await o({fb_api_req_friendly_name:"CometFeedPreferencesFollowProfileGridRefetchQuery",variables:{after_cursor:e,included_types:["USER","PAGE","GROUP"],num_profiles:30,profile_picture_scale:2,query_substring:""},doc_id:"5052828684764210"}):await o({fb_api_req_friendly_name:"CometFeedPreferencesFollowPageQuery",variables:{included_types:["USER","PAGE","GROUP"],num_profiles:18,profile_picture_scale:2,query_substring:""},doc_id:"4868917013156094"}),a=i(n),{edges:r=[],page_info:d={}}=l(a);return r.map((e=>{var o,i,l,n,a,r;return{type:null==(o=e.node)?void 0:o.__typename,status:"UnFollowed",id:null==(i=e.node)?void 0:i.id,name:null==(l=e.node)?void 0:l.name,avatar:null==(a=null==(n=e.node)?void 0:n.profile_picture)?void 0:a.uri,url:null==(r=e.node)?void 0:r.url,cursor:e.cursor}}))}async function u(l){const a="Group"===l.type?await o({fb_api_req_friendly_name:"CommitGroupSubscribeStatusSubscribeMutation",variables:{input:{subscribe_location:"FEED_SETTINGS",group_id:l.id,actor_id:await e()}},doc_id:"4771392912988139"}):await o({fb_api_req_friendly_name:"CommitActorSubscribeStatusSubscribeMutation",variables:{input:{subscribe_location:"FEED_SETTINGS",subscribee_id:l.id,actor_id:await e()}},doc_id:"7750648234952596"}),r=i(a);return console.log(r),n(r,"id")===l.id}async function s(l){const a="Group"===l.type?await o({fb_api_req_friendly_name:"CommitGroupSubscribeStatusUnsubscribeMutation",variables:{input:{subscribe_location:"FEED_SETTINGS",group_id:l.id,actor_id:await e()}},doc_id:"4841019119329325"}):await o({fb_api_req_friendly_name:"CommitActorSubscribeStatusUnsubscribeMutation",variables:{input:{subscribe_location:"FEED_SETTINGS",unsubscribee_id:l.id,actor_id:await e()}},doc_id:"7531739850199826"}),r=i(a);return console.log(r),n(r,"id")===l.id}async function _(n=""){var a,r,d,t,u,s;const _=await e();if(!n){const e=await o({fb_api_req_friendly_name:"ProfileCometTopAppSectionQuery",variables:{collectionToken:btoa("app_collection:"+_+":**********:33"),feedbackSource:65,feedLocation:"COMET_MEDIA_VIEWER",scale:1,sectionToken:btoa("app_section:"+_+":**********"),userID:_,__relay_internal__pv__CometUFIReactionsEnableShortNamerelayprovider:!1,__relay_internal__pv__FBReelsMediaFooter_comet_enable_reels_ads_gkrelayprovider:!1},doc_id:"8449649991761948"}),n=i(e),c=null==(s=null==(u=null==(t=null==(d=null==(r=null==(a=null==n?void 0:n.data)?void 0:a.node)?void 0:r.all_collections)?void 0:d.nodes)?void 0:t.find((e=>(null==e?void 0:e.id)&&atob(e.id).includes("**********:33"))))?void 0:u.style_renderer)?void 0:s.collection,{edges:v=[],page_info:p={}}=l(c);return v.map((e=>{var o,i,l,n,a,r,d,t,u;return{type:null==(i=null==(o=null==e?void 0:e.node)?void 0:o.node)?void 0:i.__typename,status:"Following",id:null==(n=null==(l=null==e?void 0:e.node)?void 0:l.node)?void 0:n.id,name:null==(r=null==(a=null==e?void 0:e.node)?void 0:a.title)?void 0:r.text,avatar:null==(t=null==(d=null==e?void 0:e.node)?void 0:d.image)?void 0:t.uri,url:null==(u=null==e?void 0:e.node)?void 0:u.url,cursor:p.end_cursor||(null==e?void 0:e.cursor),isFavorite:!1}}))}const c=await o({fb_api_req_friendly_name:"ProfileCometAppCollectionListRendererPaginationQuery",variables:{count:8,cursor:n,scale:1,search:null,id:btoa("app_collection:"+_+":**********:33")},doc_id:"8710009249031910"}),v=i(c);console.log(v);const{edges:p=[],page_info:b={}}=l(v);return p.map((e=>{var o,i,l,n,a,r,d,t,u,s,_;return{type:null==(i=null==(o=null==e?void 0:e.node)?void 0:o.node)?void 0:i.__typename,status:"Following",id:null==(n=null==(l=null==e?void 0:e.node)?void 0:l.node)?void 0:n.id,name:null==(r=null==(a=null==e?void 0:e.node)?void 0:a.title)?void 0:r.text,avatar:null==(t=null==(d=null==e?void 0:e.node)?void 0:d.image)?void 0:t.uri,url:null==(u=null==e?void 0:e.node)?void 0:u.url,cursor:b.end_cursor||e.cursor,isFavorite:!1,desc:(null==(_=null==(s=null==e?void 0:e.node)?void 0:s.subtitle_text)?void 0:_.text)||""}}))}export{r as F,a,d as b,t as c,u as f,_ as g,s as u};
