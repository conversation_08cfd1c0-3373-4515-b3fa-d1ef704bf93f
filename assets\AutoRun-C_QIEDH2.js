import{b3 as e,r as t,b0 as n,c6 as i,bh as o,b5 as a,aN as s,c7 as r,aO as c,bb as d}from"./index-Cak6rALw.js";import{u as l,d as m,t as p,c as h,S as u,A as b,T as f,b as g,B as w,V as k}from"./MyApp-DW5WH4Ub.js";import v from"./useCacheState-CAnxkewm.js";import{B as j}from"./BadgeWrapper-BCbYXXfB.js";import{adsLinkList as _}from"./adsLinkList-C2Suxade.js";import{R as y}from"./row-BMfM-of4.js";import{L as x}from"./index-jrOnBmdW.js";import{a as S}from"./index-QU7lSj_a.js";import{D as A}from"./index-CC5caqt_.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./PurePanel-BvHjNOhQ.js";import"./index-SRy1SMeO.js";import"./move-ESLFEEsv.js";import"./DownOutlined-B-JcS-29.js";import"./SearchOutlined--mXbzQ68.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./index-CxAc8H5Q.js";const F={Facebook:{params:["fbclid","mc_eid"]},Instagram:{params:["igshid"]},Tiktok:{params:["ttclid"]},Twitter:{domains:["twitter.com"],params:["s","t"]},Google:{domains:["google.com"],params:["gclid","glsrc","_ga","sxsrf","source","ei","iflsig","gs_lp","gs_lcrp","sclient","ved","uact","stick","sca_esv"]},Spotify:{domains:["spotify.com","youtube.com","reddit.com"],params:["si","context"]},Youtube:{domains:["youtube.com"],params:["feature","ab_channel","pp","bp","redir_token","event","embeds_referring_euri","source_ve_path"]},Amazon:{domains:["amazon.*"],params:["ref","tag","linkCode","hv(adid|netw|qmt|bmt|dev|locint|locphy|targid)","psc","content-id","pd_rd_(w|p|r|i|wg)"]},Bing:{domains:["bing.com"],params:["toWww","redig","form","qs","sp","ghc","lq","pq","sc","sk","cvid","ghsh","ghacc","ghpl","ccid","id","thid","simid","FORM","ck","ajaxhist","ajaxserp","osid"]},LinkedIn:{domains:["linkedin.com"],params:["trackingId","lipi","midSig","trkEmail","otpToken","refId","midToken","trk","eid","mcid","ePP","ccuid","cid"]},Reddit:{params:["share_id"]},Adobe:{params:["ef_id","s_kwcid"]},Microsoft:{params:["msclkid"]},Pinterest:{params:["epik"]}},T=[{id:"header_all",name:{vi:"Mọi trang web",en:"All websites"},isHeader:!0,icon:n.jsx("i",{className:"fa-solid fa-globe"})},{id:"web_timer",icon:"⏰",name:{vi:"Web timer - Đếm thời gian truy cập internet",en:"Web timer - Count time you spend on internet"},desc:{vi:"Đếm thời gian bạn dành cho từng trang web",en:"Count time you spend on each web page"},contentScript:{id:"web_timer",js:["./scripts/content/web_timer.js"],matches:["<all_urls>"],runAt:"document_start",world:"ISOLATED",allFrames:!0},button:n.jsx(f,{title:k({vi:"Xem biểu đồ",en:"Dashboard"}),children:n.jsx(a,{href:"#/web-timer",type:"default",icon:n.jsx("i",{className:"fa-solid fa-chart-line"})})}),link:"https://www.facebook.com/groups/fbaio/posts/1661830854471596"},{id:"block_open_urls",icon:"🚫",name:{vi:"Chặn click nhầm link quảng cáo",en:"Block click on ads"},desc:{vi:"Hiện cảnh báo khi bạn bấm mở link giả mạo quảng cáo",en:"Show warning when you click on fake ad link"},contentScript:{id:"block_open_urls",js:["./scripts/content/block_open_urls.js"],matches:["<all_urls>"],runAt:"document_start",world:"MAIN",allFrames:!1},link:"https://www.facebook.com/groups/fbaio/posts/1612579296063419",button:n.jsxs(u.Compact,{children:[n.jsx(f,{title:k({vi:"Mở link quảng cáo để thử chức năng",en:"Open ads link to Try this feature"}),children:n.jsx(a,{href:"https://"+_[0],target:"_blank",icon:n.jsx("i",{className:"fa-solid fa-eye"})})}),n.jsx(f,{title:k({vi:"Danh sách link quảng cáo",en:"Ads link list"}),children:n.jsx(a,{type:"default",icon:n.jsx("i",{className:"fa-solid fa-question"}),onClick:async()=>{(await s((async()=>{const{default:e}=await import("./sweetalert2.esm.all-BZxvatOx.js");return{default:e}}),[],import.meta.url)).default.fire({icon:"info",title:k({vi:"Danh sách link quảng cáo",en:"Ads link list"}),html:'<textarea rows="20" style="width: 100%" readonly>'+_.join("\n")+"</textarea>"})}})})]})},{id:"fb_removeFbclid",icon:"🔗",name:{vi:"Xoá tham số theo dõi trên mọi url (fbclid, ttclid, ...)",en:"Delete tracking param from all urls (fbclid, ttclid, ...)"},desc:{vi:"Tự động xoá các tham số theo dõi trên mọi url ("+Object.keys(F).join(", ")+")",en:"Auto remove all tracking params from all urls ("+Object.keys(F).join(", ")+")"},onEnable:async()=>{function e(e){const t=e.match(/\((.*?)\)/);if(t){const n=t[1].split("|"),i=e.split("(")[0];return n.map((e=>i+e.trim()))}return[e]}let t=0,n=[];for(let i in F){let o=1e3+t;t++;let{domains:a=[],params:s}=F[i];a=(null==a?void 0:a.filter(Boolean))||[],n.push({id:o,priority:1,action:{type:"redirect",redirect:{transform:{queryTransform:{removeParams:s.map((t=>e(t))).flat()}}}},condition:{isUrlFilterCaseSensitive:!0,regexFilter:`[?&](${s.join("|")})=`,resourceTypes:["main_frame","sub_frame"],...a.length>0?{requestDomains:a}:{}}})}return console.log(n),await c("chrome.declarativeNetRequest.updateDynamicRules",[{addRules:n,removeRuleIds:n.map((e=>e.id))}]),d("remove_tracking_in_url",n.map((e=>e.id))),console.log("added",n),!0},onDisable:async()=>{const e=await r("remove_tracking_in_url");return console.log("deleted",e),c("chrome.declarativeNetRequest.updateDynamicRules",[{removeRuleIds:e}]),!0},supported:!0,button:n.jsx(f,{title:k({vi:"Danh sách tham số",en:"Tracking params list"}),children:n.jsx(a,{type:"default",icon:n.jsx("i",{className:"fa-solid fa-question"}),onClick:async()=>{(await s((async()=>{const{default:e}=await import("./sweetalert2.esm.all-BZxvatOx.js");return{default:e}}),[],import.meta.url)).default.fire({icon:"info",title:k({vi:"Danh sách tham số",en:"Tracking params list"}),html:'<textarea rows="20" style="width: 100%" readonly>'+JSON.stringify(F,null,2)+"</textarea>"})}})})},{id:"header_fb",name:"Facebook",isHeader:!0,icon:n.jsx("i",{className:"fa-brands fa-facebook"})},{id:"fb_blockSeenStory",icon:"👀",name:{vi:'Chặn "đã xem" trên story Facebook',en:'Block the "seen" feature in Facebook Stories'},desc:{vi:"Xem story không bị đối phương phát hiện ngay trên Facebook",en:"View stories anonymously right on Facebook"},contentScript:{id:"fb_blockSeenStory",js:["./scripts/content/fb_blockSeenStory.js"],matches:["https://www.facebook.com/*","https://web.facebook.com/*"],runAt:"document_start",world:"MAIN",allFrames:!0}},{id:"fb_addDownloadVideoBtn",icon:"⬇",name:{vi:"Thêm nút tải cho mọi video trên Facebook",en:"Add download button on every video on Facebook"},desc:{vi:"Hỗ trợ mọi video/reel/comment",en:"Support all video/reel/comment"},isMaintenanceFn:()=>{var e;return(null==(e=o)?void 0:e.version)<"1.91"},contentScript:{id:"fb_addDownloadVideoBtn",js:["./scripts/content/fb_addDownloadVideoBtn.js"],matches:["https://www.facebook.com/*","https://web.facebook.com/*"],runAt:"document_start",world:"MAIN",allFrames:!1}},{id:"fb_addVideoControlBtn",icon:"🕹️",name:{vi:"Thêm giao diện điều khiển video trên Facebook",en:"Add control button on video on Facebook"},desc:{vi:"Có thể tua tới thời điểm bất kỳ của tất cả video/reel",en:"Jump to any point in any video/reel"},contentScript:{id:"fb_addVideoControlBtn",js:["./scripts/content/fb_addVideoControlBtn.js"],matches:["https://www.facebook.com/*","https://web.facebook.com/*"],runAt:"document_start",world:"MAIN",allFrames:!1}},{id:"fb_showTotalPostReactions",icon:"❤️",name:{vi:"Đếm tổng lượt thích bài viết trên Facebook",en:"Count total post reactions on Facebook"},desc:{vi:"Áp dụng cho những bài viết bị ẩn lượt thích",en:"Applies to posts that hide reactions"},contentScript:{id:"fb_showTotalPostReactions",js:["./scripts/content/fb_showTotalPostReactions.js"],matches:["https://www.facebook.com/*","https://web.facebook.com/*"],runAt:"document_start",world:"MAIN",allFrames:!0},link:"https://www.facebook.com/groups/fbaio/posts/1437552863566064"},{id:"fb_stopNewFeed",icon:"⏳",name:{vi:"Tạm dừng tải dòng thời gian trên facebook",en:"Stop load new feed on facebook"},desc:{vi:"Giúp tập trung làm việc. Không cho fb tải các tab: story, home, video, nhóm, mua bán",en:"Focus to work. Stop load these new feeds: stories, home, video, group, marketplace"},contentScript:{id:"fb_stopNewFeed",js:["./scripts/content/fb_stopNewFeed.js"],matches:["https://www.facebook.com/*","https://web.facebook.com/*"],runAt:"document_start",world:"MAIN",allFrames:!0},link:"https://www.facebook.com/groups/fbaio/posts/1515339635787386"},{id:"header_insta",name:"Instagram",isHeader:!0,icon:n.jsx("i",{className:"fa-brands fa-instagram"})},{id:"insta_blockSeenStory",icon:"👀",name:{vi:'Chặn "đã xem" trên story Instagram',en:'Block the "seen" feature in Instagram Stories'},desc:{vi:"Xem story không bị đối phương phát hiện ngay trên Instagram",en:"View stories anonymously right on Instagram"},contentScript:{id:"insta_blockSeenStory",js:["./scripts/content/insta_blockSeenStory.js"],matches:["https://*.instagram.com/*"],runAt:"document_start",world:"MAIN",allFrames:!0}},{id:"header_threads",name:"Threads",isHeader:!0,icon:n.jsx("i",{className:"fa-solid fa-at"})},{id:"threads_addDownloadVideoBtn",icon:"⬇",name:{vi:"Thêm nút tải cho mọi video trên Threads",en:"Add download button on every video on Threads"},desc:{vi:"Hỗ trợ mọi video/reel/comment",en:"Support all video/reel/comment"},isMaintenanceFn:()=>{var e;return(null==(e=o)?void 0:e.version)<"1.91"},contentScript:{id:"threads_addDownloadVideoBtn",js:["./scripts/content/threads_addDownloadVideoBtn.js"],matches:["https://*.threads.net/*","https://*.threads.com/*"],runAt:"document_start",world:"MAIN",allFrames:!1},link:"https://www.facebook.com/groups/fbaio/posts/1515339635787386"},{id:"header_tiktok",name:"Tiktok",isHeader:!0,icon:n.jsx("i",{className:"fa-brands fa-tiktok"})},{id:"tiktok_addDownloadVideoBtn",icon:"⬇",name:{vi:"Thêm nút tải cho mọi video trên Tiktok",en:"Add download button on every video on Tiktok"},desc:{vi:"Hỗ trợ mọi video từ trang chủ, trang tìm kiếm, trang cá nhân, ... ",en:"Support all video from home, search, user profile, ..."},contentScript:{id:"tiktok_addDownloadVideoBtn",js:["./scripts/content/tiktok_addDownloadVideoBtn.js"],matches:["https://*.tiktok.com/*"],runAt:"document_start",world:"MAIN",allFrames:!1},link:"https://www.facebook.com/groups/fbaio/posts/1571748466813169"}],I="AutoRun.OtherScriptEnabled";async function D(e=!1){const t=(await c("chrome.scripting.getRegisteredContentScripts")||[]).map((e=>e.id));if(e)return t;const n=await r(I)||[];return[...t,...n]}async function B(e){return(await D(!0)).includes(e)}function N(){const{ti:a}=l(),{message:s}=m(),r=e(),[k,_]=v("AutoRun.supported",new Set),[F,B]=v("AutoRun.enabled",new Set),N=t.useMemo((()=>T.map((e=>e.isHeader?e:{...e,enabled:F.has(null==e?void 0:e.id),supported:e.supported??k.has(null==e?void 0:e.id)}))),[k,F]),R=async e=>{const t=F.has(e.id),n=new Set(F);let i;t?(p("AutoRun.Disable."+e.id),n.delete(e.id),i=!e.onDisable||await e.onDisable()):(p("AutoRun.Enable."+e.id),n.add(e.id),i=!e.onEnable||await e.onEnable()),!1!==i&&(B(n),async function(e){const t=e.map((e=>e.contentScript)).filter(Boolean),n=await D(!0);await c("chrome.scripting.unregisterContentScripts",[{ids:n}]),t.length&&await c("chrome.scripting.registerContentScripts",[t]);const i=e.filter((e=>!e.contentScript)).map((e=>e.id));i.length&&await d(I,i)}(T.filter((e=>n.has(e.id)))))};t.useEffect((()=>{const e=T.filter((e=>{var t;return null==(t=e.isMaintenanceFn)?void 0:t.call(e)})).filter((e=>F.has(e.id)));if(e.length)for(const t of e)R(t),s.warning(a({en:"Disabled due to maintenance: "+a(t.name),vi:"Đã tắt do bảo trì: "+a(t.name)}))}),[F]),t.useEffect((()=>{p("AutoRun:onLoad"),i().then(((e=[])=>{_(new Set(e))})),D().then(((e=[])=>{B(new Set(e))}))}),[]);return n.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",width:"100%",height:"100%"},children:[n.jsx(y,{align:"middle",style:{margin:16},children:n.jsx(h.Title,{level:3,style:{margin:0},children:a({en:"AutoRun Features",vi:"Tự động chạy"})})}),n.jsxs(u,{align:"center",direction:"vertical",children:[n.jsx(b,{showIcon:!0,type:"success",message:a({en:"These features will be automatically run, when you open relevant websites.",vi:"Các chức năng dưới đây sẽ được tự động chạy, khi bạn mở trang web tương ứng"})}),!o&&n.jsx(b,{showIcon:!0,type:"error",message:a({en:"Please install FB AIO extension to use these features.",vi:"Vui lòng cài đặt FB AIO để sử dụng các chức năng này."})})]}),n.jsx(x,{style:{padding:10},dataSource:N,renderItem:(e,t)=>{var i,s,c;const d=r.search.includes(e.id),l=e.isHeader?n.jsx(n.Fragment,{children:n.jsxs(h.Title,{level:4,style:{textAlign:"center"},children:[e.icon," ",a(e.name)]},e.id)}):n.jsx(x.Item,{className:d?"gradient-border":"",style:{marginBottom:(null==(i=N[t+1])?void 0:i.isHeader)?40:0,borderRadius:10,...d?{paddingLeft:10}:{}},children:n.jsxs(S,{disabled:!e.supported||(null==(s=e.isMaintenanceFn)?void 0:s.call(e)),checked:e.enabled,onChange:t=>{R(e)},children:[n.jsxs(f,{title:n.jsxs(n.Fragment,{children:[(null==(c=e.isMaintenanceFn)?void 0:c.call(e))?n.jsx(g,{color:"warning",children:a({en:"Maintenance: ",vi:"Bảo trì: "})}):""," ",a(e.desc)]}),children:[n.jsxs(h.Text,{style:{opacity:e.supported?1:.6},children:[n.jsx(A,{type:"vertical"}),e.icon,n.jsx(A,{type:"vertical"}),a(e.name)," "]}),!e.supported&&o&&n.jsx(j,{type:"coming"})]}),e.link?n.jsxs(n.Fragment,{children:[n.jsx(A,{type:"vertical"}),n.jsx(f,{title:a({en:"Learn more",vi:"Xem thêm"}),children:n.jsx(h.Link,{href:e.link,target:"_blank",children:n.jsx("i",{className:"fa-solid fa-external-link"})})})]}):null,e.button?n.jsxs(n.Fragment,{children:[n.jsx(A,{type:"vertical"}),e.button]}):null]})});return d?n.jsx(w.Ribbon,{text:a({en:"Enable this",vi:"Bật tại đây"}),children:l}):l}})]})}export{N as default,B as isScriptEnabled};
