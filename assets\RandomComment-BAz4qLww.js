const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./CommentAttachment-CjEEJe3b.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./comments-BafLCvtq.js","./MyApp-DW5WH4Ub.js","./videos-D2LbKcXH.js","./index-CDSnY0KE.js","./EyeOutlined-CNKPohhw.js","./addEventListener-C4tIKh7N.js"])))=>i.map(i=>d[i]);
import{r as e,b0 as s,b1 as i,b5 as t,bg as n,aN as r}from"./index-Cak6rALw.js";import a from"./useCacheState-CAnxkewm.js";import{u as l,aF as o,S as c,c as d,b as h,e as m,f as x,I as j}from"./MyApp-DW5WH4Ub.js";import{s as f}from"./shuffle-BAGnj1ei.js";import{G as u}from"./gender-D7cqwEK5.js";import{C as p}from"./index-D7dTP8lW.js";import{R as g}from"./row-BMfM-of4.js";import{A as y}from"./index-DdM4T8-Q.js";import{I as v}from"./index-CDSnY0KE.js";import{P as b}from"./index-DP_qolD8.js";import"./_copyArray-_bDJu6nu.js";import"./index-IxNfZAD-.js";import"./Dropdown-BcL-JHCf.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-PbLYNhdQ.js";import"./EyeOutlined-CNKPohhw.js";import"./addEventListener-C4tIKh7N.js";const C=i((()=>r((()=>import("./CommentAttachment-CjEEJe3b.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8]),import.meta.url)),{fallback:j});function k({comments:i,onPressWhere:r}){var j;const{ti:k}=l(),[w,N]=a("RandomComment.targetComment",null),[_,T]=a("RandomComment.history",[]),[A,P]=a("RandomComment.exclude",[]);function R(){const e=i.filter((e=>!A.includes(e.id)));if(0===e.length)return void N(null);const s=o(0,e.length-1),t=f([...e])[s];N(t),T((e=>[...e,t]))}return e.useEffect((()=>{R()}),[]),e.useEffect((()=>{w&&A.includes(w.id)&&R()}),[A]),s.jsxs(c,{direction:"vertical",style:{width:500,maxWidth:"100%"},children:[i.length<=0&&s.jsx(d.Text,{type:"secondary",children:k({en:"No comments",vi:"Không có bình luận"})}),!w&&A.length===i.length&&i.length>0&&s.jsx(p,{size:"small",children:s.jsx(d.Text,{type:"warning",children:k({en:"All comments are excluded. Please remove some from the exclude list.",vi:"Tất cả bình luận đã bị loại trừ. Vui lòng bỏ một số khỏi danh sách loại trừ."})})}),w&&s.jsx(p,{size:"small",title:"#"+w.index,styles:{title:{textAlign:"center"}},children:s.jsxs(c,{direction:"vertical",align:"center",style:{width:"100%"},children:[s.jsx(p,{size:"small",children:s.jsxs(g,{align:"middle",justify:"start",children:[s.jsx(y,{shape:"square",src:s.jsx(v,{src:w.author.avatar,fallback:w.author.avatar,preview:!1}),size:50}),s.jsxs(c,{direction:"vertical",size:0,style:{marginLeft:10},align:"start",children:[s.jsx(d.Link,{href:w.author.url||"https://fb.com/"+w.author.id,target:"_blank",strong:!0,children:w.author.name}),s.jsx(d.Text,{type:"secondary",children:w.author.id}),s.jsx(h,{color:(null==(j=u[w.author.gender])?void 0:j.color)||"blue",children:k(u[w.author.gender]||w.author.gender)})]})]})}),s.jsxs(c,{size:0,children:[s.jsx(h,{children:m(w.created_time)}),s.jsx(d.Text,{type:"secondary",children:x(w.created_time)})]}),w.text&&s.jsx(d.Text,{children:w.text}),w.attachments.length>0&&s.jsx(C,{attachment:w.attachments[0],maxWidth:900}),s.jsxs(c.Compact,{style:{marginTop:10},children:[s.jsx(t,{onClick:()=>r(w),icon:s.jsx("i",{className:"fa-solid fa-magnifying-glass"}),children:k({en:"View position",vi:"Xem vị trí"})}),s.jsx(t,{href:w.url,target:"_blank",icon:s.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"}),iconPosition:"end",children:k({en:"View on FB",vi:"Xem trên FB"})})]}),s.jsxs(c,{children:[s.jsx(b,{title:k({en:"Remove this comment from history?",vi:"Xóa bình luận này khỏi lịch sử?"}),onConfirm:()=>T((e=>e.filter((e=>e.id!==w.id)))),children:s.jsx(t,{danger:!0,icon:s.jsx("i",{className:"fa-solid fa-trash"}),children:k({en:"Remove",vi:"Xóa"})})}),s.jsx(t,{type:"primary",onClick:R,icon:s.jsx("i",{className:"fa-solid fa-shuffle"}),children:k({en:"Retry",vi:"Thử lại"})})]})]})}),_.length>0&&s.jsx(n,{expandIconPosition:"end",children:s.jsx(n.Panel,{header:s.jsxs(c,{children:[k({en:"History",vi:"Lịch sử"}),s.jsx(h,{children:_.length})]}),children:s.jsxs(c,{align:"center",wrap:!0,children:[s.jsx(b,{title:k({en:"Are you sure to clear history?",vi:"Bạn có chắc chắn muốn xóa lịch sử?"}),onConfirm:()=>T([]),children:s.jsx(t,{danger:!0,icon:s.jsx("i",{className:"fa-solid fa-trash"}),children:k({en:"Clear history",vi:"Xóa lịch sử"})})}),_.map(((e,i)=>s.jsxs("div",{className:"show-on-hover-trigger",children:[s.jsxs(t,{type:e.id===(null==w?void 0:w.id)?"primary":"default",onClick:()=>N(e),children:["#",e.index]}),s.jsx(t,{className:"show-on-hover-item",danger:!0,size:"small",icon:s.jsx("i",{className:"fa-solid fa-close"}),onClick:()=>T((e=>e.filter(((e,s)=>s!==i)))),style:{position:"absolute",right:-10,top:-10}})]},e.id+i)))]})},"history")})]})}export{k as default};
