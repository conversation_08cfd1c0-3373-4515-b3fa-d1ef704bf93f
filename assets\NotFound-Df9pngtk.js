import{aJ as e,b3 as t,r as n,b0 as r,b9 as s}from"./index-Cak6rALw.js";import{S as i}from"./Screen-Buq7HJpK.js";import{u as a,m as o,S as l,A as c,T as h,b as m}from"./MyApp-DW5WH4Ub.js";import{E as g}from"./index-SRy1SMeO.js";import{L as u}from"./index-jrOnBmdW.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./DownOutlined-B-JcS-29.js";import"./SearchOutlined--mXbzQ68.js";import"./Pagination-2X6SDgot.js";var p,d;const f=e(function(){if(d)return p;function e(e,t){if((e=e.replace(/\s+/g,""))===(t=t.replace(/\s+/g,"")))return 1;if(e.length<2||t.length<2)return 0;let n=new Map;for(let s=0;s<e.length-1;s++){const t=e.substring(s,s+2),r=n.has(t)?n.get(t)+1:1;n.set(t,r)}let r=0;for(let s=0;s<t.length-1;s++){const e=t.substring(s,s+2),i=n.has(e)?n.get(e):0;i>0&&(n.set(e,i-1),r++)}return 2*r/(e.length+t.length-2)}return d=1,p={compareTwoStrings:e,findBestMatch:function(t,n){if(!function(e,t){return"string"==typeof e&&(!!Array.isArray(t)&&(!!t.length&&!t.find((function(e){return"string"!=typeof e}))))}(t,n))throw new Error("Bad arguments: First argument should be a string, second should be an array of strings");const r=[];let s=0;for(let a=0;a<n.length;a++){const i=n[a],o=e(t,i);r.push({target:i,rating:o}),o>r[s].rating&&(s=a)}const i=r[s];return{ratings:r,bestMatch:i,bestMatchIndex:s}}}}());function j(){const{ti:e}=a(),p=t(),d=n.useMemo((()=>{const t=p.pathname;return o.map((n=>{var r;return(null==(r=n.children)?void 0:r.map((r=>({...r,label:e(n.label)+" - "+e(r.label),hidden:r.hidden||n.hidden,similarity:f.compareTwoStrings(t||"",r.path||"")}))))||n})).flat().filter((e=>!e.hidden)).sort(((e,t)=>(t.similarity||0)-(e.similarity||0))).filter(((e,t)=>e.similarity>.5||t<5))}),[p.pathname]);return r.jsx(i,{title:"404 Not Found",mode:"center",children:r.jsxs(l,{direction:"vertical",align:"center",children:[r.jsx(g,{image:g.PRESENTED_IMAGE_DEFAULT,description:r.jsx(c,{type:"warning",showIcon:!0,message:e({en:"Not found your requested page",vi:"Không tìm thấy trang bạn yêu cầu"})})}),r.jsx(c,{type:"info",showIcon:!0,message:e({en:"Are you looking for these pages?",vi:"Có phải bạn đang tìm những trang này?"})}),r.jsx(u,{dataSource:d,renderItem:t=>r.jsx(u.Item,{children:r.jsx(s,{to:t.path,className:"show-on-hover-trigger",children:r.jsxs(l,{children:[r.jsx("i",{className:t.icon+" fa-lg"})," ",e(t.label)," ",r.jsx(h,{className:"show-on-hover-item",title:e({en:"Similarity score",vi:"Điểm tương đồng"}),children:r.jsxs(m,{children:[Math.round(100*(t.similarity||0)),"%"]})})]})})},t.path)}),r.jsx(c,{type:"success",showIcon:!0,message:r.jsxs(l,{children:[e({en:"Or you can view ",vi:"Hoặc bạn có thể xem"}),r.jsxs(s,{to:"/vip",children:[r.jsx("i",{className:"fa-solid fa-puzzle-piece"})," ",e({en:"All Features",vi:"Tất cả tính năng"})]})]})})]})})}export{j as default};
