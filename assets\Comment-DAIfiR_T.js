const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./WordStatisticButton-D2UxB2M4.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./MyApp-DW5WH4Ub.js","./BadgeWrapper-BCbYXXfB.js","./index-Bp2s1Wws.js","./index-Bx3o6rwA.js","./PurePanel-BvHjNOhQ.js","./index-IxNfZAD-.js","./Dropdown-BcL-JHCf.js","./index-BzMPigdO.js","./DownOutlined-B-JcS-29.js","./RandomComment-BAz4qLww.js","./useCacheState-CAnxkewm.js","./shuffle-BAGnj1ei.js","./_copyArray-_bDJu6nu.js","./gender-D7cqwEK5.js","./index-D7dTP8lW.js","./row-BMfM-of4.js","./useBreakpoint-CPcdMRfj.js","./index-DdM4T8-Q.js","./index-PbLYNhdQ.js","./index-CDSnY0KE.js","./EyeOutlined-CNKPohhw.js","./addEventListener-C4tIKh7N.js","./index-DP_qolD8.js","./CommentAttachment-CjEEJe3b.js","./comments-BafLCvtq.js","./videos-D2LbKcXH.js"])))=>i.map(i=>d[i]);
import{r as e,b0 as t,b5 as n,b1 as i,bi as r,aN as l}from"./index-Cak6rALw.js";import{CommentIntentToken as o,getPostComments as s,CommentAttachmentType as a,IdentityBadgeType as d,IdentityBadgeTypeNames as c,getReplyComments as m,CommentIntentTokenNames as h}from"./comments-BafLCvtq.js";import{u,d as p,S as g,T as x,i as v,c as y,h as j,e as f,f as k,b,g as w,z as C,A as T,s as _,I as $,v as I,aF as R,C as S,l as A}from"./MyApp-DW5WH4Ub.js";import N from"./MyTable-DnjDmN7z.js";import L from"./useCacheState-CAnxkewm.js";import{G as E}from"./gender-D7cqwEK5.js";import{E as F}from"./ExportButton-CbyepAf_.js";import{R as O}from"./reactions-CR2nCZPF.js";import{B}from"./BadgeWrapper-BCbYXXfB.js";import{R as M}from"./row-BMfM-of4.js";import{A as z}from"./index-DdM4T8-Q.js";import{I as D}from"./index-CDSnY0KE.js";import{S as P}from"./index-C5gzSBcY.js";import{T as V}from"./index-BzMPigdO.js";import{M as W}from"./index-Bx3o6rwA.js";import"./videos-D2LbKcXH.js";import"./index-ByRdMNW-.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./DownOutlined-B-JcS-29.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./index-PbLYNhdQ.js";const G=i((()=>l((()=>import("./WordStatisticButton-D2UxB2M4.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11]),import.meta.url))),H=i((()=>l((()=>import("./RandomComment-BAz4qLww.js")),__vite__mapDeps([12,1,2,13,3,14,15,16,17,8,9,18,19,20,21,22,23,24,25]),import.meta.url)),{fallback:S}),K=i((()=>l((()=>import("./CommentAttachment-CjEEJe3b.js")),__vite__mapDeps([26,1,2,27,3,28,22,23,24]),import.meta.url)),{fallback:$});function J(e){const t=[];function n(e,i){if(t.push({...e,index:i}),e.reply&&Array.isArray(e.reply.comments)&&e.reply.comments.length)for(let t=0;t<e.reply.comments.length;t++){n(e.reply.comments[t],i+"_"+(t+1))}}for(let i=0;i<e.length;i++)n(e[i],(i+1).toString());return t}function X({target:i=null,postId:l}){var $,S;const{ti:X}=u(),{message:q}=p(),[U,Y]=e.useState(!1),[Z,Q]=L(`Comment.${l}.total`,0),[ee,te]=L(`Comment.${l}.loadMoreCount`,10),[ne,ie]=L(`Comment.${l}`,[]),[re,le]=L(`Comment.${l}.intentToken`,o.RECENT_ACTIVITY),[oe,se]=e.useState({}),[ae,de]=L("Comment.maxReplyDepth",1),[ce,me]=e.useState(!1),[he,ue]=e.useState(!1),pe=e.useMemo((()=>J(ne)),[ne]);e.useEffect((()=>{l&&0===ne.length&&ge()}),[l,ne.length]);const ge=()=>{Y(!0),s({postId:l,type:re}).then((e=>{console.log("comment ne",e.comments),ie([...e.comments]),Q(e.total)})).finally((()=>{Y(!1)}))},xe=async()=>{var e;if((null==pe?void 0:pe.length)||ge(),ee<=0)return;const n="Comment.onLoadMore",i=n+".sleep",r=[...pe],o=(null==r?void 0:r.length)+ee;let a=!1,d=0;for(Y(!0),q.loading({key:n,duration:0,content:X({en:"Loading comments...",vi:"Đang tải bình luận..."})});(null==r?void 0:r.length)<o&&!a;){const o=null==(e=r[(null==r?void 0:r.length)-1])?void 0:e.cursor,{comments:c,total:m}=await s({postId:l,type:re,cursor:o});if(null==c?void 0:c.length){const e=new Set(r.map((e=>e.id))),l=c.filter((t=>!e.has(t.id)));if(!(null==l?void 0:l.length))break;if(r.push(...l),d+=null==l?void 0:l.length,ie([...r]),m&&Q(m),q.loading({key:n,duration:0,content:t.jsxs(t.Fragment,{children:[X({en:"Loading comments...",vi:"Đang tải bình luận..."})+` (${d}/${ee})`,t.jsx("br",{}),X({en:"Click to stop",vi:"Click để dừng"})]}),onClick:()=>a=!0}),d>=ee)break;await I(R(2e3,5e3),(e=>(q.loading({key:i,content:X({en:"Waiting...",vi:"Đang chờ..."})+` (${~~(e/1e3)})`}),a))),q.destroy(i)}else a=!0}Y(!1),q.destroy(n),q.success(X({en:`Loaded ${d}/${ee} comments`,vi:`Đã tải ${d}/${ee} bình luận`}))},ve=e.useMemo((()=>[...new Set(pe.flatMap((e=>{var t;return null==(t=e.signals)?void 0:t.map((e=>e.title))})))].filter(Boolean).map((e=>{var t;return{text:e+" ("+(null==(t=pe.filter((t=>{var n;return null==(n=t.signals)?void 0:n.some((t=>t.title===e))})))?void 0:t.length)+")",value:e}}))),[pe]),ye=[{title:"#",key:"index",dataIndex:"index"},{title:X({en:"Reply",vi:"Trả lời"}),key:"reply",dataIndex:"reply",render:(e,i,l)=>{var o;const s=i.reply.count||0,a=(null==(o=i.reply.comments)?void 0:o.length)||0;if(0===s)return"";const d=a>=s;return t.jsxs(g,{children:[a?`${a}/${s}`:s,t.jsx(x,{title:X(d?{en:"All replies loaded",vi:"Đã tải hết bình luận"}:{en:"Load replies ",vi:"Tải bình luận trả lời"}),children:t.jsx(n,{icon:t.jsx("i",{className:"fa-solid fa-comments"}),loading:oe[i.id],disabled:d,onClick:()=>(async e=>{se((t=>({...t,[e.id]:!0})));const t=`Comment.onLoadReply.${e.id}`;q.loading({key:t,duration:0,content:X({en:"Loading replies...",vi:"Đang tải trả lời..."})});const n=e.reply.comments[e.reply.comments.length-1],i=null==n?void 0:n.cursor,l=await m({commentId:e.id,expansionToken:e.reply.expansion_token,cursor:i});console.log("replies",l),l.length?(q.success({key:t,content:X({en:"Replies loaded",vi:"Trả lời đã tải"})+` (${l.length})`}),ie((t=>r(t,(t=>{!function t(n){for(const i of n){if(i.id===e.id)return i.reply.comments||(i.reply.comments=[]),i.reply.comments.push(...l),!0;if(i.reply&&Array.isArray(i.reply.comments)&&i.reply.comments.length&&t(i.reply.comments))return!0}return!1}(t)}))))):q.error({key:t,content:X({en:"No replies found",vi:"Không tìm thấy trả lời"})}),se((t=>({...t,[e.id]:!1})))})(i)})})]})},align:"end",rangeFilter:{getValue:e=>e.reply.count},sorter:(e,t)=>t.reply.count-e.reply.count,width:100},{title:X({en:"Author",vi:"Tác giả"}),key:"author",dataIndex:"author",render:(e,n,i)=>{const r=n.depth>0?t.jsx("div",{style:{paddingLeft:20+100*(n.depth-1),paddingRight:5},children:"╰──── "}):null;return t.jsxs(M,{align:"middle",justify:"start",style:{flex:1},children:[r,t.jsx(z,{shape:"square",src:t.jsx(D,{src:v(n.author.id),fallback:n.author.avatar}),size:50}),t.jsxs(g,{direction:"vertical",size:0,style:{marginLeft:10},children:[t.jsx(y.Link,{href:n.author.url||j(n.author.id),target:"_blank",strong:!0,children:n.author.name}),t.jsx(y.Text,{type:"secondary",children:n.author.id})]})]})}},{title:X({en:"Text",vi:"Nội dung"}),key:"text",dataIndex:"text",render:(e,t,n)=>t.text,filters:[{text:X({en:"Has text",vi:"Có nội dung"})+" ("+(null==($=pe.filter((e=>{var t;return(null==(t=e.text)?void 0:t.length)>0})))?void 0:$.length)+")",value:!0},{text:X({en:"No text",vi:"Không nội dung"})+" ("+(null==(S=pe.filter((e=>{var t;return 0===(null==(t=e.text)?void 0:t.length)})))?void 0:S.length)+")",value:!1}],onFilter:(e,t)=>{var n,i;return e?(null==(n=t.text)?void 0:n.length)>0:0===(null==(i=t.text)?void 0:i.length)},width:300},{title:X({en:"Attachments",vi:"Đính kèm"}),key:"attachments",dataIndex:"attachments",render:(e,n,i)=>n.attachments.map((e=>t.jsx(K,{attachment:e}))),filters:Object.entries(a).map((([e,t])=>{var n;return{text:e+" ("+(null==(n=pe.filter((e=>e.attachments.some((e=>e.type===t)))))?void 0:n.length)+")",value:t}})),onFilter:(e,t)=>t.attachments.some((t=>t.type===e)),width:200},{title:X({en:"Time",vi:"Thời gian"}),key:"created_time",dataIndex:"created_time",render:(e,n,i)=>t.jsxs(g,{direction:"vertical",size:0,children:[t.jsx(y.Text,{children:f(n.created_time)}),t.jsx(y.Text,{type:"secondary",children:k(n.created_time)})]}),sorter:(e,t)=>e.created_time-t.created_time,width:150},{title:X({en:"React",vi:"Thích"}),key:"react",dataIndex:"react",render:(e,n,i)=>t.jsxs(g,{size:3,direction:"vertical",children:[t.jsx(b,{color:n.react.total>0?"success":"default",children:w(n.react.total)}),n.react.each.map((e=>t.jsxs(g,{size:2,children:[t.jsx(D,{preview:!1,src:O[e.id].icon,width:20,height:20})," ",w(e.count)]})))]}),rangeFilter:{getValue:e=>e.react.total},sorter:(e,t)=>t.react.total-e.react.total,width:100},{title:X({en:"Badges",vi:"Huy hiệu"}),key:"badges",dataIndex:"badges",render:(e,n,i)=>{var r,l;return(null==(r=n.badges)?void 0:r.length)?t.jsx(g,{direction:"vertical",children:null==(l=n.badges)?void 0:l.map((e=>t.jsx(x,{title:X(c[e.type]),children:t.jsx(D,{src:e.icon,preview:!1,width:20,height:20})},e.type)))}):"-"},filters:Object.entries(d).map((([e,t])=>{var n;return{text:X(c[t])+" ("+(null==(n=pe.filter((e=>{var n;return null==(n=e.badges)?void 0:n.some((e=>e.type===t))})))?void 0:n.length)+")",value:t}})),onFilter:(e,t)=>{var n;return null==(n=t.badges)?void 0:n.some((t=>t.type===e))},width:100},(null==i?void 0:i.type)===C.Group?{title:X({en:"Signals",vi:"Tín hiệu"}),key:"signals",dataIndex:"signals",render:(e,n,i)=>{var r;return t.jsx(g,{direction:"vertical",children:null==(r=n.signals)?void 0:r.map((e=>t.jsxs(b,{children:[e.icon&&t.jsx(D,{src:e.icon,preview:!1,width:20,height:20}),e.title]},e.id)))})},filters:ve,onFilter:(e,t)=>{var n;return null==(n=t.signals)?void 0:n.some((t=>t.title===e))},width:100}:{},{title:X({en:"Gender",vi:"Giới tính"}),key:"gender",dataIndex:"gender",render:(e,n,i)=>{const r=E[n.author.gender];return r?t.jsx(b,{color:r.color,children:X(r)}):null},filters:Object.entries(E).map((([e,t])=>{var n;return{text:X(t)+" ("+(null==(n=pe.filter((t=>t.author.gender===e)))?void 0:n.length)+")",value:e}})),onFilter:(e,t)=>t.author.gender===e,width:100},{title:X({en:"Actions",vi:"Hành động"}),key:"actions",dataIndex:"actions",render:(e,i,r)=>t.jsx(g,{children:t.jsx(x,{title:X({en:"View comment",vi:"Xem bình luận"}),children:t.jsx(n,{href:i.url,target:"_blank",icon:t.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})})})}),width:100}],je=async(e,n,i,r)=>{var l,o,s,a,d;if(n>=i||!(null==(l=e.reply)?void 0:l.count)||(null==(o=e.reply.comments)?void 0:o.length)>=e.reply.count)return e;let c={...e},h=(null==(s=c.reply.comments)?void 0:s.length)||0,u=h>0?null==(a=c.reply.comments[h-1])?void 0:a.cursor:void 0,p=c.reply.comments?[...c.reply.comments]:[],x=c.reply.expansion_token;for(;p.length<c.reply.count;){const i=await m({commentId:c.id,expansionToken:x,cursor:u});if(!i.length)break;p.push(...i),u=null==(d=i[i.length-1])?void 0:d.cursor;const l="fetchAllReplies.waiting."+n+"."+e.id;if(await I(R(500,2e3),(e=>(q.loading({key:l,content:t.jsxs(g,{direction:"vertical",size:3,children:[t.jsxs(y.Text,{children:[X({en:"Comment",vi:"Bình luận"}),":"," ",A(c.text,50)]}),t.jsxs(y.Text,{children:[X({en:"Depth",vi:"Độ sâu"}),": ",n,"; ",X({en:"Loaded",vi:"Đã tải"}),": ",p.length,"/",c.reply.count,"; ",X({en:"Waiting...",vi:"Đang chờ..."})," ",~~(e/1e3)]})]})}),r()))),r())break}for(let t=0;t<p.length&&(p[t]=await je(p[t],n+1,i,r),!r());t++);return c.reply.comments=p,c},fe=e.useRef(null),ke=()=>{if(0===pe.length)return q.error(X({en:"No comments to choose from",vi:"Không có bình luận để chọn"}));ue(!0)};return t.jsxs(M,{align:"middle",justify:"center",children:[t.jsx(T,{type:"info",showIcon:!0,message:X({en:"Total comments (include replies): ",vi:"Tổng bình luận (bao gồm trả lời): "})+w(Z),style:{marginBottom:10}}),t.jsx(N,{ref:fe,columns:ye,data:pe,searchable:!0,selectable:!0,keyExtractor:e=>e.id+" "+e.parent_id+e.created_time,renderTitle:e=>{const i=(null==e?void 0:e.length)>0?e:pe,r=i.filter((e=>e.reply.count>0&&e.reply.comments.length<e.reply.count));return t.jsxs(g,{wrap:!0,children:[t.jsxs(g.Compact,{children:[t.jsx(F,{data:(null==e?void 0:e.length)>0?e:pe,options:[{key:"json (raw)",label:".json (raw)",prepareData:e=>({data:JSON.stringify(e,null,2),fileName:`${l}-comments-raw-${l}.json`})},{key:"json (flat array)",label:".json (flat array)",prepareData:e=>({data:JSON.stringify(J(e),null,2),fileName:`${l}-comments-flat-${l}.json`})}]}),t.jsx(P,{value:re,onChange:le,options:Object.entries(h).map((([e,t])=>({value:e}))),labelRender:e=>t.jsxs(g,{size:5,children:[t.jsx("i",{className:h[e.value].icon}),X(h[e.value])]}),placeholder:X({en:"Select display mode",vi:"Chọn mode hiển thị"}),popupMatchSelectWidth:!1,optionRender:e=>{const n=h[e.value],i=X(n),r=X(n.description);return t.jsxs("div",{style:{width:300,padding:5},children:[t.jsxs(g,{children:[t.jsx("i",{className:n.icon}),t.jsx(y.Text,{strong:!0,children:i})]}),t.jsx(y.Text,{type:"secondary",style:{fontSize:"12px",width:"100%",display:"block",whiteSpace:"normal",wordBreak:"break-word",marginTop:5},children:r})]})}}),t.jsx(n,{icon:t.jsx("i",{className:"fa-solid fa-rotate-right"}),onClick:ge,loading:U,children:X({en:"Refresh",vi:"Làm mới"})})]}),t.jsxs(g.Compact,{children:[t.jsx(x,{title:X({en:"Number of comments to load more",vi:"Số lượng bình luận muốn tải thêm"})+"\nmin: 1, max: "+(Z-(null==pe?void 0:pe.length)-pe.map((e=>e.reply.count)).reduce(((e,t)=>e+t),0)),children:t.jsx(V,{min:1,max:Z-(null==pe?void 0:pe.length),value:ee,onChange:e=>{te(e)},style:{width:60}})}),t.jsx(n,{icon:t.jsx("i",{className:"fa-solid fa-play"}),onClick:xe,disabled:ee<=0,loading:U,children:X({en:"Load more",vi:"Tải thêm"})})]}),t.jsxs(g.Compact,{children:[t.jsx(x,{title:X({en:"Max reply depth",vi:"Độ sâu trả lời tối đa"}),children:t.jsx(V,{min:1,max:10,value:ae,onChange:e=>de(e||1),style:{width:60}})}),t.jsx(x,{title:X({en:"Fetch replies of selected comments",vi:"Tải trả lời của bình luận đang chọn"}),children:t.jsx(n,{icon:t.jsx("i",{className:"fa-solid fa-comments"}),loading:ce,onClick:()=>(async e=>{me(!0);try{let n=function(e,t){var i;for(let r=0;r<e.length;r++){if(e[r].id===t.id)return e[r]=t,!0;if(e[r].reply&&(null==(i=e[r].reply.comments)?void 0:i.length)&&n(e[r].reply.comments,t))return!0}return!1},i=[...ne],r=0,l=!1,o="fetchAllReplies.stopping";for(let s=0;s<e.length;s++){const a=e[s];q.loading({key:"fetchAllReplies",duration:0,content:t.jsxs(t.Fragment,{children:[X({en:`Fetching replies (${s+1}/${e.length})...`,vi:`Đang tải trả lời (${s+1}/${e.length})...`}),t.jsx("br",{}),X({en:"Click to stop",vi:"Nhấn để dừng"})]}),onClick:()=>{l=!0,q.loading({key:o,duration:0,content:X({en:"Stopping...",vi:"Đang dừng..."})})}});const d=await je(a,a.depth||0,ae,(()=>l));if(n(i,d),r++,ie([...i]),l)break}q.destroy(o),q.success({key:"fetchAllReplies",content:X({en:`Fetched all replies for ${r} comments`,vi:`Đã tải trả lời cho ${r} bình luận`})})}catch(n){q.error({key:"fetchAllReplies",content:X({en:"Error fetching replies",vi:"Lỗi khi tải trả lời"})})}finally{me(!1)}})(r),disabled:ce||0===r.length,children:r.length})})]}),t.jsxs(g.Compact,{children:[t.jsx(B,{type:"new",children:t.jsx(x,{title:X({en:`Choose a random comment in ${i.length} selected comments`,vi:`Chọn ngẫu nhiên 1 bình luận trong ${i.length} bình luận đang chọn`}),children:t.jsxs(n,{type:pe.length>0?"primary":"default",icon:t.jsx("i",{className:"fa-solid fa-shuffle"}),onClick:ke,children:[X({en:"Random",vi:"Ngẫu nhiên"})," ",e.length>0?e.length:i.length]})})}),t.jsx(G,{text:i.map((e=>e.text)).join(" ")})]}),t.jsx(W,{open:he,onCancel:()=>ue(!1),destroyOnHidden:!0,title:X({en:"Random comment",vi:"Bình luận ngẫu nhiên"}),styles:{header:{textAlign:"center"},body:{padding:0}},footer:null,children:he&&t.jsx(H,{comments:i.map(((e,t)=>({...e,index:t.toString()}))),onPressWhere:e=>{var t,n;null==(n=fe.current)||n.setSearch(e.text||(null==(t=null==e?void 0:e.author)?void 0:t.name))}})})]})},onSearchRow:(e,t)=>{var n;return[null==(n=t.author)?void 0:n.name,t.text].some((t=>_(e,t)))},searchPlaceholder:X({en:"Search by name, text...",vi:"Tìm kiếm tên, nội dung..."})})]})}export{X as default};
