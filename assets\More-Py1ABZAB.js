import{b0 as e,b5 as i}from"./index-Cak6rALw.js";import{S as a}from"./Screen-Buq7HJpK.js";import{u as n,S as o,c as s,T as t}from"./MyApp-DW5WH4Ub.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";function c(){const{ti:c}=n();return e.jsx(a,{mode:"center",title:c({en:"More Search Tools",vi:"Công cụ tìm kiếm khác"}),children:e.jsx(o,{direction:"vertical",style:{justifyContent:"center"},children:[{header:c({en:"Information Tools",vi:"Công cụ thông tin"}),icon:e.jsx("i",{className:"fa-solid fa-info"})},{label:c({en:"Leaked Passwords",vi:"<PERSON>ật khẩu bị lộ"}),description:c({en:"Check if your password has been compromised in a data breach",vi:"Kiểm tra xem mật khẩu của bạn có bị lộ trong dữ liệu rò rỉ hay không"}),icon:e.jsx("i",{className:"fa-solid fa-key"}),href:"https://haveibeenpwned.com/Passwords"},{label:c({en:"Leaked Emails",vi:"Email bị lộ"}),description:c({en:"Check if your email has been compromised in a data breach",vi:"Kiểm tra xem email của bạn có bị lộ trong dữ liệu rò rỉ hay không"}),icon:e.jsx("i",{className:"fa-solid fa-envelope"}),href:"https://haveibeenpwned.com/"},{label:c({en:"Google Fact Check - Verify information",vi:"Google Fact Check - Xác thực thông tin"}),description:c({en:"Search for fact check by google",vi:"Tìm kiếm các bản tin fact check bởi google"}),icon:e.jsx("i",{className:"fa-solid fa-circle-check"}),href:"https://toolbox.google.com/factcheck/explorer/search/list:recent;hl=en"},{header:c({en:"Image Tools",vi:"Công cụ ảnh"}),icon:e.jsx("i",{className:"fa-solid fa-image"})},{label:c({en:"Generate a random person face",vi:"Tạo khuôn mặt người ngẫu nhiên"}),description:c({en:"Generate a random person face",vi:"Tạo khuôn mặt người ngẫu nhiên"}),icon:e.jsx("i",{className:"fa-solid fa-face-smile"}),href:"https://thispersondoesnotexist.com/"},{label:c({en:"Shademap - Image time detector",vi:"Shademap - Thời gian chụp ảnh"}),description:c({en:"Detect the time of an image using the sun position",vi:"Xác định thời gian chụp ảnh dựa vào vị trí mặt trời"}),icon:e.jsx("i",{className:"fa-solid fa-map-location-dot"}),href:"https://shademap.app/"},{label:c({en:"SunCalc - Image time detector",vi:"SunCalc - Thời gian chụp ảnh"}),description:c({en:"Detect the time of an image using the sun position",vi:"Xác định thời gian chụp ảnh dựa vào vị trí mặt trời"}),icon:e.jsx("i",{className:"fa-solid fa-sun"}),href:"https://www.suncalc.org/"},{label:c({en:"Photo Forensics - Fake image detector",vi:"Photo Forensics - Kiểm tra ảnh giả"}),description:c({en:"Detect fake images (edited) using a lot of techniques",vi:"Kiểm tra ảnh giả (bị chỉnh sửa) bằng nhiều phương pháp chuyên sâu"}),icon:e.jsx("i",{className:"fa-solid fa-image"}),href:"https://29a.ch/photo-forensics/#forensic-magnifier"},{label:c({en:"Jimpl - Image metadata viewer",vi:"Jimpl - Xem metadata ảnh"}),description:c({en:"View more information about an image",vi:"Xem được nhiều thông tin hơn về bức ảnh"}),icon:e.jsx("i",{className:"fa-solid fa-file-image"}),href:"https://jimpl.com/"},{header:c({en:"Map Tools",vi:"Công cụ bản đồ"}),icon:e.jsx("i",{className:"fa-solid fa-map"})},{label:c({en:"MC - Map comparator",vi:"MC - So sánh bản đồ"}),description:c({en:"Compare multiple maps from different sources",vi:"So sánh bản đồ từ nhiều nguồn khác nhau"}),icon:e.jsx("i",{className:"fa-solid fa-map"}),href:"https://mc.bbbike.org/mc/"},{header:c({en:"OSINT Tools",vi:"Công cụ OSINT"}),icon:e.jsx("i",{className:"fa-solid fa-magnifying-glass"})},{label:c({en:"OSINT Tools",vi:"Công cụ OSINT"}),description:c({en:"Free online tools for OSINT",vi:"Công cụ OSINT miễn phí"}),icon:e.jsx("i",{className:"fa-solid fa-magnifying-glass"}),href:"https://www.osintcombine.com/tools"}].map((a=>a.header?e.jsx(s.Title,{level:4,children:a.header},a.header):e.jsx(t,{title:a.description,children:e.jsx(i,{icon:a.icon,onClick:()=>window.open(a.href,"_blank"),children:a.label},a.href)})))})})}export{c as default};
