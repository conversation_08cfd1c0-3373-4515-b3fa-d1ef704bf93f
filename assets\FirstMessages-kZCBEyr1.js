const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./VideoWithMuted-CYlwayKv.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./InfiniteScroll-CXje5Qht.js","./index-ByRdMNW-.js","./MyApp-DW5WH4Ub.js","./file-download-B-gszDlL.js"])))=>i.map(i=>d[i]);
import{b3 as e,r as t,bi as i,b0 as n,b5 as s,bj as r,aN as a,b1 as l,aS as o,aQ as d}from"./index-Cak6rALw.js";import{d as c}from"./dayjs.min-CzPn7FMI.js";import{u,a as h,d as g,t as m,j as f,S as v,c as p,T as x,C as j,h as y,i as w,I as b}from"./MyApp-DW5WH4Ub.js";import k,{getCacheByKey as M,setCacheByKey as N}from"./useCacheState-CAnxkewm.js";import{b as P,a as F,s as R,f as I,c as C}from"./messages-wZDWdkVz.js";import{checkVIP as T}from"./useVIP-D5FAMGQQ.js";import{getUidFromUrl as _}from"./getIds-DAzc-wzf.js";import{S as D}from"./Screen-Buq7HJpK.js";import{I as L}from"./index-Bg865k-U.js";import{D as S}from"./index-DwLJmsHL.js";import{L as E}from"./index-jrOnBmdW.js";import{A as O}from"./index-DdM4T8-Q.js";import{I as B}from"./index-CDSnY0KE.js";import"./sweetalert2.esm.all-BZxvatOx.js";import"./index-IxNfZAD-.js";import"./Dropdown-BcL-JHCf.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./PurePanel-BvHjNOhQ.js";import"./ClockCircleOutlined-BVovxNRp.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./index-PbLYNhdQ.js";import"./addEventListener-C4tIKh7N.js";const H=l((()=>a((()=>import("./VideoWithMuted-CYlwayKv.js")),__vite__mapDeps([0,1,2]),import.meta.url)),{fallback:b}),Y=l((()=>a((()=>import("./InfiniteScroll-CXje5Qht.js")),__vite__mapDeps([3,1,2,4,5]),import.meta.url)),{fallback:j});function V(){var l,o;const{ti:d}=u(),j=e(),{profile:y}=h(),{message:w,notification:b}=g(),O=(null==(l=j.state)?void 0:l.threadId)||"",[B,H]=k("FirstMessages.friendUrlOrUid."+O,O),[V,A]=k("FirstMessages.friendProfile."+B,null),[W,U]=k("FirstMessages.messages."+B,[]),[z,Q]=k("FirstMessages.time."+O,null),[q,G]=t.useState(!1),[J,$]=t.useState({fetchingNext:!1,fetchingPrev:!1,hasNext:!0,hasPrev:!0}),X=(null==V?void 0:V.name)||(null==(o=j.state)?void 0:o.threadName)||"";t.useEffect((()=>{B&&!(null==W?void 0:W.length)&&re()}),[B,null==W?void 0:W.length]),t.useEffect((()=>{var e,t,i;null==(i=null==(e=ee.current)?void 0:e.scrollTo)||i.call(e,{top:M("FirstMessages.scrollY",(null==(t=ee.current)?void 0:t.scrollHeight)||0)})}),[]);const Z=t.useMemo((()=>{var e,t,i;if(!(null==W?void 0:W.length))return[];const n=[];for(let s=0;s<W.length;s++){let r=null==(e=W[s-1])?void 0:e.sender,a=null==(t=W[s])?void 0:t.sender,l=null==(i=W[s+1])?void 0:i.sender,o="single";a==r&&a==l?o="middle":a==r?o="end":a==l&&(o="start"),n.push({...W[s],position:o})}return n}),[W]),ee=t.useRef(null),te=t.useRef(0),ie=e=>{var t;te.current=(null==(t=ee.current)?void 0:t.scrollHeight)||0,console.log(e),U(e),N("FirstMessages.scrollY",te.current)},ne=t.useRef({prev:!1,next:!1});t.useLayoutEffect((()=>{var e,t,i,n;if(ne.current.prev&&!J.fetchingPrev){ne.current.prev=!1;let i=(null==(e=ee.current)?void 0:e.scrollHeight)||0;i>te.current&&(null==(t=ee.current)||t.scrollTo({top:i-te.current}))}se.current&&(se.current=!1,null==(n=ee.current)||n.scrollTo({top:(null==(i=ee.current)?void 0:i.scrollHeight)||0}))}),[W,J.fetchingPrev]);const se=t.useRef(!1),re=async()=>{var e,t;if(!(null==(t=null==(e=null==B?void 0:B.trim)?void 0:e.call(B))?void 0:t.length))return w.warning({content:d({en:"Please input friend's url/uid first",vi:"Vui lý nhập uid hoặc link bạn bè trước"})});m("FirstMessages:getRecentMessage");const n="getRecentMessage";try{$(i((e=>{e.hasNext=!1,e.hasPrev=!1})));let e=M("FirstMessages.friendProfile."+B);if(!e){let t;if(/\d+$/.test(B)?t=B:(w.loading({key:n,duration:0,content:d({en:"Fetching friend uid...",vi:"Đang tải uid bạn bè..."})}),t=await _(B)),!t)throw new Error("Invalid friend url");w.loading({key:n,duration:0,content:d({en:"Fetching friend info...",vi:"Đang tải thông tin bạn bè"})});try{e=await f(t)}catch(s){}e||(e={uid:t})}A(e),console.log(e),w.loading({key:n,duration:0,content:d({en:"Fetching recent messages...",vi:"Đang tải tin nhắn gần nhất..."})});const t=c();Q(t);const r=await P({threadId:e.uid,time:t.valueOf()});ie(r),(null==r?void 0:r.length)>0?w.success({key:n,content:d({en:"Fetch completed",vi:"Tải xong"})}):w.info({key:n,content:d({en:"No data to show",vi:"Không có dữ liệu"})}),se.current=!0,$(i((e=>{e.hasNext=r.length>1,e.hasPrev=!0,e.fetchingNext=!1,e.fetchingPrev=!1})))}catch(s){w.error({key:n,content:d({en:"Failed to fetch",vi:"Lỗi tải"})+": "+s.message})}},ae=async()=>{var e;if(V){m("FirstMessages:fetchNext");try{$(i((e=>{e.fetchingNext=!0})));const t=await C({threadId:null==V?void 0:V.uid,msgId:null==(e=null==W?void 0:W[W.length-1])?void 0:e.id,direction:"down"});console.log(t),t.length>1&&(t.shift(),ie([...W,...t])),ne.current.next=!0,$(i((e=>{e.hasNext=t.length>1})))}catch(t){w.error(d({en:"Failed to fetch",vi:"Lỗi tải"})+": "+t.message)}finally{$(i((e=>{e.fetchingNext=!1})))}}},le=async()=>{var e;if(V){m("FirstMessages:fetchPrev");try{$(i((e=>{e.fetchingPrev=!0})));const t=await C({threadId:null==V?void 0:V.uid,msgId:null==(e=null==W?void 0:W[0])?void 0:e.id,direction:"up"});t.length>1&&(t.pop(),ie([...t,...W])),ne.current.prev=!0,$(i((e=>{e.hasPrev=t.length>1})))}catch(t){console.error(t),w.error(d({en:"Failed to fetch",vi:"Lỗi tải"})+": "+t.message)}finally{$(i((e=>{e.fetchingPrev=!1})))}}};return n.jsxs(D,{children:[n.jsxs(v,{align:"center",wrap:!0,children:[n.jsx(p.Title,{level:3,children:X||d({en:"First messages",vi:"Tin nhắn đầu tiên"})}),(null==V?void 0:V.uid)&&n.jsxs(v.Compact,{children:[n.jsx(x,{title:d({en:"Open in Messenger",vi:"Mở trong Messenger"}),children:n.jsx(s,{icon:n.jsx("i",{className:"fa-solid fa-external-link"}),href:"https://www.facebook.com/messages/t/"+(null==V?void 0:V.uid),target:"_blank"})}),n.jsx(x,{title:d({en:"Download all messages",vi:"Tải xuống tất cả tin nhắn"}),children:n.jsx(s,{icon:n.jsx("i",{className:"fa-solid fa-download"}),onClick:async()=>{if(!(await T()))return;const e="FirstMessages:downloadAllMessages";m(e);const t=e+(null==V?void 0:V.uid);w.loading({key:t,duration:0,content:d({en:"Downloading messages...",vi:"Đang tải tin nhắn..."})+X});let i=!1;const s=await F({threadId:null==V?void 0:V.uid,checkStopFn:()=>i,progressCallback:e=>{w.loading({key:t,duration:0,content:n.jsxs(n.Fragment,{children:[d({en:"Downloading messages... ",vi:"Đang tải tin nhắn... "})+X,n.jsx("br",{}),e.length,d({en:" messages",vi:" tin nhắn"}),n.jsx("br",{}),c(e[0].time).format("YYYY-MM-DD HH:mm:ss"),n.jsx("br",{}),n.jsx("i",{children:d({en:"Click to stop",vi:"Bấm để dừng"})})]}),onClick:()=>{w.loading({key:t,duration:0,content:d({en:"Stopping...",vi:"Đang dừng..."})}),i=!0}})}});w.destroy(t),b.open({type:"success",duration:0,message:d(i?{en:"Download stopped: ",vi:"Đã dừng tải: "}:{en:"Download completed: ",vi:"Tải xong: "})+X,description:s.length+d({en:" messages",vi:" tin nhắn"})});const r=R({threadId:(null==V?void 0:V.uid)||"",threadName:X,myUid:(null==y?void 0:y.uid)||"",msgs:s});(await(await a((async()=>{const{default:e}=await import("./file-download-B-gszDlL.js").then((e=>e.f));return{default:e}}),__vite__mapDeps([6,1,2]),import.meta.url)).default)(r,X+".html")},disabled:!V})})]}),n.jsxs(v.Compact,{children:[n.jsx(x,{title:d({en:"Enter friend url/uid or thread id",vi:"Nhập url/uid của bạn bè/đoạn chat"}),children:n.jsx(L,{value:B,placeholder:d({en:"Enter friend url/uid or thread id",vi:"Nhập url/uid của bạn bè/đoạn chat"}),onChange:e=>H(e.target.value)})}),n.jsx(s,{type:"primary",onClick:re,loading:q,children:n.jsx("i",{className:"fas fa-search"})})]}),n.jsxs(v.Compact,{children:[n.jsx(x,{title:d({en:"Choose any date to view messages in that day",vi:"Chọn ngày bất kỳ để xem tin nhắn trong ngày đó"}),children:n.jsx(S,{showTime:!0,value:z,minDate:c(r),onChange:(e,t)=>{console.log("Selected Time: ",e),console.log("Formatted Selected Time: ",t),Q(e)},onOk:async e=>{if(!(await T())||!V)return;m("FirstMessages:onSelectDate");let t=c(e).valueOf();const n=await P({threadId:null==V?void 0:V.uid,time:t});console.log(n),ie(n),$(i((e=>{e.hasNext=!0,e.hasPrev=!0})))},disabled:!V,placeholder:d({en:"Select time",vi:"Chọn thời gian"})})}),n.jsx(x,{title:d({en:"Find first message",vi:"Tìm tin nhắn đầu tiên"}),children:n.jsx(s,{type:"primary",onClick:async()=>{if(await T()&&V){m("FirstMessages:getFirstMessage");try{G(!0);const e=await I({threadId:null==V?void 0:V.uid,progress:e=>{Q(c(e))}});if(console.log("first message",e),null==e?void 0:e.length){let t=await C({threadId:null==V?void 0:V.uid,msgId:e[0].id});(null==t?void 0:t.length)||(t=await P({threadId:null==V?void 0:V.uid,time:c(Number(e[0].time)).add(1,"day").valueOf()})),console.log(t),t.length?ie(t):w.info(d({en:"No data to show",vi:"Không có dữ liệu"})),$(i((e=>{e.hasNext=!0,e.hasPrev=!0})))}else w.info(d({en:"No data to show",vi:"Không có dữ liệu"}))}catch(e){w.error(d({en:"Failed to fetch",vi:"Lỗi tải"})+": "+e.message),console.log(e)}finally{G(!1)}}},loading:q,disabled:!V,children:n.jsx("i",{className:"fa-solid fa-clock-rotate-left"})})})]})]}),n.jsx(v,{ref:ee,direction:"vertical",size:3,style:{width:"100%",maxHeight:"70vh",overflow:"auto",padding:12},children:(null==Z?void 0:Z.length)?n.jsx(Y,{prev:le,next:ae,hasPrev:J.hasPrev,hasNext:J.hasNext,loader:n.jsx(v,{style:{display:"flex",justifyContent:"center"},children:n.jsx("i",{className:"fa-solid fa-spinner fa-spin fa-lg"})}),endMessage:n.jsx(v,{style:{display:"flex",justifyContent:"center"},children:d({en:"No more message",vi:"Không còn tin nào"})}),children:n.jsx(E,{split:!1,dataSource:Z,renderItem:e=>n.jsx(K,{message:e,myProfile:y})})}):null})]})}function A(e,t){if(t){if("start"===e)return{borderBottomRightRadius:4};if("middle"===e)return{borderBottomRightRadius:4,borderTopRightRadius:4};if("end"===e)return{borderTopRightRadius:4}}else{if("start"===e)return{borderBottomLeftRadius:4};if("middle"===e)return{borderBottomLeftRadius:4,borderTopLeftRadius:4};if("end"===e)return{borderTopLeftRadius:4}}return{}}function K({message:e,myProfile:t}){var i;const s=o(d.darkMode),r=e.position,a=e.sender===(null==t?void 0:t.uid),l={display:"block",wordBreak:"break-word",padding:"8px 12px",backgroundColor:a?"#0184ff":s?"#303030":"#f0f0f0",color:s||a?"#eee":"#111",whiteSpace:"pre-line",margin:0,maxWidth:400,borderTopLeftRadius:24,borderTopRightRadius:24,borderBottomLeftRadius:24,borderBottomRightRadius:24,...A(r,a)},u="end"===r||"single"===r,h=n.jsx("div",{style:{flex:1}});return n.jsxs("div",{style:{display:"flex",alignItems:"center",width:"100%",marginBottom:u?12:2},children:[a?h:u?n.jsx("a",{href:y(e.sender),target:"_blank",children:n.jsx(O,{shape:"circle",size:40,src:w(e.sender),style:{marginRight:8}})}):n.jsx("div",{style:{width:48}}),n.jsxs(x,{title:c(e.time).format("YYYY-MM-DD HH:mm"),placement:a?"left":"right",children:[e.text&&n.jsx(p.Paragraph,{style:l,children:e.text}),e.sticker&&n.jsx(B,{width:150,src:e.sticker}),null==(i=e.attachments)?void 0:i.map(((e,t)=>"video"===e.type?n.jsx(H,{src:e.uri,style:{maxHeight:300,maxWidth:300}},"attachment"+t):"image"===e.type||"gif"===e.type?n.jsx(B,{src:e.uri,style:{maxHeight:300,maxWidth:300}},"attachment"+t):"file"===e.type?n.jsxs("a",{href:e.uri,target:"_blank",rel:"noreferrer",style:l,children:["File: ",e.filename]},"attachment"+t):null))]}),!a&&h]})}export{V as default};
