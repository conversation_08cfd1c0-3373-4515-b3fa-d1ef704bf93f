import{$ as e,r as t,G as i,m as n,Z as a,L as r,M as o,J as l,O as s,R as m,aw as d,br as c,i as g}from"./index-Cak6rALw.js";import{u as p,r as $}from"./useBreakpoint-CPcdMRfj.js";import{D as f}from"./index-C5gzSBcY.js";import{R as u}from"./row-BMfM-of4.js";import{P as h}from"./Pagination-2X6SDgot.js";import{ah as y}from"./MyApp-DW5WH4Ub.js";import{C as S}from"./col-CzA09p0P.js";const x=e.createContext({});x.Consumer;var v=function(e,t){var i={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(i[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(i[n[a]]=e[n[a]])}return i};const b=e.forwardRef(((r,o)=>{const{prefixCls:l,children:s,actions:m,extra:d,styles:c,className:g,classNames:p,colStyle:$}=r,f=v(r,["prefixCls","children","actions","extra","styles","className","classNames","colStyle"]),{grid:u,itemLayout:h}=t.useContext(x),{getPrefixCls:y,list:b}=t.useContext(i),C=e=>{var t,i;return n(null===(i=null===(t=null==b?void 0:b.item)||void 0===t?void 0:t.classNames)||void 0===i?void 0:i[e],null==p?void 0:p[e])},E=e=>{var t,i;return Object.assign(Object.assign({},null===(i=null===(t=null==b?void 0:b.item)||void 0===t?void 0:t.styles)||void 0===i?void 0:i[e]),null==c?void 0:c[e])},k=y("list",l),O=m&&m.length>0&&e.createElement("ul",{className:n(`${k}-item-action`,C("actions")),key:"actions",style:E("actions")},m.map(((t,i)=>e.createElement("li",{key:`${k}-item-action-${i}`},t,i!==m.length-1&&e.createElement("em",{className:`${k}-item-action-split`}))))),N=u?"div":"li",j=e.createElement(N,Object.assign({},f,u?{}:{ref:o},{className:n(`${k}-item`,{[`${k}-item-no-flex`]:!("vertical"===h?d:!(()=>{let e=!1;return t.Children.forEach(s,(t=>{"string"==typeof t&&(e=!0)})),e&&t.Children.count(s)>1})())},g)}),"vertical"===h&&d?[e.createElement("div",{className:`${k}-item-main`,key:"content"},s,O),e.createElement("div",{className:n(`${k}-item-extra`,C("extra")),key:"extra",style:E("extra")},d)]:[s,O,a(d,{key:"extra"})]);return u?e.createElement(S,{ref:o,flex:1,style:$},j):j}));b.Meta=a=>{var{prefixCls:r,className:o,avatar:l,title:s,description:m}=a,d=v(a,["prefixCls","className","avatar","title","description"]);const{getPrefixCls:c}=t.useContext(i),g=c("list",r),p=n(`${g}-item-meta`,o),$=e.createElement("div",{className:`${g}-item-meta-content`},s&&e.createElement("h4",{className:`${g}-item-meta-title`},s),m&&e.createElement("div",{className:`${g}-item-meta-description`},m));return e.createElement("div",Object.assign({},d,{className:p}),l&&e.createElement("div",{className:`${g}-item-meta-avatar`},l),(s||m)&&$)};const C=e=>{const{listBorderedCls:t,componentCls:i,paddingLG:n,margin:a,itemPaddingSM:r,itemPaddingLG:o,marginLG:s,borderRadiusLG:m}=e;return{[t]:{border:`${l(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:m,[`${i}-header,${i}-footer,${i}-item`]:{paddingInline:n},[`${i}-pagination`]:{margin:`${l(a)} ${l(s)}`}},[`${t}${i}-sm`]:{[`${i}-item,${i}-header,${i}-footer`]:{padding:r}},[`${t}${i}-lg`]:{[`${i}-item,${i}-header,${i}-footer`]:{padding:o}}}},E=e=>{const{componentCls:t,screenSM:i,screenMD:n,marginLG:a,marginSM:r,margin:o}=e;return{[`@media screen and (max-width:${n}px)`]:{[t]:{[`${t}-item`]:{[`${t}-item-action`]:{marginInlineStart:a}}},[`${t}-vertical`]:{[`${t}-item`]:{[`${t}-item-extra`]:{marginInlineStart:a}}}},[`@media screen and (max-width: ${i}px)`]:{[t]:{[`${t}-item`]:{flexWrap:"wrap",[`${t}-action`]:{marginInlineStart:r}}},[`${t}-vertical`]:{[`${t}-item`]:{flexWrap:"wrap-reverse",[`${t}-item-main`]:{minWidth:e.contentWidth},[`${t}-item-extra`]:{margin:`auto auto ${l(o)}`}}}}}},k=e=>{const{componentCls:t,antCls:i,controlHeight:n,minHeight:a,paddingSM:r,marginLG:o,padding:m,itemPadding:d,colorPrimary:c,itemPaddingSM:g,itemPaddingLG:p,paddingXS:$,margin:f,colorText:u,colorTextDescription:h,motionDurationSlow:y,lineWidth:S,headerBg:x,footerBg:v,emptyTextPadding:b,metaMarginBottom:C,avatarMarginRight:E,titleMarginBottom:k,descriptionFontSize:O}=e;return{[t]:Object.assign(Object.assign({},s(e)),{position:"relative","*":{outline:"none"},[`${t}-header`]:{background:x},[`${t}-footer`]:{background:v},[`${t}-header, ${t}-footer`]:{paddingBlock:r},[`${t}-pagination`]:{marginBlockStart:o,[`${i}-pagination-options`]:{textAlign:"start"}},[`${t}-spin`]:{minHeight:a,textAlign:"center"},[`${t}-items`]:{margin:0,padding:0,listStyle:"none"},[`${t}-item`]:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:d,color:u,[`${t}-item-meta`]:{display:"flex",flex:1,alignItems:"flex-start",maxWidth:"100%",[`${t}-item-meta-avatar`]:{marginInlineEnd:E},[`${t}-item-meta-content`]:{flex:"1 0",width:0,color:u},[`${t}-item-meta-title`]:{margin:`0 0 ${l(e.marginXXS)} 0`,color:u,fontSize:e.fontSize,lineHeight:e.lineHeight,"> a":{color:u,transition:`all ${y}`,"&:hover":{color:c}}},[`${t}-item-meta-description`]:{color:h,fontSize:O,lineHeight:e.lineHeight}},[`${t}-item-action`]:{flex:"0 0 auto",marginInlineStart:e.marginXXL,padding:0,fontSize:0,listStyle:"none","& > li":{position:"relative",display:"inline-block",padding:`0 ${l($)}`,color:h,fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"center","&:first-child":{paddingInlineStart:0}},[`${t}-item-action-split`]:{position:"absolute",insetBlockStart:"50%",insetInlineEnd:0,width:S,height:e.calc(e.fontHeight).sub(e.calc(e.marginXXS).mul(2)).equal(),transform:"translateY(-50%)",backgroundColor:e.colorSplit}}},[`${t}-empty`]:{padding:`${l(m)} 0`,color:h,fontSize:e.fontSizeSM,textAlign:"center"},[`${t}-empty-text`]:{padding:b,color:e.colorTextDisabled,fontSize:e.fontSize,textAlign:"center"},[`${t}-item-no-flex`]:{display:"block"}}),[`${t}-grid ${i}-col > ${t}-item`]:{display:"block",maxWidth:"100%",marginBlockEnd:f,paddingBlock:0,borderBlockEnd:"none"},[`${t}-vertical ${t}-item`]:{alignItems:"initial",[`${t}-item-main`]:{display:"block",flex:1},[`${t}-item-extra`]:{marginInlineStart:o},[`${t}-item-meta`]:{marginBlockEnd:C,[`${t}-item-meta-title`]:{marginBlockStart:0,marginBlockEnd:k,color:u,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}},[`${t}-item-action`]:{marginBlockStart:m,marginInlineStart:"auto","> li":{padding:`0 ${l(m)}`,"&:first-child":{paddingInlineStart:0}}}},[`${t}-split ${t}-item`]:{borderBlockEnd:`${l(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderBlockEnd:"none"}},[`${t}-split ${t}-header`]:{borderBlockEnd:`${l(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},[`${t}-split${t}-empty ${t}-footer`]:{borderTop:`${l(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},[`${t}-loading ${t}-spin-nested-loading`]:{minHeight:n},[`${t}-split${t}-something-after-last-item ${i}-spin-container > ${t}-items > ${t}-item:last-child`]:{borderBlockEnd:`${l(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},[`${t}-lg ${t}-item`]:{padding:p},[`${t}-sm ${t}-item`]:{padding:g},[`${t}:not(${t}-vertical)`]:{[`${t}-item-no-flex`]:{[`${t}-item-action`]:{float:"right"}}}}},O=r("List",(e=>{const t=o(e,{listBorderedCls:`${e.componentCls}-bordered`,minHeight:e.controlHeightLG});return[k(t),C(t),E(t)]}),(e=>({contentWidth:220,itemPadding:`${l(e.paddingContentVertical)} 0`,itemPaddingSM:`${l(e.paddingContentVerticalSM)} ${l(e.paddingContentHorizontal)}`,itemPaddingLG:`${l(e.paddingContentVerticalLG)} ${l(e.paddingContentHorizontalLG)}`,headerBg:"transparent",footerBg:"transparent",emptyTextPadding:e.padding,metaMarginBottom:e.padding,avatarMarginRight:e.padding,titleMarginBottom:e.paddingSM,descriptionFontSize:e.fontSize})));function N(e,a){const{pagination:r=!1,prefixCls:o,bordered:l=!1,split:s=!0,className:S,rootClassName:v,style:b,children:C,itemLayout:E,loadMore:k,grid:N,dataSource:j=[],size:z,header:B,footer:M,loading:w=!1,rowKey:P,renderItem:L,locale:I}=e,H=function(e,t){var i={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(i[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(i[n[a]]=e[n[a]])}return i}(e,["pagination","prefixCls","bordered","split","className","rootClassName","style","children","itemLayout","loadMore","grid","dataSource","size","header","footer","loading","rowKey","renderItem","locale"]),G=r&&"object"==typeof r?r:{},[W,T]=t.useState(G.defaultCurrent||1),[R,X]=t.useState(G.defaultPageSize||10),{getPrefixCls:A,direction:D,className:F,style:J}=m("list"),{renderEmpty:V}=t.useContext(i),K=e=>(t,i)=>{var n;T(t),X(i),r&&(null===(n=null==r?void 0:r[e])||void 0===n||n.call(r,t,i))},q=K("onChange"),Y=K("onShowSizeChange"),Z=(e,i)=>{if(!L)return null;let n;return n="function"==typeof P?P(e):P?e[P]:e.key,n||(n=`list-item-${i}`),t.createElement(t.Fragment,{key:n},L(e,i))},Q=!!(k||r||M),U=A("list",o),[_,ee,te]=O(U);let ie=w;"boolean"==typeof ie&&(ie={spinning:ie});const ne=!!(null==ie?void 0:ie.spinning);let ae="";switch(d(z)){case"large":ae="lg";break;case"small":ae="sm"}const re=n(U,{[`${U}-vertical`]:"vertical"===E,[`${U}-${ae}`]:ae,[`${U}-split`]:s,[`${U}-bordered`]:l,[`${U}-loading`]:ne,[`${U}-grid`]:!!N,[`${U}-something-after-last-item`]:Q,[`${U}-rtl`]:"rtl"===D},F,S,v,ee,te),oe=c({current:1,total:0,position:"bottom"},{total:j.length,current:W,pageSize:R},r||{}),le=Math.ceil(oe.total/oe.pageSize);oe.current=Math.min(oe.current,le);const se=r&&t.createElement("div",{className:n(`${U}-pagination`)},t.createElement(h,Object.assign({align:"end"},oe,{onChange:q,onShowSizeChange:Y})));let me=g(j);r&&j.length>(oe.current-1)*oe.pageSize&&(me=g(j).splice((oe.current-1)*oe.pageSize,oe.pageSize));const de=Object.keys(N||{}).some((e=>["xs","sm","md","lg","xl","xxl"].includes(e))),ce=p(de),ge=t.useMemo((()=>{for(let e=0;e<$.length;e+=1){const t=$[e];if(ce[t])return t}}),[ce]),pe=t.useMemo((()=>{if(!N)return;const e=ge&&N[ge]?N[ge]:N.column;return e?{width:100/e+"%",maxWidth:100/e+"%"}:void 0}),[JSON.stringify(N),ge]);let $e=ne&&t.createElement("div",{style:{minHeight:53}});if(me.length>0){const e=me.map(Z);$e=N?t.createElement(u,{gutter:N.gutter},t.Children.map(e,(e=>t.createElement("div",{key:null==e?void 0:e.key,style:pe},e)))):t.createElement("ul",{className:`${U}-items`},e)}else C||ne||($e=t.createElement("div",{className:`${U}-empty-text`},(null==I?void 0:I.emptyText)||(null==V?void 0:V("List"))||t.createElement(f,{componentName:"List"})));const fe=oe.position,ue=t.useMemo((()=>({grid:N,itemLayout:E})),[JSON.stringify(N),E]);return _(t.createElement(x.Provider,{value:ue},t.createElement("div",Object.assign({ref:a,style:Object.assign(Object.assign({},J),b),className:re},H),("top"===fe||"both"===fe)&&se,B&&t.createElement("div",{className:`${U}-header`},B),t.createElement(y,Object.assign({},ie),$e,C),M&&t.createElement("div",{className:`${U}-footer`},M),k||("bottom"===fe||"both"===fe)&&se)))}const j=t.forwardRef(N);j.Item=b;export{j as L};
