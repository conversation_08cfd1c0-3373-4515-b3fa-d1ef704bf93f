const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./VideoIntro-CygWxn40.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./MyApp-DW5WH4Ub.js","./index-Bx3o6rwA.js","./PurePanel-BvHjNOhQ.js"])))=>i.map(i=>d[i]);
import{r as e,b0 as i,b9 as s,bg as t,b5 as n,b1 as r,aN as o}from"./index-Cak6rALw.js";import{u as a,t as l,s as d,c,S as m,B as j,b as h,A as p,T as x}from"./MyApp-DW5WH4Ub.js";import u,{checkVIP as f}from"./useVIP-D5FAMGQQ.js";import{f as g}from"./features-BdMou7IV.js";import{S as v}from"./Screen-Buq7HJpK.js";import{u as y}from"./index-ByRdMNW-.js";import{F as b}from"./Table-BO80-bCE.js";import{R as I}from"./row-BMfM-of4.js";import{I as w}from"./index-Bg865k-U.js";import"./sweetalert2.esm.all-BZxvatOx.js";import"./getIds-DAzc-wzf.js";import"./index-IxNfZAD-.js";import"./Dropdown-BcL-JHCf.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./col-CzA09p0P.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./DownOutlined-B-JcS-29.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";const k=r((()=>o((()=>import("./VideoIntro-CygWxn40.js")),__vite__mapDeps([0,1,2,3,4,5]),import.meta.url)));function P(){const{ti:r}=a(),{isVIP:o,expiredTime:P,timeLeft:T}=u(),[V,_]=e.useState(""),N=y(_,500);e.useEffect((()=>{l("VIP:onLoad")}),[]);const S=e.useMemo((()=>(null==V?void 0:V.trim())?g.filter((e=>{var i,s;return d(V,e.name.vi+" "+e.name.en+" "+(null==(i=e.description)?void 0:i.vi)+" "+(null==(s=e.description)?void 0:s.en))})):g),[V]),L=[{title:"#",dataIndex:"id",key:"id",width:40,render:(e,i,s)=>i.header?"":i.id},{title:r({en:"Features",vi:"Chức năng"}),dataIndex:"name",key:"name",render:(e,n,o)=>{var a;return n.header?i.jsx(c.Title,{level:4,style:{marginTop:15,marginBottom:0},children:i.jsxs(s,{to:n.path,target:n.newTab?"_blank":"_self",children:[i.jsx("i",{className:n.icon})," ",r(e)]})}):i.jsx(t,{style:{maxWidth:500,flex:1},bordered:!1,expandIcon:()=>"",items:[{key:null==(a=n.name)?void 0:a.vi,label:i.jsxs(m,{children:[n.icon&&i.jsx("i",{className:n.icon,style:{opacity:.7}}),r(n.name),n.badge&&i.jsx(j,{color:n.badge.bg,count:r(n.badge.text),style:{color:n.badge.color}})]}),children:i.jsxs(m,{direction:"vertical",children:[r(n.description),i.jsxs(m,{children:[(null==n?void 0:n.path)&&i.jsxs("b",{children:[" ",i.jsxs(s,{to:n.path,target:n.newTab?"_blank":"_self",children:[r({en:"Try",vi:"Thử ngay"})," ",i.jsx("i",{className:"fa-solid fa-arrow-right"})]})]}),(null==n?void 0:n.link)&&i.jsx("b",{children:i.jsxs(s,{to:n.link,target:"_blank",children:[r({en:"More info",vi:"Xem thêm"})," ",i.jsx("i",{className:"fa-solid fa-up-right-from-square"})]})})]})]})}]})}},{title:"Free",dataIndex:"requireVIP",key:"requireVIP",align:"center",render:(e,s,t)=>s.header?"":s.requireVIP?i.jsx(h,{color:"gold",children:i.jsx("i",{className:"fa-solid fa-crown"})}):i.jsx(h,{color:"success",children:i.jsx("i",{className:"fa-solid fa-check"})})}],E=i.jsxs(m,{wrap:!0,style:{margin:8,justifyContent:"center",width:"100%"},align:"center",size:8,children:[o?i.jsx(p,{showIcon:!0,type:"success",message:i.jsxs(i.Fragment,{children:[r({en:"VIP Unlocked",vi:"Thành viên VIP"})+" ",i.jsxs(x,{title:r({en:"Until ",vi:"Tới "})+new Date(P).toLocaleString(),children:["(",i.jsx("i",{children:T}),")"]})]})}):P?i.jsx(p,{showIcon:!0,type:"info",message:r({en:"Expired at",vi:"Hết hạn lúc"})+" "+new Date(P).toLocaleString()}):null,i.jsx(j,{count:r({en:"New",vi:"Mới"}),style:{color:"white"},color:"purple",children:i.jsx(n,{size:"large",icon:i.jsx("i",{className:"fa-solid fa-crown",style:{color:"gold"}}),onClick:()=>f(!0),children:r(o?{en:"Renew VIP",vi:"Gia hạn VIP"}:{en:"Unlock VIP",vi:"Mở khoá VIP"})})})]});return i.jsxs(v,{title:"✨ "+r({en:"All Features",vi:"Tất cả tính năng"}),mode:"center",children:[i.jsx(k,{style:{marginBottom:20}}),E,i.jsx(p,{type:"info",showIcon:!0,message:r({en:"Click any feature to see more details",vi:"Nhấn chức năng bất kỳ để xem chi tiết"}),style:{marginBottom:10}}),i.jsx(b,{sticky:{offsetHeader:0},fixedHeader:!0,size:"small",tableLayout:"auto",dataSource:S,columns:L,pagination:!1,style:{width:600},rowKey:e=>e.id,footer:()=>E,title:()=>i.jsx(I,{justify:"end",children:i.jsx(w.Search,{placeholder:r({en:"Search feature",vi:"Tìm kiếm chức năng"}),onChange:e=>N(e.target.value)})})})]})}export{P as default};
