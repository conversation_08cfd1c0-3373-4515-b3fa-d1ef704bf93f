const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./index-Cak6rALw.js","./index-DIX6R8z-.css","./MyApp-DW5WH4Ub.js"])))=>i.map(i=>d[i]);
import{aN as i}from"./index-Cak6rALw.js";async function o(o){var e,l,n;const d=null==(e=o.match(/\/(\d+)(?=\?|$)/))?void 0:e[1];if(!d)throw new Error("Video ID not found");const u=o.includes("/play/"),r=o.includes("/video/"),{fetchExtension:t}=await i((async()=>{const{fetchExtension:i}=await import("./index-Cak6rALw.js").then((i=>i.cS));return{fetchExtension:i}}),__vite__mapDeps([0,1]),import.meta.url),a=await t("https://api.bilibili.tv/intl/gateway/web/playurl?"+new URLSearchParams({platform:"web",qn:"64",type:"0",device:"wap",...u&&{ep_id:d},...r&&{aid:d}}).toString()),{parseSafe:s}=await i((async()=>{const{parseSafe:i}=await import("./MyApp-DW5WH4Ub.js").then((i=>i.aU));return{parseSafe:i}}),__vite__mapDeps([2,0,1]),import.meta.url),v=s(a);console.log(a,v);const c=null==(l=null==v?void 0:v.data)?void 0:l.playurl;if(!c)throw new Error("Video info not found");const p=[(c.video||[]).map((i=>{var o,e,l,n,d,u,r,t,a,s;return{id:null==(o=null==i?void 0:i.video_resource)?void 0:o.id,length:((null==(e=null==i?void 0:i.video_resource)?void 0:e.duration)||0)/1e3,codecs:null==(l=null==i?void 0:i.video_resource)?void 0:l.codecs,mimeType:null==(n=null==i?void 0:i.video_resource)?void 0:n.mime_type,width:null==(d=null==i?void 0:i.video_resource)?void 0:d.width,height:null==(u=null==i?void 0:i.video_resource)?void 0:u.height,bandwidth:null==(r=null==i?void 0:i.video_resource)?void 0:r.bandwidth,isAudio:!1,qualityClass:null==(t=null==i?void 0:i.stream_info)?void 0:t.desc_words,source:null==(a=null==i?void 0:i.video_resource)?void 0:a.url,urls:null==(s=null==i?void 0:i.video_resource)?void 0:s.backup_url}})),(c.audio_resource||[]).map((i=>({id:null==i?void 0:i.id,length:((null==i?void 0:i.duration)||0)/1e3,codecs:null==i?void 0:i.codecs,mimeType:null==i?void 0:i.mime_type,width:0,height:0,bandwidth:null==i?void 0:i.bandwidth,isAudio:!0,qualityClass:"",source:null==i?void 0:i.url,urls:null==i?void 0:i.backup_url})))].flat().filter((i=>{var o;return!!i.source||(null==(o=i.urls)?void 0:o.length)>0}));return console.log(p),{source:null==(n=p[0])?void 0:n.source,length:((null==c?void 0:c.duration)||0)/1e3,variants:p}}export{o as getBilibiliVideoFromURL};
