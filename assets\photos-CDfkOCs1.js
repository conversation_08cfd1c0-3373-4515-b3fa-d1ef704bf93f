import{y as e,x as o,X as i,az as l}from"./MyApp-DW5WH4Ub.js";import"./index-Cak6rALw.js";var n=(e=>(e.OWNER="5",e.TAGGED="4",e))(n||{});const a={5:{en:"👤 Owner",vi:"👤 Chính chủ"},4:{en:"📌 Tagged",vi:"📌 Được tag"}};async function r({id:i,count:n=8,cursor:a="",type:r="5"}){const t=await e({doc_id:"4820192058049838",fb_api_caller_class:"RelayModern",fb_api_req_friendly_name:"ProfileCometAppCollectionPhotosRendererPaginationQuery",variables:{count:n,cursor:a,scale:1,id:btoa(`app_collection:${i}:2305272732:${r}`)}});let d=o(t),{edges:_=[],page_info:u}=l(d)||{};return _.map((e=>{var o,i,l,n,a,t,d,_,u,s,v,c,m,p,y;return{id:atob(null==(o=null==e?void 0:e.node)?void 0:o.id).split(":").pop(),url:null==(i=null==e?void 0:e.node)?void 0:i.url,thumbnail:null==(n=null==(l=null==e?void 0:e.node)?void 0:l.image)?void 0:n.uri,image:null==(d=null==(t=null==(a=null==e?void 0:e.node)?void 0:a.node)?void 0:t.viewer_image)?void 0:d.uri,width:null==(s=null==(u=null==(_=null==e?void 0:e.node)?void 0:_.node)?void 0:u.viewer_image)?void 0:s.width,height:null==(m=null==(c=null==(v=null==e?void 0:e.node)?void 0:v.node)?void 0:c.viewer_image)?void 0:m.height,accessibility_caption:null==(y=null==(p=null==e?void 0:e.node)?void 0:p.node)?void 0:y.accessibility_caption,cursor:null==e?void 0:e.cursor,photo_type:r}}))}async function t({id:i,count:l=8,cursor:n=""}){var a,r,t;const d=await e({doc_id:"6022153214500431",fb_api_caller_class:"RelayModern",fb_api_req_friendly_name:"GroupsCometMediaPhotosTabGridQuery",variables:{count:l,cursor:n,scale:2,id:i}}),_=o(d);console.log(_);const{edges:u=[],page_info:s}=(null==(t=null==(r=null==(a=null==_?void 0:_.data)?void 0:a.node)?void 0:r.group_mediaset)?void 0:t.media)||{};return u.map((e=>{var o,i,l,n,a,r,t,d,_,u,s;return{id:null==(o=null==e?void 0:e.node)?void 0:o.id,url:null==(i=null==e?void 0:e.node)?void 0:i.url,thumbnail:null==(n=null==(l=null==e?void 0:e.node)?void 0:l.image)?void 0:n.uri,image:null==(r=null==(a=null==e?void 0:e.node)?void 0:a.viewer_image)?void 0:r.uri,width:null==(d=null==(t=null==e?void 0:e.node)?void 0:t.viewer_image)?void 0:d.width,height:null==(u=null==(_=null==e?void 0:e.node)?void 0:_.viewer_image)?void 0:u.height,accessibility_caption:null==(s=null==e?void 0:e.node)?void 0:s.accessibility_caption,cursor:null==e?void 0:e.cursor}}))}async function d(i){var l,n,a,r,t,d,_;const u=await e({fb_api_req_friendly_name:"CometPhotoRootContentQuery",variables:{UFI2CommentsProvider_commentsKey:"CometPhotoRootQuery",feedbackSource:65,feedLocation:"COMET_MEDIA_VIEWER",isMediaset:!1,nodeID:i,privacySelectorRenderLocation:"COMET_MEDIA_VIEWER",renderLocation:"permalink",scale:2,useDefaultActor:!1,useHScroll:!1,focusCommentID:null,displayCommentsContextEnableComment:null,displayCommentsContextIsAdPreview:null,displayCommentsContextIsAggregatedShare:null,displayCommentsContextIsStorySet:null,displayCommentsFeedbackContext:null,__relay_internal__pv__CometUFIReactionEnableShortNamerelayprovider:!0,__relay_internal__pv__CometUFIShareActionMigrationrelayprovider:!1,__relay_internal__pv__CometUFIReactionsEnableShortNamerelayprovider:!1,__relay_internal__pv__CometImmersivePhotoCanUserDisable3DMotionrelayprovider:!1},doc_id:"7830475950340566"}),s=o((null==(l=null==u?void 0:u.split("\n"))?void 0:l[0])||"{}"),v=(null==(n=null==s?void 0:s.data)?void 0:n.currMedia)||{};return{id:i,url:null==(a=v.creation_story)?void 0:a.url,accessibility_caption:v.accessibility_caption,image:null==(r=v.image)?void 0:r.uri,width:null==(t=v.image)?void 0:t.width,height:null==(d=v.image)?void 0:d.height,thumbnail:null==(_=v.image)?void 0:_.uri}}async function _(i,l){const n=await e({fb_api_req_friendly_name:"CometPhotoRootContentQuery",variables:{isMediaset:!0,renderLocation:"comet_media_viewer",nodeID:i,mediasetToken:l,scale:2,feedLocation:"COMET_MEDIA_VIEWER",feedbackSource:65,focusCommentID:null,glbFileURIHackToRenderAs3D_DO_NOT_USE:null,privacySelectorRenderLocation:"COMET_MEDIA_VIEWER",useDefaultActor:!1,shouldShowComments:!0,__relay_internal__pv__GHLShouldChangeSponsoredDataFieldNamerelayprovider:!0,__relay_internal__pv__CometUFIShareActionMigrationrelayprovider:!0,__relay_internal__pv__IsWorkUserrelayprovider:!1,__relay_internal__pv__CometUFIReactionsEnableShortNamerelayprovider:!1,__relay_internal__pv__CometImmersivePhotoCanUserDisable3DMotionrelayprovider:!1},doc_id:"29711186038527111"}),a=o(n,[],!0);return console.log(a),a}async function u(l){const n=await e({fb_api_req_friendly_name:"CometPhotoTagLayerQuery",variables:{nodeID:l,scale:2},doc_id:"9999655556759066"}),a=o(n,[],!0);console.log(a);const r=i(a,"data.image"),t=i(a,"data.container_story"),d=i(a,"data.owner");return{id:l,width:null==r?void 0:r.width,height:null==r?void 0:r.height,post:{id:null==t?void 0:t.id,post_id:null==t?void 0:t.post_id},owner:{type:null==d?void 0:d.__typename,id:null==d?void 0:d.id}}}export{n as PhotoCollectionType,a as PhotoCollectionTypeLang,u as getBasicPhotoInfo,t as getGroupPhotos,d as getLargestPhoto,_ as getPhotoInfo,r as getUserPhotos};
