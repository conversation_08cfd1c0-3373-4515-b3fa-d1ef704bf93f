import{bO as t,aJ as e,r as n}from"./index-Cak6rALw.js";var r,i;const o=e(function(){if(i)return r;i=1;var e=/^\s+|\s+$/g,n=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,u=/^0o[0-7]+$/i,c=parseInt,s="object"==typeof t&&t&&t.Object===Object&&t,f="object"==typeof self&&self&&self.Object===Object&&self,a=s||f||Function("return this")(),l=Object.prototype.toString,v=Math.max,y=Math.min,d=function(){return a.Date.now()};function g(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function b(t){if("number"==typeof t)return t;if(function(t){return"symbol"==typeof t||function(t){return!!t&&"object"==typeof t}(t)&&"[object Symbol]"==l.call(t)}(t))return NaN;if(g(t)){var r="function"==typeof t.valueOf?t.valueOf():t;t=g(r)?r+"":r}if("string"!=typeof t)return 0===t?t:+t;t=t.replace(e,"");var i=o.test(t);return i||u.test(t)?c(t.slice(2),i?2:8):n.test(t)?NaN:+t}return r=function(t,e,n){var r,i,o,u,c,s,f=0,a=!1,l=!1,h=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function p(e){var n=r,o=i;return r=i=void 0,f=e,u=t.apply(o,n)}function m(t){var n=t-s;return void 0===s||n>=e||n<0||l&&t-f>=o}function I(){var t=d();if(m(t))return O(t);c=setTimeout(I,function(t){var n=e-(t-s);return l?y(n,o-(t-f)):n}(t))}function O(t){return c=void 0,h&&r?p(t):(r=i=void 0,u)}function j(){var t=d(),n=m(t);if(r=arguments,i=this,s=t,n){if(void 0===c)return function(t){return f=t,c=setTimeout(I,e),a?p(t):u}(s);if(l)return c=setTimeout(I,e),p(s)}return void 0===c&&(c=setTimeout(I,e)),u}return e=b(e)||0,g(n)&&(a=!!n.leading,o=(l="maxWait"in n)?v(b(n.maxWait)||0,e):o,h="trailing"in n?!!n.trailing:h),j.cancel=function(){void 0!==c&&clearTimeout(c),f=0,r=s=i=c=void 0},j.flush=function(){return void 0===c?u:O(d())},j}}());var u="undefined"!=typeof window?n.useLayoutEffect:n.useEffect;function c(t,e){const r=n.useRef(t);u((()=>{r.current=t}),[t]),n.useEffect((()=>{const t=setInterval((()=>{r.current()}),e);return()=>{clearInterval(t)}}),[e])}function s(t,e=500,r){const i=n.useRef();!function(t){const e=n.useRef(t);e.current=t,n.useEffect((()=>()=>{e.current()}),[])}((()=>{i.current&&i.current.cancel()}));const u=n.useMemo((()=>{const n=o(t,e,r),u=(...t)=>n(...t);return u.cancel=()=>{n.cancel()},u.isPending=()=>!!i.current,u.flush=()=>n.flush(),u}),[t,e,r]);return n.useEffect((()=>{i.current=o(t,e,r)}),[t,e,r]),u}function f({threshold:t=0,root:e=null,rootMargin:r="0%",freezeOnceVisible:i=!1,initialIsIntersecting:o=!1,onChange:u}={}){var c;const[s,f]=n.useState(null),[a,l]=n.useState((()=>({isIntersecting:o,entry:void 0}))),v=n.useRef();v.current=u;const y=(null==(c=a.entry)?void 0:c.isIntersecting)&&i;n.useEffect((()=>{if(!s)return;if(!("IntersectionObserver"in window))return;if(y)return;const n=new IntersectionObserver((t=>{const e=Array.isArray(n.thresholds)?n.thresholds:[n.thresholds];t.forEach((t=>{const n=t.isIntersecting&&e.some((e=>t.intersectionRatio>=e));l({isIntersecting:n,entry:t}),v.current&&v.current(n,t)}))}),{threshold:t,root:e,rootMargin:r});return n.observe(s),()=>{n.disconnect()}}),[s,JSON.stringify(t),e,r,y,i]);const d=n.useRef(null);n.useEffect((()=>{var t;s||!(null==(t=a.entry)?void 0:t.target)||i||y||d.current===a.entry.target||(d.current=a.entry.target,l({isIntersecting:o,entry:void 0}))}),[s,a.entry,i,y,o]);const g=[f,!!a.isIntersecting,a.entry];return g.ref=g[0],g.isIntersecting=g[1],g.entry=g[2],g}export{f as a,c as b,s as u};
