const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./MyApp-DW5WH4Ub.js","./index-Cak6rALw.js","./index-DIX6R8z-.css"])))=>i.map(i=>d[i]);
import{r as e,b0 as i,b5 as r,aN as t}from"./index-Cak6rALw.js";import{W as l,y as n,x as o,X as a,u as _,S as d}from"./MyApp-DW5WH4Ub.js";import s from"./useCacheState-CAnxkewm.js";import{E as p}from"./index-SRy1SMeO.js";import{I as v}from"./index-CDSnY0KE.js";import"./EyeOutlined-CNKPohhw.js";import"./addEventListener-C4tIKh7N.js";function u({target:u}){const{ti:y}=_(),[f,m]=s("Friendship."+(null==u?void 0:u.id),[]),[h,g]=e.useState(!1);e.useEffect((()=>{x()}),[null==u?void 0:u.id]);const x=()=>{(null==u?void 0:u.id)&&(g(!0),async function(e){const i=await l(),r=await n({fb_api_req_friendly_name:"ProfileCometFriendshipTimelineRootQuery",variables:{feedbackSource:0,feedLocation:"TIMELINE_FRIENDSHIP",privacySelectorRenderLocation:"COMET_STREAM",renderLocation:"timeline",scale:2,user_id_1:i,user_id_2:e,userID:i,__relay_internal__pv__GHLShouldChangeAdIdFieldNamerelayprovider:!0,__relay_internal__pv__GHLShouldChangeSponsoredDataFieldNamerelayprovider:!0,__relay_internal__pv__IsWorkUserrelayprovider:!1,__relay_internal__pv__FBReels_deprecate_short_form_video_context_gkrelayprovider:!0,__relay_internal__pv__FeedDeepDiveTopicPillThreadViewEnabledrelayprovider:!1,__relay_internal__pv__CometImmersivePhotoCanUserDisable3DMotionrelayprovider:!1,__relay_internal__pv__WorkCometIsEmployeeGKProviderrelayprovider:!1,__relay_internal__pv__IsMergQAPollsrelayprovider:!1,__relay_internal__pv__FBReelsMediaFooter_comet_enable_reels_ads_gkrelayprovider:!0,__relay_internal__pv__CometUFIReactionsEnableShortNamerelayprovider:!1,__relay_internal__pv__CometUFIShareActionMigrationrelayprovider:!0,__relay_internal__pv__CometUFI_dedicated_comment_routable_dialog_gkrelayprovider:!1,__relay_internal__pv__StoriesArmadilloReplyEnabledrelayprovider:!0,__relay_internal__pv__FBReelsIFUTileContent_reelsIFUPlayOnHoverrelayprovider:!0},doc_id:"24517260827892142"}),t=o(r,[],!0);return(a(t,"friendship_context_items.nodes")||[]).map((e=>{var i,r,t,l,n;return{type:null==e?void 0:e.target_type,icon:null==(i=null==e?void 0:e.icon)?void 0:i.uri,text:(null==(r=null==e?void 0:e.plaintext_title)?void 0:r.text)||(null==(t=null==e?void 0:e.title)?void 0:t.text),url:null==e?void 0:e.url,entities:((null==(l=null==e?void 0:e.plaintext_title)?void 0:l.ranges)||(null==(n=null==e?void 0:e.title)?void 0:n.ranges)||[]).map((e=>{var i,r,t,l;return{type:null==(i=null==e?void 0:e.entity)?void 0:i.__typename,id:null==(r=null==e?void 0:e.entity)?void 0:r.id,url:(null==(t=null==e?void 0:e.entity)?void 0:t.url)||(null==(l=null==e?void 0:e.entity)?void 0:l.mobileUrl),length:null==e?void 0:e.length,offset:null==e?void 0:e.offset}}))}}))}(u.id).then(m).finally((()=>g(!1))))};return i.jsxs(d,{direction:"vertical",align:"center",style:{width:"100%",justifyContent:"center"},size:20,children:[i.jsxs(d.Compact,{children:[i.jsx(r,{type:"primary",icon:i.jsx("i",{className:"fa-solid fa-rotate-right"}),onClick:x,loading:h,children:y({en:"Reload",vi:"Tải lại"})}),i.jsx(r,{icon:i.jsx("i",{className:"fa-solid fa-up-right-from-square"}),onClick:async()=>{const{getMyUid:e}=await t((async()=>{const{getMyUid:e}=await import("./MyApp-DW5WH4Ub.js").then((e=>e.aV));return{getMyUid:e}}),__vite__mapDeps([0,1,2]),import.meta.url),i=await e();window.open(`https://fb.com/friendship/${i}/${null==u?void 0:u.id}`)},children:y({en:"More on Facebook",vi:"Xem trên Facebook"})})]}),(null==f?void 0:f.length)?i.jsx(d,{direction:"vertical",children:f.map((e=>{var r;return i.jsxs(d,{children:[e.icon&&i.jsx(v,{src:e.icon,style:{width:30,height:30,backgroundColor:"#bbb",borderRadius:5,padding:5},preview:!1}),(null==(r=e.entities)?void 0:r.length)?i.jsx(c,{text:e.text,entities:e.entities},e.url):i.jsx("div",{children:e.text})]},e.url)}))}):i.jsx(p,{image:p.PRESENTED_IMAGE_SIMPLE,description:y({en:"No data",vi:"Không có dữ liệu"})})]})}function c({text:e,entities:r}){if(!r||!r.length)return i.jsx("div",{children:e});const t=[...r].sort(((e,i)=>e.offset-i.offset)),l=[];let n=0;return t.forEach(((r,t)=>{r.offset>n&&l.push(e.substring(n,r.offset));const o=e.substring(r.offset,r.offset+r.length);l.push(i.jsx("a",{href:r.url,target:"_blank",rel:"noopener noreferrer",children:o},r.id||t)),n=r.offset+r.length})),n<e.length&&l.push(e.substring(n)),i.jsx("div",{children:l})}export{u as default};
