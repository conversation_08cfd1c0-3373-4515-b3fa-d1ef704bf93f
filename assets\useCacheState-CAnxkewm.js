import{aS as t,aQ as e,r as n}from"./index-Cak6rALw.js";const o={};function s(s,c,u=!1){const a=t(e.setCache),r=t(e.getCache),[f,i]=n.useState((()=>{if(u){const t=r(s);if(void 0!==t)return o[s]=t,t}return void 0===o[s]&&(o[s]=c),o[s]}));return n.useEffect((()=>{u?a(s,f):o[s]=f}),[f]),n.useEffect((()=>{const t=u?r(s):o[s];i(void 0!==t?t:c)}),[s,u]),[f,i]}function c(t,e){return void 0!==o[t]?o[t]:e}function u(n,s,c=!1){const u=o[n];if(o[n]=s,c){const o=e.setCache(t.getState());null==o||o(n,s)}return{oldValue:u,newValue:s}}function a(){console.log(o)}export{s as default,c as getCacheByKey,a as logCache,u as setCacheByKey};
