const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./copy-D4Fd8-zB.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./useVIP-D5FAMGQQ.js","./MyApp-DW5WH4Ub.js","./sweetalert2.esm.all-BZxvatOx.js","./getIds-DAzc-wzf.js","./index-Bg865k-U.js","./EyeOutlined-CNKPohhw.js","./SearchOutlined--mXbzQ68.js","./index-IxNfZAD-.js","./Dropdown-BcL-JHCf.js","./MySwal-8JMBfvUJ.js"])))=>i.map(i=>d[i]);
import{r as e,aM as n,b0 as t,b5 as r,aN as i,bg as s,aP as a}from"./index-Cak6rALw.js";import{u as o,W as c,x as l,j as d,aO as h,c as u,T as m,t as p,ah as f,S as v,b as g,f as x}from"./MyApp-DW5WH4Ub.js";import j from"./useCacheState-CAnxkewm.js";import{A as y}from"./index-DdM4T8-Q.js";import{I as b}from"./index-Bg865k-U.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-PbLYNhdQ.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";function w(){const{ti:w}=o(),[C,V]=j("Referral.friends",[]),[_,k]=j("Referral.friendsProfiles",new Map),[S,E]=e.useState(!1),[T,N]=j("Referral.entered",null),[M,R]=j("Referral.enteredProfile",null),[O,A]=j("Referral.myRefCode",""),[F,B]=e.useState(!1),[z,D]=e.useState(""),[L,K]=e.useState("");e.useEffect((()=>{c().then((async e=>{A(I(e));const t=await n(a.FB_AIO.server+"/check-referral?uid="+e),r=l(t||"{}");console.log("check",r);const{friends:i=[],entered:s=null}=r;if(V(i),N(s),null==s?void 0:s.uid){const e=await d(null==s?void 0:s.uid);R(e)}}))}),[]);const W=e.useRef(!1),Y=e.useRef(!1);e.useEffect((()=>{F&&C.length&&!W.current&&!Y.current&&(W.current=!0,(async()=>{E(!0);const e=new Map,n=C.map(((n,t)=>async()=>{try{const t=await d(null==n?void 0:n.uid);return e.set(null==n?void 0:n.uid,t),k(new Map(e)),t}catch(t){return console.error(t),null}}));await h(5,n).start(),E(!1),W.current=!1,Y.current=!0})())}),[F,C]),e.useEffect((()=>{K("")}),[z]);return t.jsxs(t.Fragment,{children:[t.jsx("h4",{children:w({en:t.jsxs(t.Fragment,{children:["Share code to your friend, both get"," ",t.jsx("b",{className:"highlight",children:"3 days VIP"})]}),vi:t.jsxs(t.Fragment,{children:["Rủ bạn bè nhập mã, cả 2 nhận ",t.jsx("b",{className:"highlight",children:"3 ngày VIP"})]})})}),t.jsx("hr",{}),t.jsxs("div",{className:"vip-option","data-option":w({en:"Share (unlimited)",vi:"Chia sẻ (vô hạn)"}),children:[t.jsxs(u.Text,{children:[w({en:"Your code",vi:"Mã của bạn"}),t.jsxs("b",{className:"highlight",style:{display:"block"},children:[O,t.jsx(m,{title:w({en:"Copy",vi:"Sao chép"}),children:t.jsx(r,{size:"small",type:"text",onClick:async()=>{p("VIP:copyRefCode");const{copyWithMessage:e}=await i((async()=>{const{copyWithMessage:e}=await import("./copy-D4Fd8-zB.js");return{copyWithMessage:e}}),__vite__mapDeps([0,1,2]),import.meta.url);e(O)},children:t.jsx("i",{className:"fa-regular fa-copy fa-lg"})})})]})]}),t.jsx("div",{style:{marginTop:10},children:C.length?t.jsx(s,{expandIconPosition:"end",size:"small",onChange:e=>B(e.length>0),activeKey:F?"1":void 0,children:t.jsx(s.Panel,{header:w({en:t.jsxs(u.Text,{children:["✅ ",C.length," friends enter your code",t.jsx("br",{}),"👑 added ",3*C.length," days VIP"]}),vi:t.jsxs(u.Text,{children:["✅ ",C.length," bạn bè đã nhập mã của bạn",t.jsx("br",{})," 👑 tặng ",3*C.length," ngày VIP"]})}),children:t.jsxs("div",{children:[S&&t.jsx(f,{}),t.jsx(v,{direction:"vertical",align:"start",children:C.map((e=>{var n,r;return t.jsxs(v,{size:0,children:[t.jsx(g,{children:x(e.time)}),t.jsx(y,{src:null==(n=_.get(e.uid))?void 0:n.avatar,size:30}),t.jsx(u.Link,{href:`https://www.facebook.com/${e.uid}`,target:"_blank",children:(null==(r=_.get(e.uid))?void 0:r.name)||e.uid})]},e.uid)}))})]})},"1")}):w({en:t.jsxs(t.Fragment,{children:["❓ No friends enter your code",t.jsx("br",{}),"Copy and Share now 🚀"]}),vi:t.jsxs(t.Fragment,{children:["❓ Chưa có ai nhập mã của bạn",t.jsx("br",{}),"Sao chép và Chia sẻ ngay 🚀"]})})})]}),t.jsx("hr",{}),t.jsx("div",{className:"vip-option","data-option":w({en:"Enter code (once)",vi:"Nhập mã (1 lần)"}),children:(null==T?void 0:T.uid)&&(null==T?void 0:T.time)?t.jsxs(u.Text,{children:[w({vi:"✅ Bạn đã nhập mã",en:"✅ Already entered code"}),t.jsx("br",{}),t.jsx("b",{className:"highlight",style:{display:"block"},children:I((null==T?void 0:T.uid)||"")}),M&&t.jsx("div",{children:t.jsxs(u.Link,{href:`https://www.facebook.com/${null==M?void 0:M.id}`,target:"_blank",children:[t.jsx(y,{src:null==M?void 0:M.avatar,size:50}),(null==M?void 0:M.name)||(null==M?void 0:M.id)]})}),w({en:"at",vi:"lúc"})," ",new Date(null==T?void 0:T.time).toLocaleString()]}):t.jsxs(v,{direction:"vertical",children:[t.jsxs(u.Text,{children:["🎁"," ",w({en:"Enter your friend's referral code",vi:"Nhập mã giới thiệu của bạn bè"})]}),t.jsx(b,{size:"large",value:z,onChange:e=>D(e.target.value),placeholder:w({en:"Friend referral code",vi:"Mã giới thiệu của bạn bè"}),allowClear:!0}),!!L&&t.jsxs(u.Text,{type:"danger",children:["❗",L]}),t.jsx(r,{type:"primary",icon:t.jsx("i",{className:"fa-solid fa-search"}),onClick:async()=>{var e;K("");const t=z;if(!t)return K(w({en:"Please enter referral code",vi:"Vui lòng nhập mã"}));const r=P(t);if(!r)return p("VIP:enterInvalidCode"),K(w({en:"Invalid code (wrong uid)",vi:"Mã không hợp lệ (sai uid)"}));const s=await c();if(r===s)return p("VIP:enterYourOwnCode"),K(w({en:"Cannot use your own code",vi:"Không thể dùng mã của chính bạn"}));const o="uidOnThisDevice",h=[];try{const n=(null==(e=localStorage.getItem(o))?void 0:e.split(","))||[];h.push(...n),n.includes(s)||(n.push(s),localStorage.setItem(o,n.join(",")))}catch(g){console.error(g)}if(h.includes(r))return p("VIP:enterSameDeviceAccountCode"),K(w({en:"Cannot use code from accounts on same device. Please use another code (from your friends)",vi:"Không thể dùng mã từ các tài khoản trên cùng 1 máy. Vui lòng nhập mã khác (từ bạn bè)"}));try{if(!(await d(r)))throw new Error}catch(g){return p("VIP:enterInvalidCodeAboutNotFound"),K(w({en:"Invalid code (cannot find code owner infor)",vi:"Mã không hợp lệ (không tìm được thông tin chủ nhân của mã)"}))}const u=await n(a.FB_AIO.server+"/referral",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({uid:s,referralCode:t})});console.log(u);const m=l(u);if(null==m?void 0:m.error)return K(w(m.error));p("VIP:enterCodeSuccess");const{isBEVIP:f}=await i((async()=>{const{isBEVIP:e}=await import("./useVIP-D5FAMGQQ.js");return{isBEVIP:e}}),__vite__mapDeps([3,1,2,4,5,6,7,8,9,10,11]),import.meta.url);f(!0);const v=(await i((async()=>{const{default:e}=await import("./MySwal-8JMBfvUJ.js");return{default:e}}),__vite__mapDeps([12,5,1,2]),import.meta.url)).default;await v.fire({icon:"success",title:w({en:"Code entered successfully",vi:"Nhập mã thành công"}),text:w({en:"3 days VIP has been added to your account",vi:"3 ngày VIP đã được thêm vào tài khoản của bạn"}),confirmButtonText:w({en:"Awesome!",vi:"Tuyêt vời!"})})},children:w({en:"Check ✨",vi:"Kiểm tra ✨"})})]})})]})}function I(e){const n=e.split("").map((e=>parseInt(e))),t=n.reduce(((e,n)=>e+n),0),r=Math.max(...n);return btoa(t*r+"")+"_"+e}function P(e){try{if(!e)return!1;const[n,t]=e.trim().split("_");return e===I(t)&&t}catch(n){return!1}}export{P as checkReferralCode,w as default,I as genReferralCodeFromUID};
