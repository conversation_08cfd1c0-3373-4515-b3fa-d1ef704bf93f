import{L as t,M as e,O as r,J as n,R as i,aw as o,r as a,m as l}from"./index-Cak6rALw.js";const d=t=>{const{componentCls:e}=t;return{[e]:{"&-horizontal":{[`&${e}`]:{"&-sm":{marginBlock:t.marginXS},"&-md":{marginBlock:t.margin}}}}}},s=t=>{const{componentCls:e,sizePaddingEdgeHorizontal:i,colorSplit:o,lineWidth:a,textPaddingInline:l,orientationMargin:d,verticalMarginInline:s}=t;return{[e]:Object.assign(Object.assign({},r(t)),{borderBlockStart:`${n(a)} solid ${o}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:s,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${n(a)} solid ${o}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${n(t.marginLG)} 0`},[`&-horizontal${e}-with-text`]:{display:"flex",alignItems:"center",margin:`${n(t.dividerHorizontalWithTextGutterMargin)} 0`,color:t.colorTextHeading,fontWeight:500,fontSize:t.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${o}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${n(a)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${e}-with-text-start`]:{"&::before":{width:`calc(${d} * 100%)`},"&::after":{width:`calc(100% - ${d} * 100%)`}},[`&-horizontal${e}-with-text-end`]:{"&::before":{width:`calc(100% - ${d} * 100%)`},"&::after":{width:`calc(${d} * 100%)`}},[`${e}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:l},"&-dashed":{background:"none",borderColor:o,borderStyle:"dashed",borderWidth:`${n(a)} 0 0`},[`&-horizontal${e}-with-text${e}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${e}-dashed`]:{borderInlineStartWidth:a,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:o,borderStyle:"dotted",borderWidth:`${n(a)} 0 0`},[`&-horizontal${e}-with-text${e}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${e}-dotted`]:{borderInlineStartWidth:a,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${e}-with-text`]:{color:t.colorText,fontWeight:"normal",fontSize:t.fontSize},[`&-horizontal${e}-with-text-start${e}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${e}-inner-text`]:{paddingInlineStart:i}},[`&-horizontal${e}-with-text-end${e}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${e}-inner-text`]:{paddingInlineEnd:i}}})}},c=t("Divider",(t=>{const r=e(t,{dividerHorizontalWithTextGutterMargin:t.margin,sizePaddingEdgeHorizontal:0});return[s(r),d(r)]}),(t=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:t.marginXS})),{unitless:{orientationMargin:!0}});const h={small:"sm",middle:"md"},g=t=>{const{getPrefixCls:e,direction:r,className:n,style:d}=i("divider"),{prefixCls:s,type:g="horizontal",orientation:m="center",orientationMargin:b,className:$,rootClassName:f,children:p,dashed:x,variant:w="solid",plain:y,style:S,size:u}=t,v=function(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(t);i<n.length;i++)e.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(t,n[i])&&(r[n[i]]=t[n[i]])}return r}(t,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),z=e("divider",s),[k,I,O]=c(z),B=o(u),E=h[B],M=!!p,j=a.useMemo((()=>"left"===m?"rtl"===r?"end":"start":"right"===m?"rtl"===r?"start":"end":m),[r,m]),C="start"===j&&null!=b,W="end"===j&&null!=b,N=l(z,n,I,O,`${z}-${g}`,{[`${z}-with-text`]:M,[`${z}-with-text-${j}`]:M,[`${z}-dashed`]:!!x,[`${z}-${w}`]:"solid"!==w,[`${z}-plain`]:!!y,[`${z}-rtl`]:"rtl"===r,[`${z}-no-default-orientation-margin-start`]:C,[`${z}-no-default-orientation-margin-end`]:W,[`${z}-${E}`]:!!E},$,f),P=a.useMemo((()=>"number"==typeof b?b:/^\d+$/.test(b)?Number(b):b),[b]),H={marginInlineStart:C?P:void 0,marginInlineEnd:W?P:void 0};return k(a.createElement("div",Object.assign({className:N,style:Object.assign(Object.assign({},d),S)},v,{role:"separator"}),p&&"vertical"!==g&&a.createElement("span",{className:`${z}-inner-text`,style:H},p)))};export{g as D};
