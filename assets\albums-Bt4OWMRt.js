import{aM as o}from"./index-Cak6rALw.js";import{aC as l,aD as n,aE as i,x as e,y as d}from"./MyApp-DW5WH4Ub.js";async function u({uid:o="",cursor:l=""}){var n,i,u,a,r,t,c,s,v,p;const m=[],_=await d({fb_api_req_friendly_name:"ProfileCometAppCollectionAlbumsRendererPaginationQuery",variables:{cursor:l,count:8,scale:2,id:btoa(`app_collection:${o}:2305272732:6`)},doc_id:"8672545689426653"}),f=e(_),{edges:b=[],page_info:g={}}=(null==(i=null==(n=null==f?void 0:f.data)?void 0:n.node)?void 0:i.pageItems)||{};for(const e of b){const o=atob(null==(u=null==e?void 0:e.node)?void 0:u.id).split(":").pop()||"";m.push({id:o,type:"album",name:null==(r=null==(a=null==e?void 0:e.node)?void 0:a.title)?void 0:r.text,count:parseInt(null==(c=null==(t=null==e?void 0:e.node)?void 0:t.subtitle_text)?void 0:c.text)||0,link:null==(s=null==e?void 0:e.node)?void 0:s.url,picture:null==(p=null==(v=null==e?void 0:e.node)?void 0:v.image)?void 0:p.uri,cursor:null==e?void 0:e.cursor,recent:m.length})}return{albums:m,nextCursor:null==g?void 0:g.end_cursor}}async function a({groupId:o="",cursor:l=""}){var n,i,u,a,r,t,c,s,v,p,m,_,f,b;const g=[],y=await d({fb_api_req_friendly_name:"GroupsCometMediaAlbumsTabGridQuery",variables:{cursor:l,count:8,scale:2,id:o},doc_id:"6894403247286675"}),h=e(y),{edges:x=[],page_info:I={}}=(null==(i=null==(n=null==h?void 0:h.data)?void 0:n.node)?void 0:i.group_albums)||{};for(const e of x){const o=null==(u=null==e?void 0:e.node)?void 0:u.id;g.push({id:o,type:"album",name:null==(r=null==(a=null==e?void 0:e.node)?void 0:a.title)?void 0:r.text,count:((null==(c=null==(t=null==e?void 0:e.node)?void 0:t.photos)?void 0:c.count)||0)+((null==(v=null==(s=null==e?void 0:e.node)?void 0:s.video)?void 0:v.count)||0),link:null==(p=null==e?void 0:e.node)?void 0:p.url,picture:null==(b=null==(f=null==(_=null==(m=null==e?void 0:e.node)?void 0:m.album_cover_focused_image)?void 0:_.image)?void 0:f.image)?void 0:b.uri,cursor:null==e?void 0:e.cursor,recent:g.length})}return{albums:g,nextCursor:null==I?void 0:I.end_cursor}}async function r(d=""){var u,a;const r=await l(n.FBAndroid),t=await o(`https://graph.facebook.com/v14.0/${d}?fields=albums{${["count","created_time","name","updated_time","type","link","picture{url}"].join(",")}}&access_token=${r}`);i(t);const c=e(t),s=null==(a=null==(u=null==c?void 0:c.albums)?void 0:u.data)?void 0:a.filter((o=>["wall","mobile","profile"].includes(o.type)));return s&&s.forEach((o=>{var l,n;o.picture=null==(n=null==(l=o.picture)?void 0:l.data)?void 0:n.url})),s}var t=(o=>(o.IMAGE="image",o.VIDEO="video",o))(t||{});async function c({albumId:o,fromId:l=""}){let n,i;n=await d({doc_id:"8142948395762884",variables:{id:o,cursor:l?btoa("fbid:"+l):"",count:14,renderLocation:"permalink",scale:2}}),i=e(n);try{return i.data.node.media.edges.map((o=>({id:o.node.id,uri:o.node.image.uri,type:"Video"===o.node.__typename?"video":"image"})))}catch(u){return console.error(u),[]}}export{t as MediaType,c as getAlbumPhoto,a as getGroupAlbum,r as getHiddenAlbums,u as getUserAlbum};
