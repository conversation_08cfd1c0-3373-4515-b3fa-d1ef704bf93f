const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./index-BD0KAi1n.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./copy-D4Fd8-zB.js"])))=>i.map(i=>d[i]);
import{r as t,b0 as e,b5 as a,b1 as o,aN as s}from"./index-Cak6rALw.js";import{u as i,C as n}from"./MyApp-DW5WH4Ub.js";const r=o((()=>s((()=>import("./index-BD0KAi1n.js")),__vite__mapDeps([0,1,2]),import.meta.url)),{fallback:n});function c({code:o,language:n}){const{ti:c}=i(),[l,u]=t.useState(!1),[p,d]=t.useState(!1),[m,y]=t.useState(100),f=t.useRef(null);t.useEffect((()=>{p&&setTimeout((()=>{d(!1)}),2e3)}),[p]),t.useEffect((()=>{f.current&&x()}),[o]);const x=()=>{var t;const e=null==(t=f.current)?void 0:t.getModel();if(!e)return;const a=e.getLineCount(),o=Math.min(19*a+20,500);y(o),u(a>50)};return e.jsxs("div",{style:{position:"relative",display:"flex",flexDirection:"column",alignItems:"flex-start",gap:8},children:[e.jsx(a,{icon:e.jsx("i",{className:"fa-solid "+(p?"fa-check":"fa-copy")}),onClick:()=>(async t=>{try{const{copy:e}=await s((async()=>{const{copy:t}=await import("./copy-D4Fd8-zB.js");return{copy:t}}),__vite__mapDeps([3,1,2]),import.meta.url);if(!(await e(t)))throw new Error("");d(!0)}catch(e){}})(o),disabled:p,style:{position:"absolute",right:0,top:0,zIndex:1e3,opacity:.8},children:c(p?{en:"Copied!",vi:"Đã Copy!"}:{en:"Copy",vi:"Copy"})}),e.jsx(r,{height:m,language:n,value:o,theme:"vs-dark",options:{minimap:{enabled:l},readOnly:!0,automaticLayout:!0,stickyScroll:{enabled:!1},scrollBeyondLastLine:!1},onMount:t=>{f.current=t,x()}})]})}export{c as default};
