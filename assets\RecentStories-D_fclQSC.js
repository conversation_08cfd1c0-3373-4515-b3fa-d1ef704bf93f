const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./RecentStoryViewer-Bikh4z-C.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./stories-DQ6hTlzb.js","./MyApp-DW5WH4Ub.js","./useCacheState-CAnxkewm.js","./index-CDSnY0KE.js","./EyeOutlined-CNKPohhw.js","./addEventListener-C4tIKh7N.js","./index-DdM4T8-Q.js","./useBreakpoint-CPcdMRfj.js","./index-PbLYNhdQ.js"])))=>i.map(i=>d[i]);
import{bc as e,r as o,b0 as s,b5 as t,b1 as i,aN as r,bk as n}from"./index-Cak6rALw.js";import{u as a,t as l,s as m,T as d,A as c,w as p,B as j,S as x,c as h,aR as u,I as b}from"./MyApp-DW5WH4Ub.js";import f from"./Collection-BYcChfHL.js";import{getRecentStories as w,getStoriesBucketsData as y}from"./stories-DQ6hTlzb.js";import{B as g}from"./BadgeWrapper-BCbYXXfB.js";import{L as k}from"./index-jrOnBmdW.js";import{I as v}from"./index-CDSnY0KE.js";import{P as F}from"./index-PbLYNhdQ.js";import{A as R}from"./index-DdM4T8-Q.js";import"./index-ByRdMNW-.js";import"./useCacheState-CAnxkewm.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CC5caqt_.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./addEventListener-C4tIKh7N.js";const N=i((()=>r((()=>import("./RecentStoryViewer-Bikh4z-C.js").then((e=>e.R))),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11]),import.meta.url)),{fallback:b});function C(){const{ti:i}=a(),r=e();o.useEffect((()=>{l("RecentStories:onLoad")}),[]);return s.jsx(f,{collectionName:"Recent Stories "+p(),fetchNext:async(e,o)=>{var s;return await w(o||(null==(s=null==e?void 0:e[e.length-1])?void 0:s.cursor)||"")},renderItem:(e,o)=>s.jsx(k.Item,{children:s.jsxs(j.Ribbon,{text:e.stories.length,style:{opacity:e.stories.length>1?1:0},children:[s.jsx(v,{src:e.thumbnail,style:{width:160,height:270,borderRadius:10,objectFit:"cover"},preview:{destroyOnClose:!0,toolbarRender:()=>null,imageRender:()=>s.jsx(N,{story:e})}}),s.jsx("div",{style:{position:"absolute",top:10,left:10},children:s.jsxs(F,{title:e.seen?s.jsxs(s.Fragment,{children:[s.jsx("i",{className:"fa-solid fa-eye"})," ",e.owner.name," ",i({en:" KNOW you saw",vi:" BIẾT bạn đã xem"})]}):e.owner.name,content:s.jsxs(x.Compact,{children:[s.jsx(d,{title:i({en:"Bulk downloader",vi:"Tải hàng loạt"}),placement:"bottom",children:s.jsx(t,{icon:s.jsx("i",{className:"fa-solid fa-download"}),onClick:()=>{return o=e.owner.id,void r("/bulk-downloader",{state:{targetId:o,platform:n.Facebook}});var o}})}),s.jsx(d,{title:i({en:"Open Facebook",vi:"Mở Facebook"}),placement:"bottom",children:s.jsx(t,{icon:s.jsx("i",{className:"fa-solid fa-external-link"}),onClick:()=>{window.open("https://fb.com/"+e.owner.id)}})})]}),children:[s.jsx(R,{src:e.owner.avatar,size:40,style:{borderWidth:4,borderColor:"#0866FF"}}),e.seen?s.jsx("i",{className:"fa-solid fa-eye fa-lg",style:{position:"absolute",bottom:0,right:0}}):null]})}),s.jsx("div",{style:{background:"linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%)",position:"absolute",bottom:0,left:0,padding:"5px 10px",width:"100%",paddingTop:"50px",pointerEvents:"none"},children:s.jsx(h.Text,{style:{color:"#eee"},children:e.owner.name})})]})}),downloadItem:async e=>(await y(e.bucket_id)).map(((o,s)=>({name:u(e.owner.name)+(o.video?".mp4":".jpg"),url:o.video||o.image||o.thumbnail}))),getItemCursor:e=>null==e?void 0:e.cursor,rowKey:e=>e.bucket_id,all:!0,onSearch:(e,o)=>m(e,o.owner.name),header:()=>s.jsx(c,{type:"info",message:s.jsxs(s.Fragment,{children:[s.jsx("i",{className:"fa fa-eye-slash",style:{color:"gray"}})," ",i({en:"View stories anonymously - Friends won't know you've viewed their stories",vi:"Xem tin ẩn danh - Bạn bè sẽ không biết bạn đã xem tin của họ"})]})}),headerButtons:()=>s.jsx(s.Fragment,{children:s.jsx(g,{type:"new",children:s.jsx(d,{title:s.jsxs(s.Fragment,{children:[s.jsx("i",{className:"fa fa-history"})," ",i({en:"Undo your reactions on Facebook story",vi:"Thu hồi cảm xúc đã thả trên Facebook story"})]}),children:s.jsx(t,{icon:s.jsx("i",{className:"fa-solid fa-up-right-from-square"}),onClick:()=>{l("RecentStories:clickHistory"),window.open("https://www.facebook.com/me/allactivity/?category_key=STORIESFEEDBACK","_blank")},children:i({en:"Remove reactions",vi:"Thu hồi cảm xúc"})})})})})})}export{C as default};
