import{r as t,G as e,bu as r,m as n}from"./index-Cak6rALw.js";import{r as o,u as s}from"./useBreakpoint-CPcdMRfj.js";const i=t.createContext({});function a(e,r){const[n,s]=t.useState("string"==typeof e?e:"");return t.useEffect((()=>{(()=>{if("string"==typeof e&&s(e),"object"==typeof e)for(let t=0;t<o.length;t++){const n=o[t];if(!r||!r[n])continue;const i=e[n];if(void 0!==i)return void s(i)}})()}),[JSON.stringify(e),r]),n}const l=t.forwardRef(((l,c)=>{const{prefixCls:f,justify:u,align:p,className:g,style:y,children:m,gutter:d=0,wrap:b}=l,j=function(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(t);o<n.length;o++)e.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(r[n[o]]=t[n[o]])}return r}(l,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:v,direction:x}=t.useContext(e),O=s(!0,null),w=a(p,O),h=a(u,O),$=v("row",f),[C,E,P]=r($),N=function(t,e){const r=[void 0,void 0],n=Array.isArray(t)?t:[t,void 0],s=e||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return n.forEach(((t,e)=>{if("object"==typeof t&&null!==t)for(let n=0;n<o.length;n++){const i=o[n];if(s[i]&&void 0!==t[i]){r[e]=t[i];break}}else r[e]=t})),r}(d,O),S=n($,{[`${$}-no-wrap`]:!1===b,[`${$}-${h}`]:h,[`${$}-${w}`]:w,[`${$}-rtl`]:"rtl"===x},g,E,P),R={},k=null!=N[0]&&N[0]>0?N[0]/-2:void 0;k&&(R.marginLeft=k,R.marginRight=k);const[A,G]=N;R.rowGap=G;const B=t.useMemo((()=>({gutter:[A,G],wrap:b})),[A,G,b]);return C(t.createElement(i.Provider,{value:B},t.createElement("div",Object.assign({},j,{className:S,style:Object.assign(Object.assign({},R),y),ref:c}),m)))}));export{l as R,i as a};
