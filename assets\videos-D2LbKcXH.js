import{b$ as t}from"./index-Cak6rALw.js";import{y as e,x as n,X as r}from"./MyApp-DW5WH4Ub.js";const i=Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"}));var o={};const s=t(i);var a,u={},c={exports:{}},l={},f={};var h,p,d,g,y,m,E,b,T,w,A,v,_,N,I,x,F,C,B,O={};
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */function D(){return p||(p=1,function(t){const e=function(){if(a)return f;a=1,f.byteLength=function(t){var e=o(t),n=e[0],r=e[1];return 3*(n+r)/4-r},f.toByteArray=function(t){var r,i,s=o(t),a=s[0],u=s[1],c=new n(function(t,e,n){return 3*(e+n)/4-n}(0,a,u)),l=0,f=u>0?a-4:a;for(i=0;i<f;i+=4)r=e[t.charCodeAt(i)]<<18|e[t.charCodeAt(i+1)]<<12|e[t.charCodeAt(i+2)]<<6|e[t.charCodeAt(i+3)],c[l++]=r>>16&255,c[l++]=r>>8&255,c[l++]=255&r;return 2===u&&(r=e[t.charCodeAt(i)]<<2|e[t.charCodeAt(i+1)]>>4,c[l++]=255&r),1===u&&(r=e[t.charCodeAt(i)]<<10|e[t.charCodeAt(i+1)]<<4|e[t.charCodeAt(i+2)]>>2,c[l++]=r>>8&255,c[l++]=255&r),c},f.fromByteArray=function(e){for(var n,r=e.length,i=r%3,o=[],a=16383,u=0,c=r-i;u<c;u+=a)o.push(s(e,u,u+a>c?c:u+a));return 1===i?(n=e[r-1],o.push(t[n>>2]+t[n<<4&63]+"==")):2===i&&(n=(e[r-2]<<8)+e[r-1],o.push(t[n>>10]+t[n>>4&63]+t[n<<2&63]+"=")),o.join("")};for(var t=[],e=[],n="undefined"!=typeof Uint8Array?Uint8Array:Array,r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",i=0;i<64;++i)t[i]=r[i],e[r.charCodeAt(i)]=i;function o(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");return-1===n&&(n=e),[n,n===e?0:4-n%4]}function s(e,n,r){for(var i,o,s=[],a=n;a<r;a+=3)i=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),s.push(t[(o=i)>>18&63]+t[o>>12&63]+t[o>>6&63]+t[63&o]);return s.join("")}return e["-".charCodeAt(0)]=62,e["_".charCodeAt(0)]=63,f}(),n=(h||(h=1,O.read=function(t,e,n,r,i){var o,s,a=8*i-r-1,u=(1<<a)-1,c=u>>1,l=-7,f=n?i-1:0,h=n?-1:1,p=t[e+f];for(f+=h,o=p&(1<<-l)-1,p>>=-l,l+=a;l>0;o=256*o+t[e+f],f+=h,l-=8);for(s=o&(1<<-l)-1,o>>=-l,l+=r;l>0;s=256*s+t[e+f],f+=h,l-=8);if(0===o)o=1-c;else{if(o===u)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,r),o-=c}return(p?-1:1)*s*Math.pow(2,o-r)},O.write=function(t,e,n,r,i,o){var s,a,u,c=8*o-i-1,l=(1<<c)-1,f=l>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=r?0:o-1,d=r?1:-1,g=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(a=isNaN(e)?1:0,s=l):(s=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-s))<1&&(s--,u*=2),(e+=s+f>=1?h/u:h*Math.pow(2,1-f))*u>=2&&(s++,u/=2),s+f>=l?(a=0,s=l):s+f>=1?(a=(e*u-1)*Math.pow(2,i),s+=f):(a=e*Math.pow(2,f-1)*Math.pow(2,i),s=0));i>=8;t[n+p]=255&a,p+=d,a/=256,i-=8);for(s=s<<i|a,c+=i;c>0;t[n+p]=255&s,p+=d,s/=256,c-=8);t[n+p-d]|=128*g}),O),r="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;t.Buffer=s,t.SlowBuffer=function(t){+t!=t&&(t=0);return s.alloc(+t)},t.INSPECT_MAX_BYTES=50;const i=2147483647;function o(t){if(t>i)throw new RangeError('The value "'+t+'" is invalid for option "size"');const e=new Uint8Array(t);return Object.setPrototypeOf(e,s.prototype),e}function s(t,e,n){if("number"==typeof t){if("string"==typeof e)throw new TypeError('The "string" argument must be of type string. Received type number');return l(t)}return u(t,e,n)}function u(t,e,n){if("string"==typeof t)return function(t,e){"string"==typeof e&&""!==e||(e="utf8");if(!s.isEncoding(e))throw new TypeError("Unknown encoding: "+e);const n=0|y(t,e);let r=o(n);const i=r.write(t,e);i!==n&&(r=r.slice(0,i));return r}(t,e);if(ArrayBuffer.isView(t))return function(t){if(z(t,Uint8Array)){const e=new Uint8Array(t);return d(e.buffer,e.byteOffset,e.byteLength)}return p(t)}(t);if(null==t)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(z(t,ArrayBuffer)||t&&z(t.buffer,ArrayBuffer))return d(t,e,n);if("undefined"!=typeof SharedArrayBuffer&&(z(t,SharedArrayBuffer)||t&&z(t.buffer,SharedArrayBuffer)))return d(t,e,n);if("number"==typeof t)throw new TypeError('The "value" argument must not be of type number. Received type number');const r=t.valueOf&&t.valueOf();if(null!=r&&r!==t)return s.from(r,e,n);const i=function(t){if(s.isBuffer(t)){const e=0|g(t.length),n=o(e);return 0===n.length||t.copy(n,0,0,e),n}if(void 0!==t.length)return"number"!=typeof t.length||J(t.length)?o(0):p(t);if("Buffer"===t.type&&Array.isArray(t.data))return p(t.data)}(t);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return s.from(t[Symbol.toPrimitive]("string"),e,n);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function c(t){if("number"!=typeof t)throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function l(t){return c(t),o(t<0?0:0|g(t))}function p(t){const e=t.length<0?0:0|g(t.length),n=o(e);for(let r=0;r<e;r+=1)n[r]=255&t[r];return n}function d(t,e,n){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(n||0))throw new RangeError('"length" is outside of buffer bounds');let r;return r=void 0===e&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,e):new Uint8Array(t,e,n),Object.setPrototypeOf(r,s.prototype),r}function g(t){if(t>=i)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i.toString(16)+" bytes");return 0|t}function y(t,e){if(s.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||z(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);const n=t.length,r=arguments.length>2&&!0===arguments[2];if(!r&&0===n)return 0;let i=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return Q(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return $(t).length;default:if(i)return r?-1:Q(t).length;e=(""+e).toLowerCase(),i=!0}}function m(t,e,n){let r=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return D(this,e,n);case"utf8":case"utf-8":return x(this,e,n);case"ascii":return C(this,e,n);case"latin1":case"binary":return B(this,e,n);case"base64":return I(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,e,n);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function E(t,e,n){const r=t[e];t[e]=t[n],t[n]=r}function b(t,e,n,r,i){if(0===t.length)return-1;if("string"==typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),J(n=+n)&&(n=i?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(i)return-1;n=t.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof e&&(e=s.from(e,r)),s.isBuffer(e))return 0===e.length?-1:T(t,e,n,r,i);if("number"==typeof e)return e&=255,"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):T(t,[e],n,r,i);throw new TypeError("val must be string, number or Buffer")}function T(t,e,n,r,i){let o,s=1,a=t.length,u=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return-1;s=2,a/=2,u/=2,n/=2}function c(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){let r=-1;for(o=n;o<a;o++)if(c(t,o)===c(e,-1===r?0:o-r)){if(-1===r&&(r=o),o-r+1===u)return r*s}else-1!==r&&(o-=o-r),r=-1}else for(n+u>a&&(n=a-u),o=n;o>=0;o--){let n=!0;for(let r=0;r<u;r++)if(c(t,o+r)!==c(e,r)){n=!1;break}if(n)return o}return-1}function w(t,e,n,r){n=Number(n)||0;const i=t.length-n;r?(r=Number(r))>i&&(r=i):r=i;const o=e.length;let s;for(r>o/2&&(r=o/2),s=0;s<r;++s){const r=parseInt(e.substr(2*s,2),16);if(J(r))return s;t[n+s]=r}return s}function A(t,e,n,r){return W(Q(e,t.length-n),t,n,r)}function v(t,e,n,r){return W(function(t){const e=[];for(let n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}(e),t,n,r)}function _(t,e,n,r){return W($(e),t,n,r)}function N(t,e,n,r){return W(function(t,e){let n,r,i;const o=[];for(let s=0;s<t.length&&!((e-=2)<0);++s)n=t.charCodeAt(s),r=n>>8,i=n%256,o.push(i),o.push(r);return o}(e,t.length-n),t,n,r)}function I(t,n,r){return 0===n&&r===t.length?e.fromByteArray(t):e.fromByteArray(t.slice(n,r))}function x(t,e,n){n=Math.min(t.length,n);const r=[];let i=e;for(;i<n;){const e=t[i];let o=null,s=e>239?4:e>223?3:e>191?2:1;if(i+s<=n){let n,r,a,u;switch(s){case 1:e<128&&(o=e);break;case 2:n=t[i+1],128==(192&n)&&(u=(31&e)<<6|63&n,u>127&&(o=u));break;case 3:n=t[i+1],r=t[i+2],128==(192&n)&&128==(192&r)&&(u=(15&e)<<12|(63&n)<<6|63&r,u>2047&&(u<55296||u>57343)&&(o=u));break;case 4:n=t[i+1],r=t[i+2],a=t[i+3],128==(192&n)&&128==(192&r)&&128==(192&a)&&(u=(15&e)<<18|(63&n)<<12|(63&r)<<6|63&a,u>65535&&u<1114112&&(o=u))}}null===o?(o=65533,s=1):o>65535&&(o-=65536,r.push(o>>>10&1023|55296),o=56320|1023&o),r.push(o),i+=s}return function(t){const e=t.length;if(e<=F)return String.fromCharCode.apply(String,t);let n="",r=0;for(;r<e;)n+=String.fromCharCode.apply(String,t.slice(r,r+=F));return n}(r)}t.kMaxLength=i,s.TYPED_ARRAY_SUPPORT=function(){try{const t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),s.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(s.prototype,"parent",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.buffer}}),Object.defineProperty(s.prototype,"offset",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.byteOffset}}),s.poolSize=8192,s.from=function(t,e,n){return u(t,e,n)},Object.setPrototypeOf(s.prototype,Uint8Array.prototype),Object.setPrototypeOf(s,Uint8Array),s.alloc=function(t,e,n){return function(t,e,n){return c(t),t<=0?o(t):void 0!==e?"string"==typeof n?o(t).fill(e,n):o(t).fill(e):o(t)}(t,e,n)},s.allocUnsafe=function(t){return l(t)},s.allocUnsafeSlow=function(t){return l(t)},s.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==s.prototype},s.compare=function(t,e){if(z(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),z(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),!s.isBuffer(t)||!s.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;let n=t.length,r=e.length;for(let i=0,o=Math.min(n,r);i<o;++i)if(t[i]!==e[i]){n=t[i],r=e[i];break}return n<r?-1:r<n?1:0},s.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return s.alloc(0);let n;if(void 0===e)for(e=0,n=0;n<t.length;++n)e+=t[n].length;const r=s.allocUnsafe(e);let i=0;for(n=0;n<t.length;++n){let e=t[n];if(z(e,Uint8Array))i+e.length>r.length?(s.isBuffer(e)||(e=s.from(e)),e.copy(r,i)):Uint8Array.prototype.set.call(r,e,i);else{if(!s.isBuffer(e))throw new TypeError('"list" argument must be an Array of Buffers');e.copy(r,i)}i+=e.length}return r},s.byteLength=y,s.prototype._isBuffer=!0,s.prototype.swap16=function(){const t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let e=0;e<t;e+=2)E(this,e,e+1);return this},s.prototype.swap32=function(){const t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let e=0;e<t;e+=4)E(this,e,e+3),E(this,e+1,e+2);return this},s.prototype.swap64=function(){const t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let e=0;e<t;e+=8)E(this,e,e+7),E(this,e+1,e+6),E(this,e+2,e+5),E(this,e+3,e+4);return this},s.prototype.toString=function(){const t=this.length;return 0===t?"":0===arguments.length?x(this,0,t):m.apply(this,arguments)},s.prototype.toLocaleString=s.prototype.toString,s.prototype.equals=function(t){if(!s.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===s.compare(this,t)},s.prototype.inspect=function(){let e="";const n=t.INSPECT_MAX_BYTES;return e=this.toString("hex",0,n).replace(/(.{2})/g,"$1 ").trim(),this.length>n&&(e+=" ... "),"<Buffer "+e+">"},r&&(s.prototype[r]=s.prototype.inspect),s.prototype.compare=function(t,e,n,r,i){if(z(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),!s.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),e<0||n>t.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&e>=n)return 0;if(r>=i)return-1;if(e>=n)return 1;if(this===t)return 0;let o=(i>>>=0)-(r>>>=0),a=(n>>>=0)-(e>>>=0);const u=Math.min(o,a),c=this.slice(r,i),l=t.slice(e,n);for(let s=0;s<u;++s)if(c[s]!==l[s]){o=c[s],a=l[s];break}return o<a?-1:a<o?1:0},s.prototype.includes=function(t,e,n){return-1!==this.indexOf(t,e,n)},s.prototype.indexOf=function(t,e,n){return b(this,t,e,n,!0)},s.prototype.lastIndexOf=function(t,e,n){return b(this,t,e,n,!1)},s.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"==typeof e)r=e,n=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e>>>=0,isFinite(n)?(n>>>=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}const i=this.length-e;if((void 0===n||n>i)&&(n=i),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");let o=!1;for(;;)switch(r){case"hex":return w(this,t,e,n);case"utf8":case"utf-8":return A(this,t,e,n);case"ascii":case"latin1":case"binary":return v(this,t,e,n);case"base64":return _(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return N(this,t,e,n);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};const F=4096;function C(t,e,n){let r="";n=Math.min(t.length,n);for(let i=e;i<n;++i)r+=String.fromCharCode(127&t[i]);return r}function B(t,e,n){let r="";n=Math.min(t.length,n);for(let i=e;i<n;++i)r+=String.fromCharCode(t[i]);return r}function D(t,e,n){const r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);let i="";for(let o=e;o<n;++o)i+=Z[t[o]];return i}function S(t,e,n){const r=t.slice(e,n);let i="";for(let o=0;o<r.length-1;o+=2)i+=String.fromCharCode(r[o]+256*r[o+1]);return i}function U(t,e,n){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}function R(t,e,n,r,i,o){if(!s.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}function L(t,e,n,r,i){Y(e,r,i,t,n,7);let o=Number(e&BigInt(4294967295));t[n++]=o,o>>=8,t[n++]=o,o>>=8,t[n++]=o,o>>=8,t[n++]=o;let s=Number(e>>BigInt(32)&BigInt(4294967295));return t[n++]=s,s>>=8,t[n++]=s,s>>=8,t[n++]=s,s>>=8,t[n++]=s,n}function P(t,e,n,r,i){Y(e,r,i,t,n,7);let o=Number(e&BigInt(4294967295));t[n+7]=o,o>>=8,t[n+6]=o,o>>=8,t[n+5]=o,o>>=8,t[n+4]=o;let s=Number(e>>BigInt(32)&BigInt(4294967295));return t[n+3]=s,s>>=8,t[n+2]=s,s>>=8,t[n+1]=s,s>>=8,t[n]=s,n+8}function K(t,e,n,r,i,o){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function k(t,e,r,i,o){return e=+e,r>>>=0,o||K(t,0,r,4),n.write(t,e,r,i,23,4),r+4}function M(t,e,r,i,o){return e=+e,r>>>=0,o||K(t,0,r,8),n.write(t,e,r,i,52,8),r+8}s.prototype.slice=function(t,e){const n=this.length;(t=~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),(e=void 0===e?n:~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),e<t&&(e=t);const r=this.subarray(t,e);return Object.setPrototypeOf(r,s.prototype),r},s.prototype.readUintLE=s.prototype.readUIntLE=function(t,e,n){t>>>=0,e>>>=0,n||U(t,e,this.length);let r=this[t],i=1,o=0;for(;++o<e&&(i*=256);)r+=this[t+o]*i;return r},s.prototype.readUintBE=s.prototype.readUIntBE=function(t,e,n){t>>>=0,e>>>=0,n||U(t,e,this.length);let r=this[t+--e],i=1;for(;e>0&&(i*=256);)r+=this[t+--e]*i;return r},s.prototype.readUint8=s.prototype.readUInt8=function(t,e){return t>>>=0,e||U(t,1,this.length),this[t]},s.prototype.readUint16LE=s.prototype.readUInt16LE=function(t,e){return t>>>=0,e||U(t,2,this.length),this[t]|this[t+1]<<8},s.prototype.readUint16BE=s.prototype.readUInt16BE=function(t,e){return t>>>=0,e||U(t,2,this.length),this[t]<<8|this[t+1]},s.prototype.readUint32LE=s.prototype.readUInt32LE=function(t,e){return t>>>=0,e||U(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},s.prototype.readUint32BE=s.prototype.readUInt32BE=function(t,e){return t>>>=0,e||U(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},s.prototype.readBigUInt64LE=tt((function(t){q(t>>>=0,"offset");const e=this[t],n=this[t+7];void 0!==e&&void 0!==n||X(t,this.length-8);const r=e+256*this[++t]+65536*this[++t]+this[++t]*2**24,i=this[++t]+256*this[++t]+65536*this[++t]+n*2**24;return BigInt(r)+(BigInt(i)<<BigInt(32))})),s.prototype.readBigUInt64BE=tt((function(t){q(t>>>=0,"offset");const e=this[t],n=this[t+7];void 0!==e&&void 0!==n||X(t,this.length-8);const r=e*2**24+65536*this[++t]+256*this[++t]+this[++t],i=this[++t]*2**24+65536*this[++t]+256*this[++t]+n;return(BigInt(r)<<BigInt(32))+BigInt(i)})),s.prototype.readIntLE=function(t,e,n){t>>>=0,e>>>=0,n||U(t,e,this.length);let r=this[t],i=1,o=0;for(;++o<e&&(i*=256);)r+=this[t+o]*i;return i*=128,r>=i&&(r-=Math.pow(2,8*e)),r},s.prototype.readIntBE=function(t,e,n){t>>>=0,e>>>=0,n||U(t,e,this.length);let r=e,i=1,o=this[t+--r];for(;r>0&&(i*=256);)o+=this[t+--r]*i;return i*=128,o>=i&&(o-=Math.pow(2,8*e)),o},s.prototype.readInt8=function(t,e){return t>>>=0,e||U(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},s.prototype.readInt16LE=function(t,e){t>>>=0,e||U(t,2,this.length);const n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},s.prototype.readInt16BE=function(t,e){t>>>=0,e||U(t,2,this.length);const n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},s.prototype.readInt32LE=function(t,e){return t>>>=0,e||U(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},s.prototype.readInt32BE=function(t,e){return t>>>=0,e||U(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},s.prototype.readBigInt64LE=tt((function(t){q(t>>>=0,"offset");const e=this[t],n=this[t+7];void 0!==e&&void 0!==n||X(t,this.length-8);const r=this[t+4]+256*this[t+5]+65536*this[t+6]+(n<<24);return(BigInt(r)<<BigInt(32))+BigInt(e+256*this[++t]+65536*this[++t]+this[++t]*2**24)})),s.prototype.readBigInt64BE=tt((function(t){q(t>>>=0,"offset");const e=this[t],n=this[t+7];void 0!==e&&void 0!==n||X(t,this.length-8);const r=(e<<24)+65536*this[++t]+256*this[++t]+this[++t];return(BigInt(r)<<BigInt(32))+BigInt(this[++t]*2**24+65536*this[++t]+256*this[++t]+n)})),s.prototype.readFloatLE=function(t,e){return t>>>=0,e||U(t,4,this.length),n.read(this,t,!0,23,4)},s.prototype.readFloatBE=function(t,e){return t>>>=0,e||U(t,4,this.length),n.read(this,t,!1,23,4)},s.prototype.readDoubleLE=function(t,e){return t>>>=0,e||U(t,8,this.length),n.read(this,t,!0,52,8)},s.prototype.readDoubleBE=function(t,e){return t>>>=0,e||U(t,8,this.length),n.read(this,t,!1,52,8)},s.prototype.writeUintLE=s.prototype.writeUIntLE=function(t,e,n,r){if(t=+t,e>>>=0,n>>>=0,!r){R(this,t,e,n,Math.pow(2,8*n)-1,0)}let i=1,o=0;for(this[e]=255&t;++o<n&&(i*=256);)this[e+o]=t/i&255;return e+n},s.prototype.writeUintBE=s.prototype.writeUIntBE=function(t,e,n,r){if(t=+t,e>>>=0,n>>>=0,!r){R(this,t,e,n,Math.pow(2,8*n)-1,0)}let i=n-1,o=1;for(this[e+i]=255&t;--i>=0&&(o*=256);)this[e+i]=t/o&255;return e+n},s.prototype.writeUint8=s.prototype.writeUInt8=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,1,255,0),this[e]=255&t,e+1},s.prototype.writeUint16LE=s.prototype.writeUInt16LE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},s.prototype.writeUint16BE=s.prototype.writeUInt16BE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},s.prototype.writeUint32LE=s.prototype.writeUInt32LE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},s.prototype.writeUint32BE=s.prototype.writeUInt32BE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},s.prototype.writeBigUInt64LE=tt((function(t,e=0){return L(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))})),s.prototype.writeBigUInt64BE=tt((function(t,e=0){return P(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))})),s.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e>>>=0,!r){const r=Math.pow(2,8*n-1);R(this,t,e,n,r-1,-r)}let i=0,o=1,s=0;for(this[e]=255&t;++i<n&&(o*=256);)t<0&&0===s&&0!==this[e+i-1]&&(s=1),this[e+i]=(t/o|0)-s&255;return e+n},s.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e>>>=0,!r){const r=Math.pow(2,8*n-1);R(this,t,e,n,r-1,-r)}let i=n-1,o=1,s=0;for(this[e+i]=255&t;--i>=0&&(o*=256);)t<0&&0===s&&0!==this[e+i+1]&&(s=1),this[e+i]=(t/o|0)-s&255;return e+n},s.prototype.writeInt8=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},s.prototype.writeInt16LE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},s.prototype.writeInt16BE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},s.prototype.writeInt32LE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,4,2147483647,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},s.prototype.writeInt32BE=function(t,e,n){return t=+t,e>>>=0,n||R(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},s.prototype.writeBigInt64LE=tt((function(t,e=0){return L(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),s.prototype.writeBigInt64BE=tt((function(t,e=0){return P(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),s.prototype.writeFloatLE=function(t,e,n){return k(this,t,e,!0,n)},s.prototype.writeFloatBE=function(t,e,n){return k(this,t,e,!1,n)},s.prototype.writeDoubleLE=function(t,e,n){return M(this,t,e,!0,n)},s.prototype.writeDoubleBE=function(t,e,n){return M(this,t,e,!1,n)},s.prototype.copy=function(t,e,n,r){if(!s.isBuffer(t))throw new TypeError("argument should be a Buffer");if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);const i=r-n;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,n,r):Uint8Array.prototype.set.call(t,this.subarray(n,r),e),i},s.prototype.fill=function(t,e,n,r){if("string"==typeof t){if("string"==typeof e?(r=e,e=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!s.isEncoding(r))throw new TypeError("Unknown encoding: "+r);if(1===t.length){const e=t.charCodeAt(0);("utf8"===r&&e<128||"latin1"===r)&&(t=e)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;let i;if(e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"==typeof t)for(i=e;i<n;++i)this[i]=t;else{const o=s.isBuffer(t)?t:s.from(t,r),a=o.length;if(0===a)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<n-e;++i)this[i+e]=o[i%a]}return this};const V={};function j(t,e,n){V[t]=class extends n{constructor(){super(),Object.defineProperty(this,"message",{value:e.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${t}]`,this.stack,delete this.name}get code(){return t}set code(t){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:t,writable:!0})}toString(){return`${this.name} [${t}]: ${this.message}`}}}function G(t){let e="",n=t.length;const r="-"===t[0]?1:0;for(;n>=r+4;n-=3)e=`_${t.slice(n-3,n)}${e}`;return`${t.slice(0,n)}${e}`}function Y(t,e,n,r,i,o){if(t>n||t<e){const n="bigint"==typeof e?"n":"";let r;throw r=0===e||e===BigInt(0)?`>= 0${n} and < 2${n} ** ${8*(o+1)}${n}`:`>= -(2${n} ** ${8*(o+1)-1}${n}) and < 2 ** ${8*(o+1)-1}${n}`,new V.ERR_OUT_OF_RANGE("value",r,t)}!function(t,e,n){q(e,"offset"),void 0!==t[e]&&void 0!==t[e+n]||X(e,t.length-(n+1))}(r,i,o)}function q(t,e){if("number"!=typeof t)throw new V.ERR_INVALID_ARG_TYPE(e,"number",t)}function X(t,e,n){if(Math.floor(t)!==t)throw q(t,n),new V.ERR_OUT_OF_RANGE("offset","an integer",t);if(e<0)throw new V.ERR_BUFFER_OUT_OF_BOUNDS;throw new V.ERR_OUT_OF_RANGE("offset",`>= 0 and <= ${e}`,t)}j("ERR_BUFFER_OUT_OF_BOUNDS",(function(t){return t?`${t} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"}),RangeError),j("ERR_INVALID_ARG_TYPE",(function(t,e){return`The "${t}" argument must be of type number. Received type ${typeof e}`}),TypeError),j("ERR_OUT_OF_RANGE",(function(t,e,n){let r=`The value of "${t}" is out of range.`,i=n;return Number.isInteger(n)&&Math.abs(n)>2**32?i=G(String(n)):"bigint"==typeof n&&(i=String(n),(n>BigInt(2)**BigInt(32)||n<-(BigInt(2)**BigInt(32)))&&(i=G(i)),i+="n"),r+=` It must be ${e}. Received ${i}`,r}),RangeError);const H=/[^+/0-9A-Za-z-_]/g;function Q(t,e){let n;e=e||1/0;const r=t.length;let i=null;const o=[];for(let s=0;s<r;++s){if(n=t.charCodeAt(s),n>55295&&n<57344){if(!i){if(n>56319){(e-=3)>-1&&o.push(239,191,189);continue}if(s+1===r){(e-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(e-=3)>-1&&o.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((e-=1)<0)break;o.push(n)}else if(n<2048){if((e-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function $(t){return e.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(H,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function W(t,e,n,r){let i;for(i=0;i<r&&!(i+n>=e.length||i>=t.length);++i)e[i+n]=t[i];return i}function z(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}function J(t){return t!=t}const Z=function(){const t="0123456789abcdef",e=new Array(256);for(let n=0;n<16;++n){const r=16*n;for(let i=0;i<16;++i)e[r+i]=t[n]+t[i]}return e}();function tt(t){return"undefined"==typeof BigInt?et:t}function et(){throw new Error("BigInt not supported")}}(l)),l}function S(){if(g)return u;g=1;var t=(d||(d=1,function(t,e){var n=D(),r=n.Buffer;function i(t,e){for(var n in t)e[n]=t[n]}function o(t,e,n){return r(t,e,n)}r.from&&r.alloc&&r.allocUnsafe&&r.allocUnsafeSlow?t.exports=n:(i(n,e),e.Buffer=o),i(r,o),o.from=function(t,e,n){if("number"==typeof t)throw new TypeError("Argument must not be a number");return r(t,e,n)},o.alloc=function(t,e,n){if("number"!=typeof t)throw new TypeError("Argument must be a number");var i=r(t);return void 0!==e?"string"==typeof n?i.fill(e,n):i.fill(e):i.fill(0),i},o.allocUnsafe=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return r(t)},o.allocUnsafeSlow=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return n.SlowBuffer(t)}}(c,c.exports)),c.exports).Buffer,e=t.isEncoding||function(t){switch((t=""+t)&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function n(n){var r;switch(this.encoding=function(n){var r=function(t){if(!t)return"utf8";for(var e;;)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(e)return;t=(""+t).toLowerCase(),e=!0}}(n);if("string"!=typeof r&&(t.isEncoding===e||!e(n)))throw new Error("Unknown encoding: "+n);return r||n}(n),this.encoding){case"utf16le":this.text=o,this.end=s,r=4;break;case"utf8":this.fillLast=i,r=4;break;case"base64":this.text=a,this.end=l,r=3;break;default:return this.write=f,void(this.end=h)}this.lastNeed=0,this.lastTotal=0,this.lastChar=t.allocUnsafe(r)}function r(t){return t<=127?0:t>>5==6?2:t>>4==14?3:t>>3==30?4:t>>6==2?-1:-2}function i(t){var e=this.lastTotal-this.lastNeed,n=function(t,e){if(128!=(192&e[0]))return t.lastNeed=0,"�";if(t.lastNeed>1&&e.length>1){if(128!=(192&e[1]))return t.lastNeed=1,"�";if(t.lastNeed>2&&e.length>2&&128!=(192&e[2]))return t.lastNeed=2,"�"}}(this,t);return void 0!==n?n:this.lastNeed<=t.length?(t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(t.copy(this.lastChar,e,0,t.length),void(this.lastNeed-=t.length))}function o(t,e){if((t.length-e)%2==0){var n=t.toString("utf16le",e);if(n){var r=n.charCodeAt(n.length-1);if(r>=55296&&r<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],n.slice(0,-1)}return n}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1)}function s(t){var e=t&&t.length?this.write(t):"";if(this.lastNeed){var n=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,n)}return e}function a(t,e){var n=(t.length-e)%3;return 0===n?t.toString("base64",e):(this.lastNeed=3-n,this.lastTotal=3,1===n?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-n))}function l(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function f(t){return t.toString(this.encoding)}function h(t){return t&&t.length?this.write(t):""}return u.StringDecoder=n,n.prototype.write=function(t){if(0===t.length)return"";var e,n;if(this.lastNeed){if(void 0===(e=this.fillLast(t)))return"";n=this.lastNeed,this.lastNeed=0}else n=0;return n<t.length?e?e+this.text(t,n):this.text(t,n):e||""},n.prototype.end=function(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+"�":e},n.prototype.text=function(t,e){var n=function(t,e,n){var i=e.length-1;if(i<n)return 0;var o=r(e[i]);if(o>=0)return o>0&&(t.lastNeed=o-1),o;if(--i<n||-2===o)return 0;if(o=r(e[i]),o>=0)return o>0&&(t.lastNeed=o-2),o;if(--i<n||-2===o)return 0;if(o=r(e[i]),o>=0)return o>0&&(2===o?o=0:t.lastNeed=o-3),o;return 0}(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=n;var i=t.length-(n-this.lastNeed);return t.copy(this.lastChar,0,i),t.toString("utf8",e,i)},n.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length},u}function U(){return y||(y=1,function(t){t.parser=function(t,e){return new r(t,e)},t.SAXParser=r,t.SAXStream=o,t.createStream=function(t,e){return new o(t,e)},t.MAX_BUFFER_LENGTH=65536;var e,n=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];function r(e,i){if(!(this instanceof r))return new r(e,i);var o=this;!function(t){for(var e=0,r=n.length;e<r;e++)t[n[e]]=""}(o),o.q=o.c="",o.bufferCheckPosition=t.MAX_BUFFER_LENGTH,o.opt=i||{},o.opt.lowercase=o.opt.lowercase||o.opt.lowercasetags,o.looseCase=o.opt.lowercase?"toLowerCase":"toUpperCase",o.tags=[],o.closed=o.closedRoot=o.sawRoot=!1,o.tag=o.error=null,o.strict=!!e,o.noscript=!(!e&&!o.opt.noscript),o.state=_.BEGIN,o.strictEntities=o.opt.strictEntities,o.ENTITIES=o.strictEntities?Object.create(t.XML_ENTITIES):Object.create(t.ENTITIES),o.attribList=[],o.opt.xmlns&&(o.ns=Object.create(f)),void 0===o.opt.unquotedAttributeValues&&(o.opt.unquotedAttributeValues=!e),o.trackPosition=!1!==o.opt.position,o.trackPosition&&(o.position=o.line=o.column=0),I(o,"onready")}t.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","opentagstart","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"],Object.create||(Object.create=function(t){function e(){}return e.prototype=t,new e}),Object.keys||(Object.keys=function(t){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);return e}),r.prototype={end:function(){O(this)},write:function(e){var r=this;if(this.error)throw this.error;if(r.closed)return B(r,"Cannot write after close. Assign an onready handler.");if(null===e)return O(r);"object"==typeof e&&(e=e.toString());for(var i=0,o="";o=V(e,i++),r.c=o,o;)switch(r.trackPosition&&(r.position++,"\n"===o?(r.line++,r.column=0):r.column++),r.state){case _.BEGIN:if(r.state=_.BEGIN_WHITESPACE,"\ufeff"===o)continue;M(r,o);continue;case _.BEGIN_WHITESPACE:M(r,o);continue;case _.TEXT:if(r.sawRoot&&!r.closedRoot){for(var s=i-1;o&&"<"!==o&&"&"!==o;)(o=V(e,i++))&&r.trackPosition&&(r.position++,"\n"===o?(r.line++,r.column=0):r.column++);r.textNode+=e.substring(s,i-1)}"<"!==o||r.sawRoot&&r.closedRoot&&!r.strict?(y(o)||r.sawRoot&&!r.closedRoot||D(r,"Text data outside of root node."),"&"===o?r.state=_.TEXT_ENTITY:r.textNode+=o):(r.state=_.OPEN_WAKA,r.startTagPosition=r.position);continue;case _.SCRIPT:"<"===o?r.state=_.SCRIPT_ENDING:r.script+=o;continue;case _.SCRIPT_ENDING:"/"===o?r.state=_.CLOSE_TAG:(r.script+="<"+o,r.state=_.SCRIPT);continue;case _.OPEN_WAKA:if("!"===o)r.state=_.SGML_DECL,r.sgmlDecl="";else if(y(o));else if(b(h,o))r.state=_.OPEN_TAG,r.tagName=o;else if("/"===o)r.state=_.CLOSE_TAG,r.tagName="";else if("?"===o)r.state=_.PROC_INST,r.procInstName=r.procInstBody="";else{if(D(r,"Unencoded <"),r.startTagPosition+1<r.position){var c=r.position-r.startTagPosition;o=new Array(c).join(" ")+o}r.textNode+="<"+o,r.state=_.TEXT}continue;case _.SGML_DECL:if(r.sgmlDecl+o==="--"){r.state=_.COMMENT,r.comment="",r.sgmlDecl="";continue}r.doctype&&!0!==r.doctype&&r.sgmlDecl?(r.state=_.DOCTYPE_DTD,r.doctype+="<!"+r.sgmlDecl+o,r.sgmlDecl=""):(r.sgmlDecl+o).toUpperCase()===a?(x(r,"onopencdata"),r.state=_.CDATA,r.sgmlDecl="",r.cdata=""):(r.sgmlDecl+o).toUpperCase()===u?(r.state=_.DOCTYPE,(r.doctype||r.sawRoot)&&D(r,"Inappropriately located doctype declaration"),r.doctype="",r.sgmlDecl=""):">"===o?(x(r,"onsgmldeclaration",r.sgmlDecl),r.sgmlDecl="",r.state=_.TEXT):m(o)?(r.state=_.SGML_DECL_QUOTED,r.sgmlDecl+=o):r.sgmlDecl+=o;continue;case _.SGML_DECL_QUOTED:o===r.q&&(r.state=_.SGML_DECL,r.q=""),r.sgmlDecl+=o;continue;case _.DOCTYPE:">"===o?(r.state=_.TEXT,x(r,"ondoctype",r.doctype),r.doctype=!0):(r.doctype+=o,"["===o?r.state=_.DOCTYPE_DTD:m(o)&&(r.state=_.DOCTYPE_QUOTED,r.q=o));continue;case _.DOCTYPE_QUOTED:r.doctype+=o,o===r.q&&(r.q="",r.state=_.DOCTYPE);continue;case _.DOCTYPE_DTD:"]"===o?(r.doctype+=o,r.state=_.DOCTYPE):"<"===o?(r.state=_.OPEN_WAKA,r.startTagPosition=r.position):m(o)?(r.doctype+=o,r.state=_.DOCTYPE_DTD_QUOTED,r.q=o):r.doctype+=o;continue;case _.DOCTYPE_DTD_QUOTED:r.doctype+=o,o===r.q&&(r.state=_.DOCTYPE_DTD,r.q="");continue;case _.COMMENT:"-"===o?r.state=_.COMMENT_ENDING:r.comment+=o;continue;case _.COMMENT_ENDING:"-"===o?(r.state=_.COMMENT_ENDED,r.comment=C(r.opt,r.comment),r.comment&&x(r,"oncomment",r.comment),r.comment=""):(r.comment+="-"+o,r.state=_.COMMENT);continue;case _.COMMENT_ENDED:">"!==o?(D(r,"Malformed comment"),r.comment+="--"+o,r.state=_.COMMENT):r.doctype&&!0!==r.doctype?r.state=_.DOCTYPE_DTD:r.state=_.TEXT;continue;case _.CDATA:"]"===o?r.state=_.CDATA_ENDING:r.cdata+=o;continue;case _.CDATA_ENDING:"]"===o?r.state=_.CDATA_ENDING_2:(r.cdata+="]"+o,r.state=_.CDATA);continue;case _.CDATA_ENDING_2:">"===o?(r.cdata&&x(r,"oncdata",r.cdata),x(r,"onclosecdata"),r.cdata="",r.state=_.TEXT):"]"===o?r.cdata+="]":(r.cdata+="]]"+o,r.state=_.CDATA);continue;case _.PROC_INST:"?"===o?r.state=_.PROC_INST_ENDING:y(o)?r.state=_.PROC_INST_BODY:r.procInstName+=o;continue;case _.PROC_INST_BODY:if(!r.procInstBody&&y(o))continue;"?"===o?r.state=_.PROC_INST_ENDING:r.procInstBody+=o;continue;case _.PROC_INST_ENDING:">"===o?(x(r,"onprocessinginstruction",{name:r.procInstName,body:r.procInstBody}),r.procInstName=r.procInstBody="",r.state=_.TEXT):(r.procInstBody+="?"+o,r.state=_.PROC_INST_BODY);continue;case _.OPEN_TAG:b(p,o)?r.tagName+=o:(U(r),">"===o?P(r):"/"===o?r.state=_.OPEN_TAG_SLASH:(y(o)||D(r,"Invalid character in tag name"),r.state=_.ATTRIB));continue;case _.OPEN_TAG_SLASH:">"===o?(P(r,!0),K(r)):(D(r,"Forward-slash in opening tag not followed by >"),r.state=_.ATTRIB);continue;case _.ATTRIB:if(y(o))continue;">"===o?P(r):"/"===o?r.state=_.OPEN_TAG_SLASH:b(h,o)?(r.attribName=o,r.attribValue="",r.state=_.ATTRIB_NAME):D(r,"Invalid attribute name");continue;case _.ATTRIB_NAME:"="===o?r.state=_.ATTRIB_VALUE:">"===o?(D(r,"Attribute without value"),r.attribValue=r.attribName,L(r),P(r)):y(o)?r.state=_.ATTRIB_NAME_SAW_WHITE:b(p,o)?r.attribName+=o:D(r,"Invalid attribute name");continue;case _.ATTRIB_NAME_SAW_WHITE:if("="===o)r.state=_.ATTRIB_VALUE;else{if(y(o))continue;D(r,"Attribute without value"),r.tag.attributes[r.attribName]="",r.attribValue="",x(r,"onattribute",{name:r.attribName,value:""}),r.attribName="",">"===o?P(r):b(h,o)?(r.attribName=o,r.state=_.ATTRIB_NAME):(D(r,"Invalid attribute name"),r.state=_.ATTRIB)}continue;case _.ATTRIB_VALUE:if(y(o))continue;m(o)?(r.q=o,r.state=_.ATTRIB_VALUE_QUOTED):(r.opt.unquotedAttributeValues||B(r,"Unquoted attribute value"),r.state=_.ATTRIB_VALUE_UNQUOTED,r.attribValue=o);continue;case _.ATTRIB_VALUE_QUOTED:if(o!==r.q){"&"===o?r.state=_.ATTRIB_VALUE_ENTITY_Q:r.attribValue+=o;continue}L(r),r.q="",r.state=_.ATTRIB_VALUE_CLOSED;continue;case _.ATTRIB_VALUE_CLOSED:y(o)?r.state=_.ATTRIB:">"===o?P(r):"/"===o?r.state=_.OPEN_TAG_SLASH:b(h,o)?(D(r,"No whitespace between attributes"),r.attribName=o,r.attribValue="",r.state=_.ATTRIB_NAME):D(r,"Invalid attribute name");continue;case _.ATTRIB_VALUE_UNQUOTED:if(!E(o)){"&"===o?r.state=_.ATTRIB_VALUE_ENTITY_U:r.attribValue+=o;continue}L(r),">"===o?P(r):r.state=_.ATTRIB;continue;case _.CLOSE_TAG:if(r.tagName)">"===o?K(r):b(p,o)?r.tagName+=o:r.script?(r.script+="</"+r.tagName,r.tagName="",r.state=_.SCRIPT):(y(o)||D(r,"Invalid tagname in closing tag"),r.state=_.CLOSE_TAG_SAW_WHITE);else{if(y(o))continue;T(h,o)?r.script?(r.script+="</"+o,r.state=_.SCRIPT):D(r,"Invalid tagname in closing tag."):r.tagName=o}continue;case _.CLOSE_TAG_SAW_WHITE:if(y(o))continue;">"===o?K(r):D(r,"Invalid characters in closing tag");continue;case _.TEXT_ENTITY:case _.ATTRIB_VALUE_ENTITY_Q:case _.ATTRIB_VALUE_ENTITY_U:var l,f;switch(r.state){case _.TEXT_ENTITY:l=_.TEXT,f="textNode";break;case _.ATTRIB_VALUE_ENTITY_Q:l=_.ATTRIB_VALUE_QUOTED,f="attribValue";break;case _.ATTRIB_VALUE_ENTITY_U:l=_.ATTRIB_VALUE_UNQUOTED,f="attribValue"}if(";"===o){var w=k(r);r.opt.unparsedEntities&&!Object.values(t.XML_ENTITIES).includes(w)?(r.entity="",r.state=l,r.write(w)):(r[f]+=w,r.entity="",r.state=l)}else b(r.entity.length?g:d,o)?r.entity+=o:(D(r,"Invalid character in entity name"),r[f]+="&"+r.entity+o,r.entity="",r.state=l);continue;default:throw new Error(r,"Unknown state: "+r.state)}return r.position>=r.bufferCheckPosition&&function(e){for(var r=Math.max(t.MAX_BUFFER_LENGTH,10),i=0,o=0,s=n.length;o<s;o++){var a=e[n[o]].length;if(a>r)switch(n[o]){case"textNode":F(e);break;case"cdata":x(e,"oncdata",e.cdata),e.cdata="";break;case"script":x(e,"onscript",e.script),e.script="";break;default:B(e,"Max buffer length exceeded: "+n[o])}i=Math.max(i,a)}var u=t.MAX_BUFFER_LENGTH-i;e.bufferCheckPosition=u+e.position}(r),r}
/*! http://mths.be/fromcodepoint v0.1.0 by @mathias */,resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){var t;F(t=this),""!==t.cdata&&(x(t,"oncdata",t.cdata),t.cdata=""),""!==t.script&&(x(t,"onscript",t.script),t.script="")}};try{e=s.Stream}catch(j){e=function(){}}e||(e=function(){});var i=t.EVENTS.filter((function(t){return"error"!==t&&"end"!==t}));function o(t,n){if(!(this instanceof o))return new o(t,n);e.apply(this),this._parser=new r(t,n),this.writable=!0,this.readable=!0;var s=this;this._parser.onend=function(){s.emit("end")},this._parser.onerror=function(t){s.emit("error",t),s._parser.error=null},this._decoder=null,i.forEach((function(t){Object.defineProperty(s,"on"+t,{get:function(){return s._parser["on"+t]},set:function(e){if(!e)return s.removeAllListeners(t),s._parser["on"+t]=e,e;s.on(t,e)},enumerable:!0,configurable:!1})}))}o.prototype=Object.create(e.prototype,{constructor:{value:o}}),o.prototype.write=function(t){if("function"==typeof Buffer&&"function"==typeof Buffer.isBuffer&&Buffer.isBuffer(t)){if(!this._decoder){var e=S().StringDecoder;this._decoder=new e("utf8")}t=this._decoder.write(t)}return this._parser.write(t.toString()),this.emit("data",t),!0},o.prototype.end=function(t){return t&&t.length&&this.write(t),this._parser.end(),!0},o.prototype.on=function(t,n){var r=this;return r._parser["on"+t]||-1===i.indexOf(t)||(r._parser["on"+t]=function(){var e=1===arguments.length?[arguments[0]]:Array.apply(null,arguments);e.splice(0,0,t),r.emit.apply(r,e)}),e.prototype.on.call(r,t,n)};var a="[CDATA[",u="DOCTYPE",c="http://www.w3.org/XML/1998/namespace",l="http://www.w3.org/2000/xmlns/",f={xml:c,xmlns:l},h=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,p=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/,d=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,g=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/;function y(t){return" "===t||"\n"===t||"\r"===t||"\t"===t}function m(t){return'"'===t||"'"===t}function E(t){return">"===t||y(t)}function b(t,e){return t.test(e)}function T(t,e){return!b(t,e)}var w,A,v,_=0;for(var N in t.STATE={BEGIN:_++,BEGIN_WHITESPACE:_++,TEXT:_++,TEXT_ENTITY:_++,OPEN_WAKA:_++,SGML_DECL:_++,SGML_DECL_QUOTED:_++,DOCTYPE:_++,DOCTYPE_QUOTED:_++,DOCTYPE_DTD:_++,DOCTYPE_DTD_QUOTED:_++,COMMENT_STARTING:_++,COMMENT:_++,COMMENT_ENDING:_++,COMMENT_ENDED:_++,CDATA:_++,CDATA_ENDING:_++,CDATA_ENDING_2:_++,PROC_INST:_++,PROC_INST_BODY:_++,PROC_INST_ENDING:_++,OPEN_TAG:_++,OPEN_TAG_SLASH:_++,ATTRIB:_++,ATTRIB_NAME:_++,ATTRIB_NAME_SAW_WHITE:_++,ATTRIB_VALUE:_++,ATTRIB_VALUE_QUOTED:_++,ATTRIB_VALUE_CLOSED:_++,ATTRIB_VALUE_UNQUOTED:_++,ATTRIB_VALUE_ENTITY_Q:_++,ATTRIB_VALUE_ENTITY_U:_++,CLOSE_TAG:_++,CLOSE_TAG_SAW_WHITE:_++,SCRIPT:_++,SCRIPT_ENDING:_++},t.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},t.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(t.ENTITIES).forEach((function(e){var n=t.ENTITIES[e],r="number"==typeof n?String.fromCharCode(n):n;t.ENTITIES[e]=r})),t.STATE)t.STATE[t.STATE[N]]=N;function I(t,e,n){t[e]&&t[e](n)}function x(t,e,n){t.textNode&&F(t),I(t,e,n)}function F(t){t.textNode=C(t.opt,t.textNode),t.textNode&&I(t,"ontext",t.textNode),t.textNode=""}function C(t,e){return t.trim&&(e=e.trim()),t.normalize&&(e=e.replace(/\s+/g," ")),e}function B(t,e){return F(t),t.trackPosition&&(e+="\nLine: "+t.line+"\nColumn: "+t.column+"\nChar: "+t.c),e=new Error(e),t.error=e,I(t,"onerror",e),t}function O(t){return t.sawRoot&&!t.closedRoot&&D(t,"Unclosed root tag"),t.state!==_.BEGIN&&t.state!==_.BEGIN_WHITESPACE&&t.state!==_.TEXT&&B(t,"Unexpected end"),F(t),t.c="",t.closed=!0,I(t,"onend"),r.call(t,t.strict,t.opt),t}function D(t,e){if("object"!=typeof t||!(t instanceof r))throw new Error("bad call to strictFail");t.strict&&B(t,e)}function U(t){t.strict||(t.tagName=t.tagName[t.looseCase]());var e=t.tags[t.tags.length-1]||t,n=t.tag={name:t.tagName,attributes:{}};t.opt.xmlns&&(n.ns=e.ns),t.attribList.length=0,x(t,"onopentagstart",n)}function R(t,e){var n=t.indexOf(":")<0?["",t]:t.split(":"),r=n[0],i=n[1];return e&&"xmlns"===t&&(r="xmlns",i=""),{prefix:r,local:i}}function L(t){if(t.strict||(t.attribName=t.attribName[t.looseCase]()),-1!==t.attribList.indexOf(t.attribName)||t.tag.attributes.hasOwnProperty(t.attribName))t.attribName=t.attribValue="";else{if(t.opt.xmlns){var e=R(t.attribName,!0),n=e.prefix,r=e.local;if("xmlns"===n)if("xml"===r&&t.attribValue!==c)D(t,"xml: prefix must be bound to "+c+"\nActual: "+t.attribValue);else if("xmlns"===r&&t.attribValue!==l)D(t,"xmlns: prefix must be bound to "+l+"\nActual: "+t.attribValue);else{var i=t.tag,o=t.tags[t.tags.length-1]||t;i.ns===o.ns&&(i.ns=Object.create(o.ns)),i.ns[r]=t.attribValue}t.attribList.push([t.attribName,t.attribValue])}else t.tag.attributes[t.attribName]=t.attribValue,x(t,"onattribute",{name:t.attribName,value:t.attribValue});t.attribName=t.attribValue=""}}function P(t,e){if(t.opt.xmlns){var n=t.tag,r=R(t.tagName);n.prefix=r.prefix,n.local=r.local,n.uri=n.ns[r.prefix]||"",n.prefix&&!n.uri&&(D(t,"Unbound namespace prefix: "+JSON.stringify(t.tagName)),n.uri=r.prefix);var i=t.tags[t.tags.length-1]||t;n.ns&&i.ns!==n.ns&&Object.keys(n.ns).forEach((function(e){x(t,"onopennamespace",{prefix:e,uri:n.ns[e]})}));for(var o=0,s=t.attribList.length;o<s;o++){var a=t.attribList[o],u=a[0],c=a[1],l=R(u,!0),f=l.prefix,h=l.local,p=""===f?"":n.ns[f]||"",d={name:u,value:c,prefix:f,local:h,uri:p};f&&"xmlns"!==f&&!p&&(D(t,"Unbound namespace prefix: "+JSON.stringify(f)),d.uri=f),t.tag.attributes[u]=d,x(t,"onattribute",d)}t.attribList.length=0}t.tag.isSelfClosing=!!e,t.sawRoot=!0,t.tags.push(t.tag),x(t,"onopentag",t.tag),e||(t.noscript||"script"!==t.tagName.toLowerCase()?t.state=_.TEXT:t.state=_.SCRIPT,t.tag=null,t.tagName=""),t.attribName=t.attribValue="",t.attribList.length=0}function K(t){if(!t.tagName)return D(t,"Weird empty close tag."),t.textNode+="</>",void(t.state=_.TEXT);if(t.script){if("script"!==t.tagName)return t.script+="</"+t.tagName+">",t.tagName="",void(t.state=_.SCRIPT);x(t,"onscript",t.script),t.script=""}var e=t.tags.length,n=t.tagName;t.strict||(n=n[t.looseCase]());for(var r=n;e--&&t.tags[e].name!==r;)D(t,"Unexpected close tag");if(e<0)return D(t,"Unmatched closing tag: "+t.tagName),t.textNode+="</"+t.tagName+">",void(t.state=_.TEXT);t.tagName=n;for(var i=t.tags.length;i-- >e;){var o=t.tag=t.tags.pop();t.tagName=t.tag.name,x(t,"onclosetag",t.tagName);var s={};for(var a in o.ns)s[a]=o.ns[a];var u=t.tags[t.tags.length-1]||t;t.opt.xmlns&&o.ns!==u.ns&&Object.keys(o.ns).forEach((function(e){var n=o.ns[e];x(t,"onclosenamespace",{prefix:e,uri:n})}))}0===e&&(t.closedRoot=!0),t.tagName=t.attribValue=t.attribName="",t.attribList.length=0,t.state=_.TEXT}function k(t){var e,n=t.entity,r=n.toLowerCase(),i="";return t.ENTITIES[n]?t.ENTITIES[n]:t.ENTITIES[r]?t.ENTITIES[r]:("#"===(n=r).charAt(0)&&("x"===n.charAt(1)?(n=n.slice(2),i=(e=parseInt(n,16)).toString(16)):(n=n.slice(1),i=(e=parseInt(n,10)).toString(10))),n=n.replace(/^0+/,""),isNaN(e)||i.toLowerCase()!==n?(D(t,"Invalid character entity"),"&"+t.entity+";"):String.fromCodePoint(e))}function M(t,e){"<"===e?(t.state=_.OPEN_WAKA,t.startTagPosition=t.position):y(e)||(D(t,"Non-whitespace before first tag."),t.textNode=e,t.state=_.TEXT)}function V(t,e){var n="";return e<t.length&&(n=t.charAt(e)),n}_=t.STATE,String.fromCodePoint||(w=String.fromCharCode,A=Math.floor,v=function(){var t,e,n=[],r=-1,i=arguments.length;if(!i)return"";for(var o="";++r<i;){var s=Number(arguments[r]);if(!isFinite(s)||s<0||s>1114111||A(s)!==s)throw RangeError("Invalid code point: "+s);s<=65535?n.push(s):(t=55296+((s-=65536)>>10),e=s%1024+56320,n.push(t,e)),(r+1===i||n.length>16384)&&(o+=w.apply(null,n),n.length=0)}return o},Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:v,configurable:!0,writable:!0}):String.fromCodePoint=v)}(o)),o}function R(){return E?m:(E=1,m={isArray:function(t){return Array.isArray?Array.isArray(t):"[object Array]"===Object.prototype.toString.call(t)}})}function L(){if(T)return b;T=1;var t=R().isArray;return b={copyOptions:function(t){var e,n={};for(e in t)t.hasOwnProperty(e)&&(n[e]=t[e]);return n},ensureFlagExists:function(t,e){t in e&&"boolean"==typeof e[t]||(e[t]=!1)},ensureSpacesExists:function(t){(!("spaces"in t)||"number"!=typeof t.spaces&&"string"!=typeof t.spaces)&&(t.spaces=0)},ensureAlwaysArrayExists:function(e){"alwaysArray"in e&&("boolean"==typeof e.alwaysArray||t(e.alwaysArray))||(e.alwaysArray=!1)},ensureKeyExists:function(t,e){t+"Key"in e&&"string"==typeof e[t+"Key"]||(e[t+"Key"]=e.compact?"_"+t:t)},checkFnExists:function(t,e){return t+"Fn"in e}}}function P(){if(A)return w;A=1;var t,e,n=U(),r=L(),i=R().isArray;function o(t){var e=Number(t);if(!isNaN(e))return e;var n=t.toLowerCase();return"true"===n||"false"!==n&&t}function s(n,r){var o;if(t.compact){if(!e[t[n+"Key"]]&&(i(t.alwaysArray)?-1!==t.alwaysArray.indexOf(t[n+"Key"]):t.alwaysArray)&&(e[t[n+"Key"]]=[]),e[t[n+"Key"]]&&!i(e[t[n+"Key"]])&&(e[t[n+"Key"]]=[e[t[n+"Key"]]]),n+"Fn"in t&&"string"==typeof r&&(r=t[n+"Fn"](r,e)),"instruction"===n&&("instructionFn"in t||"instructionNameFn"in t))for(o in r)if(r.hasOwnProperty(o))if("instructionFn"in t)r[o]=t.instructionFn(r[o],o,e);else{var s=r[o];delete r[o],r[t.instructionNameFn(o,s,e)]=s}i(e[t[n+"Key"]])?e[t[n+"Key"]].push(r):e[t[n+"Key"]]=r}else{e[t.elementsKey]||(e[t.elementsKey]=[]);var a={};if(a[t.typeKey]=n,"instruction"===n){for(o in r)if(r.hasOwnProperty(o))break;a[t.nameKey]="instructionNameFn"in t?t.instructionNameFn(o,r,e):o,t.instructionHasAttributes?(a[t.attributesKey]=r[o][t.attributesKey],"instructionFn"in t&&(a[t.attributesKey]=t.instructionFn(a[t.attributesKey],o,e))):("instructionFn"in t&&(r[o]=t.instructionFn(r[o],o,e)),a[t.instructionKey]=r[o])}else n+"Fn"in t&&(r=t[n+"Fn"](r,e)),a[t[n+"Key"]]=r;t.addParent&&(a[t.parentKey]=e),e[t.elementsKey].push(a)}}function a(n){var r;if("attributesFn"in t&&n&&(n=t.attributesFn(n,e)),(t.trim||"attributeValueFn"in t||"attributeNameFn"in t||t.nativeTypeAttributes)&&n)for(r in n)if(n.hasOwnProperty(r)&&(t.trim&&(n[r]=n[r].trim()),t.nativeTypeAttributes&&(n[r]=o(n[r])),"attributeValueFn"in t&&(n[r]=t.attributeValueFn(n[r],r,e)),"attributeNameFn"in t)){var i=n[r];delete n[r],n[t.attributeNameFn(r,n[r],e)]=i}return n}function u(n){var r={};if(n.body&&("xml"===n.name.toLowerCase()||t.instructionHasAttributes)){for(var i,o=/([\w:-]+)\s*=\s*(?:"([^"]*)"|'([^']*)'|(\w+))\s*/g;null!==(i=o.exec(n.body));)r[i[1]]=i[2]||i[3]||i[4];r=a(r)}if("xml"===n.name.toLowerCase()){if(t.ignoreDeclaration)return;e[t.declarationKey]={},Object.keys(r).length&&(e[t.declarationKey][t.attributesKey]=r),t.addParent&&(e[t.declarationKey][t.parentKey]=e)}else{if(t.ignoreInstruction)return;t.trim&&(n.body=n.body.trim());var u={};t.instructionHasAttributes&&Object.keys(r).length?(u[n.name]={},u[n.name][t.attributesKey]=r):u[n.name]=n.body,s("instruction",u)}}function c(n,r){var o;if("object"==typeof n&&(r=n.attributes,n=n.name),r=a(r),"elementNameFn"in t&&(n=t.elementNameFn(n,e)),t.compact){var s;if(o={},!t.ignoreAttributes&&r&&Object.keys(r).length)for(s in o[t.attributesKey]={},r)r.hasOwnProperty(s)&&(o[t.attributesKey][s]=r[s]);!(n in e)&&(i(t.alwaysArray)?-1!==t.alwaysArray.indexOf(n):t.alwaysArray)&&(e[n]=[]),e[n]&&!i(e[n])&&(e[n]=[e[n]]),i(e[n])?e[n].push(o):e[n]=o}else e[t.elementsKey]||(e[t.elementsKey]=[]),(o={})[t.typeKey]="element",o[t.nameKey]=n,!t.ignoreAttributes&&r&&Object.keys(r).length&&(o[t.attributesKey]=r),t.alwaysChildren&&(o[t.elementsKey]=[]),e[t.elementsKey].push(o);o[t.parentKey]=e,e=o}function l(e){t.ignoreText||(e.trim()||t.captureSpacesBetweenElements)&&(t.trim&&(e=e.trim()),t.nativeType&&(e=o(e)),t.sanitize&&(e=e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")),s("text",e))}function f(e){t.ignoreComment||(t.trim&&(e=e.trim()),s("comment",e))}function h(n){var r=e[t.parentKey];t.addParent||delete e[t.parentKey],e=r}function p(e){t.ignoreCdata||(t.trim&&(e=e.trim()),s("cdata",e))}function d(e){t.ignoreDoctype||(e=e.replace(/^ /,""),t.trim&&(e=e.trim()),s("doctype",e))}function g(t){t.note=t}return w=function(i,o){var s=n.parser(!0,{}),a={};if(e=a,t=function(e){return t=r.copyOptions(e),r.ensureFlagExists("ignoreDeclaration",t),r.ensureFlagExists("ignoreInstruction",t),r.ensureFlagExists("ignoreAttributes",t),r.ensureFlagExists("ignoreText",t),r.ensureFlagExists("ignoreComment",t),r.ensureFlagExists("ignoreCdata",t),r.ensureFlagExists("ignoreDoctype",t),r.ensureFlagExists("compact",t),r.ensureFlagExists("alwaysChildren",t),r.ensureFlagExists("addParent",t),r.ensureFlagExists("trim",t),r.ensureFlagExists("nativeType",t),r.ensureFlagExists("nativeTypeAttributes",t),r.ensureFlagExists("sanitize",t),r.ensureFlagExists("instructionHasAttributes",t),r.ensureFlagExists("captureSpacesBetweenElements",t),r.ensureAlwaysArrayExists(t),r.ensureKeyExists("declaration",t),r.ensureKeyExists("instruction",t),r.ensureKeyExists("attributes",t),r.ensureKeyExists("text",t),r.ensureKeyExists("comment",t),r.ensureKeyExists("cdata",t),r.ensureKeyExists("doctype",t),r.ensureKeyExists("type",t),r.ensureKeyExists("name",t),r.ensureKeyExists("elements",t),r.ensureKeyExists("parent",t),r.checkFnExists("doctype",t),r.checkFnExists("instruction",t),r.checkFnExists("cdata",t),r.checkFnExists("comment",t),r.checkFnExists("text",t),r.checkFnExists("instructionName",t),r.checkFnExists("elementName",t),r.checkFnExists("attributeName",t),r.checkFnExists("attributeValue",t),r.checkFnExists("attributes",t),t}(o),s.opt={strictEntities:!0},s.onopentag=c,s.ontext=l,s.oncomment=f,s.onclosetag=h,s.onerror=g,s.oncdata=p,s.ondoctype=d,s.onprocessinginstruction=u,s.write(i).close(),a[t.elementsKey]){var y=a[t.elementsKey];delete a[t.elementsKey],a[t.elementsKey]=y,delete a.text}return a}}function K(){if(_)return v;_=1;var t=L(),e=P();return v=function(n,r){var i,o,s;return i=function(e){var n=t.copyOptions(e);return t.ensureSpacesExists(n),n}(r),o=e(n,i),s="compact"in i&&i.compact?"_parent":"parent",("addParent"in i&&i.addParent?JSON.stringify(o,(function(t,e){return t===s?"_":e}),i.spaces):JSON.stringify(o,null,i.spaces)).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}}function k(){if(I)return N;I=1;var t,e,n=L(),r=R().isArray;function i(t,e,n){return(!n&&t.spaces?"\n":"")+Array(e+1).join(t.spaces)}function o(n,r,o){if(r.ignoreAttributes)return"";"attributesFn"in r&&(n=r.attributesFn(n,e,t));var s,a,u,c,l=[];for(s in n)n.hasOwnProperty(s)&&null!==n[s]&&void 0!==n[s]&&(c=r.noQuotesForNativeAttributes&&"string"!=typeof n[s]?"":'"',a=(a=""+n[s]).replace(/"/g,"&quot;"),u="attributeNameFn"in r?r.attributeNameFn(s,a,e,t):s,l.push(r.spaces&&r.indentAttributes?i(r,o+1,!1):" "),l.push(u+"="+c+("attributeValueFn"in r?r.attributeValueFn(a,s,e,t):a)+c));return n&&Object.keys(n).length&&r.spaces&&r.indentAttributes&&l.push(i(r,o,!1)),l.join("")}function s(n,r,i){return t=n,e="xml",r.ignoreDeclaration?"":"<?xml"+o(n[r.attributesKey],r,i)+"?>"}function a(n,r,i){if(r.ignoreInstruction)return"";var s;for(s in n)if(n.hasOwnProperty(s))break;var a="instructionNameFn"in r?r.instructionNameFn(s,n[s],e,t):s;if("object"==typeof n[s])return t=n,e=a,"<?"+a+o(n[s][r.attributesKey],r,i)+"?>";var u=n[s]?n[s]:"";return"instructionFn"in r&&(u=r.instructionFn(u,s,e,t)),"<?"+a+(u?" "+u:"")+"?>"}function u(n,r){return r.ignoreComment?"":"\x3c!--"+("commentFn"in r?r.commentFn(n,e,t):n)+"--\x3e"}function c(n,r){return r.ignoreCdata?"":"<![CDATA["+("cdataFn"in r?r.cdataFn(n,e,t):n.replace("]]>","]]]]><![CDATA[>"))+"]]>"}function l(n,r){return r.ignoreDoctype?"":"<!DOCTYPE "+("doctypeFn"in r?r.doctypeFn(n,e,t):n)+">"}function f(n,r){return r.ignoreText?"":(n=(n=(n=""+n).replace(/&amp;/g,"&")).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),"textFn"in r?r.textFn(n,e,t):n)}function h(n,r,s,p){return n.reduce((function(n,d){var g=i(r,s,p&&!n);switch(d.type){case"element":return n+g+function(n,r,i){t=n,e=n.name;var s=[],a="elementNameFn"in r?r.elementNameFn(n.name,n):n.name;s.push("<"+a),n[r.attributesKey]&&s.push(o(n[r.attributesKey],r,i));var u=n[r.elementsKey]&&n[r.elementsKey].length||n[r.attributesKey]&&"preserve"===n[r.attributesKey]["xml:space"];return u||(u="fullTagEmptyElementFn"in r?r.fullTagEmptyElementFn(n.name,n):r.fullTagEmptyElement),u?(s.push(">"),n[r.elementsKey]&&n[r.elementsKey].length&&(s.push(h(n[r.elementsKey],r,i+1)),t=n,e=n.name),s.push(r.spaces&&function(t,e){var n;if(t.elements&&t.elements.length)for(n=0;n<t.elements.length;++n)switch(t.elements[n][e.typeKey]){case"text":if(e.indentText)return!0;break;case"cdata":if(e.indentCdata)return!0;break;case"instruction":if(e.indentInstruction)return!0;break;default:return!0}return!1}(n,r)?"\n"+Array(i+1).join(r.spaces):""),s.push("</"+a+">")):s.push("/>"),s.join("")}(d,r,s);case"comment":return n+g+u(d[r.commentKey],r);case"doctype":return n+g+l(d[r.doctypeKey],r);case"cdata":return n+(r.indentCdata?g:"")+c(d[r.cdataKey],r);case"text":return n+(r.indentText?g:"")+f(d[r.textKey],r);case"instruction":var y={};return y[d[r.nameKey]]=d[r.attributesKey]?d:d[r.instructionKey],n+(r.indentInstruction?g:"")+a(y,r,s)}}),"")}function p(t,e,n){var r;for(r in t)if(t.hasOwnProperty(r))switch(r){case e.parentKey:case e.attributesKey:break;case e.textKey:if(e.indentText||n)return!0;break;case e.cdataKey:if(e.indentCdata||n)return!0;break;case e.instructionKey:if(e.indentInstruction||n)return!0;break;case e.doctypeKey:case e.commentKey:default:return!0}return!1}function d(n,r,s,a,u){t=n,e=r;var c="elementNameFn"in s?s.elementNameFn(r,n):r;if(null==n||""===n)return"fullTagEmptyElementFn"in s&&s.fullTagEmptyElementFn(r,n)||s.fullTagEmptyElement?"<"+c+"></"+c+">":"<"+c+"/>";var l=[];if(r){if(l.push("<"+c),"object"!=typeof n)return l.push(">"+f(n,s)+"</"+c+">"),l.join("");n[s.attributesKey]&&l.push(o(n[s.attributesKey],s,a));var h=p(n,s,!0)||n[s.attributesKey]&&"preserve"===n[s.attributesKey]["xml:space"];if(h||(h="fullTagEmptyElementFn"in s?s.fullTagEmptyElementFn(r,n):s.fullTagEmptyElement),!h)return l.push("/>"),l.join("");l.push(">")}return l.push(g(n,s,a+1,!1)),t=n,e=r,r&&l.push((u?i(s,a,!1):"")+"</"+c+">"),l.join("")}function g(t,e,n,o){var h,g,y,m=[];for(g in t)if(t.hasOwnProperty(g))for(y=r(t[g])?t[g]:[t[g]],h=0;h<y.length;++h){switch(g){case e.declarationKey:m.push(s(y[h],e,n));break;case e.instructionKey:m.push((e.indentInstruction?i(e,n,o):"")+a(y[h],e,n));break;case e.attributesKey:case e.parentKey:break;case e.textKey:m.push((e.indentText?i(e,n,o):"")+f(y[h],e));break;case e.cdataKey:m.push((e.indentCdata?i(e,n,o):"")+c(y[h],e));break;case e.doctypeKey:m.push(i(e,n,o)+l(y[h],e));break;case e.commentKey:m.push(i(e,n,o)+u(y[h],e));break;default:m.push(i(e,n,o)+d(y[h],g,e,n,p(y[h],e)))}o=o&&!m.length}return m.join("")}return N=function(r,i){i=function(t){var e=n.copyOptions(t);return n.ensureFlagExists("ignoreDeclaration",e),n.ensureFlagExists("ignoreInstruction",e),n.ensureFlagExists("ignoreAttributes",e),n.ensureFlagExists("ignoreText",e),n.ensureFlagExists("ignoreComment",e),n.ensureFlagExists("ignoreCdata",e),n.ensureFlagExists("ignoreDoctype",e),n.ensureFlagExists("compact",e),n.ensureFlagExists("indentText",e),n.ensureFlagExists("indentCdata",e),n.ensureFlagExists("indentAttributes",e),n.ensureFlagExists("indentInstruction",e),n.ensureFlagExists("fullTagEmptyElement",e),n.ensureFlagExists("noQuotesForNativeAttributes",e),n.ensureSpacesExists(e),"number"==typeof e.spaces&&(e.spaces=Array(e.spaces+1).join(" ")),n.ensureKeyExists("declaration",e),n.ensureKeyExists("instruction",e),n.ensureKeyExists("attributes",e),n.ensureKeyExists("text",e),n.ensureKeyExists("comment",e),n.ensureKeyExists("cdata",e),n.ensureKeyExists("doctype",e),n.ensureKeyExists("type",e),n.ensureKeyExists("name",e),n.ensureKeyExists("elements",e),n.checkFnExists("doctype",e),n.checkFnExists("instruction",e),n.checkFnExists("cdata",e),n.checkFnExists("comment",e),n.checkFnExists("text",e),n.checkFnExists("instructionName",e),n.checkFnExists("elementName",e),n.checkFnExists("attributeName",e),n.checkFnExists("attributeValue",e),n.checkFnExists("attributes",e),n.checkFnExists("fullTagEmptyElement",e),e}(i);var o=[];return t=r,e="_root_",i.compact?o.push(g(r,i,0,!0)):(r[i.declarationKey]&&o.push(s(r[i.declarationKey],i,0)),r[i.elementsKey]&&r[i.elementsKey].length&&o.push(h(r[i.elementsKey],i,0,!o.length))),o.join("")}}var M=function(){if(B)return C;B=1;var t=P(),e=K(),n=k(),r=function(){if(F)return x;F=1;var t=k();return x=function(e,n){e instanceof Buffer&&(e=e.toString());var r=null;if("string"==typeof e)try{r=JSON.parse(e)}catch(i){throw new Error("The JSON structure is invalid")}else r=e;return t(r,n)}}();return C={xml2js:t,xml2json:e,js2xml:n,json2xml:r}}();async function V({id:t="",cursor:r=""}){var i,o,s,a,u,c,l,f,h,p,d,g;const y=[],m=await e({variables:{cursor:r,count:8,scale:1,id:btoa(`app_collection:${t}:1560653304174514:133`)},doc_id:"3975496529227403"}),E=n(m),{edges:b=[],page_info:T={}}=(null==(o=null==(i=null==E?void 0:E.data)?void 0:i.node)?void 0:o.pageItems)||{};console.log("user video",E);for(const e of b){const t=(null==(a=null==(s=null==e?void 0:e.node)?void 0:s.node)?void 0:a.id)||"";y.push({id:t,recent:y.length,created_time:"",description:null==(l=null==(c=null==(u=null==e?void 0:e.node)?void 0:u.node)?void 0:c.title)?void 0:l.text,length:null==(h=null==(f=null==e?void 0:e.node)?void 0:f.node)?void 0:h.playable_duration,url:null==(p=null==e?void 0:e.node)?void 0:p.url,source:"",picture:null==(g=null==(d=null==e?void 0:e.node)?void 0:d.image)?void 0:g.uri,cursor:(null==e?void 0:e.cursor)||""})}return{videos:y,nextCursor:null==T?void 0:T.end_cursor}}async function j({id:t="",cursor:r=""}){var i,o,s,a,u,c,l,f,h,p,d,g;const y=[],m=await e({fb_api_req_friendly_name:"GroupsCometVideosRootQueryContainerQuery",variables:{cursor:r,count:8,scale:2,groupID:t},doc_id:"6553573504724585"}),E=n(m),{edges:b=[],page_info:T={}}=(null==(s=null==(o=null==(i=null==E?void 0:E.data)?void 0:i.group)?void 0:o.group_mediaset)?void 0:s.media)||{};console.log("group video",E);for(const e of b){const t=(null==(a=null==e?void 0:e.node)?void 0:a.id)||"";y.push({id:t,recent:y.length,created_time:"",description:null==(l=null==(c=null==(u=null==e?void 0:e.node)?void 0:u.creation_story)?void 0:c.message)?void 0:l.text,length:null==(h=null==(f=null==e?void 0:e.node)?void 0:f.node)?void 0:h.playable_duration,url:null==(p=null==e?void 0:e.node)?void 0:p.url,source:"",picture:null==(g=null==(d=null==e?void 0:e.node)?void 0:d.image)?void 0:g.uri,cursor:(null==e?void 0:e.cursor)||""})}return{videos:y,nextCursor:null==T?void 0:T.end_cursor}}const G={};function Y(t){var e,n;try{if(!t)return[];const i=M.xml2js(decodeURIComponent(t),{compact:!0});console.log("playlist",i);let o=r(i,"AdaptationSet",!1)||[];Array.isArray(o)||(o=[o]);return((null==(n=null==(e=null==o?void 0:o.flat())?void 0:e.map((t=>null==t?void 0:t.Representation)))?void 0:n.flat())||[]).map((t=>{var e,n,r,i;const o=null==t?void 0:t._attributes;return o?{id:o.id,codecs:o.codecs,mimeType:o.mimeType,isAudio:null==(e=o.mimeType)?void 0:e.startsWith("audio"),height:parseInt(o.height),width:parseInt(o.width),bandwidth:parseInt(o.bandwidth),qualityClass:o.FBQualityClass,qualityLabel:o.FBQualityLabel,source:null==(n=null==t?void 0:t.BaseURL)?void 0:n._text,length:parseInt(null==(i=null==(r=null==t?void 0:t.SegmentBase)?void 0:r._attributes)?void 0:i.timescale)}:null})).filter(Boolean)}catch(i){return console.log("extractVideoVariants error",i),[]}}function q(t){var e,n,r,i,o;try{return console.log("video info",t),{id:t.videoId||t.id,owner:(null==(e=t.owner)?void 0:e.id)||(null==(n=t.video_owner)?void 0:n.id),length:t.playable_duration_in_ms/1e3,url:t.url||t.permalink_url,width:t.original_width||t.width,height:t.original_height||t.height,source:t.browser_native_hd_url||t.playable_url_quality_hd||t.browser_native_sd_url||t.playable_url,quality:t.min_quality_preference,variants:Y(t.playlist),created_time:(1e3*t.publish_time).toString(),thumbnail:(null==(r=t.image)?void 0:r.uri)||(null==(o=null==(i=t.preferred_thumbnail)?void 0:i.image)?void 0:o.uri)}}catch(s){return console.log("extractVideoInfo error",s),null}}async function X(t,r=!1){var i,o;if(!r&&(null==(i=G[t])?void 0:i.source))return G[t];const s=await e({fb_api_req_friendly_name:"CometTahoeRootQuery",variables:{caller:"TAHOE",chainingCursor:null,chainingSeedVideoId:null,channelEntryPoint:"TAHOE",channelID:"",feedbackSource:41,feedLocation:"TAHOE",focusCommentID:null,isCrawler:!1,privacySelectorRenderLocation:"COMET_STREAM",renderLocation:"video_channel",scale:1,streamChainingSection:!1,useDefaultActor:!1,videoChainingContext:null,videoID:t,__relay_internal__pv__CometUFIShareActionMigrationrelayprovider:!0,__relay_internal__pv__CometUFIReactionsEnableShortNamerelayprovider:!1,__relay_internal__pv__StoriesLWRVariantrelayprovider:"www_new_reactions"},doc_id:"26374037368876407"}),a=n(s.split("\n")[0]),u=null==(o=null==a?void 0:a.data)?void 0:o.video;return console.log("video info",u),u&&(G[t]=q(u)),G[t]}export{q as extractVideoInfo,Y as extractVideoVariants,j as getGroupVideo,V as getUserVideo,X as getVideoInfo};
