const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./MyApp-DW5WH4Ub.js","./index-Cak6rALw.js","./index-DIX6R8z-.css"])))=>i.map(i=>d[i]);
import{aN as e}from"./index-Cak6rALw.js";import{getBiggestUrl as o}from"./core-Dw7OsFL6.js";import{x as l}from"./MyApp-DW5WH4Ub.js";import{fetchThreadsGraphQl as n}from"./core-CRQoNxLH.js";function i(e,l){var n,i,r,a,_,t,d,s,u,v,p,c,m,f,y,h,b,B,R,g;const w=null==(i=null==(n=null==e?void 0:e.node)?void 0:n.thread_items)?void 0:i[0],C=(null==(_=null==(a=null==(r=null==w?void 0:w.post)?void 0:r.text_post_app_info)?void 0:a.share_info)?void 0:_.reposted_post)||(null==(s=null==(d=null==(t=null==w?void 0:w.post)?void 0:t.text_post_app_info)?void 0:d.share_info)?void 0:s.quoted_post)||(null==w?void 0:w.post);return{id:null==(u=null==e?void 0:e.node)?void 0:u.id,pk:null==C?void 0:C.pk,code:null==C?void 0:C.code,caption:null==(v=null==C?void 0:C.caption)?void 0:v.text,image:o(null==(p=null==C?void 0:C.image_versions2)?void 0:p.candidates),video:o(null==C?void 0:C.video_versions),carousel:null==(c=null==C?void 0:C.carousel_media)?void 0:c.map((e=>{var l;return{id:null==e?void 0:e.id,pk:null==e?void 0:e.pk,code:null==e?void 0:e.code,accessibilityCaption:null==e?void 0:e.accessibility_caption,image:o(null==(l=null==e?void 0:e.image_versions2)?void 0:l.candidates),video:o(null==e?void 0:e.video_versions),isCarouselItem:!0}})),audio:null==C?void 0:C.audio,createdTime:1e3*(null==C?void 0:C.taken_at),mentions:null==(h=null==(y=null==(f=null==(m=null==C?void 0:C.text_post_app_info)?void 0:m.text_fragments)?void 0:f.fragments)?void 0:y.filter((e=>"mentrion"===(null==e?void 0:e.fragment_type))))?void 0:h.map((e=>{var o,l,n,i;return{id:null==(l=null==(o=null==e?void 0:e.mention_fragment)?void 0:o.mentioned_user)?void 0:l.id,username:null==(i=null==(n=null==e?void 0:e.mention_fragment)?void 0:n.mentioned_user)?void 0:i.username,text:null==e?void 0:e.plaintext}})),likeCount:null==C?void 0:C.like_count,replyCount:null==(b=null==C?void 0:C.text_post_app_info)?void 0:b.direct_reply_count,repostCount:null==(B=null==C?void 0:C.text_post_app_info)?void 0:B.repost_count,quoteCount:null==(R=null==C?void 0:C.text_post_app_info)?void 0:R.quote_count,isGenAI:"NONE"!=(null==(g=null==C?void 0:C.gen_ai_detection_method)?void 0:g.detection_method),cursor:(null==e?void 0:e.cursor)||(null==l?void 0:l.end_cursor),raw:e}}var r=(e=>(e.Threads="Threads",e.Replies="Replies",e.Reposts="Reposts",e))(r||{});const a={Threads:{first:{name:"BarcelonaProfileThreadsTabQuery",doc_id:"8436285959793247"},withCursor:{name:"BarcelonaProfileThreadsTabRefetchableQuery",doc_id:"8921337961211543"}},Replies:{first:{name:"BarcelonaProfileRepliesTabQuery",doc_id:"8488857591189950"},withCursor:{name:"BarcelonaProfileRepliesTabRefetchableQuery",doc_id:"8562442443831413"}},Reposts:{first:{name:"BarcelonaProfileRepostsTabQuery",doc_id:"8425754140878176"},withCursor:{name:"BarcelonaProfileRepostsTabRefetchableQuery",doc_id:"9365308133484269"}}};async function _(o="",r="",_="Threads"){let t;if(r){const e=await n({fb_api_req_friendly_name:a[_].withCursor.name,variables:{after:r,before:null,first:10,last:null,userID:o,__relay_internal__pv__BarcelonaIsLoggedInrelayprovider:!0,__relay_internal__pv__BarcelonaIsInlineReelsEnabledrelayprovider:!1,__relay_internal__pv__BarcelonaOptionalCookiesEnabledrelayprovider:!0,__relay_internal__pv__BarcelonaShowReshareCountrelayprovider:!1,__relay_internal__pv__BarcelonaQuotedPostUFIEnabledrelayprovider:!1,__relay_internal__pv__BarcelonaIsCrawlerrelayprovider:!1,__relay_internal__pv__BarcelonaShouldShowFediverseM075Featuresrelayprovider:!0},doc_id:a[_].withCursor.doc_id});t=l(e)}else{const e=await n({fb_api_req_friendly_name:a[_].first.name,variables:{userID:o,__relay_internal__pv__BarcelonaIsLoggedInrelayprovider:!0,__relay_internal__pv__BarcelonaIsInlineReelsEnabledrelayprovider:!1,__relay_internal__pv__BarcelonaOptionalCookiesEnabledrelayprovider:!0,__relay_internal__pv__BarcelonaShowReshareCountrelayprovider:!1,__relay_internal__pv__BarcelonaQuotedPostUFIEnabledrelayprovider:!1,__relay_internal__pv__BarcelonaIsCrawlerrelayprovider:!1,__relay_internal__pv__BarcelonaShouldShowFediverseM075Featuresrelayprovider:!0},doc_id:a[_].first.doc_id});t=l(e)}console.log(t);const{findDataObject:d}=await e((async()=>{const{findDataObject:e}=await import("./MyApp-DW5WH4Ub.js").then((e=>e.aX));return{findDataObject:e}}),__vite__mapDeps([0,1,2]),import.meta.url),{edges:s=[],page_info:u={}}=d(t);return s.map((e=>i(e,u)))}export{r as ThreadsPostType,i as extractThreadsPostData,_ as getThreadsPosts};
