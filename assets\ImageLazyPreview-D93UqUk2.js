import{aT as e,r,b0 as t}from"./index-Cak6rALw.js";import{getCacheByKey as o,setCacheByKey as a}from"./useCacheState-CAnxkewm.js";import{u as n}from"./MyApp-DW5WH4Ub.js";import{I as i}from"./index-CDSnY0KE.js";import"./EyeOutlined-CNKPohhw.js";import"./addEventListener-C4tIKh7N.js";function s(s){const{cacheId:c,getPreview:l,renderPreview:d,...m}=s,{message:p}=e.useApp(),{ti:f}=n(),u=m.src||"",y="ImageLazyPreview."+(c||u),[g,v]=r.useState(!1),[w,j]=r.useState(o(y,"")),L=w||u;return t.jsx(i,{...m,preview:"function"==typeof d?d(L,g):{src:L},onClick:e=>{var r;null==(r=m.onClick)||r.call(m,e),(async()=>{if(!w)try{v(!0),p.loading({duration:0,key:y,content:f({en:"Loading...",vi:"Đang tải..."})});let e=performance.now();const r=await l();let t=performance.now();console.log("getPreview",t-e),r&&r!==u&&(j(r),a(y,r)),p.destroy(y)}catch(e){console.error(e),p.error({key:y,content:f({en:"Load failed: ",vi:"Load lỗi: "})+e.message})}finally{v(!1)}})()}})}export{s as default};
