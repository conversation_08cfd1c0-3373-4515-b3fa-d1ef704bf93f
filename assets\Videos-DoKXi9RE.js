const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./VideoViewer-DSZVlhxl.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./videos-D2LbKcXH.js","./MyApp-DW5WH4Ub.js"])))=>i.map(i=>d[i]);
import{r as e,b0 as i,b1 as o,b5 as t,aN as r}from"./index-Cak6rALw.js";import{z as s,B as n,aQ as a,S as l,l as d,h as m,I as p}from"./MyApp-DW5WH4Ub.js";import u from"./Collection-BYcChfHL.js";import{getGroupVideo as c,getUserVideo as j,getVideoInfo as h}from"./videos-D2LbKcXH.js";import{L as x}from"./index-jrOnBmdW.js";import{I as g}from"./index-CDSnY0KE.js";import"./index-ByRdMNW-.js";import"./useCacheState-CAnxkewm.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-PbLYNhdQ.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CC5caqt_.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./addEventListener-C4tIKh7N.js";const v=o((()=>r((()=>import("./VideoViewer-DSZVlhxl.js")),__vite__mapDeps([0,1,2,3,4]),import.meta.url)),{fallback:p});function b({target:o}){const r=e.useCallback((async(e=[],i)=>{var t;if(!(null==o?void 0:o.id)||!(null==o?void 0:o.type))return;i=i||(null==(t=null==e?void 0:e[(null==e?void 0:e.length)-1])?void 0:t.cursor)||"";return((null==o?void 0:o.type)===s.Group?await c({id:null==o?void 0:o.id,cursor:i}):await j({id:null==o?void 0:o.id,cursor:i})).videos}),[o]),p=e.useCallback((async e=>{if(!e.source){const i=await h(e.id);e.source=i.source}return{url:e.source,name:e.id+".mp4"}}),[]),b=e.useCallback((e=>i.jsx(x.Item,{className:"show-on-hover-trigger",children:i.jsxs(n.Ribbon,{text:e.length?a(e.length):null,style:{opacity:e.length?1:0},children:[i.jsx(g,{src:e.picture,width:200,height:200,style:{objectFit:"cover",borderRadius:10},preview:{destroyOnClose:!0,imageRender:()=>i.jsx(v,{info:e,style:{maxWidth:"90vw",maxHeight:"90vh"}}),toolbarRender:()=>null}}),e.description&&i.jsx(l,{direction:"vertical",size:1,style:{position:"absolute",bottom:0,left:0,padding:5,width:"100%",background:"linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 100%)",pointerEvents:"none"},children:d(e.description,100)}),i.jsx(t,{type:"default",icon:i.jsx("i",{className:"fa-solid fa-up-right-from-square"}),style:{position:"absolute",bottom:10,right:10},className:"show-on-hover-item",target:"_blank",href:m(e.id)})]})})),[]);return i.jsx(u,{collectionName:(null==o?void 0:o.name)+" - Videos",fetchNext:r,renderItem:b,downloadItem:p,getItemCursor:e=>e.cursor,rowKey:e=>e.id})}export{b as default};
