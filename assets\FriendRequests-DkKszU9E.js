import{r as e,b0 as n,b5 as i,bi as t}from"./index-Cak6rALw.js";import{u as s,d as r,t as c,v as a,s as o,S as l,i as d,f as u,e as m,b as h,T as g,h as p}from"./MyApp-DW5WH4Ub.js";import x from"./MyTable-DnjDmN7z.js";import{F as f,g as j,a as k,d as y,b as v,c as T}from"./friendRequests-DyD2rqMz.js";import C from"./useCacheState-CAnxkewm.js";import{u as E}from"./useAction-uz-MrZwJ.js";import{u as q}from"./useDevMode-yj0U7_8D.js";import{S as I}from"./Screen-Buq7HJpK.js";import{A as b}from"./index-DdM4T8-Q.js";import{I as N}from"./index-CDSnY0KE.js";import{R}from"./Table-BO80-bCE.js";import"./index-ByRdMNW-.js";import"./index-BzMPigdO.js";import"./DownOutlined-B-JcS-29.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./useVIP-D5FAMGQQ.js";import"./sweetalert2.esm.all-BZxvatOx.js";import"./getIds-DAzc-wzf.js";import"./index-IxNfZAD-.js";import"./Dropdown-BcL-JHCf.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-CC5caqt_.js";import"./col-CzA09p0P.js";import"./index-PbLYNhdQ.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./index-D6X7PZwe.js";import"./index-Dg3cFar0.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./Pagination-2X6SDgot.js";const F={[f.INCOMING_REQUEST]:{vi:"Đã nhận",en:"Incoming",color:"default",icon:"fa-solid fa-arrow-turn-down"},[f.OUTGOING_REQUEST]:{vi:"Đã gửi",en:"Outgoing",color:"default",icon:"fa-solid fa-arrow-turn-up"},[f.DECLINED]:{vi:"Đã từ chối",en:"Declined",color:"error",icon:"fa-solid fa-xmark"},[f.ACCEPTED]:{vi:"Đã chấp nhận",en:"Accepted",color:"success",icon:"fa-solid fa-check"},[f.CANCELED]:{vi:"Đã huỷ",en:"Canceled",color:"error",icon:"fa-solid fa-xmark"}};function S(){const{ti:S}=s(),{message:D,notification:O}=r(),{onClickAction:A,onClickBulkActions:w}=E(),{devMode:G}=q(),[U,_]=C("FriendRequestType",f.INCOMING_REQUEST),[L,M]=C("FriendRequests.incoming",[]),[Q,$]=C("FriendRequests.outgoing",[]),[P,B]=C("FriendRequests.finished",!1),[V,z]=C("FriendRequests.loading",!1),[W,H]=C("FriendRequests.recordLoading",{});e.useEffect((()=>{P||K(!1)}),[U,P]);const J=(e,n)=>H((i=>({...i,[e]:n}))),K=async(e=!1)=>{const n="FriendRequests:onReload";c(n);const i=[[f.INCOMING_REQUEST,j,M,L],[f.OUTGOING_REQUEST,k,$,Q]].map((async([i,t,s,r])=>{var c;const o=F[i],l=n+i;try{const n=e?[]:r,i=n[n.length-1];let d=n.length,u=e?"":null==i?void 0:i.cursor;for(;;){D.loading({key:l,content:S({en:`Loading ${o.en} friend requests...`,vi:`Đang tải yêu cầu kết bạn ${o.vi}...`})+d,duration:0});const e=await t(u),i=new Set(n.map((e=>e.id))),r=e.filter((e=>!i.has(e.id)));if(!(null==r?void 0:r.length))break;n.push(...r),s([...n]),d=n.length,u=null==(c=n[n.length-1])?void 0:c.cursor,await a(500)}D.success({key:l,content:S({en:"Loaded friend requests "+o.en+": ",vi:"Tải xong yêu cầu kết bạn "+o.vi+": "})+d})}catch(d){O.open({type:"error",message:S({en:"Failed to load friend requests "+o.en+": ",vi:"Lỗi tải yêu cầu kết bạn "+o.vi+": "}),description:d.message})}}));z(!0),B(!1),await Promise.all(i),z(!1),B(!0)},X=(e,n=!1)=>(J(e.id,!0),A({key:"FriendRequests:onClickDeclineFriendRequest",id:e.id,record:e,loadingText:e=>S({en:"Declining friend request...",vi:"Đang từ chối yêu cầu kết bạn..."})+" "+e.name,successText:e=>S({en:"Declined friend request ",vi:"Từ chối yêu cầu thành công "})+" "+e.name,failedText:e=>S({en:"Failed to decline friend request: ",vi:"Lỗi từ chối yêu cầu kết bạn: "})+" "+e.name,actionFn:()=>G?a(1e3):y(e.id),onSuccess:()=>{M((n=>t(n,(n=>{const i=n.findIndex((n=>n.id===e.id));return i>=0&&(n[i].type=f.DECLINED),n}))))},finallyFn(){J(e.id,!1)},needConfirm:n,confirmProps:{title:S({en:"Decline friend request?",vi:"Từ chối yêu cầu kết bạn?"}),text:e.name}})),Y=(e,n=!1)=>(J(e.id,!0),A({key:"FriendRequests:onClickAcceptFriendRequest",id:e.id,record:e,loadingText:e=>S({en:"Accepting friend request...",vi:"Đang đồng ý yêu cầu kết bạn..."})+" "+e.name,successText:e=>S({en:"Accepted friend request ",vi:"Đã đồng ý yêu cầu kết bạn "})+" "+e.name,failedText:e=>S({en:"Failed to accept friend request: ",vi:"Lỗi đồng ý yêu cầu kết bạn: "})+" "+e.name,actionFn:()=>G?a(1e3):v(e.id),onSuccess:()=>{M((n=>t(n,(n=>{const i=n.findIndex((n=>n.id===e.id));return i>=0&&(n[i].type=f.ACCEPTED),n}))))},finallyFn(){J(e.id,!1)},needConfirm:n,confirmProps:{title:S({en:"Accept friend request?",vi:"Đồng ý yêu cầu kết bạn?"}),text:e.name}})),Z=(e,n=!1)=>(J(e.id,!0),A({key:"FriendRequests:onClickCancelOutgoingRequest",id:e.id,record:e,loadingText:e=>S({en:"Canceling friend request...",vi:"Đang thu hồi yêu cầu kết bạn..."})+" "+e.name,successText:e=>S({en:"canceled friend request ",vi:"Đã thu hồi yêu cầu kết bạn "})+" "+e.name,failedText:e=>S({en:"Failed to cancel friend request: ",vi:"Lỗi thu hồi yêu cầu kết bạn: "})+" "+e.name,actionFn:()=>G?a(1e3):T(e.id),onSuccess:()=>{$((n=>t(n,(n=>{const i=n.findIndex((n=>n.id===e.id));return i>=0&&(n[i].type=f.CANCELED),n}))))},finallyFn(){J(e.id,!1)},needConfirm:n,confirmProps:{title:S({en:"Cancel friend request?",vi:"Thu hồi yêu cầu kết bạn?"}),text:e.name}})),ee=[{title:"#",key:"recent",dataIndex:"recent",render:(e,n,i)=>n.recent+1,sorter:(e,n)=>e.recent-n.recent,width:50},{title:S({en:"Name",vi:"Tên"}),key:"name",dataIndex:"name",sorter:(e,n)=>(e.name||"").localeCompare(n.name||""),render:(e,i,t)=>n.jsxs(l,{align:"center",style:{maxWidth:300},children:[n.jsx(b,{shape:"square",src:n.jsx(N,{src:d(i.id),fallback:i.avatar}),size:50}),n.jsxs(l,{direction:"vertical",style:{marginLeft:"10px"},size:0,children:[n.jsx("a",{href:i.url,target:"_blank",children:n.jsx("b",{children:i.name})}),n.jsx("span",{style:{opacity:.5},children:i.id})]})]})},{title:S({en:"Description",vi:"Mô tả"}),key:"desc",dataIndex:"desc",sorter:(e,n)=>(e.desc||"").localeCompare(n.desc||""),render:(e,i,t)=>n.jsx("p",{style:{maxWidth:150,minWidth:100},children:i.desc}),width:"auto"},{title:S({en:"Time",vi:"Thời gian"}),key:"time",dataIndex:"time",sorter:(e,n)=>e.time-n.time,render:(e,i,t)=>i.time?n.jsxs(l,{direction:"vertical",children:[m(new Date(i.time).getTime()),n.jsx("span",{style:{opacity:.5},children:u(i.time)})]}):"-",onSearch:(e,n)=>o(e,u(n)),width:180},{title:S({en:"Status",vi:"Trạng thái"}),key:"type",dataIndex:"type",filters:(U===f.INCOMING_REQUEST?[f.INCOMING_REQUEST,f.ACCEPTED,f.DECLINED]:[f.OUTGOING_REQUEST,f.CANCELED]).map((e=>{const n=F[e],i=[...L,...Q].filter((n=>n.type===e)).length;return{text:S(n)+` (${i})`,value:e}})),onFilter:(e,n)=>n.type===e,render:(e,i,t)=>{const s=F[i.type];return n.jsx(h,{color:null==s?void 0:s.color,children:S(s)})},align:"right"},{title:S({en:"Action",vi:"Hành động"}),key:"action",dataIndex:"action",render:(e,t,s)=>t.type===f.INCOMING_REQUEST?n.jsxs(l.Compact,{children:[n.jsx(g,{title:S({en:"Accept",vi:"Đồng ý"}),children:n.jsx(i,{loading:W[t.id],onClick:()=>Y(t,!0),icon:n.jsx("i",{className:"fa-solid fa-check"})})}),n.jsx(g,{title:S({en:"Decline",vi:"Từ chối"}),children:n.jsx(i,{danger:!0,loading:W[t.id],onClick:()=>X(t,!0),icon:n.jsx("i",{className:"fa-solid fa-trash-can"})})})]}):t.type===f.OUTGOING_REQUEST?n.jsx(l.Compact,{children:n.jsx(g,{title:S({en:"Cancel",vi:"Thu hồi"}),children:n.jsx(i,{danger:!0,loading:W[t.id],onClick:()=>Z(t,!0),icon:n.jsx("i",{className:"fa-solid fa-trash-can"})})})}):null,align:"right",width:100}];return n.jsxs(I,{title:S({en:"Friend Requests",vi:"Lời mời kết bạn"}),children:[n.jsx(R.Group,{defaultValue:U,buttonStyle:"solid",style:{marginBottom:10},children:[[f.INCOMING_REQUEST,L],[f.OUTGOING_REQUEST,Q]].map((([e,i])=>{const t=i.length;return n.jsxs(R.Button,{value:e,onClick:()=>{_(e)},children:[n.jsx("i",{className:F[e].icon})," ",S(F[e])," ",t]},e)}))}),n.jsx(x,{data:(U===f.INCOMING_REQUEST?L:Q).map(((e,n)=>({...e,recent:n}))),columns:ee,renderTitle:e=>{const t=(null==e?void 0:e.length)?[...e]:[...U===f.INCOMING_REQUEST?L:Q],s=t.filter((e=>e.type===f.INCOMING_REQUEST)),r=t.filter((e=>e.type===f.OUTGOING_REQUEST));return n.jsxs(n.Fragment,{children:[n.jsx(i,{type:"primary",icon:n.jsx("i",{className:"fa-solid fa-rotate-right"}),loading:V,onClick:()=>K(!0),children:S({en:"Reload",vi:"Tải lại"})}),U===f.INCOMING_REQUEST?n.jsxs(l.Compact,{children:[n.jsx(g,{title:S({en:`Accept ${s.length} selected incoming requests`,vi:`Đồng ý ${s.length} lời mời đang chọn`}),children:n.jsx(i,{disabled:s.length<=0,icon:n.jsx("i",{className:"fa-solid fa-check"}),onClick:()=>{return w({data:e=s,key:"FriendRequests:onClickAcceptSelectedIncomingFriendRequests",actionFn:Y,loadingText:(e,n,i)=>S({en:"Accepting incoming requests...",vi:"Đang đồng ý lời mời..."}),successText:(e,n)=>S({en:"Accepted incoming requests done: ",vi:"Đồng ý lời mời xong: "}),successDescItem:e=>n.jsx("a",{target:"_blank",href:p(e.id),children:e.name}),confirmProps:{title:S({en:`Accept ${e.length} incoming requests`,vi:`Đồng ý ${e.length} lời mời kết bạn`}),text:S({en:"Are your sure want to bulk accept?",vi:"Bạn có chãc muốn đồng ý hàng loạt?"})}});var e},children:s.length})}),n.jsx(g,{title:S({en:`Decline ${s.length} selected incoming requests`,vi:`Từ chối ${s.length} lời mời đang chọn`}),children:n.jsx(i,{danger:!0,disabled:s.length<=0,icon:n.jsx("i",{className:"fa-regular fa-trash-can"}),onClick:()=>{return w({data:e=s,key:"FriendRequests:onClickDeclineSelectedIncomingFriendRequests",actionFn:X,loadingText:(e,n,i)=>S({en:"Declining incoming requests...",vi:"Đang từ chối lời mời..."}),successText:(e,n)=>S({en:"Declined friend requests",vi:"Từ chối lời mời kết bạn xong"}),successDescItem:e=>n.jsx("a",{target:"_blank",href:p(e.id),children:e.name}),confirmProps:{title:S({en:`Decline ${e.length} selected incoming requests`,vi:`Từ chối ${e.length} lời mời đang chọn`}),text:S({en:"Are your sure want to bulk decline?",vi:"Bản có chãc muốn từ chối hàng loạt?"})}});var e},children:s.length})})]}):n.jsx(n.Fragment,{children:n.jsx(g,{title:S({en:`Cancel ${r.length} selected outgoing requests`,vi:`Thu hồi ${r.length} yêu cầu đang chọn`}),children:n.jsx(i,{danger:!0,disabled:r.length<=0,icon:n.jsx("i",{className:"fa-regular fa-trash-can"}),onClick:()=>{return w({data:e=r,key:"FriendRequests:onClickCancelSelectedOutgoingFriendRequests",actionFn:Z,loadingText:(e,n,i)=>S({en:"Canceling outgoing requests...",vi:"Đang thu hồi yêu cầu..."}),successText:(e,n)=>S({en:"Canceled outgoing requests",vi:"Thu hồi yêu cầu kết bạn xong"}),successDescItem:e=>n.jsx("a",{target:"_blank",href:p(e.id),children:e.name}),confirmProps:{title:S({en:`Cancel ${e.length} outgoing requests`,vi:`Thu hồi ${e.length} yêu cầu kết bạn`}),text:S({en:"Are your sure want to bulk cancel?",vi:"Bạn có chãc muốn thu hồi hàng loạt?"})}});var e},children:r.length})})}),n.jsx(g,{title:S({en:"View on Facebook",vi:"Xem trên Facebook"}),children:n.jsx(i,{icon:n.jsx("i",{className:"fa-solid fa-external-link"}),onClick:()=>window.open("https://www.facebook.com/friends/requests","_blank")})})]})},size:"small",searchable:!0,selectable:!0,keyExtractor:e=>e.id})]})}export{S as default};
