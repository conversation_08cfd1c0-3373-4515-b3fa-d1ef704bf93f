import{bj as e,aN as n}from"./index-Cak6rALw.js";import{y as a,x as t,i,v as r,j as o}from"./MyApp-DW5WH4Ub.js";async function l({threadId:e="",progressCallback:n,checkStopFn:a}){var t;const i=[],o=await u({threadId:e});if(o.length)for(i.push(...o),null==n||n(i);;)try{const o=null==(t=i[0])?void 0:t.id,l=await m({threadId:e,msgId:o,direction:"up",limit:200});if(l.pop(),!(null==l?void 0:l.length)||(null==a?void 0:a()))break;i.unshift(...l),null==n||n(i),await r(500)}catch(l){console.error(l);break}return i}async function s(){return(await Promise.all([a("viewer(){message_threads{count,nodes{customization_info{emoji,outgoing_bubble_color,participant_customizations{participant_id,nickname}},all_participants{nodes{messaging_actor{name,id,profile_picture}}},thread_type,name,messages_count,image,id}}}").then((e=>t(e||"{}"))),a({queries:{o0:{doc_id:"1475048592613093",query_params:{limit:500,before:Date.now(),tags:["ARCHIVED"],includeDeliveryReceipts:!0,includeSeqID:!1}}}},"https://www.facebook.com/api/graphqlbatch/").then((e=>{var n,a,i;return null==(i=null==(a=t((null==(n=null==e?void 0:e.split("\n"))?void 0:n[0])||"{}"))?void 0:a.o0)?void 0:i.data}))])).map((e=>{var n,a;return null==(a=null==(n=null==e?void 0:e.viewer)?void 0:n.message_threads)?void 0:a.nodes})).filter(Boolean).flat().map(((e,n)=>({...e,recent:n}))).map((e=>{var n,a,t,r,o,l;let s=e.all_participants.nodes.map((e=>{var n,a,t;return{id:e.messaging_actor.id,name:e.messaging_actor.name,avatar:(null==(n=e.messaging_actor.profile_picture)?void 0:n.uri)||(null==(t=null==(a=e.messaging_actor.avatar)?void 0:a.big_image_src)?void 0:t.uri)||i(e.messaging_actor.id)}})),d={recent:e.recent,type:e.thread_type,id:(null==(n=e.thread_key)?void 0:n.other_user_id)||(null==(a=e.thread_key)?void 0:a.thread_fbid)||(e.id?null==(r=null==(t=atob(e.id))?void 0:t.split(":"))?void 0:r[1]:""),count:e.messages_count,name:e.name||(null==(o=s[0])?void 0:o.name)||"-no data-",participants:s,image:null==(l=e.image)?void 0:l.uri};return{...d,url:`https://www.facebook.com/messages/t/${d.id}`,isGroup:"GROUP"===d.type||"COMMUNITY_GROUP"===d.type}}))}async function d({threadId:n,startTime:a=e,endTime:t=Date.now(),progress:i}){var r;let o=1e3*Math.round((a+t)/2/1e3);null==i||i(o);let l=await u({threadId:n,time:o,limit:1});if(Math.abs(t-a)<=1e3){for(;;)try{const e=await m({threadId:n,msgId:null==(r=null==l?void 0:l[0])?void 0:r.id,direction:"up"});if(!((null==e?void 0:e.length)>1))break;l=e}catch(s){console.error(s);break}return l}return(null==l?void 0:l.length)?await d({threadId:n,startTime:a,endTime:o-1,progress:i}):await d({threadId:n,startTime:o+1,endTime:t,progress:i})}async function m({threadId:e,msgId:i,direction:r="down",limit:o=50}){var l,s;try{const n=await a({other_user_fbid:e,message_id:i,direction:r,limit:o,__a:1},"https://www.facebook.com/ajax/mercury/search_context.php"),d=t(n.substr(9));if(null==d?void 0:d.errorSummary)throw new Error(null==d?void 0:d.errorSummary);return(null==(s=null==(l=null==d?void 0:d.payload)?void 0:l.graphql_payload)?void 0:s.map(c))||[]}catch(d){return(await n((async()=>{const{default:e}=await import("./sweetalert2.esm.all-BZxvatOx.js");return{default:e}}),[],import.meta.url)).default.fire({icon:"error",title:"Error while fetching messages",text:d.message}),[]}}async function u({threadId:e,time:n=null,limit:i=50}){var r;try{const o=await a({queries:{o0:{doc_id:"1526314457427586",query_params:{id:e,message_limit:i,load_messages:1,load_read_receipts:!0,before:n}}}},"https://www.facebook.com/api/graphqlbatch/"),l=t(o.split("\n")[0]);if(l.o0.data.message_thread)return(null==(r=l.o0.data.message_thread.messages)?void 0:r.nodes.map(c))||[]}catch(o){console.error(o.message)}return[]}function c(e){var n,a,t,i,r,o,l,s,d;return{id:null==e?void 0:e.message_id,sender:null==(n=null==e?void 0:e.message_sender)?void 0:n.id,time:Number(null==e?void 0:e.timestamp_precise),text:(null==(a=null==e?void 0:e.message)?void 0:a.text)||(null==(i=null==(t=null==e?void 0:e.extensible_attachment)?void 0:t.story_attachment)?void 0:i.url)||(null==(l=null==(o=null==(r=null==e?void 0:e.extensible_attachment)?void 0:r.story_attachment)?void 0:o.description)?void 0:l.text)||(null==e?void 0:e.snippet),sticker:null==(s=null==e?void 0:e.sticker)?void 0:s.url,replied_to_message:null==e?void 0:e.replied_to_message,attachments:null==(d=null==e?void 0:e.blob_attachments)?void 0:d.map((e=>{var n,a,t,i,r,o,l,s;return"MessageVideo"===e.__typename?{type:"video",uri:e.playable_url,thumbnail:(null==(n=e.large_image)?void 0:n.uri)||(null==(a=e.inbox_image)?void 0:a.uri)||(null==(t=e.chat_image)?void 0:t.uri)}:"MessageImage"===e.__typename?{type:"image",uri:(null==(i=e.large_preview)?void 0:i.uri)||(null==(r=e.preview)?void 0:r.uri)||(null==(o=e.thumbnail)?void 0:o.uri)}:"MessageFile"===e.__typename?{type:"file",uri:e.url,filename:e.filename}:"MessageAnimatedImage"===e.__typename?{type:"gif",uri:(null==(l=null==e?void 0:e.animated_image)?void 0:l.uri)||(null==(s=null==e?void 0:e.preview_image)?void 0:s.uri),filename:e.filename}:null})).filter(Boolean)}}async function g(e=""){var n,a,t,i,r,l;return!!(null==(l=null==(r=null==(i=null==(t=null==(a=null==(n=(await o(e)).raw)?void 0:n.data)?void 0:a.node)?void 0:t.comet_hovercard_renderer)?void 0:i.user)?void 0:r.primaryActions)?void 0:l.find((e=>"MESSAGE"==(null==e?void 0:e.profile_action_type))))}function p({msgs:e,threadId:n="",threadName:a="",myUid:t=""}){return`<!DOCTYPE html>\n<html lang="en">\n\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>${a}</title>\n\n    <style>\n        * {\n            margin: 0;\n            box-sizing: border-box;\n        }\n\n        h1 {\n            text-align: center;\n        }\n\n        .chat {\n            --rad: 20px;\n            --rad-sm: 3px;\n            font: 16px/1.5 sans-serif;\n            display: flex;\n            flex-direction: column;\n            padding: 20px;\n            max-width: 700px;\n            margin: auto;\n        }\n\n        .msg {\n            position: relative;\n            max-width: 75%;\n            padding: 7px 15px;\n            margin-bottom: 20px;\n        }\n\n        .msg.sent {\n            border-radius: var(--rad) var(--rad-sm) var(--rad-sm) var(--rad);\n            background: #42a5f5;\n            color: #fff;\n            margin-left: auto;\n        }\n\n        .msg.rcvd {\n            border-radius: var(--rad-sm) var(--rad) var(--rad) var(--rad-sm);\n            background: #f1f1f1;\n            color: #555;\n            margin-right: auto;\n        }\n\n        .msg.sent:first-child,\n        .msg.rcvd+.msg.sent {\n            border-top-right-radius: var(--rad);\n        }\n\n        .msg.rcvd:first-child,\n        .msg.sent+.msg.rcvd {\n            border-top-left-radius: var(--rad);\n        }\n\n        /* time */\n        .msg::before {\n            content: attr(data-time);\n            font-size: 0.8rem;\n            position: absolute;\n            bottom: 100%;\n            color: #888;\n            white-space: nowrap;\n        }\n\n        .msg.sent::before {\n            right: 5px;\n        }\n\n        .msg.rcvd::before {\n            left: 5px;\n        }\n\n        .msg img,\n        .msg video {\n            max-width: 300px;\n            max-height: 300px;\n        }\n\n        .msg .avatar img {\n            width: 35px;\n            height: 35px;\n            border-radius: 50%;\n        }\n    </style>\n</head>\n\n<body>\n    <h1><a target="_blank" href="https://www.facebook.com/messages/t/${n}">${a}</a></h1>\n    <div id="root" class="chat"></div>\n    <script>\n        const myUid = '${t}';\n        const data = ${JSON.stringify(e)};\n\n        let html = ''\n        for (let d of data) {\n            const isMy = d.sender === myUid;\n\n            let msg = ''\n            if (!isMy)\n                msg += '<a class="avatar" href="https://fb.com/' + d.sender + '"><img src="' + avatar(d.sender) +\n                '"/></a>';\n\n            if (d.text) msg += '<p>' + d.text + '</p>'\n            if (d.sticker) msg += '<img src="' + d.sticker + '"/>'\n            msg += (d.attachments || []).map(a => {\n                if (a.type === 'video')\n                    return '<video loop controls src="' + a.uri + '"/>';\n                if (a.type === 'image' || a.type === 'gif')\n                    return '<img src="' + a.uri + '" />';\n                if (a.type === 'file')\n                    return '<a href="' + a.uri + '" target="_blank" rel="noreferrer">File: ' + a.filename +\n                        '</a>';\n                return null;\n            }).filter(Boolean).join('');\n\n            html += '<div data-time="' + new Date(d.time).toLocaleString() + '" class="msg ' +\n                (isMy ? 'sent' : 'rcvd') +\n                '">' + msg + '</div>'\n        }\n        const root = document.getElementById('root')\n        root.innerHTML = html;\n\n        function avatar(uid) {\n            return 'https://graph.facebook.com/' + uid +\n                '/picture?height=30&access_token=6628568379%7Cc1e620fa708a1d5696fb991c1bde5662';\n        }\n    <\/script>\n</body>\n\n</html>\n`}export{l as a,u as b,m as c,g as d,d as f,s as g,p as s};
