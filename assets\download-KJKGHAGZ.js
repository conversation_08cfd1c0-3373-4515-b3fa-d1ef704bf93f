function e(e,n){const o=document.createElement("a");o.href=e,o.download=n,o.target="_blank",o.rel="noopener noreferrer",o.referrerPolicy="no-referrer-when-downgrade",document.body.appendChild(o),o.click(),document.body.removeChild(o)}async function n(n,o,r="application/json"){const t=new Blob([n],{type:r}),c=URL.createObjectURL(t);e(c,o),URL.revokeObjectURL(c)}export{e as download,n as downloadData};
