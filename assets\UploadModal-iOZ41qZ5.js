import{r as e,b0 as t}from"./index-Cak6rALw.js";import{_ as o}from"./index-D3qvoGmV.js";import{M as n}from"./index-Bx3o6rwA.js";import{U as s}from"./index-BVNdrzmU.js";import"./PurePanel-BvHjNOhQ.js";import"./useBreakpoint-CPcdMRfj.js";import"./MyApp-DW5WH4Ub.js";import"./EyeOutlined-CNKPohhw.js";import"./progress-ApOaEpgr.js";const{Dragger:r}=s;function a({title:o="Upload file",text:s="Click or drag file to this area to upload",hint:a="Support for a single or bulk upload. Strictly prohibited from uploading company data or other banned files.",renderButton:i=({showModal:e})=>{},onSubmit:l=e=>{},accept:p=".json, .csv",keepFileList:d=!1}){const[u,c]=e.useState(!1),[m,f]=e.useState(!1),[x,j]=e.useState(null),[g,h]=e.useState([]);return t.jsxs(t.Fragment,{children:[null==i?void 0:i({showModal:()=>{c(!0)}}),t.jsx(n,{title:o,open:u,onOk:()=>{l(x),c(!1),d||(h([]),j(null))},confirmLoading:m,onCancel:()=>{c(!1)},closeIcon:null,children:t.jsxs(r,{accept:p,beforeUpload:e=>{f(!0),h([e]);const t=new FileReader;return t.onload=e=>{var t;f(!1),j((null==(t=e.target)?void 0:t.result)??null)},t.readAsText(e),!1},maxCount:1,fileList:g,onRemove:()=>h([]),children:[t.jsx("p",{className:"ant-upload-drag-icon",children:t.jsx("i",{className:"fa-solid fa-inbox fa-3x"})}),t.jsx("p",{className:"ant-upload-text",children:s}),t.jsx("p",{className:"ant-upload-hint",children:a})]})})]})}a.propTypes={title:o.string,text:o.string,hint:o.string,renderButton:o.func,onSubmit:o.func,accept:o.string,keepFileList:o.bool};export{a as default};
