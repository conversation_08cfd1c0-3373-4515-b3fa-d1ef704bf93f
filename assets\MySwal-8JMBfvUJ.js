import t from"./sweetalert2.esm.all-BZxvatOx.js";import{$ as e,be as n}from"./index-Cak6rALw.js";const o=[{key:"title",getter:t=>t.getTitle()},{key:"html",getter:t=>t.getHtmlContainer()},{key:"confirmButtonText",getter:t=>t.getConfirmButton()},{key:"denyButtonText",getter:t=>t.getDenyButton()},{key:"cancelButtonText",getter:t=>t.getCancelButton()},{key:"footer",getter:t=>t.getFooter()},{key:"closeButtonHtml",getter:t=>t.getCloseButton()},{key:"iconHtml",getter:t=>t.getIconContent()},{key:"loaderHtml",getter:t=>t.getLoader()}],s=()=>{};const r=function(t){function r(t){const n={},s={},r=o.map((t=>t.key));return Object.entries(t).forEach((t=>{let[o,i]=t;r.includes(o)&&e.isValidElement(i)?(n[o]=i,s[o]=" "):s[o]=i})),[n,s]}function i(e,s){Object.entries(s).forEach((s=>{let[r,i]=s;const a=o.find((t=>t.key===r)).getter(t),c=n.createRoot(a);c.render(i),e.__roots.push(c)}))}function a(t){t.__roots.forEach((t=>{t.unmount()})),t.__roots=[]}return class extends t{static argsToParams(n){if(e.isValidElement(n[0])||e.isValidElement(n[1])){const t={};return["title","html","icon"].forEach(((e,o)=>{void 0!==n[o]&&(t[e]=n[o])})),t}return t.argsToParams(n)}_main(t,e){this.__roots=[],this.__params=Object.assign({},e,t);const[n,o]=r(this.__params),c=o.willOpen||s,l=o.didOpen||s,m=o.didDestroy||s;return super._main(Object.assign({},o,{willOpen:t=>{i(this,n),c(t)},didOpen:t=>{setTimeout((()=>{l(t)}))},didDestroy:t=>{m(t),a(this)}}))}update(t){Object.assign(this.__params,t),a(this);const[e,n]=r(this.__params);super.update(n),i(this,e)}}}(t);export{r as default};
