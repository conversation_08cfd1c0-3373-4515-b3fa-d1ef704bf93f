import{ce as r,aX as n,aJ as t}from"./index-Cak6rALw.js";import{r as u}from"./_copyArray-_bDJu6nu.js";var e,o,f,i,a,c,v,s,l,h,m,p,d,g,y,j;function x(){if(i)return f;i=1;var r=function(){if(o)return e;o=1;var r=Math.floor,n=Math.random;return e=function(t,u){return t+r(n()*(u-t+1))}}();return f=function(n,t){var u=-1,e=n.length,o=e-1;for(t=void 0===t?e:t;++u<t;){var f=r(u,o),i=n[f];n[f]=n[u],n[u]=i}return n.length=t,n}}function A(){if(h)return l;h=1;var r=s?v:(s=1,v=function(r,n){for(var t=-1,u=null==r?0:r.length,e=Array(u);++t<u;)e[t]=n(r[t],t,r);return e});return l=function(n,t){return r(t,(function(r){return n[r]}))}}function M(){if(g)return d;g=1;var n=x(),t=function(){if(p)return m;p=1;var n=A(),t=r();return m=function(r){return null==r?[]:n(r,t(r))}}();return d=function(r){return n(t(r))}}const J=t(function(){if(j)return y;j=1;var r=function(){if(c)return a;c=1;var r=u(),n=x();return a=function(t){return n(r(t))}}(),t=M(),e=n();return y=function(n){return(e(n)?r:t)(n)}}());export{J as s};
