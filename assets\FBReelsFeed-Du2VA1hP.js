const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./VideoViewer-DSZVlhxl.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./videos-D2LbKcXH.js","./MyApp-DW5WH4Ub.js"])))=>i.map(i=>d[i]);
import{bc as e,r as i,b0 as s,b1 as o,b5 as r,bk as t,aN as a}from"./index-Cak6rALw.js";import{getReelsFeed as n}from"./reels-Bl7il3t1.js";import{u as l,t as d,A as c,S as m,aQ as p,e as j,T as x,h,c as u,l as g,I as f}from"./MyApp-DW5WH4Ub.js";import b from"./Collection-BYcChfHL.js";import{L as v}from"./index-jrOnBmdW.js";import{I as y}from"./index-CDSnY0KE.js";import{A as w}from"./index-DdM4T8-Q.js";import"./index-ByRdMNW-.js";import"./useCacheState-CAnxkewm.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-PbLYNhdQ.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CC5caqt_.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./addEventListener-C4tIKh7N.js";const k=o((()=>a((()=>import("./VideoViewer-DSZVlhxl.js")),__vite__mapDeps([0,1,2,3,4]),import.meta.url)),{fallback:f});function _(){const{ti:o}=l(),a=e();i.useEffect((()=>{d("FBReelsFeed:onLoad")}),[]);return s.jsx(b,{collectionName:"FBReelsFeed",fetchNext:async(e,i)=>{var s;return await n({cursor:i||(null==(s=null==e?void 0:e[e.length-1])?void 0:s.cursor)||""})},renderItem:(e,i)=>s.jsx(v.Item,{children:s.jsxs("div",{className:"show-on-hover-trigger",children:[s.jsxs("div",{style:{position:"relative"},children:[s.jsx(y,{src:e.video.thumbnail,style:{width:200,height:300,borderRadius:10,objectFit:"cover"},preview:{destroyOnClose:!0,toolbarRender:()=>null,imageRender:()=>s.jsx(k,{info:e.video})}}),s.jsxs("div",{style:{color:"#eee",background:"linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%)",position:"absolute",bottom:0,left:0,padding:"5px 10px",width:"100%",paddingTop:"50px",pointerEvents:"none",display:"flex",flexDirection:"column"},children:[s.jsxs(m,{children:[s.jsx("i",{className:"fa-solid fa-film"}),p(e.video.length/1e3)]}),s.jsxs(m,{children:[s.jsx("i",{className:"fa-regular fa-clock"}),j(parseInt(e.video.created_time))]})]}),s.jsxs(m,{className:"show-on-hover-item",direction:"horizontal",style:{position:"absolute",bottom:10,right:10},size:3,children:[s.jsx(x,{title:o({en:"Open in Bulk Downloader",vi:"Mở trong Tải hàng loạt"}),children:s.jsx(r,{icon:s.jsx("i",{className:"fa-solid fa-search"}),onClick:()=>{const i=!!e.owner.ig_username;a("/bulk-downloader",{state:{targetId:i?e.owner.ig_username:e.owner.id,platform:i?t.Instagram:t.Facebook}})}})}),s.jsx(r,{icon:s.jsx("i",{className:"fa-solid fa-up-right-from-square"}),target:"_blank",href:h(e.video.id)})]})]}),s.jsx("div",{style:{width:200,display:"flex",flexDirection:"column"},children:s.jsx(x,{title:e.translated_caption||e.caption,children:s.jsx(u.Text,{children:g(e.caption,80)})})}),s.jsxs("div",{style:{position:"absolute",top:5,left:5,right:5,display:"flex"},children:[s.jsxs("div",{style:{position:"relative",height:40},children:[s.jsx(w,{onClick:()=>window.open(e.owner.url,"_blank"),src:e.owner.avatar,size:40,style:{borderWidth:2,borderColor:"#0866FF",marginRight:3,cursor:"pointer"}}),e.owner.ig_username&&s.jsx("i",{className:"fa-brands fa-instagram fa-lg",style:{position:"absolute",bottom:3,right:3}})]}),s.jsx("div",{style:{display:"inline-block",background:"#1119",padding:"3px 5px",borderRadius:5,pointerEvents:"none",alignSelf:"center",flex:1},children:s.jsx(u.Text,{style:{color:"#eee",fontWeight:"bold"},children:e.owner.name||e.owner.ig_username})})]})]})}),downloadItem:async(e,i)=>({name:e.video.id+".mp4",url:e.video.source}),getItemCursor:e=>null==e?void 0:e.cursor,rowKey:e=>e.id,header:()=>s.jsx(c,{type:"info",message:s.jsxs(s.Fragment,{children:[s.jsx("i",{className:"fa fa-eye-slash",style:{color:"gray"}})," ",o({en:"Watch all Reels without being tracked by Facebook",vi:"Lướt Reels ẩn danh - Không bị Facebook theo dõi"})]})})})}export{_ as default};
