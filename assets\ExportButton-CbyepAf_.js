const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./file-download-B-gszDlL.js","./index-Cak6rALw.js","./index-DIX6R8z-.css"])))=>i.map(i=>d[i]);
import{aT as l,b0 as e,b5 as o,aN as i}from"./index-Cak6rALw.js";import{u as n,t as a}from"./MyApp-DW5WH4Ub.js";import{D as t}from"./index-Dg3cFar0.js";function r({data:r,children:s,options:d,title:p={en:"Export",vi:"Xuất"}}){const{message:c}=l.useApp(),{ti:u}=n(),m=d.map((({key:l,label:e})=>({key:l,label:e})));return e.jsx(t,{menu:{items:m,onClick:l=>(l=>{var e;if(!(null==r?void 0:r.length))return c.error(u({en:"No data to export",vi:"Không có dữ liệu"}));const o=d.find((e=>e.key==l));if("function"==typeof(null==o?void 0:o.onClick))a("onClickExport:"+l+"onClick"),null==o||o.onClick(r);else{const n=null==(e=null==o?void 0:o.prepareData)?void 0:e.call(o,r);(null==n?void 0:n.data)&&(a("onClickExport:"+l+":"+n.fileName),i((()=>import("./file-download-B-gszDlL.js").then((l=>l.f))),__vite__mapDeps([0,1,2]),import.meta.url).then((l=>l.default(n.data,n.fileName))))}})(l.key)},children:s||e.jsx(o,{type:"primary",icon:e.jsx("i",{className:"fa-solid fa-download"}),children:u(p)+" "+((null==r?void 0:r.length)||0)})})}export{r as E};
