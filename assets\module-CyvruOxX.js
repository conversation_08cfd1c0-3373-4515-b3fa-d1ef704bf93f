import{r as e,$ as r}from"./index-Cak6rALw.js";import{$ as t}from"./RecentStoryViewer-Bikh4z-C.js";import"./stories-DQ6hTlzb.js";import"./MyApp-DW5WH4Ub.js";import"./useCacheState-CAnxkewm.js";import"./index-CDSnY0KE.js";import"./EyeOutlined-CNKPohhw.js";import"./addEventListener-C4tIKh7N.js";import"./index-DdM4T8-Q.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-PbLYNhdQ.js";function s(s){const i=e.useRef(null),n=e.useRef(null);return n.current&&n.current.update(s),e.useEffect((()=>(n.current=new t({...s,ref:i}),()=>{n.current=null})),[]),r.createElement("div",{ref:i})}export{s as default};
