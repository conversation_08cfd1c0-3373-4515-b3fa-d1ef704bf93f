const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./Collection-BYcChfHL.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./index-ByRdMNW-.js","./MyApp-DW5WH4Ub.js","./useCacheState-CAnxkewm.js","./react-hotkeys-hook.esm-wjq6pdfC.js","./index-PbLYNhdQ.js","./index-QU7lSj_a.js","./index-CxAc8H5Q.js","./index-Dg3cFar0.js","./Dropdown-BcL-JHCf.js","./PurePanel-BvHjNOhQ.js","./move-ESLFEEsv.js","./index-Bg865k-U.js","./EyeOutlined-CNKPohhw.js","./SearchOutlined--mXbzQ68.js","./index-jrOnBmdW.js","./useBreakpoint-CPcdMRfj.js","./index-C5gzSBcY.js","./List-B6ZMXgC_.js","./index-SRy1SMeO.js","./DownOutlined-B-JcS-29.js","./row-BMfM-of4.js","./Pagination-2X6SDgot.js","./col-CzA09p0P.js","./index-CC5caqt_.js","./MediaSet-CUZEUMm6.js","./posts-Fn0UXLRN.js","./videos-D2LbKcXH.js","./index-CDSnY0KE.js","./addEventListener-C4tIKh7N.js","./VideoViewer-DSZVlhxl.js","./FacebookTextFormat-DUrR9MIJ.js","./reactions-CR2nCZPF.js","./download-KJKGHAGZ.js","./Table-BO80-bCE.js","./index-D6X7PZwe.js","./index-DdM4T8-Q.js","./index-BzMPigdO.js","./index-DRXkAFwH.js"])))=>i.map(i=>d[i]);
import{r as e,n as t,$ as n,m as a,a4 as o,d as r,o as i,cA as l,cB as s,T as c,cC as d,cD as u,cE as m,h as g,cF as h,cG as p,q as f,C as b,v,l as x,a6 as C,e as j,L as y,M as $,O as w,an as S,J as k,a9 as E,aa as O,D as N,R as _,aw as R,cH as I,cI as P,cJ as M,cK as H,cL as T,i as D,cM as z,cN as A,cO as L,aL as F,aj as W,ah as q,G as B,at as V,au as X,av as G,af as K,U,b0 as J,b5 as Y,b1 as Q,aN as Z}from"./index-Cak6rALw.js";import{a6 as ee,u as te,d as ne,S as ae,A as oe,h as re,c as ie,f as le,b as se,e as ce,i as de,g as ue,aQ as me,C as ge,I as he}from"./MyApp-DW5WH4Ub.js";import{getPostContent as pe}from"./posts-Fn0UXLRN.js";import fe from"./useCacheState-CAnxkewm.js";import{R as be}from"./reactions-CR2nCZPF.js";import{downloadData as ve}from"./download-KJKGHAGZ.js";import{F as xe}from"./Table-BO80-bCE.js";import{A as Ce}from"./index-DdM4T8-Q.js";import{a as je,I as ye}from"./index-Bg865k-U.js";import{R as $e}from"./row-BMfM-of4.js";import{I as we}from"./index-CDSnY0KE.js";import{L as Se}from"./index-jrOnBmdW.js";import{g as ke}from"./PurePanel-BvHjNOhQ.js";import{P as Ee}from"./index-PbLYNhdQ.js";import{D as Oe}from"./index-CC5caqt_.js";import{S as Ne}from"./index-C5gzSBcY.js";import{T as _e}from"./index-BzMPigdO.js";import{S as Re,U as Ie,a as Pe}from"./index-DRXkAFwH.js";function Me(n){var a=n.targetRef,o=n.containerRef,r=n.direction,i=n.onDragChange,l=n.onDragChangeComplete,s=n.calculate,c=n.color,d=n.disabledDrag,u=e.useState({x:0,y:0}),m=t(u,2),g=m[0],h=m[1],p=e.useRef(null),f=e.useRef(null);e.useEffect((function(){h(s())}),[c]),e.useEffect((function(){return function(){document.removeEventListener("mousemove",p.current),document.removeEventListener("mouseup",f.current),document.removeEventListener("touchmove",p.current),document.removeEventListener("touchend",f.current),p.current=null,f.current=null}}),[]);var b=function(e){var t=function(e){var t="touches"in e?e.touches[0]:e,n=document.documentElement.scrollLeft||document.body.scrollLeft||window.pageXOffset,a=document.documentElement.scrollTop||document.body.scrollTop||window.pageYOffset;return{pageX:t.pageX-n,pageY:t.pageY-a}}(e),n=t.pageX,l=t.pageY,s=o.current.getBoundingClientRect(),c=s.x,d=s.y,u=s.width,m=s.height,h=a.current.getBoundingClientRect(),p=h.width,f=h.height,b=p/2,v=f/2,x=Math.max(0,Math.min(n-c,u))-b,C=Math.max(0,Math.min(l-d,m))-v,j={x:x,y:"x"===r?g.y:C};if(0===p&&0===f||p!==f)return!1;null==i||i(j)},v=function(e){e.preventDefault(),b(e)},x=function(e){e.preventDefault(),document.removeEventListener("mousemove",p.current),document.removeEventListener("mouseup",f.current),document.removeEventListener("touchmove",p.current),document.removeEventListener("touchend",f.current),p.current=null,f.current=null,null==l||l()};return[g,function(e){document.removeEventListener("mousemove",p.current),document.removeEventListener("mouseup",f.current),d||(b(e),document.addEventListener("mousemove",v),document.addEventListener("mouseup",x),document.addEventListener("touchmove",v),document.addEventListener("touchend",x),p.current=v,f.current=x)}]}var He=function(e){var t=e.size,r=void 0===t?"default":t,i=e.color,l=e.prefixCls;return n.createElement("div",{className:a("".concat(l,"-handler"),o({},"".concat(l,"-handler-sm"),"small"===r)),style:{backgroundColor:i}})},Te=function(e){var t=e.children,a=e.style,o=e.prefixCls;return n.createElement("div",{className:"".concat(o,"-palette"),style:r({position:"relative"},a)},t)},De=e.forwardRef((function(e,t){var a=e.children,o=e.x,r=e.y;return n.createElement("div",{ref:t,style:{position:"absolute",left:"".concat(o,"%"),top:"".concat(r,"%"),zIndex:1,transform:"translate(-50%, -50%)"}},a)})),ze=function(a){var o=a.color,r=a.onChange,c=a.prefixCls,d=a.onChangeComplete,u=a.disabled,m=e.useRef(),g=e.useRef(),h=e.useRef(o),p=i((function(e){var t=l({offset:e,targetRef:g,containerRef:m,color:o});h.current=t,r(t)})),f=Me({color:o,containerRef:m,targetRef:g,calculate:function(){return s(o)},onDragChange:p,onDragChangeComplete:function(){return null==d?void 0:d(h.current)},disabledDrag:u}),b=t(f,2),v=b[0],x=b[1];return n.createElement("div",{ref:m,className:"".concat(c,"-select"),onMouseDown:x,onTouchStart:x},n.createElement(Te,{prefixCls:c},n.createElement(De,{x:v.x,y:v.y,ref:g},n.createElement(He,{color:o.toRgbString(),prefixCls:c})),n.createElement("div",{className:"".concat(c,"-saturation"),style:{backgroundColor:"hsl(".concat(o.toHsb().h,",100%, 50%)"),backgroundImage:"linear-gradient(0deg, #000, transparent),linear-gradient(90deg, #fff, hsla(0, 0%, 100%, 0))"}})))},Ae=function(t){var a=t.colors,o=t.children,r=t.direction,i=void 0===r?"to right":r,l=t.type,s=t.prefixCls,c=e.useMemo((function(){return a.map((function(e,t){var n=d(e);return"alpha"===l&&t===a.length-1&&(n=new u(n.setA(1))),n.toRgbString()})).join(",")}),[a,l]);return n.createElement("div",{className:"".concat(s,"-gradient"),style:{position:"absolute",inset:0,background:"linear-gradient(".concat(i,", ").concat(c,")")}},o)},Le=function(o){var r=o.prefixCls,c=o.colors,d=o.disabled,m=o.onChange,g=o.onChangeComplete,h=o.color,p=o.type,f=e.useRef(),b=e.useRef(),v=e.useRef(h),x=function(e){return"hue"===p?e.getHue():100*e.a},C=i((function(e){var t=l({offset:e,targetRef:b,containerRef:f,color:h,type:p});v.current=t,m(x(t))})),j=Me({color:h,targetRef:b,containerRef:f,calculate:function(){return s(h,p)},onDragChange:C,onDragChangeComplete:function(){g(x(v.current))},direction:"x",disabledDrag:d}),y=t(j,2),$=y[0],w=y[1],S=n.useMemo((function(){if("hue"===p){var e=h.toHsb();return e.s=1,e.b=1,e.a=1,new u(e)}return h}),[h,p]),k=n.useMemo((function(){return c.map((function(e){return"".concat(e.color," ").concat(e.percent,"%")}))}),[c]);return n.createElement("div",{ref:f,className:a("".concat(r,"-slider"),"".concat(r,"-slider-").concat(p)),onMouseDown:w,onTouchStart:w},n.createElement(Te,{prefixCls:r},n.createElement(De,{x:$.x,y:$.y,ref:b},n.createElement(He,{size:"small",color:S.toHexString(),prefixCls:r})),n.createElement(Ae,{colors:k,type:p,prefixCls:r})))};var Fe=[{color:"rgb(255, 0, 0)",percent:0},{color:"rgb(255, 255, 0)",percent:17},{color:"rgb(0, 255, 0)",percent:33},{color:"rgb(0, 255, 255)",percent:50},{color:"rgb(0, 0, 255)",percent:67},{color:"rgb(255, 0, 255)",percent:83},{color:"rgb(255, 0, 0)",percent:100}],We=e.forwardRef((function(r,i){var l=r.value,s=r.defaultValue,f=r.prefixCls,b=void 0===f?m:f,v=r.onChange,x=r.onChangeComplete,C=r.className,j=r.style,y=r.panelRender,$=r.disabledAlpha,w=void 0!==$&&$,S=r.disabled,k=void 0!==S&&S,E=function(t){return e.useMemo((function(){return[(t||{}).slider||Le]}),[t])}(r.components),O=t(E,1)[0],N=function(n,a){var o=c(n,{value:a}),r=t(o,2),i=r[0],l=r[1];return[e.useMemo((function(){return d(i)}),[i]),l]}(s||p,l),_=t(N,2),R=_[0],I=_[1],P=e.useMemo((function(){return R.setA(1).toRgbString()}),[R]),M=function(e,t){l||I(e),null==v||v(e,t)},H=function(e){return new u(R.setHue(e))},T=function(e){return new u(R.setA(e/100))},D=a("".concat(b,"-panel"),C,o({},"".concat(b,"-panel-disabled"),k)),z={prefixCls:b,disabled:k,color:R},A=n.createElement(n.Fragment,null,n.createElement(ze,g({onChange:M},z,{onChangeComplete:x})),n.createElement("div",{className:"".concat(b,"-slider-container")},n.createElement("div",{className:a("".concat(b,"-slider-group"),o({},"".concat(b,"-slider-group-disabled-alpha"),w))},n.createElement(O,g({},z,{type:"hue",colors:Fe,min:0,max:359,value:R.getHue(),onChange:function(e){M(H(e),{type:"hue",value:e})},onChangeComplete:function(e){x&&x(H(e))}})),!w&&n.createElement(O,g({},z,{type:"alpha",colors:[{percent:0,color:"rgba(255, 0, 4, 0)"},{percent:100,color:P}],min:0,max:100,value:100*R.a,onChange:function(e){M(T(e),{type:"alpha",value:e})},onChangeComplete:function(e){x&&x(T(e))}}))),n.createElement(h,{color:R.toRgbString(),prefixCls:b})));return n.createElement("div",{className:D,style:j,ref:i},"function"==typeof y?y(A):A)})),qe=function(e,t){if(!e)return null;var n={left:e.offsetLeft,right:e.parentElement.clientWidth-e.clientWidth-e.offsetLeft,width:e.clientWidth,top:e.offsetTop,bottom:e.parentElement.clientHeight-e.clientHeight-e.offsetTop,height:e.clientHeight};return t?{left:0,right:0,width:0,top:n.top,bottom:n.bottom,height:n.height}:{left:n.left,right:n.right,width:n.width,top:0,bottom:0,height:0}},Be=function(e){return void 0!==e?"".concat(e,"px"):void 0};function Ve(n){var o=n.prefixCls,i=n.containerRef,l=n.value,s=n.getValueIndex,c=n.motionName,d=n.onMotionStart,u=n.onMotionEnd,m=n.direction,g=n.vertical,h=void 0!==g&&g,p=e.useRef(null),x=e.useState(l),C=t(x,2),j=C[0],y=C[1],$=function(e){var t,n=s(e),a=null===(t=i.current)||void 0===t?void 0:t.querySelectorAll(".".concat(o,"-item"))[n];return(null==a?void 0:a.offsetParent)&&a},w=e.useState(null),S=t(w,2),k=S[0],E=S[1],O=e.useState(null),N=t(O,2),_=N[0],R=N[1];f((function(){if(j!==l){var e=$(j),t=$(l),n=qe(e,h),a=qe(t,h);y(l),E(n),R(a),e&&t?d():u()}}),[l]);var I=e.useMemo((function(){var e;return Be(h?null!==(e=null==k?void 0:k.top)&&void 0!==e?e:0:"rtl"===m?-(null==k?void 0:k.right):null==k?void 0:k.left)}),[h,m,k]),P=e.useMemo((function(){var e;return Be(h?null!==(e=null==_?void 0:_.top)&&void 0!==e?e:0:"rtl"===m?-(null==_?void 0:_.right):null==_?void 0:_.left)}),[h,m,_]);return k&&_?e.createElement(b,{visible:!0,motionName:c,motionAppear:!0,onAppearStart:function(){return h?{transform:"translateY(var(--thumb-start-top))",height:"var(--thumb-start-height)"}:{transform:"translateX(var(--thumb-start-left))",width:"var(--thumb-start-width)"}},onAppearActive:function(){return h?{transform:"translateY(var(--thumb-active-top))",height:"var(--thumb-active-height)"}:{transform:"translateX(var(--thumb-active-left))",width:"var(--thumb-active-width)"}},onVisibleChanged:function(){E(null),R(null),u()}},(function(t,n){var i=t.className,l=t.style,s=r(r({},l),{},{"--thumb-start-left":I,"--thumb-start-width":Be(null==k?void 0:k.width),"--thumb-active-left":P,"--thumb-active-width":Be(null==_?void 0:_.width),"--thumb-start-top":I,"--thumb-start-height":Be(null==k?void 0:k.height),"--thumb-active-top":P,"--thumb-active-height":Be(null==_?void 0:_.height)}),c={ref:v(p,n),style:s,className:a("".concat(o,"-thumb"),i)};return e.createElement("div",c)})):null}var Xe=["prefixCls","direction","vertical","options","disabled","defaultValue","value","name","onChange","className","motionName"];function Ge(e){return e.map((function(e){if("object"===j(e)&&null!==e){var t=function(e){return void 0!==e.title?e.title:"object"!==j(e.label)?null===(t=e.label)||void 0===t?void 0:t.toString():void 0;var t}(e);return r(r({},e),{},{title:t})}return{label:null==e?void 0:e.toString(),title:null==e?void 0:e.toString(),value:e}}))}var Ke=function(t){var n=t.prefixCls,r=t.className,i=t.disabled,l=t.checked,s=t.label,c=t.title,d=t.value,u=t.name,m=t.onChange,g=t.onFocus,h=t.onBlur,p=t.onKeyDown,f=t.onKeyUp,b=t.onMouseDown;return e.createElement("label",{className:a(r,o({},"".concat(n,"-item-disabled"),i)),onMouseDown:b},e.createElement("input",{name:u,className:"".concat(n,"-item-input"),type:"radio",disabled:i,checked:l,onChange:function(e){i||m(e,d)},onFocus:g,onBlur:h,onKeyDown:p,onKeyUp:f}),e.createElement("div",{className:"".concat(n,"-item-label"),title:c,"aria-selected":l},s))},Ue=e.forwardRef((function(n,r){var i,l,s=n.prefixCls,d=void 0===s?"rc-segmented":s,u=n.direction,m=n.vertical,h=n.options,p=void 0===h?[]:h,f=n.disabled,b=n.defaultValue,j=n.value,y=n.name,$=n.onChange,w=n.className,S=void 0===w?"":w,k=n.motionName,E=void 0===k?"thumb-motion":k,O=x(n,Xe),N=e.useRef(null),_=e.useMemo((function(){return v(N,r)}),[N,r]),R=e.useMemo((function(){return Ge(p)}),[p]),I=c(null===(i=R[0])||void 0===i?void 0:i.value,{value:j,defaultValue:b}),P=t(I,2),M=P[0],H=P[1],T=e.useState(!1),D=t(T,2),z=D[0],A=D[1],L=function(e,t){H(t),null==$||$(t)},F=C(O,["children"]),W=e.useState(!1),q=t(W,2),B=q[0],V=q[1],X=e.useState(!1),G=t(X,2),K=G[0],U=G[1],J=function(){U(!0)},Y=function(){U(!1)},Q=function(){V(!1)},Z=function(e){"Tab"===e.key&&V(!0)},ee=function(e){var t=R.findIndex((function(e){return e.value===M})),n=R.length,a=R[(t+e+n)%n];a&&(H(a.value),null==$||$(a.value))},te=function(e){switch(e.key){case"ArrowLeft":case"ArrowUp":ee(-1);break;case"ArrowRight":case"ArrowDown":ee(1)}};return e.createElement("div",g({role:"radiogroup","aria-label":"segmented control",tabIndex:f?void 0:0},F,{className:a(d,(l={},o(l,"".concat(d,"-rtl"),"rtl"===u),o(l,"".concat(d,"-disabled"),f),o(l,"".concat(d,"-vertical"),m),l),S),ref:_}),e.createElement("div",{className:"".concat(d,"-group")},e.createElement(Ve,{vertical:m,prefixCls:d,value:M,containerRef:N,motionName:"".concat(d,"-").concat(E),direction:u,getValueIndex:function(e){return R.findIndex((function(t){return t.value===e}))},onMotionStart:function(){A(!0)},onMotionEnd:function(){A(!1)}}),R.map((function(t){var n;return e.createElement(Ke,g({},t,{name:y,key:t.value,prefixCls:d,className:a(t.className,"".concat(d,"-item"),(n={},o(n,"".concat(d,"-item-selected"),t.value===M&&!z),o(n,"".concat(d,"-item-focused"),K&&B&&t.value===M),n)),checked:t.value===M,onChange:L,onFocus:J,onBlur:Y,onKeyDown:te,onKeyUp:Z,onMouseDown:Q,disabled:!!f||!!t.disabled}))}))))}));function Je(e,t){return{[`${e}, ${e}:hover, ${e}:focus`]:{color:t.colorTextDisabled,cursor:"not-allowed"}}}function Ye(e){return{backgroundColor:e.itemSelectedBg,boxShadow:e.boxShadowTertiary}}const Qe=Object.assign({overflow:"hidden"},O),Ze=e=>{const{componentCls:t}=e,n=e.calc(e.controlHeight).sub(e.calc(e.trackPadding).mul(2)).equal(),a=e.calc(e.controlHeightLG).sub(e.calc(e.trackPadding).mul(2)).equal(),o=e.calc(e.controlHeightSM).sub(e.calc(e.trackPadding).mul(2)).equal();return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},w(e)),{display:"inline-block",padding:e.trackPadding,color:e.itemColor,background:e.trackBg,borderRadius:e.borderRadius,transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`}),S(e)),{[`${t}-group`]:{position:"relative",display:"flex",alignItems:"stretch",justifyItems:"flex-start",flexDirection:"row",width:"100%"},[`&${t}-rtl`]:{direction:"rtl"},[`&${t}-vertical`]:{[`${t}-group`]:{flexDirection:"column"},[`${t}-thumb`]:{width:"100%",height:0,padding:`0 ${k(e.paddingXXS)}`}},[`&${t}-block`]:{display:"flex"},[`&${t}-block ${t}-item`]:{flex:1,minWidth:0},[`${t}-item`]:{position:"relative",textAlign:"center",cursor:"pointer",transition:`color ${e.motionDurationMid} ${e.motionEaseInOut}`,borderRadius:e.borderRadiusSM,transform:"translateZ(0)","&-selected":Object.assign(Object.assign({},Ye(e)),{color:e.itemSelectedColor}),"&-focused":Object.assign({},E(e)),"&::after":{content:'""',position:"absolute",zIndex:-1,width:"100%",height:"100%",top:0,insetInlineStart:0,borderRadius:"inherit",opacity:0,transition:`opacity ${e.motionDurationMid}`,pointerEvents:"none"},[`&:hover:not(${t}-item-selected):not(${t}-item-disabled)`]:{color:e.itemHoverColor,"&::after":{opacity:1,backgroundColor:e.itemHoverBg}},[`&:active:not(${t}-item-selected):not(${t}-item-disabled)`]:{color:e.itemHoverColor,"&::after":{opacity:1,backgroundColor:e.itemActiveBg}},"&-label":Object.assign({minHeight:n,lineHeight:k(n),padding:`0 ${k(e.segmentedPaddingHorizontal)}`},Qe),"&-icon + *":{marginInlineStart:e.calc(e.marginSM).div(2).equal()},"&-input":{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:0,height:0,opacity:0,pointerEvents:"none"}},[`${t}-thumb`]:Object.assign(Object.assign({},Ye(e)),{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:0,height:"100%",padding:`${k(e.paddingXXS)} 0`,borderRadius:e.borderRadiusSM,transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOut}, height ${e.motionDurationSlow} ${e.motionEaseInOut}`,[`& ~ ${t}-item:not(${t}-item-selected):not(${t}-item-disabled)::after`]:{backgroundColor:"transparent"}}),[`&${t}-lg`]:{borderRadius:e.borderRadiusLG,[`${t}-item-label`]:{minHeight:a,lineHeight:k(a),padding:`0 ${k(e.segmentedPaddingHorizontal)}`,fontSize:e.fontSizeLG},[`${t}-item, ${t}-thumb`]:{borderRadius:e.borderRadius}},[`&${t}-sm`]:{borderRadius:e.borderRadiusSM,[`${t}-item-label`]:{minHeight:o,lineHeight:k(o),padding:`0 ${k(e.segmentedPaddingHorizontalSM)}`},[`${t}-item, ${t}-thumb`]:{borderRadius:e.borderRadiusXS}}}),Je(`&-disabled ${t}-item`,e)),Je(`${t}-item-disabled`,e)),{[`${t}-thumb-motion-appear-active`]:{transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOut}, width ${e.motionDurationSlow} ${e.motionEaseInOut}`,willChange:"transform, width"},[`&${t}-shape-round`]:{borderRadius:9999,[`${t}-item, ${t}-thumb`]:{borderRadius:9999}}})}},et=y("Segmented",(e=>{const{lineWidth:t,calc:n}=e,a=$(e,{segmentedPaddingHorizontal:n(e.controlPaddingHorizontal).sub(t).equal(),segmentedPaddingHorizontalSM:n(e.controlPaddingHorizontalSM).sub(t).equal()});return[Ze(a)]}),(e=>{const{colorTextLabel:t,colorText:n,colorFillSecondary:a,colorBgElevated:o,colorFill:r,lineWidthBold:i,colorBgLayout:l}=e;return{trackPadding:i,trackBg:l,itemColor:t,itemHoverColor:n,itemHoverBg:a,itemSelectedBg:o,itemActiveBg:r,itemSelectedColor:n}}));var tt=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n};const nt=e.forwardRef(((t,n)=>{const o=N(),{prefixCls:r,className:i,rootClassName:l,block:s,options:c=[],size:d="middle",style:u,vertical:m,shape:g="default",name:h=o}=t,p=tt(t,["prefixCls","className","rootClassName","block","options","size","style","vertical","shape","name"]),{getPrefixCls:f,direction:b,className:v,style:x}=_("segmented"),C=f("segmented",r),[j,y,$]=et(C),w=R(d),S=e.useMemo((()=>c.map((t=>{if(function(e){return"object"==typeof e&&!!(null==e?void 0:e.icon)}(t)){const{icon:n,label:a}=t,o=tt(t,["icon","label"]);return Object.assign(Object.assign({},o),{label:e.createElement(e.Fragment,null,e.createElement("span",{className:`${C}-item-icon`},n),a&&e.createElement("span",null,a))})}return t}))),[c,C]),k=a(i,l,v,{[`${C}-block`]:s,[`${C}-sm`]:"small"===w,[`${C}-lg`]:"large"===w,[`${C}-vertical`]:m,[`${C}-shape-${g}`]:"round"===g},y,$),E=Object.assign(Object.assign({},x),u);return j(e.createElement(Ue,Object.assign({},p,{name:h,className:k,style:E,options:S,ref:n,prefixCls:C,direction:b,vertical:m})))})),at=n.createContext({}),ot=n.createContext({}),rt=({prefixCls:e,value:t,onChange:a})=>n.createElement("div",{className:`${e}-clear`,onClick:()=>{if(a&&t&&!t.cleared){const e=t.toHsb();e.a=0;const n=I(e);n.cleared=!0,a(n)}}}),it="hex",lt="rgb",st="hsb",ct=({prefixCls:t,min:o=0,max:r=100,value:i,onChange:l,className:s,formatter:c})=>{const d=`${t}-steppers`,[u,m]=e.useState(0),g=Number.isNaN(i)?u:i;return n.createElement(_e,{className:a(d,s),min:o,max:r,value:g,formatter:c,size:"small",onChange:e=>{m(e||0),null==l||l(e)}})},dt=({prefixCls:t,value:a,onChange:o})=>{const r=`${t}-alpha-input`,[i,l]=e.useState((()=>I(a||"#000"))),s=a||i;return n.createElement(ct,{value:P(s),prefixCls:t,formatter:e=>`${e}%`,className:r,onChange:e=>{const t=s.toHsb();t.a=(e||0)/100;const n=I(t);l(n),null==o||o(n)}})},ut=/(^#[\da-f]{6}$)|(^#[\da-f]{8}$)/i,mt=({prefixCls:t,value:a,onChange:o})=>{const r=`${t}-hex-input`,[i,l]=e.useState((()=>a?M(a.toHexString()):void 0));e.useEffect((()=>{a&&l(M(a.toHexString()))}),[a]);return n.createElement(je,{className:r,value:i,prefix:"#",onChange:e=>{const t=e.target.value;var n;l(M(t)),n=M(t,!0),ut.test(`#${n}`)&&(null==o||o(I(t)))},size:"small"})},gt=({prefixCls:t,value:a,onChange:o})=>{const r=`${t}-hsb-input`,[i,l]=e.useState((()=>I(a||"#000"))),s=a||i,c=(e,t)=>{const n=s.toHsb();n[t]="h"===t?e:(e||0)/100;const a=I(n);l(a),null==o||o(a)};return n.createElement("div",{className:r},n.createElement(ct,{max:360,min:0,value:Number(s.toHsb().h),prefixCls:t,className:r,formatter:e=>H(e||0).toString(),onChange:e=>c(Number(e),"h")}),n.createElement(ct,{max:100,min:0,value:100*Number(s.toHsb().s),prefixCls:t,className:r,formatter:e=>`${H(e||0)}%`,onChange:e=>c(Number(e),"s")}),n.createElement(ct,{max:100,min:0,value:100*Number(s.toHsb().b),prefixCls:t,className:r,formatter:e=>`${H(e||0)}%`,onChange:e=>c(Number(e),"b")}))},ht=({prefixCls:t,value:a,onChange:o})=>{const r=`${t}-rgb-input`,[i,l]=e.useState((()=>I(a||"#000"))),s=a||i,c=(e,t)=>{const n=s.toRgb();n[t]=e||0;const a=I(n);l(a),null==o||o(a)};return n.createElement("div",{className:r},n.createElement(ct,{max:255,min:0,value:Number(s.toRgb().r),prefixCls:t,className:r,onChange:e=>c(Number(e),"r")}),n.createElement(ct,{max:255,min:0,value:Number(s.toRgb().g),prefixCls:t,className:r,onChange:e=>c(Number(e),"g")}),n.createElement(ct,{max:255,min:0,value:Number(s.toRgb().b),prefixCls:t,className:r,onChange:e=>c(Number(e),"b")}))},pt=[it,st,lt].map((e=>({value:e,label:e.toUpperCase()}))),ft=t=>{const{prefixCls:a,format:o,value:r,disabledAlpha:i,onFormatChange:l,onChange:s,disabledFormat:d}=t,[u,m]=c(it,{value:o,onChange:l}),g=`${a}-input`,h=e.useMemo((()=>{const e={value:r,prefixCls:a,onChange:s};switch(u){case st:return n.createElement(gt,Object.assign({},e));case lt:return n.createElement(ht,Object.assign({},e));default:return n.createElement(mt,Object.assign({},e))}}),[u,a,r,s]);return n.createElement("div",{className:`${g}-container`},!d&&n.createElement(Ne,{value:u,variant:"borderless",getPopupContainer:e=>e,popupMatchSelectWidth:68,placement:"bottomRight",onChange:e=>{m(e)},className:`${a}-format-select`,size:"small",options:pt}),n.createElement("div",{className:g},h),!i&&n.createElement(dt,{prefixCls:a,value:r,onChange:s}))};const bt=t=>{const{prefixCls:n,colors:o,type:r,color:l,range:s=!1,className:c,activeIndex:d,onActive:u,onDragStart:m,onDragChange:g,onKeyDelete:h}=t,p=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n}(t,["prefixCls","colors","type","color","range","className","activeIndex","onActive","onDragStart","onDragChange","onKeyDelete"]),f=Object.assign(Object.assign({},p),{track:!1}),b=e.useMemo((()=>`linear-gradient(90deg, ${o.map((e=>`${e.color} ${e.percent}%`)).join(", ")})`),[o]),v=e.useMemo((()=>l&&r?"alpha"===r?l.toRgbString():`hsl(${l.toHsb().h}, 100%, 50%)`:null),[l,r]),x=i(m),C=i(g),j=e.useMemo((()=>({onDragStart:x,onDragChange:C})),[]),y=i(((t,i)=>{const{onFocus:l,style:s,className:c,onKeyDown:m}=t.props,g=Object.assign({},s);return"gradient"===r&&(g.background=T(o,i.value)),e.cloneElement(t,{onFocus:e=>{null==u||u(i.index),null==l||l(e)},style:g,className:a(c,{[`${n}-slider-handle-active`]:d===i.index}),onKeyDown:e=>{"Delete"!==e.key&&"Backspace"!==e.key||!h||h(i.index),null==m||m(e)}})})),$=e.useMemo((()=>({direction:"ltr",handleRender:y})),[]);return e.createElement(Re.Provider,{value:$},e.createElement(Ie.Provider,{value:j},e.createElement(Pe,Object.assign({},f,{className:a(c,`${n}-slider`),tooltip:{open:!1},range:{editable:s,minCount:2},styles:{rail:{background:b},handle:v?{background:v}:{}},classNames:{rail:`${n}-slider-rail`,handle:`${n}-slider-handle`}}))))};function vt(e){return D(e).sort(((e,t)=>e.percent-t.percent))}const xt=t=>{const{prefixCls:n,mode:a,onChange:o,onChangeComplete:r,onActive:i,activeIndex:l,onGradientDragging:s,colors:c}=t,d="gradient"===a,u=e.useMemo((()=>c.map((e=>({percent:e.percent,color:e.color.toRgbString()})))),[c]),m=e.useMemo((()=>u.map((e=>e.percent))),[u]),g=e.useRef(u);return d?e.createElement(bt,{min:0,max:100,prefixCls:n,className:`${n}-gradient-slider`,colors:u,color:null,value:m,range:!0,onChangeComplete:e=>{r(new z(u)),l>=e.length&&i(e.length-1),s(!1)},disabled:!1,type:"gradient",activeIndex:l,onActive:i,onDragStart:({rawValues:e,draggingIndex:t,draggingValue:n})=>{if(e.length>u.length){const e=T(u,n),a=D(u);a.splice(t,0,{percent:n,color:e}),g.current=a}else g.current=u;s(!0),o(new z(vt(g.current)),!0)},onDragChange:({deleteIndex:e,draggingIndex:t,draggingValue:n})=>{let a=D(g.current);-1!==e?a.splice(e,1):(a[t]=Object.assign(Object.assign({},a[t]),{percent:n}),a=vt(a)),o(new z(a),!0)},onKeyDelete:e=>{const t=D(u);t.splice(e,1);const n=new z(t);o(n),r(n)}}):null},Ct=e.memo(xt);const jt={slider:t=>{const{value:n,onChange:a,onChangeComplete:o}=t;return e.createElement(bt,Object.assign({},t,{value:[n],onChange:e=>a(e[0]),onChangeComplete:e=>o(e[0])}))}},yt=()=>{const t=e.useContext(at),{mode:a,onModeChange:o,modeOptions:r,prefixCls:i,allowClear:l,value:s,disabledAlpha:c,onChange:d,onClear:u,onChangeComplete:m,activeIndex:g,gradientDragging:h}=t,p=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n}(t,["mode","onModeChange","modeOptions","prefixCls","allowClear","value","disabledAlpha","onChange","onClear","onChangeComplete","activeIndex","gradientDragging"]),b=n.useMemo((()=>s.cleared?[{percent:0,color:new z("")},{percent:100,color:new z("")}]:s.getColors()),[s]),v=!s.isGradient(),[x,C]=n.useState(s);f((()=>{var e;v||C(null===(e=b[g])||void 0===e?void 0:e.color)}),[h,g]);const j=n.useMemo((()=>{var e;return v?s:h?x:null===(e=b[g])||void 0===e?void 0:e.color}),[s,g,v,x,h]),[y,$]=n.useState(j),[w,S]=n.useState(0),k=(null==y?void 0:y.equals(j))?j:y;f((()=>{$(j)}),[w,null==j?void 0:j.toHexString()]);const E=(e,t)=>{let n=I(e);if(s.cleared){const e=n.toRgb();if(e.r||e.g||e.b||!t)n=A(n);else{const{type:e,value:a=0}=t;n=new z({h:"hue"===e?a:0,s:1,b:1,a:"alpha"===e?a/100:1})}}if("single"===a)return n;const o=D(b);return o[g]=Object.assign(Object.assign({},o[g]),{color:n}),new z(o)};let O=null;const N=r.length>1;return(l||N)&&(O=n.createElement("div",{className:`${i}-operation`},N&&n.createElement(nt,{size:"small",options:r,value:a,onChange:o}),n.createElement(rt,Object.assign({prefixCls:i,value:s,onChange:e=>{d(e),null==u||u()}},p)))),n.createElement(n.Fragment,null,O,n.createElement(Ct,Object.assign({},t,{colors:b})),n.createElement(We,{prefixCls:i,value:null==k?void 0:k.toHsb(),disabledAlpha:c,onChange:(e,t)=>{((e,t,n)=>{const a=E(e,n);$(a.isGradient()?a.getColors()[g].color:a),d(a,t)})(e,!0,t)},onChangeComplete:(e,t)=>{((e,t)=>{m(E(e,t)),S((e=>e+1))})(e,t)},components:jt}),n.createElement(ft,Object.assign({value:j,onChange:e=>{d(E(e))},prefixCls:i,disabledAlpha:c},p)))},$t=()=>{const{prefixCls:t,value:a,presets:o,onChange:r}=e.useContext(ot);return Array.isArray(o)?n.createElement(L,{value:a,presets:o,prefixCls:t,onChange:r}):null},wt=e=>{const{prefixCls:t,presets:a,panelRender:o,value:r,onChange:i,onClear:l,allowClear:s,disabledAlpha:c,mode:d,onModeChange:u,modeOptions:m,onChangeComplete:g,activeIndex:h,onActive:p,format:f,onFormatChange:b,gradientDragging:v,onGradientDragging:x,disabledFormat:C}=e,j=`${t}-inner`,y=n.useMemo((()=>({prefixCls:t,value:r,onChange:i,onClear:l,allowClear:s,disabledAlpha:c,mode:d,onModeChange:u,modeOptions:m,onChangeComplete:g,activeIndex:h,onActive:p,format:f,onFormatChange:b,gradientDragging:v,onGradientDragging:x,disabledFormat:C})),[t,r,i,l,s,c,d,u,m,g,h,p,f,b,v,x,C]),$=n.useMemo((()=>({prefixCls:t,value:r,presets:a,onChange:i})),[t,r,a,i]),w=n.createElement("div",{className:`${j}-content`},n.createElement(yt,null),Array.isArray(a)&&n.createElement(Oe,null),n.createElement($t,null));return n.createElement(at.Provider,{value:y},n.createElement(ot.Provider,{value:$},n.createElement("div",{className:j},"function"==typeof o?o(w,{components:{Picker:yt,Presets:$t}}):w)))};const St=e.forwardRef(((t,o)=>{const{color:r,prefixCls:i,open:l,disabled:s,format:c,className:d,showText:u,activeIndex:m}=t,g=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n}(t,["color","prefixCls","open","disabled","format","className","showText","activeIndex"]),p=`${i}-trigger`,f=`${p}-text`,b=`${f}-cell`,[v]=F("ColorPicker"),x=n.useMemo((()=>{if(!u)return"";if("function"==typeof u)return u(r);if(r.cleared)return v.transparent;if(r.isGradient())return r.getColors().map(((e,t)=>{const o=-1!==m&&m!==t;return n.createElement("span",{key:t,className:a(b,o&&`${b}-inactive`)},e.color.toRgbString()," ",e.percent,"%")}));const e=r.toHexString().toUpperCase(),t=P(r);switch(c){case"rgb":return r.toRgbString();case"hsb":return r.toHsbString();default:return t<100?`${e.slice(0,7)},${t}%`:e}}),[r,c,u,m]),C=e.useMemo((()=>r.cleared?n.createElement(rt,{prefixCls:i}):n.createElement(h,{prefixCls:i,color:r.toCssString()})),[r,i]);return n.createElement("div",Object.assign({ref:o,className:a(p,d,{[`${p}-active`]:l,[`${p}-disabled`]:s})},W(g)),C,u&&n.createElement("div",{className:f},x))}));const kt=(e,t)=>({backgroundImage:`conic-gradient(${t} 25%, transparent 25% 50%, ${t} 50% 75%, transparent 75% 100%)`,backgroundSize:`${e} ${e}`}),Et=(e,t)=>{const{componentCls:n,borderRadiusSM:a,colorPickerInsetShadow:o,lineWidth:r,colorFillSecondary:i}=e;return{[`${n}-color-block`]:Object.assign(Object.assign({position:"relative",borderRadius:a,width:t,height:t,boxShadow:o,flex:"none"},kt("50%",e.colorFillSecondary)),{[`${n}-color-block-inner`]:{width:"100%",height:"100%",boxShadow:`inset 0 0 0 ${k(r)} ${i}`,borderRadius:"inherit"}})}},Ot=e=>{const{componentCls:t,antCls:n,fontSizeSM:a,lineHeightSM:o,colorPickerAlphaInputWidth:r,marginXXS:i,paddingXXS:l,controlHeightSM:s,marginXS:c,fontSizeIcon:d,paddingXS:u,colorTextPlaceholder:m,colorPickerInputNumberHandleWidth:g,lineWidth:h}=e;return{[`${t}-input-container`]:{display:"flex",[`${t}-steppers${n}-input-number`]:{fontSize:a,lineHeight:o,[`${n}-input-number-input`]:{paddingInlineStart:l,paddingInlineEnd:0},[`${n}-input-number-handler-wrap`]:{width:g}},[`${t}-steppers${t}-alpha-input`]:{flex:`0 0 ${k(r)}`,marginInlineStart:i},[`${t}-format-select${n}-select`]:{marginInlineEnd:c,width:"auto","&-single":{[`${n}-select-selector`]:{padding:0,border:0},[`${n}-select-arrow`]:{insetInlineEnd:0},[`${n}-select-selection-item`]:{paddingInlineEnd:e.calc(d).add(i).equal(),fontSize:a,lineHeight:k(s)},[`${n}-select-item-option-content`]:{fontSize:a,lineHeight:o},[`${n}-select-dropdown`]:{[`${n}-select-item`]:{minHeight:"auto"}}}},[`${t}-input`]:{gap:i,alignItems:"center",flex:1,width:0,[`${t}-hsb-input,${t}-rgb-input`]:{display:"flex",gap:i,alignItems:"center"},[`${t}-steppers`]:{flex:1},[`${t}-hex-input${n}-input-affix-wrapper`]:{flex:1,padding:`0 ${k(u)}`,[`${n}-input`]:{fontSize:a,textTransform:"uppercase",lineHeight:k(e.calc(s).sub(e.calc(h).mul(2)).equal())},[`${n}-input-prefix`]:{color:m}}}}}},Nt=e=>{const{componentCls:t,controlHeightLG:n,borderRadiusSM:a,colorPickerInsetShadow:o,marginSM:r,colorBgElevated:i,colorFillSecondary:l,lineWidthBold:s,colorPickerHandlerSize:c}=e;return{userSelect:"none",[`${t}-select`]:{[`${t}-palette`]:{minHeight:e.calc(n).mul(4).equal(),overflow:"hidden",borderRadius:a},[`${t}-saturation`]:{position:"absolute",borderRadius:"inherit",boxShadow:o,inset:0},marginBottom:r},[`${t}-handler`]:{width:c,height:c,border:`${k(s)} solid ${i}`,position:"relative",borderRadius:"50%",cursor:"pointer",boxShadow:`${o}, 0 0 0 1px ${l}`}}},_t=e=>{const{componentCls:t,antCls:n,colorTextQuaternary:a,paddingXXS:o,colorPickerPresetColorSize:r,fontSizeSM:i,colorText:l,lineHeightSM:s,lineWidth:c,borderRadius:d,colorFill:u,colorWhite:m,marginXXS:g,paddingXS:h,fontHeightSM:p}=e;return{[`${t}-presets`]:{[`${n}-collapse-item > ${n}-collapse-header`]:{padding:0,[`${n}-collapse-expand-icon`]:{height:p,color:a,paddingInlineEnd:o}},[`${n}-collapse`]:{display:"flex",flexDirection:"column",gap:g},[`${n}-collapse-item > ${n}-collapse-content > ${n}-collapse-content-box`]:{padding:`${k(h)} 0`},"&-label":{fontSize:i,color:l,lineHeight:s},"&-items":{display:"flex",flexWrap:"wrap",gap:e.calc(g).mul(1.5).equal(),[`${t}-presets-color`]:{position:"relative",cursor:"pointer",width:r,height:r,"&::before":{content:'""',pointerEvents:"none",width:e.calc(r).add(e.calc(c).mul(4)).equal(),height:e.calc(r).add(e.calc(c).mul(4)).equal(),position:"absolute",top:e.calc(c).mul(-2).equal(),insetInlineStart:e.calc(c).mul(-2).equal(),borderRadius:d,border:`${k(c)} solid transparent`,transition:`border-color ${e.motionDurationMid} ${e.motionEaseInBack}`},"&:hover::before":{borderColor:u},"&::after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"21.5%",display:"table",width:e.calc(r).div(13).mul(5).equal(),height:e.calc(r).div(13).mul(8).equal(),border:`${k(e.lineWidthBold)} solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`},[`&${t}-presets-color-checked`]:{"&::after":{opacity:1,borderColor:m,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`transform ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`},[`&${t}-presets-color-bright`]:{"&::after":{borderColor:"rgba(0, 0, 0, 0.45)"}}}}},"&-empty":{fontSize:i,color:a}}}},Rt=e=>{const{componentCls:t,colorPickerInsetShadow:n,colorBgElevated:a,colorFillSecondary:o,lineWidthBold:r,colorPickerHandlerSizeSM:i,colorPickerSliderHeight:l,marginSM:s,marginXS:c}=e,d=e.calc(i).sub(e.calc(r).mul(2).equal()).equal(),u=e.calc(i).add(e.calc(r).mul(2).equal()).equal(),m={"&:after":{transform:"scale(1)",boxShadow:`${n}, 0 0 0 1px ${e.colorPrimaryActive}`}};return{[`${t}-slider`]:[kt(k(l),e.colorFillSecondary),{margin:0,padding:0,height:l,borderRadius:e.calc(l).div(2).equal(),"&-rail":{height:l,borderRadius:e.calc(l).div(2).equal(),boxShadow:n},[`& ${t}-slider-handle`]:{width:d,height:d,top:0,borderRadius:"100%","&:before":{display:"block",position:"absolute",background:"transparent",left:{_skip_check_:!0,value:"50%"},top:"50%",transform:"translate(-50%, -50%)",width:u,height:u,borderRadius:"100%"},"&:after":{width:i,height:i,border:`${k(r)} solid ${a}`,boxShadow:`${n}, 0 0 0 1px ${o}`,outline:"none",insetInlineStart:e.calc(r).mul(-1).equal(),top:e.calc(r).mul(-1).equal(),background:"transparent",transition:"none"},"&:focus":m}}],[`${t}-slider-container`]:{display:"flex",gap:s,marginBottom:s,[`${t}-slider-group`]:{flex:1,flexDirection:"column",justifyContent:"space-between",display:"flex","&-disabled-alpha":{justifyContent:"center"}}},[`${t}-gradient-slider`]:{marginBottom:c,[`& ${t}-slider-handle`]:{"&:after":{transform:"scale(0.8)"},"&-active, &:focus":m}}}},It=(e,t,n)=>({borderInlineEndWidth:e.lineWidth,borderColor:t,boxShadow:`0 0 0 ${k(e.controlOutlineWidth)} ${n}`,outline:0}),Pt=e=>{const{componentCls:t}=e;return{"&-rtl":{[`${t}-presets-color`]:{"&::after":{direction:"ltr"}},[`${t}-clear`]:{"&::after":{direction:"ltr"}}}}},Mt=(e,t,n)=>{const{componentCls:a,borderRadiusSM:o,lineWidth:r,colorSplit:i,colorBorder:l,red6:s}=e;return{[`${a}-clear`]:Object.assign(Object.assign({width:t,height:t,borderRadius:o,border:`${k(r)} solid ${i}`,position:"relative",overflow:"hidden",cursor:"inherit",transition:`all ${e.motionDurationFast}`},n),{"&::after":{content:'""',position:"absolute",insetInlineEnd:e.calc(r).mul(-1).equal(),top:e.calc(r).mul(-1).equal(),display:"block",width:40,height:2,transformOrigin:"calc(100% - 1px) 1px",transform:"rotate(-45deg)",backgroundColor:s},"&:hover":{borderColor:l}})}},Ht=e=>{const{componentCls:t,colorError:n,colorWarning:a,colorErrorHover:o,colorWarningHover:r,colorErrorOutline:i,colorWarningOutline:l}=e;return{[`&${t}-status-error`]:{borderColor:n,"&:hover":{borderColor:o},[`&${t}-trigger-active`]:Object.assign({},It(e,n,i))},[`&${t}-status-warning`]:{borderColor:a,"&:hover":{borderColor:r},[`&${t}-trigger-active`]:Object.assign({},It(e,a,l))}}},Tt=e=>{const{componentCls:t,controlHeightLG:n,controlHeightSM:a,controlHeight:o,controlHeightXS:r,borderRadius:i,borderRadiusSM:l,borderRadiusXS:s,borderRadiusLG:c,fontSizeLG:d}=e;return{[`&${t}-lg`]:{minWidth:n,minHeight:n,borderRadius:c,[`${t}-color-block, ${t}-clear`]:{width:o,height:o,borderRadius:i},[`${t}-trigger-text`]:{fontSize:d}},[`&${t}-sm`]:{minWidth:a,minHeight:a,borderRadius:l,[`${t}-color-block, ${t}-clear`]:{width:r,height:r,borderRadius:s},[`${t}-trigger-text`]:{lineHeight:k(r)}}}},Dt=e=>{const{antCls:t,componentCls:n,colorPickerWidth:a,colorPrimary:o,motionDurationMid:r,colorBgElevated:i,colorTextDisabled:l,colorText:s,colorBgContainerDisabled:c,borderRadius:d,marginXS:u,marginSM:m,controlHeight:g,controlHeightSM:h,colorBgTextActive:p,colorPickerPresetColorSize:f,colorPickerPreviewSize:b,lineWidth:v,colorBorder:x,paddingXXS:C,fontSize:j,colorPrimaryHover:y,controlOutline:$}=e;return[{[n]:Object.assign({[`${n}-inner`]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({"&-content":{display:"flex",flexDirection:"column",width:a,[`& > ${t}-divider`]:{margin:`${k(m)} 0 ${k(u)}`}},[`${n}-panel`]:Object.assign({},Nt(e))},Rt(e)),Et(e,b)),Ot(e)),_t(e)),Mt(e,f,{marginInlineStart:"auto"})),{[`${n}-operation`]:{display:"flex",justifyContent:"space-between",marginBottom:u}}),"&-trigger":Object.assign(Object.assign(Object.assign(Object.assign({minWidth:g,minHeight:g,borderRadius:d,border:`${k(v)} solid ${x}`,cursor:"pointer",display:"inline-flex",alignItems:"flex-start",justifyContent:"center",transition:`all ${r}`,background:i,padding:e.calc(C).sub(v).equal(),[`${n}-trigger-text`]:{marginInlineStart:u,marginInlineEnd:e.calc(u).sub(e.calc(C).sub(v)).equal(),fontSize:j,color:s,alignSelf:"center","&-cell":{"&:not(:last-child):after":{content:'", "'},"&-inactive":{color:l}}},"&:hover":{borderColor:y},[`&${n}-trigger-active`]:Object.assign({},It(e,o,$)),"&-disabled":{color:l,background:c,cursor:"not-allowed","&:hover":{borderColor:p},[`${n}-trigger-text`]:{color:l}}},Mt(e,h)),Et(e,h)),Ht(e)),Tt(e))},Pt(e))},q(e,{focusElCls:`${n}-trigger-active`})]},zt=y("ColorPicker",(e=>{const{colorTextQuaternary:t,marginSM:n}=e,a=$(e,{colorPickerWidth:234,colorPickerHandlerSize:16,colorPickerHandlerSizeSM:12,colorPickerAlphaInputWidth:44,colorPickerInputNumberHandleWidth:16,colorPickerPresetColorSize:24,colorPickerInsetShadow:`inset 0 0 1px 0 ${t}`,colorPickerSliderHeight:8,colorPickerPreviewSize:e.calc(8).mul(2).add(n).equal()});return[Dt(a)]}));const At=t=>{const{mode:o,value:r,defaultValue:l,format:s,defaultFormat:d,allowClear:u=!1,presets:m,children:g,trigger:h="click",open:p,disabled:f,placement:b="bottomLeft",arrow:v=!0,panelRender:x,showText:C,style:j,className:y,size:$,rootClassName:w,prefixCls:S,styles:k,disabledAlpha:E=!1,onFormatChange:O,onChange:N,onClear:_,onOpenChange:M,onChangeComplete:H,getPopupContainer:T,autoAdjustOverflow:D=!0,destroyTooltipOnHide:L,destroyOnHidden:W,disabledFormat:q}=t,J=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]])}return n}(t,["mode","value","defaultValue","format","defaultFormat","allowClear","presets","children","trigger","open","disabled","placement","arrow","panelRender","showText","style","className","size","rootClassName","prefixCls","styles","disabledAlpha","onFormatChange","onChange","onClear","onOpenChange","onChangeComplete","getPopupContainer","autoAdjustOverflow","destroyTooltipOnHide","destroyOnHidden","disabledFormat"]),{getPrefixCls:Y,direction:Q,colorPicker:Z}=e.useContext(B),te=e.useContext(V),ne=null!=f?f:te,[ae,oe]=c(!1,{value:p,postState:e=>!ne&&e,onChange:M}),[re,ie]=c(s,{value:s,defaultValue:d,onChange:O}),le=Y("color-picker",S),[se,ce,de,ue,me]=function(t,n,a){const[o]=F("ColorPicker"),[r,l]=c(t,{value:n}),[s,d]=e.useState("single"),[u,m]=e.useMemo((()=>{const e=(Array.isArray(a)?a:[a]).filter((e=>e));e.length||e.push("single");const t=new Set(e),n=[],r=(e,a)=>{t.has(e)&&n.push({label:a,value:e})};return r("single",o.singleColor),r("gradient",o.gradientColor),[n,t]}),[a]),[g,h]=e.useState(null),p=i((e=>{h(e),l(e)})),f=e.useMemo((()=>{const e=I(r||"");return e.equals(g)?g:e}),[r,g]),b=e.useMemo((()=>{var e;return m.has(s)?s:null===(e=u[0])||void 0===e?void 0:e.value}),[m,s,u]);return e.useEffect((()=>{d(f.isGradient()?"gradient":"single")}),[f]),[f,p,b,d,u]}(l,r,o),ge=e.useMemo((()=>P(se)<100),[se]),[he,pe]=n.useState(null),fe=e=>{if(H){let t=I(e);E&&ge&&(t=A(e)),H(t)}},be=(e,t)=>{let n=I(e);E&&ge&&(n=A(n)),ce(n),pe(null),N&&N(n,n.toCssString()),t||fe(n)},[ve,xe]=n.useState(0),[Ce,je]=n.useState(!1),{status:ye}=n.useContext(X),{compactSize:$e,compactItemClassnames:we}=G(le,Q),Se=R((e=>{var t;return null!==(t=null!=$?$:$e)&&void 0!==t?t:e})),ke=K(le),[Oe,Ne,_e]=zt(le,ke),Re=a(w,_e,ke,{[`${le}-rtl`]:Q}),Ie=a(ee(le,ye),{[`${le}-sm`]:"small"===Se,[`${le}-lg`]:"large"===Se},we,null==Z?void 0:Z.className,Re,y,Ne),Pe=a(le,Re),Me={open:ae,trigger:h,placement:b,arrow:v,rootClassName:w,getPopupContainer:T,autoAdjustOverflow:D,destroyOnHidden:null!=W?W:!!L},He=Object.assign(Object.assign({},null==Z?void 0:Z.style),j);return Oe(n.createElement(Ee,Object.assign({style:null==k?void 0:k.popup,styles:{body:null==k?void 0:k.popupOverlayInner},onOpenChange:e=>{e&&ne||oe(e)},content:n.createElement(U,{form:!0},n.createElement(wt,{mode:de,onModeChange:e=>{if(ue(e),"single"===e&&se.isGradient())xe(0),be(new z(se.getColors()[0].color)),pe(se);else if("gradient"===e&&!se.isGradient()){const e=ge?A(se):se;be(new z(he||[{percent:0,color:e},{percent:100,color:e}]))}},modeOptions:me,prefixCls:le,value:se,allowClear:u,disabled:ne,disabledAlpha:E,presets:m,panelRender:x,format:re,onFormatChange:ie,onChange:be,onChangeComplete:fe,onClear:_,activeIndex:ve,onActive:xe,gradientDragging:Ce,onGradientDragging:je,disabledFormat:q})),classNames:{root:Pe}},Me),g||n.createElement(St,Object.assign({activeIndex:ae?ve:-1,open:ae,className:Ie,style:He,prefixCls:le,disabled:ne,showText:C,format:re},J,{color:se}))))},Lt=ke(At,void 0,(e=>Object.assign(Object.assign({},e),{placement:"bottom",autoAdjustOverflow:!1})),"color-picker",(e=>e));function Ft(e){return e?8==e.length?"#"+e.slice(2):"#"+e:"#000000"}At._InternalPanelDoNotUseOrYouWillBeFired=Lt;const Wt=Q((()=>Z((()=>import("./Collection-BYcChfHL.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26]),import.meta.url)),{fallback:he}),qt=Q((()=>Z((()=>import("./MediaSet-CUZEUMm6.js")),__vite__mapDeps([27,1,2,28,4,29,0,3,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,30,31]),import.meta.url)),{fallback:ge}),Bt=Q((()=>Z((()=>import("./VideoViewer-DSZVlhxl.js")),__vite__mapDeps([32,1,2,29,4]),import.meta.url)),{fallback:he}),Vt=Q((()=>Z((()=>import("./FacebookTextFormat-DUrR9MIJ.js")),__vite__mapDeps([33,1,2,4,28,29,5,34,35,36,31,20,22,37,9,8,10,11,12,13,19,21,16,18,24,14,15,38,7,23,30,17,25,26,39,40]),import.meta.url)),{fallback:ge});function Xt({photo:e,allPhotos:t}){const{ti:n}=te(),[a,o]=fe("PhotoInfo."+e.id+".selectedPhotoIndex",0),r=(null==t?void 0:t[a])||e;return J.jsx(xe,{bordered:!0,pagination:!1,showHeader:!1,size:"small",style:{maxWidth:600},title:()=>J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-image fa-lg"})," ",n({en:"Photo",vi:"Ảnh"})]}),columns:[{title:n({en:"Field",vi:"Thông tin"}),dataIndex:"label",key:"label",render:(e,t)=>J.jsx("div",{style:{minWidth:100},children:t.label})},{title:n({en:"Value",vi:"Giá trị"}),dataIndex:"value",key:"value"}],dataSource:[{key:"sub_attachments",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-image"})," ",t.length||0," ",n({en:"Sub attachments",vi:"Ảnh con"})]}),value:t.length?J.jsx(Wt,{collectionName:`${e.id} - Sub attachments`,initialData:t,rowKey:e=>e.id,downloadItem:e=>({name:e.id+".jpg",url:e.uri}),hideLoadMore:!0,renderItem:(e,t)=>J.jsx(Se.Item,{className:"show-on-hover-trigger",children:J.jsx(we,{src:e.uri,style:{borderRadius:10,maxWidth:100,maxHeight:100,cursor:"pointer",...a===t&&{borderWidth:3,borderStyle:"solid",borderColor:"#1677ff"}},preview:!1,onClick:()=>{o(t)}})}),header:()=>J.jsx(oe,{type:"info",showIcon:!0,message:n({en:"Click any image to view detail",vi:"Click ảnh bất kỳ để xem chi tiết"})})}):"-"},{key:"id",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-hashtag"})," ",n({en:"Photo id",vi:"ID ảnh"})]}),value:J.jsxs(J.Fragment,{children:[r.id," ",J.jsx(ie.Link,{href:re(r.id),target:"_blank",children:J.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})})]})},{key:"size",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-ruler"})," ",n({en:"Photo size",vi:"Kích thước"})]}),value:(r.width||"?")+" x "+(r.height||"?")+(r.real_height&&r.real_width&&(r.real_height!==r.height||r.real_width!==r.width)?" => "+r.real_width+" x "+r.real_height:"")},{key:"url",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-link"})," ",n({en:"Photo url",vi:"URL ảnh"})]}),value:J.jsx(ie.Link,{href:r.url,target:"_blank",children:r.url})},{key:"caption",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-info"})," ",n({en:"Description",vi:"Mô tả"})]}),value:J.jsx(ye.TextArea,{value:r.accessibility_caption,rows:4,contentEditable:!1,style:{width:"100%"}})},{key:"accent_color",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-palette"})," ",n({en:"Accent color",vi:"Màu nổi bật"})]}),value:r.accent_color?J.jsx(At,{value:Ft(r.accent_color),showText:!0}):"-"},{key:"uri",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-image"})," ",n({en:"Photo",vi:"Ảnh"})]}),value:J.jsx(we,{src:r.uri,style:{borderRadius:10,maxWidth:400,maxHeight:400}})}]})}function Gt({sprite:t}){var n;const a=t.uris.length*t.max_number_of_images_per_sprite,o=200*(t.time_interval_between_image||1),[r,i]=e.useState(!1),[l,s]=e.useState(0);if(e.useEffect((()=>{if(a<=1||r)return;const e=setInterval((()=>{s((e=>(e+1)%a))}),o);return()=>clearInterval(e)}),[a,o,r]),a<=0||(null==(n=t.uris)?void 0:n.length)<=0)return null;const c=Math.floor(l/t.max_number_of_images_per_sprite),d=l%t.max_number_of_images_per_sprite,u=Math.floor(d/t.num_images_per_row),m=d%t.num_images_per_row,g=200/t.thumbnail_width,h=t.thumbnail_height*g;return J.jsxs(ae,{direction:"vertical",children:[J.jsx("div",{style:{width:200,height:h,backgroundImage:`url(${t.uris[c]})`,backgroundPosition:`-${m*t.thumbnail_width*g}px -${u*t.thumbnail_height*g}px`,backgroundSize:`${t.thumbnail_width*t.num_images_per_row*g}px ${t.thumbnail_height*Math.ceil(t.max_number_of_images_per_sprite/t.num_images_per_row)*g}px`,borderRadius:10,display:"inline-block"},title:`Thumbnail ${l+1} / ${a}`}),J.jsx(Pe,{min:0,max:a,value:l,step:1,onChange:e=>{i(!0),s(e)},onChangeComplete:()=>{i(!1)}})]})}const Kt=Object.freeze(Object.defineProperty({__proto__:null,default:function({target:t,postId:n,showMediaSet:a=!0}){var o,r,i;const{ti:l}=te(),{message:s}=ne(),[c,d]=fe("PostMedia."+n+".post_content",null),[u,m]=e.useState(!1),g=e.useRef(!1),h=()=>{g.current||(m(!0),g.current=!0,console.log("load post content",null==t?void 0:t.id,n),pe(null==t?void 0:t.id,n).then((e=>{console.log(t,n,e),d(e)})).catch((e=>{s.error({content:l({en:"Fail to load post content ",vi:"Lỗi tải dữ liệu bài post "})+e.message})})).finally((()=>{m(!1),g.current=!1})))};return e.useEffect((()=>{(null==t?void 0:t.id)&&n&&h()}),[n,null==t?void 0:t.id]),J.jsxs(ae,{direction:"vertical",align:"center",style:{width:"100%"},children:[J.jsx(oe,{message:J.jsxs(ae,{size:0,children:[l({en:"🚧 Work in progress",vi:"🚧 Tính năng đang phát triển"}),J.jsx(Y,{type:"link",target:"_blank",href:"https://www.facebook.com/share/p/1ATfMSpNcp/",icon:J.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"}),iconPosition:"end",children:l({en:"More inro",vi:"Xem thêm"})})]}),type:"warning"}),J.jsxs(ae.Compact,{children:[J.jsx(Y,{icon:J.jsx("i",{className:"fa-solid fa-rotate-right"}),onClick:h,loading:u,children:l({en:"Reload",vi:"Tải lại"})}),J.jsx(Y,{href:re(n),target:"_blank",icon:J.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"}),children:l({en:"View post",vi:"Xem bài viết"})})]}),u&&J.jsx(oe,{showIcon:!0,icon:J.jsx("i",{className:"fa-solid fa-spinner fa-spin"}),type:"info",message:l({en:"Loading post content...",vi:"Đang tải nội dung bài viết..."})}),c&&J.jsxs(J.Fragment,{children:[J.jsxs(ae,{wrap:!0,size:"middle",align:"start",style:{justifyContent:"center"},children:[(()=>{var e,a;const o=c.media.mediaset_token?800:c.media.photo||c.media.video?600:800;return J.jsx(xe,{bordered:!0,pagination:!1,showHeader:!1,size:"small",style:{maxWidth:o},title:()=>J.jsxs($e,{justify:"space-between",style:{margin:"5px"},gutter:[8,8],align:"middle",children:[J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-file-lines fa-lg"})," ",l({en:"Post info",vi:"Thông tin bài viết"})]}),J.jsx(Y,{icon:J.jsx("i",{className:"fa-solid fa-download"}),onClick:()=>{ve(JSON.stringify(c,null,2),(null==t?void 0:t.name)+" - "+n+".json")},children:l({en:"Download .JSON",vi:"Tải .JSON"})})]}),columns:[{title:l({en:"Field",vi:"Thông tin"}),dataIndex:"label",key:"label",render:(e,t)=>J.jsx("div",{style:{minWidth:100},children:t.label})},{title:l({en:"Value",vi:"Giá trị"}),dataIndex:"value",key:"value"}],dataSource:[{key:"postid",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-hashtag"})," ",l({en:"Post id",vi:"ID bài viết"})]}),value:J.jsxs(J.Fragment,{children:[n," ",J.jsx(ie.Link,{href:re(n),target:"_blank",children:J.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})})]})},{key:"url",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-link"})," ",l({en:"Post url",vi:"URL bài viết"})]}),value:J.jsx(ie.Link,{href:c.url,target:"_blank",children:c.url})},{key:"time",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-clock"})," ",l({en:"Created time",vi:"Thời gian tạo"})]}),value:c.created_time?J.jsxs(J.Fragment,{children:[J.jsx(se,{children:ce(c.created_time)+l({en:" ago",vi:" trước"})}),le(c.created_time)," "]}):"-"},{key:"author",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-user"})," ",l({en:"Author",vi:"Tác giả"})]}),value:J.jsx(ae,{wrap:!0,children:c.actors.map((e=>J.jsxs(ae,{size:3,children:[J.jsx(Ce,{src:de(e.id,45),size:45}),J.jsxs(ae,{direction:"vertical",size:0,children:[J.jsx(ie.Link,{href:e.url,target:"_blank",children:e.name}),J.jsxs(ie.Text,{type:"secondary",children:[" ",e.id," "]})]})]},e.id)))})},{key:"to",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-arrow-right"})," ",l({en:"Post to",vi:"Đăng đến"})]}),value:(null==(e=c.posted_to)?void 0:e.id)?J.jsxs(ae,{size:3,children:[c.posted_to.type,":",J.jsx(Ce,{src:c.posted_to.avatar,size:45}),J.jsxs(ae,{direction:"vertical",size:0,children:[J.jsx(ie.Link,{href:c.posted_to.url,target:"_blank",children:c.posted_to.name}),J.jsx(ie.Text,{type:"secondary",children:c.posted_to.id})]})]},c.posted_to.id):"-"},{key:"privacy",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-lock"})," ",l({en:"Privacy scope",vi:"Quyền riêng tư"})]}),value:c.privacy_scope},{key:"caption",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-quote-left"})," ",l({en:"Caption",vi:"Nội dung"})]}),value:(null==(a=c.text)?void 0:a.length)?J.jsx(ye.TextArea,{value:c.text,rows:4,contentEditable:!1,style:{width:"100%"}}):""},{key:"text_format",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-palette"})," ",l({en:"Text format",vi:"Định dạng"})]}),value:J.jsx(Vt,{text:c.text,text_format:c.text_format,style:{maxWidth:o-120,minHeight:450}})},{key:"hashtags",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-hashtag"})," ",l({en:"Hashtags",vi:"Hashtags"})]}),value:J.jsx(ae,{wrap:!0,size:3,children:c.hashtags.map((e=>J.jsx(ie.Link,{href:re("/hashtag/"+e.slice(1).toLowerCase()),target:"_blank",children:J.jsx(se,{children:e})},e)))})},{key:"metions",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-at"})," ",l({en:"Metions",vi:"Đề cập"})]}),value:J.jsx(ae,{wrap:!0,size:3,children:c.metions.map((e=>J.jsx(ie.Link,{href:e.url,target:"_blank",children:J.jsx(se,{children:e.name})},e.id)))})},{key:"reactions",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-heart"})," ",l({en:"Reactions",vi:"Lượt thích"})]}),value:J.jsxs(ae,{children:[J.jsx(se,{color:"green",children:ue(c.reactions.total)}),J.jsx(ae,{wrap:!0,size:3,children:c.reactions.top.map((e=>{var t;return J.jsxs(se,{children:[ue(e.count)," ",(null==(t=be[e.id])?void 0:t.emoji)||e.name]},e.id)}))})]})},{key:"comments",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-comment"})," ",l({en:"Comments",vi:"Bình luận"})]}),value:J.jsxs(J.Fragment,{children:[J.jsx(se,{color:"green",children:ue(c.comments.total)}),"+ ",l({en:"replies",vi:"trả lời"}),J.jsx(se,{color:"blue",children:c.comments.total_count-c.comments.total}),"= ",l({en:"total",vi:"tổng"}),J.jsx(se,{color:"blue",children:c.comments.total_count})]})},{key:"shares",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-share"})," ",l({en:"Shares",vi:"Chia sẻ"})]}),value:J.jsx(se,{color:"green",children:ue(c.shares)})}]})})(),(null==(o=null==c?void 0:c.media)?void 0:o.photo)&&((e,t=[])=>J.jsx(Xt,{photo:e,allPhotos:t}))(c.media.photo,c.media.subAttachments),(null==(r=null==c?void 0:c.media)?void 0:r.video)&&(p=c.media.video,J.jsx(xe,{bordered:!0,pagination:!1,showHeader:!1,size:"small",style:{maxWidth:600},title:()=>J.jsxs($e,{justify:"space-between",style:{margin:"5px"},gutter:[8,8],align:"middle",children:[J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-film fa-lg"})," ",l({en:"Video",vi:"Video"})]}),J.jsx(Y,{icon:J.jsx("i",{className:"fa-solid fa-download"}),onClick:()=>{ve(JSON.stringify(p,null,2),(null==t?void 0:t.name)+" - "+n+".video.json")},children:l({en:"Download .JSON",vi:"Tải .JSON"})})]}),columns:[{title:l({en:"Field",vi:"Thông tin"}),dataIndex:"label",key:"label",render:(e,t)=>J.jsx("div",{style:{minWidth:100},children:t.label})},{title:l({en:"Value",vi:"Giá trị"}),dataIndex:"value",key:"value"}],dataSource:[{key:"id",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-hashtag"})," ",l({en:"Video id",vi:"ID video"})]}),value:J.jsxs(J.Fragment,{children:[p.id," ",J.jsx(ie.Link,{href:re(p.id),target:"_blank",children:J.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})})]})},{key:"size",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-ruler"})," ",l({en:"Photo size",vi:"Kích thước"})]}),value:(p.width||"?")+" x "+(p.height||"?")+(p.original_height&&p.original_width&&(p.original_height!==p.height||p.original_width!==p.width)?` (${l({en:"original",vi:"gốc"})} ${p.original_width} x ${p.original_height})`:"")},{key:"duration",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-clock"})," ",l({en:"Duration",vi:"Thời lượng"})]}),value:p.duration?me(p.duration/1e3):"-"},{key:"url",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-link"})," ",l({en:"Video url",vi:"URL video"})]}),value:J.jsx(ie.Link,{href:p.url,target:"_blank",children:p.url})},{key:"publish_time",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-clock"})," ",l({en:"Publish time",vi:"Thời gian đăng"})]}),value:J.jsxs(ae,{size:0,children:[J.jsx(se,{children:ce(p.publish_time)+l({en:" ago",vi:" trước"})}),le(p.publish_time)," "]})},{key:"statuses",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-tags"})," ",l({en:"Status",vi:"Trạng thái"})]}),value:J.jsxs(ae,{wrap:!0,size:3,children:[p.can_share&&J.jsx(se,{color:"green",children:l({en:"Can share",vi:"Có thể chia sẻ"})}),p.is_live_streaming&&J.jsx(se,{color:"red",children:"Live"}),p.is_clip&&J.jsx(se,{color:"blue",children:"Clip"}),p.is_looping&&J.jsxs(se,{color:"purple",children:[l({en:"Looping",vi:"Lặp lại"})," ",p.loop_count]}),p.is_soundbites_video&&J.jsx(se,{color:"red",children:l({en:"Soundbite",vi:"Âm thanh chói tai"})}),p.is_spherical&&J.jsx(se,{color:"purple",children:l({en:"Spherical video",vi:"Video 360"})})]})},{key:"video",label:J.jsxs(ie.Text,{strong:!0,children:[J.jsx("i",{className:"fa-solid fa-film"})," ",l({en:"Video",vi:"Video"})]}),value:J.jsxs(ae,{wrap:!0,children:[J.jsx(we,{src:p.thumbnail,style:{borderRadius:10,maxWidth:300,maxHeight:300},preview:{destroyOnClose:!0,imageRender:()=>J.jsx(Bt,{info:{id:p.id,source:p.source.hd||p.source.sd,variants:p.source.variants}}),toolbarRender:()=>null}}),J.jsx(Gt,{sprite:p.sprite})]})}]}))]}),(null==(i=null==c?void 0:c.media)?void 0:i.mediaset_token)&&a&&J.jsx(qt,{name:(null==t?void 0:t.name)+" - "+n,token:c.media.mediaset_token})]})]});var p}},Symbol.toStringTag,{value:"Module"}));export{Kt as M,Ft as g};
