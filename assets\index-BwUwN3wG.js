const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./core-CRQoNxLH.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./MyApp-DW5WH4Ub.js","./posts-PKpyG5Nq.js","./core-Dw7OsFL6.js","./stories-CXRVmsoM.js","./reels-CadhT4bZ.js","./videos-D2LbKcXH.js","./posts-6XIlY8Zu.js","./getIds-DAzc-wzf.js","./albums-Bt4OWMRt.js","./comments-BafLCvtq.js","./pages-DvqVBMHR.js","./groups-D1WZyVD8.js","./posts-Fn0UXLRN.js","./highlights-BVXchAWb.js","./stories-DQ6hTlzb.js","./reels-Bl7il3t1.js","./photos-CDfkOCs1.js","./friends-CRlBxmhf.js","./gender-D7cqwEK5.js","./core--w8KKccJ.js","./core-Bg7H-yRQ.js","./core-Cg0rLx7n.js","./window-pPC-ycGO.js","./core-B0p8eH3S.js","./CodeExample-BkCKQWGR.js","./useCacheState-CAnxkewm.js","./CodeBlock-BP5ddTLM.js","./index-C5gzSBcY.js","./List-B6ZMXgC_.js","./PurePanel-BvHjNOhQ.js","./index-SRy1SMeO.js","./move-ESLFEEsv.js","./DownOutlined-B-JcS-29.js","./SearchOutlined--mXbzQ68.js","./ServerCode-fNUPazUT.js","./col-CzA09p0P.js","./row-BMfM-of4.js","./useBreakpoint-CPcdMRfj.js","./index-IxNfZAD-.js","./Dropdown-BcL-JHCf.js","./index-D7dTP8lW.js"])))=>i.map(i=>d[i]);
import{aQ as e,aS as t,r as n,aN as i,ci as r,aP as o,b0 as a,b5 as s,b1 as c,bi as d,aM as l}from"./index-Cak6rALw.js";import{W as u,d as m,u as g,t as p,S as h,A as f,B as y,c as v,aM as _,s as b,b as w,T as U,e as I,f as T,p as k,C as j}from"./MyApp-DW5WH4Ub.js";import{S as L}from"./Screen-Buq7HJpK.js";import x from"./useCacheState-CAnxkewm.js";import{T as R}from"./index-IxNfZAD-.js";import{C as D}from"./index-D7dTP8lW.js";import{S as E}from"./index-C5gzSBcY.js";import{I as F}from"./index-Bg865k-U.js";import{L as P}from"./index-jrOnBmdW.js";import{M as C}from"./index-Bx3o6rwA.js";import{D as A}from"./index-CC5caqt_.js";import{a as S}from"./index-QU7lSj_a.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./Dropdown-BcL-JHCf.js";import"./List-B6ZMXgC_.js";import"./PurePanel-BvHjNOhQ.js";import"./index-SRy1SMeO.js";import"./move-ESLFEEsv.js";import"./DownOutlined-B-JcS-29.js";import"./SearchOutlined--mXbzQ68.js";import"./EyeOutlined-CNKPohhw.js";import"./Pagination-2X6SDgot.js";import"./index-CxAc8H5Q.js";class V{constructor(){this.ws=null,this.reconnectTimeout=null,this.heartbeatInterval=null,this.reconnectAttempts=0,this.clientId=null,this.messageSubscribers=new Set,this._isConnecting=!1,this.connectionState="disconnected",this.connectionStateSubscribers=new Set,this.connectedServerUrl=null,this.connectingServerUrl=null,this.manualDisconnect=!1}static getInstance(){return V.instance||(V.instance=new V),V.instance}connect(e){var t;if((null==(t=this.ws)?void 0:t.readyState)===WebSocket.OPEN||this._isConnecting)return;this.connectingServerUrl=e;const n=e.replace("https","wss").replace("http","ws");this.manualDisconnect=!1,this._isConnecting=!0,this.setConnectionState("connecting");try{this.ws=new WebSocket(n),this.setupWebSocket()}catch(i){console.error("Error creating WebSocket:",i),this.setConnectionState("error"),this._isConnecting=!1}}disconnect(){this.manualDisconnect=!0,this.ws&&(this.ws.close(),this.ws=null,this.connectedServerUrl=null,this.connectingServerUrl=null),this.clearHeartbeat(),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this._isConnecting=!1,this.setConnectionState("disconnected")}setupWebSocket(){this.ws&&(this.ws.onopen=async()=>{var n;console.log("WebSocket connected"),this.reconnectAttempts=0;const i=e.getCache(t.getState()),r=e.setCache(t.getState()),o="ws_client_id",a=await u(),s=(null==i?void 0:i(o))||{};this.clientId=null==s?void 0:s[a],this.clientId||(this.clientId=`client_${Math.random().toString(36).substr(2,9)}_`+await u(),null==r||r(o,{...s,[a]:this.clientId})),this.connectedServerUrl=this.connectingServerUrl,this.connectingServerUrl=null,null==(n=this.ws)||n.send(JSON.stringify({type:"register",id:this.clientId})),this.setConnectionState("connected"),this.startHeartbeat(),this._isConnecting=!1},this.ws.onmessage=e=>{try{const t=JSON.parse(e.data);this.handleMessage(t)}catch(t){console.error("Error parsing WebSocket message:",t)}},this.ws.onclose=()=>{console.log("WebSocket disconnected"),this.setConnectionState(this.manualDisconnect?"disconnected":"reconnecting"),this.clearHeartbeat(),this.manualDisconnect||this.scheduleReconnect(),this._isConnecting=!1},this.ws.onerror=e=>{console.error("WebSocket error:",e),this.setConnectionState("error"),this._isConnecting=!1})}handleMessage(e){this.messageSubscribers.forEach((t=>t(e)))}startHeartbeat(){this.heartbeatInterval=setInterval((()=>{var e;(null==(e=this.ws)?void 0:e.readyState)===WebSocket.OPEN&&this.ws.send(JSON.stringify({type:"heartbeat"}))}),3e4)}clearHeartbeat(){this.heartbeatInterval&&(clearInterval(this.heartbeatInterval),this.heartbeatInterval=null)}scheduleReconnect(){const e=Math.min(1e3*Math.pow(2,this.reconnectAttempts),3e4);this.setConnectionState("reconnecting"),this.reconnectTimeout=setTimeout((()=>{this.reconnectAttempts++,this.connect(this.connectedServerUrl||"")}),e)}subscribeToMessage(e){return this.messageSubscribers.add(e),()=>this.messageSubscribers.delete(e)}sendResponse(e,t){var n;(null==(n=this.ws)?void 0:n.readyState)===WebSocket.OPEN&&this.ws.send(JSON.stringify({type:"response",requestId:e,result:t}))}sendError(e,t){var n;(null==(n=this.ws)?void 0:n.readyState)===WebSocket.OPEN&&this.ws.send(JSON.stringify({type:"response",requestId:e,error:t}))}getClientId(){return this.clientId}getConnectionStatus(){var e;return{isConnected:(null==(e=this.ws)?void 0:e.readyState)===WebSocket.OPEN,isConnecting:this._isConnecting}}setConnectionState(e){this.connectionState!==e&&(this.connectionState=e,this.connectionStateSubscribers.forEach((t=>t(e))))}getConnectionState(){return this.connectionState}get isConnected(){return"connected"===this.connectionState}get isConnecting(){return"connecting"===this.connectionState||"reconnecting"===this.connectionState}subscribeToConnectionState(e){return this.connectionStateSubscribers.add(e),e(this.connectionState),()=>this.connectionStateSubscribers.delete(e)}getConnectedServerUrl(){return this.connectedServerUrl}}const O=V.getInstance(),q=[{id:"get_threads_user_info",created:17478468e5,name:{vi:"Lấy thông tin người dùng Threads",en:"Get Threads user info"},icon:"fa-solid fa-at fa-lg",description:{vi:"Hỗ trợ lấy thông tin của người dùng Threads",en:"Support get Threads user info"},params:[{name:"url",type:"string",description:{vi:"Link hoặc Tên người dùng Threads",en:"The URL or Username of Threads user"},required:!0},{name:"raw",type:"boolean",description:{vi:"Trả về dữ liệu raw? Dữ liệu gốc từ Threads",en:"Return the raw data? Raw data from Threads"},required:!1}],response:[{name:"info",type:"object",description:{vi:"Thông tin user Threads",en:"The info of the Threads user"}}],function:async e=>{const{getThreadsUserInfo:t,getThreadsUsernameFromURL:n}=await i((async()=>{const{getThreadsUserInfo:e,getThreadsUsernameFromURL:t}=await import("./core-CRQoNxLH.js");return{getThreadsUserInfo:e,getThreadsUsernameFromURL:t}}),__vite__mapDeps([0,1,2,3]),import.meta.url);let r=e.url;r.includes("https://")&&(r=n(r));const o=await t(r);return e.raw||(o.raw=void 0),o},tags:["threads","user","info"]},{id:"get_list_threads_posts",created:17478468e5,name:{vi:"Lấy danh sách bài viết Threads",en:"Get list Threads post of user"},icon:"fa-solid fa-at fa-lg",description:{vi:"Hỗ trợ lấy danh sách bài viết Threads từ URL",en:"Support get list Threads post from URL"},params:[{name:"url",type:"string",description:{vi:"Link hoặc Tên người dùng Threads",en:"The URL or Username of Threads user"},required:!0},{name:"type",type:"string",values:["Threads","Replies","Reposts"],default:"Threads",description:{vi:"Loại bài viết 'Threads', 'Replies' hoặc 'Reposts' (mặc định 'Threads')",en:"Post type: 'Threads', 'Replies' or 'Reposts' (default 'Threads')"},required:!1},{name:"cursor",type:"string",description:{vi:"Cursor phân trang (bỏ trống để lấy trang đầu)",en:"Pagination cursor (empty for first page)"},required:!1},{name:"raw",type:"boolean",description:{vi:"Trả về dữ liệu raw? Dữ liệu gốc từ Threads",en:"Return the raw data? Raw data from Threads"},required:!1}],response:[{name:"posts",type:"array",description:{vi:"Danh sách bài viết Threads",en:"The list of the Threads post"}}],function:async e=>{const{getThreadsUserInfo:t,getThreadsUsernameFromURL:n}=await i((async()=>{const{getThreadsUserInfo:e,getThreadsUsernameFromURL:t}=await import("./core-CRQoNxLH.js");return{getThreadsUserInfo:e,getThreadsUsernameFromURL:t}}),__vite__mapDeps([0,1,2,3]),import.meta.url),{getThreadsPosts:r,ThreadsPostType:o}=await i((async()=>{const{getThreadsPosts:e,ThreadsPostType:t}=await import("./posts-PKpyG5Nq.js");return{getThreadsPosts:e,ThreadsPostType:t}}),__vite__mapDeps([4,1,2,5,3,0]),import.meta.url);let a=e.url;a.includes("threads.com")&&(a=n(a));const s=await t(a);if(!s)throw new Error("User not found");const c=e.cursor||"",d=e.type?o[e.type]:o.Threads;if(!d)throw new Error("Type mistake");const l=await r(s.id,c,d);return e.raw||l.forEach((e=>e.raw=void 0)),l},tags:["threads","post","list"]},{id:"get_ig_user_info",created:1747674e6,name:{vi:"Lấy thông tin người dùng Instagram",en:"Get Instagram user info"},icon:"fa-brands fa-instagram",description:{vi:"avatar/url/type",en:"avatar/url/type"},params:[{name:"url",type:"string",description:{vi:"Link hoặc Tên người dùng Instagram",en:"The URL or Username of Instagram user"},required:!0},{name:"raw",type:"boolean",description:{vi:"Trả về dữ liệu raw? Dữ liệu gốc từ Instagram",en:"Return the raw data? Raw data from Instagram"},required:!1}],response:[{name:"info",type:"object",description:{vi:"Thông tin người dùng Instagram",en:"The info of the Instagram user"}}],function:async e=>{const{getInstaUserInfo:t}=await i((async()=>{const{getInstaUserInfo:e}=await import("./core-Dw7OsFL6.js");return{getInstaUserInfo:e}}),__vite__mapDeps([5,1,2,3]),import.meta.url),{getInstaUsernameFromURL:n}=await i((async()=>{const{getInstaUsernameFromURL:e}=await import("./getIds-Dst2ZIJk.js");return{getInstaUsernameFromURL:e}}),[],import.meta.url);let r=e.url;r.includes("instagram.com")&&(r=n(r));const o=await t(r);return e.raw||(o.raw=void 0),o},tags:["instagram","user","info"]},{id:"get_list_ig_user_stories",created:1747674e6,name:{vi:"Lấy danh sách story của user Instagram",en:"Get Instagram user stories"},icon:"fa-brands fa-instagram",params:[{name:"url",type:"string",description:{vi:"Link hoặc Tên người dùng Instagram",en:"The URL or Username of Instagram user"},required:!0},{name:"raw",type:"boolean",description:{vi:"Trả về dữ liệu raw? Dữ liệu gốc từ Instagram",en:"Return the raw data? Raw data from Instagram"},required:!1}],response:[{name:"stories",type:"array",description:{vi:"Danh sách story của người dùng Instagram",en:"The stories of the Instagram user"}}],function:async e=>{const{getInstaUserInfo:t}=await i((async()=>{const{getInstaUserInfo:e}=await import("./core-Dw7OsFL6.js");return{getInstaUserInfo:e}}),__vite__mapDeps([5,1,2,3]),import.meta.url),{getInstaUsernameFromURL:n}=await i((async()=>{const{getInstaUsernameFromURL:e}=await import("./getIds-Dst2ZIJk.js");return{getInstaUsernameFromURL:e}}),[],import.meta.url),{getIGUserStories:r}=await i((async()=>{const{getIGUserStories:e}=await import("./stories-CXRVmsoM.js");return{getIGUserStories:e}}),__vite__mapDeps([6,1,2,3,5]),import.meta.url);let o=e.url;o.includes("instagram.com")&&(o=n(o));const a=await t(o);if(!a)throw new Error("User not found");const s=await r(a.id);return e.raw||s.forEach((e=>e.raw=void 0)),s},tags:["instagram","user","stories","list"]},{id:"get_list_ig_user_reels",created:1747674e6,name:{vi:"Lấy danh sách reels của người dùng Instagram",en:"Get Instagram user reels"},icon:"fa-solid fa-clapperboard",params:[{name:"url",type:"string",description:{vi:"Link hoặc Tên người dùng Instagram",en:"The URL or Username of Instagram user"},required:!0},{name:"cursor",type:"string",description:{vi:"Cursor phân trang (bỏ trống để lấy trang đầu)",en:"Pagination cursor (empty for first page)"},required:!1},{name:"raw",type:"boolean",description:{vi:"Trả về dữ liệu raw? Dữ liệu gốc từ Instagram",en:"Return the raw data? Raw data from Instagram"},required:!1}],response:[{name:"reels",type:"array",description:{vi:"Danh sách reels của người dùng Instagram",en:"The reels of the Instagram user"}}],function:async e=>{const{getInstaUserInfo:t}=await i((async()=>{const{getInstaUserInfo:e}=await import("./core-Dw7OsFL6.js");return{getInstaUserInfo:e}}),__vite__mapDeps([5,1,2,3]),import.meta.url),{getInstaUsernameFromURL:n}=await i((async()=>{const{getInstaUsernameFromURL:e}=await import("./getIds-Dst2ZIJk.js");return{getInstaUsernameFromURL:e}}),[],import.meta.url),{getInstaReels:r}=await i((async()=>{const{getInstaReels:e}=await import("./reels-CadhT4bZ.js");return{getInstaReels:e}}),__vite__mapDeps([7,8,1,2,3,5]),import.meta.url);let o=e.url;o.includes("instagram.com")&&(o=n(o));const a=await t(o);if(!a)throw new Error("User not found");const s=e.cursor||"",c=await r(a.id,s);return e.raw||c.forEach((e=>e.raw=void 0)),c},tags:["instagram","user","reels","list"]},{id:"get_list_ig_post",created:1747674e6,name:{vi:"Lấy danh sách bài viết Instagram",en:"Get Instagram post list"},icon:"fa-solid fa-file-lines",description:{vi:"Lấy tất cả bài viết người dùng Instagram",en:"Get all posts of the Instagram user"},params:[{name:"url",type:"string",description:{vi:"Link hoặc Username người dùng Instagram",en:"The URL or Username of Instagram user"},required:!0},{name:"cursor",type:"string",description:{vi:"Cursor phân trang (bỏ trống để lấy trang đầu)",en:"Pagination cursor (empty for first page)"},required:!1}],response:[{name:"posts",type:"array",description:{vi:"Danh sách bài viết Instagram",en:"The list of the Instagram post"}}],function:async e=>{const{getInstaUsernameFromURL:t}=await i((async()=>{const{getInstaUsernameFromURL:e}=await import("./getIds-Dst2ZIJk.js");return{getInstaUsernameFromURL:e}}),[],import.meta.url),{getInstaPosts:n}=await i((async()=>{const{getInstaPosts:e}=await import("./posts-6XIlY8Zu.js");return{getInstaPosts:e}}),__vite__mapDeps([9,3,1,2,5]),import.meta.url);let r=e.url;r.includes("instagram.com")&&(r=t(r));const o=e.cursor||"";return await n(r,o)},tags:["instagram","post","list"],changeLog:[{created:1749290915097,title:{vi:"Sửa id và mô tả",en:"Fix id and description"},description:{vi:"Sửa id từ get_ig_post_info sang get_list_ig_post",en:"Fix id from get_ig_post_info to get_list_ig_post"}}]},{id:"get_list_fb_hidden_albums",created:1750301873273,name:{vi:"Lấy danh sách album Facebook ẩn",en:"Get list hidden albums on Facebook"},description:{vi:"Hỗ trợ user/page. Biết được tổng số ảnh, ngày tạo, ngày cập nhật gần nhất.",en:"Support user/page. Know the total number of photos, creation date, and last update date."},icon:"fa-solid fa-eye-slash",params:[{name:"url",type:"string",description:{vi:"Đường dẫn / UID của người dùng/trang Facebook",en:"The URL / UID of the Facebook user/page"},required:!0}],response:[{name:"albums",type:"array",description:{vi:"Danh sách album Facebook ẩn",en:"The list of hidden albums"}}],function:async e=>{if(!e.url)throw new Error("URL is required");const{getUidFromUrl:t}=await i((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url),n=await t(e.url);if(!n)throw new Error("Cannot get uid from URL");const{getHiddenAlbums:r}=await i((async()=>{const{getHiddenAlbums:e}=await import("./albums-Bt4OWMRt.js");return{getHiddenAlbums:e}}),__vite__mapDeps([11,1,2,3]),import.meta.url);return await r(n)},tags:["facebook","album","hidden","list"]},{id:"get_list_fb_album_media",created:1750180307080,name:{vi:"Lấy danh sách ảnh/video của album Facebook",en:"Get list photos/videos of album Facebook"},description:{vi:"Hỗ trợ user / group / page. Video chỉ trả về thumbnail, dùng api get_fb_video_info để lấy thông tin video từ id",en:"Support user / group / page. Video only returns thumbnail, use api get_fb_video_info to get video info from id"},icon:"fa-solid fa-images",params:[{name:"album_id",type:"string",description:{vi:"ID của album Facebook",en:"The ID of the Facebook album"},required:!0},{name:"cursor",type:"string",description:{vi:"Cursor phân trang (bỏ trống để lấy trang đầu)",en:"Pagination cursor (empty for first page)"},required:!1}],response:[{name:"medias",type:"array",description:{vi:"Danh sách ảnh/video của album Facebook",en:"The list of photos/videos of the album"}}],function:async e=>{var t,n;const r=null==(n=null==(t=e.album_id)?void 0:t.match(/\d+/))?void 0:n[0];if(!r)throw new Error("Album ID is required");const{getAlbumPhoto:o}=await i((async()=>{const{getAlbumPhoto:e}=await import("./albums-Bt4OWMRt.js");return{getAlbumPhoto:e}}),__vite__mapDeps([11,1,2,3]),import.meta.url);return await o({albumId:r,fromId:e.cursor})},tags:["facebook","album","media","list"]},{id:"get_list_fb_albums",created:1750179797659,name:{vi:"Lấy danh sách album Facebook",en:"Get list albums Facebook"},description:{vi:"Hỗ trợ user / group / page",en:"Support user / group / page"},icon:"fa-solid fa-images",params:[{name:"url",type:"string",description:{vi:"Đường dẫn / UID của người dùng / nhóm / trang Facebook",en:"The URL / UID of the Facebook user / group / page"},required:!0},{name:"cursor",type:"string",description:{vi:"Cursor phân trang (bỏ trống để lấy trang đầu)",en:"Pagination cursor (empty for first page)"},required:!1}],response:[{name:"albums",type:"array",description:{vi:"Danh sách album Facebook",en:"The list of albums"}}],function:async e=>{const{getUidFromUrl:t}=await i((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url),n=await t(e.url);if(!n)throw new Error("Cannot get uid from URL");const{getEntityAbout:r}=await i((async()=>{const{getEntityAbout:e}=await import("./MyApp-DW5WH4Ub.js").then((e=>e.aV));return{getEntityAbout:e}}),__vite__mapDeps([3,1,2]),import.meta.url),o=await r(n);if(!o)throw new Error("Cannot get user info from UID");const{getUserAlbum:a,getGroupAlbum:s}=await i((async()=>{const{getUserAlbum:e,getGroupAlbum:t}=await import("./albums-Bt4OWMRt.js");return{getUserAlbum:e,getGroupAlbum:t}}),__vite__mapDeps([11,1,2,3]),import.meta.url),c="user"===o.type?await a({uid:n,cursor:e.cursor}):await s({groupId:n,cursor:e.cursor});return(null==c?void 0:c.albums)||[]},tags:["facebook","album","list"]},{id:"get_list_fb_comment_reply",created:1749397378781,name:{vi:"Lấy danh sách bình luận trả lời",en:"Get list replies comment"},description:{vi:"Lấy danh sách bình luận trả lời của bình luận fb bất kỳ",en:"Get list replies of any fb post's comment"},icon:"fa-solid fa-comment-dots",params:[{name:"comment_id",type:"string",description:{vi:"ID của bình luận cần lấy trả lời (id dạng base64)",en:"The ID of the comment to get replies (id in base64)"},required:!0},{name:"expansion_token",type:"string",description:{vi:"Expansion token của bình luận (Dùng api get_list_fb_comment để lấy)",en:"The expansion token of the comment (Use api get_list_fb_comment to get)"},required:!0},{name:"cursor",type:"string",description:{vi:"Cursor phân trang (bỏ trống để lấy trang đầu)",en:"Pagination cursor (empty for first page)"},required:!1}],response:[{name:"replies",type:"array",description:{vi:"Danh sách bình luận trả lời",en:"List of replies"}}],function:async e=>{const{getReplyComments:t}=await i((async()=>{const{getReplyComments:e}=await import("./comments-BafLCvtq.js");return{getReplyComments:e}}),__vite__mapDeps([12,3,1,2,8]),import.meta.url);return console.log("params",e),await t({commentId:e.comment_id,expansionToken:e.expansion_token,cursor:e.cursor})},tags:["facebook","post","comment","reply","list"]},{id:"get_list_fb_pages_other",created:1749061768257,name:{vi:"Lấy danh sách trang fb người dùng thích",en:"Get list fb pages that the user liked"},icon:"fa-solid fa-thumbs-up",description:{vi:"Hỗ trợ lấy danh sách trang fb công khai người dùng thích",en:"Support get list fb public pages that the user liked"},params:[{name:"url",type:"string",description:{vi:"Đường dẫn / UID của người dùng Facebook",en:"The URL / UID of the Facebook user"},required:!0},{name:"cursor",type:"string",description:{vi:"Cursor phân trang (bỏ trống để lấy trang đầu)",en:"Pagination cursor (empty for first page)"},required:!1}],response:[{name:"pages",type:"array",description:{vi:"Danh sách trang fb đối phương thích",en:"The list of fb pages that the user liked"}}],function:async e=>{const{getUidFromUrl:t}=await i((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url),n=await t(e.url);if(!n)throw new Error("Cannot get uid from URL");const{getOtherLikedPages:r}=await i((async()=>{const{getOtherLikedPages:e}=await import("./pages-DvqVBMHR.js");return{getOtherLikedPages:e}}),__vite__mapDeps([13,3,1,2]),import.meta.url);return await r({uid:n,cursor:e.cursor})},tags:["facebook","page","liked","list"]},{id:"get_list_fb_groups_other",created:1749060962019,name:{vi:"Lấy danh sách nhóm fb người dùng tham gia",en:"Get all fb groups that the user joined"},icon:"fa-solid fa-users",description:{vi:"Hỗ trợ lấy danh sách nhóm công khai, khoá trang cá nhân cũng lấy được",en:"Support public groups, even with user locked profile"},params:[{name:"url",type:"string",description:{vi:"Đường dẫn / UID của người dùng Facebook",en:"The URL / UID of the Facebook user"},required:!0},{name:"cursor",type:"string",description:{vi:"Cursor phân trang (bỏ trống để lấy trang đầu)",en:"Pagination cursor (empty for first page)"},required:!1}],response:[{name:"groups",type:"array",description:{vi:"Danh sách nhóm fb đối phương tham gia",en:"The list of fb groups that the user joined"}}],function:async e=>{const{getUidFromUrl:t}=await i((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url),n=await t(e.url);if(!n)throw new Error("Cannot get uid from URL");const{getOtherJoinedGroups:r}=await i((async()=>{const{getOtherJoinedGroups:e}=await import("./groups-D1WZyVD8.js");return{getOtherJoinedGroups:e}}),__vite__mapDeps([14,3,1,2]),import.meta.url);return await r({uid:n,cursor:e.cursor})},tags:["facebook","group","joined","list"]},{id:"get_fb_post_info",created:1749019397729,name:{vi:"Lấy thông tin bài viết Facebook",en:"Get Facebook post info"},icon:"fa-solid fa-file-lines",description:{vi:"người đăng, nội dung, ảnh, video, like, comments,...",en:"author, content, photos, videos, likes, comments,..."},params:[{name:"url",type:"string",description:{vi:"Đường dẫn / UID của bài viết Facebook",en:"The URL / UID of the Facebook post"},required:!0}],response:[{name:"info",type:"object",description:{vi:"Thông tin chi tiết về bài viết",en:"The detailed info of the post"}}],function:async e=>{const{getPostContent:t}=await i((async()=>{const{getPostContent:e}=await import("./posts-Fn0UXLRN.js");return{getPostContent:e}}),__vite__mapDeps([15,1,2,3,8]),import.meta.url),{getUidFromUrl:n,getPostIdFromUrl:r}=await i((async()=>{const{getUidFromUrl:e,getPostIdFromUrl:t}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e,getPostIdFromUrl:t}}),__vite__mapDeps([10,1,2,3]),import.meta.url),o=await n(e.url);if(!o)throw new Error("Cannot get uid from URL");const a=await r(e.url);if(!a)throw new Error("Cannot get postId from URL");return await t(o,a)},tags:["facebook","post","info"]},{id:"get_list_fb_mediaset",created:1749059976524,name:{vi:"Lấy mọi ảnh/video từ mediaset_token",en:"Get all medias from mediaset_token"},icon:"fa-solid fa-file-lines",description:{vi:"mediaset_token thường lấy từ bài viết nhiều ảnh/video, hoặc album facebook",en:"mediaset_token usually get from post with multiple medias, or facebook album"},params:[{name:"mediaset_token",type:"string",description:{vi:"mediaset_token",en:"The mediaset_token"},required:!0},{name:"cursor",type:"string",description:{vi:"Cursor phân trang (bỏ trống để lấy trang đầu)",en:"Pagination cursor (empty for first page)"},required:!1}],response:[{name:"medias",type:"array",description:{vi:"Danh sách ảnh/video",en:"The list of medias"}}],function:async e=>{const{getMediasetContent:t}=await i((async()=>{const{getMediasetContent:e}=await import("./posts-Fn0UXLRN.js");return{getMediasetContent:e}}),__vite__mapDeps([15,1,2,3,8]),import.meta.url);return t(e.mediaset_token,e.cursor)},tags:["facebook","media","list"]},{id:"get_fb_entity_info",created:1747674e6,name:{vi:"Lấy thông tin đối tượng Facebook",en:"Get Facebook entity info"},icon:"fa-solid fa-user",description:{vi:"avatar/url/type (user/group/page/...)",en:"avatar/url/type (user/group/page/...)"},params:[{name:"url",type:"string",description:{vi:"Đường dẫn / UID của User/Group/Page",en:"The URL / UID of the User/Group/Page"},required:!0},{name:"raw",type:"boolean",description:{vi:"Trả về dữ liệu raw? Dữ liệu gốc từ Facebook",en:"Return the raw data? Raw data from Facebook"},required:!1}],response:[{name:"info",type:"object",description:{vi:"Thông tin cơ bản của đối tượng (avatar, url, type)",en:"The simple info of the entity (avatar, url, type)"}}],function:async e=>{const{getEntityAbout:t}=await i((async()=>{const{getEntityAbout:e}=await import("./MyApp-DW5WH4Ub.js").then((e=>e.aV));return{getEntityAbout:e}}),__vite__mapDeps([3,1,2]),import.meta.url),{getUidFromUrl:n}=await i((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url),r=await n(e.url);if(!r)throw new Error("Cannot get uid from URL");const o=await t(r);return e.raw||(o.raw=void 0),o},tags:["facebook","user","info"]},{id:"get_fb_user_info",created:1747674e6,name:{vi:"Lấy thông tin người dùng Facebook",en:"Get Facebook user info"},icon:"fa-solid fa-user",description:{vi:"",en:""},params:[{name:"url",type:"string",description:{vi:"Đường dẫn / UID của Người dùng Facebook",en:"The URL / UID of the Facebook User"},required:!0}],response:[{name:"info",type:"object",description:{vi:"Thông tin chi tiết User (tên, tên phụ, giới tính, avatar, ảnh bìa, ...)",en:"The detailed info of the user (name, alternate name, gender, avatar, cover, ...)"}}],function:async e=>{const{getUserInfoFromUid:t}=await i((async()=>{const{getUserInfoFromUid:e}=await import("./MyApp-DW5WH4Ub.js").then((e=>e.aV));return{getUserInfoFromUid:e}}),__vite__mapDeps([3,1,2]),import.meta.url),{getUidFromUrl:n}=await i((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url),r=await n(e.url);if(!r)throw new Error("Cannot get uid from URL");return t(r)},tags:["facebook","user","info"]},{id:"get_fb_uid",created:1747674e6,name:{vi:"Lấy UID Facebook từ URL",en:"Get Facebook UID from URL"},icon:"fa-solid fa-hashtag",description:{vi:"Hỗ trợ User/Page/Group",en:"Support User/Page/Group"},params:[{name:"url",type:"string",description:{vi:"Đường dẫn của User/Page/Group",en:"The URL of the User/Page/Group"},required:!0}],response:[{name:"uid",type:"string",description:{vi:"UID",en:"UID"}}],function:async e=>{const{getUidFromUrl:t}=await i((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url);return t(e.url)},tags:["facebook","uid"]},{id:"get_fb_post_id",created:1747674e6,name:{vi:"Lấy ID bài viết Facebook từ URL",en:"Get Facebook post ID from URL"},icon:"fa-solid fa-hashtag",description:{vi:"Hỗ trợ mọi bài viết",en:"Support all posts"},params:[{name:"url",type:"string",description:{vi:"Đường dẫn bài viết Facebook",en:"The URL of the Facebook post"},required:!0}],response:[{name:"post_id",type:"string",description:{vi:"ID bài viết",en:"Post ID"}}],function:async e=>{const{getPostIdFromUrl:t}=await i((async()=>{const{getPostIdFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getPostIdFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url);return t(e.url)},tags:["facebook","post","id"]},{id:"get_fb_video_id",created:1747674e6,name:{vi:"Lấy ID video Facebook từ URL",en:"Get Facebook video ID from URL"},icon:"fa-solid fa-hashtag",description:{vi:"Hỗ trợ Video/Story/Post",en:"Support Video/Story/Post"},params:[{name:"url",type:"string",description:{vi:"Đường dẫn của Facebook video",en:"The URL of the Facebook video"},required:!0}],response:[{name:"video_id",type:"string",description:{vi:"ID video",en:"Video ID"}}],function:async e=>{const{getVideoIdFromUrl:t}=await i((async()=>{const{getVideoIdFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getVideoIdFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url);return t(e.url)},tags:["facebook","video","id"]},{id:"call_fb_graphql",created:1747674e6,name:{vi:"Gọi GraphQL API Facebook",en:"Call Facebook GraphQL API"},icon:"fa-solid fa-code",description:{vi:"Chỉ cần truyền doc_id, và variables",en:"Only need to pass doc_id, and variables"},params:[{name:"doc_id",type:"string",description:{vi:"doc_id của GraphQL API",en:"The doc_id of the GraphQL API"},required:!0},{name:"variables",type:"object",description:{vi:"Tham số variables của GraphQL API",en:"The variables data of the GraphQL API"},required:!0}],response:[{name:"data",type:"object",description:{vi:"Dữ liệu Facebook trả về",en:"The data returned from Facebook"}}],function:async e=>{const{fetchGraphQl:t}=await i((async()=>{const{fetchGraphQl:e}=await import("./MyApp-DW5WH4Ub.js").then((e=>e.aV));return{fetchGraphQl:e}}),__vite__mapDeps([3,1,2]),import.meta.url);return await t(e)},tags:["facebook","graphql"]},{id:"get_fb_video_info",created:1747674e6,name:{vi:"Lấy thông tin video Facebook",en:"Get Facebook video info"},icon:"fa-solid fa-video",description:{vi:"Hỗ trợ Video/Post/Comment",en:"Support Video/Post/Comment"},params:[{name:"url",type:"string",description:{vi:"Đường dẫn / ID của video",en:"The URL / ID of the video"},required:!0}],response:[{name:"info",type:"object",description:{vi:"Thông tin của video",en:"The info of the video"}}],function:async e=>{const{getVideoIdFromUrl:t}=await i((async()=>{const{getVideoIdFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getVideoIdFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url),n=await t(e.url);if(!n)throw new Error("Cannot get video ID from URL");const{getVideoInfo:r}=await i((async()=>{const{getVideoInfo:e}=await import("./videos-D2LbKcXH.js");return{getVideoInfo:e}}),__vite__mapDeps([8,1,2,3]),import.meta.url);return r(n)},tags:["facebook","video","info"],changeLog:[{created:1750301432373,title:{vi:"Hỗ trợ link bài viết Facebook chứa video",en:"Support url of facebook post (post have video)"},description:{vi:"Có thể điền link bài viết (có video), thay vì link video",en:"Can use post url (include video), instead of video url"}}]},{id:"get_list_fb_highlights",created:1747674e6,name:{vi:"Lấy danh sách Tin nổi bật Facebook",en:"Get list Highlight story on Facebook"},icon:"fa-solid fa-star",description:{vi:"Trả về thông tin cơ bản của các Tin nổi bật",en:"Return the basic info of the highlights"},params:[{name:"url",type:"string",description:{vi:"Đường dẫn / UID của người dùng Facebook",en:"The URL / UID of the Facebook user"},required:!0},{name:"cursor",type:"string",description:{vi:"Cursor phân trang (bỏ trống để lấy trang đầu)",en:"Pagination cursor (empty for first page)"},required:!1}],response:[{name:"info",type:"array",description:{vi:"Thông tin của các Tin nổi bật",en:"The basic info of the highlights"}}],function:async e=>{const{getUidFromUrl:t}=await i((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url),{getHighlights:n}=await i((async()=>{const{getHighlights:e}=await import("./highlights-BVXchAWb.js");return{getHighlights:e}}),__vite__mapDeps([16,3,1,2]),import.meta.url),r=await t(e.url);if(!r)throw new Error("Cannot get uid from URL");const o=e.cursor||null;return await n(r,o)},tags:["facebook","highlights","list"]},{id:"get_fb_highlight_info",created:1747674e6,name:{vi:"Lấy danh sách story trong một Tin nổi bật Facebook",en:"Get list story in one highlight on Facebook"},icon:"fa-solid fa-star",params:[{name:"url",type:"string",description:{vi:"Đường dẫn của video tin nổi bật",en:"The URL of the highlights video"},required:!0}],response:[{name:"info",type:"array",description:{vi:"Thông tin của video tin nổi bật",en:"The info of the highlights video"}}],function:async e=>{const{getStoryBucketIdFromUrl:t}=await i((async()=>{const{getStoryBucketIdFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getStoryBucketIdFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url),n=await t(e.url);if(!n)throw new Error("Cannot get story ID from URL");const{getStoriesBucketsData:r}=await i((async()=>{const{getStoriesBucketsData:e}=await import("./stories-DQ6hTlzb.js");return{getStoriesBucketsData:e}}),__vite__mapDeps([17,3,1,2]),import.meta.url);return await r(n)},tags:["facebook","highlights","video","list"]},{id:"get_list_fb_user_reels",created:17478468e5,name:{vi:"Lấy danh sách Reels của người dùng Facebook",en:"Get list of Facebook user reels"},icon:"fa-solid fa-clapperboard",description:{vi:"Hỗ trợ lấy danh sách Reels người dùng",en:"Support getting user reels roll list"},params:[{name:"url",type:"string",description:{vi:"Đường dẫn / UID của người dùng Facebook",en:"The URL / UID of the Facebook user"},required:!0},{name:"cursor",type:"string",description:{vi:"Cursor phân trang (bỏ trống để lấy trang đầu)",en:"Pagination cursor (empty for first page)"},required:!1}],response:[{name:"reels",type:"array",description:{vi:"Danh sách của Reels người dùng",en:"List of the user reels"}}],function:async e=>{const{getUidFromUrl:t}=await i((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url),{getUserReels:n}=await i((async()=>{const{getUserReels:e}=await import("./reels-Bl7il3t1.js");return{getUserReels:e}}),__vite__mapDeps([18,1,2,3]),import.meta.url),r=await t(e.url);if(!r)throw new Error("Cannot get UID from URL");const o=e.cursor||"";return await n({id:r,cursor:o})},tags:["facebook","user","reels","list"]},{id:"get_list_fb_user_photos",created:17478468e5,name:{vi:"Lấy danh sách hình ảnh người dùng Facebook",en:"Get list photos of Facebook user"},icon:"fa-solid fa-image",description:{vi:"Hỗ trợ lấy danh sách hình ảnh người dùng",en:"Support getting user photos list"},params:[{name:"url",type:"string",description:{vi:"Đường dẫn / UID của người dùng Facebook",en:"The URL / UID of the Facebook user"},required:!0},{name:"type",type:"string",valuesFn:async()=>{const{PhotoCollectionType:e,PhotoCollectionTypeLang:t}=await i((async()=>{const{PhotoCollectionType:e,PhotoCollectionTypeLang:t}=await import("./photos-CDfkOCs1.js");return{PhotoCollectionType:e,PhotoCollectionTypeLang:t}}),__vite__mapDeps([19,3,1,2]),import.meta.url);return Object.entries(e).map((([e,n])=>({name:t[n],value:n})))},defaultFn:async()=>{const{PhotoCollectionType:e}=await i((async()=>{const{PhotoCollectionType:e}=await import("./photos-CDfkOCs1.js");return{PhotoCollectionType:e}}),__vite__mapDeps([19,3,1,2]),import.meta.url);return e.OWNER},description:{vi:"Loại hình ảnh",en:"The type of the photos"},required:!1},{name:"cursor",type:"string",description:{vi:"Cursor phân trang (bỏ trống để lấy trang đầu)",en:"Pagination cursor (empty for first page)"},required:!1}],response:[{name:"photos",type:"array",description:{vi:"Danh sách hình ảnh của người dùng",en:"List of user photos"}}],function:async e=>{const{getUidFromUrl:t}=await i((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url),{getUserPhotos:n,PhotoCollectionType:r}=await i((async()=>{const{getUserPhotos:e,PhotoCollectionType:t}=await import("./photos-CDfkOCs1.js");return{getUserPhotos:e,PhotoCollectionType:t}}),__vite__mapDeps([19,3,1,2]),import.meta.url),o=await t(e.url);if(!o)throw new Error("Cannot get UID from URL");const a=e.cursor||"",s=r[e.type]||r.OWNER;return await n({id:o,cursor:a,type:s})},tags:["facebook","user","photo","list"]},{id:"add_fb_friend",created:17478468e5,name:{vi:"Thêm bạn bè trên Facebook",en:"Add friend on Facebook"},icon:"fa-solid fa-user-plus",description:{vi:"Dùng tài khoản Facebook đang đăng nhập để gửi",en:"Use the Facebook account logged in to send"},params:[{name:"targetUid",type:"string",description:{vi:"Đường dẫn / UID của người dùng Facebook cần kết bạn",en:"The URL / UID of Facebook profile need add friend"},required:!0}],response:[{name:"result",type:"object",description:{vi:"Kết quả khi gửi lời mời kết bạn",en:"Results when sending invitation to add friends"}}],function:async e=>{const{getMyUid:t}=await i((async()=>{const{getMyUid:e}=await import("./MyApp-DW5WH4Ub.js").then((e=>e.aV));return{getMyUid:e}}),__vite__mapDeps([3,1,2]),import.meta.url),{getUidFromUrl:n}=await i((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url),{addFriend:r}=await i((async()=>{const{addFriend:e}=await import("./friends-CRlBxmhf.js");return{addFriend:e}}),__vite__mapDeps([20,3,1,2,21]),import.meta.url),o=await t();if(!o)throw new Error("Cannot get your logged in Facebook UID");const a=await n(e.targetUid);if(!a)throw new Error("Cannot get target UID from URL");return await r({myUid:o,targetUid:a})},tags:["facebook","friend","add"]},{id:"remove_fb_friend",created:17478468e5,name:{vi:"Xóa bạn bè trên Facebook",en:"Remove Facebook friend"},icon:"fa-solid fa-user-minus",description:{vi:"Dùng tài khoản Facebook đang đăng nhập để xóa",en:"Use the Facebook account logged in to remove"},params:[{name:"targetUid",type:"string",description:{vi:"Đường dẫn / UID của người dùng Facebook cần xóa kết bạn (có trong danh sách bạn bè)",en:"The URL / UID of Facebook profile need remove friend (in friends list)"},required:!0}],response:[{name:"result",type:"object",description:{vi:"Kết quả khi gửi xóa kết bạn",en:"Results when sending to remove friends"}}],function:async e=>{const{getMyUid:t}=await i((async()=>{const{getMyUid:e}=await import("./MyApp-DW5WH4Ub.js").then((e=>e.aV));return{getMyUid:e}}),__vite__mapDeps([3,1,2]),import.meta.url),{getUidFromUrl:n}=await i((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url),{unfriend:r}=await i((async()=>{const{unfriend:e}=await import("./friends-CRlBxmhf.js");return{unfriend:e}}),__vite__mapDeps([20,3,1,2,21]),import.meta.url),o=await t();if(!o)throw new Error("Cannot get your logged in Facebook UID");const a=await n(e.targetUid);if(!a)throw new Error("Cannot get target UID from URL");return await r({myUid:o,targetUid:a})},tags:["facebook","friend","remove"]},{id:"poke_fb_friend",created:17478468e5,name:{vi:"Chọc bạn bè trên Facebook",en:"Poke Facebook friend"},icon:"fa-solid fa-hand-point-up",description:{vi:"Dùng tài khoản Facebook đang đăng nhập để chọc",en:"Use the Facebook account logged in to poke"},params:[{name:"targetUid",type:"string",description:{vi:"Đường dẫn / UID của người dùng Facebook cần chọc (có trong danh sách bạn bè)",en:"The URL / UID of Facebook profile need poke (in friends list)"},required:!0}],response:[{name:"result",type:"object",description:{vi:"Kết quả khi gửi chọc",en:"Results when sending to poke"}}],function:async e=>{const{getMyUid:t}=await i((async()=>{const{getMyUid:e}=await import("./MyApp-DW5WH4Ub.js").then((e=>e.aV));return{getMyUid:e}}),__vite__mapDeps([3,1,2]),import.meta.url),{getUidFromUrl:n}=await i((async()=>{const{getUidFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getUidFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url),{pokeFriend:r}=await i((async()=>{const{pokeFriend:e}=await import("./friends-CRlBxmhf.js");return{pokeFriend:e}}),__vite__mapDeps([20,3,1,2,21]),import.meta.url),o=await t();if(!o)throw new Error("Cannot get your logged in Facebook UID");const a=await n(e.targetUid);if(!a)throw new Error("Cannot get target UID from URL");return await r({myUid:o,targetUid:a})},tags:["facebook","friend","poke"]},{id:"get_list_fb_all_friend",created:17478468e5,name:{vi:"Lấy danh sách tất cả bạn bè Facebook",en:"Get list all friends Facebook"},icon:"fa-solid fa-users",description:{vi:"Bạn bè của tài khoản Facebook đang đăng nhập",en:"Friends of the Facebook account logged in"},params:[],response:[{name:"friends",type:"array",description:{vi:"Danh sách kết quả tất cả bạn bè",en:"List results all friends"}}],function:async e=>{const{getMyUid:t}=await i((async()=>{const{getMyUid:e}=await import("./MyApp-DW5WH4Ub.js").then((e=>e.aV));return{getMyUid:e}}),__vite__mapDeps([3,1,2]),import.meta.url),{getAllFriends:n}=await i((async()=>{const{getAllFriends:e}=await import("./friends-CRlBxmhf.js");return{getAllFriends:e}}),__vite__mapDeps([20,3,1,2,21]),import.meta.url),r=await t();if(!r)throw new Error("Cannot get your logged in Facebook UID");return await n({myUid:r})},tags:["facebook","friend","list"]},{id:"get_list_fb_comment",created:*************,name:{vi:"Lấy danh sách bình luận của bài viết Facebook",en:"Get list comments of Facebook post"},description:{vi:"Hỗ trợ bình luận bậc 1 (không bao gồm trả lời)",en:"Support comments level 1 (not included replies)"},icon:"fa-solid fa-comment",params:[{name:"url",type:"string",description:{vi:"Đường dẫn / ID của bài viết",en:"The URL / ID of the post"},required:!0},{name:"type",type:"string",valuesFn:async()=>{const{CommentIntentToken:e,CommentIntentTokenNames:t}=await i((async()=>{const{CommentIntentToken:e,CommentIntentTokenNames:t}=await import("./comments-BafLCvtq.js");return{CommentIntentToken:e,CommentIntentTokenNames:t}}),__vite__mapDeps([12,3,1,2,8]),import.meta.url);return Object.entries(e).map((([e,n])=>({name:t[n],value:n})))},defaultFn:async()=>{const{CommentIntentToken:e}=await i((async()=>{const{CommentIntentToken:e}=await import("./comments-BafLCvtq.js");return{CommentIntentToken:e}}),__vite__mapDeps([12,3,1,2,8]),import.meta.url);return e.RECENT_ACTIVITY},description:{vi:"Loại comment (mặc định Mới nhất)",en:"Comment type (default Newest)"},required:!1},{name:"cursor",type:"string",description:{vi:"Cursor phân trang (bỏ trống để lấy trang đầu)",en:"Pagination cursor (empty for first page)"},required:!1}],response:[{name:"comments",type:"array",description:{vi:"Danh sách comment",en:"List of comments"}}],function:async e=>{const{getPostIdFromUrl:t}=await i((async()=>{const{getPostIdFromUrl:e}=await import("./getIds-DAzc-wzf.js");return{getPostIdFromUrl:e}}),__vite__mapDeps([10,1,2,3]),import.meta.url),n=await t(e.url);if(!n)throw new Error("Cannot get post ID from URL");const{getPostComments:r}=await i((async()=>{const{getPostComments:e}=await import("./comments-BafLCvtq.js");return{getPostComments:e}}),__vite__mapDeps([12,3,1,2,8]),import.meta.url);return await r({postId:n,cursor:e.cursor,type:e.type})},tags:["facebook","post","comment","list"],changeLog:[{created:1749061384589,title:{vi:"Đổi id",en:"Change id"},description:{vi:"Đổi id từ get_fb_comment -> get_list_fb_comment",en:"Change id from get_fb_comment -> get_list_fb_comment"}}]},{id:"get_bilibili_video_info",created:1747674e6,name:{vi:"Lấy thông tin video Bilibili",en:"Get Bilibili video info"},icon:"fa-solid fa-video",description:{vi:"Lấy thông tin video Bilibili",en:"Get Bilibili video info"},params:[{name:"url",type:"string",description:{vi:"Đường dẫn của video",en:"The URL of the video"},required:!0}],response:[{name:"info",type:"object",description:{vi:"Thông tin của video",en:"The info of the video"}}],function:async e=>{const{getBilibiliVideoFromURL:t}=await i((async()=>{const{getBilibiliVideoFromURL:e}=await import("./core--w8KKccJ.js");return{getBilibiliVideoFromURL:e}}),__vite__mapDeps([22,1,2]),import.meta.url);return await t(e.url)},tags:["bilibili","video"]},{id:"get_tiktok_video_info",created:1747674e6,name:{vi:"Lấy thông tin video Tiktok",en:"Get Tiktok video info"},icon:"fa-brands fa-tiktok",params:[{name:"url",type:"string",description:{vi:"Đường dẫn của video",en:"The URL of the video"},required:!0}],response:[{name:"info",type:"object",description:{vi:"Thông tin của video",en:"The info of the video"}}],function:async e=>{const{getTiktokVideoFromURL:t}=await i((async()=>{const{getTiktokVideoFromURL:e}=await import("./core-Bg7H-yRQ.js");return{getTiktokVideoFromURL:e}}),__vite__mapDeps([23,1,2,3]),import.meta.url);return await t(e.url)},tags:["tiktok","video","info"]},{id:"get_tiktok_user_info",created:1747674e6,name:{vi:"Lấy thông tin người dùng Tiktok",en:"Get Tiktok user info"},description:"id, nickname, secUid, avatar, created, verified, language, bio ...",icon:"fa-brands fa-tiktok",params:[{name:"url",type:"string",description:{vi:"Đường dẫn của người dùng",en:"The URL of the user"},required:!0}],response:[{name:"info",type:"object",description:{vi:"Thông tin của người dùng",en:"The info of the user"}}],function:async e=>{const{getTiktokUserInfo:t}=await i((async()=>{const{getTiktokUserInfo:e}=await import("./core-Bg7H-yRQ.js");return{getTiktokUserInfo:e}}),__vite__mapDeps([23,1,2,3]),import.meta.url);return await t(e.url)},tags:["tiktok","user","info"]},{id:"get_ggdrive_video_info",created:1747674e6,name:{vi:"Lấy thông tin video Google Drive",en:"Get Google Drive video info"},description:{vi:"Hỗ trợ cả video bị chặn tải",en:"Support videos dont have download button"},icon:"fa-brands fa-google-drive",params:[{name:"url",type:"string",description:{vi:"Đường dẫn của video",en:"The URL of the video"},required:!0}],response:[{name:"info",type:"object",description:{vi:"Thông tin của video",en:"The info of the video"}}],function:async e=>{const{getGoogleDriveVideoFromURL:t}=await i((async()=>{const{getGoogleDriveVideoFromURL:e}=await import("./core-Cg0rLx7n.js");return{getGoogleDriveVideoFromURL:e}}),__vite__mapDeps([24,1,2,3,25]),import.meta.url);return await t(e.url)},tags:["ggdrive","video","info"]},{id:"extract_douyin_url",created:1747674e6,name:{vi:"Tách URL Douyin từ nội dung",en:"Extract Douyin URL from text"},description:{vi:"Hỗ trợ đường dẫn chia sẻ Douyin (thường bị dính tên video, tên user, ...)",en:"Support shared Douyin link (often injected video name, user name, ...)"},icon:"fa-solid fa-link",params:[{name:"text",type:"string",description:{vi:"Nội dung chứa URL Douyin",en:"The text contains Douyin URL"},required:!0}],response:[{name:"url",type:"string",description:{vi:"URL Douyin",en:"The Douyin URL"}}],function:async e=>{const{extractRealDouyinUrl:t}=await i((async()=>{const{extractRealDouyinUrl:e}=await import("./core-B0p8eH3S.js");return{extractRealDouyinUrl:e}}),__vite__mapDeps([26,1,2]),import.meta.url);return await t(e.text)},tags:["extract","douyin","url"]},{id:"get_douyin_video_id",created:1747674e6,name:{vi:"Lấy ID video Douyin từ URL",en:"Get Douyin video ID from URL"},description:{vi:"Hỗ trợ đường dẫn chia sẻ Douyin (thường bị dính tên video, tên user, ...)",en:"Support shared Douyin link (often injected video name, user name, ...)"},icon:"fa-solid fa-hashtag",params:[{name:"url",type:"string",description:{vi:"URL video Douyin",en:"The Douyin video URL "},required:!0}],response:[{name:"id",type:"string",description:{vi:"ID video Douyin",en:"The Douyin video ID"}}],function:async e=>{const{extractVideoAwemeIdFromUrl:t,extractRealDouyinUrl:n}=await i((async()=>{const{extractVideoAwemeIdFromUrl:e,extractRealDouyinUrl:t}=await import("./core-B0p8eH3S.js");return{extractVideoAwemeIdFromUrl:e,extractRealDouyinUrl:t}}),__vite__mapDeps([26,1,2]),import.meta.url);return t(await n(e.url))},tags:["douyin","video","id"]},{id:"get_douyin_video_info",created:1747674e6,name:{vi:"Lấy thông tin video Douyin",en:"Get Douyin video info"},description:{vi:"Hỗ trợ đường dẫn chia sẻ Douyin (thường bị dính tên video, tên user, ...)",en:"Support shared Douyin link (often injected video name, user name, ...)"},icon:"fa-brands fa-tiktok",params:[{name:"url",type:"string",description:{vi:"URL video Douyin, hỗ trợ đường dẫn chia sẻ (dính thông tin video, tên user, ...)",en:"The Douyin video URL, support shared link (injected video name, user name, ...)"},required:!0}],response:[{name:"info",type:"object",description:{vi:"Thông tin video Douyin",en:"The Douyin video info"}}],function:async e=>{const{getDouyinVideoDetail:t,extractRealDouyinUrl:n}=await i((async()=>{const{getDouyinVideoDetail:e,extractRealDouyinUrl:t}=await import("./core-B0p8eH3S.js");return{getDouyinVideoDetail:e,extractRealDouyinUrl:t}}),__vite__mapDeps([26,1,2]),import.meta.url),r=await n(e.url);return await t(r)},tags:["douyin","video","info"]}];const G=c((()=>i((()=>import("./CodeExample-BkCKQWGR.js")),__vite__mapDeps([27,1,2,28,29,3,30,31,32,33,34,35,36]),import.meta.url)),{fallback:j}),N=c((()=>i((()=>import("./CodeBlock-BP5ddTLM.js")),__vite__mapDeps([29,1,2,3]),import.meta.url)),{fallback:j}),M=c((()=>i((()=>import("./ServerCode-fNUPazUT.js")),__vite__mapDeps([37,1,2,3,38,39,40,41,42,43]),import.meta.url)),{fallback:j}),z=[{id:"fbaio",icon:"fa-solid fa-droplet",label:"FB AIO",value:o.FB_AIO.server},{id:"glitch",icon:"fa-solid fa-fish",label:"Glitch",value:"https://fbaio-apis.glitch.me"},{id:"custom",icon:"fa-solid fa-wand-magic-sparkles",label:{en:"Custom",vi:"Tuỳ chỉnh"},value:"",description:{en:"Use your own server",vi:"Dùng server của bạn"}}];let H;function B(){const{ti:e}=g(),{message:t}=m(),{connectionState:i,isConnected:o,isConnecting:c,clientId:u,connect:j,disconnect:S,sendResponse:V,sendError:B,subscribe:X,connectedServerUrl:$}=(()=>{const[e,t]=n.useState(O.getConnectionState()),[i,r]=n.useState(null),{message:o}=m();n.useEffect((()=>{t(O.getConnectionState()),r(O.getClientId());const e=O.subscribeToConnectionState((e=>{t(e),"connected"===e?r(O.getClientId()):"disconnected"===e&&r(null)})),n=O.subscribeToMessage((e=>{"error"===e.type&&o.error(e.error||"WebSocket error occurred")}));return()=>{e(),n()}}),[o]);const a="connected"===e,s="connecting"===e||"reconnecting"===e;return{connectedServerUrl:n.useMemo((()=>O.getConnectedServerUrl()),[O,e]),connectionState:e,isConnected:a,isConnecting:s,clientId:i,connect:O.connect.bind(O),disconnect:O.disconnect.bind(O),sendResponse:O.sendResponse.bind(O),sendError:O.sendError.bind(O),subscribe:O.subscribeToMessage.bind(O),websocketManager:O}})();!function({message:e="Are you sure you want to scroll away?",check:t={pathname:!0,search:!0,hash:!0},willBlock:i=()=>!0}={}){const[o,a]=n.useState(!1),s=r((({currentLocation:n,nextLocation:r})=>{const s=t.pathname&&n.pathname!==r.pathname||t.search&&n.search!==r.search||t.hash&&n.hash!==r.hash;if(!o&&s&&i(n,r)){const t=window.confirm(e);return t&&a(!0),!t}return!1}));n.useEffect((()=>{const e=new AbortController;return window.addEventListener("beforeunload",(e=>{const t={pathname:window.location.pathname,search:window.location.search,hash:window.location.hash,state:null,key:""};i(t,t)&&(e.preventDefault(),e.returnValue="")}),{signal:e.signal}),()=>{e.abort()}}),[i]),n.useEffect((()=>()=>{if("blocked"===s.state)try{s.proceed()}catch(e){console.debug("Cleanup blocker error:",e)}}),[])}({message:e({en:"API history will not be recorded if you leave this page!",vi:"Lịch sử gọi API sẽ không được ghi lại nếu bạn rời trang này!"}),willBlock:()=>o});const[Y,Z]=x("Apis.loadingApiId",null),[ee,te]=n.useState(!1),[ne,ie]=n.useState(null),[re,oe]=x("Apis.docParams",{}),[ae,se]=x("Apis.docSearch",""),[ce,de]=x("Apis.selectedTags",[]),[le,ue]=x("Apis.curTime",(new Date).getTime()),[me,ge]=x("Apis.apiCallHistory",[],!0),[pe,he]=x("Apis.historySearch",""),[fe,ye]=n.useState(!1),[ve,_e]=n.useState(null),[be,we]=x("Apis.serverUrl",z[0].value,!0),[Ue,Ie]=x("Apis.customServerUrl","http://localhost:3000",!0),[Te,ke]=n.useState(!1),je=be||Ue.replace(/\/$/,"");n.useEffect((()=>{p("Apis:onLoad")}),[]),n.useEffect((()=>{const e=setInterval((()=>{ue((new Date).getTime())}),15e3);return()=>clearInterval(e)}),[]);const Le=n.useRef(null);n.useEffect((()=>{o&&("function"==typeof H&&H(),Le.current&&Le.current(),H=X((async e=>{if("api_call"===e.type){const{requestId:n,apiname:i,apiparams:r}=e;let o=null,a=null,s=(new Date).getTime();try{p("Apis:apiCall:"+i);const e=await J(i,r);o=e,V(n,e)}catch(t){a=t instanceof Error?t.message:"Unknown error occurred",B(n,a)}finally{ge((e=>[{requestTime:s,responseTime:(new Date).getTime(),apiname:i,apiparams:r,result:o,error:a},...e])),Z(null)}}})),Le.current=H)}),[o,V,B,X]);const xe=async()=>{if(ne)try{p("Apis:testAPI:"+ne.id),Z(ne.id),await(async(n,i,r=!1)=>{if(!o)return t.error(e({en:"Not connected. Please connect first.",vi:"Chưa kết nối. Vui lòng kết nối trước."})),void(r&&Z(null));r&&Z(n);const a="api-call";try{if(!$)throw new Error("No server URL available");t.loading({key:a,content:e({en:"Calling API...",vi:"Đang gọi API..."}),duration:0});const o=await l($+"/call",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:u,apiname:n,apiparams:i})});console.log("sendApiCall",o),'{"error":"Client not connected"}'==o?(t.error({key:a,content:e({en:"Client not connected. Reconnecting...",vi:"Client chưa kết nối. Đang kết nối lại..."})}),S(),setTimeout((()=>{j($)}),1e3)):r&&t.success({key:a,content:"API call success"})}catch(s){t.error({key:a,content:e({en:"API call failed",vi:"Gọi API thất bại"})})}finally{r&&Z(null)}})(ne.id,re[ne.id],!0)}finally{Z(null)}},{statusText:Re,statusIcon:De}=function(e){let t={en:"",vi:""},n="";switch(e){case"connecting":t={en:"Connecting...",vi:"Đang kết nối..."},n="🟡";break;case"connected":t={en:"Connected",vi:"Đã kết nối"},n="🟢";break;case"reconnecting":t={en:"Reconnecting...",vi:"Đang kết nối lại..."},n="🟠";break;case"error":t={en:"Error",vi:"Lỗi kết nối"},n="🔴";break;default:t={en:"Disconnected",vi:"Đã ngắt kết nối"},n="🔴"}return{statusText:t,statusIcon:n}}(i),Ee=(t=me)=>{const n=t.filter((e=>{if(!pe)return!0;const t=pe.toLowerCase();return e.apiname&&e.apiname.toLowerCase().includes(t)||JSON.stringify(e.apiparams).toLowerCase().includes(t)||e.result&&JSON.stringify(e.result).toLowerCase().includes(t)||e.error&&String(e.error).toLowerCase().includes(t)}));return a.jsx(D,{bordered:!0,children:a.jsxs(h,{direction:"vertical",style:{width:"100%"},children:[t.length>0&&a.jsxs(a.Fragment,{children:[a.jsx(F.Search,{placeholder:e({en:"Search API name, params, result, or error",vi:"Tìm tên API, tham số, kết quả, hoặc lỗi"}),allowClear:!0,value:pe,onChange:e=>he(e.target.value)}),a.jsx(s,{danger:!0,icon:a.jsx("i",{className:"fa-solid fa-trash"}),onClick:()=>ge([]),disabled:0===t.length,children:e({en:"Clear",vi:"Xoá hết"})})]}),a.jsx(P,{dataSource:n,pagination:{pageSize:4,showSizeChanger:!1,align:"center",hideOnSinglePage:!0},renderItem:(t,n)=>{const{statusColor:i,statusText:r}=K(t),o=t.responseTime-t.requestTime,s=q.find((e=>e.id===t.apiname));return a.jsx(P.Item,{style:{cursor:"pointer"},onClick:()=>{_e(t),ye(!0)},children:a.jsxs(h,{style:{justifyContent:"space-between",width:"100%"},children:[a.jsxs(h,{children:[s&&a.jsx("i",{className:s.icon+" fa-xl"}),a.jsxs(h,{direction:"vertical",size:4,children:[a.jsx(h,{wrap:!0,children:a.jsx(v.Text,{strong:!0,children:e((null==s?void 0:s.name)||t.apiname)})}),a.jsxs(h,{size:0,wrap:!0,children:[a.jsx(w,{color:i,children:e(r)}),a.jsxs(w,{color:"blue",children:[o,"ms"]})]}),a.jsx(v.Text,{type:"secondary",children:t.apiname}),a.jsx(v.Text,{type:"secondary",style:{fontSize:12},children:k(le-t.requestTime)+e({en:" ago",vi:" trước"})})]})]}),a.jsx("i",{className:"fa-solid fa-chevron-right"})]})},t.requestTime+"-"+n)}})]})})};return a.jsx(L,{title:e({en:"APIs Integration",vi:"Tích hợp APIs"}),mode:"center",children:a.jsxs(h,{direction:"vertical",size:20,align:"center",children:[a.jsx(f,{message:a.jsxs(h,{size:0,children:[e({en:"🔥 Integrate FB AIO APIs into your product",vi:"🔥 Tích hợp API FB AIO vào sản phẩm của bạn"}),a.jsx(s,{type:"link",target:"_blank",href:"https://www.facebook.com/groups/fbaio/posts/1648145939173421",children:e({en:"What is this?",vi:"Đây là gì?"})}),a.jsx(s,{type:"link",target:"_blank",href:"https://www.facebook.com/groups/fbaio/posts/1652285728759442",children:e({en:"Server Local",vi:"Server Local"})})]}),type:"success"}),a.jsx(D,{children:a.jsxs(h,{direction:"vertical",align:"center",style:{width:"100%"},size:25,children:[a.jsxs(h,{direction:"vertical",align:"center",children:[a.jsxs(v.Text,{strong:!0,children:[e({en:"Select Server",vi:"Chọn Máy chủ"}),":"," ",a.jsx(y,{color:"purple",count:e({en:"New",vi:"Mới"})})]}),a.jsx(E,{style:{width:"100%"},value:be,popupMatchSelectWidth:!1,onChange:e=>{o&&S(),we(e)},options:z.map((t=>({value:t.value,label:a.jsxs(h,{children:[a.jsx("i",{className:t.icon||"fa-solid fa-server"}),a.jsx(v.Text,{children:e(t.label)}),a.jsx(v.Text,{type:"secondary",children:e(t.value||t.description)})]})}))),placeholder:e({en:"Select or enter server URL",vi:"Chọn hoặc nhập URL máy chủ"})}),""===be&&a.jsxs(h,{children:[a.jsx(F,{placeholder:e({en:"Enter your server URL",vi:"Nhập URL máy chủ của bạn"}),value:Ue,onChange:e=>Ie(e.target.value)}),a.jsx(s,{type:"link",size:"small",icon:a.jsx("i",{className:"fa-solid fa-code"}),iconPosition:"end",onClick:()=>ke(!0),children:e({en:"Code",vi:"Code"})})]})]}),a.jsxs(h,{direction:"vertical",align:"center",children:[!o&&a.jsx(f,{type:"info",showIcon:!0,message:e({en:"Click Connect to start listening to APIs",vi:"Bấm Kết nối để bắt đầu lắng nghe API"})}),a.jsxs(v.Text,{strong:!0,children:[e({en:"Status",vi:"Trạng thái"}),": ",De," ",e(Re)]}),u&&a.jsxs(v.Text,{type:"secondary",children:["Client ID: ",a.jsx("i",{children:u})]}),o?a.jsxs(a.Fragment,{children:[a.jsx(s,{danger:!0,icon:a.jsx("i",{className:"fa-solid fa-stop"}),onClick:S,disabled:c,children:e({en:"Disconnect",vi:"Ngắt kết nối"})}),a.jsx(v.Text,{type:"success",children:e({en:"Ready to receive API calls",vi:"Sẵn sàng nhận API"})})]}):a.jsx(s,{type:"primary",icon:a.jsx("i",{className:"fa-solid fa-play"}),onClick:()=>{Q(je)?(p("Apis:connect:"+je),j(je)):t.error(e({en:"Please enter a valid server URL",vi:"Vui lòng nhập URL máy chủ hợp lệ"}))},disabled:c||!je,loading:c,children:e({en:"Connect",vi:"Kết nối"})})]})]})}),a.jsx(R,{defaultActiveKey:"doc",type:"card",centered:!0,items:[{label:a.jsxs(h,{size:4,children:[e({en:"📚 API Document",vi:"📚 Tài liệu API"})," ",a.jsx(y,{color:"blue",count:q.length})]}),key:"doc",children:(()=>{const t=[...q].filter((t=>{const n=_(ae)||b(ae,t.id+t.name.en+t.name.vi+e(t.description)),i=0===ce.length||ce.every((e=>{var n;return null==(n=t.tags)?void 0:n.includes(e)}));return n&&i})).map((e=>{const t=e.changeLog||[];return{...e,lastEdit:Math.max(e.created||0,...t.map((e=>e.created||0)))}})).sort(((e,t)=>(t.lastEdit||0)-(e.lastEdit||0))),n={};t.forEach((e=>{(e.tags||[]).forEach((e=>{n[e]=(n[e]||0)+1}))}));const i=Object.keys(n).sort(((e,t)=>n[t]!==n[e]?n[t]-n[e]:e.localeCompare(t))).map((e=>({tag:e,frequency:n[e]})));return a.jsx(D,{bordered:!0,style:{maxWidth:550},children:a.jsxs(h,{direction:"vertical",style:{width:"100%"},size:"middle",children:[a.jsx(F.Search,{placeholder:e({en:"Search API name or description",vi:"Tìm tên API hoặc mô tả"}),allowClear:!0,value:ae,onChange:e=>se(e.target.value)}),a.jsxs(h,{size:[0,4],wrap:!0,children:[ce.length>0&&a.jsx(s,{danger:!0,type:"text",onClick:()=>de([]),icon:a.jsx("i",{className:"fa-solid fa-xmark"})}),i.map((({tag:e,frequency:t})=>a.jsxs(w.CheckableTag,{checked:ce.includes(e),onChange:t=>{de(t?[...ce,e]:ce.filter((t=>t!==e)))},children:[e," ",t>1&&a.jsx(y,{color:"gray",count:t})]},e)))]}),a.jsx(P,{dataSource:t,pagination:{pageSize:4,showSizeChanger:!1,align:"center",hideOnSinglePage:!0},renderItem:t=>a.jsx(P.Item,{style:{cursor:"pointer"},onClick:()=>(async e=>{var t;p("Apis:clickDoc:"+e.id),ie(e),te(!0);const n={...e.params};for(const i of e.params)n[i.name]=(null==(t=e.params[i.name])?void 0:t.default)||(i.defaultFn?await i.defaultFn():"")||"";oe((t=>d(t,(t=>{t[e.id]||(t[e.id]={});for(const i of e.params)t[e.id][i.name]=t[e.id][i.name]||n[i.name]}))))})(t),children:a.jsxs(h,{style:{width:"100%",justifyContent:"space-between"},children:[a.jsxs(h,{align:"start",children:[a.jsx("i",{className:t.icon+" fa-xl"}),a.jsxs(h,{direction:"vertical",size:4,children:[a.jsxs(h,{wrap:!0,children:[a.jsx(v.Text,{strong:!0,children:e(t.name)}),t.lastEdit&&a.jsx(U,{title:e({vi:"Cập nhật lần cuối: ",en:"Last updated: "})+T(t.lastEdit),children:a.jsx(w,{children:I(t.lastEdit)})})]}),a.jsx(h,{size:[0,8],wrap:!0,children:(t.tags||[]).map((e=>a.jsx(w,{color:"blue",children:e},e)))}),a.jsx(v.Text,{type:"secondary",children:t.id}),t.description&&a.jsx(v.Text,{type:"secondary",children:a.jsx("i",{children:e(t.description)})})]})]}),a.jsx("i",{className:"fa-solid fa-chevron-right"})]})})})]})})})()},{label:a.jsxs(h,{size:4,children:[a.jsx("i",{className:"fa-solid fa-clock-rotate-left"})," ",e({en:"History",vi:"Lịch sử"})," ",me.length>0&&a.jsx(y,{color:"blue",count:me.length})]}),key:"history",children:Ee()}]}),(()=>{var t,n,i,r,s,c;const d=me.filter((e=>e.apiname===(null==ne?void 0:ne.id))),l=ne?[{label:a.jsxs(h,{size:4,children:[a.jsx("i",{className:"fa-solid fa-bolt"})," ",e({en:"Test API",vi:"Kiểm thử API"})]}),key:"params",children:a.jsxs(h,{direction:"vertical",style:{width:"100%"},size:"middle",children:[a.jsx(D,{title:"Params",size:"small",children:a.jsx(P,{dataSource:(null==ne?void 0:ne.params)||[],renderItem:e=>a.jsx(W,{param:e,docParams:re,setDocParams:oe,selectedDoc:ne})})}),a.jsx(D,{title:"Response",size:"small",children:null==(n=null==(t=null==ne?void 0:ne.response)?void 0:t.map)?void 0:n.call(t,(t=>a.jsxs(v.Text,{strong:!0,children:[a.jsx(w,{children:t.name}),a.jsx(w,{color:"green",children:t.type}),a.jsx("i",{style:{fontWeight:400,color:"gray"},children:e(t.description)})]},t.name)))}),a.jsxs(h,{direction:"vertical",align:"center",style:{width:"100%"},children:[!o&&a.jsx(f,{showIcon:!0,message:e({en:"Please Connect first to Test API",vi:"Vui lòng Kết nối trước để Kiểm thử API"}),type:"error"}),(!ne||(null==(r=null==(i=null==ne?void 0:ne.params)?void 0:i.some)?void 0:r.call(i,(e=>{var t;return e.required&&!(null==(t=null==re?void 0:re[null==ne?void 0:ne.id])?void 0:t[e.name])}))))&&a.jsx(f,{showIcon:!0,message:e({en:"Please fill all required fields *",vi:"Vui lòng điền đầy đủ các trường bắt buộc *"}),type:"error"})]})]})},{label:a.jsxs(h,{size:4,children:[a.jsx("i",{className:"fa-solid fa-code"})," ",e({en:"Code",vi:"Code"})]}),key:"code",children:a.jsxs(h,{direction:"vertical",style:{width:"100%"},children:[!o&&a.jsx(f,{showIcon:!0,message:e({en:"Please Connect first to get Client ID",vi:"Vui lòng Kết nối trước để lấy Client ID"}),type:"error"}),a.jsx(G,{url:($||je)+"/call",apiname:null==ne?void 0:ne.id,apiparams:(null==re?void 0:re[null==ne?void 0:ne.id])||{},clientId:u})]})},{label:a.jsxs(h,{size:4,children:[a.jsx("i",{className:"fa-solid fa-clock-rotate-left"})," ",e({en:"History",vi:"Lịch sử"})," ",d.length>0&&a.jsx(y,{color:"blue",count:d.length})]}),key:"history",children:Ee(d)}]:null;return(null==ne?void 0:ne.changeLog)&&l.push({label:a.jsxs(h,{size:4,children:[a.jsx("i",{className:"fa-solid fa-pen-to-square"})," ",e({en:"Change Log",vi:"Cập nhật"})]}),key:"changeLog",children:a.jsx(P,{dataSource:ne.changeLog,renderItem:t=>a.jsx(P.Item,{children:a.jsxs(h,{children:[a.jsxs(v.Text,{children:[I(t.created)," ",e({en:"ago",vi:"trước"}),a.jsx("br",{}),a.jsx(v.Text,{type:"secondary",children:T(t.created)})]}),a.jsxs(h,{direction:"vertical",children:[a.jsx(v.Text,{strong:!0,children:e(t.title)}),a.jsx(v.Text,{type:"secondary",children:e(t.description)})]})]})})})}),a.jsx(C,{open:ee,title:ne?a.jsxs(h,{children:[a.jsx("i",{className:ne.icon}),a.jsx(v.Text,{strong:!0,style:{fontSize:16},children:e(ne.name)})]}):"",onCancel:()=>te(!1),onOk:xe,okText:a.jsxs(h,{children:[e({en:"Test API",vi:"Kiếm thử API"}),a.jsx("i",{className:"fa-solid fa-play"})]}),cancelText:e({en:"Close",vi:"Đóng"}),confirmLoading:!!Y,destroyOnClose:!0,okButtonProps:{disabled:!o||ne&&(null==(c=null==(s=null==ne?void 0:ne.params)?void 0:s.some)?void 0:c.call(s,(e=>{var t;return e.required&&!(null==(t=null==re?void 0:re[null==ne?void 0:ne.id])?void 0:t[e.name])})))},children:ne&&a.jsxs(h,{direction:"vertical",style:{width:"100%"},size:"middle",children:[ne.tags&&a.jsx(h,{size:0,wrap:!0,children:ne.tags.map((e=>a.jsx(w,{color:"blue",children:e},e)))}),a.jsxs(v.Text,{type:"secondary",children:["ID: ",ne.id,a.jsx("br",{}),e({en:"Description",vi:"Mô tả"}),": ",e(ne.description)]}),a.jsx(R,{defaultActiveKey:"params",type:"card",items:l})]})})})(),(()=>{if(!ve)return null;const t=ve,{statusColor:n,statusText:i}=K(t),r=t.responseTime-t.requestTime,o=q.find((e=>e.id===t.apiname));return a.jsx(C,{open:fe,title:a.jsxs(h,{children:[o&&a.jsx("i",{className:o.icon}),a.jsx(v.Text,{strong:!0,style:{fontSize:16},children:e((null==o?void 0:o.name)||t.apiname)})]}),onCancel:()=>ye(!1),footer:null,destroyOnClose:!0,children:a.jsxs(h,{direction:"vertical",size:"small",style:{width:"100%"},children:[a.jsxs(h,{size:0,wrap:!0,children:[a.jsx(w,{color:n,children:e(i)}),a.jsxs(w,{color:"blue",children:[r,"ms"]})]}),a.jsx(v.Text,{type:"secondary",children:t.apiname}),a.jsx(A,{style:{margin:"8px 0"}}),a.jsx(v.Text,{type:"secondary",style:{fontSize:12},children:k(le-t.requestTime)+e({en:" ago",vi:" trước"})}),a.jsxs(h,{direction:"vertical",size:3,children:[a.jsx(U,{title:e({en:"Request received at",vi:"Thời gian nhận yêu cầu"}),placement:"right",children:a.jsxs(h,{children:[a.jsx("i",{className:"fa-solid fa-arrow-turn-down"}),T(t.requestTime,{second:"numeric"})]})}),a.jsx(U,{title:e({en:"Response sent at",vi:"Thời gian trả kết quả"}),placement:"right",children:a.jsxs(h,{children:[a.jsx("i",{className:"fa-solid fa-arrow-turn-up"}),T(t.responseTime,{second:"numeric"})]})}),a.jsx(U,{title:e({en:"Processing time",vi:"Thời gian xử lý"}),placement:"right",children:a.jsxs(h,{children:[a.jsx("i",{className:"fa-solid fa-stopwatch"}),t.responseTime-t.requestTime,"ms"]})})]}),a.jsx(A,{}),a.jsx(v.Text,{strong:!0,children:"Params:"}),a.jsx(N,{code:JSON.stringify(t.apiparams,null,2),language:"json"}),t.result&&a.jsxs(a.Fragment,{children:[a.jsx(v.Text,{strong:!0,children:"✅️ Result:"}),a.jsx(N,{code:JSON.stringify(t.result,null,2),language:"json"})]}),t.error&&a.jsxs(a.Fragment,{children:[a.jsx(v.Text,{strong:!0,children:"❌️ Error:"}),a.jsx(F.TextArea,{value:t.error,autoSize:{minRows:2,maxRows:6},readOnly:!0,style:{fontFamily:"monospace",resize:"vertical",minWidth:120}})]})]})})})(),a.jsx(C,{title:e({en:"Server Code (Node.js)",vi:"Mã nguồn server (Node.js)"}),open:Te,onCancel:()=>ke(!1),footer:null,destroyOnClose:!0,children:a.jsx(M,{})})]})})}function W({param:e,docParams:t,setDocParams:i,selectedDoc:r}){var o,s,c;const{ti:l}=g(),[u,m]=n.useState(e.values||[]);return n.useEffect((()=>{!e.values&&e.valuesFn&&e.valuesFn().then(m)}),[e.valuesFn,e.values]),a.jsxs(h,{direction:"vertical",style:{width:"100%",marginBottom:16},size:4,children:[a.jsxs(v.Text,{strong:!0,children:[a.jsx(w,{children:e.name}),a.jsx(w,{color:"green",children:e.type}),e.required&&a.jsx("span",{style:{color:"red"},children:"*"})]}),e.description&&a.jsx(v.Text,{type:"secondary",style:{fontStyle:"italic"},children:l(e.description)}),"boolean"===e.type?a.jsx(S,{checked:null==(o=null==t?void 0:t[null==r?void 0:r.id])?void 0:o[e.name],onChange:n=>i(d(t,(t=>(t[null==r?void 0:r.id]||(t[null==r?void 0:r.id]={}),t[null==r?void 0:r.id][e.name]=n.target.checked,t)))),children:l({en:"Yes",vi:"Có"})}):(null==u?void 0:u.length)>0?a.jsx(E,{value:null==(s=null==t?void 0:t[null==r?void 0:r.id])?void 0:s[e.name],popupMatchSelectWidth:!1,onChange:n=>i(d(t,(t=>{t[null==r?void 0:r.id]||(t[null==r?void 0:r.id]={}),t[null==r?void 0:r.id][e.name]=n}))),children:null==u?void 0:u.map((e=>{const t="string"==typeof e?e:e.value;return a.jsx(E.Option,{value:t,children:a.jsxs(h,{children:[a.jsx(v.Text,{strong:!0,children:l(t)}),"object"==typeof e&&e.name&&a.jsxs(v.Text,{type:"secondary",children:["- ",l(e.name)]})]})},t)}))}):a.jsx(F.TextArea,{value:null==(c=null==t?void 0:t[null==r?void 0:r.id])?void 0:c[e.name],onChange:n=>i(d(t,(t=>{t[null==r?void 0:r.id]||(t[null==r?void 0:r.id]={}),t[null==r?void 0:r.id][e.name]=n.target.value}))),placeholder:l(e.description)})]})}const J=(e,t)=>{const n=q.find((t=>t.id===e));if(!n)throw new Error(`Unknown API: ${e}`);return n.function(t)};function K(e){let t="default",n={en:"Pending",vi:"Đang xử lý"};return e.error?(t="red",n={en:"Error",vi:"Lỗi"}):e.result&&(t="green",n={en:"Success",vi:"Thành công"}),{statusColor:t,statusText:n}}const Q=e=>{try{return new URL(e),e.startsWith("http://")||e.startsWith("https://")}catch{return!1}};export{B as default};
