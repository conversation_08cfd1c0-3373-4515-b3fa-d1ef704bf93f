import{r as e,b0 as n}from"./index-Cak6rALw.js";import{a as t,b as s}from"./index-ByRdMNW-.js";import{d as c,u as r}from"./MyApp-DW5WH4Ub.js";function i({disabled:i,children:o,next:a,hasNext:l,prev:u,hasPrev:d,loader:f,endMessage:h,prevLoader:g,prevEndMessage:v}){const{message:x}=c(),{ti:y}=r(),{isIntersecting:p,ref:m}=t(),{isIntersecting:k,ref:F}=t();s((()=>{T(),E()}),1e3),e.useLayoutEffect((()=>{T()}),[k]),e.useLayoutEffect((()=>{E()}),[p]);const j=e.useRef(!1),T=()=>{if(!i&&k&&!j.current){j.current=!0;const e="checkFetchNext";x.loading({key:e,content:y({en:"Fetching next...",vi:"<PERSON><PERSON> tải tiếp theo..."}),duration:0}),null==a||a().then((()=>{x.success({key:e,content:y({en:"Fetch success",vi:"Tải xong"})})})).catch((n=>{x.error({key:e,content:y({en:"Fetch failed: ",vi:"Tải lỗi: "})+n.message}),console.log(n)})).finally((()=>{j.current=!1}))}},b=e.useRef(!1),E=()=>{if(!i&&p&&!b.current){b.current=!0;const e="checkFetchPrev";x.loading({key:e,content:y({en:"Fetching previous...",vi:"Đang tải trước đó..."}),duration:0}),null==u||u().then((()=>{x.success({key:e,content:y({en:"Fetch success",vi:"Tải xong"})})})).catch((n=>{x.error({key:e,content:y({en:"Fetch failed: ",vi:"Tải lỗi: "})+n.message}),console.log(n)})).finally((()=>{b.current=!1}))}};return n.jsxs("div",{style:{flex:1},children:[u&&d&&!i&&n.jsx("div",{ref:m,children:g||f}),u&&!d&&(v||h),o,a&&l&&!i&&n.jsx("div",{ref:F,children:f}),!l&&h]})}export{i as default};
