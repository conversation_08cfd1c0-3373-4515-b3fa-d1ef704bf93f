import{b0 as e,bg as i}from"./index-Cak6rALw.js";import t from"./useCacheState-CAnxkewm.js";import{u as n,S as o,A as s,f as a,b as r,e as c}from"./MyApp-DW5WH4Ub.js";import{I as d}from"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";function l(){const{ti:t}=n();return e.jsx(i,{items:[{key:"1",label:t({en:"Tiktok Video Upload Time",vi:"Thời gian đăng video Tiktok"}),children:e.jsx(m,{})}]})}function m(){const{ti:i}=n(),[l,m]=t("TiktokVideoUploadTime.url",""),[g,h]=t("TiktokVideoUploadTime.error",""),[p,u]=t("TiktokVideoUploadTime.timestamp",0);return e.jsxs(o,{direction:"vertical",align:"center",style:{width:"100%"},children:[e.jsx(s,{message:i({en:"Find upload time of any Tiktok video",vi:"Tìm thời gian đăng của bất kỳ video Tiktok nào"}),type:"info",showIcon:!0}),e.jsx(d.Search,{placeholder:i({en:"Enter Tiktok video URL",vi:"Nhập URL video Tiktok"}),value:l,onChange:e=>{m(e.target.value),h("")},onSearch:e=>{try{const t=function(e){var i;const t=new RegExp("(?<=\\/video\\/)(.*?)(?=$|[^0-9])"),n=null==(i=t.exec(e))?void 0:i[0];return n}(e);if(!t)throw new Error(i({en:"Cannot find video ID",vi:"Không tìm thấy ID video"}));const n=function(e){const i=BigInt(e).toString(2),t=i.slice(0,31),n=parseInt(t,2);return n}(t);if(!n)throw new Error(i({en:"Cannot find upload time",vi:"Không tìm thấy thời gian đăng"}));u(1e3*n)}catch(t){u(0),h(t.message)}},enterButton:e.jsx("i",{className:"fa-solid fa-magnifying-glass"})}),g?e.jsx(s,{message:g,type:"error",showIcon:!0}):null,p?e.jsx(s,{message:e.jsxs(o,{align:"center",style:{justifyContent:"center"},children:[i({en:"Uploaded at",vi:"Đăng lúc"}),a(p),e.jsxs(r,{children:[c(p),i({en:" ago",vi:" trước"})]})]}),type:"success",showIcon:!0}):null]})}export{l as default};
