import{aJ as e}from"./index-Cak6rALw.js";var t,n={exports:{}};const o=e((t||(t=1,n.exports=function(){function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function o(e,t,o){t&&n(e.prototype,t),o&&n(e,o),Object.defineProperty(e,"prototype",{writable:!1})}function i(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&c(e,t)}function u(e){return(u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function c(e,t){return(c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function a(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");if(void 0===(t=e))throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function l(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,o=u(e);return a(this,t?(n=u(this).constructor,Reflect.construct(o,arguments,n)):o.apply(this,arguments))}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function s(e,t){var n,o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!o){if(Array.isArray(e)||(o=function(e,t){if(e){if("string"==typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Map"===(n="Object"===n&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length)return o&&(e=o),n=0,{s:t=function(){},n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,r=!0,u=!1;return{s:function(){o=o.call(e)},n:function(){var e=o.next();return r=e.done,e},e:function(e){u=!0,i=e},f:function(){try{r||null==o.return||o.return()}finally{if(u)throw i}}}}function d(){if(v.url)window.location.href=v.url;else if(v.rewriteHTML)try{document.documentElement.innerHTML=v.rewriteHTML}catch(e){document.documentElement.innerText=v.rewriteHTML}else{try{window.opener=null,window.open("","_self"),window.close(),window.history.back()}catch(e){console.log(e)}setTimeout((function(){window.location.href=v.timeOutUrl||"https://theajack.github.io/disable-devtool/404.html?h=".concat(encodeURIComponent(location.host))}),500)}}var v={md5:"",ondevtoolopen:d,ondevtoolclose:null,url:"",timeOutUrl:"",tkName:"ddtk",interval:500,disableMenu:!0,stopIntervalTime:5e3,clearIntervalWhenDevOpenTrigger:!1,detectors:[0,1,3,4,5,6,7],clearLog:!0,disableSelect:!1,disableCopy:!1,disableCut:!1,disablePaste:!1,ignore:null,disableIframeParents:!0,seo:!0,rewriteHTML:""},h=["detectors","ondevtoolclose","ignore"];function p(t){var n,o=0<arguments.length&&void 0!==t?t:{};for(n in v){var i=n;void 0===o[i]||e(v[i])!==e(o[i])&&-1===h.indexOf(i)||(v[i]=o[i])}"function"==typeof v.ondevtoolclose&&!0===v.clearIntervalWhenDevOpenTrigger&&(v.clearIntervalWhenDevOpenTrigger=!1,console.warn("【DISABLE-DEVTOOL】clearIntervalWhenDevOpenTrigger 在使用 ondevtoolclose 时无效"))}function y(){return(new Date).getTime()}function b(e){var t=y();return e(),y()-t}function w(e,t){function n(n){return function(){e&&e();var o=n.apply(void 0,arguments);return t&&t(),o}}var o=window.alert,i=window.confirm,r=window.prompt;try{window.alert=n(o),window.confirm=n(i),window.prompt=n(r)}catch(u){}}var g,m,T,O={iframe:!1,pc:!1,qqBrowser:!1,firefox:!1,macos:!1,edge:!1,oldEdge:!1,ie:!1,iosChrome:!1,iosEdge:!1,chrome:!1,seoBot:!1,mobile:!1};function S(){function e(e){return-1!==t.indexOf(e)}var t=navigator.userAgent.toLowerCase(),n=function(){var e,t=(e=navigator).platform;if("number"==typeof(e=e.maxTouchPoints))return 1<e;if("string"==typeof t){if(e=t.toLowerCase(),/(mac|win)/i.test(e))return!1;if(/(android|iphone|ipad|ipod|arch)/i.test(e))return!0}return/(iphone|ipad|ipod|ios|android)/i.test(navigator.userAgent.toLowerCase())}(),o=!!window.top&&window!==window.top,i=!n,r=e("qqbrowser"),u=e("firefox"),c=e("macintosh"),a=e("edge"),l=a&&!e("chrome"),f=l||e("trident")||e("msie"),s=e("crios"),d=e("edgios"),v=e("chrome")||s,h=!n&&/(googlebot|baiduspider|bingbot|applebot|petalbot|yandexbot|bytespider|chrome\-lighthouse|moto g power)/i.test(t);Object.assign(O,{iframe:o,pc:i,qqBrowser:r,firefox:u,macos:c,edge:a,oldEdge:l,ie:f,iosChrome:s,iosEdge:d,chrome:v,seoBot:h,mobile:n})}function D(){for(var e=function(){for(var e={},t=0;t<500;t++)e["".concat(t)]="".concat(t);return e}(),t=[],n=0;n<50;n++)t.push(e);return t}function k(){v.clearLog&&T()}var x="",P=!1;function j(){var e=v.ignore;if(e){if("function"==typeof e)return e();if(0!==e.length){var t=location.href;if(x===t)return P;x=t;var n,o=!1,i=s(e);try{for(i.s();!(n=i.n()).done;){var r=n.value;if("string"==typeof r){if(-1!==t.indexOf(r)){o=!0;break}}else if(r.test(t)){o=!0;break}}}catch(u){i.e(u)}finally{i.f()}return P=o}}}var E=function(){return!1};function I(e){var t,n,o=74,i=73,r=85,u=83,c=123,a=O.macos?function(e,t){return e.metaKey&&e.altKey&&(t===i||t===o)}:function(e,t){return e.ctrlKey&&e.shiftKey&&(t===i||t===o)},l=O.macos?function(e,t){return e.metaKey&&e.altKey&&t===r||e.metaKey&&t===u}:function(e,t){return e.ctrlKey&&(t===u||t===r)};e.addEventListener("keydown",(function(t){var n=(t=t||e.event).keyCode||t.which;if(n===c||a(t,n)||l(t,n))return _(e,t)}),!0),t=e,v.disableMenu&&t.addEventListener("contextmenu",(function(e){if("touch"!==e.pointerType)return _(t,e)})),n=e,v.disableSelect&&L(n,"selectstart"),n=e,v.disableCopy&&L(n,"copy"),n=e,v.disableCut&&L(n,"cut"),n=e,v.disablePaste&&L(n,"paste")}function L(e,t){e.addEventListener(t,(function(t){return _(e,t)}))}function _(e,t){if(!j()&&!E())return(t=t||e.event).returnValue=!1,t.preventDefault(),!1}var C,A=!1,R={};function z(e){R[e]=!1}function H(){for(var e in R)if(R[e])return A=!0;return A=!1}(ee=C=C||{})[ee.Unknown=-1]="Unknown",ee[ee.RegToString=0]="RegToString",ee[ee.DefineId=1]="DefineId",ee[ee.Size=2]="Size",ee[ee.DateToString=3]="DateToString",ee[ee.FuncToString=4]="FuncToString",ee[ee.Debugger=5]="Debugger",ee[ee.Performance=6]="Performance",ee[ee.DebugLib=7]="DebugLib";var U=function(){function e(n){var o=n.type;n=void 0===(n=n.enabled)||n,t(this,e),this.type=C.Unknown,this.enabled=!0,this.type=o,this.enabled=n,this.enabled&&(o=this,W.push(o),this.init())}return o(e,[{key:"onDevToolOpen",value:function(){var e;console.warn("You don't have permission to use DEVTOOL!【type = ".concat(this.type,"】")),v.clearIntervalWhenDevOpenTrigger&&F(),window.clearTimeout(M),v.ondevtoolopen(this.type,d),e=this.type,R[e]=!0}},{key:"init",value:function(){}}]),e}(),q=function(){r(n,U);var e=l(n);function n(){return t(this,n),e.call(this,{type:C.DebugLib})}return o(n,[{key:"init",value:function(){}},{key:"detect",value:function(){var e;(!0===(null==(e=null==(e=window.eruda)?void 0:e._devTools)?void 0:e._isShow)||window._vcOrigConsole&&window.document.querySelector("#__vconsole.vc-toggle"))&&this.onDevToolOpen()}}],[{key:"isUsing",value:function(){return!!window.eruda||!!window._vcOrigConsole}}]),n}(),B=0,M=0,W=[],K=0;function V(e){function t(){l=!0}function n(){l=!1}var o,i,r,u,c,a,l=!1;function f(){(a[u]===r?i:o)()}w(t,n),o=n,i=t,void 0!==(a=document).hidden?(r="hidden",c="visibilitychange",u="visibilityState"):void 0!==a.mozHidden?(r="mozHidden",c="mozvisibilitychange",u="mozVisibilityState"):void 0!==a.msHidden?(r="msHidden",c="msvisibilitychange",u="msVisibilityState"):void 0!==a.webkitHidden&&(r="webkitHidden",c="webkitvisibilitychange",u="webkitVisibilityState"),a.removeEventListener(c,f,!1),a.addEventListener(c,f,!1),B=window.setInterval((function(){if(!(e.isSuspend||l||j())){var t,n,o=s(W);try{for(o.s();!(t=o.n()).done;){var i=t.value;z(i.type),i.detect(K++)}}catch(r){o.e(r)}finally{o.f()}k(),"function"==typeof v.ondevtoolclose&&(n=A,!H()&&n&&v.ondevtoolclose())}}),v.interval),M=setTimeout((function(){O.pc||q.isUsing()||F()}),v.stopIntervalTime)}function F(){window.clearInterval(B)}var X=8;function N(e){for(var t=function(e,t){e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;for(var n=1732584193,o=-271733879,i=-1732584194,r=271733878,u=0;u<e.length;u+=16){var c=n,a=o,l=i,f=r;n=G(n,o,i,r,e[u+0],7,-680876936),r=G(r,n,o,i,e[u+1],12,-389564586),i=G(i,r,n,o,e[u+2],17,606105819),o=G(o,i,r,n,e[u+3],22,-1044525330),n=G(n,o,i,r,e[u+4],7,-176418897),r=G(r,n,o,i,e[u+5],12,1200080426),i=G(i,r,n,o,e[u+6],17,-1473231341),o=G(o,i,r,n,e[u+7],22,-45705983),n=G(n,o,i,r,e[u+8],7,1770035416),r=G(r,n,o,i,e[u+9],12,-1958414417),i=G(i,r,n,o,e[u+10],17,-42063),o=G(o,i,r,n,e[u+11],22,-1990404162),n=G(n,o,i,r,e[u+12],7,1804603682),r=G(r,n,o,i,e[u+13],12,-40341101),i=G(i,r,n,o,e[u+14],17,-1502002290),n=J(n,o=G(o,i,r,n,e[u+15],22,1236535329),i,r,e[u+1],5,-165796510),r=J(r,n,o,i,e[u+6],9,-1069501632),i=J(i,r,n,o,e[u+11],14,643717713),o=J(o,i,r,n,e[u+0],20,-373897302),n=J(n,o,i,r,e[u+5],5,-701558691),r=J(r,n,o,i,e[u+10],9,38016083),i=J(i,r,n,o,e[u+15],14,-660478335),o=J(o,i,r,n,e[u+4],20,-405537848),n=J(n,o,i,r,e[u+9],5,568446438),r=J(r,n,o,i,e[u+14],9,-1019803690),i=J(i,r,n,o,e[u+3],14,-187363961),o=J(o,i,r,n,e[u+8],20,1163531501),n=J(n,o,i,r,e[u+13],5,-1444681467),r=J(r,n,o,i,e[u+2],9,-51403784),i=J(i,r,n,o,e[u+7],14,1735328473),n=Y(n,o=J(o,i,r,n,e[u+12],20,-1926607734),i,r,e[u+5],4,-378558),r=Y(r,n,o,i,e[u+8],11,-2022574463),i=Y(i,r,n,o,e[u+11],16,1839030562),o=Y(o,i,r,n,e[u+14],23,-35309556),n=Y(n,o,i,r,e[u+1],4,-1530992060),r=Y(r,n,o,i,e[u+4],11,1272893353),i=Y(i,r,n,o,e[u+7],16,-155497632),o=Y(o,i,r,n,e[u+10],23,-1094730640),n=Y(n,o,i,r,e[u+13],4,681279174),r=Y(r,n,o,i,e[u+0],11,-358537222),i=Y(i,r,n,o,e[u+3],16,-722521979),o=Y(o,i,r,n,e[u+6],23,76029189),n=Y(n,o,i,r,e[u+9],4,-640364487),r=Y(r,n,o,i,e[u+12],11,-421815835),i=Y(i,r,n,o,e[u+15],16,530742520),n=Q(n,o=Y(o,i,r,n,e[u+2],23,-995338651),i,r,e[u+0],6,-198630844),r=Q(r,n,o,i,e[u+7],10,1126891415),i=Q(i,r,n,o,e[u+14],15,-1416354905),o=Q(o,i,r,n,e[u+5],21,-57434055),n=Q(n,o,i,r,e[u+12],6,1700485571),r=Q(r,n,o,i,e[u+3],10,-1894986606),i=Q(i,r,n,o,e[u+10],15,-1051523),o=Q(o,i,r,n,e[u+1],21,-2054922799),n=Q(n,o,i,r,e[u+8],6,1873313359),r=Q(r,n,o,i,e[u+15],10,-30611744),i=Q(i,r,n,o,e[u+6],15,-1560198380),o=Q(o,i,r,n,e[u+13],21,1309151649),n=Q(n,o,i,r,e[u+4],6,-145523070),r=Q(r,n,o,i,e[u+11],10,-1120210379),i=Q(i,r,n,o,e[u+2],15,718787259),o=Q(o,i,r,n,e[u+9],21,-343485551),n=Z(n,c),o=Z(o,a),i=Z(i,l),r=Z(r,f)}return Array(n,o,i,r)}(function(e){for(var t=Array(),n=(1<<X)-1,o=0;o<e.length*X;o+=X)t[o>>5]|=(e.charCodeAt(o/X)&n)<<o%32;return t}(e),e.length*X),n="0123456789abcdef",o="",i=0;i<4*t.length;i++)o+=n.charAt(t[i>>2]>>i%4*8+4&15)+n.charAt(t[i>>2]>>i%4*8&15);return o}function $(e,t,n,o,i,r){return Z((t=Z(Z(t,e),Z(o,r)))<<i|t>>>32-i,n)}function G(e,t,n,o,i,r,u){return $(t&n|~t&o,e,t,i,r,u)}function J(e,t,n,o,i,r,u){return $(t&o|n&~o,e,t,i,r,u)}function Y(e,t,n,o,i,r,u){return $(t^n^o,e,t,i,r,u)}function Q(e,t,n,o,i,r,u){return $(n^(t|~o),e,t,i,r,u)}function Z(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}var ee=function(){r(n,U);var e=l(n);function n(){return t(this,n),e.call(this,{type:C.RegToString,enabled:O.qqBrowser||O.firefox})}return o(n,[{key:"init",value:function(){var e=this;this.lastTime=0,this.reg=/./,g(this.reg),this.reg.toString=function(){var t;return O.qqBrowser?(t=(new Date).getTime(),e.lastTime&&t-e.lastTime<100?e.onDevToolOpen():e.lastTime=t):O.firefox&&e.onDevToolOpen(),""}}},{key:"detect",value:function(){g(this.reg)}}]),n}(),te=function(){r(n,U);var e=l(n);function n(){return t(this,n),e.call(this,{type:C.DefineId})}return o(n,[{key:"init",value:function(){var e=this;this.div=document.createElement("div"),this.div.__defineGetter__("id",(function(){e.onDevToolOpen()})),Object.defineProperty(this.div,"id",{get:function(){e.onDevToolOpen()}})}},{key:"detect",value:function(){g(this.div)}}]),n}(),ne=function(){r(n,U);var e=l(n);function n(){return t(this,n),e.call(this,{type:C.Size,enabled:!O.iframe&&!O.edge})}return o(n,[{key:"init",value:function(){var e=this;this.checkWindowSizeUneven(),window.addEventListener("resize",(function(){setTimeout((function(){e.checkWindowSizeUneven()}),100)}),!0)}},{key:"detect",value:function(){}},{key:"checkWindowSizeUneven",value:function(){if(!1!==(t=function(){if(oe(window.devicePixelRatio))return window.devicePixelRatio;var e=window.screen;return!(oe(e)||!e.deviceXDPI||!e.logicalXDPI)&&e.deviceXDPI/e.logicalXDPI}())){var e=200<window.outerWidth-window.innerWidth*t,t=300<window.outerHeight-window.innerHeight*t;if(e||t)return this.onDevToolOpen(),!1;z(this.type)}return!0}}]),n}();function oe(e){return null!=e}var ie,re=function(){r(n,U);var e=l(n);function n(){return t(this,n),e.call(this,{type:C.DateToString,enabled:!O.iosChrome&&!O.iosEdge})}return o(n,[{key:"init",value:function(){var e=this;this.count=0,this.date=new Date,this.date.toString=function(){return e.count++,""}}},{key:"detect",value:function(){this.count=0,g(this.date),k(),2<=this.count&&this.onDevToolOpen()}}]),n}(),ue=function(){r(n,U);var e=l(n);function n(){return t(this,n),e.call(this,{type:C.FuncToString,enabled:!O.iosChrome&&!O.iosEdge})}return o(n,[{key:"init",value:function(){var e=this;this.count=0,this.func=function(){},this.func.toString=function(){return e.count++,""}}},{key:"detect",value:function(){this.count=0,g(this.func),k(),2<=this.count&&this.onDevToolOpen()}}]),n}(),ce=function(){r(n,U);var e=l(n);function n(){return t(this,n),e.call(this,{type:C.Debugger,enabled:O.iosChrome||O.iosEdge})}return o(n,[{key:"detect",value:function(){var e=y();100<y()-e&&this.onDevToolOpen()}}]),n}(),ae=function(){r(n,U);var e=l(n);function n(){return t(this,n),e.call(this,{type:C.Performance,enabled:O.chrome||!O.mobile})}return o(n,[{key:"init",value:function(){this.maxPrintTime=0,this.largeObjectArray=D()}},{key:"detect",value:function(){var e=this,t=b((function(){m(e.largeObjectArray)})),n=b((function(){g(e.largeObjectArray)}));if(this.maxPrintTime=Math.max(this.maxPrintTime,n),k(),0===t||0===this.maxPrintTime)return!1;t>10*this.maxPrintTime&&this.onDevToolOpen()}}]),n}(),le=(i(ie={},C.RegToString,ee),i(ie,C.DefineId,te),i(ie,C.Size,ne),i(ie,C.DateToString,re),i(ie,C.FuncToString,ue),i(ie,C.Debugger,ce),i(ie,C.Performance,ae),i(ie,C.DebugLib,q),ie),fe=Object.assign((function(e){function t(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"";return{success:!e,reason:e}}var n,o,i,r;if(fe.isRunning)return t("already running");if(S(),n=window.console||{log:function(){},table:function(){},clear:function(){}},T=O.ie?(g=function(){return n.log.apply(n,arguments)},m=function(){return n.table.apply(n,arguments)},function(){return n.clear()}):(g=n.log,m=n.table,n.clear),p(e),v.md5&&N((o=v.tkName,i=window.location.search,r=window.location.hash,""!==(i=""===i&&""!==r?"?".concat(r.split("?")[1]):i)&&void 0!==i&&(r=new RegExp("(^|&)"+o+"=([^&]*)(&|$)","i"),null!=(o=i.substr(1).match(r)))?unescape(o[2]):""))===v.md5)return t("token passed");if(v.seo&&O.seoBot)return t("seobot");fe.isRunning=!0,V(fe);var u=fe,c=(E=function(){return u.isSuspend},window.top),a=window.parent;if(I(window),v.disableIframeParents&&c&&a&&c!==window){for(;a!==c;)I(a),a=a.parent;I(c)}return("all"===v.detectors?Object.keys(le):v.detectors).forEach((function(e){new le[e]})),t()}),{isRunning:!1,isSuspend:!1,md5:N,version:"0.3.8",DetectorType:C,isDevToolOpened:H});return(ee=function(){if("undefined"==typeof window||!window.document)return null;var e=document.querySelector("[disable-devtool-auto]");if(!e)return null;var t=["disable-menu","disable-select","disable-copy","disable-cut","disable-paste","clear-log"],n=["interval"],o={};return["md5","url","tk-name","detectors"].concat(t,n).forEach((function(i){var r=e.getAttribute(i);null!==r&&(-1!==n.indexOf(i)?r=parseInt(r):-1!==t.indexOf(i)?r="false"!==r:"detector"===i&&"all"!==r&&(r=r.split(" ")),o[function(e){if(-1===e.indexOf("-"))return e;var t=!1;return e.split("").map((function(e){return"-"===e?(t=!0,""):t?(t=!1,e.toUpperCase()):e})).join("")}(i)]=r)})),o}())&&fe(ee),fe}()),n.exports)),i=Object.freeze(Object.defineProperty({__proto__:null,default:o},Symbol.toStringTag,{value:"Module"}));export{i as d};
