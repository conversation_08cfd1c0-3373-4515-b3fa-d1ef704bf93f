import{r as e,l as t,h as n,d as r,m as a,a4 as o,a3 as l,n as i,p as u,q as c,o as s,i as d,e as v,T as f,a2 as g,a5 as m,v as h,w as b,L as p,M as C,O as k,J as x,ad as y,$ as S,R as E,at as $}from"./index-Cak6rALw.js";import{T as M}from"./MyApp-DW5WH4Ub.js";function w(e,t,n){return(e-t)/(n-t)}function O(e,t,n,r){var a=w(t,n,r),o={};switch(e){case"rtl":o.right="".concat(100*a,"%"),o.transform="translateX(50%)";break;case"btt":o.bottom="".concat(100*a,"%"),o.transform="translateY(50%)";break;case"ttb":o.top="".concat(100*a,"%"),o.transform="translateY(-50%)";break;default:o.left="".concat(100*a,"%"),o.transform="translateX(-50%)"}return o}function B(e,t){return Array.isArray(e)?e[t]:e}var D=e.createContext({min:0,max:0,direction:"ltr",step:1,includedStart:0,includedEnd:0,tabIndex:0,keyboard:!0,styles:{},classNames:{}}),P=e.createContext({}),j=["prefixCls","value","valueIndex","onStartMove","onDelete","style","render","dragging","draggingDelete","onOffsetChange","onChangeComplete","onFocus","onMouseEnter"],R=e.forwardRef((function(i,u){var c,s=i.prefixCls,d=i.value,v=i.valueIndex,f=i.onStartMove,g=i.onDelete,m=i.style,h=i.render,b=i.dragging,p=i.draggingDelete,C=i.onOffsetChange,k=i.onChangeComplete,x=i.onFocus,y=i.onMouseEnter,S=t(i,j),E=e.useContext(D),$=E.min,M=E.max,w=E.direction,P=E.disabled,R=E.keyboard,F=E.range,H=E.tabIndex,N=E.ariaLabelForHandle,I=E.ariaLabelledByForHandle,L=E.ariaRequired,A=E.ariaValueTextFormatterForHandle,q=E.styles,T=E.classNames,z="".concat(s,"-handle"),W=function(e){P||f(e,v)},V=O(w,d,$,M),X={};null!==v&&(X={tabIndex:P?null:B(H,v),role:"slider","aria-valuemin":$,"aria-valuemax":M,"aria-valuenow":d,"aria-disabled":P,"aria-label":B(N,v),"aria-labelledby":B(I,v),"aria-required":B(L,v),"aria-valuetext":null===(c=B(A,v))||void 0===c?void 0:c(d),"aria-orientation":"ltr"===w||"rtl"===w?"horizontal":"vertical",onMouseDown:W,onTouchStart:W,onFocus:function(e){null==x||x(e,v)},onMouseEnter:function(e){y(e,v)},onKeyDown:function(e){if(!P&&R){var t=null;switch(e.which||e.keyCode){case l.LEFT:t="ltr"===w||"btt"===w?-1:1;break;case l.RIGHT:t="ltr"===w||"btt"===w?1:-1;break;case l.UP:t="ttb"!==w?1:-1;break;case l.DOWN:t="ttb"!==w?-1:1;break;case l.HOME:t="min";break;case l.END:t="max";break;case l.PAGE_UP:t=2;break;case l.PAGE_DOWN:t=-2;break;case l.BACKSPACE:case l.DELETE:g(v)}null!==t&&(e.preventDefault(),C(t,v))}},onKeyUp:function(e){switch(e.which||e.keyCode){case l.LEFT:case l.RIGHT:case l.UP:case l.DOWN:case l.HOME:case l.END:case l.PAGE_UP:case l.PAGE_DOWN:null==k||k()}}});var G=e.createElement("div",n({ref:u,className:a(z,o(o(o({},"".concat(z,"-").concat(v+1),null!==v&&F),"".concat(z,"-dragging"),b),"".concat(z,"-dragging-delete"),p),T.handle),style:r(r(r({},V),m),q.handle)},X,S));return h&&(G=h(G,{index:v,prefixCls:s,value:d,dragging:b,draggingDelete:p})),G})),F=["prefixCls","style","onStartMove","onOffsetChange","values","handleRender","activeHandleRender","draggingIndex","draggingDelete","onFocus"],H=e.forwardRef((function(a,o){var l=a.prefixCls,c=a.style,s=a.onStartMove,d=a.onOffsetChange,v=a.values,f=a.handleRender,g=a.activeHandleRender,m=a.draggingIndex,h=a.draggingDelete,b=a.onFocus,p=t(a,F),C=e.useRef({}),k=e.useState(!1),x=i(k,2),y=x[0],S=x[1],E=e.useState(-1),$=i(E,2),M=$[0],w=$[1],O=function(e){w(e),S(!0)};e.useImperativeHandle(o,(function(){return{focus:function(e){var t;null===(t=C.current[e])||void 0===t||t.focus()},hideHelp:function(){u.flushSync((function(){S(!1)}))}}}));var D=r({prefixCls:l,onStartMove:s,onOffsetChange:d,render:f,onFocus:function(e,t){O(t),null==b||b(e)},onMouseEnter:function(e,t){O(t)}},p);return e.createElement(e.Fragment,null,v.map((function(t,r){var a=m===r;return e.createElement(R,n({ref:function(e){e?C.current[r]=e:delete C.current[r]},dragging:a,draggingDelete:a&&h,style:B(c,r),key:r,value:t,valueIndex:r},D))})),g&&y&&e.createElement(R,n({key:"a11y"},D,{value:v[M],valueIndex:null,dragging:-1!==m,draggingDelete:h,render:g,style:{pointerEvents:"none"},tabIndex:null,"aria-hidden":!0})))})),N=function(t){var n=t.prefixCls,l=t.style,i=t.children,u=t.value,c=t.onClick,s=e.useContext(D),d=s.min,v=s.max,f=s.direction,g=s.includedStart,m=s.includedEnd,h=s.included,b="".concat(n,"-text"),p=O(f,u,d,v);return e.createElement("span",{className:a(b,o({},"".concat(b,"-active"),h&&g<=u&&u<=m)),style:r(r({},p),l),onMouseDown:function(e){e.stopPropagation()},onClick:function(){c(u)}},i)},I=function(t){var n=t.prefixCls,r=t.marks,a=t.onClick,o="".concat(n,"-mark");return r.length?e.createElement("div",{className:o},r.map((function(t){var n=t.value,r=t.style,l=t.label;return e.createElement(N,{key:n,prefixCls:o,style:r,value:n,onClick:a},l)}))):null},L=function(t){var n=t.prefixCls,l=t.value,i=t.style,u=t.activeStyle,c=e.useContext(D),s=c.min,d=c.max,v=c.direction,f=c.included,g=c.includedStart,m=c.includedEnd,h="".concat(n,"-dot"),b=f&&g<=l&&l<=m,p=r(r({},O(v,l,s,d)),"function"==typeof i?i(l):i);return b&&(p=r(r({},p),"function"==typeof u?u(l):u)),e.createElement("span",{className:a(h,o({},"".concat(h,"-active"),b)),style:p})},A=function(t){var n=t.prefixCls,r=t.marks,a=t.dots,o=t.style,l=t.activeStyle,i=e.useContext(D),u=i.min,c=i.max,s=i.step,d=e.useMemo((function(){var e=new Set;if(r.forEach((function(t){e.add(t.value)})),a&&null!==s)for(var t=u;t<=c;)e.add(t),t+=s;return Array.from(e)}),[u,c,s,a,r]);return e.createElement("div",{className:"".concat(n,"-step")},d.map((function(t){return e.createElement(L,{prefixCls:n,key:t,value:t,style:o,activeStyle:l})})))},q=function(t){var n=t.prefixCls,l=t.style,i=t.start,u=t.end,c=t.index,s=t.onStartMove,d=t.replaceCls,v=e.useContext(D),f=v.direction,g=v.min,m=v.max,h=v.disabled,b=v.range,p=v.classNames,C="".concat(n,"-track"),k=w(i,g,m),x=w(u,g,m),y=function(e){!h&&s&&s(e,-1)},S={};switch(f){case"rtl":S.right="".concat(100*k,"%"),S.width="".concat(100*x-100*k,"%");break;case"btt":S.bottom="".concat(100*k,"%"),S.height="".concat(100*x-100*k,"%");break;case"ttb":S.top="".concat(100*k,"%"),S.height="".concat(100*x-100*k,"%");break;default:S.left="".concat(100*k,"%"),S.width="".concat(100*x-100*k,"%")}var E=d||a(C,o(o({},"".concat(C,"-").concat(c+1),null!==c&&b),"".concat(n,"-track-draggable"),s),p.track);return e.createElement("div",{className:E,style:r(r({},S),l),onMouseDown:y,onTouchStart:y})},T=function(t){var n=t.prefixCls,o=t.style,l=t.values,i=t.startPoint,u=t.onStartMove,c=e.useContext(D),s=c.included,d=c.range,v=c.min,f=c.styles,g=c.classNames,m=e.useMemo((function(){if(!d){if(0===l.length)return[];var e=null!=i?i:v,t=l[0];return[{start:Math.min(e,t),end:Math.max(e,t)}]}for(var n=[],r=0;r<l.length-1;r+=1)n.push({start:l[r],end:l[r+1]});return n}),[l,d,i,v]);if(!s)return null;var h=null!=m&&m.length&&(g.tracks||f.tracks)?e.createElement(q,{index:null,prefixCls:n,start:m[0].start,end:m[m.length-1].end,replaceCls:a(g.tracks,"".concat(n,"-tracks")),style:f.tracks}):null;return e.createElement(e.Fragment,null,h,m.map((function(t,a){var l=t.start,i=t.end;return e.createElement(q,{index:a,prefixCls:n,style:r(r({},B(o,a)),f.track),start:l,end:i,key:a,onStartMove:u})})))};function z(e){var t="targetTouches"in e?e.targetTouches[0]:e;return{pageX:t.pageX,pageY:t.pageY}}var W=e.forwardRef((function(t,n){var l=t.prefixCls,u=void 0===l?"rc-slider":l,h=t.className,b=t.style,p=t.classNames,C=t.styles,k=t.id,x=t.disabled,y=void 0!==x&&x,S=t.keyboard,E=void 0===S||S,$=t.autoFocus,M=t.onFocus,w=t.onBlur,O=t.min,B=void 0===O?0:O,j=t.max,R=void 0===j?100:j,F=t.step,N=void 0===F?1:F,L=t.value,q=t.defaultValue,W=t.range,V=t.count,X=t.onChange,G=t.onBeforeChange,Y=t.onAfterChange,U=t.onChangeComplete,_=t.allowCross,K=void 0===_||_,J=t.pushable,Q=void 0!==J&&J,Z=t.reverse,ee=t.vertical,te=t.included,ne=void 0===te||te,re=t.startPoint,ae=t.trackStyle,oe=t.handleStyle,le=t.railStyle,ie=t.dotStyle,ue=t.activeDotStyle,ce=t.marks,se=t.dots,de=t.handleRender,ve=t.activeHandleRender,fe=t.track,ge=t.tabIndex,me=void 0===ge?0:ge,he=t.ariaLabelForHandle,be=t.ariaLabelledByForHandle,pe=t.ariaRequired,Ce=t.ariaValueTextFormatterForHandle,ke=e.useRef(null),xe=e.useRef(null),ye=e.useMemo((function(){return ee?Z?"ttb":"btt":Z?"rtl":"ltr"}),[Z,ee]),Se=function(t){return e.useMemo((function(){if(!0===t||!t)return[!!t,!1,!1,0];var e=t.editable,n=t.draggableTrack;return[!0,e,!e&&n,t.minCount||0,t.maxCount]}),[t])}(W),Ee=i(Se,5),$e=Ee[0],Me=Ee[1],we=Ee[2],Oe=Ee[3],Be=Ee[4],De=e.useMemo((function(){return isFinite(B)?B:0}),[B]),Pe=e.useMemo((function(){return isFinite(R)?R:100}),[R]),je=e.useMemo((function(){return null!==N&&N<=0?1:N}),[N]),Re=e.useMemo((function(){return"boolean"==typeof Q?!!Q&&je:Q>=0&&Q}),[Q,je]),Fe=e.useMemo((function(){return Object.keys(ce||{}).map((function(t){var n=ce[t],r={value:Number(t)};return n&&"object"===v(n)&&!e.isValidElement(n)&&("label"in n||"style"in n)?(r.style=n.style,r.label=n.label):r.label=n,r})).filter((function(e){var t=e.label;return t||"number"==typeof t})).sort((function(e,t){return e.value-t.value}))}),[ce]),He=function(t,n,r,a,o,l){var i=e.useCallback((function(e){return Math.max(t,Math.min(n,e))}),[t,n]),u=e.useCallback((function(e){if(null!==r){var a=t+Math.round((i(e)-t)/r)*r,o=function(e){return(String(e).split(".")[1]||"").length},l=Math.max(o(r),o(n),o(t)),u=Number(a.toFixed(l));return t<=u&&u<=n?u:null}return null}),[r,t,n,i]),c=e.useCallback((function(e){var o=i(e),l=a.map((function(e){return e.value}));null!==r&&l.push(u(e)),l.push(t,n);var c=l[0],s=n-t;return l.forEach((function(e){var t=Math.abs(o-e);t<=s&&(c=e,s=t)})),c}),[t,n,a,r,i,u]),s=function e(o,l,i){var c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit";if("number"==typeof l){var s,v=o[i],f=v+l,g=[];a.forEach((function(e){g.push(e.value)})),g.push(t,n),g.push(u(v));var m=l>0?1:-1;"unit"===c?g.push(u(v+m*r)):g.push(u(f)),g=g.filter((function(e){return null!==e})).filter((function(e){return l<0?e<=v:e>=v})),"unit"===c&&(g=g.filter((function(e){return e!==v})));var h="unit"===c?v:f;s=g[0];var b=Math.abs(s-h);if(g.forEach((function(e){var t=Math.abs(e-h);t<b&&(s=e,b=t)})),void 0===s)return l<0?t:n;if("dist"===c)return s;if(Math.abs(l)>1){var p=d(o);return p[i]=s,e(p,l-m,i,c)}return s}return"min"===l?t:"max"===l?n:void 0},v=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit",a=e[n],o=s(e,t,n,r);return{value:o,changed:o!==a}},f=function(e){return null===l&&0===e||"number"==typeof l&&e<l};return[c,function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"unit",a=e.map(c),i=a[n],u=s(a,t,n,r);if(a[n]=u,!1===o){var d=l||0;n>0&&a[n-1]!==i&&(a[n]=Math.max(a[n],a[n-1]+d)),n<a.length-1&&a[n+1]!==i&&(a[n]=Math.min(a[n],a[n+1]-d))}else if("number"==typeof l||null===l){for(var g=n+1;g<a.length;g+=1)for(var m=!0;f(a[g]-a[g-1])&&m;){var h=v(a,1,g);a[g]=h.value,m=h.changed}for(var b=n;b>0;b-=1)for(var p=!0;f(a[b]-a[b-1])&&p;){var C=v(a,-1,b-1);a[b-1]=C.value,p=C.changed}for(var k=a.length-1;k>0;k-=1)for(var x=!0;f(a[k]-a[k-1])&&x;){var y=v(a,-1,k-1);a[k-1]=y.value,x=y.changed}for(var S=0;S<a.length-1;S+=1)for(var E=!0;f(a[S+1]-a[S])&&E;){var $=v(a,1,S+1);a[S+1]=$.value,E=$.changed}}return{value:a[n],values:a}}]}(De,Pe,je,Fe,K,Re),Ne=i(He,2),Ie=Ne[0],Le=Ne[1],Ae=f(q,{value:L}),qe=i(Ae,2),Te=qe[0],ze=qe[1],We=e.useMemo((function(){var e=null==Te?[]:Array.isArray(Te)?Te:[Te],t=i(e,1)[0],n=null===Te?[]:[void 0===t?De:t];if($e){if(n=d(e),V||void 0===Te){var r=V>=0?V+1:2;for(n=n.slice(0,r);n.length<r;){var a;n.push(null!==(a=n[n.length-1])&&void 0!==a?a:De)}}n.sort((function(e,t){return e-t}))}return n.forEach((function(e,t){n[t]=Ie(e)})),n}),[Te,$e,De,V,Ie]),Ve=function(e){return $e?e:e[0]},Xe=s((function(e){var t=d(e).sort((function(e,t){return e-t}));X&&!g(t,We,!0)&&X(Ve(t)),ze(t)})),Ge=s((function(e){e&&ke.current.hideHelp();var t=Ve(We);null==Y||Y(t),m(!Y,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),null==U||U(t)})),Ye=function(t,n,r,a,o,l,u,v,f,g,m){var h=e.useState(null),b=i(h,2),p=b[0],C=b[1],k=e.useState(-1),x=i(k,2),y=x[0],S=x[1],E=e.useState(!1),$=i(E,2),M=$[0],w=$[1],O=e.useState(r),B=i(O,2),D=B[0],j=B[1],R=e.useState(r),F=i(R,2),H=F[0],N=F[1],I=e.useRef(null),L=e.useRef(null),A=e.useRef(null),q=e.useContext(P),T=q.onDragStart,W=q.onDragChange;c((function(){-1===y&&j(r)}),[r,y]),e.useEffect((function(){return function(){document.removeEventListener("mousemove",I.current),document.removeEventListener("mouseup",L.current),A.current&&(A.current.removeEventListener("touchmove",I.current),A.current.removeEventListener("touchend",L.current))}}),[]);var V=function(e,t,n){void 0!==t&&C(t),j(e);var r=e;n&&(r=e.filter((function(e,t){return t!==y}))),u(r),W&&W({rawValues:e,deleteIndex:n?y:-1,draggingIndex:y,draggingValue:t})},X=s((function(e,t,n){if(-1===e){var r=H[0],i=H[H.length-1],u=a-r,c=o-i,s=t*(o-a);s=Math.max(s,u),s=Math.min(s,c);var v=l(r+s);s=v-r;var g=H.map((function(e){return e+s}));V(g)}else{var m=(o-a)*t,h=d(D);h[e]=H[e];var b=f(h,m,e,"dist");V(b.values,b.value,n)}})),G=e.useMemo((function(){var e=d(r).sort((function(e,t){return e-t})),t=d(D).sort((function(e,t){return e-t})),n={};t.forEach((function(e){n[e]=(n[e]||0)+1})),e.forEach((function(e){n[e]=(n[e]||0)-1}));var a=g?1:0;return Object.values(n).reduce((function(e,t){return e+Math.abs(t)}),0)<=a?D:r}),[r,D,g]);return[y,p,M,G,function(e,a,o){e.stopPropagation();var l=o||r,i=l[a];S(a),C(i),N(l),j(l),w(!1);var u=z(e),c=u.pageX,s=u.pageY,d=!1;T&&T({rawValues:l,draggingIndex:a,draggingValue:i});var f=function(e){e.preventDefault();var r,o,l=z(e),i=l.pageX,u=l.pageY,v=i-c,f=u-s,h=t.current.getBoundingClientRect(),b=h.width,p=h.height;switch(n){case"btt":r=-f/p,o=v;break;case"ttb":r=f/p,o=v;break;case"rtl":r=-v/b,o=f;break;default:r=v/b,o=f}d=!!g&&Math.abs(o)>130&&m<D.length,w(d),X(a,r,d)},h=function e(t){t.preventDefault(),document.removeEventListener("mouseup",e),document.removeEventListener("mousemove",f),A.current&&(A.current.removeEventListener("touchmove",I.current),A.current.removeEventListener("touchend",L.current)),I.current=null,L.current=null,A.current=null,v(d),S(-1),w(!1)};document.addEventListener("mouseup",h),document.addEventListener("mousemove",f),e.currentTarget.addEventListener("touchend",h),e.currentTarget.addEventListener("touchmove",f),I.current=f,L.current=h,A.current=e.currentTarget}]}(xe,ye,We,De,Pe,Ie,Xe,Ge,Le,Me,Oe),Ue=i(Ye,5),_e=Ue[0],Ke=Ue[1],Je=Ue[2],Qe=Ue[3],Ze=Ue[4],et=function(e,t){if(!y){var n=d(We),r=0,a=0,o=Pe-De;We.forEach((function(t,n){var l=Math.abs(e-t);l<=o&&(o=l,r=n),t<e&&(a=n)}));var l=r;Me&&0!==o&&(!Be||We.length<Be)?(n.splice(a+1,0,e),l=a+1):n[r]=e,$e&&!We.length&&void 0===V&&n.push(e);var i,u,c=Ve(n);if(null==G||G(c),Xe(n),t)null===(i=document.activeElement)||void 0===i||null===(u=i.blur)||void 0===u||u.call(i),ke.current.focus(l),Ze(t,l,n);else null==Y||Y(c),m(!Y,"[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead."),null==U||U(c)}},tt=e.useState(null),nt=i(tt,2),rt=nt[0],at=nt[1];e.useEffect((function(){if(null!==rt){var e=We.indexOf(rt);e>=0&&ke.current.focus(e)}at(null)}),[rt]);var ot=e.useMemo((function(){return(!we||null!==je)&&we}),[we,je]),lt=s((function(e,t){Ze(e,t),null==G||G(Ve(We))})),it=-1!==_e;e.useEffect((function(){if(!it){var e=We.lastIndexOf(Ke);ke.current.focus(e)}}),[it]);var ut=e.useMemo((function(){return d(Qe).sort((function(e,t){return e-t}))}),[Qe]),ct=e.useMemo((function(){return $e?[ut[0],ut[ut.length-1]]:[De,ut[0]]}),[ut,$e,De]),st=i(ct,2),dt=st[0],vt=st[1];e.useImperativeHandle(n,(function(){return{focus:function(){ke.current.focus(0)},blur:function(){var e,t=document.activeElement;null!==(e=xe.current)&&void 0!==e&&e.contains(t)&&(null==t||t.blur())}}})),e.useEffect((function(){$&&ke.current.focus(0)}),[]);var ft=e.useMemo((function(){return{min:De,max:Pe,direction:ye,disabled:y,keyboard:E,step:je,included:ne,includedStart:dt,includedEnd:vt,range:$e,tabIndex:me,ariaLabelForHandle:he,ariaLabelledByForHandle:be,ariaRequired:pe,ariaValueTextFormatterForHandle:Ce,styles:C||{},classNames:p||{}}}),[De,Pe,ye,y,E,je,ne,dt,vt,$e,me,he,be,pe,Ce,C,p]);return e.createElement(D.Provider,{value:ft},e.createElement("div",{ref:xe,className:a(u,h,o(o(o(o({},"".concat(u,"-disabled"),y),"".concat(u,"-vertical"),ee),"".concat(u,"-horizontal"),!ee),"".concat(u,"-with-marks"),Fe.length)),style:b,onMouseDown:function(e){e.preventDefault();var t,n=xe.current.getBoundingClientRect(),r=n.width,a=n.height,o=n.left,l=n.top,i=n.bottom,u=n.right,c=e.clientX,s=e.clientY;switch(ye){case"btt":t=(i-s)/a;break;case"ttb":t=(s-l)/a;break;case"rtl":t=(u-c)/r;break;default:t=(c-o)/r}et(Ie(De+t*(Pe-De)),e)},id:k},e.createElement("div",{className:a("".concat(u,"-rail"),null==p?void 0:p.rail),style:r(r({},le),null==C?void 0:C.rail)}),!1!==fe&&e.createElement(T,{prefixCls:u,style:ae,values:We,startPoint:re,onStartMove:ot?lt:void 0}),e.createElement(A,{prefixCls:u,marks:Fe,dots:se,style:ie,activeStyle:ue}),e.createElement(H,{ref:ke,prefixCls:u,style:oe,values:Qe,draggingIndex:_e,draggingDelete:Je,onStartMove:lt,onOffsetChange:function(e,t){if(!y){var n=Le(We,e,t);null==G||G(Ve(We)),Xe(n.values),at(n.value)}},onFocus:M,onBlur:w,handleRender:de,activeHandleRender:ve,onChangeComplete:Ge,onDelete:Me?function(e){if(!(y||!Me||We.length<=Oe)){var t=d(We);t.splice(e,1),null==G||G(Ve(t)),Xe(t);var n=Math.max(0,e-1);ke.current.hideHelp(),ke.current.focus(n)}}:void 0}),e.createElement(I,{prefixCls:u,marks:Fe,onClick:et})))}));const V=e.createContext({}),X=e.forwardRef(((t,n)=>{const{open:r,draggingDelete:a,value:o}=t,l=e.useRef(null),i=r&&!a,u=e.useRef(null);function c(){b.cancel(u.current),u.current=null}return e.useEffect((()=>(i?u.current=b((()=>{var e;null===(e=l.current)||void 0===e||e.forceAlign(),u.current=null})):c(),c)),[i,t.title,o]),e.createElement(M,Object.assign({ref:h(l,n)},t,{open:i}))})),G=e=>{const{componentCls:t,antCls:n,controlSize:r,dotSize:a,marginFull:o,marginPart:l,colorFillContentHover:i,handleColorDisabled:u,calc:c,handleSize:s,handleSizeHover:d,handleActiveColor:v,handleActiveOutlineColor:f,handleLineWidth:g,handleLineWidthHover:m,motionDurationMid:h}=e;return{[t]:Object.assign(Object.assign({},k(e)),{position:"relative",height:r,margin:`${x(l)} ${x(o)}`,padding:0,cursor:"pointer",touchAction:"none","&-vertical":{margin:`${x(o)} ${x(l)}`},[`${t}-rail`]:{position:"absolute",backgroundColor:e.railBg,borderRadius:e.borderRadiusXS,transition:`background-color ${h}`},[`${t}-track,${t}-tracks`]:{position:"absolute",transition:`background-color ${h}`},[`${t}-track`]:{backgroundColor:e.trackBg,borderRadius:e.borderRadiusXS},[`${t}-track-draggable`]:{boxSizing:"content-box",backgroundClip:"content-box",border:"solid rgba(0,0,0,0)"},"&:hover":{[`${t}-rail`]:{backgroundColor:e.railHoverBg},[`${t}-track`]:{backgroundColor:e.trackHoverBg},[`${t}-dot`]:{borderColor:i},[`${t}-handle::after`]:{boxShadow:`0 0 0 ${x(g)} ${e.colorPrimaryBorderHover}`},[`${t}-dot-active`]:{borderColor:e.dotActiveBorderColor}},[`${t}-handle`]:{position:"absolute",width:s,height:s,outline:"none",userSelect:"none","&-dragging-delete":{opacity:0},"&::before":{content:'""',position:"absolute",insetInlineStart:c(g).mul(-1).equal(),insetBlockStart:c(g).mul(-1).equal(),width:c(s).add(c(g).mul(2)).equal(),height:c(s).add(c(g).mul(2)).equal(),backgroundColor:"transparent"},"&::after":{content:'""',position:"absolute",insetBlockStart:0,insetInlineStart:0,width:s,height:s,backgroundColor:e.colorBgElevated,boxShadow:`0 0 0 ${x(g)} ${e.handleColor}`,outline:"0px solid transparent",borderRadius:"50%",cursor:"pointer",transition:`\n            inset-inline-start ${h},\n            inset-block-start ${h},\n            width ${h},\n            height ${h},\n            box-shadow ${h},\n            outline ${h}\n          `},"&:hover, &:active, &:focus":{"&::before":{insetInlineStart:c(d).sub(s).div(2).add(m).mul(-1).equal(),insetBlockStart:c(d).sub(s).div(2).add(m).mul(-1).equal(),width:c(d).add(c(m).mul(2)).equal(),height:c(d).add(c(m).mul(2)).equal()},"&::after":{boxShadow:`0 0 0 ${x(m)} ${v}`,outline:`6px solid ${f}`,width:d,height:d,insetInlineStart:e.calc(s).sub(d).div(2).equal(),insetBlockStart:e.calc(s).sub(d).div(2).equal()}}},[`&-lock ${t}-handle`]:{"&::before, &::after":{transition:"none"}},[`${t}-mark`]:{position:"absolute",fontSize:e.fontSize},[`${t}-mark-text`]:{position:"absolute",display:"inline-block",color:e.colorTextDescription,textAlign:"center",wordBreak:"keep-all",cursor:"pointer",userSelect:"none","&-active":{color:e.colorText}},[`${t}-step`]:{position:"absolute",background:"transparent",pointerEvents:"none"},[`${t}-dot`]:{position:"absolute",width:a,height:a,backgroundColor:e.colorBgElevated,border:`${x(g)} solid ${e.dotBorderColor}`,borderRadius:"50%",cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,pointerEvents:"auto","&-active":{borderColor:e.dotActiveBorderColor}},[`&${t}-disabled`]:{cursor:"not-allowed",[`${t}-rail`]:{backgroundColor:`${e.railBg} !important`},[`${t}-track`]:{backgroundColor:`${e.trackBgDisabled} !important`},[`\n          ${t}-dot\n        `]:{backgroundColor:e.colorBgElevated,borderColor:e.trackBgDisabled,boxShadow:"none",cursor:"not-allowed"},[`${t}-handle::after`]:{backgroundColor:e.colorBgElevated,cursor:"not-allowed",width:s,height:s,boxShadow:`0 0 0 ${x(g)} ${u}`,insetInlineStart:0,insetBlockStart:0},[`\n          ${t}-mark-text,\n          ${t}-dot\n        `]:{cursor:"not-allowed !important"}},[`&-tooltip ${n}-tooltip-inner`]:{minWidth:"unset"}})}},Y=(e,t)=>{const{componentCls:n,railSize:r,handleSize:a,dotSize:o,marginFull:l,calc:i}=e,u=t?"paddingBlock":"paddingInline",c=t?"width":"height",s=t?"height":"width",d=t?"insetBlockStart":"insetInlineStart",v=t?"top":"insetInlineStart",f=i(r).mul(3).sub(a).div(2).equal(),g=i(a).sub(r).div(2).equal(),m=t?{borderWidth:`${x(g)} 0`,transform:`translateY(${x(i(g).mul(-1).equal())})`}:{borderWidth:`0 ${x(g)}`,transform:`translateX(${x(e.calc(g).mul(-1).equal())})`};return{[u]:r,[s]:i(r).mul(3).equal(),[`${n}-rail`]:{[c]:"100%",[s]:r},[`${n}-track,${n}-tracks`]:{[s]:r},[`${n}-track-draggable`]:Object.assign({},m),[`${n}-handle`]:{[d]:f},[`${n}-mark`]:{insetInlineStart:0,top:0,[v]:i(r).mul(3).add(t?0:l).equal(),[c]:"100%"},[`${n}-step`]:{insetInlineStart:0,top:0,[v]:r,[c]:"100%",[s]:r},[`${n}-dot`]:{position:"absolute",[d]:i(r).sub(o).div(2).equal()}}},U=e=>{const{componentCls:t,marginPartWithMark:n}=e;return{[`${t}-horizontal`]:Object.assign(Object.assign({},Y(e,!0)),{[`&${t}-with-marks`]:{marginBottom:n}})}},_=e=>{const{componentCls:t}=e;return{[`${t}-vertical`]:Object.assign(Object.assign({},Y(e,!1)),{height:"100%"})}},K=p("Slider",(e=>{const t=C(e,{marginPart:e.calc(e.controlHeight).sub(e.controlSize).div(2).equal(),marginFull:e.calc(e.controlSize).div(2).equal(),marginPartWithMark:e.calc(e.controlHeightLG).sub(e.controlSize).equal()});return[G(t),U(t),_(t)]}),(e=>{const t=e.controlHeightLG/4,n=e.controlHeightSM/2,r=e.lineWidth+1,a=e.lineWidth+1.5,o=e.colorPrimary,l=new y(o).setA(.2).toRgbString();return{controlSize:t,railSize:4,handleSize:t,handleSizeHover:n,dotSize:8,handleLineWidth:r,handleLineWidthHover:a,railBg:e.colorFillTertiary,railHoverBg:e.colorFillSecondary,trackBg:e.colorPrimaryBorder,trackHoverBg:e.colorPrimaryBorderHover,handleColor:e.colorPrimaryBorder,handleActiveColor:o,handleActiveOutlineColor:l,handleColorDisabled:new y(e.colorTextDisabled).onBackground(e.colorBgContainer).toHexString(),dotBorderColor:e.colorBorderSecondary,dotActiveBorderColor:e.colorPrimaryBorder,trackBgDisabled:e.colorBgContainerDisabled}}));function J(){const[t,n]=e.useState(!1),r=e.useRef(null),a=()=>{b.cancel(r.current)};return e.useEffect((()=>a),[]),[t,e=>{a(),e?n(e):r.current=b((()=>{n(e)}))}]}const Q=S.forwardRef(((e,t)=>{const{prefixCls:n,range:r,className:o,rootClassName:l,style:i,disabled:u,tooltipPrefixCls:c,tipFormatter:s,tooltipVisible:d,getTooltipPopupContainer:v,tooltipPlacement:f,tooltip:g={},onChangeComplete:m,classNames:h,styles:p}=e,C=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n}(e,["prefixCls","range","className","rootClassName","style","disabled","tooltipPrefixCls","tipFormatter","tooltipVisible","getTooltipPopupContainer","tooltipPlacement","tooltip","onChangeComplete","classNames","styles"]),{vertical:k}=e,{getPrefixCls:x,direction:y,className:M,style:w,classNames:O,styles:B,getPopupContainer:D}=E("slider"),P=S.useContext($),j=null!=u?u:P,{handleRender:R,direction:F}=S.useContext(V),H="rtl"===(F||y),[N,I]=J(),[L,A]=J(),q=Object.assign({},g),{open:T,placement:z,getPopupContainer:G,prefixCls:Y,formatter:U}=q,_=null!=T?T:d,Q=(N||L)&&!1!==_,Z=function(e,t){return e||null===e?e:t||null===t?t:e=>"number"==typeof e?e.toString():""}(U,s),[ee,te]=J(),ne=(e,t)=>e||(t?H?"left":"right":"top"),re=x("slider",n),[ae,oe,le]=K(re),ie=a(o,M,O.root,null==h?void 0:h.root,l,{[`${re}-rtl`]:H,[`${re}-lock`]:ee},oe,le);H&&!C.vertical&&(C.reverse=!C.reverse),S.useEffect((()=>{const e=()=>{b((()=>{A(!1)}),1)};return document.addEventListener("mouseup",e),()=>{document.removeEventListener("mouseup",e)}}),[]);const ue=r&&!_,ce=R||((e,t)=>{const{index:n}=t,r=e.props;function a(e,t,n){var a,o,l,i;n&&(null===(o=(a=C)[e])||void 0===o||o.call(a,t)),null===(i=(l=r)[e])||void 0===i||i.call(l,t)}const o=Object.assign(Object.assign({},r),{onMouseEnter:e=>{I(!0),a("onMouseEnter",e)},onMouseLeave:e=>{I(!1),a("onMouseLeave",e)},onMouseDown:e=>{A(!0),te(!0),a("onMouseDown",e)},onFocus:e=>{var t;A(!0),null===(t=C.onFocus)||void 0===t||t.call(C,e),a("onFocus",e,!0)},onBlur:e=>{var t;A(!1),null===(t=C.onBlur)||void 0===t||t.call(C,e),a("onBlur",e,!0)}}),l=S.cloneElement(e,o),i=(!!_||Q)&&null!==Z;return ue?l:S.createElement(X,Object.assign({},q,{prefixCls:x("tooltip",null!=Y?Y:c),title:Z?Z(t.value):"",value:t.value,open:i,placement:ne(null!=z?z:f,k),key:n,classNames:{root:`${re}-tooltip`},getPopupContainer:G||v||D}),l)}),se=ue?(e,t)=>{const n=S.cloneElement(e,{style:Object.assign(Object.assign({},e.props.style),{visibility:"hidden"})});return S.createElement(X,Object.assign({},q,{prefixCls:x("tooltip",null!=Y?Y:c),title:Z?Z(t.value):"",open:null!==Z&&Q,placement:ne(null!=z?z:f,k),key:"tooltip",classNames:{root:`${re}-tooltip`},getPopupContainer:G||v||D,draggingDelete:t.draggingDelete}),n)}:void 0,de=Object.assign(Object.assign(Object.assign(Object.assign({},B.root),w),null==p?void 0:p.root),i),ve=Object.assign(Object.assign({},B.tracks),null==p?void 0:p.tracks),fe=a(O.tracks,null==h?void 0:h.tracks);return ae(S.createElement(W,Object.assign({},C,{classNames:Object.assign({handle:a(O.handle,null==h?void 0:h.handle),rail:a(O.rail,null==h?void 0:h.rail),track:a(O.track,null==h?void 0:h.track)},fe?{tracks:fe}:{}),styles:Object.assign({handle:Object.assign(Object.assign({},B.handle),null==p?void 0:p.handle),rail:Object.assign(Object.assign({},B.rail),null==p?void 0:p.rail),track:Object.assign(Object.assign({},B.track),null==p?void 0:p.track)},Object.keys(ve).length?{tracks:ve}:{}),step:C.step,range:r,className:ie,style:de,disabled:j,ref:t,prefixCls:re,handleRender:ce,activeHandleRender:se,onChangeComplete:e=>{null==m||m(e),te(!1)}})))}));export{V as S,P as U,Q as a};
