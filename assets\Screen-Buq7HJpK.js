import{b0 as e}from"./index-Cak6rALw.js";import{C as t}from"./col-CzA09p0P.js";import{R as l}from"./row-BMfM-of4.js";import{c as i}from"./MyApp-DW5WH4Ub.js";function r({children:r,title:s,titleLevel:n=3,titleSuffix:o,mode:c="left",style:d={}}){return e.jsxs(t,{style:{...d},children:[s&&e.jsxs(l,{align:"middle",justify:"center"===c?"center":"start",style:{padding:16},children:[s&&e.jsx(i.Title,{level:n,style:{margin:0},children:s}),o]}),e.jsx("div",{style:{display:"flex",flexDirection:"column",alignItems:"center"===c?"center":"flex-start",maxWidth:"100vw"},children:r})]})}export{r as S};
