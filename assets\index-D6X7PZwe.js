import{d as e,a5 as t,a6 as n,i as o,e as r,t as a,l as i,r as d,I as l,h as s,m as c,a4 as u,$ as f,n as p,aj as h,q as g,C as v,_ as y,a as m,b as k,bV as b,a3 as K,c as x,L as N,ab as E,M as C,O as w,J as S,K as D,a9 as P,aK as O,Z as $,G as L,ag as M,Q as T}from"./index-Cak6rALw.js";import{L as I}from"./List-B6ZMXgC_.js";import{g as R}from"./index-CxAc8H5Q.js";function A(e,t){return e[t]}var B=["children"];function H(e,t){return"".concat(e,"-").concat(t)}function j(e,t){return null!=e?e:t}function z(e){var t=e||{},n=t.title||"title";return{title:n,_title:t._title||[n],key:t.key||"key",children:t.children||"children"}}function q(n){return function n(o){return a(o).map((function(o){if(!function(e){return e&&e.type&&e.type.isTreeNode}(o))return t(!o,"Tree/TreeNode can only accept TreeNode as children."),null;var r=o.key,a=o.props,d=a.children,l=i(a,B),s=e({key:r},l),c=n(d);return c.length&&(s.children=c),s})).filter((function(e){return e}))}(n)}function V(e,t,r){var a=z(r),i=a._title,d=a.key,l=a.children,s=new Set(!0===t?[]:t),c=[];return function e(r){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return r.map((function(u,f){for(var p,h=H(a?a.pos:"0",f),g=j(u[d],h),v=0;v<i.length;v+=1){var y=i[v];if(void 0!==u[y]){p=u[y];break}}var m=Object.assign(n(u,[].concat(o(i),[d,l])),{title:p,key:g,parent:a,pos:h,children:null,data:u,isStart:[].concat(o(a?a.isStart:[]),[0===f]),isEnd:[].concat(o(a?a.isEnd:[]),[f===r.length-1])});return c.push(m),!0===t||s.has(g)?m.children=e(u[l]||[],m):m.children=[],m}))}(e),c}function W(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.initWrapper,a=t.processEntity,i=t.onProcessFinished,d=t.externalGetKey,l=t.childrenPropName,s=t.fieldNames,c=d||(arguments.length>2?arguments[2]:void 0),u={},f={},p={posEntities:u,keyEntities:f};return n&&(p=n(p)||p),function(e,t,n){var a,i=("object"===r(n)?n:{externalGetKey:n})||{},d=i.childrenPropName,l=i.externalGetKey,s=z(i.fieldNames),c=s.key,u=s.children,f=d||u;l?"string"==typeof l?a=function(e){return e[l]}:"function"==typeof l&&(a=function(e){return l(e)}):a=function(e,t){return j(e[c],t)},function n(r,i,d,l){var s=r?r[f]:e,c=r?H(d.pos,i):"0",u=r?[].concat(o(l),[r]):[];if(r){var p=a(r,c),h={node:r,index:i,pos:c,key:p,parentPos:d.node?d.pos:null,level:d.level+1,nodes:u};t(h)}s&&s.forEach((function(e,t){n(e,t,{node:r,pos:c,level:d?d.level+1:-1},u)}))}(null)}(e,(function(e){var t=e.node,n=e.index,o=e.pos,r=e.key,i=e.parentPos,d=e.level,l={node:t,nodes:e.nodes,index:n,key:r,pos:o,level:d},s=j(r,o);u[o]=l,f[s]=l,l.parent=u[i],l.parent&&(l.parent.children=l.parent.children||[],l.parent.children.push(l)),a&&a(l,p)}),{externalGetKey:c,childrenPropName:l,fieldNames:s}),i&&i(p),p}function _(e,t){var n=t.expandedKeys,o=t.selectedKeys,r=t.loadedKeys,a=t.loadingKeys,i=t.checkedKeys,d=t.halfCheckedKeys,l=t.dragOverNodeKey,s=t.dropPosition,c=A(t.keyEntities,e);return{eventKey:e,expanded:-1!==n.indexOf(e),selected:-1!==o.indexOf(e),loaded:-1!==r.indexOf(e),loading:-1!==a.indexOf(e),checked:-1!==i.indexOf(e),halfChecked:-1!==d.indexOf(e),pos:String(c?c.pos:""),dragOver:l===e&&0===s,dragOverGapTop:l===e&&-1===s,dragOverGapBottom:l===e&&1===s}}function G(n){var o=n.data,r=n.expanded,a=n.selected,i=n.checked,d=n.loaded,l=n.loading,s=n.halfChecked,c=n.dragOver,u=n.dragOverGapTop,f=n.dragOverGapBottom,p=n.pos,h=n.active,g=n.eventKey,v=e(e({},o),{},{expanded:r,selected:a,checked:i,loaded:d,loading:l,halfChecked:s,dragOver:c,dragOverGapTop:u,dragOverGapBottom:f,pos:p,active:h,key:g});return"props"in v||Object.defineProperty(v,"props",{get:function(){return t(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),n}}),v}function U(e,t){var n=new Set;return e.forEach((function(e){t.has(e)||n.add(e)})),n}function F(e){var t=e||{},n=t.disabled,o=t.disableCheckbox,r=t.checkable;return!(!n&&!o)||!1===r}function X(e,n,o,r){var a,i=[];a=r||F;var d,l=new Set(e.filter((function(e){var t=!!A(o,e);return t||i.push(e),t}))),s=new Map,c=0;return Object.keys(o).forEach((function(e){var t=o[e],n=t.level,r=s.get(n);r||(r=new Set,s.set(n,r)),r.add(t),c=Math.max(c,n)})),t(!i.length,"Tree missing follow keys: ".concat(i.slice(0,100).map((function(e){return"'".concat(e,"'")})).join(", "))),d=!0===n?function(e,t,n,o){for(var r=new Set(e),a=new Set,i=0;i<=n;i+=1)(t.get(i)||new Set).forEach((function(e){var t=e.key,n=e.node,a=e.children,i=void 0===a?[]:a;r.has(t)&&!o(n)&&i.filter((function(e){return!o(e.node)})).forEach((function(e){r.add(e.key)}))}));for(var d=new Set,l=n;l>=0;l-=1)(t.get(l)||new Set).forEach((function(e){var t=e.parent,n=e.node;if(!o(n)&&e.parent&&!d.has(e.parent.key))if(o(e.parent.node))d.add(t.key);else{var i=!0,l=!1;(t.children||[]).filter((function(e){return!o(e.node)})).forEach((function(e){var t=e.key,n=r.has(t);i&&!n&&(i=!1),l||!n&&!a.has(t)||(l=!0)})),i&&r.add(t.key),l&&a.add(t.key),d.add(t.key)}}));return{checkedKeys:Array.from(r),halfCheckedKeys:Array.from(U(a,r))}}(l,s,c,a):function(e,t,n,o,r){for(var a=new Set(e),i=new Set(t),d=0;d<=o;d+=1)(n.get(d)||new Set).forEach((function(e){var t=e.key,n=e.node,o=e.children,d=void 0===o?[]:o;a.has(t)||i.has(t)||r(n)||d.filter((function(e){return!r(e.node)})).forEach((function(e){a.delete(e.key)}))}));i=new Set;for(var l=new Set,s=o;s>=0;s-=1)(n.get(s)||new Set).forEach((function(e){var t=e.parent,n=e.node;if(!r(n)&&e.parent&&!l.has(e.parent.key))if(r(e.parent.node))l.add(t.key);else{var o=!0,d=!1;(t.children||[]).filter((function(e){return!r(e.node)})).forEach((function(e){var t=e.key,n=a.has(t);o&&!n&&(o=!1),d||!n&&!i.has(t)||(d=!0)})),o||a.delete(t.key),d&&i.add(t.key),l.add(t.key)}}));return{checkedKeys:Array.from(a),halfCheckedKeys:Array.from(U(i,a))}}(l,n.halfCheckedKeys,s,c,a),d}var Q={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"},Y=function(e,t){return d.createElement(l,s({},e,{ref:t,icon:Q}))},J=d.forwardRef(Y),Z={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"},ee=function(e,t){return d.createElement(l,s({},e,{ref:t,icon:Z}))},te=d.forwardRef(ee),ne={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"},oe=function(e,t){return d.createElement(l,s({},e,{ref:t,icon:ne}))},re=d.forwardRef(oe),ae={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"},ie=function(e,t){return d.createElement(l,s({},e,{ref:t,icon:ae}))},de=d.forwardRef(ie),le={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z"}}]},name:"holder",theme:"outlined"},se=function(e,t){return d.createElement(l,s({},e,{ref:t,icon:le}))},ce=d.forwardRef(se),ue={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"},fe=function(e,t){return d.createElement(l,s({},e,{ref:t,icon:ue}))},pe=d.forwardRef(fe),he={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"},ge=function(e,t){return d.createElement(l,s({},e,{ref:t,icon:he}))},ve=d.forwardRef(ge),ye=d.createContext(null),me=d.createContext({}),ke=function(e){for(var t=e.prefixCls,n=e.level,o=e.isStart,r=e.isEnd,a="".concat(t,"-indent-unit"),i=[],l=0;l<n;l+=1)i.push(d.createElement("span",{key:l,className:c(a,u(u({},"".concat(a,"-start"),o[l]),"".concat(a,"-end"),r[l]))}));return d.createElement("span",{"aria-hidden":"true",className:"".concat(t,"-indent")},i)};const be=d.memo(ke);var Ke=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],xe="open",Ne="close",Ee=function(t){var n,o,r,a=t.eventKey,d=t.className,l=t.style,g=t.dragOver,v=t.dragOverGapTop,y=t.dragOverGapBottom,m=t.isLeaf,k=t.isStart,b=t.isEnd,K=t.expanded,x=t.selected,N=t.checked,E=t.halfChecked,C=t.loading,w=t.domRef,S=t.active,D=t.data,P=t.onMouseMove,O=t.selectable,$=i(t,Ke),L=f.useContext(ye),M=f.useContext(me),T=f.useRef(null),I=f.useState(!1),R=p(I,2),B=R[0],H=R[1],j=!!(L.disabled||t.disabled||null!==(n=M.nodeDisabled)&&void 0!==n&&n.call(M,D)),z=f.useMemo((function(){return!(!L.checkable||!1===t.checkable)&&L.checkable}),[L.checkable,t.checkable]),q=function(e){j||z&&!t.disableCheckbox&&L.onNodeCheck(e,G(t),!N)},V=f.useMemo((function(){return"boolean"==typeof O?O:L.selectable}),[O,L.selectable]),W=function(e){L.onNodeClick(e,G(t)),V?function(e){j||L.onNodeSelect(e,G(t))}(e):q(e)},_=function(e){L.onNodeDoubleClick(e,G(t))},U=function(e){L.onNodeMouseEnter(e,G(t))},F=function(e){L.onNodeMouseLeave(e,G(t))},X=function(e){L.onNodeContextMenu(e,G(t))},Q=f.useMemo((function(){return!(!L.draggable||L.draggable.nodeDraggable&&!L.draggable.nodeDraggable(D))}),[L.draggable,D]),Y=function(e){C||L.onNodeExpand(e,G(t))},J=f.useMemo((function(){var e=(A(L.keyEntities,a)||{}).children;return Boolean((e||[]).length)}),[L.keyEntities,a]),Z=f.useMemo((function(){return!1!==m&&(m||!L.loadData&&!J||L.loadData&&t.loaded&&!J)}),[m,L.loadData,J,t.loaded]);f.useEffect((function(){C||"function"!=typeof L.loadData||!K||Z||t.loaded||L.onNodeLoad(G(t))}),[C,L.loadData,L.onNodeLoad,K,Z,t]);var ee=f.useMemo((function(){var e;return null!==(e=L.draggable)&&void 0!==e&&e.icon?f.createElement("span",{className:"".concat(L.prefixCls,"-draggable-icon")},L.draggable.icon):null}),[L.draggable]),te=function(n){var o=t.switcherIcon||L.switcherIcon;return"function"==typeof o?o(e(e({},t),{},{isLeaf:n})):o},ne=f.useMemo((function(){if(!z)return null;var e="boolean"!=typeof z?z:null;return f.createElement("span",{className:c("".concat(L.prefixCls,"-checkbox"),u(u(u({},"".concat(L.prefixCls,"-checkbox-checked"),N),"".concat(L.prefixCls,"-checkbox-indeterminate"),!N&&E),"".concat(L.prefixCls,"-checkbox-disabled"),j||t.disableCheckbox)),onClick:q,role:"checkbox","aria-checked":E?"mixed":N,"aria-disabled":j||t.disableCheckbox,"aria-label":"Select ".concat("string"==typeof t.title?t.title:"tree node")},e)}),[z,N,E,j,t.disableCheckbox,t.title]),oe=f.useMemo((function(){return Z?null:K?xe:Ne}),[Z,K]),re=f.useMemo((function(){return f.createElement("span",{className:c("".concat(L.prefixCls,"-iconEle"),"".concat(L.prefixCls,"-icon__").concat(oe||"docu"),u({},"".concat(L.prefixCls,"-icon_loading"),C))})}),[L.prefixCls,oe,C]),ae=f.useMemo((function(){var e=Boolean(L.draggable);return!t.disabled&&e&&L.dragOverNodeKey===a?L.dropIndicatorRender({dropPosition:L.dropPosition,dropLevelOffset:L.dropLevelOffset,indent:L.indent,prefixCls:L.prefixCls,direction:L.direction}):null}),[L.dropPosition,L.dropLevelOffset,L.indent,L.prefixCls,L.direction,L.draggable,L.dragOverNodeKey,L.dropIndicatorRender]),ie=f.useMemo((function(){var e,n,o=t.title,r=void 0===o?"---":o,a="".concat(L.prefixCls,"-node-content-wrapper");if(L.showIcon){var i=t.icon||L.icon;e=i?f.createElement("span",{className:c("".concat(L.prefixCls,"-iconEle"),"".concat(L.prefixCls,"-icon__customize"))},"function"==typeof i?i(t):i):re}else L.loadData&&C&&(e=re);return n="function"==typeof r?r(D):L.titleRender?L.titleRender(D):r,f.createElement("span",{ref:T,title:"string"==typeof r?r:"",className:c(a,"".concat(a,"-").concat(oe||"normal"),u({},"".concat(L.prefixCls,"-node-selected"),!j&&(x||B))),onMouseEnter:U,onMouseLeave:F,onContextMenu:X,onClick:W,onDoubleClick:_},e,f.createElement("span",{className:"".concat(L.prefixCls,"-title")},n),ae)}),[L.prefixCls,L.showIcon,t,L.icon,re,L.titleRender,D,oe,U,F,X,W,_]),de=h($,{aria:!0,data:!0}),le=(A(L.keyEntities,a)||{}).level,se=b[b.length-1],ce=!j&&Q,ue=L.draggingNodeKey===a,fe=void 0!==O?{"aria-selected":!!O}:void 0;return f.createElement("div",s({ref:w,role:"treeitem","aria-expanded":m?void 0:K,className:c(d,"".concat(L.prefixCls,"-treenode"),(r={},u(u(u(u(u(u(u(u(u(u(r,"".concat(L.prefixCls,"-treenode-disabled"),j),"".concat(L.prefixCls,"-treenode-switcher-").concat(K?"open":"close"),!m),"".concat(L.prefixCls,"-treenode-checkbox-checked"),N),"".concat(L.prefixCls,"-treenode-checkbox-indeterminate"),E),"".concat(L.prefixCls,"-treenode-selected"),x),"".concat(L.prefixCls,"-treenode-loading"),C),"".concat(L.prefixCls,"-treenode-active"),S),"".concat(L.prefixCls,"-treenode-leaf-last"),se),"".concat(L.prefixCls,"-treenode-draggable"),Q),"dragging",ue),u(u(u(u(u(u(u(r,"drop-target",L.dropTargetKey===a),"drop-container",L.dropContainerKey===a),"drag-over",!j&&g),"drag-over-gap-top",!j&&v),"drag-over-gap-bottom",!j&&y),"filter-node",null===(o=L.filterTreeNode)||void 0===o?void 0:o.call(L,G(t))),"".concat(L.prefixCls,"-treenode-leaf"),Z))),style:l,draggable:ce,onDragStart:ce?function(e){e.stopPropagation(),H(!0),L.onNodeDragStart(e,t);try{e.dataTransfer.setData("text/plain","")}catch(n){}}:void 0,onDragEnter:Q?function(e){e.preventDefault(),e.stopPropagation(),L.onNodeDragEnter(e,t)}:void 0,onDragOver:Q?function(e){e.preventDefault(),e.stopPropagation(),L.onNodeDragOver(e,t)}:void 0,onDragLeave:Q?function(e){e.stopPropagation(),L.onNodeDragLeave(e,t)}:void 0,onDrop:Q?function(e){e.preventDefault(),e.stopPropagation(),H(!1),L.onNodeDrop(e,t)}:void 0,onDragEnd:Q?function(e){e.stopPropagation(),H(!1),L.onNodeDragEnd(e,t)}:void 0,onMouseMove:P},fe,de),f.createElement(be,{prefixCls:L.prefixCls,level:le,isStart:k,isEnd:b}),ee,function(){if(Z){var e=te(!0);return!1!==e?f.createElement("span",{className:c("".concat(L.prefixCls,"-switcher"),"".concat(L.prefixCls,"-switcher-noop"))},e):null}var t=te(!1);return!1!==t?f.createElement("span",{onClick:Y,className:c("".concat(L.prefixCls,"-switcher"),"".concat(L.prefixCls,"-switcher_").concat(K?xe:Ne))},t):null}(),ne,ie)};function Ce(e,t){if(!e)return[];var n=e.slice(),o=n.indexOf(t);return o>=0&&n.splice(o,1),n}function we(e,t){var n=(e||[]).slice();return-1===n.indexOf(t)&&n.push(t),n}function Se(e){return e.split("-")}function De(e,t){var n=[];return function e(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).forEach((function(t){var o=t.key,r=t.children;n.push(o),e(r)}))}(A(t,e).children),n}function Pe(e){if(e.parent){var t=Se(e.pos);return Number(t[t.length-1])===e.parent.children.length-1}return!1}function Oe(e,t,n,o,r,a,i,d,l,s){var c,u=e.clientX,f=e.clientY,p=e.target.getBoundingClientRect(),h=p.top,g=p.height,v=(("rtl"===s?-1:1)*(((null==r?void 0:r.x)||0)-u)-12)/o,y=l.filter((function(e){var t;return null===(t=d[e])||void 0===t||null===(t=t.children)||void 0===t?void 0:t.length})),m=A(d,n.eventKey);if(f<h+g/2){var k=i.findIndex((function(e){return e.key===m.key})),b=i[k<=0?0:k-1].key;m=A(d,b)}var K=m.key,x=m,N=m.key,E=0,C=0;if(!y.includes(K))for(var w=0;w<v&&Pe(m);w+=1)m=m.parent,C+=1;var S,D=t.data,P=m.node,O=!0;return S=Se(m.pos),0===Number(S[S.length-1])&&0===m.level&&f<h+g/2&&a({dragNode:D,dropNode:P,dropPosition:-1})&&m.key===n.eventKey?E=-1:(x.children||[]).length&&y.includes(N)?a({dragNode:D,dropNode:P,dropPosition:0})?E=0:O=!1:0===C?v>-1.5?a({dragNode:D,dropNode:P,dropPosition:1})?E=1:O=!1:a({dragNode:D,dropNode:P,dropPosition:0})?E=0:a({dragNode:D,dropNode:P,dropPosition:1})?E=1:O=!1:a({dragNode:D,dropNode:P,dropPosition:1})?E=1:O=!1,{dropPosition:E,dropLevelOffset:C,dropTargetKey:m.key,dropTargetPos:m.pos,dragOverNodeKey:N,dropContainerKey:0===E?null:(null===(c=m.parent)||void 0===c?void 0:c.key)||null,dropAllowed:O}}function $e(e,t){if(e)return t.multiple?e.slice():e.length?[e[0]]:e}function Le(e){if(!e)return null;var n;if(Array.isArray(e))n={checkedKeys:e,halfCheckedKeys:void 0};else{if("object"!==r(e))return t(!1,"`checkedKeys` is not an array or an object"),null;n={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0}}return n}function Me(e,t){var n=new Set;function r(e){if(!n.has(e)){var o=A(t,e);if(o){n.add(e);var a=o.parent;o.node.disabled||a&&r(a.key)}}}return(e||[]).forEach((function(e){r(e)})),o(n)}Ee.isTreeNode=1;function Te(e){if(null==e)throw new TypeError("Cannot destructure "+e)}var Ie=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],Re=d.forwardRef((function(e,t){var n=e.className,o=e.style,r=e.motion,a=e.motionNodes,l=e.motionType,u=e.onMotionStart,f=e.onMotionEnd,h=e.active,y=e.treeNodeRequiredProps,m=i(e,Ie),k=d.useState(!0),b=p(k,2),K=b[0],x=b[1],N=d.useContext(ye).prefixCls,E=a&&"hide"!==l;g((function(){a&&E!==K&&x(E)}),[a]);var C=d.useRef(!1),w=function(){a&&!C.current&&(C.current=!0,f())};!function(e,t){var n=d.useState(!1),o=p(n,2),r=o[0],a=o[1];g((function(){if(r)return e(),function(){t()}}),[r]),g((function(){return a(!0),function(){a(!1)}}),[])}((function(){a&&u()}),w);return a?d.createElement(v,s({ref:t,visible:K},r,{motionAppear:"show"===l,onVisibleChanged:function(e){E===e&&w()}}),(function(e,t){var n=e.className,o=e.style;return d.createElement("div",{ref:t,className:c("".concat(N,"-treenode-motion"),n),style:o},a.map((function(e){var t=Object.assign({},(Te(e.data),e.data)),n=e.title,o=e.key,r=e.isStart,a=e.isEnd;delete t.children;var i=_(o,y);return d.createElement(Ee,s({},t,i,{title:n,active:h,data:e.data,key:o,isStart:r,isEnd:a}))})))})):d.createElement(Ee,s({domRef:t,className:n,style:o},m,{active:h}))}));function Ae(e,t,n){var o=e.findIndex((function(e){return e.key===n})),r=e[o+1],a=t.findIndex((function(e){return e.key===n}));if(r){var i=t.findIndex((function(e){return e.key===r.key}));return t.slice(a+1,i)}return t.slice(a+1)}var Be=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","scrollWidth","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],He={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},je=function(){},ze="RC_TREE_MOTION_".concat(Math.random()),qe={key:ze},Ve={key:ze,level:0,index:0,pos:"0",node:qe,nodes:[qe]},We={parent:null,children:[],pos:Ve.pos,data:qe,title:null,key:ze,isStart:[],isEnd:[]};function _e(e,t,n,o){return!1!==t&&n?e.slice(0,Math.ceil(n/o)+1):e}function Ge(e){return j(e.key,e.pos)}var Ue=d.forwardRef((function(e,t){var n=e.prefixCls,o=e.data;e.selectable,e.checkable;var r=e.expandedKeys,a=e.selectedKeys,l=e.checkedKeys,c=e.loadedKeys,u=e.loadingKeys,f=e.halfCheckedKeys,h=e.keyEntities,v=e.disabled,y=e.dragging,m=e.dragOverNodeKey,k=e.dropPosition,b=e.motion,K=e.height,x=e.itemHeight,N=e.virtual,E=e.scrollWidth,C=e.focusable,w=e.activeItem,S=e.focused,D=e.tabIndex,P=e.onKeyDown,O=e.onFocus,$=e.onBlur,L=e.onActiveChange,M=e.onListChangeStart,T=e.onListChangeEnd,R=i(e,Be),A=d.useRef(null),B=d.useRef(null);d.useImperativeHandle(t,(function(){return{scrollTo:function(e){A.current.scrollTo(e)},getIndentWidth:function(){return B.current.offsetWidth}}}));var H=d.useState(r),z=p(H,2),q=z[0],V=z[1],W=d.useState(o),G=p(W,2),U=G[0],F=G[1],X=d.useState(o),Q=p(X,2),Y=Q[0],J=Q[1],Z=d.useState([]),ee=p(Z,2),te=ee[0],ne=ee[1],oe=d.useState(null),re=p(oe,2),ae=re[0],ie=re[1],de=d.useRef(o);function le(){var e=de.current;F(e),J(e),ne([]),ie(null),T()}de.current=o,g((function(){V(r);var e=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=e.length,o=t.length;if(1!==Math.abs(n-o))return{add:!1,key:null};function r(e,t){var n=new Map;e.forEach((function(e){n.set(e,!0)}));var o=t.filter((function(e){return!n.has(e)}));return 1===o.length?o[0]:null}return n<o?{add:!0,key:r(e,t)}:{add:!1,key:r(t,e)}}(q,r);if(null!==e.key)if(e.add){var t=U.findIndex((function(t){return t.key===e.key})),n=_e(Ae(U,o,e.key),N,K,x),a=U.slice();a.splice(t+1,0,We),J(a),ne(n),ie("show")}else{var i=o.findIndex((function(t){return t.key===e.key})),d=_e(Ae(o,U,e.key),N,K,x),l=o.slice();l.splice(i+1,0,We),J(l),ne(d),ie("hide")}else U!==o&&(F(o),J(o))}),[r,o]),d.useEffect((function(){y||le()}),[y]);var se=b?Y:o,ce={expandedKeys:r,selectedKeys:a,loadedKeys:c,loadingKeys:u,checkedKeys:l,halfCheckedKeys:f,dragOverNodeKey:m,dropPosition:k,keyEntities:h};return d.createElement(d.Fragment,null,S&&w&&d.createElement("span",{style:He,"aria-live":"assertive"},function(e){for(var t=String(e.data.key),n=e;n.parent;)n=n.parent,t="".concat(n.data.key," > ").concat(t);return t}(w)),d.createElement("div",null,d.createElement("input",{style:He,disabled:!1===C||v,tabIndex:!1!==C?D:null,onKeyDown:P,onFocus:O,onBlur:$,value:"",onChange:je,"aria-label":"for screen reader"})),d.createElement("div",{className:"".concat(n,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},d.createElement("div",{className:"".concat(n,"-indent")},d.createElement("div",{ref:B,className:"".concat(n,"-indent-unit")}))),d.createElement(I,s({},R,{data:se,itemKey:Ge,height:K,fullHeight:!1,virtual:N,itemHeight:x,scrollWidth:E,prefixCls:"".concat(n,"-list"),ref:A,role:"tree",onVisibleChange:function(e){e.every((function(e){return Ge(e)!==ze}))&&le()}}),(function(e){var t=e.pos,n=Object.assign({},(Te(e.data),e.data)),o=e.title,r=e.key,a=e.isStart,i=e.isEnd,l=j(r,t);delete n.key,delete n.children;var c=_(l,ce);return d.createElement(Re,s({},n,c,{title:o,active:!!w&&r===w.key,pos:t,data:e.data,isStart:a,isEnd:i,motion:b,motionNodes:r===ze?te:null,motionType:ae,onMotionStart:M,onMotionEnd:le,treeNodeRequiredProps:ce,onMouseMove:function(){L(null)}}))})))})),Fe=function(n){y(i,n);var a=m(i);function i(){var n;k(this,i);for(var r=arguments.length,l=new Array(r),s=0;s<r;s++)l[s]=arguments[s];return n=a.call.apply(a,[this].concat(l)),u(b(n),"destroyed",!1),u(b(n),"delayedDragEnterLogic",void 0),u(b(n),"loadingRetryTimes",{}),u(b(n),"state",{keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:z()}),u(b(n),"dragStartMousePosition",null),u(b(n),"dragNodeProps",null),u(b(n),"currentMouseOverDroppableNodeKey",null),u(b(n),"listRef",d.createRef()),u(b(n),"onNodeDragStart",(function(e,t){var o=n.state,r=o.expandedKeys,a=o.keyEntities,i=n.props.onDragStart,d=t.eventKey;n.dragNodeProps=t,n.dragStartMousePosition={x:e.clientX,y:e.clientY};var l=Ce(r,d);n.setState({draggingNodeKey:d,dragChildrenKeys:De(d,a),indent:n.listRef.current.getIndentWidth()}),n.setExpandedKeys(l),window.addEventListener("dragend",n.onWindowDragEnd),null==i||i({event:e,node:G(t)})})),u(b(n),"onNodeDragEnter",(function(e,t){var r=n.state,a=r.expandedKeys,i=r.keyEntities,d=r.dragChildrenKeys,l=r.flattenNodes,s=r.indent,c=n.props,u=c.onDragEnter,f=c.onExpand,p=c.allowDrop,h=c.direction,g=t.pos,v=t.eventKey;if(n.currentMouseOverDroppableNodeKey!==v&&(n.currentMouseOverDroppableNodeKey=v),n.dragNodeProps){var y=Oe(e,n.dragNodeProps,t,s,n.dragStartMousePosition,p,l,i,a,h),m=y.dropPosition,k=y.dropLevelOffset,b=y.dropTargetKey,K=y.dropContainerKey,x=y.dropTargetPos,N=y.dropAllowed,E=y.dragOverNodeKey;!d.includes(b)&&N?(n.delayedDragEnterLogic||(n.delayedDragEnterLogic={}),Object.keys(n.delayedDragEnterLogic).forEach((function(e){clearTimeout(n.delayedDragEnterLogic[e])})),n.dragNodeProps.eventKey!==t.eventKey&&(e.persist(),n.delayedDragEnterLogic[g]=window.setTimeout((function(){if(null!==n.state.draggingNodeKey){var r=o(a),d=A(i,t.eventKey);d&&(d.children||[]).length&&(r=we(a,t.eventKey)),n.props.hasOwnProperty("expandedKeys")||n.setExpandedKeys(r),null==f||f(r,{node:G(t),expanded:!0,nativeEvent:e.nativeEvent})}}),800)),n.dragNodeProps.eventKey!==b||0!==k?(n.setState({dragOverNodeKey:E,dropPosition:m,dropLevelOffset:k,dropTargetKey:b,dropContainerKey:K,dropTargetPos:x,dropAllowed:N}),null==u||u({event:e,node:G(t),expandedKeys:a})):n.resetDragState()):n.resetDragState()}else n.resetDragState()})),u(b(n),"onNodeDragOver",(function(e,t){var o=n.state,r=o.dragChildrenKeys,a=o.flattenNodes,i=o.keyEntities,d=o.expandedKeys,l=o.indent,s=n.props,c=s.onDragOver,u=s.allowDrop,f=s.direction;if(n.dragNodeProps){var p=Oe(e,n.dragNodeProps,t,l,n.dragStartMousePosition,u,a,i,d,f),h=p.dropPosition,g=p.dropLevelOffset,v=p.dropTargetKey,y=p.dropContainerKey,m=p.dropTargetPos,k=p.dropAllowed,b=p.dragOverNodeKey;!r.includes(v)&&k&&(n.dragNodeProps.eventKey===v&&0===g?null===n.state.dropPosition&&null===n.state.dropLevelOffset&&null===n.state.dropTargetKey&&null===n.state.dropContainerKey&&null===n.state.dropTargetPos&&!1===n.state.dropAllowed&&null===n.state.dragOverNodeKey||n.resetDragState():h===n.state.dropPosition&&g===n.state.dropLevelOffset&&v===n.state.dropTargetKey&&y===n.state.dropContainerKey&&m===n.state.dropTargetPos&&k===n.state.dropAllowed&&b===n.state.dragOverNodeKey||n.setState({dropPosition:h,dropLevelOffset:g,dropTargetKey:v,dropContainerKey:y,dropTargetPos:m,dropAllowed:k,dragOverNodeKey:b}),null==c||c({event:e,node:G(t)}))}})),u(b(n),"onNodeDragLeave",(function(e,t){n.currentMouseOverDroppableNodeKey!==t.eventKey||e.currentTarget.contains(e.relatedTarget)||(n.resetDragState(),n.currentMouseOverDroppableNodeKey=null);var o=n.props.onDragLeave;null==o||o({event:e,node:G(t)})})),u(b(n),"onWindowDragEnd",(function(e){n.onNodeDragEnd(e,null,!0),window.removeEventListener("dragend",n.onWindowDragEnd)})),u(b(n),"onNodeDragEnd",(function(e,t){var o=n.props.onDragEnd;n.setState({dragOverNodeKey:null}),n.cleanDragState(),null==o||o({event:e,node:G(t)}),n.dragNodeProps=null,window.removeEventListener("dragend",n.onWindowDragEnd)})),u(b(n),"onNodeDrop",(function(o,r){var a,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],d=n.state,l=d.dragChildrenKeys,s=d.dropPosition,c=d.dropTargetKey,u=d.dropTargetPos;if(d.dropAllowed){var f=n.props.onDrop;if(n.setState({dragOverNodeKey:null}),n.cleanDragState(),null!==c){var p=e(e({},_(c,n.getTreeNodeRequiredProps())),{},{active:(null===(a=n.getActiveItem())||void 0===a?void 0:a.key)===c,data:A(n.state.keyEntities,c).node}),h=l.includes(c);t(!h,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var g=Se(u),v={event:o,node:G(p),dragNode:n.dragNodeProps?G(n.dragNodeProps):null,dragNodesKeys:[n.dragNodeProps.eventKey].concat(l),dropToGap:0!==s,dropPosition:s+Number(g[g.length-1])};i||null==f||f(v),n.dragNodeProps=null}}})),u(b(n),"cleanDragState",(function(){null!==n.state.draggingNodeKey&&n.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),n.dragStartMousePosition=null,n.currentMouseOverDroppableNodeKey=null})),u(b(n),"triggerExpandActionExpand",(function(t,o){var r=n.state,a=r.expandedKeys,i=r.flattenNodes,d=o.expanded,l=o.key;if(!(o.isLeaf||t.shiftKey||t.metaKey||t.ctrlKey)){var s=i.filter((function(e){return e.key===l}))[0],c=G(e(e({},_(l,n.getTreeNodeRequiredProps())),{},{data:s.data}));n.setExpandedKeys(d?Ce(a,l):we(a,l)),n.onNodeExpand(t,c)}})),u(b(n),"onNodeClick",(function(e,t){var o=n.props,r=o.onClick;"click"===o.expandAction&&n.triggerExpandActionExpand(e,t),null==r||r(e,t)})),u(b(n),"onNodeDoubleClick",(function(e,t){var o=n.props,r=o.onDoubleClick;"doubleClick"===o.expandAction&&n.triggerExpandActionExpand(e,t),null==r||r(e,t)})),u(b(n),"onNodeSelect",(function(e,t){var o=n.state.selectedKeys,r=n.state,a=r.keyEntities,i=r.fieldNames,d=n.props,l=d.onSelect,s=d.multiple,c=t.selected,u=t[i.key],f=!c,p=(o=f?s?we(o,u):[u]:Ce(o,u)).map((function(e){var t=A(a,e);return t?t.node:null})).filter(Boolean);n.setUncontrolledState({selectedKeys:o}),null==l||l(o,{event:"select",selected:f,node:t,selectedNodes:p,nativeEvent:e.nativeEvent})})),u(b(n),"onNodeCheck",(function(e,t,r){var a,i=n.state,d=i.keyEntities,l=i.checkedKeys,s=i.halfCheckedKeys,c=n.props,u=c.checkStrictly,f=c.onCheck,p=t.key,h={event:"check",node:t,checked:r,nativeEvent:e.nativeEvent};if(u){var g=r?we(l,p):Ce(l,p);a={checked:g,halfChecked:Ce(s,p)},h.checkedNodes=g.map((function(e){return A(d,e)})).filter(Boolean).map((function(e){return e.node})),n.setUncontrolledState({checkedKeys:g})}else{var v=X([].concat(o(l),[p]),!0,d),y=v.checkedKeys,m=v.halfCheckedKeys;if(!r){var k=new Set(y);k.delete(p);var b=X(Array.from(k),{halfCheckedKeys:m},d);y=b.checkedKeys,m=b.halfCheckedKeys}a=y,h.checkedNodes=[],h.checkedNodesPositions=[],h.halfCheckedKeys=m,y.forEach((function(e){var t=A(d,e);if(t){var n=t.node,o=t.pos;h.checkedNodes.push(n),h.checkedNodesPositions.push({node:n,pos:o})}})),n.setUncontrolledState({checkedKeys:y},!1,{halfCheckedKeys:m})}null==f||f(a,h)})),u(b(n),"onNodeLoad",(function(e){var o,r=e.key,a=A(n.state.keyEntities,r);if(null==a||null===(o=a.children)||void 0===o||!o.length){var i=new Promise((function(o,a){n.setState((function(i){var d=i.loadedKeys,l=void 0===d?[]:d,s=i.loadingKeys,c=void 0===s?[]:s,u=n.props,f=u.loadData,p=u.onLoad;return!f||l.includes(r)||c.includes(r)?null:(f(e).then((function(){var t=we(n.state.loadedKeys,r);null==p||p(t,{event:"load",node:e}),n.setUncontrolledState({loadedKeys:t}),n.setState((function(e){return{loadingKeys:Ce(e.loadingKeys,r)}})),o()})).catch((function(e){if(n.setState((function(e){return{loadingKeys:Ce(e.loadingKeys,r)}})),n.loadingRetryTimes[r]=(n.loadingRetryTimes[r]||0)+1,n.loadingRetryTimes[r]>=10){var i=n.state.loadedKeys;t(!1,"Retry for `loadData` many times but still failed. No more retry."),n.setUncontrolledState({loadedKeys:we(i,r)}),o()}a(e)})),{loadingKeys:we(c,r)})}))}));return i.catch((function(){})),i}})),u(b(n),"onNodeMouseEnter",(function(e,t){var o=n.props.onMouseEnter;null==o||o({event:e,node:t})})),u(b(n),"onNodeMouseLeave",(function(e,t){var o=n.props.onMouseLeave;null==o||o({event:e,node:t})})),u(b(n),"onNodeContextMenu",(function(e,t){var o=n.props.onRightClick;o&&(e.preventDefault(),o({event:e,node:t}))})),u(b(n),"onFocus",(function(){var e=n.props.onFocus;n.setState({focused:!0});for(var t=arguments.length,o=new Array(t),r=0;r<t;r++)o[r]=arguments[r];null==e||e.apply(void 0,o)})),u(b(n),"onBlur",(function(){var e=n.props.onBlur;n.setState({focused:!1}),n.onActiveChange(null);for(var t=arguments.length,o=new Array(t),r=0;r<t;r++)o[r]=arguments[r];null==e||e.apply(void 0,o)})),u(b(n),"getTreeNodeRequiredProps",(function(){var e=n.state;return{expandedKeys:e.expandedKeys||[],selectedKeys:e.selectedKeys||[],loadedKeys:e.loadedKeys||[],loadingKeys:e.loadingKeys||[],checkedKeys:e.checkedKeys||[],halfCheckedKeys:e.halfCheckedKeys||[],dragOverNodeKey:e.dragOverNodeKey,dropPosition:e.dropPosition,keyEntities:e.keyEntities}})),u(b(n),"setExpandedKeys",(function(e){var t=n.state,o=V(t.treeData,e,t.fieldNames);n.setUncontrolledState({expandedKeys:e,flattenNodes:o},!0)})),u(b(n),"onNodeExpand",(function(e,o){var r=n.state.expandedKeys,a=n.state,i=a.listChanging,d=a.fieldNames,l=n.props,s=l.onExpand,c=l.loadData,u=o.expanded,f=o[d.key];if(!i){var p=r.includes(f),h=!u;if(t(u&&p||!u&&!p,"Expand state not sync with index check"),r=h?we(r,f):Ce(r,f),n.setExpandedKeys(r),null==s||s(r,{node:o,expanded:h,nativeEvent:e.nativeEvent}),h&&c){var g=n.onNodeLoad(o);g&&g.then((function(){var e=V(n.state.treeData,r,d);n.setUncontrolledState({flattenNodes:e})})).catch((function(){var e=Ce(n.state.expandedKeys,f);n.setExpandedKeys(e)}))}}})),u(b(n),"onListChangeStart",(function(){n.setUncontrolledState({listChanging:!0})})),u(b(n),"onListChangeEnd",(function(){setTimeout((function(){n.setUncontrolledState({listChanging:!1})}))})),u(b(n),"onActiveChange",(function(e){var t=n.state.activeKey,o=n.props,r=o.onActiveChange,a=o.itemScrollOffset,i=void 0===a?0:a;t!==e&&(n.setState({activeKey:e}),null!==e&&n.scrollTo({key:e,offset:i}),null==r||r(e))})),u(b(n),"getActiveItem",(function(){var e=n.state,t=e.activeKey,o=e.flattenNodes;return null===t?null:o.find((function(e){return e.key===t}))||null})),u(b(n),"offsetActiveKey",(function(e){var t=n.state,o=t.flattenNodes,r=t.activeKey,a=o.findIndex((function(e){return e.key===r}));-1===a&&e<0&&(a=o.length);var i=o[a=(a+e+o.length)%o.length];if(i){var d=i.key;n.onActiveChange(d)}else n.onActiveChange(null)})),u(b(n),"onKeyDown",(function(t){var o=n.state,r=o.activeKey,a=o.expandedKeys,i=o.checkedKeys,d=o.fieldNames,l=n.props,s=l.onKeyDown,c=l.checkable,u=l.selectable;switch(t.which){case K.UP:n.offsetActiveKey(-1),t.preventDefault();break;case K.DOWN:n.offsetActiveKey(1),t.preventDefault()}var f=n.getActiveItem();if(f&&f.data){var p=n.getTreeNodeRequiredProps(),h=!1===f.data.isLeaf||!!(f.data[d.children]||[]).length,g=G(e(e({},_(r,p)),{},{data:f.data,active:!0}));switch(t.which){case K.LEFT:h&&a.includes(r)?n.onNodeExpand({},g):f.parent&&n.onActiveChange(f.parent.key),t.preventDefault();break;case K.RIGHT:h&&!a.includes(r)?n.onNodeExpand({},g):f.children&&f.children.length&&n.onActiveChange(f.children[0].key),t.preventDefault();break;case K.ENTER:case K.SPACE:!c||g.disabled||!1===g.checkable||g.disableCheckbox?c||!u||g.disabled||!1===g.selectable||n.onNodeSelect({},g):n.onNodeCheck({},g,!i.includes(r))}}null==s||s(t)})),u(b(n),"setUncontrolledState",(function(t){var o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!n.destroyed){var a=!1,i=!0,d={};Object.keys(t).forEach((function(e){n.props.hasOwnProperty(e)?i=!1:(a=!0,d[e]=t[e])})),!a||o&&!i||n.setState(e(e({},d),r))}})),u(b(n),"scrollTo",(function(e){n.listRef.current.scrollTo(e)})),n}return x(i,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var e=this.props,t=e.activeKey,n=e.itemScrollOffset,o=void 0===n?0:n;void 0!==t&&t!==this.state.activeKey&&(this.setState({activeKey:t}),null!==t&&this.scrollTo({key:t,offset:o}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var e,t=this.state,n=t.focused,o=t.flattenNodes,a=t.keyEntities,i=t.draggingNodeKey,l=t.activeKey,f=t.dropLevelOffset,p=t.dropContainerKey,g=t.dropTargetKey,v=t.dropPosition,y=t.dragOverNodeKey,m=t.indent,k=this.props,b=k.prefixCls,K=k.className,x=k.style,N=k.showLine,E=k.focusable,C=k.tabIndex,w=void 0===C?0:C,S=k.selectable,D=k.showIcon,P=k.icon,O=k.switcherIcon,$=k.draggable,L=k.checkable,M=k.checkStrictly,T=k.disabled,I=k.motion,R=k.loadData,A=k.filterTreeNode,B=k.height,H=k.itemHeight,j=k.scrollWidth,z=k.virtual,q=k.titleRender,V=k.dropIndicatorRender,W=k.onContextMenu,_=k.onScroll,G=k.direction,U=k.rootClassName,F=k.rootStyle,X=h(this.props,{aria:!0,data:!0});$&&(e="object"===r($)?$:"function"==typeof $?{nodeDraggable:$}:{});var Q={prefixCls:b,selectable:S,showIcon:D,icon:P,switcherIcon:O,draggable:e,draggingNodeKey:i,checkable:L,checkStrictly:M,disabled:T,keyEntities:a,dropLevelOffset:f,dropContainerKey:p,dropTargetKey:g,dropPosition:v,dragOverNodeKey:y,indent:m,direction:G,dropIndicatorRender:V,loadData:R,filterTreeNode:A,titleRender:q,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop};return d.createElement(ye.Provider,{value:Q},d.createElement("div",{className:c(b,K,U,u(u(u({},"".concat(b,"-show-line"),N),"".concat(b,"-focused"),n),"".concat(b,"-active-focused"),null!==l)),style:F},d.createElement(Ue,s({ref:this.listRef,prefixCls:b,style:x,data:o,disabled:T,selectable:S,checkable:!!L,motion:I,dragging:null!==i,height:B,itemHeight:H,virtual:z,focusable:E,focused:n,tabIndex:w,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:W,onScroll:_,scrollWidth:j},this.getTreeNodeRequiredProps(),X))))}}],[{key:"getDerivedStateFromProps",value:function(n,o){var r,a=o.prevProps,i={prevProps:n};function d(e){return!a&&n.hasOwnProperty(e)||a&&a[e]!==n[e]}var l=o.fieldNames;if(d("fieldNames")&&(l=z(n.fieldNames),i.fieldNames=l),d("treeData")?r=n.treeData:d("children")&&(t(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),r=q(n.children)),r){i.treeData=r;var s=W(r,{fieldNames:l});i.keyEntities=e(u({},ze,Ve),s.keyEntities)}var c,f=i.keyEntities||o.keyEntities;if(d("expandedKeys")||a&&d("autoExpandParent"))i.expandedKeys=n.autoExpandParent||!a&&n.defaultExpandParent?Me(n.expandedKeys,f):n.expandedKeys;else if(!a&&n.defaultExpandAll){var p=e({},f);delete p[ze];var h=[];Object.keys(p).forEach((function(e){var t=p[e];t.children&&t.children.length&&h.push(t.key)})),i.expandedKeys=h}else!a&&n.defaultExpandedKeys&&(i.expandedKeys=n.autoExpandParent||n.defaultExpandParent?Me(n.defaultExpandedKeys,f):n.defaultExpandedKeys);if(i.expandedKeys||delete i.expandedKeys,r||i.expandedKeys){var g=V(r||o.treeData,i.expandedKeys||o.expandedKeys,l);i.flattenNodes=g}if((n.selectable&&(d("selectedKeys")?i.selectedKeys=$e(n.selectedKeys,n):!a&&n.defaultSelectedKeys&&(i.selectedKeys=$e(n.defaultSelectedKeys,n))),n.checkable)&&(d("checkedKeys")?c=Le(n.checkedKeys)||{}:!a&&n.defaultCheckedKeys?c=Le(n.defaultCheckedKeys)||{}:r&&(c=Le(n.checkedKeys)||{checkedKeys:o.checkedKeys,halfCheckedKeys:o.halfCheckedKeys}),c)){var v=c,y=v.checkedKeys,m=void 0===y?[]:y,k=v.halfCheckedKeys,b=void 0===k?[]:k;if(!n.checkStrictly){var K=X(m,!0,f);m=K.checkedKeys,b=K.halfCheckedKeys}i.checkedKeys=m,i.halfCheckedKeys=b}return d("loadedKeys")&&(i.loadedKeys=n.loadedKeys),i}}]),i}(d.Component);u(Fe,"defaultProps",{prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:function(e){var t=e.dropPosition,n=e.dropLevelOffset,o=e.indent,r={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(t){case-1:r.top=0,r.left=-n*o;break;case 1:r.bottom=0,r.left=-n*o;break;case 0:r.bottom=0,r.left=o}return f.createElement("div",{style:r})},allowDrop:function(){return!0},expandAction:!1}),u(Fe,"TreeNode",Ee);const Xe=({treeCls:e,treeNodeCls:t,directoryNodeSelectedBg:n,directoryNodeSelectedColor:o,motionDurationMid:r,borderRadius:a,controlItemBgHover:i})=>({[`${e}${e}-directory ${t}`]:{[`${e}-node-content-wrapper`]:{position:"static",[`> *:not(${e}-drop-indicator)`]:{position:"relative"},"&:hover":{background:"transparent"},"&:before":{position:"absolute",inset:0,transition:`background-color ${r}`,content:'""',borderRadius:a},"&:hover:before":{background:i}},[`${e}-switcher, ${e}-checkbox, ${e}-draggable-icon`]:{zIndex:1},"&-selected":{[`${e}-switcher, ${e}-draggable-icon`]:{color:o},[`${e}-node-content-wrapper`]:{color:o,background:"transparent","&:before, &:hover:before":{background:n}}}}}),Qe=new D("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),Ye=(e,t)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),Je=(e,t)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${S(t.lineWidthBold)} solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),Ze=(e,t)=>{const{treeCls:n,treeNodeCls:o,treeNodePadding:r,titleHeight:a,indentSize:i,nodeSelectedBg:d,nodeHoverBg:l,colorTextQuaternary:s,controlItemBgActiveDisabled:c}=t;return{[n]:Object.assign(Object.assign({},w(t)),{background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,"&-rtl":{direction:"rtl"},[`&${n}-rtl ${n}-switcher_close ${n}-switcher-icon svg`]:{transform:"rotate(90deg)"},[`&-focused:not(:hover):not(${n}-active-focused)`]:Object.assign({},P(t)),[`${n}-list-holder-inner`]:{alignItems:"flex-start"},[`&${n}-block-node`]:{[`${n}-list-holder-inner`]:{alignItems:"stretch",[`${n}-node-content-wrapper`]:{flex:"auto"},[`${o}.dragging:after`]:{position:"absolute",inset:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:Qe,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none",borderRadius:t.borderRadius}}},[o]:{display:"flex",alignItems:"flex-start",marginBottom:r,lineHeight:S(a),position:"relative","&:before":{content:'""',position:"absolute",zIndex:1,insetInlineStart:0,width:"100%",top:"100%",height:r},[`&-disabled ${n}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}},[`${n}-checkbox-disabled + ${n}-node-selected,&${o}-disabled${o}-selected ${n}-node-content-wrapper`]:{backgroundColor:c},[`${n}-checkbox-disabled`]:{pointerEvents:"unset"},[`&:not(${o}-disabled)`]:{[`${n}-node-content-wrapper`]:{"&:hover":{color:t.nodeHoverColor}}},[`&-active ${n}-node-content-wrapper`]:{background:t.controlItemBgHover},[`&:not(${o}-disabled).filter-node ${n}-title`]:{color:t.colorPrimary,fontWeight:500},"&-draggable":{cursor:"grab",[`${n}-draggable-icon`]:{flexShrink:0,width:a,textAlign:"center",visibility:"visible",color:s},[`&${o}-disabled ${n}-draggable-icon`]:{visibility:"hidden"}}},[`${n}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:i}},[`${n}-draggable-icon`]:{visibility:"hidden"},[`${n}-switcher, ${n}-checkbox`]:{marginInlineEnd:t.calc(t.calc(a).sub(t.controlInteractiveSize)).div(2).equal()},[`${n}-switcher`]:Object.assign(Object.assign({},Ye(e,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:a,textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${t.motionDurationSlow}`,"&-noop":{cursor:"unset"},"&:before":{pointerEvents:"none",content:'""',width:a,height:a,position:"absolute",left:{_skip_check_:!0,value:0},top:0,borderRadius:t.borderRadius,transition:`all ${t.motionDurationSlow}`},[`&:not(${n}-switcher-noop):hover:before`]:{backgroundColor:t.colorBgTextHover},[`&_close ${n}-switcher-icon svg`]:{transform:"rotate(-90deg)"},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(a).div(2).equal(),bottom:t.calc(r).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:t.calc(t.calc(a).div(2).equal()).mul(.8).equal(),height:t.calc(a).div(2).equal(),borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${n}-node-content-wrapper`]:Object.assign(Object.assign({position:"relative",minHeight:a,paddingBlock:0,paddingInline:t.paddingXS,background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`},Je(e,t)),{"&:hover":{backgroundColor:l},[`&${n}-node-selected`]:{color:t.nodeSelectedColor,backgroundColor:d},[`${n}-iconEle`]:{display:"inline-block",width:a,height:a,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}}),[`${n}-unselectable ${n}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${o}.drop-container > [draggable]`]:{boxShadow:`0 0 0 2px ${t.colorPrimary}`},"&-show-line":{[`${n}-indent-unit`]:{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(a).div(2).equal(),bottom:t.calc(r).mul(-1).equal(),borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end:before":{display:"none"}},[`${n}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${o}-leaf-last ${n}-switcher-leaf-line:before`]:{top:"auto !important",bottom:"auto !important",height:`${S(t.calc(a).div(2).equal())} !important`}})}},et=(e,t,n=!0)=>{const o=`.${e}`,r=`${o}-treenode`,a=t.calc(t.paddingXS).div(2).equal(),i=C(t,{treeCls:o,treeNodeCls:r,treeNodePadding:a});return[Ze(e,i),n&&Xe(i)].filter(Boolean)},tt=N("Tree",((e,{prefixCls:t})=>[{[e.componentCls]:R(`${t}-checkbox`,e)},et(t,e),E(e)]),(e=>{const{colorTextLightSolid:t,colorPrimary:n}=e;return Object.assign(Object.assign({},(e=>{const{controlHeightSM:t,controlItemBgHover:n,controlItemBgActive:o}=e;return{titleHeight:t,indentSize:t,nodeHoverBg:n,nodeHoverColor:e.colorText,nodeSelectedBg:o,nodeSelectedColor:e.colorText}})(e)),{directoryNodeSelectedColor:t,directoryNodeSelectedBg:n})}));function nt(e){const{dropPosition:t,dropLevelOffset:n,prefixCls:o,indent:r,direction:a="ltr"}=e,i="ltr"===a?"left":"right",d="ltr"===a?"right":"left",l={[i]:-n*r+4,[d]:0};switch(t){case-1:l.top=-3;break;case 1:l.bottom=-3;break;default:l.bottom=-3,l[i]=r+4}return f.createElement("div",{style:l,className:`${o}-drop-indicator`})}const ot=e=>{const{prefixCls:t,switcherIcon:n,treeNodeProps:o,showLine:r,switcherLoadingIcon:a}=e,{isLeaf:i,expanded:l,loading:s}=o;if(s)return d.isValidElement(a)?a:d.createElement(O,{className:`${t}-switcher-loading-icon`});let u;if(r&&"object"==typeof r&&(u=r.showLeafIcon),i){if(!r)return null;if("boolean"!=typeof u&&u){const e="function"==typeof u?u(o):u,n=`${t}-switcher-line-custom-icon`;return d.isValidElement(e)?$(e,{className:c(e.props.className||"",n)}):e}return u?d.createElement(te,{className:`${t}-switcher-line-icon`}):d.createElement("span",{className:`${t}-switcher-leaf-line`})}const f=`${t}-switcher-icon`,p="function"==typeof n?n(o):n;return d.isValidElement(p)?$(p,{className:c(p.props.className||"",f)}):void 0!==p?p:r?l?d.createElement(pe,{className:`${t}-switcher-line-icon`}):d.createElement(ve,{className:`${t}-switcher-line-icon`}):d.createElement(J,{className:f})},rt=f.forwardRef(((e,t)=>{var n;const{getPrefixCls:o,direction:r,virtual:a,tree:i}=f.useContext(L),{prefixCls:d,className:l,showIcon:s=!1,showLine:u,switcherIcon:p,switcherLoadingIcon:h,blockNode:g=!1,children:v,checkable:y=!1,selectable:m=!0,draggable:k,motion:b,style:K}=e,x=o("tree",d),N=o(),E=null!=b?b:Object.assign(Object.assign({},M(N)),{motionAppear:!1}),C=Object.assign(Object.assign({},e),{checkable:y,selectable:m,showIcon:s,motion:E,blockNode:g,showLine:Boolean(u),dropIndicatorRender:nt}),[w,S,D]=tt(x),[,P]=T(),O=P.paddingXS/2+((null===(n=P.Tree)||void 0===n?void 0:n.titleHeight)||P.controlHeightSM),$=f.useMemo((()=>{if(!k)return!1;let e={};switch(typeof k){case"function":e.nodeDraggable=k;break;case"object":e=Object.assign({},k)}return!1!==e.icon&&(e.icon=e.icon||f.createElement(ce,null)),e}),[k]);return w(f.createElement(Fe,Object.assign({itemHeight:O,ref:t,virtual:a},C,{style:Object.assign(Object.assign({},null==i?void 0:i.style),K),prefixCls:x,className:c({[`${x}-icon-hide`]:!s,[`${x}-block-node`]:g,[`${x}-unselectable`]:!m,[`${x}-rtl`]:"rtl"===r},null==i?void 0:i.className,l,S,D),direction:r,checkable:y?f.createElement("span",{className:`${x}-checkbox-inner`}):y,selectable:m,switcherIcon:e=>f.createElement(ot,{prefixCls:x,switcherIcon:p,switcherLoadingIcon:h,treeNodeProps:e,showLine:u}),draggable:$}),v))}));function at(e,t,n){const{key:o,children:r}=n;e.forEach((function(e){const a=e[o],i=e[r];!1!==t(a,e)&&at(i||[],t,n)}))}function it({treeData:e,expandedKeys:t,startKey:n,endKey:o,fieldNames:r}){const a=[];let i=0;if(n&&n===o)return[n];if(!n||!o)return[];return at(e,(e=>{if(2===i)return!1;if(function(e){return e===n||e===o}(e)){if(a.push(e),0===i)i=1;else if(1===i)return i=2,!1}else 1===i&&a.push(e);return t.includes(e)}),z(r)),a}function dt(e,t,n){const r=o(t),a=[];return at(e,((e,t)=>{const n=r.indexOf(e);return-1!==n&&(a.push(t),r.splice(n,1)),!!r.length}),z(n)),a}var lt=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function st(e){const{isLeaf:t,expanded:n}=e;return t?d.createElement(te,null):n?d.createElement(re,null):d.createElement(de,null)}function ct({treeData:e,children:t}){return e||q(t)}const ut=(e,t)=>{var{defaultExpandAll:n,defaultExpandParent:r,defaultExpandedKeys:a}=e,i=lt(e,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]);const l=d.useRef(null),s=d.useRef(null),[u,f]=d.useState(i.selectedKeys||i.defaultSelectedKeys||[]),[p,h]=d.useState((()=>(()=>{const{keyEntities:e}=W(ct(i));let t;return t=n?Object.keys(e):r?Me(i.expandedKeys||a||[],e):i.expandedKeys||a||[],t})()));d.useEffect((()=>{"selectedKeys"in i&&f(i.selectedKeys)}),[i.selectedKeys]),d.useEffect((()=>{"expandedKeys"in i&&h(i.expandedKeys)}),[i.expandedKeys]);const{getPrefixCls:g,direction:v}=d.useContext(L),{prefixCls:y,className:m,showIcon:k=!0,expandAction:b="click"}=i,K=lt(i,["prefixCls","className","showIcon","expandAction"]),x=g("tree",y),N=c(`${x}-directory`,{[`${x}-directory-rtl`]:"rtl"===v},m);return d.createElement(rt,Object.assign({icon:st,ref:t,blockNode:!0},K,{showIcon:k,expandAction:b,prefixCls:x,className:N,expandedKeys:p,selectedKeys:u,onSelect:(e,t)=>{var n;const{multiple:r,fieldNames:a}=i,{node:d,nativeEvent:c}=t,{key:u=""}=d,h=ct(i),g=Object.assign(Object.assign({},t),{selected:!0}),v=(null==c?void 0:c.ctrlKey)||(null==c?void 0:c.metaKey),y=null==c?void 0:c.shiftKey;let m;r&&v?(m=e,l.current=u,s.current=m,g.selectedNodes=dt(h,m,a)):r&&y?(m=Array.from(new Set([].concat(o(s.current||[]),o(it({treeData:h,expandedKeys:p,startKey:u,endKey:l.current,fieldNames:a}))))),g.selectedNodes=dt(h,m,a)):(m=[u],l.current=u,s.current=m,g.selectedNodes=dt(h,m,a)),null===(n=i.onSelect)||void 0===n||n.call(i,m,g),"selectedKeys"in i||f(m)},onExpand:(e,t)=>{var n;return"expandedKeys"in i||h(e),null===(n=i.onExpand)||void 0===n?void 0:n.call(i,e,t)}}))},ft=d.forwardRef(ut),pt=rt;pt.DirectoryTree=ft,pt.TreeNode=Ee;export{pt as T,X as a,Ce as b,W as c,we as d};
