import{r as n,l as e,T as i,n as a,m as t,a4 as l,h as c,a3 as r,L as o,M as d,O as s,J as h,an as u,ad as g,G as m,at as $,aw as k,aG as S,aK as p}from"./index-Cak6rALw.js";var b=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],I=n.forwardRef((function(o,d){var s,h=o.prefixCls,u=void 0===h?"rc-switch":h,g=o.className,m=o.checked,$=o.defaultChecked,k=o.disabled,S=o.loadingIcon,p=o.checkedChildren,I=o.unCheckedChildren,w=o.onClick,f=o.onChange,C=o.onKeyDown,v=e(o,b),M=i(!1,{value:m,defaultValue:$}),y=a(M,2),E=y[0],x=y[1];function q(n,e){var i=E;return k||(x(i=n),null==f||f(i,e)),i}var O=t(u,g,(l(s={},"".concat(u,"-checked"),E),l(s,"".concat(u,"-disabled"),k),s));return n.createElement("button",c({},v,{type:"button",role:"switch","aria-checked":E,disabled:k,className:O,ref:d,onKeyDown:function(n){n.which===r.LEFT?q(!1,n):n.which===r.RIGHT&&q(!0,n),null==C||C(n)},onClick:function(n){var e=q(!E,n);null==w||w(e,n)}}),S,n.createElement("span",{className:"".concat(u,"-inner")},n.createElement("span",{className:"".concat(u,"-inner-checked")},p),n.createElement("span",{className:"".concat(u,"-inner-unchecked")},I)))}));I.displayName="Switch";const w=n=>{const{componentCls:e,trackHeightSM:i,trackPadding:a,trackMinWidthSM:t,innerMinMarginSM:l,innerMaxMarginSM:c,handleSizeSM:r,calc:o}=n,d=`${e}-inner`,s=h(o(r).add(o(a).mul(2)).equal()),u=h(o(c).mul(2).equal());return{[e]:{[`&${e}-small`]:{minWidth:t,height:i,lineHeight:h(i),[`${e}-inner`]:{paddingInlineStart:c,paddingInlineEnd:l,[`${d}-checked, ${d}-unchecked`]:{minHeight:i},[`${d}-checked`]:{marginInlineStart:`calc(-100% + ${s} - ${u})`,marginInlineEnd:`calc(100% - ${s} + ${u})`},[`${d}-unchecked`]:{marginTop:o(i).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`${e}-handle`]:{width:r,height:r},[`${e}-loading-icon`]:{top:o(o(r).sub(n.switchLoadingIconSize)).div(2).equal(),fontSize:n.switchLoadingIconSize},[`&${e}-checked`]:{[`${e}-inner`]:{paddingInlineStart:l,paddingInlineEnd:c,[`${d}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${d}-unchecked`]:{marginInlineStart:`calc(100% - ${s} + ${u})`,marginInlineEnd:`calc(-100% + ${s} - ${u})`}},[`${e}-handle`]:{insetInlineStart:`calc(100% - ${h(o(r).add(a).equal())})`}},[`&:not(${e}-disabled):active`]:{[`&:not(${e}-checked) ${d}`]:{[`${d}-unchecked`]:{marginInlineStart:o(n.marginXXS).div(2).equal(),marginInlineEnd:o(n.marginXXS).mul(-1).div(2).equal()}},[`&${e}-checked ${d}`]:{[`${d}-checked`]:{marginInlineStart:o(n.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:o(n.marginXXS).div(2).equal()}}}}}}},f=n=>{const{componentCls:e,handleSize:i,calc:a}=n;return{[e]:{[`${e}-loading-icon${n.iconCls}`]:{position:"relative",top:a(a(i).sub(n.fontSize)).div(2).equal(),color:n.switchLoadingIconColor,verticalAlign:"top"},[`&${e}-checked ${e}-loading-icon`]:{color:n.switchColor}}}},C=n=>{const{componentCls:e,trackPadding:i,handleBg:a,handleShadow:t,handleSize:l,calc:c}=n,r=`${e}-handle`;return{[e]:{[r]:{position:"absolute",top:i,insetInlineStart:i,width:l,height:l,transition:`all ${n.switchDuration} ease-in-out`,"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:a,borderRadius:c(l).div(2).equal(),boxShadow:t,transition:`all ${n.switchDuration} ease-in-out`,content:'""'}},[`&${e}-checked ${r}`]:{insetInlineStart:`calc(100% - ${h(c(l).add(i).equal())})`},[`&:not(${e}-disabled):active`]:{[`${r}::before`]:{insetInlineEnd:n.switchHandleActiveInset,insetInlineStart:0},[`&${e}-checked ${r}::before`]:{insetInlineEnd:0,insetInlineStart:n.switchHandleActiveInset}}}}},v=n=>{const{componentCls:e,trackHeight:i,trackPadding:a,innerMinMargin:t,innerMaxMargin:l,handleSize:c,calc:r}=n,o=`${e}-inner`,d=h(r(c).add(r(a).mul(2)).equal()),s=h(r(l).mul(2).equal());return{[e]:{[o]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:l,paddingInlineEnd:t,transition:`padding-inline-start ${n.switchDuration} ease-in-out, padding-inline-end ${n.switchDuration} ease-in-out`,[`${o}-checked, ${o}-unchecked`]:{display:"block",color:n.colorTextLightSolid,fontSize:n.fontSizeSM,transition:`margin-inline-start ${n.switchDuration} ease-in-out, margin-inline-end ${n.switchDuration} ease-in-out`,pointerEvents:"none",minHeight:i},[`${o}-checked`]:{marginInlineStart:`calc(-100% + ${d} - ${s})`,marginInlineEnd:`calc(100% - ${d} + ${s})`},[`${o}-unchecked`]:{marginTop:r(i).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`&${e}-checked ${o}`]:{paddingInlineStart:t,paddingInlineEnd:l,[`${o}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${o}-unchecked`]:{marginInlineStart:`calc(100% - ${d} + ${s})`,marginInlineEnd:`calc(-100% + ${d} - ${s})`}},[`&:not(${e}-disabled):active`]:{[`&:not(${e}-checked) ${o}`]:{[`${o}-unchecked`]:{marginInlineStart:r(a).mul(2).equal(),marginInlineEnd:r(a).mul(-1).mul(2).equal()}},[`&${e}-checked ${o}`]:{[`${o}-checked`]:{marginInlineStart:r(a).mul(-1).mul(2).equal(),marginInlineEnd:r(a).mul(2).equal()}}}}}},M=n=>{const{componentCls:e,trackHeight:i,trackMinWidth:a}=n;return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},s(n)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:a,height:i,lineHeight:h(i),verticalAlign:"middle",background:n.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:`all ${n.motionDurationMid}`,userSelect:"none",[`&:hover:not(${e}-disabled)`]:{background:n.colorTextTertiary}}),u(n)),{[`&${e}-checked`]:{background:n.switchColor,[`&:hover:not(${e}-disabled)`]:{background:n.colorPrimaryHover}},[`&${e}-loading, &${e}-disabled`]:{cursor:"not-allowed",opacity:n.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},[`&${e}-rtl`]:{direction:"rtl"}})}},y=o("Switch",(n=>{const e=d(n,{switchDuration:n.motionDurationMid,switchColor:n.colorPrimary,switchDisabledOpacity:n.opacityLoading,switchLoadingIconSize:n.calc(n.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:`rgba(0, 0, 0, ${n.opacityLoading})`,switchHandleActiveInset:"-30%"});return[M(e),v(e),C(e),f(e),w(e)]}),(n=>{const{fontSize:e,lineHeight:i,controlHeight:a,colorWhite:t}=n,l=e*i,c=a/2,r=l-4,o=c-4;return{trackHeight:l,trackHeightSM:c,trackMinWidth:2*r+8,trackMinWidthSM:2*o+4,trackPadding:2,handleBg:t,handleSize:r,handleSizeSM:o,handleShadow:`0 2px 4px 0 ${new g("#00230b").setA(.2).toRgbString()}`,innerMinMargin:r/2,innerMaxMargin:r+2+4,innerMinMarginSM:o/2,innerMaxMarginSM:o+2+4}}));const E=n.forwardRef(((e,a)=>{const{prefixCls:l,size:c,disabled:r,loading:o,className:d,rootClassName:s,style:h,checked:u,value:g,defaultChecked:b,defaultValue:w,onChange:f}=e,C=function(n,e){var i={};for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&e.indexOf(a)<0&&(i[a]=n[a]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols){var t=0;for(a=Object.getOwnPropertySymbols(n);t<a.length;t++)e.indexOf(a[t])<0&&Object.prototype.propertyIsEnumerable.call(n,a[t])&&(i[a[t]]=n[a[t]])}return i}(e,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),[v,M]=i(!1,{value:null!=u?u:g,defaultValue:null!=b?b:w}),{getPrefixCls:E,direction:x,switch:q}=n.useContext(m),O=n.useContext($),z=(null!=r?r:O)||o,H=E("switch",l),N=n.createElement("div",{className:`${H}-handle`},o&&n.createElement(p,{className:`${H}-loading-icon`})),[D,j,T]=y(H),L=k(c),P=t(null==q?void 0:q.className,{[`${H}-small`]:"small"===L,[`${H}-loading`]:o,[`${H}-rtl`]:"rtl"===x},d,s,j,T),W=Object.assign(Object.assign({},null==q?void 0:q.style),h);return D(n.createElement(S,{component:"Switch"},n.createElement(I,Object.assign({},C,{checked:v,onChange:(...n)=>{M(n[0]),null==f||f.apply(void 0,n)},prefixCls:H,className:P,style:W,disabled:z,ref:a,loadingIcon:N}))))}));E.__ANT_SWITCH=!0;export{E as S};
