const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./SearchByImage-AbX8bPmd.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./useCacheState-CAnxkewm.js","./MyApp-DW5WH4Ub.js","./index-fI_muw4K.js","./PurePanel-BvHjNOhQ.js","./index-C5gzSBcY.js","./List-B6ZMXgC_.js","./index-SRy1SMeO.js","./move-ESLFEEsv.js","./DownOutlined-B-JcS-29.js","./SearchOutlined--mXbzQ68.js","./index-Bg865k-U.js","./EyeOutlined-CNKPohhw.js","./index-CDSnY0KE.js","./addEventListener-C4tIKh7N.js","./index-Bx3o6rwA.js","./index-BVNdrzmU.js","./useBreakpoint-CPcdMRfj.js","./progress-ApOaEpgr.js","./SearchByUsername-D2xSpbaj.js","./SearchByPhone-CPOl_Df2.js","./sweetalert2.esm.all-BZxvatOx.js","./TiktokSearcher-Desksq9O.js","./InstagramSearcher-C59akzek.js","./Screen-Buq7HJpK.js","./col-CzA09p0P.js","./row-BMfM-of4.js","./More-Py1ABZAB.js"])))=>i.map(i=>d[i]);
import{r as a,b0 as e,b1 as s,aN as i}from"./index-Cak6rALw.js";import{u as r,t as o,A as l,I as t,C as m}from"./MyApp-DW5WH4Ub.js";import{S as c}from"./Screen-Buq7HJpK.js";import{T as n}from"./index-IxNfZAD-.js";import"./col-CzA09p0P.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./Dropdown-BcL-JHCf.js";const f=s((()=>i((()=>import("./SearchByImage-AbX8bPmd.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]),import.meta.url)),{fallback:t}),j=s((()=>i((()=>import("./SearchByUsername-D2xSpbaj.js")),__vite__mapDeps([21,1,2,4]),import.meta.url)),{fallback:m}),p=s((()=>i((()=>import("./SearchByPhone-CPOl_Df2.js")),__vite__mapDeps([22,1,2,4,23]),import.meta.url)),{fallback:m}),_=s((()=>i((()=>import("./TiktokSearcher-Desksq9O.js")),__vite__mapDeps([24,1,2,3,4,13,14,12]),import.meta.url)),{fallback:m}),h=s((()=>i((()=>import("./InstagramSearcher-C59akzek.js")),__vite__mapDeps([25,1,2,26,27,28,19,4]),import.meta.url)),{fallback:m}),k=s((()=>i((()=>import("./More-Py1ABZAB.js")),__vite__mapDeps([29,1,2,26,27,28,19,4]),import.meta.url)),{fallback:m});function d(){const{ti:s}=r();return a.useEffect((()=>{o("Search:onLoad")}),[]),e.jsxs(c,{title:s({en:"Search tools",vi:"Máy tìm kiếm"}),mode:"center",children:[e.jsx(l,{type:"info",showIcon:!0,message:s({vi:"Tổng hợp các Công cụ Tìm kiếm hữu ích cho các Thám tử 🕵",en:"A collection of Useful Search Tools for Detectives 🕵"}),style:{marginBottom:10}}),e.jsx(n,{centered:!0,type:"card",style:{maxWidth:"100%"},items:[{key:"1",label:s({en:"Image",vi:"Hình ảnh"}),icon:e.jsx("i",{className:"fa-solid fa-image fa-lg"}),children:e.jsx(f,{})},{key:"2",label:s({en:"Username",vi:"Username"}),icon:e.jsx("i",{className:"fa-solid fa-user fa-lg"}),children:e.jsx(j,{})},{key:"3",label:s({en:"Phone/Email",vi:"SĐT/Email"}),icon:e.jsx("i",{className:"fa-solid fa-phone fa-lg"}),children:e.jsx(p,{})},{key:"4",label:s({en:"Tiktok",vi:"Tiktok"}),icon:e.jsx("i",{className:"fa-brands fa-tiktok fa-lg"}),children:e.jsx(_,{})},{key:"5",label:s({en:"Instagram",vi:"Instagram"}),icon:e.jsx("i",{className:"fa-brands fa-instagram fa-lg"}),children:e.jsx(h,{})},{key:"6",label:s({en:"More",vi:"Khác"}),icon:e.jsx("i",{className:"fa-solid fa-ellipsis fa-lg"}),children:e.jsx(k,{})}]})]})}export{d as default};
