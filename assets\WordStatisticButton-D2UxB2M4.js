const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./index-Dsk8s7oD.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./Wordcloud-C_XnZNah.js","./index-D3qvoGmV.js","./MyTable-DnjDmN7z.js","./MyApp-DW5WH4Ub.js","./index-ByRdMNW-.js","./index-BzMPigdO.js","./DownOutlined-B-JcS-29.js","./Table-BO80-bCE.js","./addEventListener-C4tIKh7N.js","./List-B6ZMXgC_.js","./index-D6X7PZwe.js","./index-CxAc8H5Q.js","./index-QU7lSj_a.js","./index-Dg3cFar0.js","./Dropdown-BcL-JHCf.js","./PurePanel-BvHjNOhQ.js","./move-ESLFEEsv.js","./index-C5gzSBcY.js","./index-SRy1SMeO.js","./SearchOutlined--mXbzQ68.js","./useBreakpoint-CPcdMRfj.js","./Pagination-2X6SDgot.js","./index-Bg865k-U.js","./EyeOutlined-CNKPohhw.js","./row-BMfM-of4.js"])))=>i.map(i=>d[i]);
import{r as t,b0 as n,b1 as e,b5 as r,aN as i}from"./index-Cak6rALw.js";import{u as a,t as o,g as s,s as l,T as u,S as c,c as h,C as f,I as g}from"./MyApp-DW5WH4Ub.js";import{B as d}from"./BadgeWrapper-BCbYXXfB.js";import{S as p}from"./index-Bp2s1Wws.js";import{M as m}from"./index-Bx3o6rwA.js";import{T as y}from"./index-IxNfZAD-.js";import{T as w}from"./index-BzMPigdO.js";import"./PurePanel-BvHjNOhQ.js";import"./Dropdown-BcL-JHCf.js";import"./DownOutlined-B-JcS-29.js";function v(t,n){return null==t||null==n?NaN:t<n?-1:t>n?1:t>=n?0:NaN}function b(t,n){return null==t||null==n?NaN:n<t?-1:n>t?1:n>=t?0:NaN}function M(t){let n,e,r;function i(t,r,i=0,a=t.length){if(i<a){if(0!==n(r,r))return a;do{const n=i+a>>>1;e(t[n],r)<0?i=n+1:a=n}while(i<a)}return i}return 2!==t.length?(n=v,e=(n,e)=>v(t(n),e),r=(n,e)=>t(n)-e):(n=t===v||t===b?t:x,e=t,r=t),{left:i,center:function(t,n,e=0,a=t.length){const o=i(t,n,e,a-1);return o>e&&r(t[o-1],n)>-r(t[o],n)?o-1:o},right:function(t,r,i=0,a=t.length){if(i<a){if(0!==n(r,r))return a;do{const n=i+a>>>1;e(t[n],r)<=0?i=n+1:a=n}while(i<a)}return i}}}function x(){return 0}const k=M(v).right;M((function(t){return null===t?NaN:+t})).center;var N=Math.sqrt(50),T=Math.sqrt(10),C=Math.sqrt(2);function $(t,n,e){var r,i,a,o,s=-1;if(e=+e,(t=+t)===(n=+n)&&e>0)return[t];if((r=n<t)&&(i=t,t=n,n=i),0===(o=function(t,n,e){var r=(n-t)/Math.max(0,e),i=Math.floor(Math.log(r)/Math.LN10),a=r/Math.pow(10,i);return i>=0?(a>=N?10:a>=T?5:a>=C?2:1)*Math.pow(10,i):-Math.pow(10,-i)/(a>=N?10:a>=T?5:a>=C?2:1)}(t,n,e))||!isFinite(o))return[];if(o>0){let e=Math.round(t/o),r=Math.round(n/o);for(e*o<t&&++e,r*o>n&&--r,a=new Array(i=r-e+1);++s<i;)a[s]=(e+s)*o}else{o=-o;let e=Math.round(t*o),r=Math.round(n*o);for(e/o<t&&++e,r/o>n&&--r,a=new Array(i=r-e+1);++s<i;)a[s]=(e+s)/o}return r&&a.reverse(),a}function j(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}function F(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function D(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function U(){}var S=.7,E=1/S,A="\\s*([+-]?\\d+)\\s*",H="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Y="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",z=/^#([0-9a-f]{3,8})$/,I=new RegExp(`^rgb\\(${A},${A},${A}\\)$`),O=new RegExp(`^rgb\\(${Y},${Y},${Y}\\)$`),q=new RegExp(`^rgba\\(${A},${A},${A},${H}\\)$`),R=new RegExp(`^rgba\\(${Y},${Y},${Y},${H}\\)$`),_=new RegExp(`^hsl\\(${H},${Y},${Y}\\)$`),P=new RegExp(`^hsla\\(${H},${Y},${Y},${H}\\)$`),L={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function W(){return this.rgb().formatHex()}function B(){return this.rgb().formatRgb()}function V(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=z.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?G(n):3===e?new K(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?X(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?X(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=I.exec(t))?new K(n[1],n[2],n[3],1):(n=O.exec(t))?new K(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=q.exec(t))?X(n[1],n[2],n[3],n[4]):(n=R.exec(t))?X(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=_.exec(t))?it(n[1],n[2]/100,n[3]/100,1):(n=P.exec(t))?it(n[1],n[2]/100,n[3]/100,n[4]):L.hasOwnProperty(t)?G(L[t]):"transparent"===t?new K(NaN,NaN,NaN,0):null}function G(t){return new K(t>>16&255,t>>8&255,255&t,1)}function X(t,n,e,r){return r<=0&&(t=n=e=NaN),new K(t,n,e,r)}function Z(t){return t instanceof U||(t=V(t)),t?new K((t=t.rgb()).r,t.g,t.b,t.opacity):new K}function J(t,n,e,r){return 1===arguments.length?Z(t):new K(t,n,e,null==r?1:r)}function K(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function Q(){return`#${rt(this.r)}${rt(this.g)}${rt(this.b)}`}function tt(){const t=nt(this.opacity);return`${1===t?"rgb(":"rgba("}${et(this.r)}, ${et(this.g)}, ${et(this.b)}${1===t?")":`, ${t})`}`}function nt(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function et(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function rt(t){return((t=et(t))<16?"0":"")+t.toString(16)}function it(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new st(t,n,e,r)}function at(t){if(t instanceof st)return new st(t.h,t.s,t.l,t.opacity);if(t instanceof U||(t=V(t)),!t)return new st;if(t instanceof st)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),a=Math.max(n,e,r),o=NaN,s=a-i,l=(a+i)/2;return s?(o=n===a?(e-r)/s+6*(e<r):e===a?(r-n)/s+2:(n-e)/s+4,s/=l<.5?a+i:2-a-i,o*=60):s=l>0&&l<1?0:o,new st(o,s,l,t.opacity)}function ot(t,n,e,r){return 1===arguments.length?at(t):new st(t,n,e,null==r?1:r)}function st(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function lt(t){return(t=(t||0)%360)<0?t+360:t}function ut(t){return Math.max(0,Math.min(1,t||0))}function ct(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}F(U,V,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:W,formatHex:W,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return at(this).formatHsl()},formatRgb:B,toString:B}),F(K,J,D(U,{brighter(t){return t=null==t?E:Math.pow(E,t),new K(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?S:Math.pow(S,t),new K(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new K(et(this.r),et(this.g),et(this.b),nt(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Q,formatHex:Q,formatHex8:function(){return`#${rt(this.r)}${rt(this.g)}${rt(this.b)}${rt(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:tt,toString:tt})),F(st,ot,D(U,{brighter(t){return t=null==t?E:Math.pow(E,t),new st(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?S:Math.pow(S,t),new st(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new K(ct(t>=240?t-240:t+120,i,r),ct(t,i,r),ct(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new st(lt(this.h),ut(this.s),ut(this.l),nt(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=nt(this.opacity);return`${1===t?"hsl(":"hsla("}${lt(this.h)}, ${100*ut(this.s)}%, ${100*ut(this.l)}%${1===t?")":`, ${t})`}`}}));const ht=Math.PI/180,ft=180/Math.PI,gt=.96422,dt=.82521,pt=4/29,mt=6/29,yt=3*mt*mt,wt=mt*mt*mt;function vt(t){if(t instanceof Mt)return new Mt(t.l,t.a,t.b,t.opacity);if(t instanceof $t)return jt(t);t instanceof K||(t=Z(t));var n,e,r=Tt(t.r),i=Tt(t.g),a=Tt(t.b),o=xt((.2225045*r+.7168786*i+.0606169*a)/1);return r===i&&i===a?n=e=o:(n=xt((.4360747*r+.3850649*i+.1430804*a)/gt),e=xt((.0139322*r+.0971045*i+.7141733*a)/dt)),new Mt(116*o-16,500*(n-o),200*(o-e),t.opacity)}function bt(t,n,e,r){return 1===arguments.length?vt(t):new Mt(t,n,e,null==r?1:r)}function Mt(t,n,e,r){this.l=+t,this.a=+n,this.b=+e,this.opacity=+r}function xt(t){return t>wt?Math.pow(t,1/3):t/yt+pt}function kt(t){return t>mt?t*t*t:yt*(t-pt)}function Nt(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function Tt(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function Ct(t,n,e,r){return 1===arguments.length?function(t){if(t instanceof $t)return new $t(t.h,t.c,t.l,t.opacity);if(t instanceof Mt||(t=vt(t)),0===t.a&&0===t.b)return new $t(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var n=Math.atan2(t.b,t.a)*ft;return new $t(n<0?n+360:n,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}(t):new $t(t,n,e,null==r?1:r)}function $t(t,n,e,r){this.h=+t,this.c=+n,this.l=+e,this.opacity=+r}function jt(t){if(isNaN(t.h))return new Mt(t.l,0,0,t.opacity);var n=t.h*ht;return new Mt(t.l,Math.cos(n)*t.c,Math.sin(n)*t.c,t.opacity)}F(Mt,bt,D(U,{brighter(t){return new Mt(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker(t){return new Mt(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,n=isNaN(this.a)?t:t+this.a/500,e=isNaN(this.b)?t:t-this.b/200;return new K(Nt(3.1338561*(n=gt*kt(n))-1.6168667*(t=1*kt(t))-.4906146*(e=dt*kt(e))),Nt(-.9787684*n+1.9161415*t+.033454*e),Nt(.0719453*n-.2289914*t+1.4052427*e),this.opacity)}})),F($t,Ct,D(U,{brighter(t){return new $t(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker(t){return new $t(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb(){return jt(this).rgb()}}));var Ft=-.14861,Dt=1.78277,Ut=-.29227,St=-.90649,Et=1.97294,At=Et*St,Ht=Et*Dt,Yt=Dt*Ut-St*Ft;function zt(t,n,e,r){return 1===arguments.length?function(t){if(t instanceof It)return new It(t.h,t.s,t.l,t.opacity);t instanceof K||(t=Z(t));var n=t.r/255,e=t.g/255,r=t.b/255,i=(Yt*r+At*n-Ht*e)/(Yt+At-Ht),a=r-i,o=(Et*(e-i)-Ut*a)/St,s=Math.sqrt(o*o+a*a)/(Et*i*(1-i)),l=s?Math.atan2(o,a)*ft-120:NaN;return new It(l<0?l+360:l,s,i,t.opacity)}(t):new It(t,n,e,null==r?1:r)}function It(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}F(It,zt,D(U,{brighter(t){return t=null==t?E:Math.pow(E,t),new It(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?S:Math.pow(S,t),new It(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=isNaN(this.h)?0:(this.h+120)*ht,n=+this.l,e=isNaN(this.s)?0:this.s*n*(1-n),r=Math.cos(t),i=Math.sin(t);return new K(255*(n+e*(Ft*r+Dt*i)),255*(n+e*(Ut*r+St*i)),255*(n+e*(Et*r)),this.opacity)}}));const Ot=t=>()=>t;function qt(t,n){return function(e){return t+e*n}}function Rt(t,n){var e=n-t;return e?qt(t,e>180||e<-180?e-360*Math.round(e/360):e):Ot(isNaN(t)?n:t)}function _t(t){return 1===(t=+t)?Pt:function(n,e){return e-n?function(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(r){return Math.pow(t+r*n,e)}}(n,e,t):Ot(isNaN(n)?e:n)}}function Pt(t,n){var e=n-t;return e?qt(t,e):Ot(isNaN(t)?n:t)}const Lt=function t(n){var e=_t(n);function r(t,n){var r=e((t=J(t)).r,(n=J(n)).r),i=e(t.g,n.g),a=e(t.b,n.b),o=Pt(t.opacity,n.opacity);return function(n){return t.r=r(n),t.g=i(n),t.b=a(n),t.opacity=o(n),t+""}}return r.gamma=t,r}(1);function Wt(t,n){n||(n=[]);var e,r=t?Math.min(n.length,t.length):0,i=n.slice();return function(a){for(e=0;e<r;++e)i[e]=t[e]*(1-a)+n[e]*a;return i}}function Bt(t,n){var e,r=n?n.length:0,i=t?Math.min(r,t.length):0,a=new Array(i),o=new Array(r);for(e=0;e<i;++e)a[e]=Qt(t[e],n[e]);for(;e<r;++e)o[e]=n[e];return function(t){for(e=0;e<i;++e)o[e]=a[e](t);return o}}function Vt(t,n){var e=new Date;return t=+t,n=+n,function(r){return e.setTime(t*(1-r)+n*r),e}}function Gt(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}function Xt(t,n){var e,r={},i={};for(e in null!==t&&"object"==typeof t||(t={}),null!==n&&"object"==typeof n||(n={}),n)e in t?r[e]=Qt(t[e],n[e]):i[e]=n[e];return function(t){for(e in r)i[e]=r[e](t);return i}}var Zt=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Jt=new RegExp(Zt.source,"g");function Kt(t,n){var e,r,i,a=Zt.lastIndex=Jt.lastIndex=0,o=-1,s=[],l=[];for(t+="",n+="";(e=Zt.exec(t))&&(r=Jt.exec(n));)(i=r.index)>a&&(i=n.slice(a,i),s[o]?s[o]+=i:s[++o]=i),(e=e[0])===(r=r[0])?s[o]?s[o]+=r:s[++o]=r:(s[++o]=null,l.push({i:o,x:Gt(e,r)})),a=Jt.lastIndex;return a<n.length&&(i=n.slice(a),s[o]?s[o]+=i:s[++o]=i),s.length<2?l[0]?function(t){return function(n){return t(n)+""}}(l[0].x):function(t){return function(){return t}}(n):(n=l.length,function(t){for(var e,r=0;r<n;++r)s[(e=l[r]).i]=e.x(t);return s.join("")})}function Qt(t,n){var e,r,i=typeof n;return null==n||"boolean"===i?Ot(n):("number"===i?Gt:"string"===i?(e=V(n))?(n=e,Lt):Kt:n instanceof V?Lt:n instanceof Date?Vt:(r=n,!ArrayBuffer.isView(r)||r instanceof DataView?Array.isArray(n)?Bt:"function"!=typeof n.valueOf&&"function"!=typeof n.toString||isNaN(n)?Xt:Gt:Wt))(t,n)}function tn(t,n){return t=+t,n=+n,function(e){return Math.round(t*(1-e)+n*e)}}function nn(t){return function(n,e){var r=t((n=ot(n)).h,(e=ot(e)).h),i=Pt(n.s,e.s),a=Pt(n.l,e.l),o=Pt(n.opacity,e.opacity);return function(t){return n.h=r(t),n.s=i(t),n.l=a(t),n.opacity=o(t),n+""}}}const en=nn(Rt);var rn=nn(Pt);function an(t){return function(n,e){var r=t((n=Ct(n)).h,(e=Ct(e)).h),i=Pt(n.c,e.c),a=Pt(n.l,e.l),o=Pt(n.opacity,e.opacity);return function(t){return n.h=r(t),n.c=i(t),n.l=a(t),n.opacity=o(t),n+""}}}const on=an(Rt);var sn=an(Pt);function ln(t){return function n(e){function r(n,r){var i=t((n=zt(n)).h,(r=zt(r)).h),a=Pt(n.s,r.s),o=Pt(n.l,r.l),s=Pt(n.opacity,r.opacity);return function(t){return n.h=i(t),n.s=a(t),n.l=o(Math.pow(t,e)),n.opacity=s(t),n+""}}return e=+e,r.gamma=n,r}(1)}const un=ln(Rt);var cn=ln(Pt);function hn(t){return+t}var fn=[0,1];function gn(t){return t}function dn(t,n){return(n-=t=+t)?function(e){return(e-t)/n}:(e=isNaN(n)?NaN:.5,function(){return e});var e}function pn(t,n,e){var r=t[0],i=t[1],a=n[0],o=n[1];return i<r?(r=dn(i,r),a=e(o,a)):(r=dn(r,i),a=e(a,o)),function(t){return a(r(t))}}function mn(t,n,e){var r=Math.min(t.length,n.length)-1,i=new Array(r),a=new Array(r),o=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),n=n.slice().reverse());++o<r;)i[o]=dn(t[o],t[o+1]),a[o]=e(n[o],n[o+1]);return function(n){var e=k(t,n,1,r)-1;return a[e](i[e](n))}}function yn(){var t,n,e,r,i,a,o=fn,s=fn,l=Qt,u=gn;function c(){var t,n,e,l=Math.min(o.length,s.length);return u!==gn&&(t=o[0],n=o[l-1],t>n&&(e=t,t=n,n=e),u=function(e){return Math.max(t,Math.min(n,e))}),r=l>2?mn:pn,i=a=null,h}function h(n){return null==n||isNaN(n=+n)?e:(i||(i=r(o.map(t),s,l)))(t(u(n)))}return h.invert=function(e){return u(n((a||(a=r(s,o.map(t),Gt)))(e)))},h.domain=function(t){return arguments.length?(o=Array.from(t,hn),c()):o.slice()},h.range=function(t){return arguments.length?(s=Array.from(t),c()):s.slice()},h.rangeRound=function(t){return s=Array.from(t),l=tn,c()},h.clamp=function(t){return arguments.length?(u=!!t||gn,c()):u!==gn},h.interpolate=function(t){return arguments.length?(l=t,c()):l},h.unknown=function(t){return arguments.length?(e=t,h):e},function(e,r){return t=e,n=r,c()}}function wn(t,n){if((e=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var e,r=t.slice(0,e);return[r.length>1?r[0]+r.slice(2):r,+t.slice(e+1)]}var vn,bn=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Mn(t){if(!(n=bn.exec(t)))throw new Error("invalid format: "+t);var n;return new xn({fill:n[1],align:n[2],sign:n[3],symbol:n[4],zero:n[5],width:n[6],comma:n[7],precision:n[8]&&n[8].slice(1),trim:n[9],type:n[10]})}function xn(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function kn(t,n){var e=wn(t,n);if(!e)return t+"";var r=e[0],i=e[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")}Mn.prototype=xn.prototype,xn.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const Nn={"%":(t,n)=>(100*t).toFixed(n),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,n)=>t.toExponential(n),f:(t,n)=>t.toFixed(n),g:(t,n)=>t.toPrecision(n),o:t=>Math.round(t).toString(8),p:(t,n)=>kn(100*t,n),r:kn,s:function(t,n){var e=wn(t,n);if(!e)return t+"";var r=e[0],i=e[1],a=i-(vn=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,o=r.length;return a===o?r:a>o?r+new Array(a-o+1).join("0"):a>0?r.slice(0,a)+"."+r.slice(a):"0."+new Array(1-a).join("0")+wn(t,Math.max(0,n+a-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function Tn(t){return t}var Cn,$n,jn=Array.prototype.map,Fn=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Dn(t){var n,e,r=void 0===t.grouping||void 0===t.thousands?Tn:(n=jn.call(t.grouping,Number),e=t.thousands+"",function(t,r){for(var i=t.length,a=[],o=0,s=n[0],l=0;i>0&&s>0&&(l+s+1>r&&(s=Math.max(1,r-l)),a.push(t.substring(i-=s,i+s)),!((l+=s+1)>r));)s=n[o=(o+1)%n.length];return a.reverse().join(e)}),i=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",o=void 0===t.decimal?".":t.decimal+"",s=void 0===t.numerals?Tn:function(t){return function(n){return n.replace(/[0-9]/g,(function(n){return t[+n]}))}}(jn.call(t.numerals,String)),l=void 0===t.percent?"%":t.percent+"",u=void 0===t.minus?"−":t.minus+"",c=void 0===t.nan?"NaN":t.nan+"";function h(t){var n=(t=Mn(t)).fill,e=t.align,h=t.sign,f=t.symbol,g=t.zero,d=t.width,p=t.comma,m=t.precision,y=t.trim,w=t.type;"n"===w?(p=!0,w="g"):Nn[w]||(void 0===m&&(m=12),y=!0,w="g"),(g||"0"===n&&"="===e)&&(g=!0,n="0",e="=");var v="$"===f?i:"#"===f&&/[boxX]/.test(w)?"0"+w.toLowerCase():"",b="$"===f?a:/[%p]/.test(w)?l:"",M=Nn[w],x=/[defgprs%]/.test(w);function k(t){var i,a,l,f=v,k=b;if("c"===w)k=M(t)+k,t="";else{var N=(t=+t)<0||1/t<0;if(t=isNaN(t)?c:M(Math.abs(t),m),y&&(t=function(t){t:for(var n,e=t.length,r=1,i=-1;r<e;++r)switch(t[r]){case".":i=n=r;break;case"0":0===i&&(i=r),n=r;break;default:if(!+t[r])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(n+1):t}(t)),N&&0===+t&&"+"!==h&&(N=!1),f=(N?"("===h?h:u:"-"===h||"("===h?"":h)+f,k=("s"===w?Fn[8+vn/3]:"")+k+(N&&"("===h?")":""),x)for(i=-1,a=t.length;++i<a;)if(48>(l=t.charCodeAt(i))||l>57){k=(46===l?o+t.slice(i+1):t.slice(i))+k,t=t.slice(0,i);break}}p&&!g&&(t=r(t,1/0));var T=f.length+t.length+k.length,C=T<d?new Array(d-T+1).join(n):"";switch(p&&g&&(t=r(C+t,C.length?d-k.length:1/0),C=""),e){case"<":t=f+t+k+C;break;case"=":t=f+C+t+k;break;case"^":t=C.slice(0,T=C.length>>1)+f+t+k+C.slice(T);break;default:t=C+f+t+k}return s(t)}return m=void 0===m?6:/[gprs]/.test(w)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),k.toString=function(){return t+""},k}return{format:h,formatPrefix:function(t,n){var e,r=h(((t=Mn(t)).type="f",t)),i=3*Math.max(-8,Math.min(8,Math.floor((e=n,((e=wn(Math.abs(e)))?e[1]:NaN)/3)))),a=Math.pow(10,-i),o=Fn[8+i/3];return function(t){return r(a*t)+o}}}}function Un(t){return Math.log(t)}function Sn(t){return Math.exp(t)}function En(t){return-Math.log(-t)}function An(t){return-Math.exp(-t)}function Hn(t){return isFinite(t)?+("1e"+t):t<0?0:t}function Yn(t){return(n,e)=>-t(-n,e)}function zn(t){const n=t(Un,Sn),e=n.domain;let r,i,a=10;function o(){return r=function(t){return t===Math.E?Math.log:10===t&&Math.log10||2===t&&Math.log2||(t=Math.log(t),n=>Math.log(n)/t)}(a),i=function(t){return 10===t?Hn:t===Math.E?Math.exp:n=>Math.pow(t,n)}(a),e()[0]<0?(r=Yn(r),i=Yn(i),t(En,An)):t(Un,Sn),n}return n.base=function(t){return arguments.length?(a=+t,o()):a},n.domain=function(t){return arguments.length?(e(t),o()):e()},n.ticks=t=>{const n=e();let o=n[0],s=n[n.length-1];const l=s<o;l&&([o,s]=[s,o]);let u,c,h=r(o),f=r(s);const g=null==t?10:+t;let d=[];if(!(a%1)&&f-h<g){if(h=Math.floor(h),f=Math.ceil(f),o>0){for(;h<=f;++h)for(u=1;u<a;++u)if(c=h<0?u/i(-h):u*i(h),!(c<o)){if(c>s)break;d.push(c)}}else for(;h<=f;++h)for(u=a-1;u>=1;--u)if(c=h>0?u/i(-h):u*i(h),!(c<o)){if(c>s)break;d.push(c)}2*d.length<g&&(d=$(o,s,g))}else d=$(h,f,Math.min(f-h,g)).map(i);return l?d.reverse():d},n.tickFormat=(t,e)=>{if(null==t&&(t=10),null==e&&(e=10===a?"s":","),"function"!=typeof e&&(a%1||null!=(e=Mn(e)).precision||(e.trim=!0),e=$n(e)),t===1/0)return e;const o=Math.max(1,a*t/n.ticks().length);return t=>{let n=t/i(Math.round(r(t)));return n*a<a-.5&&(n*=a),n<=o?e(t):""}},n.nice=()=>e(function(t,n){var e,r=0,i=(t=t.slice()).length-1,a=t[r],o=t[i];return o<a&&(e=r,r=i,i=e,e=a,a=o,o=e),t[r]=n.floor(a),t[i]=n.ceil(o),t}(e(),{floor:t=>i(Math.floor(r(t))),ceil:t=>i(Math.ceil(r(t)))})),n}function In(){const t=zn(yn()).domain([1,10]);return t.copy=()=>{return(n=t,e=In(),e.domain(n.domain()).range(n.range()).interpolate(n.interpolate()).clamp(n.clamp()).unknown(n.unknown())).base(t.base());var n,e},j.apply(t,arguments),t}Cn=Dn({thousands:",",grouping:[3],currency:["$",""]}),$n=Cn.format,Cn.formatPrefix;const On=new Date,qn=new Date;function Rn(t,n,e,r){function i(n){return t(n=0===arguments.length?new Date:new Date(+n)),n}return i.floor=n=>(t(n=new Date(+n)),n),i.ceil=e=>(t(e=new Date(e-1)),n(e,1),t(e),e),i.round=t=>{const n=i(t),e=i.ceil(t);return t-n<e-t?n:e},i.offset=(t,e)=>(n(t=new Date(+t),null==e?1:Math.floor(e)),t),i.range=(e,r,a)=>{const o=[];if(e=i.ceil(e),a=null==a?1:Math.floor(a),!(e<r&&a>0))return o;let s;do{o.push(s=new Date(+e)),n(e,a),t(e)}while(s<e&&e<r);return o},i.filter=e=>Rn((n=>{if(n>=n)for(;t(n),!e(n);)n.setTime(n-1)}),((t,r)=>{if(t>=t)if(r<0)for(;++r<=0;)for(;n(t,-1),!e(t););else for(;--r>=0;)for(;n(t,1),!e(t););})),e&&(i.count=(n,r)=>(On.setTime(+n),qn.setTime(+r),t(On),t(qn),Math.floor(e(On,qn))),i.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?i.filter(r?n=>r(n)%t===0:n=>i.count(0,n)%t===0):i:null)),i}const _n=1e3,Pn=6e4,Ln=36e5,Wn=864e5,Bn=6048e5,Vn=Rn((t=>{t.setTime(t-t.getMilliseconds())}),((t,n)=>{t.setTime(+t+n*_n)}),((t,n)=>(n-t)/_n),(t=>t.getUTCSeconds()));Vn.range;const Gn=Rn((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*_n)}),((t,n)=>{t.setTime(+t+n*Pn)}),((t,n)=>(n-t)/Pn),(t=>t.getMinutes()));Gn.range;const Xn=Rn((t=>{t.setUTCSeconds(0,0)}),((t,n)=>{t.setTime(+t+n*Pn)}),((t,n)=>(n-t)/Pn),(t=>t.getUTCMinutes()));Xn.range;const Zn=Rn((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*_n-t.getMinutes()*Pn)}),((t,n)=>{t.setTime(+t+n*Ln)}),((t,n)=>(n-t)/Ln),(t=>t.getHours()));Zn.range;const Jn=Rn((t=>{t.setUTCMinutes(0,0,0)}),((t,n)=>{t.setTime(+t+n*Ln)}),((t,n)=>(n-t)/Ln),(t=>t.getUTCHours()));Jn.range;const Kn=Rn((t=>t.setHours(0,0,0,0)),((t,n)=>t.setDate(t.getDate()+n)),((t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*Pn)/Wn),(t=>t.getDate()-1));Kn.range;const Qn=Rn((t=>{t.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCDate(t.getUTCDate()+n)}),((t,n)=>(n-t)/Wn),(t=>t.getUTCDate()-1));Qn.range;function te(t){return Rn((n=>{n.setDate(n.getDate()-(n.getDay()+7-t)%7),n.setHours(0,0,0,0)}),((t,n)=>{t.setDate(t.getDate()+7*n)}),((t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*Pn)/Bn))}Rn((t=>{t.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCDate(t.getUTCDate()+n)}),((t,n)=>(n-t)/Wn),(t=>Math.floor(t/Wn))).range;const ne=te(0),ee=te(1),re=te(2),ie=te(3),ae=te(4),oe=te(5),se=te(6);function le(t){return Rn((n=>{n.setUTCDate(n.getUTCDate()-(n.getUTCDay()+7-t)%7),n.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCDate(t.getUTCDate()+7*n)}),((t,n)=>(n-t)/Bn))}ne.range,ee.range,re.range,ie.range,ae.range,oe.range,se.range;const ue=le(0),ce=le(1),he=le(2),fe=le(3),ge=le(4),de=le(5),pe=le(6);ue.range,ce.range,he.range,fe.range,ge.range,de.range,pe.range;const me=Rn((t=>{t.setDate(1),t.setHours(0,0,0,0)}),((t,n)=>{t.setMonth(t.getMonth()+n)}),((t,n)=>n.getMonth()-t.getMonth()+12*(n.getFullYear()-t.getFullYear())),(t=>t.getMonth()));me.range;const ye=Rn((t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCMonth(t.getUTCMonth()+n)}),((t,n)=>n.getUTCMonth()-t.getUTCMonth()+12*(n.getUTCFullYear()-t.getUTCFullYear())),(t=>t.getUTCMonth()));ye.range;const we=Rn((t=>{t.setMonth(0,1),t.setHours(0,0,0,0)}),((t,n)=>{t.setFullYear(t.getFullYear()+n)}),((t,n)=>n.getFullYear()-t.getFullYear()),(t=>t.getFullYear()));we.every=t=>isFinite(t=Math.floor(t))&&t>0?Rn((n=>{n.setFullYear(Math.floor(n.getFullYear()/t)*t),n.setMonth(0,1),n.setHours(0,0,0,0)}),((n,e)=>{n.setFullYear(n.getFullYear()+e*t)})):null,we.range;const ve=Rn((t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n)}),((t,n)=>n.getUTCFullYear()-t.getUTCFullYear()),(t=>t.getUTCFullYear()));ve.every=t=>isFinite(t=Math.floor(t))&&t>0?Rn((n=>{n.setUTCFullYear(Math.floor(n.getUTCFullYear()/t)*t),n.setUTCMonth(0,1),n.setUTCHours(0,0,0,0)}),((n,e)=>{n.setUTCFullYear(n.getUTCFullYear()+e*t)})):null,ve.range;var be={lab:function(t,n){var e=Pt((t=bt(t)).l,(n=bt(n)).l),r=Pt(t.a,n.a),i=Pt(t.b,n.b),a=Pt(t.opacity,n.opacity);return function(n){return t.l=e(n),t.a=r(n),t.b=i(n),t.opacity=a(n),t+""}},hcl:on,"hcl-long":sn,hsl:en,"hsl-long":rn,cubehelix:un,"cubehelix-long":cn,rgb:Lt};var Me=new Date(Date.UTC(2020,1,2,3,4,5));var xe={day:Kn,hour:Zn,minute:Gn,month:me,second:Vn,week:ne,year:we},ke={day:Qn,hour:Jn,minute:Xn,month:ye,second:Vn,week:ue,year:ve};var Ne=["domain","nice","zero","interpolate","round","range","reverse","align","base","clamp","constant","exponent","padding","unknown"],Te={domain:function(t,n){n.domain&&t.domain(n.domain)},nice:function(t,n){if("nice"in n&&void 0!==n.nice&&"nice"in t){var e=n.nice;if("boolean"==typeof e)e&&t.nice();else if("number"==typeof e)t.nice(e);else{var r=t,i=function(t){return"2020-02-02 03:04"===t.tickFormat(1,"%Y-%m-%d %H:%M")(Me)}(r);if("string"==typeof e)r.nice(i?ke[e]:xe[e]);else{var a=e.interval,o=e.step,s=(i?ke[a]:xe[a]).every(o);null!=s&&r.nice(s)}}}},zero:function(t,n){if("zero"in n&&!0===n.zero){var e=t.domain(),r=e[0],i=e[1],a=i<r,o=a?[i,r]:[r,i],s=o[0],l=o[1],u=[Math.min(0,s),Math.max(0,l)];t.domain(a?u.reverse():u)}},interpolate:function(t,n){if("interpolate"in n&&"interpolate"in t&&void 0!==n.interpolate){var e=function(t){switch(t){case"lab":case"hcl":case"hcl-long":case"hsl":case"hsl-long":case"cubehelix":case"cubehelix-long":case"rgb":return be[t]}var n=t.type,e=t.gamma,r=be[n];return void 0===e?r:r.gamma(e)}(n.interpolate);t.interpolate(e)}},round:function(t,n){"round"in n&&void 0!==n.round&&(n.round&&"interpolate"in n&&void 0!==n.interpolate?console.warn("[visx/scale/applyRound] ignoring round: scale config contains round and interpolate. only applying interpolate. config:",n):"round"in t?t.round(n.round):"interpolate"in t&&n.round&&t.interpolate(tn))},align:function(t,n){"align"in t&&"align"in n&&void 0!==n.align&&t.align(n.align)},base:function(t,n){"base"in t&&"base"in n&&void 0!==n.base&&t.base(n.base)},clamp:function(t,n){"clamp"in t&&"clamp"in n&&void 0!==n.clamp&&t.clamp(n.clamp)},constant:function(t,n){"constant"in t&&"constant"in n&&void 0!==n.constant&&t.constant(n.constant)},exponent:function(t,n){"exponent"in t&&"exponent"in n&&void 0!==n.exponent&&t.exponent(n.exponent)},padding:function(t,n){"padding"in t&&"padding"in n&&void 0!==n.padding&&t.padding(n.padding),"paddingInner"in t&&"paddingInner"in n&&void 0!==n.paddingInner&&t.paddingInner(n.paddingInner),"paddingOuter"in t&&"paddingOuter"in n&&void 0!==n.paddingOuter&&t.paddingOuter(n.paddingOuter)},range:function(t,n){n.range&&t.range(n.range)},reverse:function(t,n){if(n.reverse){var e=t.range().slice().reverse();t.range(e)}},unknown:function(t,n){"unknown"in t&&"unknown"in n&&void 0!==n.unknown&&t.unknown(n.unknown)}};var Ce=function(){for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];var r=new Set(n),i=Ne.filter((function(t){return r.has(t)}));return function(t,n){return void 0!==n&&i.forEach((function(e){Te[e](t,n)})),t}}("domain","range","reverse","base","clamp","interpolate","nice","round");function $e(t){return Ce(In(),t)}const je=e((()=>i((()=>import("./index-Dsk8s7oD.js")),__vite__mapDeps([0,1,2]),import.meta.url).then((t=>t.Text))),{fallback:g}),Fe=e((()=>i((()=>import("./Wordcloud-C_XnZNah.js").then((t=>t.W))),__vite__mapDeps([3,1,2,4]),import.meta.url)),{fallback:g}),De=e((()=>i((()=>import("./MyTable-DnjDmN7z.js")),__vite__mapDeps([5,1,2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27]),import.meta.url)),{fallback:f});function Ue({text:e,name:i="",title:f={en:"📈 Words statistic",vi:"📈 Thống kê từ"},renderButton:g}){const{ti:v}=a(),[b,M]=t.useState(!1),[x,k]=t.useState(5),[N,T]=t.useState(!0),[C,$]=t.useState([]),[j,F]=t.useState(!0),D=t.useMemo((()=>N?C.filter((t=>t.count>1)):C),[N,C]),U=t.useMemo((()=>{const t=D.map((t=>({text:t.word,value:j?t.count:t.score}))).slice(0,80),n=$e({domain:[Math.min(...t.map((t=>t.value))),Math.max(...t.map((t=>t.value)))],range:[10,100]});return{words:t,fontScale:n,fontSizeSetter:t=>n(t.value),fixedValueGenerator:()=>.5,colors:["#F38181","#FCE38A","#EAFFD0","#95E1D3","#E82561","#5CB338","#9694FF","#0D92F4","#E85C0D","#F8EDE3"]}}),[D,j]);t.useEffect((()=>{b&&o("WordStatisticButton:open:"+i)}),[b,i]),t.useEffect((()=>{if(b){const t=Se(e,x);$(t)}}),[b,e,x]);const S=[{title:"#",dataIndex:"index",key:"index",width:70},{title:v({en:"Word",vi:"Từ"}),dataIndex:"word",key:"word",width:"auto"},{title:v({en:"Length",vi:"Độ dài"}),dataIndex:"length",key:"length",sorter:(t,n)=>t.length-n.length,width:100,rangeFilter:{getValue:t=>t.length}},{title:v({en:"Frequency",vi:"Xuất hiện"}),dataIndex:"count",key:"count",sorter:(t,n)=>t.count-n.count,render:(t,n)=>s(n.count),width:100},{title:v({en:"Score",vi:"Điểm"}),dataIndex:"score",key:"score",sorter:(t,n)=>t.score-n.score,render:(t,n)=>s(n.score),width:100}],E=[{key:"table",icon:n.jsx("i",{className:"fa-solid fa-table"}),label:v({en:"Table",vi:"Bảng"}),children:n.jsx(De,{data:D,columns:S,keyExtractor:t=>t.index,renderTitle:()=>n.jsxs(n.Fragment,{children:[n.jsxs(c.Compact,{children:[n.jsx(r,{icon:n.jsx("i",{className:"fa-solid fa-minus"}),onClick:()=>k((t=>t-1))}),n.jsx(u,{title:v({en:"Max length",vi:"Độ dài tối đa"}),children:n.jsx(w,{value:x,min:1,max:10,onChange:t=>k(t||1)})}),n.jsx(r,{icon:n.jsx("i",{className:"fa-solid fa-plus"}),onClick:()=>k((t=>t+1))})]}),n.jsx(u,{title:v({en:"Hide words with frequency = 1",vi:"Ẩn từ chỉ xuất hiện 1 lần"}),children:n.jsx(p,{checked:N,onChange:t=>T(t)})})]}),searchable:!0,virtual:!0,onSearchRow:(t,n)=>l(t,null==n?void 0:n.word)})},{key:"graph",icon:n.jsx("i",{className:"fa-solid fa-chart-line"}),label:v({en:"Graph",vi:"Biểu đồ"}),children:n.jsxs(n.Fragment,{children:[n.jsx(u,{title:v({en:"Sort by score / by frequency",vi:"Sắp xếp theo điểm / theo lần xuất hiện"}),children:n.jsxs(c,{direction:"horizontal",children:[n.jsx(p,{checked:j,onChange:t=>F(t)}),n.jsx(h.Text,{children:v(j?{en:"Frequency",vi:"Lần xuất hiện"}:{en:"Score",vi:"Điểm"})})]})}),n.jsx(Fe,{words:U.words,width:750,height:500,fontSize:U.fontSizeSetter,font:"sans-serif",padding:2,spiral:"archimedean",rotate:0,random:U.fixedValueGenerator,children:t=>t.map(((t,e)=>n.jsx(je,{fill:U.colors[e%U.colors.length],textAnchor:"middle",transform:`translate(${t.x}, ${t.y}) rotate(${t.rotate})`,fontSize:t.size,fontFamily:t.font,children:t.text},t.text)))})]})}];return n.jsxs(n.Fragment,{children:[(null==g?void 0:g({showModal:()=>M(!0)}))||n.jsx(d,{type:"new",children:n.jsx(r,{icon:n.jsx("i",{className:"fa-solid fa-chart-line"}),onClick:()=>M(!0),children:v({en:"Statistic",vi:"Thống kê"})})}),n.jsx(m,{title:n.jsx(h.Title,{level:4,style:{margin:0},children:v(f)}),open:b,onOk:()=>M(!1),onCancel:()=>M(!1),width:"90%",style:{maxWidth:800,minWidth:500},children:n.jsx(y,{items:E,onTabClick:t=>o("WordStatistic:clickTab:"+t)})})]})}function Se(t,n=5){if(!t)return[];const e=t.replace(/[^\p{L}\s]/gu," ").split(/\s+/).filter((t=>t.length>1)).map((t=>t.toLowerCase())),r=new Map;for(let o=1;o<=n;o++)for(let t=0;t<=e.length-o;t++){const n=e.slice(t,t+o).join(" ");r.set(n,(r.get(n)||0)+1)}const i=new Map;r.forEach(((t,n)=>{const e=n.split(/\s+/).length;i.set(n,t**e)}));const a=Array.from(i.entries()).sort((([,t],[,n])=>n-t)).map((([t,n],e)=>{const i=t.split(" ").length;return{index:e,word:t,length:i,count:r.get(t)||0,score:n}}));return console.log(a),a}export{Se as calculateWordsStatitic,Ue as default};
