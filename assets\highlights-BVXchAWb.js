import{y as i,x as o,az as e,X as n}from"./MyApp-DW5WH4Ub.js";import"./index-Cak6rALw.js";async function r(r,t=""){const a=await i({variables:{scale:1,id:btoa(`profile_tile_view:${r}:intro:intro_featured_highlights_content:hscroll_cards:profile_timeline:3:7`),count:9,cursor:t},doc_id:"9516333321764857"}),l=o(a),{edges:d=[],page_info:s={}}=e(l)||{};return d.map((i=>{var o,e;return{id:null==(e=null==(o=null==i?void 0:i.node)?void 0:o.node)?void 0:e.id,title:n(i,"name"),thumbnail:n(i,"image.uri"),count:n(i,"highlighted_cards.count"),cursor:null==s?void 0:s.end_cursor}}))}export{r as getHighlights};
