import{L as e,M as t,N as o,O as n,P as r,r as a,G as l,m as i,R as s,T as c,Y as d,Z as p,a3 as m}from"./index-Cak6rALw.js";import{H as g,M as u,K as y,ag as b,T as v}from"./MyApp-DW5WH4Ub.js";const f=e=>e?"function"==typeof e?e():e:null,x=e=>{const{componentCls:t,popoverColor:o,titleMinWidth:r,fontWeightStrong:a,innerPadding:l,boxShadowSecondary:i,colorTextHeading:s,borderRadiusLG:c,zIndexPopup:d,titleMarginBottom:p,colorBgElevated:m,popoverBg:u,titleBorderBottom:y,innerContentPadding:b,titlePadding:v}=e;return[{[t]:Object.assign(Object.assign({},n(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:d,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":m,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{backgroundColor:u,backgroundClip:"padding-box",borderRadius:c,boxShadow:i,padding:l},[`${t}-title`]:{minWidth:r,marginBottom:p,color:s,fontWeight:a,borderBottom:y,padding:v},[`${t}-inner-content`]:{color:o,padding:b}})},g(e,"var(--antd-arrow-background-color)"),{[`${t}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${t}-content`]:{display:"inline-block"}}}]},O=e=>{const{componentCls:t}=e;return{[t]:r.map((o=>{const n=e[`${o}6`];return{[`&${t}-${o}`]:{"--antd-arrow-background-color":n,[`${t}-inner`]:{backgroundColor:n},[`${t}-arrow`]:{background:"transparent"}}}}))}},h=e("Popover",(e=>{const{colorBgElevated:n,colorText:r}=e,a=t(e,{popoverBg:n,popoverColor:r});return[x(a),O(a),o(a,"zoom-big")]}),(e=>{const{lineWidth:t,controlHeight:o,fontHeight:n,padding:r,wireframe:a,zIndexPopupBase:l,borderRadiusLG:i,marginXS:s,lineType:c,colorSplit:d,paddingSM:p}=e,m=o-n,g=m/2,b=m/2-t,v=r;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:l+30},u(e)),y({contentRadius:i,limitVerticalRadius:!0})),{innerPadding:a?0:12,titleMarginBottom:a?0:s,titlePadding:a?`${g}px ${v}px ${b}px`:0,titleBorderBottom:a?`${t}px ${c} ${d}`:"none",innerContentPadding:a?`${p}px ${v}px`:0})}),{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});const C=({title:e,content:t,prefixCls:o})=>e||t?a.createElement(a.Fragment,null,e&&a.createElement("div",{className:`${o}-title`},e),t&&a.createElement("div",{className:`${o}-inner-content`},t)):null,j=e=>{const{hashId:t,prefixCls:o,className:n,style:r,placement:l="top",title:s,content:c,children:d}=e,p=f(s),m=f(c),g=i(t,o,`${o}-pure`,`${o}-placement-${l}`,n);return a.createElement("div",{className:g,style:r},a.createElement("div",{className:`${o}-arrow`}),a.createElement(b,Object.assign({},e,{className:t,prefixCls:o}),d||a.createElement(C,{prefixCls:o,title:p,content:m})))},w=e=>{const{prefixCls:t,className:o}=e,n=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]])}return o}(e,["prefixCls","className"]),{getPrefixCls:r}=a.useContext(l),s=r("popover",t),[c,d,p]=h(s);return c(a.createElement(j,Object.assign({},n,{prefixCls:s,hashId:d,className:i(o,p)})))};const $=a.forwardRef(((e,t)=>{var o,n;const{prefixCls:r,title:l,content:g,overlayClassName:u,placement:y="top",trigger:b="hover",children:x,mouseEnterDelay:O=.1,mouseLeaveDelay:j=.1,onOpenChange:w,overlayStyle:$={},styles:P,classNames:N}=e,E=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]])}return o}(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:S,className:B,style:k,classNames:W,styles:M}=s("popover"),D=S("popover",r),[I,z,R]=h(D),L=S(),T=i(u,z,R,B,W.root,null==N?void 0:N.root),H=i(W.body,null==N?void 0:N.body),[V,_]=c(!1,{value:null!==(o=e.open)&&void 0!==o?o:e.visible,defaultValue:null!==(n=e.defaultOpen)&&void 0!==n?n:e.defaultVisible}),A=(e,t)=>{_(e,!0),null==w||w(e,t)},G=f(l),K=f(g);return I(a.createElement(v,Object.assign({placement:y,trigger:b,mouseEnterDelay:O,mouseLeaveDelay:j},E,{prefixCls:D,classNames:{root:T,body:H},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},M.root),k),$),null==P?void 0:P.root),body:Object.assign(Object.assign({},M.body),null==P?void 0:P.body)},ref:t,open:V,onOpenChange:e=>{A(e)},overlay:G||K?a.createElement(C,{prefixCls:D,title:G,content:K}):null,transitionName:d(L,"zoom-big",E.transitionName),"data-popover-inject":!0}),p(x,{onKeyDown:e=>{var t,o;a.isValidElement(x)&&(null===(o=null==x?void 0:(t=x.props).onKeyDown)||void 0===o||o.call(t,e)),(e=>{e.keyCode===m.ESC&&A(!1,e)})(e)}})))}));$._InternalPanelDoNotUseOrYouWillBeFired=w;export{$ as P,w as a,f as g};
