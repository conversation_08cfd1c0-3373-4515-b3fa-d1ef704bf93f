const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./MyApp-DW5WH4Ub.js","./index-Cak6rALw.js","./index-DIX6R8z-.css"])))=>i.map(i=>d[i]);
import{aM as r,aN as t,aO as n}from"./index-Cak6rALw.js";import{z as a,y as e}from"./MyApp-DW5WH4Ub.js";async function i(){const r=await n("chrome.cookies.get",[{url:"https://www.instagram.com",name:"csrftoken"}]);return null==r?void 0:r.value}async function o(r={},t="https://www.instagram.com/graphql/query"){const n=await i();return await e(r,t,!0,{},{"x-csrftoken":n})}function l(r){return`https://www.instagram.com/${r}`}async function u(n){var e,i;let o=await r("https://www.instagram.com/web/search/topsearch/?query="+n);const{parseSafe:l}=await t((async()=>{const{parseSafe:r}=await import("./MyApp-DW5WH4Ub.js").then((r=>r.aU));return{parseSafe:r}}),__vite__mapDeps([0,1,2]),import.meta.url);let u=l(o);if("ok"!=u.status)throw Error("Server response error");const s=null==(i=null==(e=null==u?void 0:u.users)?void 0:e.find((r=>{var t;return(null==(t=null==r?void 0:r.user)?void 0:t.username)==n})))?void 0:i.user;if(!s)throw Error("Instagram user not found");return{avatar:null==s?void 0:s.profile_pic_url,avatarBig:null==s?void 0:s.profile_pic_url,id:null==s?void 0:s.id,uid:null==s?void 0:s.id,name:(null==s?void 0:s.full_name)||(null==s?void 0:s.username),username:n,type:a.IGUser,url:`https://www.instagram.com/${null==s?void 0:s.username}`,raw:u}}function s(r){var t,n,a;return(null==r?void 0:r.length)?r[0].width&&r[0].height?null==(a=null==(n=null==r?void 0:r.sort(((r,t)=>t.width*t.height-r.width*r.height)))?void 0:n[0])?void 0:a.url:null==(t=r.find((r=>"101"==r.type))||r[0])?void 0:t.url:null}export{o as fetchInstaGraphQl,s as getBiggestUrl,i as getCsrftoken,l as getInstaUrlFromId,u as getInstaUserInfo};
