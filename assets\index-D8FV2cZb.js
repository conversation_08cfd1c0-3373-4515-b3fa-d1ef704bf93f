import{bc as e,r as i,b0 as n,b5 as t,bi as a,bk as s}from"./index-Cak6rALw.js";import{d as o}from"./dayjs.min-CzPn7FMI.js";import{d as l,u as r,t as d,S as c,c as m,b as g,h,T as p,s as u,v as x,o as v}from"./MyApp-DW5WH4Ub.js";import{u as f}from"./useForceStop-CvbJS7ei.js";import j from"./useCacheState-CAnxkewm.js";import{E as y}from"./ExportButton-CbyepAf_.js";import{getPageInvites as k,UserTypeInPage as T,getMyLikedPages as b,unlikePage as D,unFollowPage as I,declinePageInvite as w}from"./pages-DvqVBMHR.js";import L from"./WordStatisticButton-D2UxB2M4.js";import N from"./MyTable-DnjDmN7z.js";import{u as P}from"./useAction-uz-MrZwJ.js";import{u as U}from"./useDevMode-yj0U7_8D.js";import{S as C}from"./Screen-Buq7HJpK.js";import{I as E}from"./index-CDSnY0KE.js";import{P as F}from"./index-DP_qolD8.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./BadgeWrapper-BCbYXXfB.js";import"./index-Bp2s1Wws.js";import"./index-Bx3o6rwA.js";import"./index-IxNfZAD-.js";import"./index-BzMPigdO.js";import"./DownOutlined-B-JcS-29.js";import"./index-ByRdMNW-.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./row-BMfM-of4.js";import"./useVIP-D5FAMGQQ.js";import"./sweetalert2.esm.all-BZxvatOx.js";import"./getIds-DAzc-wzf.js";import"./index-CC5caqt_.js";import"./col-CzA09p0P.js";import"./index-PbLYNhdQ.js";function S(){const{devMode:S}=U(),{message:B}=l(),M=e(),{ti:A}=r(),{onClickAction:_,onClickBulkActions:Y}=P(),R=f(),[O,$]=j("Pages.data",[]),[H,W]=j("Pages.finished",!1),[K,V]=i.useState(!1),z=i.useMemo((()=>O.map(((e,i)=>({...e,index:i+1})))),[O]);i.useEffect((()=>{H||q(!1)}),[]);const q=async(e=!1)=>{var i;if(K&&!e)return;const n=R.start(),t=e?[]:[...z],a=new Set(t.map((e=>e.id))),s="Pages:onReload";d(s),V(!0),W(!1);try{B.loading({key:s,content:A({en:"Loading invited pages...",vi:"Đang tải trang được mời..."}),duration:0});const o=await k(),l=null==o?void 0:o.filter((e=>!a.has(e.id)));(null==l?void 0:l.length)&&(t.push(...l),$([...t]));const r=null==t?void 0:t.filter((e=>e.userType!==T.INVITED)).pop();let d=e?"":null==r?void 0:r.cursor;for(;!n.value();){B.loading({key:s,content:A({en:"Loading pages...",vi:"Đang tải trang..."})+t.length,duration:0});const e=await b(d),n=null==e?void 0:e.filter((e=>!a.has(e.id)));if(e.forEach((e=>a.add(e.id))),!(null==n?void 0:n.length))break;t.push(...n),d=null==(i=null==t?void 0:t[(null==t?void 0:t.length)-1])?void 0:i.cursor,$([...t])}B.success({key:s,content:(n.value()?A({en:"Load pages stopped ",vi:"Dừng tải trang "}):A({en:"Load pages completed ",vi:"Tải xong trang "}))+t.length}),W(!n.value())}catch(o){B.error({content:A({en:"Failed to load pages: ",vi:"Lỗi tải trang: "})+o.message})}finally{V(!1)}},G=(e,i=!1)=>_({record:e,id:e.id,key:"Pages:onClickUnlikePage",actionFn:()=>S?x(1e3):D(e.id),loadingText:()=>A({en:"Unliking page... ",vi:"Đang bỏ thích trang..."})+e.name,successText:()=>A({en:"Unliked page ",vi:"Đã bỏ thích trang "})+e.name,failedText:()=>A({en:"Failed to unlike page ",vi:"Lỗi bỏ thích trang "})+e.name,onSuccess:()=>{$((i=>a(i,(i=>{const n=i.findIndex((i=>i.id==e.id));return i[n].userType=T.UNLIKED,i}))))},needConfirm:i,confirmProps:{title:A({en:"Unlike ",vi:"Bỏ thích "})+e.name+"?"}}),J=(e,i=!1)=>_({record:e,id:e.id,key:"Pages:onClickUnfollowPage",actionFn:()=>S?x(1e3):I(e.id),loadingText:()=>A({en:"Unfollowing page... ",vi:"Đang bỏ theo dõi trang..."})+e.name,successText:()=>A({en:"Unfollowed page ",vi:"Đã bỏ theo dõi trang "})+e.name,failedText:()=>A({en:"Failed to unfollow page",vi:"Lỗi bỏ theo dõi trang"}),onSuccess:()=>{$((i=>a(i,(i=>{const n=i.findIndex((i=>i.id==e.id));return i[n].userType=T.UNFOLLOWED,i}))))},needConfirm:i,confirmProps:{title:A({en:"Unfollow ",vi:"Bỏ theo dõi "})+e.name+"?"}}),Q=async(e,i=!1)=>_({record:e,id:e.id,key:"Pages:onClickDeclineInvite",actionFn:()=>S?x(1e3):w(e.inviteId),loadingText:()=>A({en:"Declining invite... ",vi:"Đang từ chối lời mời..."})+e.name,successText:()=>A({en:"Declined invite ",vi:"Đã từ chối lời mời "})+e.name,failedText:()=>A({en:"Failed to remove invite",vi:"Lỗi từ chối lời mời"}),onSuccess:()=>{$((i=>a(i,(i=>{const n=i.findIndex((i=>i.id==e.id));return i[n].userType=T.DECLINED,i}))))},needConfirm:i,confirmProps:{title:A({en:"Decline ",vi:"Từ chối lời mời "})+e.name+"?"}}),X=e=>{switch(e){case T.ADMIN:return{color:"success",text:A({en:"Admin",vi:"Admin"})};case T.LIKED:return{color:"default",text:A({en:"Liked",vi:"Đã thích"})};case T.INVITED:return{color:"warning",text:A({en:"Invited",vi:"Lời mời"})};case T.DECLINED:return{color:"error",text:A({en:"Declined",vi:"Từ chối"})};case T.UNLIKED:return{color:"error",text:A({en:"Unliked",vi:"Bỏ thích"})};case T.UNFOLLOWED:return{color:"error",text:A({en:"Unfollowed",vi:"Bỏ theo dõi"})};default:return{color:"default",text:"?"}}},Z=i.useMemo((()=>Array.from(z.map((e=>e.category)).reduce(((e,i)=>{let n=e.get(i)||0;return e.set(i,++n)}),new Map).entries()).sort(((e,i)=>i[1]-e[1])).map((([e,i])=>({text:e+" ("+i+")",value:e})))),[z]),ee=[{title:"#",key:"index",dataIndex:"index",sorter:(e,i)=>e.index-i.index,width:60},{title:A({en:"Image",vi:"Ảnh"}),key:"image",dataIndex:"image",render:(e,i,t)=>n.jsx(E,{src:i.image,width:50,height:50,style:{objectFit:"contain",borderRadius:5}})},{title:A({en:"Page",vi:"Trang"}),key:"name",dataIndex:"name",render:(e,i,t)=>n.jsxs(c,{direction:"vertical",size:0,style:{maxWidth:300},children:[n.jsx(m.Link,{href:i.url,target:"_blank",children:n.jsx("b",{children:i.name||"???"})}),n.jsx("span",{style:{opacity:.5},children:i.id})]}),sorter:(e,i)=>e.name.localeCompare(i.name)},{title:A({en:"Category",vi:"Danh mục"})+" ("+Z.length+")",key:"category",dataIndex:"category",filters:Z,onFilter:(e,i)=>i.category==e,filterSearch:!0},{title:A({en:"Type",vi:"Loại"}),key:"type",dataIndex:"userType",render:(e,i,t)=>{const{color:a,text:s}=X(i.userType);return i.inviterId?n.jsxs(c,{children:[n.jsx(g,{color:a,children:s}),n.jsx(E,{src:i.inviterAvatar,width:40,height:40,style:{borderRadius:5}}),n.jsxs(c,{direction:"vertical",size:0,children:[n.jsx(m.Link,{href:h(i.inviterId),target:"_blank",children:n.jsx("b",{children:i.inviterName})}),n.jsx("span",{style:{opacity:.5},children:i.inviterId})]})]}):n.jsx(g,{color:a,children:s})},filters:Object.values(T).map((e=>{const{text:i}=X(e);return{text:i+" ("+z.filter((i=>i.userType==e)).length+")",value:e}})),onFilter:(e,i)=>i.userType==e,align:"left"},{title:A({en:"Action",vi:"Hành động"}),key:"action",render:(e,i,a)=>n.jsxs(c.Compact,{style:{minWidth:30},children:[i.userType===T.LIKED||i.userType===T.ADMIN?n.jsxs(n.Fragment,{children:[i.canLike?n.jsx(p,{title:A({en:"Unlike page",vi:"Bỏ thích"}),children:n.jsx(t,{onClick:()=>G(i,!0),icon:n.jsx("i",{className:"fa-solid fa-thumbs-down"})})}):null,n.jsx(p,{title:A({en:"Unfollow page",vi:"Bỏ theo dõi"}),children:n.jsx(t,{onClick:()=>J(i,!0),icon:n.jsx("i",{className:"fa-solid fa-eye-slash"})})})]}):null,i.userType===T.INVITED?n.jsx(p,{title:A({en:"Decline invite",vi:"Từ chối"}),children:n.jsx(t,{danger:!0,onClick:()=>Q(i,!0),icon:n.jsx("i",{className:"fa-solid fa-xmark"})})}):null,n.jsx(p,{title:A({en:"Bulk Downloader",vi:"Tải hàng loạt"}),children:n.jsx(t,{type:"default",onClick:()=>(e=>{M("/bulk-downloader",{state:{targetId:e.id,platform:s.Facebook}})})(i),icon:n.jsx("i",{className:"fa-solid fa-download"})})})]}),width:100,align:"right"}];return n.jsx(C,{title:A({en:"Pages manager",vi:"Quản lý trang"}),titleSuffix:n.jsx(g,{style:{marginLeft:"10px",fontWeight:"bold",color:"#888"},children:z.length}),children:n.jsx(N,{data:z,columns:ee.map(((e,i)=>({...e,index:i}))),renderTitle:e=>{var i,a;const s=(null==e?void 0:e.length)?[...e]:[...z],l=(null==(i=null==s?void 0:s.map((e=>z.find((i=>i.id===e.id)))))?void 0:i.filter((e=>(null==e?void 0:e.userType)===T.LIKED||(null==e?void 0:e.userType)===T.ADMIN)))||[],r=(null==l?void 0:l.filter((e=>null==e?void 0:e.canLike)))||[],c=(null==(a=null==s?void 0:s.map((e=>z.find((i=>i.id===e.id)))))?void 0:a.filter((e=>(null==e?void 0:e.userType)===T.INVITED)))||[];return n.jsxs(n.Fragment,{children:[n.jsx(p,{color:"red",title:A({en:"WARNING: Reload many times may cause temporarily blocked by facebook",vi:"LƯU Ý: Tải lại nhiều lần có thể bị facebook chặn tạm thời"}),children:n.jsx(t,{type:"primary",icon:K?n.jsx("i",{className:"fa-solid fa-rotate-right fa-spin"}):n.jsx("i",{className:"fa-solid fa-rotate-right"}),onClick:()=>q(!0),children:A({en:"Reload",vi:"Tải lại"})})}),n.jsx(y,{data:s,options:[{key:"id",label:".txt (page id)",prepareData:e=>({fileName:"your_pages_id"+o().format("YYYY-MM-DD-HHmmss")+".txt",data:null==e?void 0:e.map((e=>e.id)).join("\n")})},{key:"id_name",label:".csv (id+name)",prepareData:e=>({fileName:"my_liked_pages_id_name.csv",data:v(e.map((e=>({uid:e.id,name:e.name}))))})},{key:"json",label:".json",prepareData:e=>({fileName:"your_pages"+o().format("YYYY-MM-DD-HHmmss")+".json",data:JSON.stringify(e,null,4)})},{key:"csv",label:".csv",prepareData:e=>({fileName:"your_pages"+o().format("YYYY-MM-DD-HHmmss")+".csv",data:v(e)})}]}),n.jsx(p,{title:A({en:`Unlike ${r.length} selected pages`,vi:`Bỏ thích ${r.length} trang được chọn`}),children:n.jsx(t,{danger:!0,disabled:r.length<=0,icon:n.jsx("i",{className:"fa-solid fa-thumbs-down"}),onClick:()=>(async e=>Y({data:e,key:"Pages:onClickUnlikeSelectedPages",actionFn:G,loadingText:()=>A({en:"Unliking pages... ",vi:"Đang bỏ thích trang... "}),successText:()=>A({en:"Unliked pages done: ",vi:"Bỏ thích trang xong: "}),successDescItem:e=>e.name,confirmProps:{title:A({en:"Unlike "+e.length+" pages?",vi:"Bỏ thích "+e.length+" trang?"})}}))(r),children:r.length})}),n.jsx(p,{title:A({en:`Unfollow ${l.length} selected pages`,vi:`Bỏ theo dõi ${l.length} trang được chọn`}),children:n.jsx(t,{danger:!0,disabled:l.length<=0,icon:n.jsx("i",{className:"fa-solid fa-eye-slash"}),onClick:()=>(async e=>Y({data:e,key:"Pages:onClickUnfollowSelectedPages",actionFn:J,loadingText:()=>A({en:"Unfollowing pages... ",vi:"Đang bỏ theo dõi trang... "}),successText:()=>A({en:"Unfollowed pages done: ",vi:"Bỏ theo dõi trang xong: "}),successDescItem:e=>e.name,confirmProps:{title:A({en:"Unfollow "+e.length+" pages?",vi:"Bỏ theo dõi "+e.length+" trang?"})}}))(l),children:l.length})}),c.length>0&&n.jsx(p,{title:A({en:`Decline ${c.length} selected invites`,vi:`Từ chối ${c.length} lời mời được chọn`}),children:n.jsx(F,{title:A({en:`Decline ${c.length} invites`,vi:`Từ chối ${c.length} lời mời`}),description:A({en:`Are your sure want to decline ${c.length} invites?`,vi:`Bản có chãc muốn từ chối ${c.length} lời mời?`}),onConfirm:()=>(async e=>Y({data:e,key:"Pages:onClickDeclineSelectedInvites",actionFn:Q,loadingText:()=>A({en:"Removing invite... ",vi:"Đang từ chối lời mời... "}),successText:()=>A({en:"Declined invites done: ",vi:"Từ chối lời mời xong: "}),successDescItem:e=>e.name,confirmProps:{title:A({en:"Remove invite "+e.length+" pages?",vi:"Từ chối lời mời "+e.length+" trang?"})}}))(c),okText:A({en:"Yes",vi:"Có"}),cancelText:A({en:"No",vi:"Huỷ"}),children:n.jsx(t,{danger:!0,disabled:c.length<=0,icon:n.jsx("i",{className:"fa-solid fa-xmark"}),children:c.length})})}),n.jsx(L,{name:"Pages",text:z.map((e=>e.name)).join(" ")}),n.jsx(p,{title:A({en:"Manage on Facebook",vi:"Quản lý trên Facebook"}),children:n.jsx(t,{onClick:()=>{d("Pages:openManageOnFacebook"),window.open("https://www.facebook.com/me/allactivity?category_key=LIKEDINTERESTS","_blank")},icon:n.jsx("i",{className:"fa-solid fa-up-right-from-square"})})})]})},size:"small",searchable:!0,selectable:!0,onSearchRow:(e,i)=>u(e,(null==i?void 0:i.name)+(null==i?void 0:i.id)),keyExtractor:e=>(null==e?void 0:e.id)+(null==e?void 0:e.userType)})})}export{S as default};
