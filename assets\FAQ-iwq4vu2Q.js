const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./Disclaimer-D85ZEEs0.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./MyApp-DW5WH4Ub.js","./Screen-Buq7HJpK.js","./col-CzA09p0P.js","./row-BMfM-of4.js","./useBreakpoint-CPcdMRfj.js"])))=>i.map(i=>d[i]);
import{b0 as n,r as e,aP as t,bf as i,b1 as o,bg as r,b5 as c,aN as s}from"./index-Cak6rALw.js";import{u as a,t as h,c as l,b as d,g as u,S as g,C as x}from"./MyApp-DW5WH4Ub.js";import{S as m}from"./Screen-Buq7HJpK.js";import{R as j}from"./row-BMfM-of4.js";import{T as b}from"./index-BzMPigdO.js";import"./col-CzA09p0P.js";import"./useBreakpoint-CPcdMRfj.js";import"./DownOutlined-B-JcS-29.js";function p({text:e}){return"string"==typeof e?e.split("*").map(((e,t)=>t%2==0?e:n.jsx("b",{children:e}))):e}const k=o((()=>s((()=>import("./Disclaimer-D85ZEEs0.js")),__vite__mapDeps([0,1,2,3,4,5,6,7]),import.meta.url)),{fallback:x});function f(){const{ti:o}=a();e.useEffect((()=>{h("FAQ:onLoad")}),[]);const s=[{label:o({en:"❓ What is FB AIO?",vi:"❓ FB AIO là gì?"}),children:o({vi:"Viết tắt của Facebook All-in-one. Bộ công cụ tất cả trong một dành cho Meta platform (Facebook, Instagram, Threads), Tiktok. Bao gồm quản lý, tự động hoá, tải hàng loạt, tiện ích, ...",en:"A short description of Facebook All-in-one. Toolbox for Meta platform (Facebook, Instagram, Threads), TikTok. Includes management, automation, bulk download, utilities, ..."})},{label:o({en:"🌟 Is FB AIO Free?",vi:"🌟 FB AIO có miễn phí không?"}),children:o({vi:n.jsxs(l.Text,{children:["Có, bạn có thể tải miễn phí từ"," ",n.jsx("a",{href:t.FB_AIO.webstore,target:"_blank",children:"Chrome Store"})," ","với nhiều tính năng hữu ích. Một số"," ",n.jsx("a",{href:"#/vip",children:"Tính năng nâng cao (VIP)"})," cần trả phí."]}),en:n.jsxs(l.Text,{children:["Yes, you can download it for free from the"," ",n.jsx("a",{href:t.FB_AIO.webstore,target:"_blank",children:"Chrome Store"})," ","with many useful features. Some ",n.jsx("a",{href:"#/vip",children:"Advanced features (VIP)"})," ","require payment."]})})},{label:o({en:"🔥 What benefits does VIP offer?",vi:"🔥 VIP có đặc quyền gì?"}),children:o({vi:n.jsx("ol",{children:[n.jsxs(n.Fragment,{children:["🚀 ",n.jsx("b",{children:"Công cụ nâng cao"}),": Truy cập các"," ",n.jsx("a",{href:"#/vip",children:"tính năng VIP"})," không có trong bản miễn phí."]}),"🎯 *Hỗ trợ ưu tiên*: Được hỗ trợ nhanh chóng hơn cho mọi vấn đề hoặc thắc mắc.","⭐ *Yêu cầu chức năng*: Được ưu tiên hơn khi bạn yêu cầu làm thêm chức năng."].map(((e,t)=>n.jsx("li",{children:n.jsx(p,{text:e})},t)))}),en:n.jsx("ol",{children:[n.jsxs(n.Fragment,{children:["🚀 ",n.jsx("b",{children:"Advanced Tools"}),": Access ",n.jsx("a",{href:"#/vip",children:"VIP features"})," ","that are not available in the free version."]}),"🎯 *Priority Support*: Receive faster assistance for any issues or questions.","⭐ *Feature Requests*: Enjoy higher priority when requesting additional features."].map(((e,t)=>n.jsx("li",{children:n.jsx(p,{text:e})},t)))})})},{label:o({en:"💎 How to unlock VIP?",vi:"💎 Cách mở khoá VIP?"}),children:o({vi:n.jsx("ul",{children:[n.jsxs(n.Fragment,{children:["🛒 ",n.jsx("b",{children:"Mua hoặc Dùng thử Miễn phí"}),": Bạn có thể mua VIP hoặc dùng thử miễn phí. ",n.jsx("a",{href:"#/vip",children:"👉 Tại đây"})]}),"🆔 *Kích hoạt qua Facebook UID*: VIP được liên kết với UID (mã định danh) Facebook của bạn.","🔑 *Truy cập VIP*: Chỉ cần đăng nhập bằng tài khoản Facebook đúng, VIP sẽ tự động kích hoạt."].map(((e,t)=>n.jsx("li",{children:n.jsx(p,{text:e})},t)))}),en:n.jsx("ul",{children:[n.jsxs(n.Fragment,{children:["🛒 ",n.jsx("b",{children:"Buy or Try for Free"}),": You can purchase VIP or try it for free. ",n.jsx("a",{href:"#/vip",children:"👉 Click here"})]}),"🆔 *Activation via Facebook UID*: VIP is linked to your Facebook UID.","🔑 *Access VIP*: Simply log in with the correct Facebook account, and VIP will activate automatically."].map(((e,t)=>n.jsx("li",{children:n.jsx(p,{text:e})},t)))})})},{label:o({en:"👥 VIP for multiple accounts?",vi:"👥 Mua VIP cho nhiều nick?"}),children:o({vi:n.jsxs("ul",{children:[n.jsx("li",{children:"💵 Sẽ được giảm giá nếu bạn mua VIP cho nhiều nick facebook 1 lúc"}),n.jsxs("li",{children:["✅ Cứ thêm 1 nick sẽ được ",n.jsx(d,{color:"success",children:" giảm thêm 10%"})," cho nick đó, tối đa -50% /tháng"]}),n.jsxs("li",{children:["💬 Liên hệ ",n.jsx("a",{href:t.me.url,children:"Admin"})," để mua nhiều nick với giá ưu đãi"]}),n.jsx("li",{children:n.jsx(y,{price:i.vi,postfix:"VND/tháng"})})]}),en:n.jsxs("ul",{children:[n.jsx("li",{children:"💵 Will have discount when you buy VIP for multiple fb accounts."}),n.jsxs("li",{children:["✅ For each additional account, you'll receive an"," ",n.jsx(d,{color:"success",children:" extra 10% discount"})," for that account, up to a maximum of 50% off (per month)"]}),n.jsxs("li",{children:["💬 Contact ",n.jsx("a",{href:t.me.url,children:"Admin"})," to buy multiple accounts with discount price"]}),n.jsx("li",{children:n.jsx(y,{price:i.en,postfix:"USD/month",fixed:1})})]})})},{label:o({en:"✅ Buy VIP lifetime?",vi:"✅ Mua VIP vĩnh viễn?"}),children:o({vi:n.jsx("ul",{children:["💰 *Giá không cố định*: Giá VIP vĩnh viễn tăng hằng ngày do các tính năng mới liên tục được phát triển và bổ sung.","📈 *Nên mua sớm*: Mua sớm sẽ tiết kiệm hơn so với giá trong tương lai.","🚀 *Hỗ trợ đổi nick*: Khi nick cũ của bạn bị khoá, bị mất, bị hack,.. liên hệ admin để chuyển VIP sang nick mới",n.jsxs(n.Fragment,{children:["🔖 ",n.jsx("b",{children:"Giá vĩnh viễn hiện tại"}),": Chỉ"," ",n.jsxs(d,{color:"success",children:[u(149e4,".")," VNĐ"]}),"bảo hành trọn đời."]}),n.jsxs(n.Fragment,{children:["Liên hệ"," ",n.jsx("a",{href:t.me.url,target:"_blank",children:"admin"})," ","để mua, hoặc mua tại ",n.jsx("a",{href:"#/checkout",children:"trang mua hàng"})]})].map(((e,t)=>n.jsx("li",{children:n.jsx(p,{text:e})},t)))}),en:n.jsx("ul",{children:["💰 *Price is not fixed*: The lifetime VIP price increases daily as new features are continuously developed and added.","📈 *Buy early*: Purchasing early will save you money compared to future prices.","🚀 *Account transfer support*: If your old account is locked, lost, or hacked, contact the admin to transfer VIP to a new account.",n.jsxs(n.Fragment,{children:["🔖 ",n.jsx("b",{children:"Current Lifetime price"}),": Only"," ",n.jsx(d,{color:"success",children:"60 USD"}),"lifetime warranty."]}),n.jsxs(n.Fragment,{children:["Contact"," ",n.jsx("a",{href:t.me.url,target:"_blank",children:"admin"})," ","to buy, or buy at ",n.jsx("a",{href:"#/checkout",children:"checkout page"})]})].map(((e,t)=>n.jsx("li",{children:n.jsx(p,{text:e})},t)))})})},{label:o({en:"👀 Is my privacy protected?",vi:"👀 Quyền riêng tư của tôi có được bảo đảm?"}),children:o({vi:n.jsxs(g,{direction:"vertical",children:["Có, quyền riêng tư của bạn là ưu tiên hàng đầu.",n.jsx("ol",{children:["🔒 Tất cả dữ liệu được lưu trữ hoàn toàn trên *máy tính của bạn*.","🚫 Chúng tôi không gửi bất kỳ *dữ liệu nhạy cảm* (token, cookie, ...) nào của bạn lên internet.","🆔 Chỉ sử dụng *UID Facebook của bạn* (không phải dữ liệu nhạy cảm) để lưu trạng thái VIP trên máy chủ của chúng tôi.","📡 Tất cả chức năng hoạt động bằng cách gửi API *trực tiếp tới Facebook*, không qua bất kỳ máy chủ trung gian nào."].map(((e,t)=>n.jsx("li",{children:n.jsx(p,{text:e})},t)))})]}),en:n.jsxs(l.Text,{children:["Yes, your privacy is our top priority.",n.jsx("ol",{children:["🔒 All data is stored entirely on *your personal computer*","🚫 We do not send any of your *sensitive data* (token, cookie, ...) to internet.","🆔 Only *your Facebook UID* (not sensitive data) is used to save VIP status on our server.","📡 All features work by sending API requests *directly to Facebook*, without involving any intermediary servers."].map(((e,t)=>n.jsx("li",{children:n.jsx(p,{text:e})},t)))})]})})},{label:o({en:"🤝 Transparency Commitment",vi:"🤝 Cam kết Minh bạch"}),children:n.jsx(k,{})}],x=[{label:o({en:"👤 You are not logged in to Facebook",vi:"👤 Bạn chưa đăng nhập Facebook"}),children:o({vi:n.jsx("ul",{children:["❓ *Vấn đề*: Đa số chức năng liên quan tới Facebook, đều cần bạn đăng nhập Facebook trước, để có thể sử dụng.","✅ *Xử lý*: Mở tab mới và đăng nhập Facebook, rồi tải lại trang web FB AIO.","🤔 *Cách hoạt động*: FB AIO sẽ tự động kiểm tra trạng thái đăng nhập (uid của bạn), và hiển thị thông tin tài khoản tương ứng.","👉 *Cách đăng xuất*: Chỉ cần đăng xuất khỏi Facebook, tài khoản trong FB AIO cũng sẽ tự động đăng xuất theo."].map(((e,t)=>n.jsx("li",{children:n.jsx(p,{text:e})},e)))}),en:n.jsx(g,{children:n.jsx("ul",{children:["❓ *Issue*: Most features related to Facebook require you to log in to your Facebook account first in order to use them.","✅ *Solution*: Open a new tab and log in to Facebook, then reload the FB AIO webpage.","🤔 *How it works*: FB AIO will automatically check your login status (your UID) and display the corresponding account information.","👉 *How to log out*: Simply log out of Facebook, and the account in FB AIO will also be automatically logged out."].map(((e,t)=>n.jsx("li",{children:n.jsx(p,{text:e})},e)))})})})},{label:o({en:"💎 My VIP expired unexpectedly?",vi:"💎 VIP của tôi đã hết hạn đột ngột?"}),children:o({vi:n.jsx("ul",{children:["❓ *Vấn đề*: Dù bạn vẫn còn VIP, nhưng hệ thống lại hiển thị là đã hết hạn.","🤔 *Nguyên nhân*: 🔌 Lỗi kết nối giữa trình duyệt và máy chủ, 🧩 Xung đột với tiện ích (extension) lạ ⚙️ Lỗi tạm thời từ hệ thống.","✅ *Cách xử lý nhanh*: 🔄 Tải lại trang web, 🌐 Kiểm tra kết nối mạng, 🧼 Tắt các tiện ích trình duyệt không rõ nguồn gốc, 🧭 Thử mở bằng trình duyệt khác"].map(((e,t)=>n.jsx("li",{children:n.jsx(p,{text:e})},e)))}),en:n.jsx(g,{children:n.jsx("ul",{children:["❓ *Issue*: You sure still have VIP, but the system says it’s expired.","🤔 *Possible reasons*: 🔌 Browser couldn't connect to the server 🧩 Conflicts with unknown browser extensions ⚙️ Temporary server/system glitch","✅ How to fix: 🔄 Refresh the page 🌐 Check your internet connection 🧼 Disable suspicious browser extensions 🧭 Try a different browser"].map(((e,t)=>n.jsx("li",{children:n.jsx(p,{text:e})},e)))})})})},{label:o({en:"🚫 Facebook response Error: Limit exceeded?",vi:"🚫 Facebook response Error: Đạt giới hạn (limit)?"}),children:o({vi:n.jsx("ul",{children:["❓ *Lý do*: Bạn có thể đã sử dụng một tính năng quá nhanh hoặc quá nhiều trong thời gian ngắn. Khiến Facebook nhận diện hoạt động bất thường và áp dụng chặn tạm thời.","🚧 *Ảnh hưởng*: Bạn sẽ không thể sử dụng tính năng bị chặn, cả trên FB AIO lẫn trực tiếp trên Facebook.","🛠️ *Cách xử lý*: Hãy dừng sử dụng tính năng đó trong khoảng 2-3 ngày, Facebook sẽ tự động gỡ chặn.","💡 *Lưu ý*: Hạn chế thao tác quá nhanh hoặc lặp lại nhiều lần để tránh bị chặn lần sau.","✅ *Tin tốt*: Trong thời gian đó bạn vẫn có thể sử dụng các chức năng khác."].map(((e,t)=>n.jsx("li",{children:n.jsx(p,{text:e})},e)))}),en:n.jsx("ul",{children:["❓ *Reason*: You may have used a feature too quickly or too frequently within a short period, causing Facebook to detect unusual activity and temporarily block it.","🚧 *Impact*: You will not be able to use the blocked feature, either on FB AIO or directly on Facebook.","🛠️ *Solution*: Avoid using the feature for about 2-3 days, and Facebook will automatically lift the block.","💡 *Note*: Try to avoid excessive or repetitive actions to prevent being blocked in the future.","✅ *Good News*: During this time, you can still use other features without any restrictions."].map(((e,t)=>n.jsx("li",{children:n.jsx(p,{text:e})},e)))})})},{label:o({en:"🐞 What should I do when an error occur?",vi:"🐞 Tôi phải làm gì khi gặp Lỗi?"}),children:o({vi:n.jsxs(l.Text,{children:["Gửi thông báo lỗi tới một trong các kênh sau để được hỗ trợ:",n.jsxs("ul",{children:[n.jsxs("li",{children:["💬 Bình luận / gửi tin nhắn tới"," ",n.jsx("a",{href:t.GroupFB,target:"_blank",children:"Group Facebook"})," "]}),n.jsxs("li",{children:["📨 Gửi tin nhắn tới"," ",n.jsx("a",{href:t.FB_AIO.featureRequest,target:"_blank",children:"Telegram"})," ","hoặc"," ",n.jsx("a",{href:t.me.url,target:"_blank",children:"admin"})]})]})]}),en:n.jsxs(l.Text,{children:["Please report the error to one of the following channels to get support:",n.jsxs("ul",{children:[n.jsxs("li",{children:["💬 Comment / send message to"," ",n.jsx("a",{href:t.GroupFB,target:"_blank",children:"Group Facebook"})," "]}),n.jsxs("li",{children:["📨 Send message to"," ",n.jsx("a",{href:t.FB_AIO.featureRequest,target:"_blank",children:"Telegram"})," ","or"," ",n.jsx("a",{href:t.me.url,target:"_blank",children:"admin"})]})]})]})})}];return n.jsxs(m,{title:o({en:"FAQs: Frequently asked questions 🤔",vi:"FAQs: Câu hỏi thường gặp 🤔"}),mode:"center",children:[n.jsx(r,{items:s,accordion:!0,style:{width:600,maxWidth:"90vw"},expandIconPosition:"end"}),n.jsx(l.Title,{level:3,children:o({en:"Bugs & Issues",vi:"Lỗi & Vấn đề"})}),n.jsx(r,{items:x,accordion:!0,style:{width:600,maxWidth:"90vw"},expandIconPosition:"end"}),n.jsx(c,{href:"#/support",icon:n.jsx("i",{className:"fa-solid fa-headset"}),style:{marginTop:20},children:o({en:"Contact for help",vi:"Liên hệ hỗ trợ"})})]})}function y({price:t=65e3,postfix:i="VND/tháng",fixed:o=0}){const{ti:r}=a(),[s,h]=e.useState(1),x=e.useMemo((()=>{let n=0;for(let e=0;e<s;e++)n+=t*Math.max(1-.1*e,.5);return n}),[s,t]),m=t*s;return n.jsxs(j,{align:"middle",justify:"start",children:[n.jsx(l.Text,{children:r({vi:"Tính thử",en:"Estimate"})}),n.jsxs(g.Compact,{style:{marginLeft:5,marginRight:5},children:[n.jsx(c,{onClick:()=>h(s-1),disabled:s<=1,icon:n.jsx("i",{className:"fa-solid fa-minus"})}),n.jsx(b,{min:1,max:1e3,placeholder:"Count",value:s,onChange:n=>h(n),style:{width:60}}),n.jsx(c,{onClick:()=>h(s+1),disabled:s>=1e3,icon:n.jsx("i",{className:"fa-solid fa-plus"})})]}),n.jsxs(l.Text,{style:{marginRight:5},children:[r({en:"accounts",vi:"nick"})," ="," ",u(x.toFixed(o))," ",i]}),x!==m&&n.jsxs(n.Fragment,{children:[n.jsx(l.Text,{type:"secondary",children:n.jsxs("s",{children:["(",u(m.toFixed(o)),")"]})}),n.jsx(d,{color:"success",children:"-"+(100-x/m*100).toFixed(0)+"%"})]})]})}export{f as default};
