/**
 * Global VIP Utilities
 * Expose VIP functions to window object for easy console access
 */

import { checkVipStatus, clearVipCache, getCacheStats, getServerUrl } from './vipChecker.js';
import { getAllVipUsers, addVipUser, isDevelopmentMode, clearPasswordCache, getCacheInfo } from './adminBypass.js';
import { useVIPEnhanced } from './useVIPEnhanced.js';

/**
 * Initialize global VIP utilities
 */
function initGlobalVIPUtils() {
    // Tạo namespace cho VIP utilities
    if (typeof window !== 'undefined') {
        window.VIPUtils = {
            // VIP Checker functions
            check: checkVipStatus,
            clearCache: clearVipCache,
            getCacheStats: getCacheStats,
            getServerUrl: getServerUrl,
            
            // Admin Bypass functions (chỉ hoạt động trong dev mode)
            admin: {
                getAllUsers: getAllVipUsers,
                addUser: addVipUser,
                clearPasswordCache: clearPasswordCache,
                getCacheInfo: getCacheInfo,
                isDev: isDevelopmentMode
            },
            
            // Enhanced hook
            useEnhanced: useVIPEnhanced,
            
            // Quick utilities
            quick: {
                // Quick check VIP cho UID
                check: async (uid) => {
                    console.log(`🔍 Checking VIP for UID: ${uid}`);
                    const result = await checkVipStatus(uid);
                    console.log('✅ Result:', result);
                    return result;
                },
                
                // Quick add VIP (dev only)
                add: async (uid, name, days = 30) => {
                    if (!isDevelopmentMode()) {
                        console.log('❌ Quick add chỉ khả dụng trong development mode');
                        return false;
                    }
                    
                    const expireTime = Date.now() + (days * 24 * 60 * 60 * 1000);
                    console.log(`➕ Adding VIP for ${uid} (${name}) - ${days} days`);
                    
                    const result = await addVipUser(uid, name, expireTime);
                    console.log(result ? '✅ Success' : '❌ Failed');
                    return result;
                },
                
                // Quick get all VIPs (dev only)
                list: async () => {
                    if (!isDevelopmentMode()) {
                        console.log('❌ Quick list chỉ khả dụng trong development mode');
                        return null;
                    }
                    
                    console.log('📋 Getting all VIP users...');
                    const result = await getAllVipUsers();
                    
                    if (result) {
                        console.table(result.map(user => ({
                            UID: user.uid,
                            Name: user.name,
                            'Days Left': user.daysLeft,
                            Status: user.isActive ? '✅ Active' : '❌ Expired',
                            'Expire Date': user.expireDate
                        })));
                    }
                    
                    return result;
                }
            },
            
            // Helper functions
            helpers: {
                // Format timestamp
                formatTime: (timestamp) => {
                    return new Date(timestamp).toLocaleString('vi-VN');
                },
                
                // Calculate days from now
                daysFromNow: (days) => {
                    return Date.now() + (days * 24 * 60 * 60 * 1000);
                },
                
                // Calculate days until timestamp
                daysUntil: (timestamp) => {
                    return Math.round((timestamp - Date.now()) / (1000 * 60 * 60 * 24));
                },
                
                // Validate UID format
                isValidUID: (uid) => {
                    return typeof uid === 'string' && uid.length > 0 && /^\d+$/.test(uid);
                }
            },
            
            // Debug functions
            debug: {
                // Show current configuration
                showConfig: () => {
                    console.log('🔧 VIP Utils Configuration:');
                    console.log('Server URL:', getServerUrl());
                    console.log('Development Mode:', isDevelopmentMode());
                    console.log('Cache Stats:', getCacheStats());
                    console.log('Admin Cache Info:', getCacheInfo());
                },
                
                // Test all functions
                testAll: async () => {
                    console.log('🧪 Testing VIP Utils...');
                    
                    // Test VIP check
                    console.log('1. Testing VIP check...');
                    const testUID = '100000000000000'; // Facebook test UID
                    const vipResult = await checkVipStatus(testUID);
                    console.log('VIP Check Result:', vipResult);
                    
                    // Test admin functions (if in dev mode)
                    if (isDevelopmentMode()) {
                        console.log('2. Testing admin functions...');
                        const allVips = await getAllVipUsers();
                        console.log('All VIPs count:', allVips ? allVips.length : 'Failed');
                    } else {
                        console.log('2. Skipping admin tests (not in dev mode)');
                    }
                    
                    console.log('✅ Test completed');
                }
            }
        };
        
        // Shortcut aliases
        window.vip = window.VIPUtils.quick;
        window.vipAdmin = window.VIPUtils.admin;
        window.vipDebug = window.VIPUtils.debug;
        
        console.log('🚀 VIP Utils initialized!');
        console.log('Available commands:');
        console.log('  vip.check(uid) - Quick VIP check');
        console.log('  vip.add(uid, name, days) - Quick add VIP (dev only)');
        console.log('  vip.list() - List all VIPs (dev only)');
        console.log('  vipDebug.showConfig() - Show configuration');
        console.log('  vipDebug.testAll() - Test all functions');
        console.log('  VIPUtils - Full API access');
        
        return window.VIPUtils;
    }
    
    return null;
}

// Auto-initialize if in browser environment
if (typeof window !== 'undefined') {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initGlobalVIPUtils);
    } else {
        initGlobalVIPUtils();
    }
}

export { initGlobalVIPUtils };
export default initGlobalVIPUtils;
