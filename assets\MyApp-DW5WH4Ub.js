const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./Redirector-BG53AR2f.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./index-B5QOFicW.js","./index-IxNfZAD-.js","./Dropdown-BcL-JHCf.js","./Disclaimer-D85ZEEs0.js","./Screen-Buq7HJpK.js","./col-CzA09p0P.js","./row-BMfM-of4.js","./useBreakpoint-CPcdMRfj.js","./FAQ-iwq4vu2Q.js","./index-BzMPigdO.js","./DownOutlined-B-JcS-29.js","./ExtUpdated-Czm1FRkm.js","./sweetalert2.esm.all-BZxvatOx.js","./Dashboard-DteWvCcQ.js","./index-CC5caqt_.js","./AllMessages-LP8i35DW.js","./ExportButton-CbyepAf_.js","./index-Dg3cFar0.js","./PurePanel-BvHjNOhQ.js","./move-ESLFEEsv.js","./useCacheState-CAnxkewm.js","./messages-wZDWdkVz.js","./useVIP-D5FAMGQQ.js","./getIds-DAzc-wzf.js","./index-Bg865k-U.js","./EyeOutlined-CNKPohhw.js","./SearchOutlined--mXbzQ68.js","./file-download-B-gszDlL.js","./dayjs.min-CzPn7FMI.js","./BadgeWrapper-BCbYXXfB.js","./MyTable-DnjDmN7z.js","./index-ByRdMNW-.js","./Table-BO80-bCE.js","./addEventListener-C4tIKh7N.js","./List-B6ZMXgC_.js","./index-D6X7PZwe.js","./index-CxAc8H5Q.js","./index-QU7lSj_a.js","./index-C5gzSBcY.js","./index-SRy1SMeO.js","./Pagination-2X6SDgot.js","./index-DdM4T8-Q.js","./index-PbLYNhdQ.js","./index-CDSnY0KE.js","./FirstMessages-kZCBEyr1.js","./index-DwLJmsHL.js","./ClockCircleOutlined-BVovxNRp.js","./index-jrOnBmdW.js","./AllFriends-ChasWEW6.js","./useForceStop-CvbJS7ei.js","./friends-CRlBxmhf.js","./gender-D7cqwEK5.js","./posts-Fn0UXLRN.js","./videos-D2LbKcXH.js","./WordStatisticButton-D2UxB2M4.js","./index-Bp2s1Wws.js","./index-Bx3o6rwA.js","./useAction-uz-MrZwJ.js","./UploadModal-iOZ41qZ5.js","./index-D3qvoGmV.js","./index-BVNdrzmU.js","./progress-ApOaEpgr.js","./useDevMode-yj0U7_8D.js","./index-DP_qolD8.js","./FriendRequests-DkKszU9E.js","./friendRequests-DyD2rqMz.js","./index-D-JvvpDI.js","./index-fI_muw4K.js","./index-C09Nl4fP.js","./index-CCwFJ0Qh.js","./index-c8qoPkv2.js","./groups-D1WZyVD8.js","./groupMembers-T0pfmfMg.js","./index-D8FV2cZb.js","./pages-DvqVBMHR.js","./VIP-BzUqNdLn.js","./features-BdMou7IV.js","./index-DAum7_7a.js","./follows-DFGgshWa.js","./Shortcuts-BLAfYAwQ.js","./AutoRun-C_QIEDH2.js","./adsLinkList-C2Suxade.js","./Tip-CTD3sRGu.js","./index-B5mnKw3Z.js","./index-GOnsP9v3.js","./reactions-CR2nCZPF.js","./index-D7dTP8lW.js","./InvisibleText-BbGrG0bl.js","./copy-D4Fd8-zB.js","./HackDuckRace-CmjLwOqc.js","./window-pPC-ycGO.js","./AccountsStore-etvUhsV6.js","./Checkout-DKoramAx.js","./Tutorial-DLivMi4O.js","./index-Bh0HZd6B.js","./AIOBuff-BnV-m9Hh.js","./index-CotK2-Bi.js","./index-BatTeoJ3.js","./Updates-BJWNVgkT.js","./Tinder-C_lpu73L.js","./shuffle-BAGnj1ei.js","./_copyArray-_bDJu6nu.js","./Collection-BYcChfHL.js","./react-hotkeys-hook.esm-wjq6pdfC.js","./UnlockFacebook-C_nosh3F.js","./index-PO01UXcZ.js","./HackWheelOfNames-BaccxXRs.js","./index-BwUwN3wG.js","./index-CiPeuwxC.js","./GetAccessToken-CISR6svj.js","./index-BSSD-6dh.js","./index-nF4hU1vs.js","./index-fhcER1I_.js","./Comparison-BUeFg8ZB.js","./UsefulScripts-GULpB0IR.js","./Support-Bkpt2tE5.js","./LanguagePicker-CeN5tnUe.js","./ProfileHeader-DNKCz-3I.js","./Notifications-BvIEU1Cy.js","./GlobalSearch-CgICNTjl.js","./index-6ffL1WDz.js","./index.es-CS4dvFDh.js","./NotFound-Df9pngtk.js","./disable-devtool.min-T97wR4cG.js","./MySwal-8JMBfvUJ.js"])))=>i.map(i=>d[i]);
import{r as e,_ as t,a as n,b as o,c as r,s as a,g as i,u as l,d as s,f as c,e as u,t as d,h as p,w as m,i as f,P as g,A as h,E as b,K as v,j as y,k as w,l as x,m as C,n as $,o as E,p as S,$ as O,q as k,C as _,v as I,x as j,y as R,z as B,B as N,D as T,F as P,G as A,V as M,H as z,I as D,J as L,L as H,M as F,N as W,O as V,Q as q,R as X,S as G,T as K,U as Y,W as U,X as Q,Y as Z,Z as J,a0 as ee,a1 as te,a2 as ne,a3 as oe,a4 as re,a5 as ae,a6 as ie,a7 as le,a8 as se,a9 as ce,aa as ue,ab as de,ac as pe,ad as me,ae as fe,af as ge,ag as he,ah as be,ai as ve,aj as ye,ak as we,al as xe,am as Ce,an as $e,ao as Ee,ap as Se,aq as Oe,ar as ke,as as _e,at as Ie,au as je,av as Re,aw as Be,ax as Ne,ay as Te,az as Pe,aA as Ae,aB as Me,aC as ze,aD as De,aE as Le,aF as He,aG as Fe,aH as We,aI as Ve,aJ as qe,aK as Xe,aL as Ge,aM as Ke,aN as Ye,aO as Ue,aP as Qe,aQ as Ze,aR as Je,aS as et,aT as tt,aU as nt,aV as ot,aW as rt,aX as at,aY as it,aZ as lt,a_ as st,a$ as ct,b0 as ut,b1 as dt,b2 as pt,b3 as mt,b4 as ft,b5 as gt,b6 as ht,b7 as bt,b8 as vt,b9 as yt,ba as wt,bb as xt}from"./index-Cak6rALw.js";var Ct=e.createContext(null);var $t=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var n=-1;return e.some((function(e,o){return e[0]===t&&(n=o,!0)})),n}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var n=e(this.__entries__,t),o=this.__entries__[n];return o&&o[1]},t.prototype.set=function(t,n){var o=e(this.__entries__,t);~o?this.__entries__[o][1]=n:this.__entries__.push([t,n])},t.prototype.delete=function(t){var n=this.__entries__,o=e(n,t);~o&&n.splice(o,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,o=this.__entries__;n<o.length;n++){var r=o[n];e.call(t,r[1],r[0])}},t}()}(),Et="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,St="undefined"!=typeof global&&global.Math===Math?global:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),Ot="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(St):function(e){return setTimeout((function(){return e(Date.now())}),1e3/60)};var kt=["top","right","bottom","left","width","height","size","weight"],_t="undefined"!=typeof MutationObserver,It=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(e,t){var n=!1,o=!1,r=0;function a(){n&&(n=!1,e()),o&&l()}function i(){Ot(a)}function l(){var e=Date.now();if(n){if(e-r<2)return;o=!0}else n=!0,o=!1,setTimeout(i,t);r=e}return l}(this.refresh.bind(this),20)}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,n=t.indexOf(e);~n&&t.splice(n,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter((function(e){return e.gatherActive(),e.hasActive()}));return e.forEach((function(e){return e.broadcastActive()})),e.length>0},e.prototype.connect_=function(){Et&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),_t?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){Et&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,n=void 0===t?"":t;kt.some((function(e){return!!~n.indexOf(e)}))&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),jt=function(e,t){for(var n=0,o=Object.keys(t);n<o.length;n++){var r=o[n];Object.defineProperty(e,r,{value:t[r],enumerable:!1,writable:!1,configurable:!0})}return e},Rt=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||St},Bt=zt(0,0,0,0);function Nt(e){return parseFloat(e)||0}function Tt(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce((function(t,n){return t+Nt(e["border-"+n+"-width"])}),0)}function Pt(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return Bt;var o=Rt(e).getComputedStyle(e),r=function(e){for(var t={},n=0,o=["top","right","bottom","left"];n<o.length;n++){var r=o[n],a=e["padding-"+r];t[r]=Nt(a)}return t}(o),a=r.left+r.right,i=r.top+r.bottom,l=Nt(o.width),s=Nt(o.height);if("border-box"===o.boxSizing&&(Math.round(l+a)!==t&&(l-=Tt(o,"left","right")+a),Math.round(s+i)!==n&&(s-=Tt(o,"top","bottom")+i)),!function(e){return e===Rt(e).document.documentElement}(e)){var c=Math.round(l+a)-t,u=Math.round(s+i)-n;1!==Math.abs(c)&&(l-=c),1!==Math.abs(u)&&(s-=u)}return zt(r.left,r.top,l,s)}var At="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof Rt(e).SVGGraphicsElement}:function(e){return e instanceof Rt(e).SVGElement&&"function"==typeof e.getBBox};function Mt(e){return Et?At(e)?function(e){var t=e.getBBox();return zt(0,0,t.width,t.height)}(e):Pt(e):Bt}function zt(e,t,n,o){return{x:e,y:t,width:n,height:o}}var Dt=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=zt(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=Mt(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),Lt=function(){return function(e,t){var n,o,r,a,i,l,s,c=(o=(n=t).x,r=n.y,a=n.width,i=n.height,l="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,s=Object.create(l.prototype),jt(s,{x:o,y:r,width:a,height:i,top:r,right:o+a,bottom:i+r,left:o}),s);jt(this,{target:e,contentRect:c})}}(),Ht=function(){function e(e,t,n){if(this.activeObservations_=[],this.observations_=new $t,"function"!=typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=n}return e.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof Rt(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new Dt(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof Rt(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach((function(t){t.isActive()&&e.activeObservations_.push(t)}))},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map((function(e){return new Lt(e.target,e.broadcastRect())}));this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),Ft="undefined"!=typeof WeakMap?new WeakMap:new $t,Wt=function(){return function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=It.getInstance(),o=new Ht(t,n,this);Ft.set(this,o)}}();["observe","unobserve","disconnect"].forEach((function(e){Wt.prototype[e]=function(){var t;return(t=Ft.get(this))[e].apply(t,arguments)}}));var Vt=void 0!==St.ResizeObserver?St.ResizeObserver:Wt,qt=new Map;var Xt=new Vt((function(e){e.forEach((function(e){var t,n=e.target;null===(t=qt.get(n))||void 0===t||t.forEach((function(e){return e(n)}))}))}));var Gt=function(e){t(i,e);var a=n(i);function i(){return o(this,i),a.apply(this,arguments)}return r(i,[{key:"render",value:function(){return this.props.children}}]),i}(e.Component);function Kt(t,n){var o=t.children,r=t.disabled,d=e.useRef(null),p=e.useRef(null),m=e.useContext(Ct),f="function"==typeof o,g=f?o(d):o,h=e.useRef({width:-1,height:-1,offsetWidth:-1,offsetHeight:-1}),b=!f&&e.isValidElement(g)&&a(g),v=b?i(g):null,y=l(v,d),w=function(){var e;return c(d.current)||(d.current&&"object"===u(d.current)?c(null===(e=d.current)||void 0===e?void 0:e.nativeElement):null)||c(p.current)};e.useImperativeHandle(n,(function(){return w()}));var x=e.useRef(t);x.current=t;var C=e.useCallback((function(e){var t=x.current,n=t.onResize,o=t.data,r=e.getBoundingClientRect(),a=r.width,i=r.height,l=e.offsetWidth,c=e.offsetHeight,u=Math.floor(a),d=Math.floor(i);if(h.current.width!==u||h.current.height!==d||h.current.offsetWidth!==l||h.current.offsetHeight!==c){var p={width:u,height:d,offsetWidth:l,offsetHeight:c};h.current=p;var f=l===Math.round(a)?a:l,g=c===Math.round(i)?i:c,b=s(s({},p),{},{offsetWidth:f,offsetHeight:g});null==m||m(b,e,o),n&&Promise.resolve().then((function(){n(b,e)}))}}),[]);return e.useEffect((function(){var e,t,n=w();return n&&!r&&(e=n,t=C,qt.has(e)||(qt.set(e,new Set),Xt.observe(e)),qt.get(e).add(t)),function(){return function(e,t){qt.has(e)&&(qt.get(e).delete(t),qt.get(e).size||(Xt.unobserve(e),qt.delete(e)))}(n,C)}}),[d.current,r]),e.createElement(Gt,{ref:p},b?e.cloneElement(g,{ref:y}):g)}var Yt=e.forwardRef(Kt);function Ut(t,n){var o=t.children;return("function"==typeof o?[o]:d(o)).map((function(o,r){var a=(null==o?void 0:o.key)||"".concat("rc-observer-key","-").concat(r);return e.createElement(Yt,p({},t,{key:a,ref:0===r?n:void 0}),o)}))}var Qt=e.forwardRef(Ut);function Zt(e){let t;const n=(...n)=>{null==t&&(t=m((n=>()=>{t=null,e.apply(void 0,f(n))})(n)))};return n.cancel=()=>{m.cancel(t),t=null},n}function Jt(e,t){return g.reduce(((n,o)=>{const r=e[`${o}1`],a=e[`${o}3`],i=e[`${o}6`],l=e[`${o}7`];return Object.assign(Object.assign({},n),t(o,{lightColor:r,lightBorderColor:a,darkColor:i,textColor:l}))}),{})}Qt.Collection=function(t){var n=t.children,o=t.onBatchResize,r=e.useRef(0),a=e.useRef([]),i=e.useContext(Ct),l=e.useCallback((function(e,t,n){r.current+=1;var l=r.current;a.current.push({size:e,element:t,data:n}),Promise.resolve().then((function(){l===r.current&&(null==o||o(a.current),a.current=[])})),null==i||i(e,t,n)}),[o,i]);return e.createElement(Ct.Provider,{value:l},n)};const en=h;function tn(e){return null!=e&&e===e.window}en.ErrorBoundary=b;const nn=e=>{var t,n;if("undefined"==typeof window)return 0;let o=0;return tn(e)?o=e.pageYOffset:e instanceof Document?o=e.documentElement.scrollTop:(e instanceof HTMLElement||e)&&(o=e.scrollTop),e&&!tn(e)&&"number"!=typeof o&&(o=null===(n=(null!==(t=e.ownerDocument)&&void 0!==t?t:e).documentElement)||void 0===n?void 0:n.scrollTop),o};function on(e,t={}){const{getContainer:n=()=>window,callback:o,duration:r=450}=t,a=n(),i=nn(a),l=Date.now(),s=()=>{const t=Date.now()-l,n=function(e,t,n,o){const r=n-t;return(e/=o/2)<1?r/2*e*e*e+t:r/2*((e-=2)*e*e+2)+t}(t>r?r:t,i,e,r);tn(a)?a.scrollTo(window.pageXOffset,n):a instanceof Document||"HTMLDocument"===a.constructor.name?a.documentElement.scrollTop=n:a.scrollTop=n,t<r?m(s):"function"==typeof o&&o()};m(s)}const rn=new v("antSlideUpIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1}}),an=new v("antSlideUpOut",{"0%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0}}),ln=new v("antSlideDownIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1}}),sn=new v("antSlideDownOut",{"0%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0}}),cn={"slide-up":{inKeyframes:rn,outKeyframes:an},"slide-down":{inKeyframes:ln,outKeyframes:sn},"slide-left":{inKeyframes:new v("antSlideLeftIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1}}),outKeyframes:new v("antSlideLeftOut",{"0%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0}})},"slide-right":{inKeyframes:new v("antSlideRightIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1}}),outKeyframes:new v("antSlideRightOut",{"0%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0}})}},un=(e,t)=>{const{antCls:n}=e,o=`${n}-${t}`,{inKeyframes:r,outKeyframes:a}=cn[t];return[y(o,r,a,e.motionDurationMid),{[`\n      ${o}-enter,\n      ${o}-appear\n    `]:{transform:"scale(0)",transformOrigin:"0% 0%",opacity:0,animationTimingFunction:e.motionEaseOutQuint,"&-prepare":{transform:"scale(1)"}},[`${o}-leave`]:{animationTimingFunction:e.motionEaseInQuint}}]};var dn=function(e){if(w()&&window.document.documentElement){var t=Array.isArray(e)?e:[e],n=window.document.documentElement;return t.some((function(e){return e in n.style}))}return!1};function pn(e,t){return Array.isArray(e)||void 0===t?dn(e):function(e,t){if(!dn(e))return!1;var n=document.createElement("div"),o=n.style[e];return n.style[e]=t,n.style[e]!==o}(e,t)}const mn=function(){if("undefined"==typeof navigator||"undefined"==typeof window)return!1;var e=navigator.userAgent||navigator.vendor||window.opera;return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i.test(null==e?void 0:e.substr(0,4))};var fn=["prefixCls","invalidate","item","renderItem","responsive","responsiveDisabled","registerSize","itemKey","className","style","children","display","order","component"],gn=void 0;function hn(t,n){var o=t.prefixCls,r=t.invalidate,a=t.item,i=t.renderItem,l=t.responsive,c=t.responsiveDisabled,u=t.registerSize,d=t.itemKey,m=t.className,f=t.style,g=t.children,h=t.display,b=t.order,v=t.component,y=void 0===v?"div":v,w=x(t,fn),$=l&&!h;function E(e){u(d,e)}e.useEffect((function(){return function(){E(null)}}),[]);var S,O=i&&a!==gn?i(a,{index:b}):g;r||(S={opacity:$?0:1,height:$?0:gn,overflowY:$?"hidden":gn,order:l?b:gn,pointerEvents:$?"none":gn,position:$?"absolute":gn});var k={};$&&(k["aria-hidden"]=!0);var _=e.createElement(y,p({className:C(!r&&o,m),style:s(s({},S),f)},k,w,{ref:n}),O);return l&&(_=e.createElement(Qt,{onResize:function(e){E(e.offsetWidth)},disabled:c},_)),_}var bn=e.forwardRef(hn);function vn(){var t=e.useRef(null);return function(e){t.current||(t.current=[],function(e){if("undefined"==typeof MessageChannel)m(e);else{var t=new MessageChannel;t.port1.onmessage=function(){return e()},t.port2.postMessage(void 0)}}((function(){S.unstable_batchedUpdates((function(){t.current.forEach((function(e){e()})),t.current=null}))}))),t.current.push(e)}}function yn(t,n){var o=e.useState(n),r=$(o,2),a=r[0],i=r[1];return[a,E((function(e){t((function(){i(e)}))}))]}bn.displayName="Item";var wn=O.createContext(null),xn=["component"],Cn=["className"],$n=["className"],En=function(t,n){var o=e.useContext(wn);if(!o){var r=t.component,a=void 0===r?"div":r,i=x(t,xn);return e.createElement(a,p({},i,{ref:n}))}var l=o.className,s=x(o,Cn),c=t.className,u=x(t,$n);return e.createElement(wn.Provider,{value:null},e.createElement(bn,p({ref:n,className:C(l,c)},s,u)))},Sn=e.forwardRef(En);Sn.displayName="RawItem";var On=["prefixCls","data","renderItem","renderRawItem","itemKey","itemWidth","ssr","style","className","maxCount","renderRest","renderRawRest","suffix","component","itemComponent","onVisibleChange"],kn="responsive",_n="invalidate";function In(e){return"+ ".concat(e.length," ...")}function jn(t,n){var o=t.prefixCls,r=void 0===o?"rc-overflow":o,a=t.data,i=void 0===a?[]:a,l=t.renderItem,c=t.renderRawItem,u=t.itemKey,d=t.itemWidth,m=void 0===d?10:d,f=t.ssr,g=t.style,h=t.className,b=t.maxCount,v=t.renderRest,y=t.renderRawRest,w=t.suffix,E=t.component,S=void 0===E?"div":E,O=t.itemComponent,_=t.onVisibleChange,I=x(t,On),j="full"===f,R=vn(),B=yn(R,null),N=$(B,2),T=N[0],P=N[1],A=T||0,M=yn(R,new Map),z=$(M,2),D=z[0],L=z[1],H=yn(R,0),F=$(H,2),W=F[0],V=F[1],q=yn(R,0),X=$(q,2),G=X[0],K=X[1],Y=yn(R,0),U=$(Y,2),Q=U[0],Z=U[1],J=e.useState(null),ee=$(J,2),te=ee[0],ne=ee[1],oe=e.useState(null),re=$(oe,2),ae=re[0],ie=re[1],le=e.useMemo((function(){return null===ae&&j?Number.MAX_SAFE_INTEGER:ae||0}),[ae,T]),se=e.useState(!1),ce=$(se,2),ue=ce[0],de=ce[1],pe="".concat(r,"-item"),me=Math.max(W,G),fe=b===kn,ge=i.length&&fe,he=b===_n,be=ge||"number"==typeof b&&i.length>b,ve=e.useMemo((function(){var e=i;return ge?e=null===T&&j?i:i.slice(0,Math.min(i.length,A/m)):"number"==typeof b&&(e=i.slice(0,b)),e}),[i,m,T,b,ge]),ye=e.useMemo((function(){return ge?i.slice(le+1):i.slice(ve.length)}),[i,ve,ge,le]),we=e.useCallback((function(e,t){var n;return"function"==typeof u?u(e):null!==(n=u&&(null==e?void 0:e[u]))&&void 0!==n?n:t}),[u]),xe=e.useCallback(l||function(e){return e},[l]);function Ce(e,t,n){(ae!==e||void 0!==t&&t!==te)&&(ie(e),n||(de(e<i.length-1),null==_||_(e)),void 0!==t&&ne(t))}function $e(e,t){L((function(n){var o=new Map(n);return null===t?o.delete(e):o.set(e,t),o}))}function Ee(e){return D.get(we(ve[e],e))}k((function(){if(A&&"number"==typeof me&&ve){var e=Q,t=ve.length,n=t-1;if(!t)return void Ce(0,null);for(var o=0;o<t;o+=1){var r=Ee(o);if(j&&(r=r||0),void 0===r){Ce(o-1,void 0,!0);break}if(e+=r,0===n&&e<=A||o===n-1&&e+Ee(n)<=A){Ce(n,null);break}if(e+me>A){Ce(o-1,e-r-Q+G);break}}w&&Ee(0)+Q>A&&ne(null)}}),[A,D,G,Q,we,ve]);var Se=ue&&!!ye.length,Oe={};null!==te&&ge&&(Oe={position:"absolute",left:te,top:0});var ke={prefixCls:pe,responsive:ge,component:O,invalidate:he},_e=c?function(t,n){var o=we(t,n);return e.createElement(wn.Provider,{key:o,value:s(s({},ke),{},{order:n,item:t,itemKey:o,registerSize:$e,display:n<=le})},c(t,n))}:function(t,n){var o=we(t,n);return e.createElement(bn,p({},ke,{order:n,key:o,item:t,renderItem:xe,itemKey:o,registerSize:$e,display:n<=le}))},Ie={order:Se?le:Number.MAX_SAFE_INTEGER,className:"".concat(pe,"-rest"),registerSize:function(e,t){K(t),V(G)},display:Se},je=v||In,Re=y?e.createElement(wn.Provider,{value:s(s({},ke),Ie)},y(ye)):e.createElement(bn,p({},ke,Ie),"function"==typeof je?je(ye):je),Be=e.createElement(S,p({className:C(!he&&r,h),style:g,ref:n},I),ve.map(_e),be?Re:null,w&&e.createElement(bn,p({},ke,{responsive:fe,responsiveDisabled:!ge,order:le,className:"".concat(pe,"-suffix"),registerSize:function(e,t){Z(t)},display:!0,style:Oe}),w));return fe?e.createElement(Qt,{onResize:function(e,t){P(t.clientWidth)},disabled:!ge},Be):Be}var Rn=e.forwardRef(jn);function Bn(t){var n=t.prefixCls,o=t.align,r=t.arrow,a=t.arrowPos,i=r||{},l=i.className,s=i.content,c=a.x,u=void 0===c?0:c,d=a.y,p=void 0===d?0:d,m=e.useRef();if(!o||!o.points)return null;var f={position:"absolute"};if(!1!==o.autoArrow){var g=o.points[0],h=o.points[1],b=g[0],v=g[1],y=h[0],w=h[1];b!==y&&["t","b"].includes(b)?"t"===b?f.top=0:f.bottom=0:f.top=p,v!==w&&["l","r"].includes(v)?"l"===v?f.left=0:f.right=0:f.left=u}return e.createElement("div",{ref:m,className:C("".concat(n,"-arrow"),l),style:f},s)}function Nn(t){var n=t.prefixCls,o=t.open,r=t.zIndex,a=t.mask,i=t.motion;return a?e.createElement(_,p({},i,{motionAppear:!0,visible:o,removeOnLeave:!0}),(function(t){var o=t.className;return e.createElement("div",{style:{zIndex:r},className:C("".concat(n,"-mask"),o)})})):null}Rn.displayName="Overflow",Rn.Item=Sn,Rn.RESPONSIVE=kn,Rn.INVALIDATE=_n;var Tn=e.memo((function(e){return e.children}),(function(e,t){return t.cache})),Pn=e.forwardRef((function(t,n){var o=t.popup,r=t.className,a=t.prefixCls,i=t.style,l=t.target,c=t.onVisibleChanged,u=t.open,d=t.keepDom,m=t.fresh,f=t.onClick,g=t.mask,h=t.arrow,b=t.arrowPos,v=t.align,y=t.motion,w=t.maskMotion,x=t.forceRender,E=t.getPopupContainer,S=t.autoDestroy,O=t.portal,j=t.zIndex,R=t.onMouseEnter,B=t.onMouseLeave,N=t.onPointerEnter,T=t.onPointerDownCapture,P=t.ready,A=t.offsetX,M=t.offsetY,z=t.offsetR,D=t.offsetB,L=t.onAlign,H=t.onPrepare,F=t.stretch,W=t.targetWidth,V=t.targetHeight,q="function"==typeof o?o():o,X=u||d,G=(null==E?void 0:E.length)>0,K=e.useState(!E||!G),Y=$(K,2),U=Y[0],Q=Y[1];if(k((function(){!U&&G&&l&&Q(!0)}),[U,G,l]),!U)return null;var Z="auto",J={left:"-1000vw",top:"-1000vh",right:Z,bottom:Z};if(P||!u){var ee,te=v.points,ne=v.dynamicInset||(null===(ee=v._experimental)||void 0===ee?void 0:ee.dynamicInset),oe=ne&&"r"===te[0][1],re=ne&&"b"===te[0][0];oe?(J.right=z,J.left=Z):(J.left=A,J.right=Z),re?(J.bottom=D,J.top=Z):(J.top=M,J.bottom=Z)}var ae={};return F&&(F.includes("height")&&V?ae.height=V:F.includes("minHeight")&&V&&(ae.minHeight=V),F.includes("width")&&W?ae.width=W:F.includes("minWidth")&&W&&(ae.minWidth=W)),u||(ae.pointerEvents="none"),e.createElement(O,{open:x||X,getContainer:E&&function(){return E(l)},autoDestroy:S},e.createElement(Nn,{prefixCls:a,open:u,zIndex:j,mask:g,motion:w}),e.createElement(Qt,{onResize:L,disabled:!u},(function(t){return e.createElement(_,p({motionAppear:!0,motionEnter:!0,motionLeave:!0,removeOnLeave:!1,forceRender:x,leavedClassName:"".concat(a,"-hidden")},y,{onAppearPrepare:H,onEnterPrepare:H,visible:u,onVisibleChanged:function(e){var t;null==y||null===(t=y.onVisibleChanged)||void 0===t||t.call(y,e),c(e)}}),(function(o,l){var c=o.className,d=o.style,p=C(a,c,r);return e.createElement("div",{ref:I(t,n,l),className:p,style:s(s(s(s({"--arrow-x":"".concat(b.x||0,"px"),"--arrow-y":"".concat(b.y||0,"px")},J),ae),d),{},{boxSizing:"border-box",zIndex:j},i),onMouseEnter:R,onMouseLeave:B,onPointerEnter:N,onClick:f,onPointerDownCapture:T},h&&e.createElement(Bn,{prefixCls:a,arrow:h,arrowPos:b,align:v}),e.createElement(Tn,{cache:!u&&!m},q))}))})))})),An=e.forwardRef((function(t,n){var o=t.children,r=t.getTriggerDOMNode,s=a(o),c=e.useCallback((function(e){j(n,r?r(e):e)}),[r]),u=l(c,i(o));return s?e.cloneElement(o,{ref:u}):o})),Mn=e.createContext(null);function zn(e){return e?Array.isArray(e)?e:[e]:[]}function Dn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return(arguments.length>2?arguments[2]:void 0)?e[0]===t[0]:e[0]===t[0]&&e[1]===t[1]}function Ln(e,t,n,o){return t||(n?{motionName:"".concat(e,"-").concat(n)}:o?{motionName:o}:null)}function Hn(e){return e.ownerDocument.defaultView}function Fn(e){for(var t=[],n=null==e?void 0:e.parentElement,o=["hidden","scroll","clip","auto"];n;){var r=Hn(n).getComputedStyle(n);[r.overflowX,r.overflowY,r.overflow].some((function(e){return o.includes(e)}))&&t.push(n),n=n.parentElement}return t}function Wn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return Number.isNaN(e)?t:e}function Vn(e){return Wn(parseFloat(e),0)}function qn(e,t){var n=s({},e);return(t||[]).forEach((function(e){if(!(e instanceof HTMLBodyElement||e instanceof HTMLHtmlElement)){var t=Hn(e).getComputedStyle(e),o=t.overflow,r=t.overflowClipMargin,a=t.borderTopWidth,i=t.borderBottomWidth,l=t.borderLeftWidth,s=t.borderRightWidth,c=e.getBoundingClientRect(),u=e.offsetHeight,d=e.clientHeight,p=e.offsetWidth,m=e.clientWidth,f=Vn(a),g=Vn(i),h=Vn(l),b=Vn(s),v=Wn(Math.round(c.width/p*1e3)/1e3),y=Wn(Math.round(c.height/u*1e3)/1e3),w=(p-m-h-b)*v,x=(u-d-f-g)*y,C=f*y,$=g*y,E=h*v,S=b*v,O=0,k=0;if("clip"===o){var _=Vn(r);O=_*v,k=_*y}var I=c.x+E-O,j=c.y+C-k,R=I+c.width+2*O-E-S-w,B=j+c.height+2*k-C-$-x;n.left=Math.max(n.left,I),n.top=Math.max(n.top,j),n.right=Math.min(n.right,R),n.bottom=Math.min(n.bottom,B)}})),n}function Xn(e){var t="".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),n=t.match(/^(.*)\%$/);return n?e*(parseFloat(n[1])/100):parseFloat(t)}function Gn(e,t){var n=$(t||[],2),o=n[0],r=n[1];return[Xn(e.width,o),Xn(e.height,r)]}function Kn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return[e[0],e[1]]}function Yn(e,t){var n,o=t[0],r=t[1];return n="t"===o?e.y:"b"===o?e.y+e.height:e.y+e.height/2,{x:"l"===r?e.x:"r"===r?e.x+e.width:e.x+e.width/2,y:n}}function Un(e,t){var n={t:"b",b:"t",l:"r",r:"l"};return e.map((function(e,o){return o===t?n[e]||"c":e})).join("")}var Qn=["prefixCls","children","action","showAction","hideAction","popupVisible","defaultPopupVisible","onPopupVisibleChange","afterPopupVisibleChange","mouseEnterDelay","mouseLeaveDelay","focusDelay","blurDelay","mask","maskClosable","getPopupContainer","forceRender","autoDestroy","destroyPopupOnHide","popup","popupClassName","popupStyle","popupPlacement","builtinPlacements","popupAlign","zIndex","stretch","getPopupClassNameFromAlign","fresh","alignPoint","onPopupClick","onPopupAlign","arrow","popupMotion","maskMotion","popupTransitionName","popupAnimation","maskTransitionName","maskAnimation","className","getTriggerDOMNode"];const Zn=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:P;return e.forwardRef((function(n,o){var r=n.prefixCls,a=void 0===r?"rc-trigger-popup":r,i=n.children,l=n.action,c=void 0===l?"hover":l,u=n.showAction,d=n.hideAction,p=n.popupVisible,m=n.defaultPopupVisible,g=n.onPopupVisibleChange,h=n.afterPopupVisibleChange,b=n.mouseEnterDelay,v=n.mouseLeaveDelay,y=void 0===v?.1:v,w=n.focusDelay,S=n.blurDelay,O=n.mask,_=n.maskClosable,I=void 0===_||_,j=n.getPopupContainer,P=n.forceRender,A=n.autoDestroy,M=n.destroyPopupOnHide,z=n.popup,D=n.popupClassName,L=n.popupStyle,H=n.popupPlacement,F=n.builtinPlacements,W=void 0===F?{}:F,V=n.popupAlign,q=n.zIndex,X=n.stretch,G=n.getPopupClassNameFromAlign,K=n.fresh,Y=n.alignPoint,U=n.onPopupClick,Q=n.onPopupAlign,Z=n.arrow,J=n.popupMotion,ee=n.maskMotion,te=n.popupTransitionName,ne=n.popupAnimation,oe=n.maskTransitionName,re=n.maskAnimation,ae=n.className,ie=n.getTriggerDOMNode,le=x(n,Qn),se=A||M||!1,ce=e.useState(!1),ue=$(ce,2),de=ue[0],pe=ue[1];k((function(){pe(mn())}),[]);var me=e.useRef({}),fe=e.useContext(Mn),ge=e.useMemo((function(){return{registerSubPopup:function(e,t){me.current[e]=t,null==fe||fe.registerSubPopup(e,t)}}}),[fe]),he=T(),be=e.useState(null),ve=$(be,2),ye=ve[0],we=ve[1],xe=e.useRef(null),Ce=E((function(e){xe.current=e,R(e)&&ye!==e&&we(e),null==fe||fe.registerSubPopup(he,e)})),$e=e.useState(null),Ee=$($e,2),Se=Ee[0],Oe=Ee[1],ke=e.useRef(null),_e=E((function(e){R(e)&&Se!==e&&(Oe(e),ke.current=e)})),Ie=e.Children.only(i),je=(null==Ie?void 0:Ie.props)||{},Re={},Be=E((function(e){var t,n,o=Se;return(null==o?void 0:o.contains(e))||(null===(t=N(o))||void 0===t?void 0:t.host)===e||e===o||(null==ye?void 0:ye.contains(e))||(null===(n=N(ye))||void 0===n?void 0:n.host)===e||e===ye||Object.values(me.current).some((function(t){return(null==t?void 0:t.contains(e))||e===t}))})),Ne=Ln(a,J,ne,te),Te=Ln(a,ee,re,oe),Pe=e.useState(m||!1),Ae=$(Pe,2),Me=Ae[0],ze=Ae[1],De=null!=p?p:Me,Le=E((function(e){void 0===p&&ze(e)}));k((function(){ze(p||!1)}),[p]);var He=e.useRef(De);He.current=De;var Fe=e.useRef([]);Fe.current=[];var We=E((function(e){var t;Le(e),(null!==(t=Fe.current[Fe.current.length-1])&&void 0!==t?t:De)!==e&&(Fe.current.push(e),null==g||g(e))})),Ve=e.useRef(),qe=function(){clearTimeout(Ve.current)},Xe=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;qe(),0===t?We(e):Ve.current=setTimeout((function(){We(e)}),1e3*t)};e.useEffect((function(){return qe}),[]);var Ge=e.useState(!1),Ke=$(Ge,2),Ye=Ke[0],Ue=Ke[1];k((function(e){e&&!De||Ue(!0)}),[De]);var Qe=e.useState(null),Ze=$(Qe,2),Je=Ze[0],et=Ze[1],tt=e.useState(null),nt=$(tt,2),ot=nt[0],rt=nt[1],at=function(e){rt([e.clientX,e.clientY])},it=function(t,n,o,r,a,i,l){var c=e.useState({ready:!1,offsetX:0,offsetY:0,offsetR:0,offsetB:0,arrowX:0,arrowY:0,scaleX:1,scaleY:1,align:a[r]||{}}),u=$(c,2),d=u[0],p=u[1],m=e.useRef(0),f=e.useMemo((function(){return n?Fn(n):[]}),[n]),g=e.useRef({});t||(g.current={});var h=E((function(){if(n&&o&&t){let t=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:te,o=P.x+e,r=P.y+t,a=o+V,i=r+W,l=Math.max(o,n.left),s=Math.max(r,n.top),c=Math.min(a,n.right),u=Math.min(i,n.bottom);return Math.max(0,(c-l)*(u-s))},vt=function(){he=P.y+Ie,be=he+W,ve=P.x+_e,ye=ve+V};var e,c,u,d,m,h=n,b=h.ownerDocument,v=Hn(h).getComputedStyle(h),y=v.width,w=v.height,x=v.position,C=h.style.left,E=h.style.top,S=h.style.right,O=h.style.bottom,k=h.style.overflow,_=s(s({},a[r]),i),I=b.createElement("div");if(null===(e=h.parentElement)||void 0===e||e.appendChild(I),I.style.left="".concat(h.offsetLeft,"px"),I.style.top="".concat(h.offsetTop,"px"),I.style.position=x,I.style.height="".concat(h.offsetHeight,"px"),I.style.width="".concat(h.offsetWidth,"px"),h.style.left="0",h.style.top="0",h.style.right="auto",h.style.bottom="auto",h.style.overflow="hidden",Array.isArray(o))m={x:o[0],y:o[1],width:0,height:0};else{var j,N,T=o.getBoundingClientRect();T.x=null!==(j=T.x)&&void 0!==j?j:T.left,T.y=null!==(N=T.y)&&void 0!==N?N:T.top,m={x:T.x,y:T.y,width:T.width,height:T.height}}var P=h.getBoundingClientRect();P.x=null!==(c=P.x)&&void 0!==c?c:P.left,P.y=null!==(u=P.y)&&void 0!==u?u:P.top;var A=b.documentElement,M=A.clientWidth,z=A.clientHeight,D=A.scrollWidth,L=A.scrollHeight,H=A.scrollTop,F=A.scrollLeft,W=P.height,V=P.width,q=m.height,X=m.width,G={left:0,top:0,right:M,bottom:z},K={left:-F,top:-H,right:D-F,bottom:L-H},Y=_.htmlRegion,U="visible",Q="visibleFirst";"scroll"!==Y&&Y!==Q&&(Y=U);var Z=Y===Q,J=qn(K,f),ee=qn(G,f),te=Y===U?ee:J,ne=Z?ee:te;h.style.left="auto",h.style.top="auto",h.style.right="0",h.style.bottom="0";var oe=h.getBoundingClientRect();h.style.left=C,h.style.top=E,h.style.right=S,h.style.bottom=O,h.style.overflow=k,null===(d=h.parentElement)||void 0===d||d.removeChild(I);var re=Wn(Math.round(V/parseFloat(y)*1e3)/1e3),ae=Wn(Math.round(W/parseFloat(w)*1e3)/1e3);if(0===re||0===ae||R(o)&&!B(o))return;var ie=_.offset,le=_.targetOffset,se=Gn(P,ie),ce=$(se,2),ue=ce[0],de=ce[1],pe=Gn(m,le),me=$(pe,2),fe=me[0],ge=me[1];m.x-=fe,m.y-=ge;var he,be,ve,ye,we=_.points||[],xe=$(we,2),Ce=xe[0],$e=Kn(xe[1]),Ee=Kn(Ce),Se=Yn(m,$e),Oe=Yn(P,Ee),ke=s({},_),_e=Se.x-Oe.x+ue,Ie=Se.y-Oe.y+de,je=t(_e,Ie),Re=t(_e,Ie,ee),Be=Yn(m,["t","l"]),Ne=Yn(P,["t","l"]),Te=Yn(m,["b","r"]),Pe=Yn(P,["b","r"]),Ae=_.overflow||{},Me=Ae.adjustX,ze=Ae.adjustY,De=Ae.shiftX,Le=Ae.shiftY,He=function(e){return"boolean"==typeof e?e:e>=0};vt();var Fe=He(ze),We=Ee[0]===$e[0];if(Fe&&"t"===Ee[0]&&(be>ne.bottom||g.current.bt)){var Ve=Ie;We?Ve-=W-q:Ve=Be.y-Pe.y-de;var qe=t(_e,Ve),Xe=t(_e,Ve,ee);qe>je||qe===je&&(!Z||Xe>=Re)?(g.current.bt=!0,Ie=Ve,de=-de,ke.points=[Un(Ee,0),Un($e,0)]):g.current.bt=!1}if(Fe&&"b"===Ee[0]&&(he<ne.top||g.current.tb)){var Ge=Ie;We?Ge+=W-q:Ge=Te.y-Ne.y-de;var Ke=t(_e,Ge),Ye=t(_e,Ge,ee);Ke>je||Ke===je&&(!Z||Ye>=Re)?(g.current.tb=!0,Ie=Ge,de=-de,ke.points=[Un(Ee,0),Un($e,0)]):g.current.tb=!1}var Ue=He(Me),Qe=Ee[1]===$e[1];if(Ue&&"l"===Ee[1]&&(ye>ne.right||g.current.rl)){var Ze=_e;Qe?Ze-=V-X:Ze=Be.x-Pe.x-ue;var Je=t(Ze,Ie),et=t(Ze,Ie,ee);Je>je||Je===je&&(!Z||et>=Re)?(g.current.rl=!0,_e=Ze,ue=-ue,ke.points=[Un(Ee,1),Un($e,1)]):g.current.rl=!1}if(Ue&&"r"===Ee[1]&&(ve<ne.left||g.current.lr)){var tt=_e;Qe?tt+=V-X:tt=Te.x-Ne.x-ue;var nt=t(tt,Ie),ot=t(tt,Ie,ee);nt>je||nt===je&&(!Z||ot>=Re)?(g.current.lr=!0,_e=tt,ue=-ue,ke.points=[Un(Ee,1),Un($e,1)]):g.current.lr=!1}vt();var rt=!0===De?0:De;"number"==typeof rt&&(ve<ee.left&&(_e-=ve-ee.left-ue,m.x+X<ee.left+rt&&(_e+=m.x-ee.left+X-rt)),ye>ee.right&&(_e-=ye-ee.right-ue,m.x>ee.right-rt&&(_e+=m.x-ee.right+rt)));var at=!0===Le?0:Le;"number"==typeof at&&(he<ee.top&&(Ie-=he-ee.top-de,m.y+q<ee.top+at&&(Ie+=m.y-ee.top+q-at)),be>ee.bottom&&(Ie-=be-ee.bottom-de,m.y>ee.bottom-at&&(Ie+=m.y-ee.bottom+at)));var it=P.x+_e,lt=it+V,st=P.y+Ie,ct=st+W,ut=m.x,dt=ut+X,pt=m.y,mt=pt+q,ft=(Math.max(it,ut)+Math.min(lt,dt))/2-it,gt=(Math.max(st,pt)+Math.min(ct,mt))/2-st;null==l||l(n,ke);var ht=oe.right-P.x-(_e+P.width),bt=oe.bottom-P.y-(Ie+P.height);1===re&&(_e=Math.round(_e),ht=Math.round(ht)),1===ae&&(Ie=Math.round(Ie),bt=Math.round(bt)),p({ready:!0,offsetX:_e/re,offsetY:Ie/ae,offsetR:ht/re,offsetB:bt/ae,arrowX:ft/re,arrowY:gt/ae,scaleX:re,scaleY:ae,align:ke})}})),b=function(){p((function(e){return s(s({},e),{},{ready:!1})}))};return k(b,[r]),k((function(){t||b()}),[t]),[d.ready,d.offsetX,d.offsetY,d.offsetR,d.offsetB,d.arrowX,d.arrowY,d.scaleX,d.scaleY,d.align,function(){m.current+=1;var e=m.current;Promise.resolve().then((function(){m.current===e&&h()}))}]}(De,ye,Y&&null!==ot?ot:Se,H,W,V,Q),lt=$(it,11),st=lt[0],ct=lt[1],ut=lt[2],dt=lt[3],pt=lt[4],mt=lt[5],ft=lt[6],gt=lt[7],ht=lt[8],bt=lt[9],vt=lt[10],yt=function(t,n,o,r){return e.useMemo((function(){var e=zn(null!=o?o:n),a=zn(null!=r?r:n),i=new Set(e),l=new Set(a);return t&&(i.has("hover")&&(i.delete("hover"),i.add("click")),l.has("hover")&&(l.delete("hover"),l.add("click"))),[i,l]}),[t,n,o,r])}(de,c,u,d),wt=$(yt,2),xt=wt[0],Ct=wt[1],$t=xt.has("click"),Et=Ct.has("click")||Ct.has("contextMenu"),St=E((function(){Ye||vt()}));!function(e,t,n,o,r){k((function(){if(e&&t&&n){let e=function(){o(),r()};var a=n,i=Fn(t),l=Fn(a),s=Hn(a),c=new Set([s].concat(f(i),f(l)));return c.forEach((function(t){t.addEventListener("scroll",e,{passive:!0})})),s.addEventListener("resize",e,{passive:!0}),o(),function(){c.forEach((function(t){t.removeEventListener("scroll",e),s.removeEventListener("resize",e)}))}}}),[e,t,n])}(De,Se,ye,St,(function(){He.current&&Y&&Et&&Xe(!1)})),k((function(){St()}),[ot,H]),k((function(){!De||null!=W&&W[H]||St()}),[JSON.stringify(V)]);var Ot=e.useMemo((function(){var e=function(e,t,n,o){for(var r=n.points,a=Object.keys(e),i=0;i<a.length;i+=1){var l,s=a[i];if(Dn(null===(l=e[s])||void 0===l?void 0:l.points,r,o))return"".concat(t,"-placement-").concat(s)}return""}(W,a,bt,Y);return C(e,null==G?void 0:G(bt))}),[bt,G,W,a,Y]);e.useImperativeHandle(o,(function(){return{nativeElement:ke.current,popupElement:xe.current,forceAlign:St}}));var kt=e.useState(0),_t=$(kt,2),It=_t[0],jt=_t[1],Rt=e.useState(0),Bt=$(Rt,2),Nt=Bt[0],Tt=Bt[1],Pt=function(){if(X&&Se){var e=Se.getBoundingClientRect();jt(e.width),Tt(e.height)}};function At(e,t,n,o){Re[e]=function(r){var a;null==o||o(r),Xe(t,n);for(var i=arguments.length,l=new Array(i>1?i-1:0),s=1;s<i;s++)l[s-1]=arguments[s];null===(a=je[e])||void 0===a||a.call.apply(a,[je,r].concat(l))}}k((function(){Je&&(vt(),Je(),et(null))}),[Je]),($t||Et)&&(Re.onClick=function(e){var t;He.current&&Et?Xe(!1):!He.current&&$t&&(at(e),Xe(!0));for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];null===(t=je.onClick)||void 0===t||t.call.apply(t,[je,e].concat(o))});var Mt,zt,Dt=function(t,n,o,r,a,i,l,s){var c=e.useRef(t);c.current=t;var u=e.useRef(!1);return e.useEffect((function(){if(n&&r&&(!a||i)){var e=function(){u.current=!1},t=function(e){var t;!c.current||l((null===(t=e.composedPath)||void 0===t||null===(t=t.call(e))||void 0===t?void 0:t[0])||e.target)||u.current||s(!1)},d=Hn(r);d.addEventListener("pointerdown",e,!0),d.addEventListener("mousedown",t,!0),d.addEventListener("contextmenu",t,!0);var p=N(o);return p&&(p.addEventListener("mousedown",t,!0),p.addEventListener("contextmenu",t,!0)),function(){d.removeEventListener("pointerdown",e,!0),d.removeEventListener("mousedown",t,!0),d.removeEventListener("contextmenu",t,!0),p&&(p.removeEventListener("mousedown",t,!0),p.removeEventListener("contextmenu",t,!0))}}}),[n,o,r,a,i]),function(){u.current=!0}}(De,Et,Se,ye,O,I,Be,Xe),Lt=xt.has("hover"),Ht=Ct.has("hover");Lt&&(At("onMouseEnter",!0,b,(function(e){at(e)})),At("onPointerEnter",!0,b,(function(e){at(e)})),Mt=function(e){(De||Ye)&&null!=ye&&ye.contains(e.target)&&Xe(!0,b)},Y&&(Re.onMouseMove=function(e){var t;null===(t=je.onMouseMove)||void 0===t||t.call(je,e)})),Ht&&(At("onMouseLeave",!1,y),At("onPointerLeave",!1,y),zt=function(){Xe(!1,y)}),xt.has("focus")&&At("onFocus",!0,w),Ct.has("focus")&&At("onBlur",!1,S),xt.has("contextMenu")&&(Re.onContextMenu=function(e){var t;He.current&&Ct.has("contextMenu")?Xe(!1):(at(e),Xe(!0)),e.preventDefault();for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];null===(t=je.onContextMenu)||void 0===t||t.call.apply(t,[je,e].concat(o))}),ae&&(Re.className=C(je.className,ae));var Ft=s(s({},je),Re),Wt={};["onContextMenu","onClick","onMouseDown","onTouchStart","onMouseEnter","onMouseLeave","onFocus","onBlur"].forEach((function(e){le[e]&&(Wt[e]=function(){for(var t,n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];null===(t=Ft[e])||void 0===t||t.call.apply(t,[Ft].concat(o)),le[e].apply(le,o)})}));var Vt=e.cloneElement(Ie,s(s({},Ft),Wt)),qt={x:mt,y:ft},Xt=Z?s({},!0!==Z?Z:{}):null;return e.createElement(e.Fragment,null,e.createElement(Qt,{disabled:!De,ref:_e,onResize:function(){Pt(),St()}},e.createElement(An,{getTriggerDOMNode:ie},Vt)),e.createElement(Mn.Provider,{value:ge},e.createElement(Pn,{portal:t,ref:Ce,prefixCls:a,popup:z,className:C(D,Ot),style:L,target:Se,onMouseEnter:Mt,onMouseLeave:zt,onPointerEnter:Mt,zIndex:q,open:De,keepDom:Ye,fresh:K,onClick:U,onPointerDownCapture:Dt,mask:O,motion:Ne,maskMotion:Te,onVisibleChanged:function(e){Ue(!1),vt(),null==h||h(e)},onPrepare:function(){return new Promise((function(e){Pt(),et((function(){return e}))}))},forceRender:P,autoDestroy:se,getPopupContainer:j,align:bt,arrow:Xt,arrowPos:qt,ready:st,offsetX:ct,offsetY:ut,offsetR:dt,offsetB:pt,onAlign:St,stretch:X,targetWidth:It/gt,targetHeight:Nt/ht})))}))}(P);function Jn(e,t,n){return C({[`${e}-status-success`]:"success"===t,[`${e}-status-warning`]:"warning"===t,[`${e}-status-error`]:"error"===t,[`${e}-status-validating`]:"validating"===t,[`${e}-has-feedback`]:n})}const eo=(e,t)=>t||e,to=(t,n,o=void 0)=>{var r,a;const{variant:i,[t]:l}=e.useContext(A),s=e.useContext(M),c=null==l?void 0:l.variant;let u;u=void 0!==n?n:!1===o?"borderless":null!==(a=null!==(r=null!=s?s:c)&&void 0!==r?r:i)&&void 0!==a?a:"outlined";return[u,z.includes(u)]};var no={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"}}]},name:"check",theme:"outlined"},oo=function(t,n){return e.createElement(D,p({},t,{ref:n,icon:no}))},ro=e.forwardRef(oo);const ao=(e,t)=>{void 0!==(null==e?void 0:e.addEventListener)?e.addEventListener("change",t):void 0!==(null==e?void 0:e.addListener)&&e.addListener(t)},io=(e,t)=>{void 0!==(null==e?void 0:e.removeEventListener)?e.removeEventListener("change",t):void 0!==(null==e?void 0:e.removeListener)&&e.removeListener(t)};function lo(t){var n=t.children,o=t.prefixCls,r=t.id,a=t.overlayInnerStyle,i=t.bodyClassName,l=t.className,s=t.style;return e.createElement("div",{className:C("".concat(o,"-content"),l),style:s},e.createElement("div",{className:C("".concat(o,"-inner"),i),id:r,role:"tooltip",style:a},"function"==typeof n?n():n))}var so={shiftX:64,adjustY:1},co={adjustX:1,shiftY:!0},uo=[0,0],po={left:{points:["cr","cl"],overflow:co,offset:[-4,0],targetOffset:uo},right:{points:["cl","cr"],overflow:co,offset:[4,0],targetOffset:uo},top:{points:["bc","tc"],overflow:so,offset:[0,-4],targetOffset:uo},bottom:{points:["tc","bc"],overflow:so,offset:[0,4],targetOffset:uo},topLeft:{points:["bl","tl"],overflow:so,offset:[0,-4],targetOffset:uo},leftTop:{points:["tr","tl"],overflow:co,offset:[-4,0],targetOffset:uo},topRight:{points:["br","tr"],overflow:so,offset:[0,-4],targetOffset:uo},rightTop:{points:["tl","tr"],overflow:co,offset:[4,0],targetOffset:uo},bottomRight:{points:["tr","br"],overflow:so,offset:[0,4],targetOffset:uo},rightBottom:{points:["bl","br"],overflow:co,offset:[4,0],targetOffset:uo},bottomLeft:{points:["tl","bl"],overflow:so,offset:[0,4],targetOffset:uo},leftBottom:{points:["br","bl"],overflow:co,offset:[-4,0],targetOffset:uo}},mo=["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","children","onVisibleChange","afterVisibleChange","transitionName","animation","motion","placement","align","destroyTooltipOnHide","defaultVisible","getTooltipContainer","overlayInnerStyle","arrowContent","overlay","id","showArrow","classNames","styles"],fo=function(t,n){var o=t.overlayClassName,r=t.trigger,a=void 0===r?["hover"]:r,i=t.mouseEnterDelay,l=void 0===i?0:i,c=t.mouseLeaveDelay,u=void 0===c?.1:c,d=t.overlayStyle,m=t.prefixCls,f=void 0===m?"rc-tooltip":m,g=t.children,h=t.onVisibleChange,b=t.afterVisibleChange,v=t.transitionName,y=t.animation,w=t.motion,$=t.placement,E=void 0===$?"right":$,S=t.align,O=void 0===S?{}:S,k=t.destroyTooltipOnHide,_=void 0!==k&&k,I=t.defaultVisible,j=t.getTooltipContainer,R=t.overlayInnerStyle;t.arrowContent;var B=t.overlay,N=t.id,P=t.showArrow,A=void 0===P||P,M=t.classNames,z=t.styles,D=x(t,mo),L=T(N),H=e.useRef(null);e.useImperativeHandle(n,(function(){return H.current}));var F=s({},D);"visible"in t&&(F.popupVisible=t.visible);var W,V,q;return e.createElement(Zn,p({popupClassName:C(o,null==M?void 0:M.root),prefixCls:f,popup:function(){return e.createElement(lo,{key:"content",prefixCls:f,id:L,bodyClassName:null==M?void 0:M.body,overlayInnerStyle:s(s({},R),null==z?void 0:z.body)},B)},action:a,builtinPlacements:po,popupPlacement:E,ref:H,popupAlign:O,getPopupContainer:j,onPopupVisibleChange:h,afterPopupVisibleChange:b,popupTransitionName:v,popupAnimation:y,popupMotion:w,defaultPopupVisible:I,autoDestroy:_,mouseLeaveDelay:u,popupStyle:s(s({},d),null==z?void 0:z.root),mouseEnterDelay:l,arrow:A},F),(W=e.Children.only(g),V=(null==W?void 0:W.props)||{},q=s(s({},V),{},{"aria-describedby":B?L:null}),e.cloneElement(g,q)))};const go=e.forwardRef(fo);function ho(e){const{sizePopupArrow:t,borderRadiusXS:n,borderRadiusOuter:o}=e,r=t/2,a=r,i=1*o/Math.sqrt(2),l=r-o*(1-1/Math.sqrt(2)),s=r-n*(1/Math.sqrt(2)),c=o*(Math.sqrt(2)-1)+n*(1/Math.sqrt(2)),u=2*r-s,d=c,p=2*r-i,m=l,f=2*r-0,g=a,h=r*Math.sqrt(2)+o*(Math.sqrt(2)-2),b=o*(Math.sqrt(2)-1);return{arrowShadowWidth:h,arrowPath:`path('M 0 ${a} A ${o} ${o} 0 0 0 ${i} ${l} L ${s} ${c} A ${n} ${n} 0 0 1 ${u} ${d} L ${p} ${m} A ${o} ${o} 0 0 0 ${f} ${g} Z')`,arrowPolygon:`polygon(${b}px 100%, 50% ${b}px, ${2*r-b}px 100%, ${b}px 100%)`}}const bo=(e,t,n)=>{const{sizePopupArrow:o,arrowPolygon:r,arrowPath:a,arrowShadowWidth:i,borderRadiusXS:l,calc:s}=e;return{pointerEvents:"none",width:o,height:o,overflow:"hidden","&::before":{position:"absolute",bottom:0,insetInlineStart:0,width:o,height:s(o).div(2).equal(),background:t,clipPath:{_multi_value_:!0,value:[r,a]},content:'""'},"&::after":{content:'""',position:"absolute",width:i,height:i,bottom:0,insetInline:0,margin:"auto",borderRadius:{_skip_check_:!0,value:`0 0 ${L(l)} 0`},transform:"translateY(50%) rotate(-135deg)",boxShadow:n,zIndex:0,background:"transparent"}}};function vo(e){const{contentRadius:t,limitVerticalRadius:n}=e,o=t>12?t+2:12;return{arrowOffsetHorizontal:o,arrowOffsetVertical:n?8:o}}function yo(e,t){return e?t:{}}function wo(e,t,n){const{componentCls:o,boxShadowPopoverArrow:r,arrowOffsetVertical:a,arrowOffsetHorizontal:i}=e,{arrowDistance:l=0,arrowPlacement:s={left:!0,right:!0,top:!0,bottom:!0}}=n||{};return{[o]:Object.assign(Object.assign(Object.assign(Object.assign({[`${o}-arrow`]:[Object.assign(Object.assign({position:"absolute",zIndex:1,display:"block"},bo(e,t,r)),{"&:before":{background:t}})]},yo(!!s.top,{[[`&-placement-top > ${o}-arrow`,`&-placement-topLeft > ${o}-arrow`,`&-placement-topRight > ${o}-arrow`].join(",")]:{bottom:l,transform:"translateY(100%) rotate(180deg)"},[`&-placement-top > ${o}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},"&-placement-topLeft":{"--arrow-offset-horizontal":i,[`> ${o}-arrow`]:{left:{_skip_check_:!0,value:i}}},"&-placement-topRight":{"--arrow-offset-horizontal":`calc(100% - ${L(i)})`,[`> ${o}-arrow`]:{right:{_skip_check_:!0,value:i}}}})),yo(!!s.bottom,{[[`&-placement-bottom > ${o}-arrow`,`&-placement-bottomLeft > ${o}-arrow`,`&-placement-bottomRight > ${o}-arrow`].join(",")]:{top:l,transform:"translateY(-100%)"},[`&-placement-bottom > ${o}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(-100%)"},"&-placement-bottomLeft":{"--arrow-offset-horizontal":i,[`> ${o}-arrow`]:{left:{_skip_check_:!0,value:i}}},"&-placement-bottomRight":{"--arrow-offset-horizontal":`calc(100% - ${L(i)})`,[`> ${o}-arrow`]:{right:{_skip_check_:!0,value:i}}}})),yo(!!s.left,{[[`&-placement-left > ${o}-arrow`,`&-placement-leftTop > ${o}-arrow`,`&-placement-leftBottom > ${o}-arrow`].join(",")]:{right:{_skip_check_:!0,value:l},transform:"translateX(100%) rotate(90deg)"},[`&-placement-left > ${o}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(100%) rotate(90deg)"},[`&-placement-leftTop > ${o}-arrow`]:{top:a},[`&-placement-leftBottom > ${o}-arrow`]:{bottom:a}})),yo(!!s.right,{[[`&-placement-right > ${o}-arrow`,`&-placement-rightTop > ${o}-arrow`,`&-placement-rightBottom > ${o}-arrow`].join(",")]:{left:{_skip_check_:!0,value:l},transform:"translateX(-100%) rotate(-90deg)"},[`&-placement-right > ${o}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(-100%) rotate(-90deg)"},[`&-placement-rightTop > ${o}-arrow`]:{top:a},[`&-placement-rightBottom > ${o}-arrow`]:{bottom:a}}))}}const xo={left:{points:["cr","cl"]},right:{points:["cl","cr"]},top:{points:["bc","tc"]},bottom:{points:["tc","bc"]},topLeft:{points:["bl","tl"]},leftTop:{points:["tr","tl"]},topRight:{points:["br","tr"]},rightTop:{points:["tl","tr"]},bottomRight:{points:["tr","br"]},rightBottom:{points:["bl","br"]},bottomLeft:{points:["tl","bl"]},leftBottom:{points:["br","bl"]}},Co={topLeft:{points:["bl","tc"]},leftTop:{points:["tr","cl"]},topRight:{points:["br","tc"]},rightTop:{points:["tl","cr"]},bottomRight:{points:["tr","bc"]},rightBottom:{points:["bl","cr"]},bottomLeft:{points:["tl","bc"]},leftBottom:{points:["br","cl"]}},$o=new Set(["topLeft","topRight","bottomLeft","bottomRight","leftTop","leftBottom","rightTop","rightBottom"]);function Eo(e){const{arrowWidth:t,autoAdjustOverflow:n,arrowPointAtCenter:o,offset:r,borderRadius:a,visibleFirst:i}=e,l=t/2,s={};return Object.keys(xo).forEach((e=>{const c=o&&Co[e]||xo[e],u=Object.assign(Object.assign({},c),{offset:[0,0],dynamicInset:!0});switch(s[e]=u,$o.has(e)&&(u.autoArrow=!1),e){case"top":case"topLeft":case"topRight":u.offset[1]=-l-r;break;case"bottom":case"bottomLeft":case"bottomRight":u.offset[1]=l+r;break;case"left":case"leftTop":case"leftBottom":u.offset[0]=-l-r;break;case"right":case"rightTop":case"rightBottom":u.offset[0]=l+r}const d=vo({contentRadius:a,limitVerticalRadius:!0});if(o)switch(e){case"topLeft":case"bottomLeft":u.offset[0]=-d.arrowOffsetHorizontal-l;break;case"topRight":case"bottomRight":u.offset[0]=d.arrowOffsetHorizontal+l;break;case"leftTop":case"rightTop":u.offset[1]=2*-d.arrowOffsetHorizontal+l;break;case"leftBottom":case"rightBottom":u.offset[1]=2*d.arrowOffsetHorizontal-l}u.overflow=function(e,t,n,o){if(!1===o)return{adjustX:!1,adjustY:!1};const r=o&&"object"==typeof o?o:{},a={};switch(e){case"top":case"bottom":a.shiftX=2*t.arrowOffsetHorizontal+n,a.shiftY=!0,a.adjustY=!0;break;case"left":case"right":a.shiftY=2*t.arrowOffsetVertical+n,a.shiftX=!0,a.adjustX=!0}const i=Object.assign(Object.assign({},a),r);return i.shiftX||(i.adjustX=!0),i.shiftY||(i.adjustY=!0),i}(e,d,t,n),i&&(u.htmlRegion="visibleFirst")})),s}const So=e=>{const{calc:t,componentCls:n,tooltipMaxWidth:o,tooltipColor:r,tooltipBg:a,tooltipBorderRadius:i,zIndexPopup:l,controlHeight:s,boxShadowSecondary:c,paddingSM:u,paddingXS:d,arrowOffsetHorizontal:p,sizePopupArrow:m}=e,f=t(i).add(m).add(p).equal(),g=t(i).mul(2).add(m).equal();return[{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},V(e)),{position:"absolute",zIndex:l,display:"block",width:"max-content",maxWidth:o,visibility:"visible","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"&-hidden":{display:"none"},"--antd-arrow-background-color":a,[`${n}-inner`]:{minWidth:g,minHeight:s,padding:`${L(e.calc(u).div(2).equal())} ${L(d)}`,color:r,textAlign:"start",textDecoration:"none",wordWrap:"break-word",backgroundColor:a,borderRadius:i,boxShadow:c,boxSizing:"border-box"},[["&-placement-topLeft","&-placement-topRight","&-placement-bottomLeft","&-placement-bottomRight"].join(",")]:{minWidth:f},[["&-placement-left","&-placement-leftTop","&-placement-leftBottom","&-placement-right","&-placement-rightTop","&-placement-rightBottom"].join(",")]:{[`${n}-inner`]:{borderRadius:e.min(i,8)}},[`${n}-content`]:{position:"relative"}}),Jt(e,((e,{darkColor:t})=>({[`&${n}-${e}`]:{[`${n}-inner`]:{backgroundColor:t},[`${n}-arrow`]:{"--antd-arrow-background-color":t}}})))),{"&-rtl":{direction:"rtl"}})},wo(e,"var(--antd-arrow-background-color)"),{[`${n}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow}}]},Oo=e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+70},vo({contentRadius:e.borderRadius,limitVerticalRadius:!0})),ho(F(e,{borderRadiusOuter:Math.min(e.borderRadiusOuter,4)}))),ko=(e,t=!0)=>H("Tooltip",(e=>{const{borderRadius:t,colorTextLightSolid:n,colorBgSpotlight:o}=e,r=F(e,{tooltipMaxWidth:250,tooltipColor:n,tooltipBorderRadius:t,tooltipBg:o});return[So(r),W(e,"zoom-big-fast")]}),Oo,{resetStyle:!1,injectStyle:t})(e),_o=g.map((e=>`${e}-inverse`)),Io=["success","processing","error","default","warning"];function jo(e,t=!0){return t?[].concat(f(_o),f(g)).includes(e):g.includes(e)}function Ro(e,t){const n=jo(t),o=C({[`${e}-${t}`]:t&&n}),r={},a={};return t&&!n&&(r.background=t,a["--antd-arrow-background-color"]=t),{className:o,overlayStyle:r,arrowStyle:a}}const Bo=e.forwardRef(((t,n)=>{var o,r;const{prefixCls:a,openClassName:i,getTooltipContainer:l,color:s,overlayInnerStyle:c,children:u,afterOpenChange:d,afterVisibleChange:p,destroyTooltipOnHide:m,destroyOnHidden:f,arrow:g=!0,title:h,overlay:b,builtinPlacements:v,arrowPointAtCenter:y=!1,autoAdjustOverflow:w=!0,motion:x,getPopupContainer:$,placement:E="top",mouseEnterDelay:S=.1,mouseLeaveDelay:O=.1,overlayStyle:k,rootClassName:_,overlayClassName:I,styles:j,classNames:R}=t,B=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["prefixCls","openClassName","getTooltipContainer","color","overlayInnerStyle","children","afterOpenChange","afterVisibleChange","destroyTooltipOnHide","destroyOnHidden","arrow","title","overlay","builtinPlacements","arrowPointAtCenter","autoAdjustOverflow","motion","getPopupContainer","placement","mouseEnterDelay","mouseLeaveDelay","overlayStyle","rootClassName","overlayClassName","styles","classNames"]),N=!!g,[,T]=q(),{getPopupContainer:P,getPrefixCls:A,direction:M,className:z,style:D,classNames:L,styles:H}=X("tooltip"),F=G(),W=e.useRef(null),V=()=>{var e;null===(e=W.current)||void 0===e||e.forceAlign()};e.useImperativeHandle(n,(()=>{var e,t;return{forceAlign:V,forcePopupAlign:()=>{F.deprecated(!1,"forcePopupAlign","forceAlign"),V()},nativeElement:null===(e=W.current)||void 0===e?void 0:e.nativeElement,popupElement:null===(t=W.current)||void 0===t?void 0:t.popupElement}}));const[te,ne]=K(!1,{value:null!==(o=t.open)&&void 0!==o?o:t.visible,defaultValue:null!==(r=t.defaultOpen)&&void 0!==r?r:t.defaultVisible}),oe=!h&&!b&&0!==h,re=e.useMemo((()=>{var e,t;let n=y;return"object"==typeof g&&(n=null!==(t=null!==(e=g.pointAtCenter)&&void 0!==e?e:g.arrowPointAtCenter)&&void 0!==t?t:y),v||Eo({arrowPointAtCenter:n,autoAdjustOverflow:w,arrowWidth:N?T.sizePopupArrow:0,borderRadius:T.borderRadius,offset:T.marginXXS,visibleFirst:!0})}),[y,g,v,T]),ae=e.useMemo((()=>0===h?h:b||h||""),[b,h]),ie=e.createElement(Y,{space:!0},"function"==typeof ae?ae():ae),le=A("tooltip",a),se=A(),ce=t["data-popover-inject"];let ue=te;"open"in t||"visible"in t||!oe||(ue=!1);const de=e.isValidElement(u)&&!U(u)?u:e.createElement("span",null,u),pe=de.props,me=pe.className&&"string"!=typeof pe.className?pe.className:C(pe.className,i||`${le}-open`),[fe,ge,he]=ko(le,!ce),be=Ro(le,s),ve=be.arrowStyle,ye=C(I,{[`${le}-rtl`]:"rtl"===M},be.className,_,ge,he,z,L.root,null==R?void 0:R.root),we=C(L.body,null==R?void 0:R.body),[xe,Ce]=Q("Tooltip",B.zIndex),$e=e.createElement(go,Object.assign({},B,{zIndex:xe,showArrow:N,placement:E,mouseEnterDelay:S,mouseLeaveDelay:O,prefixCls:le,classNames:{root:ye,body:we},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},ve),H.root),D),k),null==j?void 0:j.root),body:Object.assign(Object.assign(Object.assign(Object.assign({},H.body),c),null==j?void 0:j.body),be.overlayStyle)},getTooltipContainer:$||l||P,ref:W,builtinPlacements:re,overlay:ie,visible:ue,onVisibleChange:e=>{var n,o;ne(!oe&&e),oe||(null===(n=t.onOpenChange)||void 0===n||n.call(t,e),null===(o=t.onVisibleChange)||void 0===o||o.call(t,e))},afterVisibleChange:null!=d?d:p,arrowContent:e.createElement("span",{className:`${le}-arrow-content`}),motion:{motionName:Z(se,"zoom-big-fast",t.transitionName),motionDeadline:1e3},destroyTooltipOnHide:null!=f?f:!!m}),ue?J(de,{className:me}):de);return fe(e.createElement(ee.Provider,{value:Ce},$e))}));Bo._InternalPanelDoNotUseOrYouWillBeFired=t=>{const{prefixCls:n,className:o,placement:r="top",title:a,color:i,overlayInnerStyle:l}=t,{getPrefixCls:s}=e.useContext(A),c=s("tooltip",n),[u,d,p]=ko(c),m=Ro(c,i),f=m.arrowStyle,g=Object.assign(Object.assign({},l),m.overlayStyle),h=C(d,p,c,`${c}-pure`,`${c}-placement-${r}`,o,m.className);return u(e.createElement("div",{className:h,style:f},e.createElement("div",{className:`${c}-arrow`}),e.createElement(lo,Object.assign({},t,{className:d,prefixCls:c,overlayInnerStyle:g}),a)))};var No={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M859.9 168H164.1c-4.5 0-8.1 3.6-8.1 8v60c0 4.4 3.6 8 8.1 8h695.8c4.5 0 8.1-3.6 8.1-8v-60c0-4.4-3.6-8-8.1-8zM518.3 355a8 8 0 00-12.6 0l-112 141.7a7.98 7.98 0 006.3 12.9h73.9V848c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V509.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 355z"}}]},name:"vertical-align-top",theme:"outlined"},To=function(t,n){return e.createElement(D,p({},t,{ref:n,icon:No}))},Po=e.forwardRef(To);const Ao=new v("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),Mo=new v("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),zo=new v("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),Do=new v("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),Lo=new v("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),Ho=new v("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),Fo=e=>{const{fontHeight:t,lineWidth:n,marginXS:o,colorBorderBg:r}=e,a=t,i=n,l=e.colorTextLightSolid,s=e.colorError,c=e.colorErrorHover;return F(e,{badgeFontHeight:a,badgeShadowSize:i,badgeTextColor:l,badgeColor:s,badgeColorHover:c,badgeShadowColor:r,badgeProcessingDuration:"1.2s",badgeRibbonOffset:o,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"})},Wo=e=>{const{fontSize:t,lineHeight:n,fontSizeSM:o,lineWidth:r}=e;return{indicatorZIndex:"auto",indicatorHeight:Math.round(t*n)-2*r,indicatorHeightSM:t,dotSize:o/2,textFontSize:o,textFontSizeSM:o,textFontWeight:"normal",statusSize:o/2}},Vo=H("Badge",(e=>(e=>{const{componentCls:t,iconCls:n,antCls:o,badgeShadowSize:r,textFontSize:a,textFontSizeSM:i,statusSize:l,dotSize:s,textFontWeight:c,indicatorHeight:u,indicatorHeightSM:d,marginXS:p,calc:m}=e,f=`${o}-scroll-number`,g=Jt(e,((e,{darkColor:n})=>({[`&${t} ${t}-color-${e}`]:{background:n,[`&:not(${t}-count)`]:{color:n},"a:hover &":{background:n}}})));return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},V(e)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,[`${t}-count`]:{display:"inline-flex",justifyContent:"center",zIndex:e.indicatorZIndex,minWidth:u,height:u,color:e.badgeTextColor,fontWeight:c,fontSize:a,lineHeight:L(u),whiteSpace:"nowrap",textAlign:"center",background:e.badgeColor,borderRadius:m(u).div(2).equal(),boxShadow:`0 0 0 ${L(r)} ${e.badgeShadowColor}`,transition:`background ${e.motionDurationMid}`,a:{color:e.badgeTextColor},"a:hover":{color:e.badgeTextColor},"a:hover &":{background:e.badgeColorHover}},[`${t}-count-sm`]:{minWidth:d,height:d,fontSize:i,lineHeight:L(d),borderRadius:m(d).div(2).equal()},[`${t}-multiple-words`]:{padding:`0 ${L(e.paddingXS)}`,bdi:{unicodeBidi:"plaintext"}},[`${t}-dot`]:{zIndex:e.indicatorZIndex,width:s,minWidth:s,height:s,background:e.badgeColor,borderRadius:"100%",boxShadow:`0 0 0 ${L(r)} ${e.badgeShadowColor}`},[`${t}-count, ${t}-dot, ${f}-custom-component`]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",[`&${n}-spin`]:{animationName:Ho,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&${t}-status`]:{lineHeight:"inherit",verticalAlign:"baseline",[`${t}-status-dot`]:{position:"relative",top:-1,display:"inline-block",width:l,height:l,verticalAlign:"middle",borderRadius:"50%"},[`${t}-status-success`]:{backgroundColor:e.colorSuccess},[`${t}-status-processing`]:{overflow:"visible",color:e.colorInfo,backgroundColor:e.colorInfo,borderColor:"currentcolor","&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:r,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:Ao,animationDuration:e.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},[`${t}-status-default`]:{backgroundColor:e.colorTextPlaceholder},[`${t}-status-error`]:{backgroundColor:e.colorError},[`${t}-status-warning`]:{backgroundColor:e.colorWarning},[`${t}-status-text`]:{marginInlineStart:p,color:e.colorText,fontSize:e.fontSize}}}),g),{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:Mo,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`${t}-zoom-leave`]:{animationName:zo,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`&${t}-not-a-wrapper`]:{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:Do,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`${t}-zoom-leave`]:{animationName:Lo,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`&:not(${t}-status)`]:{verticalAlign:"middle"},[`${f}-custom-component, ${t}-count`]:{transform:"none"},[`${f}-custom-component, ${f}`]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[f]:{overflow:"hidden",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack}`,[`${f}-only`]:{position:"relative",display:"inline-block",height:u,transition:`all ${e.motionDurationSlow} ${e.motionEaseOutBack}`,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",[`> p${f}-only-unit`]:{height:u,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},[`${f}-symbol`]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",[`${t}-count, ${t}-dot, ${f}-custom-component`]:{transform:"translate(-50%, -50%)"}}})}})(Fo(e))),Wo),qo=H(["Badge","Ribbon"],(e=>(e=>{const{antCls:t,badgeFontHeight:n,marginXS:o,badgeRibbonOffset:r,calc:a}=e,i=`${t}-ribbon`,l=`${t}-ribbon-wrapper`,s=Jt(e,((e,{darkColor:t})=>({[`&${i}-color-${e}`]:{background:t,color:t}})));return{[l]:{position:"relative"},[i]:Object.assign(Object.assign(Object.assign(Object.assign({},V(e)),{position:"absolute",top:o,padding:`0 ${L(e.paddingXS)}`,color:e.colorPrimary,lineHeight:L(n),whiteSpace:"nowrap",backgroundColor:e.colorPrimary,borderRadius:e.borderRadiusSM,[`${i}-text`]:{color:e.badgeTextColor},[`${i}-corner`]:{position:"absolute",top:"100%",width:r,height:r,color:"currentcolor",border:`${L(a(r).div(2).equal())} solid`,transform:e.badgeRibbonCornerTransform,transformOrigin:"top",filter:e.badgeRibbonCornerFilter}}),s),{[`&${i}-placement-end`]:{insetInlineEnd:a(r).mul(-1).equal(),borderEndEndRadius:0,[`${i}-corner`]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},[`&${i}-placement-start`]:{insetInlineStart:a(r).mul(-1).equal(),borderEndStartRadius:0,[`${i}-corner`]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}})(Fo(e))),Wo),Xo=t=>{const{prefixCls:n,value:o,current:r,offset:a=0}=t;let i;return a&&(i={position:"absolute",top:`${a}00%`,left:0}),e.createElement("span",{style:i,className:C(`${n}-only-unit`,{current:r})},o)};function Go(e,t,n){let o=e,r=0;for(;(o+10)%10!==t;)o+=n,r+=n;return r}const Ko=t=>{const{prefixCls:n,count:o,value:r}=t,a=Number(r),i=Math.abs(o),[l,s]=e.useState(a),[c,u]=e.useState(i),d=()=>{s(a),u(i)};let p,m;if(e.useEffect((()=>{const e=setTimeout(d,1e3);return()=>clearTimeout(e)}),[a]),l===a||Number.isNaN(a)||Number.isNaN(l))p=[e.createElement(Xo,Object.assign({},t,{key:a,current:!0}))],m={transition:"none"};else{p=[];const n=a+10,o=[];for(let e=a;e<=n;e+=1)o.push(e);const r=c<i?1:-1,s=o.findIndex((e=>e%10===l));p=(r<0?o.slice(0,s+1):o.slice(s)).map(((n,o)=>{const a=n%10;return e.createElement(Xo,Object.assign({},t,{key:n,value:a,offset:r<0?o-s:o,current:o===s}))})),m={transform:`translateY(${-Go(l,a,r)}00%)`}}return e.createElement("span",{className:`${n}-only`,style:m,onTransitionEnd:d},p)};const Yo=e.forwardRef(((t,n)=>{const{prefixCls:o,count:r,className:a,motionClassName:i,style:l,title:s,show:c,component:u="sup",children:d}=t,p=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:m}=e.useContext(A),f=m("scroll-number",o),g=Object.assign(Object.assign({},p),{"data-show":c,style:l,className:C(f,a,i),title:s});let h=r;if(r&&Number(r)%1==0){const t=String(r).split("");h=e.createElement("bdi",null,t.map(((n,o)=>e.createElement(Ko,{prefixCls:f,count:Number(r),value:n,key:t.length-o}))))}return(null==l?void 0:l.borderColor)&&(g.style=Object.assign(Object.assign({},l),{boxShadow:`0 0 0 1px ${l.borderColor} inset`})),d?J(d,(e=>({className:C(`${f}-custom-component`,null==e?void 0:e.className,i)}))):e.createElement(u,Object.assign({},g,{ref:n}),h)}));const Uo=e.forwardRef(((t,n)=>{var o,r,a,i,l;const{prefixCls:s,scrollNumberPrefixCls:c,children:u,status:d,text:p,color:m,count:f=null,overflowCount:g=99,dot:h=!1,size:b="default",title:v,offset:y,style:w,className:x,rootClassName:$,classNames:E,styles:S,showZero:O=!1}=t,k=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:I,direction:j,badge:R}=e.useContext(A),B=I("badge",s),[N,T,P]=Vo(B),M=f>g?`${g}+`:f,z="0"===M||0===M,D=(null!=d||null!=m)&&(null===f||z&&!O),L=h&&!z,H=L?"":M,F=e.useMemo((()=>(null==H||""===H||z&&!O)&&!L),[H,z,O,L]),W=e.useRef(f);F||(W.current=f);const V=W.current,q=e.useRef(H);F||(q.current=H);const X=q.current,G=e.useRef(L);F||(G.current=L);const K=e.useMemo((()=>{if(!y)return Object.assign(Object.assign({},null==R?void 0:R.style),w);const e={marginTop:y[1]};return"rtl"===j?e.left=parseInt(y[0],10):e.right=-parseInt(y[0],10),Object.assign(Object.assign(Object.assign({},e),null==R?void 0:R.style),w)}),[j,y,w,null==R?void 0:R.style]),Y=null!=v?v:"string"==typeof V||"number"==typeof V?V:void 0,U=F||!p?null:e.createElement("span",{className:`${B}-status-text`},p),Q=V&&"object"==typeof V?J(V,(e=>({style:Object.assign(Object.assign({},K),e.style)}))):void 0,Z=jo(m,!1),ee=C(null==E?void 0:E.indicator,null===(o=null==R?void 0:R.classNames)||void 0===o?void 0:o.indicator,{[`${B}-status-dot`]:D,[`${B}-status-${d}`]:!!d,[`${B}-color-${m}`]:Z}),te={};m&&!Z&&(te.color=m,te.background=m);const ne=C(B,{[`${B}-status`]:D,[`${B}-not-a-wrapper`]:!u,[`${B}-rtl`]:"rtl"===j},x,$,null==R?void 0:R.className,null===(r=null==R?void 0:R.classNames)||void 0===r?void 0:r.root,null==E?void 0:E.root,T,P);if(!u&&D){const t=K.color;return N(e.createElement("span",Object.assign({},k,{className:ne,style:Object.assign(Object.assign(Object.assign({},null==S?void 0:S.root),null===(a=null==R?void 0:R.styles)||void 0===a?void 0:a.root),K)}),e.createElement("span",{className:ee,style:Object.assign(Object.assign(Object.assign({},null==S?void 0:S.indicator),null===(i=null==R?void 0:R.styles)||void 0===i?void 0:i.indicator),te)}),p&&e.createElement("span",{style:{color:t},className:`${B}-status-text`},p)))}return N(e.createElement("span",Object.assign({ref:n},k,{className:ne,style:Object.assign(Object.assign({},null===(l=null==R?void 0:R.styles)||void 0===l?void 0:l.root),null==S?void 0:S.root)}),u,e.createElement(_,{visible:!F,motionName:`${B}-zoom`,motionAppear:!1,motionDeadline:1e3},(({className:t})=>{var n,o;const r=I("scroll-number",c),a=G.current,i=C(null==E?void 0:E.indicator,null===(n=null==R?void 0:R.classNames)||void 0===n?void 0:n.indicator,{[`${B}-dot`]:a,[`${B}-count`]:!a,[`${B}-count-sm`]:"small"===b,[`${B}-multiple-words`]:!a&&X&&X.toString().length>1,[`${B}-status-${d}`]:!!d,[`${B}-color-${m}`]:Z});let l=Object.assign(Object.assign(Object.assign({},null==S?void 0:S.indicator),null===(o=null==R?void 0:R.styles)||void 0===o?void 0:o.indicator),K);return m&&!Z&&(l=l||{},l.background=m),e.createElement(Yo,{prefixCls:r,show:!F,motionClassName:t,className:i,count:X,title:Y,style:l,key:"scrollNumber"},Q)})),U))}));Uo.Ribbon=t=>{const{className:n,prefixCls:o,style:r,color:a,children:i,text:l,placement:s="end",rootClassName:c}=t,{getPrefixCls:u,direction:d}=e.useContext(A),p=u("ribbon",o),m=`${p}-wrapper`,[f,g,h]=qo(p,m),b=jo(a,!1),v=C(p,`${p}-placement-${s}`,{[`${p}-rtl`]:"rtl"===d,[`${p}-color-${a}`]:b},n),y={},w={};return a&&!b&&(y.background=a,w.color=a),f(e.createElement("div",{className:C(m,c,g,h)},i,e.createElement("div",{className:C(v,g),style:Object.assign(Object.assign({},y),r)},e.createElement("span",{className:`${p}-text`},l),e.createElement("div",{className:`${p}-corner`,style:w}))))};var Qo={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"},Zo=function(t,n){return e.createElement(D,p({},t,{ref:n,icon:Qo}))},Jo=e.forwardRef(Zo),er=e.createContext(null);function tr(e,t){return void 0===e?null:"".concat(e,"-").concat(t)}function nr(t){return tr(e.useContext(er),t)}var or=["children","locked"],rr=e.createContext(null);function ar(t){var n=t.children,o=t.locked,r=x(t,or),a=e.useContext(rr),i=te((function(){return e=r,t=s({},a),Object.keys(e).forEach((function(n){var o=e[n];void 0!==o&&(t[n]=o)})),t;var e,t}),[a,r],(function(e,t){return!(o||e[0]===t[0]&&ne(e[1],t[1],!0))}));return e.createElement(rr.Provider,{value:i},n)}var ir=[],lr=e.createContext(null);function sr(){return e.useContext(lr)}var cr=e.createContext(ir);function ur(t){var n=e.useContext(cr);return e.useMemo((function(){return void 0!==t?[].concat(f(n),[t]):n}),[n,t])}var dr=e.createContext(null),pr=e.createContext({});function mr(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(B(e)){var n=e.nodeName.toLowerCase(),o=["input","select","textarea","button"].includes(n)||e.isContentEditable||"a"===n&&!!e.getAttribute("href"),r=e.getAttribute("tabindex"),a=Number(r),i=null;return r&&!Number.isNaN(a)?i=a:o&&null===i&&(i=0),o&&e.disabled&&(i=null),null!==i&&(i>=0||t&&i<0)}return!1}var fr=oe.LEFT,gr=oe.RIGHT,hr=oe.UP,br=oe.DOWN,vr=oe.ENTER,yr=oe.ESC,wr=oe.HOME,xr=oe.END,Cr=[hr,br,fr,gr];function $r(e,t){var n=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=f(e.querySelectorAll("*")).filter((function(e){return mr(e,t)}));return mr(e,t)&&n.unshift(e),n}(e,!0);return n.filter((function(e){return t.has(e)}))}function Er(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;if(!e)return null;var r=$r(e,t),a=r.length,i=r.findIndex((function(e){return n===e}));return o<0?-1===i?i=a-1:i-=1:o>0&&(i+=1),r[i=(i+a)%a]}var Sr=function(e,t){var n=new Set,o=new Map,r=new Map;return e.forEach((function(e){var a=document.querySelector("[data-menu-id='".concat(tr(t,e),"']"));a&&(n.add(a),r.set(a,e),o.set(e,a))})),{elements:n,key2element:o,element2key:r}};function Or(t,n,o,r,a,i,l,s,c,u){var d=e.useRef(),p=e.useRef();p.current=n;var f=function(){m.cancel(d.current)};return e.useEffect((function(){return function(){f()}}),[]),function(e){var g=e.which;if([].concat(Cr,[vr,yr,wr,xr]).includes(g)){var h=i(),b=Sr(h,r),v=b,y=v.elements,w=v.key2element,x=v.element2key,C=function(e,t){for(var n=e||document.activeElement;n;){if(t.has(n))return n;n=n.parentElement}return null}(w.get(n),y),$=x.get(C),E=function(e,t,n,o){var r,a="prev",i="next",l="children",s="parent";if("inline"===e&&o===vr)return{inlineTrigger:!0};var c=re(re({},hr,a),br,i),u=re(re(re(re({},fr,n?i:a),gr,n?a:i),br,l),vr,l),d=re(re(re(re(re(re({},hr,a),br,i),vr,l),yr,s),fr,n?l:s),gr,n?s:l);switch(null===(r={inline:c,horizontal:u,vertical:d,inlineSub:c,horizontalSub:d,verticalSub:d}["".concat(e).concat(t?"":"Sub")])||void 0===r?void 0:r[o]){case a:return{offset:-1,sibling:!0};case i:return{offset:1,sibling:!0};case s:return{offset:-1,sibling:!1};case l:return{offset:1,sibling:!1};default:return null}}(t,1===l($,!0).length,o,g);if(!E&&g!==wr&&g!==xr)return;(Cr.includes(g)||[wr,xr].includes(g))&&e.preventDefault();var S=function(e){if(e){var t=e,n=e.querySelector("a");null!=n&&n.getAttribute("href")&&(t=n);var o=x.get(e);s(o),f(),d.current=m((function(){p.current===o&&t.focus()}))}};if([wr,xr].includes(g)||E.sibling||!C){var O,k,_=$r(O=C&&"inline"!==t?function(e){for(var t=e;t;){if(t.getAttribute("data-menu-list"))return t;t=t.parentElement}return null}(C):a.current,y);k=g===wr?_[0]:g===xr?_[_.length-1]:Er(O,y,C,E.offset),S(k)}else if(E.inlineTrigger)c($);else if(E.offset>0)c($,!0),f(),d.current=m((function(){b=Sr(h,r);var e=C.getAttribute("aria-controls"),t=Er(document.getElementById(e),b.elements);S(t)}),5);else if(E.offset<0){var I=l($,!0),j=I[I.length-2],R=w.get(j);c(j,!1),S(R)}}null==u||u(e)}}var kr="__RC_UTIL_PATH_SPLIT__",_r=function(e){return e.join(kr)},Ir="rc-menu-more";function jr(){var t=e.useState({}),n=$(t,2)[1],o=e.useRef(new Map),r=e.useRef(new Map),a=e.useState([]),i=$(a,2),l=i[0],s=i[1],c=e.useRef(0),u=e.useRef(!1),d=e.useCallback((function(e,t){var a=_r(t);r.current.set(a,e),o.current.set(e,a),c.current+=1;var i,l=c.current;i=function(){l===c.current&&(u.current||n({}))},Promise.resolve().then(i)}),[]),p=e.useCallback((function(e,t){var n=_r(t);r.current.delete(n),o.current.delete(e)}),[]),m=e.useCallback((function(e){s(e)}),[]),g=e.useCallback((function(e,t){var n=o.current.get(e)||"",r=n.split(kr);return t&&l.includes(r[0])&&r.unshift(Ir),r}),[l]),h=e.useCallback((function(e,t){return e.filter((function(e){return void 0!==e})).some((function(e){return g(e,!0).includes(t)}))}),[g]),b=e.useCallback((function(e){var t="".concat(o.current.get(e)).concat(kr),n=new Set;return f(r.current.keys()).forEach((function(e){e.startsWith(t)&&n.add(r.current.get(e))})),n}),[]);return e.useEffect((function(){return function(){u.current=!0}}),[]),{registerPath:d,unregisterPath:p,refreshOverflowKeys:m,isSubPathKey:h,getKeyPath:g,getKeys:function(){var e=f(o.current.keys());return l.length&&e.push(Ir),e},getSubPathKeys:b}}function Rr(t){var n=e.useRef(t);n.current=t;var o=e.useCallback((function(){for(var e,t=arguments.length,o=new Array(t),r=0;r<t;r++)o[r]=arguments[r];return null===(e=n.current)||void 0===e?void 0:e.call.apply(e,[n].concat(o))}),[]);return t?o:void 0}var Br=Math.random().toFixed(5).toString().slice(2),Nr=0;function Tr(t,n,o,r){var a=e.useContext(rr),i=a.activeKey,l=a.onActive,s=a.onInactive,c={active:i===t};return n||(c.onMouseEnter=function(e){null==o||o({key:t,domEvent:e}),l(t)},c.onMouseLeave=function(e){null==r||r({key:t,domEvent:e}),s(t)}),c}function Pr(t){var n=e.useContext(rr),o=n.mode,r=n.rtl,a=n.inlineIndent;if("inline"!==o)return null;return r?{paddingRight:t*a}:{paddingLeft:t*a}}function Ar(t){var n,o=t.icon,r=t.props,a=t.children;return null===o||!1===o?null:("function"==typeof o?n=e.createElement(o,s({},r)):"boolean"!=typeof o&&(n=o),n||a||null)}var Mr=["item"];function zr(e){var t=e.item,n=x(e,Mr);return Object.defineProperty(n,"item",{get:function(){return ae(!1,"`info.item` is deprecated since we will move to function component that not provides React Node instance in future."),t}}),n}var Dr=["title","attribute","elementRef"],Lr=["style","className","eventKey","warnKey","disabled","itemIcon","children","role","onMouseEnter","onMouseLeave","onClick","onKeyDown","onFocus"],Hr=["active"],Fr=function(a){t(l,a);var i=n(l);function l(){return o(this,l),i.apply(this,arguments)}return r(l,[{key:"render",value:function(){var t=this.props,n=t.title,o=t.attribute,r=t.elementRef,a=x(t,Dr),i=ie(a,["eventKey","popupClassName","popupOffset","onTitleClick"]);return ae(!o,"`attribute` of Menu.Item is deprecated. Please pass attribute directly."),e.createElement(Rn.Item,p({},o,{title:"string"==typeof n?n:void 0},i,{ref:r}))}}]),l}(e.Component),Wr=e.forwardRef((function(t,n){var o=t.style,r=t.className,a=t.eventKey;t.warnKey;var i=t.disabled,c=t.itemIcon,u=t.children,d=t.role,m=t.onMouseEnter,g=t.onMouseLeave,h=t.onClick,b=t.onKeyDown,v=t.onFocus,y=x(t,Lr),w=nr(a),$=e.useContext(rr),E=$.prefixCls,S=$.onItemClick,O=$.disabled,k=$.overflowDisabled,_=$.itemIcon,I=$.selectedKeys,j=$.onActive,R=e.useContext(pr)._internalRenderMenuItem,B="".concat(E,"-item"),N=e.useRef(),T=e.useRef(),P=O||i,A=l(n,T),M=ur(a),z=function(e){return{key:a,keyPath:f(M).reverse(),item:N.current,domEvent:e}},D=c||_,L=Tr(a,P,m,g),H=L.active,F=x(L,Hr),W=I.includes(a),V=Pr(M.length),q={};"option"===t.role&&(q["aria-selected"]=W);var X=e.createElement(Fr,p({ref:N,elementRef:A,role:null===d?"none":d||"menuitem",tabIndex:i?null:-1,"data-menu-id":k&&w?null:w},ie(y,["extra"]),F,q,{component:"li","aria-disabled":i,style:s(s({},V),o),className:C(B,re(re(re({},"".concat(B,"-active"),H),"".concat(B,"-selected"),W),"".concat(B,"-disabled"),P),r),onClick:function(e){if(!P){var t=z(e);null==h||h(zr(t)),S(t)}},onKeyDown:function(e){if(null==b||b(e),e.which===oe.ENTER){var t=z(e);null==h||h(zr(t)),S(t)}},onFocus:function(e){j(a),null==v||v(e)}}),u,e.createElement(Ar,{props:s(s({},t),{},{isSelected:W}),icon:D}));return R&&(X=R(X,t,{selected:W})),X}));function Vr(t,n){var o=t.eventKey,r=sr(),a=ur(o);return e.useEffect((function(){if(r)return r.registerPath(o,a),function(){r.unregisterPath(o,a)}}),[a]),r?null:e.createElement(Wr,p({},t,{ref:n}))}const qr=e.forwardRef(Vr);var Xr=["className","children"],Gr=function(t,n){var o=t.className,r=t.children,a=x(t,Xr),i=e.useContext(rr),l=i.prefixCls,s=i.mode,c=i.rtl;return e.createElement("ul",p({className:C(l,c&&"".concat(l,"-rtl"),"".concat(l,"-sub"),"".concat(l,"-").concat("inline"===s?"inline":"vertical"),o),role:"menu"},a,{"data-menu-list":!0,ref:n}),r)},Kr=e.forwardRef(Gr);function Yr(t,n){return d(t).map((function(t,o){if(e.isValidElement(t)){var r,a,i=t.key,l=null!==(r=null===(a=t.props)||void 0===a?void 0:a.eventKey)&&void 0!==r?r:i;null==l&&(l="tmp_key-".concat([].concat(f(n),[o]).join("-")));var s={key:l,eventKey:l};return e.cloneElement(t,s)}return t}))}Kr.displayName="SubMenuList";var Ur={adjustX:1,adjustY:1},Qr={topLeft:{points:["bl","tl"],overflow:Ur},topRight:{points:["br","tr"],overflow:Ur},bottomLeft:{points:["tl","bl"],overflow:Ur},bottomRight:{points:["tr","br"],overflow:Ur},leftTop:{points:["tr","tl"],overflow:Ur},leftBottom:{points:["br","bl"],overflow:Ur},rightTop:{points:["tl","tr"],overflow:Ur},rightBottom:{points:["bl","br"],overflow:Ur}},Zr={topLeft:{points:["bl","tl"],overflow:Ur},topRight:{points:["br","tr"],overflow:Ur},bottomLeft:{points:["tl","bl"],overflow:Ur},bottomRight:{points:["tr","br"],overflow:Ur},rightTop:{points:["tr","tl"],overflow:Ur},rightBottom:{points:["br","bl"],overflow:Ur},leftTop:{points:["tl","tr"],overflow:Ur},leftBottom:{points:["bl","br"],overflow:Ur}};function Jr(e,t,n){return t||(n?n[e]||n.other:void 0)}var ea={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"};function ta(t){var n=t.prefixCls,o=t.visible,r=t.children,a=t.popup,i=t.popupStyle,l=t.popupClassName,c=t.popupOffset,u=t.disabled,d=t.mode,p=t.onVisibleChange,f=e.useContext(rr),g=f.getPopupContainer,h=f.rtl,b=f.subMenuOpenDelay,v=f.subMenuCloseDelay,y=f.builtinPlacements,w=f.triggerSubMenuAction,x=f.forceSubMenuRender,E=f.rootClassName,S=f.motion,O=f.defaultMotions,k=e.useState(!1),_=$(k,2),I=_[0],j=_[1],R=s(s({},h?Zr:Qr),y),B=ea[d],N=Jr(d,S,O),T=e.useRef(N);"inline"!==d&&(T.current=N);var P=s(s({},T.current),{},{leavedClassName:"".concat(n,"-hidden"),removeOnLeave:!1,motionAppear:!0}),A=e.useRef();return e.useEffect((function(){return A.current=m((function(){j(o)})),function(){m.cancel(A.current)}}),[o]),e.createElement(Zn,{prefixCls:n,popupClassName:C("".concat(n,"-popup"),re({},"".concat(n,"-rtl"),h),l,E),stretch:"horizontal"===d?"minWidth":null,getPopupContainer:g,builtinPlacements:R,popupPlacement:B,popupVisible:I,popup:a,popupStyle:i,popupAlign:c&&{offset:c},action:u?[]:[w],mouseEnterDelay:b,mouseLeaveDelay:v,onPopupVisibleChange:p,forceRender:x,popupMotion:P,fresh:!0},r)}function na(t){var n=t.id,o=t.open,r=t.keyPath,a=t.children,i="inline",l=e.useContext(rr),c=l.prefixCls,u=l.forceSubMenuRender,d=l.motion,m=l.defaultMotions,f=l.mode,g=e.useRef(!1);g.current=f===i;var h=e.useState(!g.current),b=$(h,2),v=b[0],y=b[1],w=!!g.current&&o;e.useEffect((function(){g.current&&y(!1)}),[f]);var x=s({},Jr(i,d,m));r.length>1&&(x.motionAppear=!1);var C=x.onVisibleChanged;return x.onVisibleChanged=function(e){return g.current||e||y(!0),null==C?void 0:C(e)},v?null:e.createElement(ar,{mode:i,locked:!g.current},e.createElement(_,p({visible:w},x,{forceRender:u,removeOnLeave:!1,leavedClassName:"".concat(c,"-hidden")}),(function(t){var o=t.className,r=t.style;return e.createElement(Kr,{id:n,className:o,style:r},a)})))}var oa=["style","className","title","eventKey","warnKey","disabled","internalPopupClose","children","itemIcon","expandIcon","popupClassName","popupOffset","popupStyle","onClick","onMouseEnter","onMouseLeave","onTitleClick","onTitleMouseEnter","onTitleMouseLeave"],ra=["active"],aa=e.forwardRef((function(t,n){var o=t.style,r=t.className,a=t.title,i=t.eventKey;t.warnKey;var l=t.disabled,c=t.internalPopupClose,u=t.children,d=t.itemIcon,m=t.expandIcon,f=t.popupClassName,g=t.popupOffset,h=t.popupStyle,b=t.onClick,v=t.onMouseEnter,y=t.onMouseLeave,w=t.onTitleClick,E=t.onTitleMouseEnter,S=t.onTitleMouseLeave,O=x(t,oa),k=nr(i),_=e.useContext(rr),I=_.prefixCls,j=_.mode,R=_.openKeys,B=_.disabled,N=_.overflowDisabled,T=_.activeKey,P=_.selectedKeys,A=_.itemIcon,M=_.expandIcon,z=_.onItemClick,D=_.onOpenChange,L=_.onActive,H=e.useContext(pr)._internalRenderSubMenuItem,F=e.useContext(dr).isSubPathKey,W=ur(),V="".concat(I,"-submenu"),q=B||l,X=e.useRef(),G=e.useRef(),K=null!=d?d:A,Y=null!=m?m:M,U=R.includes(i),Q=!N&&U,Z=F(P,i),J=Tr(i,q,E,S),ee=J.active,te=x(J,ra),ne=e.useState(!1),oe=$(ne,2),ae=oe[0],ie=oe[1],le=function(e){q||ie(e)},se=e.useMemo((function(){return ee||"inline"!==j&&(ae||F([T],i))}),[j,ee,T,ae,i,F]),ce=Pr(W.length),ue=Rr((function(e){null==b||b(zr(e)),z(e)})),de=k&&"".concat(k,"-popup"),pe=e.useMemo((function(){return e.createElement(Ar,{icon:"horizontal"!==j?Y:void 0,props:s(s({},t),{},{isOpen:Q,isSubMenu:!0})},e.createElement("i",{className:"".concat(V,"-arrow")}))}),[j,Y,t,Q,V]),me=e.createElement("div",p({role:"menuitem",style:ce,className:"".concat(V,"-title"),tabIndex:q?null:-1,ref:X,title:"string"==typeof a?a:null,"data-menu-id":N&&k?null:k,"aria-expanded":Q,"aria-haspopup":!0,"aria-controls":de,"aria-disabled":q,onClick:function(e){q||(null==w||w({key:i,domEvent:e}),"inline"===j&&D(i,!U))},onFocus:function(){L(i)}},te),a,pe),fe=e.useRef(j);if("inline"!==j&&W.length>1?fe.current="vertical":fe.current=j,!N){var ge=fe.current;me=e.createElement(ta,{mode:ge,prefixCls:V,visible:!c&&Q&&"inline"!==j,popupClassName:f,popupOffset:g,popupStyle:h,popup:e.createElement(ar,{mode:"horizontal"===ge?"vertical":ge},e.createElement(Kr,{id:de,ref:G},u)),disabled:q,onVisibleChange:function(e){"inline"!==j&&D(i,e)}},me)}var he=e.createElement(Rn.Item,p({ref:n,role:"none"},O,{component:"li",style:o,className:C(V,"".concat(V,"-").concat(j),r,re(re(re(re({},"".concat(V,"-open"),Q),"".concat(V,"-active"),se),"".concat(V,"-selected"),Z),"".concat(V,"-disabled"),q)),onMouseEnter:function(e){le(!0),null==v||v({key:i,domEvent:e})},onMouseLeave:function(e){le(!1),null==y||y({key:i,domEvent:e})}}),me,!N&&e.createElement(na,{id:de,open:Q,keyPath:W},u));return H&&(he=H(he,t,{selected:Z,active:se,open:Q,disabled:q})),e.createElement(ar,{onItemClick:ue,mode:"horizontal"===j?"vertical":j,itemIcon:K,expandIcon:Y},he)})),ia=e.forwardRef((function(t,n){var o,r=t.eventKey,a=t.children,i=ur(r),l=Yr(a,i),s=sr();return e.useEffect((function(){if(s)return s.registerPath(r,i),function(){s.unregisterPath(r,i)}}),[i]),o=s?l:e.createElement(aa,p({ref:n},t),l),e.createElement(cr.Provider,{value:i},o)}));function la(t){var n=t.className,o=t.style,r=e.useContext(rr).prefixCls;return sr()?null:e.createElement("li",{role:"separator",className:C("".concat(r,"-item-divider"),n),style:o})}var sa=["className","title","eventKey","children"],ca=e.forwardRef((function(t,n){var o=t.className,r=t.title;t.eventKey;var a=t.children,i=x(t,sa),l=e.useContext(rr).prefixCls,s="".concat(l,"-item-group");return e.createElement("li",p({ref:n,role:"presentation"},i,{onClick:function(e){return e.stopPropagation()},className:C(s,o)}),e.createElement("div",{role:"presentation",className:"".concat(s,"-title"),title:"string"==typeof r?r:void 0},r),e.createElement("ul",{role:"group",className:"".concat(s,"-list")},a))})),ua=e.forwardRef((function(t,n){var o=t.eventKey,r=Yr(t.children,ur(o));return sr()?r:e.createElement(ca,p({ref:n},ie(t,["warnKey"])),r)})),da=["label","children","key","type","extra"];function pa(t,n,o){var r=n.item,a=n.group,i=n.submenu,l=n.divider;return(t||[]).map((function(t,s){if(t&&"object"===u(t)){var c=t,d=c.label,m=c.children,f=c.key,g=c.type,h=c.extra,b=x(c,da),v=null!=f?f:"tmp-".concat(s);return m||"group"===g?"group"===g?e.createElement(a,p({key:v},b,{title:d}),pa(m,n,o)):e.createElement(i,p({key:v},b,{title:d}),pa(m,n,o)):"divider"===g?e.createElement(l,p({key:v},b)):e.createElement(r,p({key:v},b,{extra:h}),d,(!!h||0===h)&&e.createElement("span",{className:"".concat(o,"-item-extra")},h))}return null})).filter((function(e){return e}))}function ma(e,t,n,o,r){var a=e,i=s({divider:la,item:qr,group:ua,submenu:ia},o);return t&&(a=pa(t,i,r)),Yr(a,n)}var fa=["prefixCls","rootClassName","style","className","tabIndex","items","children","direction","id","mode","inlineCollapsed","disabled","disabledOverflow","subMenuOpenDelay","subMenuCloseDelay","forceSubMenuRender","defaultOpenKeys","openKeys","activeKey","defaultActiveFirst","selectable","multiple","defaultSelectedKeys","selectedKeys","onSelect","onDeselect","inlineIndent","motion","defaultMotions","triggerSubMenuAction","builtinPlacements","itemIcon","expandIcon","overflowedIndicator","overflowedIndicatorPopupClassName","getPopupContainer","onClick","onOpenChange","onKeyDown","openAnimation","openTransitionName","_internalRenderMenuItem","_internalRenderSubMenuItem","_internalComponents"],ga=[],ha=e.forwardRef((function(t,n){var o,r=t,a=r.prefixCls,i=void 0===a?"rc-menu":a,l=r.rootClassName,c=r.style,u=r.className,d=r.tabIndex,m=void 0===d?0:d,g=r.items,h=r.children,b=r.direction,v=r.id,y=r.mode,w=void 0===y?"vertical":y,E=r.inlineCollapsed,O=r.disabled,k=r.disabledOverflow,_=r.subMenuOpenDelay,I=void 0===_?.1:_,j=r.subMenuCloseDelay,R=void 0===j?.1:j,B=r.forceSubMenuRender,N=r.defaultOpenKeys,T=r.openKeys,P=r.activeKey,A=r.defaultActiveFirst,M=r.selectable,z=void 0===M||M,D=r.multiple,L=void 0!==D&&D,H=r.defaultSelectedKeys,F=r.selectedKeys,W=r.onSelect,V=r.onDeselect,q=r.inlineIndent,X=void 0===q?24:q,G=r.motion,Y=r.defaultMotions,U=r.triggerSubMenuAction,Q=void 0===U?"hover":U,Z=r.builtinPlacements,J=r.itemIcon,ee=r.expandIcon,te=r.overflowedIndicator,oe=void 0===te?"...":te,ae=r.overflowedIndicatorPopupClassName,ie=r.getPopupContainer,le=r.onClick,se=r.onOpenChange,ce=r.onKeyDown;r.openAnimation,r.openTransitionName;var ue=r._internalRenderMenuItem,de=r._internalRenderSubMenuItem,pe=r._internalComponents,me=x(r,fa),fe=e.useMemo((function(){return[ma(h,g,ga,pe,i),ma(h,g,ga,{},i)]}),[h,g,pe]),ge=$(fe,2),he=ge[0],be=ge[1],ve=e.useState(!1),ye=$(ve,2),we=ye[0],xe=ye[1],Ce=e.useRef(),$e=function(t){var n=K(t,{value:t}),o=$(n,2),r=o[0],a=o[1];return e.useEffect((function(){Nr+=1;var e="".concat(Br,"-").concat(Nr);a("rc-menu-uuid-".concat(e))}),[]),r}(v),Ee="rtl"===b,Se=K(N,{value:T,postState:function(e){return e||ga}}),Oe=$(Se,2),ke=Oe[0],_e=Oe[1],Ie=function(e){function t(){_e(e),null==se||se(e)}arguments.length>1&&void 0!==arguments[1]&&arguments[1]?S.flushSync(t):t()},je=e.useState(ke),Re=$(je,2),Be=Re[0],Ne=Re[1],Te=e.useRef(!1),Pe=e.useMemo((function(){return"inline"!==w&&"vertical"!==w||!E?[w,!1]:["vertical",E]}),[w,E]),Ae=$(Pe,2),Me=Ae[0],ze=Ae[1],De="inline"===Me,Le=e.useState(Me),He=$(Le,2),Fe=He[0],We=He[1],Ve=e.useState(ze),qe=$(Ve,2),Xe=qe[0],Ge=qe[1];e.useEffect((function(){We(Me),Ge(ze),Te.current&&(De?_e(Be):Ie(ga))}),[Me,ze]);var Ke=e.useState(0),Ye=$(Ke,2),Ue=Ye[0],Qe=Ye[1],Ze=Ue>=he.length-1||"horizontal"!==Fe||k;e.useEffect((function(){De&&Ne(ke)}),[ke]),e.useEffect((function(){return Te.current=!0,function(){Te.current=!1}}),[]);var Je=jr(),et=Je.registerPath,tt=Je.unregisterPath,nt=Je.refreshOverflowKeys,ot=Je.isSubPathKey,rt=Je.getKeyPath,at=Je.getKeys,it=Je.getSubPathKeys,lt=e.useMemo((function(){return{registerPath:et,unregisterPath:tt}}),[et,tt]),st=e.useMemo((function(){return{isSubPathKey:ot}}),[ot]);e.useEffect((function(){nt(Ze?ga:he.slice(Ue+1).map((function(e){return e.key})))}),[Ue,Ze]);var ct=K(P||A&&(null===(o=he[0])||void 0===o?void 0:o.key),{value:P}),ut=$(ct,2),dt=ut[0],pt=ut[1],mt=Rr((function(e){pt(e)})),ft=Rr((function(){pt(void 0)}));e.useImperativeHandle(n,(function(){return{list:Ce.current,focus:function(e){var t,n,o=at(),r=Sr(o,$e),a=r.elements,i=r.key2element,l=r.element2key,s=$r(Ce.current,a),c=null!=dt?dt:s[0]?l.get(s[0]):null===(t=he.find((function(e){return!e.props.disabled})))||void 0===t?void 0:t.key,u=i.get(c);c&&u&&(null==u||null===(n=u.focus)||void 0===n||n.call(u,e))}}}));var gt=K(H||[],{value:F,postState:function(e){return Array.isArray(e)?e:null==e?ga:[e]}}),ht=$(gt,2),bt=ht[0],vt=ht[1],yt=Rr((function(e){null==le||le(zr(e)),function(e){if(z){var t,n=e.key,o=bt.includes(n);t=L?o?bt.filter((function(e){return e!==n})):[].concat(f(bt),[n]):[n],vt(t);var r=s(s({},e),{},{selectedKeys:t});o?null==V||V(r):null==W||W(r)}!L&&ke.length&&"inline"!==Fe&&Ie(ga)}(e)})),wt=Rr((function(e,t){var n=ke.filter((function(t){return t!==e}));if(t)n.push(e);else if("inline"!==Fe){var o=it(e);n=n.filter((function(e){return!o.has(e)}))}ne(ke,n,!0)||Ie(n,!0)})),xt=Or(Fe,dt,Ee,$e,Ce,at,rt,pt,(function(e,t){var n=null!=t?t:!ke.includes(e);wt(e,n)}),ce);e.useEffect((function(){xe(!0)}),[]);var Ct=e.useMemo((function(){return{_internalRenderMenuItem:ue,_internalRenderSubMenuItem:de}}),[ue,de]),$t="horizontal"!==Fe||k?he:he.map((function(t,n){return e.createElement(ar,{key:t.key,overflowDisabled:n>Ue},t)})),Et=e.createElement(Rn,p({id:v,ref:Ce,prefixCls:"".concat(i,"-overflow"),component:"ul",itemComponent:qr,className:C(i,"".concat(i,"-root"),"".concat(i,"-").concat(Fe),u,re(re({},"".concat(i,"-inline-collapsed"),Xe),"".concat(i,"-rtl"),Ee),l),dir:b,style:c,role:"menu",tabIndex:m,data:$t,renderRawItem:function(e){return e},renderRawRest:function(t){var n=t.length,o=n?he.slice(-n):null;return e.createElement(ia,{eventKey:Ir,title:oe,disabled:Ze,internalPopupClose:0===n,popupClassName:ae},o)},maxCount:"horizontal"!==Fe||k?Rn.INVALIDATE:Rn.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:function(e){Qe(e)},onKeyDown:xt},me));return e.createElement(pr.Provider,{value:Ct},e.createElement(er.Provider,{value:$e},e.createElement(ar,{prefixCls:i,rootClassName:l,mode:Fe,openKeys:ke,rtl:Ee,disabled:O,motion:we?G:null,defaultMotions:we?Y:null,activeKey:dt,onActive:mt,onInactive:ft,selectedKeys:bt,inlineIndent:X,subMenuOpenDelay:I,subMenuCloseDelay:R,forceSubMenuRender:B,builtinPlacements:Z,triggerSubMenuAction:Q,getPopupContainer:ie,itemIcon:J,expandIcon:ee,onItemClick:yt,onOpenChange:wt},e.createElement(dr.Provider,{value:st},Et),e.createElement("div",{style:{display:"none"},"aria-hidden":!0},e.createElement(lr.Provider,{value:lt},be)))))}));ha.Item=qr,ha.SubMenu=ia,ha.ItemGroup=ua,ha.Divider=la;var ba={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"},va=function(t,n){return e.createElement(D,p({},t,{ref:n,icon:ba}))},ya=e.forwardRef(va);const wa=e.createContext({siderHook:{addSider:()=>null,removeSider:()=>null}}),xa=e=>{const{antCls:t,componentCls:n,colorText:o,footerBg:r,headerHeight:a,headerPadding:i,headerColor:l,footerPadding:s,fontSize:c,bodyBg:u,headerBg:d}=e;return{[n]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:u,"&, *":{boxSizing:"border-box"},[`&${n}-has-sider`]:{flexDirection:"row",[`> ${n}, > ${n}-content`]:{width:0}},[`${n}-header, &${n}-footer`]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},[`${n}-header`]:{height:a,padding:i,color:l,lineHeight:L(a),background:d,[`${t}-menu`]:{lineHeight:"inherit"}},[`${n}-footer`]:{padding:s,color:o,fontSize:c,background:r},[`${n}-content`]:{flex:"auto",color:o,minHeight:0}}},Ca=e=>{const{colorBgLayout:t,controlHeight:n,controlHeightLG:o,colorText:r,controlHeightSM:a,marginXXS:i,colorTextLightSolid:l,colorBgContainer:s}=e,c=1.25*o;return{colorBgHeader:"#001529",colorBgBody:t,colorBgTrigger:"#002140",bodyBg:t,headerBg:"#001529",headerHeight:2*n,headerPadding:`0 ${c}px`,headerColor:r,footerPadding:`${a}px ${c}px`,footerBg:t,siderBg:"#001529",triggerHeight:o+2*i,triggerBg:"#002140",triggerColor:l,zeroTriggerWidth:o,zeroTriggerHeight:o,lightSiderBg:s,lightTriggerBg:s,lightTriggerColor:r}},$a=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]],Ea=H("Layout",(e=>[xa(e)]),Ca,{deprecatedTokens:$a}),Sa=e=>{const{componentCls:t,siderBg:n,motionDurationMid:o,motionDurationSlow:r,antCls:a,triggerHeight:i,triggerColor:l,triggerBg:s,headerHeight:c,zeroTriggerWidth:u,zeroTriggerHeight:d,borderRadiusLG:p,lightSiderBg:m,lightTriggerColor:f,lightTriggerBg:g,bodyBg:h}=e;return{[t]:{position:"relative",minWidth:0,background:n,transition:`all ${o}, background 0s`,"&-has-trigger":{paddingBottom:i},"&-right":{order:1},[`${t}-children`]:{height:"100%",marginTop:-.1,paddingTop:.1,[`${a}-menu${a}-menu-inline-collapsed`]:{width:"auto"}},[`&-zero-width ${t}-children`]:{overflow:"hidden"},[`${t}-trigger`]:{position:"fixed",bottom:0,zIndex:1,height:i,color:l,lineHeight:L(i),textAlign:"center",background:s,cursor:"pointer",transition:`all ${o}`},[`${t}-zero-width-trigger`]:{position:"absolute",top:c,insetInlineEnd:e.calc(u).mul(-1).equal(),zIndex:1,width:u,height:d,color:l,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:n,borderRadius:`0 ${L(p)} ${L(p)} 0`,cursor:"pointer",transition:`background ${r} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${r}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(u).mul(-1).equal(),borderRadius:`${L(p)} 0 0 ${L(p)}`}},"&-light":{background:m,[`${t}-trigger`]:{color:f,background:g},[`${t}-zero-width-trigger`]:{color:f,background:g,border:`1px solid ${h}`,borderInlineStart:0}}}}},Oa=H(["Layout","Sider"],(e=>[Sa(e)]),Ca,{deprecatedTokens:$a});const ka={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},_a=e.createContext({}),Ia=(()=>{let e=0;return(t="")=>(e+=1,`${t}${e}`)})(),ja=e.forwardRef(((t,n)=>{const{prefixCls:o,className:r,trigger:a,children:i,defaultCollapsed:l=!1,theme:s="dark",style:c={},collapsible:u=!1,reverseArrow:d=!1,width:p=200,collapsedWidth:m=80,zeroWidthTriggerStyle:f,breakpoint:g,onCollapse:h,onBreakpoint:b}=t,v=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:y}=e.useContext(wa),[w,x]=e.useState("collapsed"in t?t.collapsed:l),[$,E]=e.useState(!1);e.useEffect((()=>{"collapsed"in t&&x(t.collapsed)}),[t.collapsed]);const S=(e,n)=>{"collapsed"in t||x(e),null==h||h(e,n)},{getPrefixCls:O,direction:k}=e.useContext(A),_=O("layout-sider",o),[I,j,R]=Oa(_),B=e.useRef(null);B.current=e=>{E(e.matches),null==b||b(e.matches),w!==e.matches&&S(e.matches,"responsive")},e.useEffect((()=>{function e(e){var t;return null===(t=B.current)||void 0===t?void 0:t.call(B,e)}let t;return void 0!==(null===window||void 0===window?void 0:window.matchMedia)&&g&&g in ka&&(t=window.matchMedia(`screen and (max-width: ${ka[g]})`),ao(t,e),e(t)),()=>{io(t,e)}}),[g]),e.useEffect((()=>{const e=Ia("ant-sider-");return y.addSider(e),()=>y.removeSider(e)}),[]);const N=()=>{S(!w,"clickTrigger")},T=ie(v,["collapsed"]),P=w?m:p,M=(z=P,!Number.isNaN(Number.parseFloat(z))&&isFinite(z)?`${P}px`:String(P));var z;const D=0===parseFloat(String(m||0))?e.createElement("span",{onClick:N,className:C(`${_}-zero-width-trigger`,`${_}-zero-width-trigger-${d?"right":"left"}`),style:f},a||e.createElement(ya,null)):null,L="rtl"===k==!d,H={expanded:L?e.createElement(le,null):e.createElement(Jo,null),collapsed:L?e.createElement(Jo,null):e.createElement(le,null)}[w?"collapsed":"expanded"],F=null!==a?D||e.createElement("div",{className:`${_}-trigger`,onClick:N,style:{width:M}},a||H):null,W=Object.assign(Object.assign({},c),{flex:`0 0 ${M}`,maxWidth:M,minWidth:M,width:M}),V=C(_,`${_}-${s}`,{[`${_}-collapsed`]:!!w,[`${_}-has-trigger`]:u&&null!==a&&!D,[`${_}-below`]:!!$,[`${_}-zero-width`]:0===parseFloat(M)},r,j,R),q=e.useMemo((()=>({siderCollapsed:w})),[w]);return I(e.createElement(_a.Provider,{value:q},e.createElement("aside",Object.assign({className:V},T,{style:W,ref:n}),e.createElement("div",{className:`${_}-children`},i),u||$&&D?F:null)))}));var Ra={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"},Ba=function(t,n){return e.createElement(D,p({},t,{ref:n,icon:Ra}))},Na=e.forwardRef(Ba);const Ta=e.createContext({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});const Pa=t=>{const{prefixCls:n,className:o,dashed:r}=t,a=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["prefixCls","className","dashed"]),{getPrefixCls:i}=e.useContext(A),l=i("menu",n),s=C({[`${l}-item-divider-dashed`]:!!r},o);return e.createElement(la,Object.assign({className:s},a))},Aa=t=>{var n;const{className:o,children:r,icon:a,title:i,danger:l,extra:s}=t,{prefixCls:c,firstLevel:u,direction:p,disableMenuItemTitleTooltip:m,inlineCollapsed:f}=e.useContext(Ta),{siderCollapsed:g}=e.useContext(_a);let h=i;void 0===i?h=u?r:"":!1===i&&(h="");const b={title:h};g||f||(b.title=null,b.open=!1);const v=d(r).length;let y=e.createElement(qr,Object.assign({},ie(t,["title","icon","danger"]),{className:C({[`${c}-item-danger`]:l,[`${c}-item-only-child`]:1===(a?v+1:v)},o),title:"string"==typeof i?i:void 0}),J(a,{className:C(e.isValidElement(a)?null===(n=a.props)||void 0===n?void 0:n.className:"",`${c}-item-icon`)}),(t=>{const n=null==r?void 0:r[0],o=e.createElement("span",{className:C(`${c}-title-content`,{[`${c}-title-content-with-extra`]:!!s||0===s})},r);return(!a||e.isValidElement(r)&&"span"===r.type)&&r&&t&&u&&"string"==typeof n?e.createElement("div",{className:`${c}-inline-collapsed-noicon`},n.charAt(0)):o})(f));return m||(y=e.createElement(Bo,Object.assign({},b,{placement:"rtl"===p?"left":"right",classNames:{root:`${c}-inline-collapsed-tooltip`}}),y)),y};const Ma=e.createContext(null),za=e.forwardRef(((t,n)=>{const{children:o}=t,r=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["children"]),a=e.useContext(Ma),s=e.useMemo((()=>Object.assign(Object.assign({},a),r)),[a,r.prefixCls,r.mode,r.selectable,r.rootClassName]),c=se(o),u=l(n,c?i(o):null);return e.createElement(Ma.Provider,{value:s},e.createElement(Y,{space:!0},c?e.cloneElement(o,{ref:u}):o))})),Da=e=>{const{componentCls:t,motionDurationSlow:n,horizontalLineHeight:o,colorSplit:r,lineWidth:a,lineType:i,itemPaddingInline:l}=e;return{[`${t}-horizontal`]:{lineHeight:o,border:0,borderBottom:`${L(a)} ${i} ${r}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${t}-item, ${t}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:l},[`> ${t}-item:hover,\n        > ${t}-item-active,\n        > ${t}-submenu ${t}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${t}-item, ${t}-submenu-title`]:{transition:[`border-color ${n}`,`background ${n}`].join(",")},[`${t}-submenu-arrow`]:{display:"none"}}}},La=({componentCls:e,menuArrowOffset:t,calc:n})=>({[`${e}-rtl`]:{direction:"rtl"},[`${e}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${e}-rtl${e}-vertical,\n    ${e}-submenu-rtl ${e}-vertical`]:{[`${e}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(${L(n(t).mul(-1).equal())})`},"&::after":{transform:`rotate(45deg) translateY(${L(t)})`}}}}),Ha=e=>Object.assign({},ce(e)),Fa=(e,t)=>{const{componentCls:n,itemColor:o,itemSelectedColor:r,subMenuItemSelectedColor:a,groupTitleColor:i,itemBg:l,subMenuItemBg:s,itemSelectedBg:c,activeBarHeight:u,activeBarWidth:d,activeBarBorderWidth:p,motionDurationSlow:m,motionEaseInOut:f,motionEaseOut:g,itemPaddingInline:h,motionDurationMid:b,itemHoverColor:v,lineType:y,colorSplit:w,itemDisabledColor:x,dangerItemColor:C,dangerItemHoverColor:$,dangerItemSelectedColor:E,dangerItemActiveBg:S,dangerItemSelectedBg:O,popupBg:k,itemHoverBg:_,itemActiveBg:I,menuSubMenuBg:j,horizontalItemSelectedColor:R,horizontalItemSelectedBg:B,horizontalItemBorderRadius:N,horizontalItemHoverBg:T}=e;return{[`${n}-${t}, ${n}-${t} > ${n}`]:{color:o,background:l,[`&${n}-root:focus-visible`]:Object.assign({},Ha(e)),[`${n}-item`]:{"&-group-title, &-extra":{color:i}},[`${n}-submenu-selected > ${n}-submenu-title`]:{color:a},[`${n}-item, ${n}-submenu-title`]:{color:o,[`&:not(${n}-item-disabled):focus-visible`]:Object.assign({},Ha(e))},[`${n}-item-disabled, ${n}-submenu-disabled`]:{color:`${x} !important`},[`${n}-item:not(${n}-item-selected):not(${n}-submenu-selected)`]:{[`&:hover, > ${n}-submenu-title:hover`]:{color:v}},[`&:not(${n}-horizontal)`]:{[`${n}-item:not(${n}-item-selected)`]:{"&:hover":{backgroundColor:_},"&:active":{backgroundColor:I}},[`${n}-submenu-title`]:{"&:hover":{backgroundColor:_},"&:active":{backgroundColor:I}}},[`${n}-item-danger`]:{color:C,[`&${n}-item:hover`]:{[`&:not(${n}-item-selected):not(${n}-submenu-selected)`]:{color:$}},[`&${n}-item:active`]:{background:S}},[`${n}-item a`]:{"&, &:hover":{color:"inherit"}},[`${n}-item-selected`]:{color:r,[`&${n}-item-danger`]:{color:E},"a, a:hover":{color:"inherit"}},[`& ${n}-item-selected`]:{backgroundColor:c,[`&${n}-item-danger`]:{backgroundColor:O}},[`&${n}-submenu > ${n}`]:{backgroundColor:j},[`&${n}-popup > ${n}`]:{backgroundColor:k},[`&${n}-submenu-popup > ${n}`]:{backgroundColor:k},[`&${n}-horizontal`]:Object.assign(Object.assign({},"dark"===t?{borderBottom:0}:{}),{[`> ${n}-item, > ${n}-submenu`]:{top:p,marginTop:e.calc(p).mul(-1).equal(),marginBottom:0,borderRadius:N,"&::after":{position:"absolute",insetInline:h,bottom:0,borderBottom:`${L(u)} solid transparent`,transition:`border-color ${m} ${f}`,content:'""'},"&:hover, &-active, &-open":{background:T,"&::after":{borderBottomWidth:u,borderBottomColor:R}},"&-selected":{color:R,backgroundColor:B,"&:hover":{backgroundColor:B},"&::after":{borderBottomWidth:u,borderBottomColor:R}}}}),[`&${n}-root`]:{[`&${n}-inline, &${n}-vertical`]:{borderInlineEnd:`${L(p)} ${y} ${w}`}},[`&${n}-inline`]:{[`${n}-sub${n}-inline`]:{background:s},[`${n}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${L(d)} solid ${r}`,transform:"scaleY(0.0001)",opacity:0,transition:[`transform ${b} ${g}`,`opacity ${b} ${g}`].join(","),content:'""'},[`&${n}-item-danger`]:{"&::after":{borderInlineEndColor:E}}},[`${n}-selected, ${n}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:[`transform ${b} ${f}`,`opacity ${b} ${f}`].join(",")}}}}}},Wa=e=>{const{componentCls:t,itemHeight:n,itemMarginInline:o,padding:r,menuArrowSize:a,marginXS:i,itemMarginBlock:l,itemWidth:s,itemPaddingInline:c}=e,u=e.calc(a).add(r).add(i).equal();return{[`${t}-item`]:{position:"relative",overflow:"hidden"},[`${t}-item, ${t}-submenu-title`]:{height:n,lineHeight:L(n),paddingInline:c,overflow:"hidden",textOverflow:"ellipsis",marginInline:o,marginBlock:l,width:s},[`> ${t}-item,\n            > ${t}-submenu > ${t}-submenu-title`]:{height:n,lineHeight:L(n)},[`${t}-item-group-list ${t}-submenu-title,\n            ${t}-submenu-title`]:{paddingInlineEnd:u}}},Va=e=>{const{componentCls:t,iconCls:n,itemHeight:o,colorTextLightSolid:r,dropdownWidth:a,controlHeightLG:i,motionEaseOut:l,paddingXL:s,itemMarginInline:c,fontSizeLG:u,motionDurationFast:d,motionDurationSlow:p,paddingXS:m,boxShadowSecondary:f,collapsedWidth:g,collapsedIconSize:h}=e,b={height:o,lineHeight:L(o),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({[`&${t}-root`]:{boxShadow:"none"}},Wa(e))},[`${t}-submenu-popup`]:{[`${t}-vertical`]:Object.assign(Object.assign({},Wa(e)),{boxShadow:f})}},{[`${t}-submenu-popup ${t}-vertical${t}-sub`]:{minWidth:a,maxHeight:`calc(100vh - ${L(e.calc(i).mul(2.5).equal())})`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${t}-inline`]:{width:"100%",[`&${t}-root`]:{[`${t}-item, ${t}-submenu-title`]:{display:"flex",alignItems:"center",transition:[`border-color ${p}`,`background ${p}`,`padding ${d} ${l}`].join(","),[`> ${t}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${t}-sub${t}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${t}-submenu > ${t}-submenu-title`]:b,[`& ${t}-item-group-title`]:{paddingInlineStart:s}},[`${t}-item`]:b}},{[`${t}-inline-collapsed`]:{width:g,[`&${t}-root`]:{[`${t}-item, ${t}-submenu ${t}-submenu-title`]:{[`> ${t}-inline-collapsed-noicon`]:{fontSize:u,textAlign:"center"}}},[`> ${t}-item,\n          > ${t}-item-group > ${t}-item-group-list > ${t}-item,\n          > ${t}-item-group > ${t}-item-group-list > ${t}-submenu > ${t}-submenu-title,\n          > ${t}-submenu > ${t}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${L(e.calc(h).div(2).equal())} - ${L(c)})`,textOverflow:"clip",[`\n            ${t}-submenu-arrow,\n            ${t}-submenu-expand-icon\n          `]:{opacity:0},[`${t}-item-icon, ${n}`]:{margin:0,fontSize:h,lineHeight:L(o),"+ span":{display:"inline-block",opacity:0}}},[`${t}-item-icon, ${n}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${t}-item-icon, ${n}`]:{display:"none"},"a, a:hover":{color:r}},[`${t}-item-group-title`]:Object.assign(Object.assign({},ue),{paddingInline:m})}}]},qa=e=>{const{componentCls:t,motionDurationSlow:n,motionDurationMid:o,motionEaseInOut:r,motionEaseOut:a,iconCls:i,iconSize:l,iconMarginInlineEnd:s}=e;return{[`${t}-item, ${t}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:[`border-color ${n}`,`background ${n}`,`padding calc(${n} + 0.1s) ${r}`].join(","),[`${t}-item-icon, ${i}`]:{minWidth:l,fontSize:l,transition:[`font-size ${o} ${a}`,`margin ${n} ${r}`,`color ${n}`].join(","),"+ span":{marginInlineStart:s,opacity:1,transition:[`opacity ${n} ${r}`,`margin ${n}`,`color ${n}`].join(",")}},[`${t}-item-icon`]:Object.assign({},fe()),[`&${t}-item-only-child`]:{[`> ${i}, > ${t}-item-icon`]:{marginInlineEnd:0}}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},[`> ${t}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},Xa=e=>{const{componentCls:t,motionDurationSlow:n,motionEaseInOut:o,borderRadius:r,menuArrowSize:a,menuArrowOffset:i}=e;return{[`${t}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:a,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${n} ${o}, opacity ${n}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(a).mul(.6).equal(),height:e.calc(a).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:r,transition:[`background ${n} ${o}`,`transform ${n} ${o}`,`top ${n} ${o}`,`color ${n} ${o}`].join(","),content:'""'},"&::before":{transform:`rotate(45deg) translateY(${L(e.calc(i).mul(-1).equal())})`},"&::after":{transform:`rotate(-45deg) translateY(${L(i)})`}}}}},Ga=e=>{const{antCls:t,componentCls:n,fontSize:o,motionDurationSlow:r,motionDurationMid:a,motionEaseInOut:i,paddingXS:l,padding:s,colorSplit:c,lineWidth:u,zIndexPopup:d,borderRadiusLG:p,subMenuItemBorderRadius:m,menuArrowSize:f,menuArrowOffset:g,lineType:h,groupTitleLineHeight:b,groupTitleFontSize:v}=e;return[{"":{[n]:Object.assign(Object.assign({},pe()),{"&-hidden":{display:"none"}})},[`${n}-submenu-hidden`]:{display:"none"}},{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},V(e)),pe()),{marginBottom:0,paddingInlineStart:0,fontSize:o,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${r} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${n}-item`]:{flex:"none"}},[`${n}-item, ${n}-submenu, ${n}-submenu-title`]:{borderRadius:e.itemBorderRadius},[`${n}-item-group-title`]:{padding:`${L(l)} ${L(s)}`,fontSize:v,lineHeight:b,transition:`all ${r}`},[`&-horizontal ${n}-submenu`]:{transition:[`border-color ${r} ${i}`,`background ${r} ${i}`].join(",")},[`${n}-submenu, ${n}-submenu-inline`]:{transition:[`border-color ${r} ${i}`,`background ${r} ${i}`,`padding ${a} ${i}`].join(",")},[`${n}-submenu ${n}-sub`]:{cursor:"initial",transition:[`background ${r} ${i}`,`padding ${r} ${i}`].join(",")},[`${n}-title-content`]:{transition:`color ${r}`,"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},[`> ${t}-typography-ellipsis-single-line`]:{display:"inline",verticalAlign:"unset"},[`${n}-item-extra`]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},[`${n}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${n}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:c,borderStyle:h,borderWidth:0,borderTopWidth:u,marginBlock:u,padding:0,"&-dashed":{borderStyle:"dashed"}}}),qa(e)),{[`${n}-item-group`]:{[`${n}-item-group-list`]:{margin:0,padding:0,[`${n}-item, ${n}-submenu-title`]:{paddingInline:`${L(e.calc(o).mul(2).equal())} ${L(s)}`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:d,borderRadius:p,boxShadow:"none",transformOrigin:"0 0",[`&${n}-submenu`]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},[`> ${n}`]:Object.assign(Object.assign(Object.assign({borderRadius:p},qa(e)),Xa(e)),{[`${n}-item, ${n}-submenu > ${n}-submenu-title`]:{borderRadius:m},[`${n}-submenu-title::after`]:{transition:`transform ${r} ${i}`}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),Xa(e)),{[`&-inline-collapsed ${n}-submenu-arrow,\n        &-inline ${n}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${L(g)})`},"&::after":{transform:`rotate(45deg) translateX(${L(e.calc(g).mul(-1).equal())})`}},[`${n}-submenu-open${n}-submenu-inline > ${n}-submenu-title > ${n}-submenu-arrow`]:{transform:`translateY(${L(e.calc(f).mul(.2).mul(-1).equal())})`,"&::after":{transform:`rotate(-45deg) translateX(${L(e.calc(g).mul(-1).equal())})`},"&::before":{transform:`rotate(45deg) translateX(${L(g)})`}}})},{[`${t}-layout-header`]:{[n]:{lineHeight:"inherit"}}}]},Ka=e=>{var t,n,o;const{colorPrimary:r,colorError:a,colorTextDisabled:i,colorErrorBg:l,colorText:s,colorTextDescription:c,colorBgContainer:u,colorFillAlter:d,colorFillContent:p,lineWidth:m,lineWidthBold:f,controlItemBgActive:g,colorBgTextHover:h,controlHeightLG:b,lineHeight:v,colorBgElevated:y,marginXXS:w,padding:x,fontSize:C,controlHeightSM:$,fontSizeLG:E,colorTextLightSolid:S,colorErrorHover:O}=e,k=null!==(t=e.activeBarWidth)&&void 0!==t?t:0,_=null!==(n=e.activeBarBorderWidth)&&void 0!==n?n:m,I=null!==(o=e.itemMarginInline)&&void 0!==o?o:e.marginXXS,j=new me(S).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:s,itemColor:s,colorItemTextHover:s,itemHoverColor:s,colorItemTextHoverHorizontal:r,horizontalItemHoverColor:r,colorGroupTitle:c,groupTitleColor:c,colorItemTextSelected:r,itemSelectedColor:r,subMenuItemSelectedColor:r,colorItemTextSelectedHorizontal:r,horizontalItemSelectedColor:r,colorItemBg:u,itemBg:u,colorItemBgHover:h,itemHoverBg:h,colorItemBgActive:p,itemActiveBg:g,colorSubItemBg:d,subMenuItemBg:d,colorItemBgSelected:g,itemSelectedBg:g,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:k,colorActiveBarHeight:f,activeBarHeight:f,colorActiveBarBorderSize:m,activeBarBorderWidth:_,colorItemTextDisabled:i,itemDisabledColor:i,colorDangerItemText:a,dangerItemColor:a,colorDangerItemTextHover:a,dangerItemHoverColor:a,colorDangerItemTextSelected:a,dangerItemSelectedColor:a,colorDangerItemBgActive:l,dangerItemActiveBg:l,colorDangerItemBgSelected:l,dangerItemSelectedBg:l,itemMarginInline:I,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:b,groupTitleLineHeight:v,collapsedWidth:2*b,popupBg:y,itemMarginBlock:w,itemPaddingInline:x,horizontalLineHeight:1.15*b+"px",iconSize:C,iconMarginInlineEnd:$-C,collapsedIconSize:E,groupTitleFontSize:C,darkItemDisabledColor:new me(S).setA(.25).toRgbString(),darkItemColor:j,darkDangerItemColor:a,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:S,darkItemSelectedBg:r,darkDangerItemSelectedBg:a,darkItemHoverBg:"transparent",darkGroupTitleColor:j,darkItemHoverColor:S,darkDangerItemHoverColor:O,darkDangerItemSelectedColor:S,darkDangerItemActiveBg:a,itemWidth:k?`calc(100% + ${_}px)`:`calc(100% - ${2*I}px)`}},Ya=t=>{var n;const{popupClassName:o,icon:r,title:a,theme:i}=t,l=e.useContext(Ta),{prefixCls:s,inlineCollapsed:c,theme:u}=l,d=ur();let p;if(r){const t=e.isValidElement(a)&&"span"===a.type;p=e.createElement(e.Fragment,null,J(r,{className:C(e.isValidElement(r)?null===(n=r.props)||void 0===n?void 0:n.className:"",`${s}-item-icon`)}),t?a:e.createElement("span",{className:`${s}-title-content`},a))}else p=c&&!d.length&&a&&"string"==typeof a?e.createElement("div",{className:`${s}-inline-collapsed-noicon`},a.charAt(0)):e.createElement("span",{className:`${s}-title-content`},a);const m=e.useMemo((()=>Object.assign(Object.assign({},l),{firstLevel:!1})),[l]),[f]=Q("Menu");return e.createElement(Ta.Provider,{value:m},e.createElement(ia,Object.assign({},ie(t,["icon"]),{title:p,popupClassName:C(s,o,`${s}-${i||u}`),popupStyle:Object.assign({zIndex:f},t.popupStyle)})))};function Ua(e){return null===e||!1===e}const Qa={item:Aa,submenu:Ya,divider:Pa},Za=e.forwardRef(((t,n)=>{var o;const r=e.useContext(Ma),a=r||{},{getPrefixCls:i,getPopupContainer:l,direction:s,menu:c}=e.useContext(A),u=i(),{prefixCls:d,className:p,style:m,theme:f="light",expandIcon:g,_internalDisableMenuItemTitleTooltip:h,inlineCollapsed:b,siderCollapsed:v,rootClassName:y,mode:w,selectable:x,onClick:$,overflowedIndicatorPopupClassName:S}=t,O=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),k=ie(O,["collapsedWidth"]);null===(o=a.validator)||void 0===o||o.call(a,{mode:w});const _=E(((...e)=>{var t;null==$||$.apply(void 0,e),null===(t=a.onClick)||void 0===t||t.call(a)})),I=a.mode||w,j=null!=x?x:a.selectable,R=null!=b?b:v,B={horizontal:{motionName:`${u}-slide-up`},inline:he(u),other:{motionName:`${u}-zoom-big`}},N=i("menu",d||a.prefixCls),T=ge(N),[P,M,z]=((e,t=e,n=!0)=>H("Menu",(e=>{const{colorBgElevated:t,controlHeightLG:n,fontSize:o,darkItemColor:r,darkDangerItemColor:a,darkItemBg:i,darkSubMenuItemBg:l,darkItemSelectedColor:s,darkItemSelectedBg:c,darkDangerItemSelectedBg:u,darkItemHoverBg:d,darkGroupTitleColor:p,darkItemHoverColor:m,darkItemDisabledColor:f,darkDangerItemHoverColor:g,darkDangerItemSelectedColor:h,darkDangerItemActiveBg:b,popupBg:v,darkPopupBg:y}=e,w=e.calc(o).div(7).mul(5).equal(),x=F(e,{menuArrowSize:w,menuHorizontalHeight:e.calc(n).mul(1.15).equal(),menuArrowOffset:e.calc(w).mul(.25).equal(),menuSubMenuBg:t,calc:e.calc,popupBg:v}),C=F(x,{itemColor:r,itemHoverColor:m,groupTitleColor:p,itemSelectedColor:s,subMenuItemSelectedColor:s,itemBg:i,popupBg:y,subMenuItemBg:l,itemActiveBg:"transparent",itemSelectedBg:c,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:d,itemDisabledColor:f,dangerItemColor:a,dangerItemHoverColor:g,dangerItemSelectedColor:h,dangerItemActiveBg:b,dangerItemSelectedBg:u,menuSubMenuBg:l,horizontalItemSelectedColor:s,horizontalItemSelectedBg:c});return[Ga(x),Da(x),Va(x),Fa(x,"light"),Fa(C,"dark"),La(x),de(x),un(x,"slide-up"),un(x,"slide-down"),W(x,"zoom-big")]}),Ka,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:n,unitless:{groupTitleLineHeight:!0}})(e,t))(N,T,!r),D=C(`${N}-${f}`,null==c?void 0:c.className,p),L=e.useMemo((()=>{var t,n;if("function"==typeof g||Ua(g))return g||null;if("function"==typeof a.expandIcon||Ua(a.expandIcon))return a.expandIcon||null;if("function"==typeof(null==c?void 0:c.expandIcon)||Ua(null==c?void 0:c.expandIcon))return(null==c?void 0:c.expandIcon)||null;const o=null!==(t=null!=g?g:null==a?void 0:a.expandIcon)&&void 0!==t?t:null==c?void 0:c.expandIcon;return J(o,{className:C(`${N}-submenu-expand-icon`,e.isValidElement(o)?null===(n=o.props)||void 0===n?void 0:n.className:void 0)})}),[g,null==a?void 0:a.expandIcon,null==c?void 0:c.expandIcon,N]),V=e.useMemo((()=>({prefixCls:N,inlineCollapsed:R||!1,direction:s,firstLevel:!0,theme:f,mode:I,disableMenuItemTitleTooltip:h})),[N,R,s,h,f]);return P(e.createElement(Ma.Provider,{value:null},e.createElement(Ta.Provider,{value:V},e.createElement(ha,Object.assign({getPopupContainer:l,overflowedIndicator:e.createElement(Na,null),overflowedIndicatorPopupClassName:C(N,`${N}-${f}`,S),mode:I,selectable:j,onClick:_},k,{inlineCollapsed:R,style:Object.assign(Object.assign({},null==c?void 0:c.style),m),className:D,prefixCls:N,direction:s,defaultMotions:B,expandIcon:L,ref:n,rootClassName:C(y,M,a.rootClassName,z,T),_internalComponents:Qa})))))})),Ja=e.forwardRef(((t,n)=>{const o=e.useRef(null),r=e.useContext(_a);return e.useImperativeHandle(n,(()=>({menu:o.current,focus:e=>{var t;null===(t=o.current)||void 0===t||t.focus(e)}}))),e.createElement(Za,Object.assign({ref:o},t,r))}));function ei(e){return F(e,{inputAffixPadding:e.paddingXXS})}Ja.Item=Aa,Ja.SubMenu=Ya,Ja.Divider=Pa,Ja.ItemGroup=ua;const ti=e=>{const{controlHeight:t,fontSize:n,lineHeight:o,lineWidth:r,controlHeightSM:a,controlHeightLG:i,fontSizeLG:l,lineHeightLG:s,paddingSM:c,controlPaddingHorizontalSM:u,controlPaddingHorizontal:d,colorFillAlter:p,colorPrimaryHover:m,colorPrimary:f,controlOutlineWidth:g,controlOutline:h,colorErrorOutline:b,colorWarningOutline:v,colorBgContainer:y,inputFontSize:w,inputFontSizeLG:x,inputFontSizeSM:C}=e,$=w||n,E=C||$,S=x||l,O=Math.round((t-$*o)/2*10)/10-r,k=Math.round((a-E*o)/2*10)/10-r,_=Math.ceil((i-S*s)/2*10)/10-r;return{paddingBlock:Math.max(O,0),paddingBlockSM:Math.max(k,0),paddingBlockLG:Math.max(_,0),paddingInline:c-r,paddingInlineSM:u-r,paddingInlineLG:d-r,addonBg:p,activeBorderColor:f,hoverBorderColor:m,activeShadow:`0 0 0 ${g}px ${h}`,errorActiveShadow:`0 0 0 ${g}px ${b}`,warningActiveShadow:`0 0 0 ${g}px ${v}`,hoverBg:y,activeBg:y,inputFontSize:$,inputFontSizeLG:S,inputFontSizeSM:E}},ni=e=>({borderColor:e.hoverBorderColor,backgroundColor:e.hoverBg}),oi=e=>({color:e.colorTextDisabled,backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,boxShadow:"none",cursor:"not-allowed",opacity:1,"input[disabled], textarea[disabled]":{cursor:"not-allowed"},"&:hover:not([disabled])":Object.assign({},ni(F(e,{hoverBorderColor:e.colorBorder,hoverBg:e.colorBgContainerDisabled})))}),ri=(e,t)=>({background:e.colorBgContainer,borderWidth:e.lineWidth,borderStyle:e.lineType,borderColor:t.borderColor,"&:hover":{borderColor:t.hoverBorderColor,backgroundColor:e.hoverBg},"&:focus, &:focus-within":{borderColor:t.activeBorderColor,boxShadow:t.activeShadow,outline:0,backgroundColor:e.activeBg}}),ai=(e,t)=>({[`&${e.componentCls}-status-${t.status}:not(${e.componentCls}-disabled)`]:Object.assign(Object.assign({},ri(e,t)),{[`${e.componentCls}-prefix, ${e.componentCls}-suffix`]:{color:t.affixColor}}),[`&${e.componentCls}-status-${t.status}${e.componentCls}-disabled`]:{borderColor:t.borderColor}}),ii=(e,t)=>({"&-outlined":Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},ri(e,{borderColor:e.colorBorder,hoverBorderColor:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeShadow:e.activeShadow})),{[`&${e.componentCls}-disabled, &[disabled]`]:Object.assign({},oi(e))}),ai(e,{status:"error",borderColor:e.colorError,hoverBorderColor:e.colorErrorBorderHover,activeBorderColor:e.colorError,activeShadow:e.errorActiveShadow,affixColor:e.colorError})),ai(e,{status:"warning",borderColor:e.colorWarning,hoverBorderColor:e.colorWarningBorderHover,activeBorderColor:e.colorWarning,activeShadow:e.warningActiveShadow,affixColor:e.colorWarning})),t)}),li=(e,t)=>({[`&${e.componentCls}-group-wrapper-status-${t.status}`]:{[`${e.componentCls}-group-addon`]:{borderColor:t.addonBorderColor,color:t.addonColor}}}),si=e=>({"&-outlined":Object.assign(Object.assign(Object.assign({[`${e.componentCls}-group`]:{"&-addon":{background:e.addonBg,border:`${L(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},"&-addon:first-child":{borderInlineEnd:0},"&-addon:last-child":{borderInlineStart:0}}},li(e,{status:"error",addonBorderColor:e.colorError,addonColor:e.colorErrorText})),li(e,{status:"warning",addonBorderColor:e.colorWarning,addonColor:e.colorWarningText})),{[`&${e.componentCls}-group-wrapper-disabled`]:{[`${e.componentCls}-group-addon`]:Object.assign({},oi(e))}})}),ci=(e,t)=>{const{componentCls:n}=e;return{"&-borderless":Object.assign({background:"transparent",border:"none","&:focus, &:focus-within":{outline:"none"},[`&${n}-disabled, &[disabled]`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${n}-status-error`]:{"&, & input, & textarea":{color:e.colorError}},[`&${n}-status-warning`]:{"&, & input, & textarea":{color:e.colorWarning}}},t)}},ui=(e,t)=>{var n;return{background:t.bg,borderWidth:e.lineWidth,borderStyle:e.lineType,borderColor:"transparent","input&, & input, textarea&, & textarea":{color:null!==(n=null==t?void 0:t.inputColor)&&void 0!==n?n:"unset"},"&:hover":{background:t.hoverBg},"&:focus, &:focus-within":{outline:0,borderColor:t.activeBorderColor,backgroundColor:e.activeBg}}},di=(e,t)=>({[`&${e.componentCls}-status-${t.status}:not(${e.componentCls}-disabled)`]:Object.assign(Object.assign({},ui(e,t)),{[`${e.componentCls}-prefix, ${e.componentCls}-suffix`]:{color:t.affixColor}})}),pi=(e,t)=>({"&-filled":Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},ui(e,{bg:e.colorFillTertiary,hoverBg:e.colorFillSecondary,activeBorderColor:e.activeBorderColor})),{[`&${e.componentCls}-disabled, &[disabled]`]:Object.assign({},oi(e))}),di(e,{status:"error",bg:e.colorErrorBg,hoverBg:e.colorErrorBgHover,activeBorderColor:e.colorError,inputColor:e.colorErrorText,affixColor:e.colorError})),di(e,{status:"warning",bg:e.colorWarningBg,hoverBg:e.colorWarningBgHover,activeBorderColor:e.colorWarning,inputColor:e.colorWarningText,affixColor:e.colorWarning})),t)}),mi=(e,t)=>({[`&${e.componentCls}-group-wrapper-status-${t.status}`]:{[`${e.componentCls}-group-addon`]:{background:t.addonBg,color:t.addonColor}}}),fi=e=>({"&-filled":Object.assign(Object.assign(Object.assign({[`${e.componentCls}-group-addon`]:{background:e.colorFillTertiary,"&:last-child":{position:"static"}}},mi(e,{status:"error",addonBg:e.colorErrorBg,addonColor:e.colorErrorText})),mi(e,{status:"warning",addonBg:e.colorWarningBg,addonColor:e.colorWarningText})),{[`&${e.componentCls}-group-wrapper-disabled`]:{[`${e.componentCls}-group`]:{"&-addon":{background:e.colorFillTertiary,color:e.colorTextDisabled},"&-addon:first-child":{borderInlineStart:`${L(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderTop:`${L(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderBottom:`${L(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},"&-addon:last-child":{borderInlineEnd:`${L(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderTop:`${L(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderBottom:`${L(e.lineWidth)} ${e.lineType} ${e.colorBorder}`}}}})}),gi=(e,t)=>({background:e.colorBgContainer,borderWidth:`${L(e.lineWidth)} 0`,borderStyle:`${e.lineType} none`,borderColor:`transparent transparent ${t.borderColor} transparent`,borderRadius:0,"&:hover":{borderColor:`transparent transparent ${t.borderColor} transparent`,backgroundColor:e.hoverBg},"&:focus, &:focus-within":{borderColor:`transparent transparent ${t.borderColor} transparent`,outline:0,backgroundColor:e.activeBg}}),hi=(e,t)=>({[`&${e.componentCls}-status-${t.status}:not(${e.componentCls}-disabled)`]:Object.assign(Object.assign({},gi(e,t)),{[`${e.componentCls}-prefix, ${e.componentCls}-suffix`]:{color:t.affixColor}}),[`&${e.componentCls}-status-${t.status}${e.componentCls}-disabled`]:{borderColor:`transparent transparent ${t.borderColor} transparent`}}),bi=(e,t)=>({"&-underlined":Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},gi(e,{borderColor:e.colorBorder,hoverBorderColor:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeShadow:e.activeShadow})),{[`&${e.componentCls}-disabled, &[disabled]`]:{color:e.colorTextDisabled,boxShadow:"none",cursor:"not-allowed","&:hover":{borderColor:`transparent transparent ${e.colorBorder} transparent`}},"input[disabled], textarea[disabled]":{cursor:"not-allowed"}}),hi(e,{status:"error",borderColor:e.colorError,hoverBorderColor:e.colorErrorBorderHover,activeBorderColor:e.colorError,activeShadow:e.errorActiveShadow,affixColor:e.colorError})),hi(e,{status:"warning",borderColor:e.colorWarning,hoverBorderColor:e.colorWarningBorderHover,activeBorderColor:e.colorWarning,activeShadow:e.warningActiveShadow,affixColor:e.colorWarning})),t)}),vi=e=>({"&::-moz-placeholder":{opacity:1},"&::placeholder":{color:e,userSelect:"none"},"&:placeholder-shown":{textOverflow:"ellipsis"}}),yi=e=>{const{paddingBlockLG:t,lineHeightLG:n,borderRadiusLG:o,paddingInlineLG:r}=e;return{padding:`${L(t)} ${L(r)}`,fontSize:e.inputFontSizeLG,lineHeight:n,borderRadius:o}},wi=e=>({padding:`${L(e.paddingBlockSM)} ${L(e.paddingInlineSM)}`,fontSize:e.inputFontSizeSM,borderRadius:e.borderRadiusSM}),xi=e=>Object.assign(Object.assign({position:"relative",display:"inline-block",width:"100%",minWidth:0,padding:`${L(e.paddingBlock)} ${L(e.paddingInline)}`,color:e.colorText,fontSize:e.inputFontSize,lineHeight:e.lineHeight,borderRadius:e.borderRadius,transition:`all ${e.motionDurationMid}`},vi(e.colorTextPlaceholder)),{"&-lg":Object.assign({},yi(e)),"&-sm":Object.assign({},wi(e)),"&-rtl, &-textarea-rtl":{direction:"rtl"}}),Ci=e=>{const{componentCls:t,antCls:n}=e;return{position:"relative",display:"table",width:"100%",borderCollapse:"separate",borderSpacing:0,"&[class*='col-']":{paddingInlineEnd:e.paddingXS,"&:last-child":{paddingInlineEnd:0}},[`&-lg ${t}, &-lg > ${t}-group-addon`]:Object.assign({},yi(e)),[`&-sm ${t}, &-sm > ${t}-group-addon`]:Object.assign({},wi(e)),[`&-lg ${n}-select-single ${n}-select-selector`]:{height:e.controlHeightLG},[`&-sm ${n}-select-single ${n}-select-selector`]:{height:e.controlHeightSM},[`> ${t}`]:{display:"table-cell","&:not(:first-child):not(:last-child)":{borderRadius:0}},[`${t}-group`]:{"&-addon, &-wrap":{display:"table-cell",width:1,whiteSpace:"nowrap",verticalAlign:"middle","&:not(:first-child):not(:last-child)":{borderRadius:0}},"&-wrap > *":{display:"block !important"},"&-addon":{position:"relative",padding:`0 ${L(e.paddingInline)}`,color:e.colorText,fontWeight:"normal",fontSize:e.inputFontSize,textAlign:"center",borderRadius:e.borderRadius,transition:`all ${e.motionDurationSlow}`,lineHeight:1,[`${n}-select`]:{margin:`${L(e.calc(e.paddingBlock).add(1).mul(-1).equal())} ${L(e.calc(e.paddingInline).mul(-1).equal())}`,[`&${n}-select-single:not(${n}-select-customize-input):not(${n}-pagination-size-changer)`]:{[`${n}-select-selector`]:{backgroundColor:"inherit",border:`${L(e.lineWidth)} ${e.lineType} transparent`,boxShadow:"none"}}},[`${n}-cascader-picker`]:{margin:`-9px ${L(e.calc(e.paddingInline).mul(-1).equal())}`,backgroundColor:"transparent",[`${n}-cascader-input`]:{textAlign:"start",border:0,boxShadow:"none"}}}},[t]:{width:"100%",marginBottom:0,textAlign:"inherit","&:focus":{zIndex:1,borderInlineEndWidth:1},"&:hover":{zIndex:1,borderInlineEndWidth:1,[`${t}-search-with-button &`]:{zIndex:0}}},[`> ${t}:first-child, ${t}-group-addon:first-child`]:{borderStartEndRadius:0,borderEndEndRadius:0,[`${n}-select ${n}-select-selector`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`> ${t}-affix-wrapper`]:{[`&:not(:first-child) ${t}`]:{borderStartStartRadius:0,borderEndStartRadius:0},[`&:not(:last-child) ${t}`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`> ${t}:last-child, ${t}-group-addon:last-child`]:{borderStartStartRadius:0,borderEndStartRadius:0,[`${n}-select ${n}-select-selector`]:{borderStartStartRadius:0,borderEndStartRadius:0}},[`${t}-affix-wrapper`]:{"&:not(:last-child)":{borderStartEndRadius:0,borderEndEndRadius:0,[`${t}-search &`]:{borderStartStartRadius:e.borderRadius,borderEndStartRadius:e.borderRadius}},[`&:not(:first-child), ${t}-search &:not(:first-child)`]:{borderStartStartRadius:0,borderEndStartRadius:0}},[`&${t}-group-compact`]:Object.assign(Object.assign({display:"block"},pe()),{[`${t}-group-addon, ${t}-group-wrap, > ${t}`]:{"&:not(:first-child):not(:last-child)":{borderInlineEndWidth:e.lineWidth,"&:hover, &:focus":{zIndex:1}}},"& > *":{display:"inline-flex",float:"none",verticalAlign:"top",borderRadius:0},[`\n        & > ${t}-affix-wrapper,\n        & > ${t}-number-affix-wrapper,\n        & > ${n}-picker-range\n      `]:{display:"inline-flex"},"& > *:not(:last-child)":{marginInlineEnd:e.calc(e.lineWidth).mul(-1).equal(),borderInlineEndWidth:e.lineWidth},[t]:{float:"none"},[`& > ${n}-select > ${n}-select-selector,\n      & > ${n}-select-auto-complete ${t},\n      & > ${n}-cascader-picker ${t},\n      & > ${t}-group-wrapper ${t}`]:{borderInlineEndWidth:e.lineWidth,borderRadius:0,"&:hover, &:focus":{zIndex:1}},[`& > ${n}-select-focused`]:{zIndex:1},[`& > ${n}-select > ${n}-select-arrow`]:{zIndex:1},[`& > *:first-child,\n      & > ${n}-select:first-child > ${n}-select-selector,\n      & > ${n}-select-auto-complete:first-child ${t},\n      & > ${n}-cascader-picker:first-child ${t}`]:{borderStartStartRadius:e.borderRadius,borderEndStartRadius:e.borderRadius},[`& > *:last-child,\n      & > ${n}-select:last-child > ${n}-select-selector,\n      & > ${n}-cascader-picker:last-child ${t},\n      & > ${n}-cascader-picker-focused:last-child ${t}`]:{borderInlineEndWidth:e.lineWidth,borderStartEndRadius:e.borderRadius,borderEndEndRadius:e.borderRadius},[`& > ${n}-select-auto-complete ${t}`]:{verticalAlign:"top"},[`${t}-group-wrapper + ${t}-group-wrapper`]:{marginInlineStart:e.calc(e.lineWidth).mul(-1).equal(),[`${t}-affix-wrapper`]:{borderRadius:0}},[`${t}-group-wrapper:not(:last-child)`]:{[`&${t}-search > ${t}-group`]:{[`& > ${t}-group-addon > ${t}-search-button`]:{borderRadius:0},[`& > ${t}`]:{borderStartStartRadius:e.borderRadius,borderStartEndRadius:0,borderEndEndRadius:0,borderEndStartRadius:e.borderRadius}}}})}},$i=e=>{const{componentCls:t,controlHeightSM:n,lineWidth:o,calc:r}=e,a=r(n).sub(r(o).mul(2)).sub(16).div(2).equal();return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},V(e)),xi(e)),ii(e)),pi(e)),ci(e)),bi(e)),{'&[type="color"]':{height:e.controlHeight,[`&${t}-lg`]:{height:e.controlHeightLG},[`&${t}-sm`]:{height:n,paddingTop:a,paddingBottom:a}},'&[type="search"]::-webkit-search-cancel-button, &[type="search"]::-webkit-search-decoration':{appearance:"none"}})}},Ei=e=>{const{componentCls:t}=e;return{[`${t}-clear-icon`]:{margin:0,padding:0,lineHeight:0,color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,verticalAlign:-1,cursor:"pointer",transition:`color ${e.motionDurationSlow}`,border:"none",outline:"none",backgroundColor:"transparent","&:hover":{color:e.colorIcon},"&:active":{color:e.colorText},"&-hidden":{visibility:"hidden"},"&-has-suffix":{margin:`0 ${L(e.inputAffixPadding)}`}}}},Si=e=>{const{componentCls:t,inputAffixPadding:n,colorTextDescription:o,motionDurationSlow:r,colorIcon:a,colorIconHover:i,iconCls:l}=e,s=`${t}-affix-wrapper`,c=`${t}-affix-wrapper-disabled`;return{[s]:Object.assign(Object.assign(Object.assign(Object.assign({},xi(e)),{display:"inline-flex",[`&:not(${t}-disabled):hover`]:{zIndex:1,[`${t}-search-with-button &`]:{zIndex:0}},"&-focused, &:focus":{zIndex:1},[`> input${t}`]:{padding:0},[`> input${t}, > textarea${t}`]:{fontSize:"inherit",border:"none",borderRadius:0,outline:"none",background:"transparent",color:"inherit","&::-ms-reveal":{display:"none"},"&:focus":{boxShadow:"none !important"}},"&::before":{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'},[t]:{"&-prefix, &-suffix":{display:"flex",flex:"none",alignItems:"center","> *:not(:last-child)":{marginInlineEnd:e.paddingXS}},"&-show-count-suffix":{color:o,direction:"ltr"},"&-show-count-has-suffix":{marginInlineEnd:e.paddingXXS},"&-prefix":{marginInlineEnd:n},"&-suffix":{marginInlineStart:n}}}),Ei(e)),{[`${l}${t}-password-icon`]:{color:a,cursor:"pointer",transition:`all ${r}`,"&:hover":{color:i}}}),[`${t}-underlined`]:{borderRadius:0},[c]:{[`${l}${t}-password-icon`]:{color:a,cursor:"not-allowed","&:hover":{color:a}}}}},Oi=e=>{const{componentCls:t,borderRadiusLG:n,borderRadiusSM:o}=e;return{[`${t}-group`]:Object.assign(Object.assign(Object.assign({},V(e)),Ci(e)),{"&-rtl":{direction:"rtl"},"&-wrapper":Object.assign(Object.assign(Object.assign({display:"inline-block",width:"100%",textAlign:"start",verticalAlign:"top","&-rtl":{direction:"rtl"},"&-lg":{[`${t}-group-addon`]:{borderRadius:n,fontSize:e.inputFontSizeLG}},"&-sm":{[`${t}-group-addon`]:{borderRadius:o}}},si(e)),fi(e)),{[`&:not(${t}-compact-first-item):not(${t}-compact-last-item)${t}-compact-item`]:{[`${t}, ${t}-group-addon`]:{borderRadius:0}},[`&:not(${t}-compact-last-item)${t}-compact-first-item`]:{[`${t}, ${t}-group-addon`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&:not(${t}-compact-first-item)${t}-compact-last-item`]:{[`${t}, ${t}-group-addon`]:{borderStartStartRadius:0,borderEndStartRadius:0}},[`&:not(${t}-compact-last-item)${t}-compact-item`]:{[`${t}-affix-wrapper`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&:not(${t}-compact-first-item)${t}-compact-item`]:{[`${t}-affix-wrapper`]:{borderStartStartRadius:0,borderEndStartRadius:0}}})})}},ki=e=>{const{componentCls:t,antCls:n}=e,o=`${t}-search`;return{[o]:{[t]:{"&:hover, &:focus":{[`+ ${t}-group-addon ${o}-button:not(${n}-btn-color-primary):not(${n}-btn-variant-text)`]:{borderInlineStartColor:e.colorPrimaryHover}}},[`${t}-affix-wrapper`]:{height:e.controlHeight,borderRadius:0},[`${t}-lg`]:{lineHeight:e.calc(e.lineHeightLG).sub(2e-4).equal()},[`> ${t}-group`]:{[`> ${t}-group-addon:last-child`]:{insetInlineStart:-1,padding:0,border:0,[`${o}-button`]:{marginInlineEnd:-1,borderStartStartRadius:0,borderEndStartRadius:0,boxShadow:"none"},[`${o}-button:not(${n}-btn-color-primary)`]:{color:e.colorTextDescription,"&:hover":{color:e.colorPrimaryHover},"&:active":{color:e.colorPrimaryActive},[`&${n}-btn-loading::before`]:{inset:0}}}},[`${o}-button`]:{height:e.controlHeight,"&:hover, &:focus":{zIndex:1}},"&-large":{[`${t}-affix-wrapper, ${o}-button`]:{height:e.controlHeightLG}},"&-small":{[`${t}-affix-wrapper, ${o}-button`]:{height:e.controlHeightSM}},"&-rtl":{direction:"rtl"},[`&${t}-compact-item`]:{[`&:not(${t}-compact-last-item)`]:{[`${t}-group-addon`]:{[`${t}-search-button`]:{marginInlineEnd:e.calc(e.lineWidth).mul(-1).equal(),borderRadius:0}}},[`&:not(${t}-compact-first-item)`]:{[`${t},${t}-affix-wrapper`]:{borderRadius:0}},[`> ${t}-group-addon ${t}-search-button,\n        > ${t},\n        ${t}-affix-wrapper`]:{"&:hover, &:focus, &:active":{zIndex:2}},[`> ${t}-affix-wrapper-focused`]:{zIndex:2}}}}},_i=e=>{const{componentCls:t}=e;return{[`${t}-out-of-range`]:{[`&, & input, & textarea, ${t}-show-count-suffix, ${t}-data-count`]:{color:e.colorError}}}},Ii=H(["Input","Shared"],(e=>{const t=F(e,ei(e));return[$i(t),Si(t)]}),ti,{resetFont:!1}),ji=H(["Input","Component"],(e=>{const t=F(e,ei(e));return[Oi(t),ki(t),_i(t),be(t)]}),ti,{resetFont:!1});function Ri(e,t,n){var o={}.atBegin;return function(e,t,n){var o,r=n||{},a=r.noTrailing,i=void 0!==a&&a,l=r.noLeading,s=void 0!==l&&l,c=r.debounceMode,u=void 0===c?void 0:c,d=!1,p=0;function m(){o&&clearTimeout(o)}function f(){for(var n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];var l=this,c=Date.now()-p;function f(){p=Date.now(),t.apply(l,r)}function g(){o=void 0}d||(s||!u||o||f(),m(),void 0===u&&c>e?s?(p=Date.now(),i||(o=setTimeout(u?g:f,e))):f():!0!==i&&(o=setTimeout(u?g:f,void 0===u?e-c:e)))}return f.cancel=function(e){var t=(e||{}).upcomingOnly,n=void 0!==t&&t;m(),d=!n},f}(e,t,{debounceMode:!1!==(void 0!==o&&o)})}function Bi(e,t,n){var o=t.cloneNode(!0),r=Object.create(e,{target:{value:o},currentTarget:{value:o}});return o.value=n,"number"==typeof t.selectionStart&&"number"==typeof t.selectionEnd&&(o.selectionStart=t.selectionStart,o.selectionEnd=t.selectionEnd),o.setSelectionRange=function(){t.setSelectionRange.apply(t,arguments)},r}function Ni(e,t,n,o){if(n){var r=t;"click"!==t.type?"file"===e.type||void 0===o?n(r):n(r=Bi(t,e,o)):n(r=Bi(t,e,""))}}function Ti(e,t){if(e){e.focus(t);var n=(t||{}).cursor;if(n){var o=e.value.length;switch(n){case"start":e.setSelectionRange(0,0);break;case"end":e.setSelectionRange(o,o);break;default:e.setSelectionRange(0,o)}}}}var Pi=O.forwardRef((function(t,n){var o,r,a,i=t.inputElement,l=t.children,c=t.prefixCls,d=t.prefix,m=t.suffix,f=t.addonBefore,g=t.addonAfter,h=t.className,b=t.style,v=t.disabled,y=t.readOnly,w=t.focused,x=t.triggerFocus,$=t.allowClear,E=t.value,S=t.handleReset,k=t.hidden,_=t.classes,I=t.classNames,j=t.dataAttrs,R=t.styles,B=t.components,N=t.onClear,T=null!=l?l:i,P=(null==B?void 0:B.affixWrapper)||"span",A=(null==B?void 0:B.groupWrapper)||"span",M=(null==B?void 0:B.wrapper)||"span",z=(null==B?void 0:B.groupAddon)||"span",D=e.useRef(null),L=function(e){return!!(e.prefix||e.suffix||e.allowClear)}(t),H=e.cloneElement(T,{value:E,className:C(null===(o=T.props)||void 0===o?void 0:o.className,!L&&(null==I?void 0:I.variant))||null}),F=e.useRef(null);if(O.useImperativeHandle(n,(function(){return{nativeElement:F.current||D.current}})),L){var W=null;if($){var V=!v&&!y&&E,q="".concat(c,"-clear-icon"),X="object"===u($)&&null!=$&&$.clearIcon?$.clearIcon:"✖";W=O.createElement("button",{type:"button",tabIndex:-1,onClick:function(e){null==S||S(e),null==N||N()},onMouseDown:function(e){return e.preventDefault()},className:C(q,re(re({},"".concat(q,"-hidden"),!V),"".concat(q,"-has-suffix"),!!m))},X)}var G="".concat(c,"-affix-wrapper"),K=C(G,re(re(re(re(re({},"".concat(c,"-disabled"),v),"".concat(G,"-disabled"),v),"".concat(G,"-focused"),w),"".concat(G,"-readonly"),y),"".concat(G,"-input-with-clear-btn"),m&&$&&E),null==_?void 0:_.affixWrapper,null==I?void 0:I.affixWrapper,null==I?void 0:I.variant),Y=(m||$)&&O.createElement("span",{className:C("".concat(c,"-suffix"),null==I?void 0:I.suffix),style:null==R?void 0:R.suffix},W,m);H=O.createElement(P,p({className:K,style:null==R?void 0:R.affixWrapper,onClick:function(e){var t;null!==(t=D.current)&&void 0!==t&&t.contains(e.target)&&(null==x||x())}},null==j?void 0:j.affixWrapper,{ref:D}),d&&O.createElement("span",{className:C("".concat(c,"-prefix"),null==I?void 0:I.prefix),style:null==R?void 0:R.prefix},d),H,Y)}if(function(e){return!(!e.addonBefore&&!e.addonAfter)}(t)){var U="".concat(c,"-group"),Q="".concat(U,"-addon"),Z="".concat(U,"-wrapper"),J=C("".concat(c,"-wrapper"),U,null==_?void 0:_.wrapper,null==I?void 0:I.wrapper),ee=C(Z,re({},"".concat(Z,"-disabled"),v),null==_?void 0:_.group,null==I?void 0:I.groupWrapper);H=O.createElement(A,{className:ee,ref:F},O.createElement(M,{className:J},f&&O.createElement(z,{className:Q},f),H,g&&O.createElement(z,{className:Q},g)))}return O.cloneElement(H,{className:C(null===(r=H.props)||void 0===r?void 0:r.className,h)||null,style:s(s({},null===(a=H.props)||void 0===a?void 0:a.style),b),hidden:k})})),Ai=["show"];function Mi(t,n){return e.useMemo((function(){var e={};n&&(e.show="object"===u(n)&&n.formatter?n.formatter:!!n);var o=e=s(s({},e),t),r=o.show,a=x(o,Ai);return s(s({},a),{},{show:!!r,showFormatter:"function"==typeof r?r:void 0,strategy:a.strategy||function(e){return e.length}})}),[t,n])}const zi=e=>{let t;return"object"==typeof e&&(null==e?void 0:e.clearIcon)?t=e:e&&(t={clearIcon:O.createElement(ve,null)}),t};var Di=e.createContext(null),Li=e.createContext({}),Hi=["prefixCls","className","containerRef"],Fi=function(t){var n=t.prefixCls,o=t.className,r=t.containerRef,a=x(t,Hi),i=e.useContext(Li).panel,s=l(i,r);return e.createElement("div",p({className:C("".concat(n,"-content"),o),role:"dialog",ref:s},ye(t,{aria:!0}),{"aria-modal":"true"},a))};function Wi(e){return"string"==typeof e&&String(Number(e))===e?(ae(!1,"Invalid value type of `width` or `height` which should be number type instead."),Number(e)):e}var Vi={width:0,height:0,overflow:"hidden",outline:"none",position:"absolute"};function qi(t,n){var o,r,a,i=t.prefixCls,l=t.open,c=t.placement,u=t.inline,d=t.push,m=t.forceRender,f=t.autoFocus,g=t.keyboard,h=t.classNames,b=t.rootClassName,v=t.rootStyle,y=t.zIndex,w=t.className,x=t.id,E=t.style,S=t.motion,O=t.width,k=t.height,I=t.children,j=t.mask,R=t.maskClosable,B=t.maskMotion,N=t.maskClassName,T=t.maskStyle,P=t.afterOpenChange,A=t.onClose,M=t.onMouseEnter,z=t.onMouseOver,D=t.onMouseLeave,L=t.onClick,H=t.onKeyDown,F=t.onKeyUp,W=t.styles,V=t.drawerRender,q=e.useRef(),X=e.useRef(),G=e.useRef();e.useImperativeHandle(n,(function(){return q.current}));e.useEffect((function(){var e;l&&f&&(null===(e=q.current)||void 0===e||e.focus({preventScroll:!0}))}),[l]);var K=e.useState(!1),Y=$(K,2),U=Y[0],Q=Y[1],Z=e.useContext(Di),J=null!==(o=null!==(r=null===(a="boolean"==typeof d?d?{}:{distance:0}:d||{})||void 0===a?void 0:a.distance)&&void 0!==r?r:null==Z?void 0:Z.pushDistance)&&void 0!==o?o:180,ee=e.useMemo((function(){return{pushDistance:J,push:function(){Q(!0)},pull:function(){Q(!1)}}}),[J]);e.useEffect((function(){var e,t;l?null==Z||null===(e=Z.push)||void 0===e||e.call(Z):null==Z||null===(t=Z.pull)||void 0===t||t.call(Z)}),[l]),e.useEffect((function(){return function(){var e;null==Z||null===(e=Z.pull)||void 0===e||e.call(Z)}}),[]);var te=e.createElement(_,p({key:"mask"},B,{visible:j&&l}),(function(t,n){var o=t.className,r=t.style;return e.createElement("div",{className:C("".concat(i,"-mask"),o,null==h?void 0:h.mask,N),style:s(s(s({},r),T),null==W?void 0:W.mask),onClick:R&&l?A:void 0,ref:n})})),ne="function"==typeof S?S(c):S,ae={};if(U&&J)switch(c){case"top":ae.transform="translateY(".concat(J,"px)");break;case"bottom":ae.transform="translateY(".concat(-J,"px)");break;case"left":ae.transform="translateX(".concat(J,"px)");break;default:ae.transform="translateX(".concat(-J,"px)")}"left"===c||"right"===c?ae.width=Wi(O):ae.height=Wi(k);var ie={onMouseEnter:M,onMouseOver:z,onMouseLeave:D,onClick:L,onKeyDown:H,onKeyUp:F},le=e.createElement(_,p({key:"panel"},ne,{visible:l,forceRender:m,onVisibleChanged:function(e){null==P||P(e)},removeOnLeave:!1,leavedClassName:"".concat(i,"-content-wrapper-hidden")}),(function(n,o){var r=n.className,a=n.style,l=e.createElement(Fi,p({id:x,containerRef:o,prefixCls:i,className:C(w,null==h?void 0:h.content),style:s(s({},E),null==W?void 0:W.content)},ye(t,{aria:!0}),ie),I);return e.createElement("div",p({className:C("".concat(i,"-content-wrapper"),null==h?void 0:h.wrapper,r),style:s(s(s({},ae),a),null==W?void 0:W.wrapper)},ye(t,{data:!0})),V?V(l):l)})),se=s({},v);return y&&(se.zIndex=y),e.createElement(Di.Provider,{value:ee},e.createElement("div",{className:C(i,"".concat(i,"-").concat(c),b,re(re({},"".concat(i,"-open"),l),"".concat(i,"-inline"),u)),style:se,tabIndex:-1,ref:q,onKeyDown:function(e){var t=e.keyCode,n=e.shiftKey;switch(t){case oe.TAB:var o;if(t===oe.TAB)if(n||document.activeElement!==G.current){if(n&&document.activeElement===X.current){var r;null===(r=G.current)||void 0===r||r.focus({preventScroll:!0})}}else null===(o=X.current)||void 0===o||o.focus({preventScroll:!0});break;case oe.ESC:A&&g&&(e.stopPropagation(),A(e))}}},te,e.createElement("div",{tabIndex:0,ref:X,style:Vi,"aria-hidden":"true","data-sentinel":"start"}),le,e.createElement("div",{tabIndex:0,ref:G,style:Vi,"aria-hidden":"true","data-sentinel":"end"})))}var Xi=e.forwardRef(qi),Gi=function(t){var n=t.open,o=void 0!==n&&n,r=t.prefixCls,a=void 0===r?"rc-drawer":r,i=t.placement,l=void 0===i?"right":i,c=t.autoFocus,u=void 0===c||c,d=t.keyboard,p=void 0===d||d,m=t.width,f=void 0===m?378:m,g=t.mask,h=void 0===g||g,b=t.maskClosable,v=void 0===b||b,y=t.getContainer,w=t.forceRender,x=t.afterOpenChange,C=t.destroyOnClose,E=t.onMouseEnter,S=t.onMouseOver,O=t.onMouseLeave,_=t.onClick,I=t.onKeyDown,j=t.onKeyUp,R=t.panelRef,B=e.useState(!1),N=$(B,2),T=N[0],A=N[1],M=e.useState(!1),z=$(M,2),D=z[0],L=z[1];k((function(){L(!0)}),[]);var H=!!D&&o,F=e.useRef(),W=e.useRef();k((function(){H&&(W.current=document.activeElement)}),[H]);var V=e.useMemo((function(){return{panel:R}}),[R]);if(!w&&!T&&!H&&C)return null;var q={onMouseEnter:E,onMouseOver:S,onMouseLeave:O,onClick:_,onKeyDown:I,onKeyUp:j},X=s(s({},t),{},{open:H,prefixCls:a,placement:l,autoFocus:u,keyboard:p,width:f,mask:h,maskClosable:v,inline:!1===y,afterOpenChange:function(e){var t,n;(A(e),null==x||x(e),e||!W.current||null!==(t=F.current)&&void 0!==t&&t.contains(W.current))||(null===(n=W.current)||void 0===n||n.focus({preventScroll:!0}))},ref:F},q);return e.createElement(Li.Provider,{value:V},e.createElement(P,{open:H||w||T,autoDestroy:!1,getContainer:y,autoLock:h&&(H||T)},e.createElement(Xi,X)))};const Ki=t=>{var n,o;const{prefixCls:r,title:a,footer:i,extra:l,loading:s,onClose:c,headerStyle:u,bodyStyle:d,footerStyle:p,children:m,classNames:f,styles:g}=t,h=X("drawer"),b=e.useCallback((t=>e.createElement("button",{type:"button",onClick:c,className:`${r}-close`},t)),[c]),[v,y]=we(xe(t),xe(h),{closable:!0,closeIconRender:b}),w=e.useMemo((()=>{var t,n;return a||v?e.createElement("div",{style:Object.assign(Object.assign(Object.assign({},null===(t=h.styles)||void 0===t?void 0:t.header),u),null==g?void 0:g.header),className:C(`${r}-header`,{[`${r}-header-close-only`]:v&&!a&&!l},null===(n=h.classNames)||void 0===n?void 0:n.header,null==f?void 0:f.header)},e.createElement("div",{className:`${r}-header-title`},y,a&&e.createElement("div",{className:`${r}-title`},a)),l&&e.createElement("div",{className:`${r}-extra`},l)):null}),[v,y,l,u,r,a]),x=e.useMemo((()=>{var t,n;if(!i)return null;const o=`${r}-footer`;return e.createElement("div",{className:C(o,null===(t=h.classNames)||void 0===t?void 0:t.footer,null==f?void 0:f.footer),style:Object.assign(Object.assign(Object.assign({},null===(n=h.styles)||void 0===n?void 0:n.footer),p),null==g?void 0:g.footer)},i)}),[i,p,r]);return e.createElement(e.Fragment,null,w,e.createElement("div",{className:C(`${r}-body`,null==f?void 0:f.body,null===(n=h.classNames)||void 0===n?void 0:n.body),style:Object.assign(Object.assign(Object.assign({},null===(o=h.styles)||void 0===o?void 0:o.body),d),null==g?void 0:g.body)},s?e.createElement(Ce,{active:!0,title:!1,paragraph:{rows:5},className:`${r}-body-skeleton`}):m),x)},Yi=e=>{const t="100%";return{left:`translateX(-${t})`,right:`translateX(${t})`,top:`translateY(-${t})`,bottom:`translateY(${t})`}[e]},Ui=(e,t)=>({"&-enter, &-appear":Object.assign(Object.assign({},e),{"&-active":t}),"&-leave":Object.assign(Object.assign({},t),{"&-active":e})}),Qi=(e,t)=>Object.assign({"&-enter, &-appear, &-leave":{"&-start":{transition:"none"},"&-active":{transition:`all ${t}`}}},Ui({opacity:e},{opacity:1})),Zi=(e,t)=>[Qi(.7,t),Ui({transform:Yi(e)},{transform:"none"})],Ji=e=>{const{componentCls:t,motionDurationSlow:n}=e;return{[t]:{[`${t}-mask-motion`]:Qi(0,n),[`${t}-panel-motion`]:["left","right","top","bottom"].reduce(((e,t)=>Object.assign(Object.assign({},e),{[`&-${t}`]:Zi(t,n)})),{})}}},el=e=>{const{borderRadiusSM:t,componentCls:n,zIndexPopup:o,colorBgMask:r,colorBgElevated:a,motionDurationSlow:i,motionDurationMid:l,paddingXS:s,padding:c,paddingLG:u,fontSizeLG:d,lineHeightLG:p,lineWidth:m,lineType:f,colorSplit:g,marginXS:h,colorIcon:b,colorIconHover:v,colorBgTextHover:y,colorBgTextActive:w,colorText:x,fontWeightStrong:C,footerPaddingBlock:$,footerPaddingInline:E,calc:S}=e,O=`${n}-content-wrapper`;return{[n]:{position:"fixed",inset:0,zIndex:o,pointerEvents:"none",color:x,"&-pure":{position:"relative",background:a,display:"flex",flexDirection:"column",[`&${n}-left`]:{boxShadow:e.boxShadowDrawerLeft},[`&${n}-right`]:{boxShadow:e.boxShadowDrawerRight},[`&${n}-top`]:{boxShadow:e.boxShadowDrawerUp},[`&${n}-bottom`]:{boxShadow:e.boxShadowDrawerDown}},"&-inline":{position:"absolute"},[`${n}-mask`]:{position:"absolute",inset:0,zIndex:o,background:r,pointerEvents:"auto"},[O]:{position:"absolute",zIndex:o,maxWidth:"100vw",transition:`all ${i}`,"&-hidden":{display:"none"}},[`&-left > ${O}`]:{top:0,bottom:0,left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowDrawerLeft},[`&-right > ${O}`]:{top:0,right:{_skip_check_:!0,value:0},bottom:0,boxShadow:e.boxShadowDrawerRight},[`&-top > ${O}`]:{top:0,insetInline:0,boxShadow:e.boxShadowDrawerUp},[`&-bottom > ${O}`]:{bottom:0,insetInline:0,boxShadow:e.boxShadowDrawerDown},[`${n}-content`]:{display:"flex",flexDirection:"column",width:"100%",height:"100%",overflow:"auto",background:a,pointerEvents:"auto"},[`${n}-header`]:{display:"flex",flex:0,alignItems:"center",padding:`${L(c)} ${L(u)}`,fontSize:d,lineHeight:p,borderBottom:`${L(m)} ${f} ${g}`,"&-title":{display:"flex",flex:1,alignItems:"center",minWidth:0,minHeight:0}},[`${n}-extra`]:{flex:"none"},[`${n}-close`]:Object.assign({display:"inline-flex",width:S(d).add(s).equal(),height:S(d).add(s).equal(),borderRadius:t,justifyContent:"center",alignItems:"center",marginInlineEnd:h,color:b,fontWeight:C,fontSize:d,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",textDecoration:"none",background:"transparent",border:0,cursor:"pointer",transition:`all ${l}`,textRendering:"auto","&:hover":{color:v,backgroundColor:y,textDecoration:"none"},"&:active":{backgroundColor:w}},$e(e)),[`${n}-title`]:{flex:1,margin:0,fontWeight:e.fontWeightStrong,fontSize:d,lineHeight:p},[`${n}-body`]:{flex:1,minWidth:0,minHeight:0,padding:u,overflow:"auto",[`${n}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center"}},[`${n}-footer`]:{flexShrink:0,padding:`${L($)} ${L(E)}`,borderTop:`${L(m)} ${f} ${g}`},"&-rtl":{direction:"rtl"}}}},tl=H("Drawer",(e=>{const t=F(e,{});return[el(t),Ji(t)]}),(e=>({zIndexPopup:e.zIndexPopupBase,footerPaddingBlock:e.paddingXS,footerPaddingInline:e.padding})));var nl=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const ol={distance:180},rl=t=>{const{rootClassName:n,width:o,height:r,size:a="default",mask:i=!0,push:l=ol,open:s,afterOpenChange:c,onClose:u,prefixCls:d,getContainer:p,style:m,className:f,visible:g,afterVisibleChange:h,maskStyle:b,drawerStyle:v,contentWrapperStyle:y,destroyOnClose:w,destroyOnHidden:x}=t,$=nl(t,["rootClassName","width","height","size","mask","push","open","afterOpenChange","onClose","prefixCls","getContainer","style","className","visible","afterVisibleChange","maskStyle","drawerStyle","contentWrapperStyle","destroyOnClose","destroyOnHidden"]),{getPopupContainer:E,getPrefixCls:S,direction:O,className:k,style:_,classNames:I,styles:j}=X("drawer"),R=S("drawer",d),[B,N,T]=tl(R),P=void 0===p&&E?()=>E(document.body):p,A=C({"no-mask":!i,[`${R}-rtl`]:"rtl"===O},n,N,T),M=e.useMemo((()=>null!=o?o:"large"===a?736:378),[o,a]),z=e.useMemo((()=>null!=r?r:"large"===a?736:378),[r,a]),D={motionName:Z(R,"mask-motion"),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500},L=Ee(),[H,F]=Q("Drawer",$.zIndex),{classNames:W={},styles:V={}}=$;return B(e.createElement(Y,{form:!0,space:!0},e.createElement(ee.Provider,{value:F},e.createElement(Gi,Object.assign({prefixCls:R,onClose:u,maskMotion:D,motion:e=>({motionName:Z(R,`panel-motion-${e}`),motionAppear:!0,motionEnter:!0,motionLeave:!0,motionDeadline:500})},$,{classNames:{mask:C(W.mask,I.mask),content:C(W.content,I.content),wrapper:C(W.wrapper,I.wrapper)},styles:{mask:Object.assign(Object.assign(Object.assign({},V.mask),b),j.mask),content:Object.assign(Object.assign(Object.assign({},V.content),v),j.content),wrapper:Object.assign(Object.assign(Object.assign({},V.wrapper),y),j.wrapper)},open:null!=s?s:g,mask:i,push:l,width:M,height:z,style:Object.assign(Object.assign({},_),m),className:C(k,f),rootClassName:A,getContainer:P,afterOpenChange:null!=c?c:h,panelRef:L,zIndex:H,destroyOnClose:null!=x?x:w}),e.createElement(Ki,Object.assign({prefixCls:R},$,{onClose:u}))))))};function al(e){return["small","middle","large"].includes(e)}function il(e){return!!e&&("number"==typeof e&&!Number.isNaN(e))}rl._InternalPanelDoNotUseOrYouWillBeFired=t=>{const{prefixCls:n,style:o,className:r,placement:a="right"}=t,i=nl(t,["prefixCls","style","className","placement"]),{getPrefixCls:l}=e.useContext(A),s=l("drawer",n),[c,u,d]=tl(s),p=C(s,`${s}-pure`,`${s}-${a}`,u,d,r);return c(e.createElement("div",{className:p,style:o},e.createElement(Ki,Object.assign({prefixCls:s},i))))};const ll=O.createContext({latestIndex:0}),sl=ll.Provider,cl=({className:t,index:n,children:o,split:r,style:a})=>{const{latestIndex:i}=e.useContext(ll);return null==o?null:e.createElement(e.Fragment,null,e.createElement("div",{className:t,style:a},o),n<i&&r&&e.createElement("span",{className:`${t}-split`},r))};const ul=e.forwardRef(((t,n)=>{var o;const{getPrefixCls:r,direction:a,size:i,className:l,style:s,classNames:c,styles:u}=X("space"),{size:p=(null!=i?i:"small"),align:m,className:f,rootClassName:g,children:h,direction:b="horizontal",prefixCls:v,split:y,style:w,wrap:x=!1,classNames:$,styles:E}=t,S=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[O,k]=Array.isArray(p)?p:[p,p],_=al(k),I=al(O),j=il(k),R=il(O),B=d(h,{keepEmpty:!0}),N=void 0===m&&"horizontal"===b?"center":m,T=r("space",v),[P,A,M]=Se(T),z=C(T,l,A,`${T}-${b}`,{[`${T}-rtl`]:"rtl"===a,[`${T}-align-${N}`]:N,[`${T}-gap-row-${k}`]:_,[`${T}-gap-col-${O}`]:I},f,g,M),D=C(`${T}-item`,null!==(o=null==$?void 0:$.item)&&void 0!==o?o:c.item);let L=0;const H=B.map(((t,n)=>{var o;null!=t&&(L=n);const r=(null==t?void 0:t.key)||`${D}-${n}`;return e.createElement(cl,{className:D,key:r,index:n,split:y,style:null!==(o=null==E?void 0:E.item)&&void 0!==o?o:u.item},t)})),F=e.useMemo((()=>({latestIndex:L})),[L]);if(0===B.length)return null;const W={};return x&&(W.flexWrap="wrap"),!I&&R&&(W.columnGap=O),!_&&j&&(W.rowGap=k),P(e.createElement("div",Object.assign({ref:n,className:z,style:Object.assign(Object.assign(Object.assign({},W),s),w)},S),e.createElement(sl,{value:F},H)))}));ul.Compact=Oe;const dl=O.createContext(void 0),{Provider:pl}=dl;var ml={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM504 618H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM312 490v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8z"}}]},name:"file-text",theme:"outlined"},fl=function(t,n){return e.createElement(D,p({},t,{ref:n,icon:ml}))},gl=e.forwardRef(fl);const hl=e=>{const{icon:t,description:n,prefixCls:o,className:r}=e,a=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["icon","description","prefixCls","className"]),i=O.createElement("div",{className:`${o}-icon`},O.createElement(gl,null));return O.createElement("div",Object.assign({},a,{className:C(r,`${o}-content`)}),t||n?O.createElement(O.Fragment,null,t&&O.createElement("div",{className:`${o}-icon`},t),n&&O.createElement("div",{className:`${o}-description`},n)):i)},bl=e.memo(hl),vl=e=>0===e?0:e-Math.sqrt(Math.pow(e,2)/2),yl=e=>{const{componentCls:t,floatButtonSize:n,motionDurationSlow:o,motionEaseInOutCirc:r,calc:a}=e,i=new v("antFloatButtonMoveTopIn",{"0%":{transform:`translate3d(0, ${L(n)}, 0)`,transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),l=new v("antFloatButtonMoveTopOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:`translate3d(0, ${L(n)}, 0)`,transformOrigin:"0 0",opacity:0}}),s=new v("antFloatButtonMoveRightIn",{"0%":{transform:`translate3d(${L(a(n).mul(-1).equal())}, 0, 0)`,transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),c=new v("antFloatButtonMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:`translate3d(${L(a(n).mul(-1).equal())}, 0, 0)`,transformOrigin:"0 0",opacity:0}}),u=new v("antFloatButtonMoveBottomIn",{"0%":{transform:`translate3d(0, ${L(a(n).mul(-1).equal())}, 0)`,transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),d=new v("antFloatButtonMoveBottomOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:`translate3d(0, ${L(a(n).mul(-1).equal())}, 0)`,transformOrigin:"0 0",opacity:0}}),p=new v("antFloatButtonMoveLeftIn",{"0%":{transform:`translate3d(${L(n)}, 0, 0)`,transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),m=new v("antFloatButtonMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:`translate3d(${L(n)}, 0, 0)`,transformOrigin:"0 0",opacity:0}}),f=`${t}-group`;return[{[f]:{[`&${f}-top ${f}-wrap`]:y(`${f}-wrap`,i,l,o,!0),[`&${f}-bottom ${f}-wrap`]:y(`${f}-wrap`,u,d,o,!0),[`&${f}-left ${f}-wrap`]:y(`${f}-wrap`,p,m,o,!0),[`&${f}-right ${f}-wrap`]:y(`${f}-wrap`,s,c,o,!0)}},{[`${f}-wrap`]:{[`&${f}-wrap-enter, &${f}-wrap-appear`]:{opacity:0,animationTimingFunction:r},[`&${f}-wrap-leave`]:{opacity:1,animationTimingFunction:r}}}]},wl=e=>{const{antCls:t,componentCls:n,floatButtonSize:o,margin:r,borderRadiusLG:a,borderRadiusSM:i,badgeOffset:l,floatButtonBodyPadding:s,zIndexPopupBase:c,calc:u}=e,d=`${n}-group`;return{[d]:Object.assign(Object.assign({},V(e)),{zIndex:c,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",border:"none",position:"fixed",height:"auto",boxShadow:"none",minWidth:o,minHeight:o,insetInlineEnd:e.floatButtonInsetInlineEnd,bottom:e.floatButtonInsetBlockEnd,borderRadius:a,[`${d}-wrap`]:{zIndex:-1,display:"flex",justifyContent:"center",alignItems:"center",position:"absolute"},[`&${d}-rtl`]:{direction:"rtl"},[n]:{position:"static"}}),[`${d}-top > ${d}-wrap`]:{flexDirection:"column",top:"auto",bottom:u(o).add(r).equal(),"&::after":{content:'""',position:"absolute",width:"100%",height:r,bottom:u(r).mul(-1).equal()}},[`${d}-bottom > ${d}-wrap`]:{flexDirection:"column",top:u(o).add(r).equal(),bottom:"auto","&::after":{content:'""',position:"absolute",width:"100%",height:r,top:u(r).mul(-1).equal()}},[`${d}-right > ${d}-wrap`]:{flexDirection:"row",left:{_skip_check_:!0,value:u(o).add(r).equal()},right:{_skip_check_:!0,value:"auto"},"&::after":{content:'""',position:"absolute",width:r,height:"100%",left:{_skip_check_:!0,value:u(r).mul(-1).equal()}}},[`${d}-left > ${d}-wrap`]:{flexDirection:"row",left:{_skip_check_:!0,value:"auto"},right:{_skip_check_:!0,value:u(o).add(r).equal()},"&::after":{content:'""',position:"absolute",width:r,height:"100%",right:{_skip_check_:!0,value:u(r).mul(-1).equal()}}},[`${d}-circle`]:{gap:r,[`${d}-wrap`]:{gap:r}},[`${d}-square`]:{[`${n}-square`]:{padding:0,borderRadius:0,[`&${d}-trigger`]:{borderRadius:a},"&:first-child":{borderStartStartRadius:a,borderStartEndRadius:a},"&:last-child":{borderEndStartRadius:a,borderEndEndRadius:a},"&:not(:last-child)":{borderBottom:`${L(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},[`${t}-badge`]:{[`${t}-badge-count`]:{top:u(u(s).add(l)).mul(-1).equal(),insetInlineEnd:u(u(s).add(l)).mul(-1).equal()}}},[`${d}-wrap`]:{borderRadius:a,boxShadow:e.boxShadowSecondary,[`${n}-square`]:{boxShadow:"none",borderRadius:0,padding:s,[`${n}-body`]:{width:e.floatButtonBodySize,height:e.floatButtonBodySize,borderRadius:i}}}},[`${d}-top > ${d}-wrap, ${d}-bottom > ${d}-wrap`]:{[`> ${n}-square`]:{"&:first-child":{borderStartStartRadius:a,borderStartEndRadius:a},"&:last-child":{borderEndStartRadius:a,borderEndEndRadius:a},"&:not(:last-child)":{borderBottom:`${L(e.lineWidth)} ${e.lineType} ${e.colorSplit}`}}},[`${d}-left > ${d}-wrap, ${d}-right > ${d}-wrap`]:{[`> ${n}-square`]:{"&:first-child":{borderStartStartRadius:a,borderEndStartRadius:a},"&:last-child":{borderStartEndRadius:a,borderEndEndRadius:a},"&:not(:last-child)":{borderInlineEnd:`${L(e.lineWidth)} ${e.lineType} ${e.colorSplit}`}}},[`${d}-circle-shadow`]:{boxShadow:"none"},[`${d}-square-shadow`]:{boxShadow:e.boxShadowSecondary,[`${n}-square`]:{boxShadow:"none",padding:s,[`${n}-body`]:{width:e.floatButtonBodySize,height:e.floatButtonBodySize,borderRadius:i}}}}},xl=e=>{const{antCls:t,componentCls:n,floatButtonBodyPadding:o,floatButtonIconSize:r,floatButtonSize:a,borderRadiusLG:i,badgeOffset:l,dotOffsetInSquare:s,dotOffsetInCircle:c,zIndexPopupBase:u,calc:d}=e;return{[n]:Object.assign(Object.assign({},V(e)),{border:"none",position:"fixed",cursor:"pointer",zIndex:u,display:"block",width:a,height:a,insetInlineEnd:e.floatButtonInsetInlineEnd,bottom:e.floatButtonInsetBlockEnd,boxShadow:e.boxShadowSecondary,"&-pure":{position:"relative",inset:"auto"},"&:empty":{display:"none"},[`${t}-badge`]:{width:"100%",height:"100%",[`${t}-badge-count`]:{transform:"translate(0, 0)",transformOrigin:"center",top:d(l).mul(-1).equal(),insetInlineEnd:d(l).mul(-1).equal()}},[`${n}-body`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",transition:`all ${e.motionDurationMid}`,[`${n}-content`]:{overflow:"hidden",textAlign:"center",minHeight:a,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:`${L(d(o).div(2).equal())} ${L(o)}`,[`${n}-icon`]:{textAlign:"center",margin:"auto",width:r,fontSize:r,lineHeight:1}}}}),[`${n}-rtl`]:{direction:"rtl"},[`${n}-circle`]:{height:a,borderRadius:"50%",[`${t}-badge`]:{[`${t}-badge-dot`]:{top:c,insetInlineEnd:c}},[`${n}-body`]:{borderRadius:"50%"}},[`${n}-square`]:{height:"auto",minHeight:a,borderRadius:i,[`${t}-badge`]:{[`${t}-badge-dot`]:{top:s,insetInlineEnd:s}},[`${n}-body`]:{height:"auto",borderRadius:i}},[`${n}-default`]:{backgroundColor:e.floatButtonBackgroundColor,transition:`background-color ${e.motionDurationMid}`,[`${n}-body`]:{backgroundColor:e.floatButtonBackgroundColor,transition:`background-color ${e.motionDurationMid}`,"&:hover":{backgroundColor:e.colorFillContent},[`${n}-content`]:{[`${n}-icon`]:{color:e.colorText},[`${n}-description`]:{display:"flex",alignItems:"center",lineHeight:L(e.fontSizeLG),color:e.colorText,fontSize:e.fontSizeSM}}}},[`${n}-primary`]:{backgroundColor:e.colorPrimary,[`${n}-body`]:{backgroundColor:e.colorPrimary,transition:`background-color ${e.motionDurationMid}`,"&:hover":{backgroundColor:e.colorPrimaryHover},[`${n}-content`]:{[`${n}-icon`]:{color:e.colorTextLightSolid},[`${n}-description`]:{display:"flex",alignItems:"center",lineHeight:L(e.fontSizeLG),color:e.colorTextLightSolid,fontSize:e.fontSizeSM}}}}}},Cl=H("FloatButton",(e=>{const{colorTextLightSolid:t,colorBgElevated:n,controlHeightLG:o,marginXXL:r,marginLG:a,fontSize:i,fontSizeIcon:l,controlItemBgHover:s,paddingXXS:c,calc:u}=e,d=F(e,{floatButtonBackgroundColor:n,floatButtonColor:t,floatButtonHoverBackgroundColor:s,floatButtonFontSize:i,floatButtonIconSize:u(l).mul(1.5).equal(),floatButtonSize:o,floatButtonInsetBlockEnd:r,floatButtonInsetInlineEnd:a,floatButtonBodySize:u(o).sub(u(c).mul(2)).equal(),floatButtonBodyPadding:c,badgeOffset:u(c).mul(1.5).equal()});return[wl(d),xl(d),ke(e),yl(d)]}),(e=>({dotOffsetInCircle:vl(e.controlHeightLG/2),dotOffsetInSquare:vl(e.borderRadiusLG)})));const $l="float-btn",El=O.forwardRef(((t,n)=>{const{prefixCls:o,className:r,rootClassName:a,style:i,type:l="default",shape:s="circle",icon:c,description:u,tooltip:d,htmlType:p="button",badge:m={}}=t,f=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["prefixCls","className","rootClassName","style","type","shape","icon","description","tooltip","htmlType","badge"]),{getPrefixCls:g,direction:h}=e.useContext(A),b=e.useContext(dl),v=g($l,o),y=ge(v),[w,x,$]=Cl(v,y),E=C(x,$,y,v,r,a,`${v}-${l}`,`${v}-${b||s}`,{[`${v}-rtl`]:"rtl"===h}),[S]=Q("FloatButton",null==i?void 0:i.zIndex),k=Object.assign(Object.assign({},i),{zIndex:S}),_=ie(m,["title","children","status","text"]);let I=O.createElement("div",{className:`${v}-body`},O.createElement(bl,{prefixCls:v,description:u,icon:c}));"badge"in t&&(I=O.createElement(Uo,Object.assign({},_),I));const j=function(t){return null==t?null:"object"!=typeof t||e.isValidElement(t)?{title:t}:t}(d);return j&&(I=O.createElement(Bo,Object.assign({},j),I)),w(t.href?O.createElement("a",Object.assign({ref:n},f,{className:E,style:k}),I):O.createElement("button",Object.assign({ref:n},f,{className:E,style:k,type:p}),I))}));const Sl=O.forwardRef(((t,n)=>{const{prefixCls:o,className:r,type:a="default",shape:i="circle",visibilityHeight:l=400,icon:s=O.createElement(Po,null),target:c,onClick:u,duration:d=450}=t,p=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["prefixCls","className","type","shape","visibilityHeight","icon","target","onClick","duration"]),[m,f]=e.useState(0===l),g=O.useRef(null);O.useImperativeHandle(n,(()=>({nativeElement:g.current})));const h=()=>{var e;return(null===(e=g.current)||void 0===e?void 0:e.ownerDocument)||window},b=Zt((e=>{const t=nn(e.target);f(t>=l)}));e.useEffect((()=>{const e=(c||h)();return b({target:e}),null==e||e.addEventListener("scroll",b),()=>{b.cancel(),null==e||e.removeEventListener("scroll",b)}}),[c]);const v=e=>{on(0,{getContainer:c||h,duration:d}),null==u||u(e)},{getPrefixCls:y}=e.useContext(A),w=y($l,o),x=y(),$=e.useContext(dl)||i,E=Object.assign({prefixCls:w,icon:s,type:a,shape:$},p);return O.createElement(_,{visible:m,motionName:`${x}-fade`},(({className:e},t)=>O.createElement(El,Object.assign({ref:I(g,t)},E,{onClick:v,className:C(r,e)}))))}));const Ol=e=>{var t;const{prefixCls:n,className:o,style:r,shape:a="circle",type:i="default",placement:l="top",icon:s=O.createElement(gl,null),closeIcon:c,description:u,trigger:d,children:p,onOpenChange:m,open:f,onClick:g}=e,h=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(e,["prefixCls","className","style","shape","type","placement","icon","closeIcon","description","trigger","children","onOpenChange","open","onClick"]),{direction:b,getPrefixCls:v,closeIcon:y}=X("floatButtonGroup"),w=null!==(t=null!=c?c:y)&&void 0!==t?t:O.createElement(_e,null),x=v($l,n),$=ge(x),[S,k,I]=Cl(x,$),j=`${x}-group`,R=d&&["click","hover"].includes(d),B=l&&["top","left","right","bottom"].includes(l),N=C(j,k,I,$,o,{[`${j}-rtl`]:"rtl"===b,[`${j}-${a}`]:a,[`${j}-${a}-shadow`]:!R,[`${j}-${l}`]:R&&B}),[T]=Q("FloatButton",null==r?void 0:r.zIndex),P=Object.assign(Object.assign({},r),{zIndex:T}),A=C(k,`${j}-wrap`),[M,z]=K(!1,{value:f}),D=O.useRef(null),L="hover"===d,H="click"===d,F=E((e=>{M!==e&&(z(e),null==m||m(e))}));return O.useEffect((()=>{if(H){const e=e=>{var t;(null===(t=D.current)||void 0===t?void 0:t.contains(e.target))||F(!1)};return document.addEventListener("click",e,{capture:!0}),()=>document.removeEventListener("click",e,{capture:!0})}}),[H]),S(O.createElement(pl,{value:a},O.createElement("div",{ref:D,className:N,style:P,onMouseEnter:()=>{L&&F(!0)},onMouseLeave:()=>{L&&F(!1)}},R?O.createElement(O.Fragment,null,O.createElement(_,{visible:M,motionName:`${j}-wrap`},(({className:e})=>O.createElement("div",{className:C(e,A)},p))),O.createElement(El,Object.assign({type:i,icon:M?w:s,description:u,"aria-label":e["aria-label"],className:`${j}-trigger`,onClick:e=>{H&&F(!M),null==g||g(e)}},h))):p)))};var kl=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const _l=t=>{var{backTop:n}=t,o=kl(t,["backTop"]);return n?e.createElement(Sl,Object.assign({},o,{visibilityHeight:0})):e.createElement(El,Object.assign({},o))};El.BackTop=Sl,El.Group=Ol,El._InternalPanelDoNotUseOrYouWillBeFired=t=>{var{className:n,items:o}=t,r=kl(t,["className","items"]);const{prefixCls:a}=r,{getPrefixCls:i}=e.useContext(A),l=`${i($l,a)}-pure`;return o?e.createElement(Ol,Object.assign({className:C(n,l)},r),o.map(((t,n)=>e.createElement(_l,Object.assign({key:n},t))))):e.createElement(_l,Object.assign({className:C(n,l)},r))};var Il,jl=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","font-variant","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing","word-break","white-space"],Rl={};function Bl(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;Il||((Il=document.createElement("textarea")).setAttribute("tab-index","-1"),Il.setAttribute("aria-hidden","true"),Il.setAttribute("name","hiddenTextarea"),document.body.appendChild(Il)),e.getAttribute("wrap")?Il.setAttribute("wrap",e.getAttribute("wrap")):Il.removeAttribute("wrap");var r=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.getAttribute("id")||e.getAttribute("data-reactid")||e.getAttribute("name");if(t&&Rl[n])return Rl[n];var o=window.getComputedStyle(e),r=o.getPropertyValue("box-sizing")||o.getPropertyValue("-moz-box-sizing")||o.getPropertyValue("-webkit-box-sizing"),a=parseFloat(o.getPropertyValue("padding-bottom"))+parseFloat(o.getPropertyValue("padding-top")),i=parseFloat(o.getPropertyValue("border-bottom-width"))+parseFloat(o.getPropertyValue("border-top-width")),l={sizingStyle:jl.map((function(e){return"".concat(e,":").concat(o.getPropertyValue(e))})).join(";"),paddingSize:a,borderSize:i,boxSizing:r};return t&&n&&(Rl[n]=l),l}(e,t),a=r.paddingSize,i=r.borderSize,l=r.boxSizing,s=r.sizingStyle;Il.setAttribute("style","".concat(s,";").concat("\n  min-height:0 !important;\n  max-height:none !important;\n  height:0 !important;\n  visibility:hidden !important;\n  overflow:hidden !important;\n  position:absolute !important;\n  z-index:-1000 !important;\n  top:0 !important;\n  right:0 !important;\n  pointer-events: none !important;\n")),Il.value=e.value||e.placeholder||"";var c,u=void 0,d=void 0,p=Il.scrollHeight;if("border-box"===l?p+=i:"content-box"===l&&(p-=a),null!==n||null!==o){Il.value=" ";var m=Il.scrollHeight-a;null!==n&&(u=m*n,"border-box"===l&&(u=u+a+i),p=Math.max(u,p)),null!==o&&(d=m*o,"border-box"===l&&(d=d+a+i),c=p>d?"":"hidden",p=Math.min(d,p))}var f={height:p,overflowY:c,resize:"none"};return u&&(f.minHeight=u),d&&(f.maxHeight=d),f}var Nl=["prefixCls","defaultValue","value","autoSize","onResize","className","style","disabled","onChange","onInternalAutoSize"],Tl=e.forwardRef((function(t,n){var o=t,r=o.prefixCls,a=o.defaultValue,i=o.value,l=o.autoSize,c=o.onResize,d=o.className,f=o.style,g=o.disabled,h=o.onChange;o.onInternalAutoSize;var b=x(o,Nl),v=K(a,{value:i,postState:function(e){return null!=e?e:""}}),y=$(v,2),w=y[0],E=y[1],S=e.useRef();e.useImperativeHandle(n,(function(){return{textArea:S.current}}));var O=e.useMemo((function(){return l&&"object"===u(l)?[l.minRows,l.maxRows]:[]}),[l]),_=$(O,2),I=_[0],j=_[1],R=!!l,B=e.useState(2),N=$(B,2),T=N[0],P=N[1],A=e.useState(),M=$(A,2),z=M[0],D=M[1],L=function(){P(0)};k((function(){R&&L()}),[i,I,j,R]),k((function(){if(0===T)P(1);else if(1===T){var e=Bl(S.current,!1,I,j);P(2),D(e)}else!function(){try{if(document.activeElement===S.current){var e=S.current,t=e.selectionStart,n=e.selectionEnd,o=e.scrollTop;S.current.setSelectionRange(t,n),S.current.scrollTop=o}}catch(r){}}()}),[T]);var H=e.useRef(),F=function(){m.cancel(H.current)};e.useEffect((function(){return F}),[]);var W=R?z:null,V=s(s({},f),W);return 0!==T&&1!==T||(V.overflowY="hidden",V.overflowX="hidden"),e.createElement(Qt,{onResize:function(e){2===T&&(null==c||c(e),l&&(F(),H.current=m((function(){L()}))))},disabled:!(l||c)},e.createElement("textarea",p({},b,{ref:S,style:V,className:C(r,d,re({},"".concat(r,"-disabled"),g)),disabled:g,value:w,onChange:function(e){E(e.target.value),null==h||h(e)}})))})),Pl=["defaultValue","value","onFocus","onBlur","onChange","allowClear","maxLength","onCompositionStart","onCompositionEnd","suffix","prefixCls","showCount","count","className","style","disabled","hidden","classNames","styles","onResize","onClear","onPressEnter","readOnly","autoSize","onKeyDown"],Al=O.forwardRef((function(t,n){var o,r=t.defaultValue,a=t.value,i=t.onFocus,l=t.onBlur,c=t.onChange,u=t.allowClear,d=t.maxLength,m=t.onCompositionStart,g=t.onCompositionEnd,h=t.suffix,b=t.prefixCls,v=void 0===b?"rc-textarea":b,y=t.showCount,w=t.count,E=t.className,S=t.style,k=t.disabled,_=t.hidden,I=t.classNames,j=t.styles,R=t.onResize,B=t.onClear,N=t.onPressEnter,T=t.readOnly,P=t.autoSize,A=t.onKeyDown,M=x(t,Pl),z=K(r,{value:a,defaultValue:r}),D=$(z,2),L=D[0],H=D[1],F=null==L?"":String(L),W=O.useState(!1),V=$(W,2),q=V[0],X=V[1],G=O.useRef(!1),Y=O.useState(null),U=$(Y,2),Q=U[0],Z=U[1],J=e.useRef(null),ee=e.useRef(null),te=function(){var e;return null===(e=ee.current)||void 0===e?void 0:e.textArea},ne=function(){te().focus()};e.useImperativeHandle(n,(function(){var e;return{resizableTextArea:ee.current,focus:ne,blur:function(){te().blur()},nativeElement:(null===(e=J.current)||void 0===e?void 0:e.nativeElement)||te()}})),e.useEffect((function(){X((function(e){return!k&&e}))}),[k]);var oe=O.useState(null),ae=$(oe,2),ie=ae[0],le=ae[1];O.useEffect((function(){var e;ie&&(e=te()).setSelectionRange.apply(e,f(ie))}),[ie]);var se,ce=Mi(w,y),ue=null!==(o=ce.max)&&void 0!==o?o:d,de=Number(ue)>0,pe=ce.strategy(F),me=!!ue&&pe>ue,fe=function(e,t){var n=t;!G.current&&ce.exceedFormatter&&ce.max&&ce.strategy(t)>ce.max&&t!==(n=ce.exceedFormatter(t,{max:ce.max}))&&le([te().selectionStart||0,te().selectionEnd||0]),H(n),Ni(e.currentTarget,e,c,n)},ge=h;ce.show&&(se=ce.showFormatter?ce.showFormatter({value:F,count:pe,maxLength:ue}):"".concat(pe).concat(de?" / ".concat(ue):""),ge=O.createElement(O.Fragment,null,ge,O.createElement("span",{className:C("".concat(v,"-data-count"),null==I?void 0:I.count),style:null==j?void 0:j.count},se)));var he=!P&&!y&&!u;return O.createElement(Pi,{ref:J,value:F,allowClear:u,handleReset:function(e){H(""),ne(),Ni(te(),e,c)},suffix:ge,prefixCls:v,classNames:s(s({},I),{},{affixWrapper:C(null==I?void 0:I.affixWrapper,re(re({},"".concat(v,"-show-count"),y),"".concat(v,"-textarea-allow-clear"),u))}),disabled:k,focused:q,className:C(E,me&&"".concat(v,"-out-of-range")),style:s(s({},S),Q&&!he?{height:"auto"}:{}),dataAttrs:{affixWrapper:{"data-count":"string"==typeof se?se:void 0}},hidden:_,readOnly:T,onClear:B},O.createElement(Tl,p({},M,{autoSize:P,maxLength:d,onKeyDown:function(e){"Enter"===e.key&&N&&N(e),null==A||A(e)},onChange:function(e){fe(e,e.target.value)},onFocus:function(e){X(!0),null==i||i(e)},onBlur:function(e){X(!1),null==l||l(e)},onCompositionStart:function(e){G.current=!0,null==m||m(e)},onCompositionEnd:function(e){G.current=!1,fe(e,e.currentTarget.value),null==g||g(e)},className:C(null==I?void 0:I.textarea),style:s(s({},null==j?void 0:j.textarea),{},{resize:null==S?void 0:S.resize}),disabled:k,prefixCls:v,onResize:function(e){var t;null==R||R(e),null!==(t=te())&&void 0!==t&&t.style.height&&Z(!0)},ref:ee,readOnly:T})))}));const Ml=e=>{const{componentCls:t,paddingLG:n}=e,o=`${t}-textarea`;return{[`textarea${t}`]:{maxWidth:"100%",height:"auto",minHeight:e.controlHeight,lineHeight:e.lineHeight,verticalAlign:"bottom",transition:`all ${e.motionDurationSlow}`,resize:"vertical",[`&${t}-mouse-active`]:{transition:`all ${e.motionDurationSlow}, height 0s, width 0s`}},[`${t}-textarea-affix-wrapper-resize-dirty`]:{width:"auto"},[o]:{position:"relative","&-show-count":{[`${t}-data-count`]:{position:"absolute",bottom:e.calc(e.fontSize).mul(e.lineHeight).mul(-1).equal(),insetInlineEnd:0,color:e.colorTextDescription,whiteSpace:"nowrap",pointerEvents:"none"}},[`\n        &-allow-clear > ${t},\n        &-affix-wrapper${o}-has-feedback ${t}\n      `]:{paddingInlineEnd:n},[`&-affix-wrapper${t}-affix-wrapper`]:{padding:0,[`> textarea${t}`]:{fontSize:"inherit",border:"none",outline:"none",background:"transparent",minHeight:e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),"&:focus":{boxShadow:"none !important"}},[`${t}-suffix`]:{margin:0,"> *:not(:last-child)":{marginInline:0},[`${t}-clear-icon`]:{position:"absolute",insetInlineEnd:e.paddingInline,insetBlockStart:e.paddingXS},[`${o}-suffix`]:{position:"absolute",top:0,insetInlineEnd:e.paddingInline,bottom:0,zIndex:1,display:"inline-flex",alignItems:"center",margin:"auto",pointerEvents:"none"}}},[`&-affix-wrapper${t}-affix-wrapper-rtl`]:{[`${t}-suffix`]:{[`${t}-data-count`]:{direction:"ltr",insetInlineStart:0}}},[`&-affix-wrapper${t}-affix-wrapper-sm`]:{[`${t}-suffix`]:{[`${t}-clear-icon`]:{insetInlineEnd:e.paddingInlineSM}}}}}},zl=H(["Input","TextArea"],(e=>{const t=F(e,ei(e));return[Ml(t)]}),ti,{resetFont:!1});const Dl=e.forwardRef(((t,n)=>{var o;const{prefixCls:r,bordered:a=!0,size:i,disabled:l,status:s,allowClear:c,classNames:u,rootClassName:d,className:p,style:m,styles:f,variant:g,showCount:h,onMouseDown:b,onResize:v}=t,y=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["prefixCls","bordered","size","disabled","status","allowClear","classNames","rootClassName","className","style","styles","variant","showCount","onMouseDown","onResize"]),{getPrefixCls:w,direction:x,allowClear:$,autoComplete:E,className:S,style:O,classNames:k,styles:_}=X("textArea"),I=e.useContext(Ie),j=null!=l?l:I,{status:R,hasFeedback:B,feedbackIcon:N}=e.useContext(je),T=eo(R,s),P=e.useRef(null);e.useImperativeHandle(n,(()=>{var e;return{resizableTextArea:null===(e=P.current)||void 0===e?void 0:e.resizableTextArea,focus:e=>{var t,n;Ti(null===(n=null===(t=P.current)||void 0===t?void 0:t.resizableTextArea)||void 0===n?void 0:n.textArea,e)},blur:()=>{var e;return null===(e=P.current)||void 0===e?void 0:e.blur()}}}));const A=w("input",r),M=ge(A),[z,D,L]=Ii(A,d),[H]=zl(A,M),{compactSize:F,compactItemClassnames:W}=Re(A,x),V=Be((e=>{var t;return null!==(t=null!=i?i:F)&&void 0!==t?t:e})),[q,G]=to("textArea",g,a),K=zi(null!=c?c:$),[Y,U]=e.useState(!1),[Q,Z]=e.useState(!1);return z(H(e.createElement(Al,Object.assign({autoComplete:E},y,{style:Object.assign(Object.assign({},O),m),styles:Object.assign(Object.assign({},_),f),disabled:j,allowClear:K,className:C(L,M,p,d,W,S,Q&&`${A}-textarea-affix-wrapper-resize-dirty`),classNames:Object.assign(Object.assign(Object.assign({},u),k),{textarea:C({[`${A}-sm`]:"small"===V,[`${A}-lg`]:"large"===V},D,null==u?void 0:u.textarea,k.textarea,Y&&`${A}-mouse-active`),variant:C({[`${A}-${q}`]:G},Jn(A,T)),affixWrapper:C(`${A}-textarea-affix-wrapper`,{[`${A}-affix-wrapper-rtl`]:"rtl"===x,[`${A}-affix-wrapper-sm`]:"small"===V,[`${A}-affix-wrapper-lg`]:"large"===V,[`${A}-textarea-show-count`]:h||(null===(o=t.count)||void 0===o?void 0:o.show)},D)}),prefixCls:A,suffix:B&&e.createElement("span",{className:`${A}-textarea-suffix`},N),showCount:h,ref:P,onResize:e=>{var t,n;if(null==v||v(e),Y&&"function"==typeof getComputedStyle){const e=null===(n=null===(t=P.current)||void 0===t?void 0:t.nativeElement)||void 0===n?void 0:n.querySelector("textarea");e&&"both"===getComputedStyle(e).resize&&Z(!0)}},onMouseDown:e=>{U(!0),null==b||b(e);const t=()=>{U(!1),document.removeEventListener("mouseup",t)};document.addEventListener("mouseup",t)}}))))}));var Ll=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};function Hl({suffixCls:t,tagName:n,displayName:o}){return o=>e.forwardRef(((r,a)=>e.createElement(o,Object.assign({ref:a,suffixCls:t,tagName:n},r))))}const Fl=e.forwardRef(((t,n)=>{const{prefixCls:o,suffixCls:r,className:a,tagName:i}=t,l=Ll(t,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:s}=e.useContext(A),c=s("layout",o),[u,d,p]=Ea(c),m=r?`${c}-${r}`:c;return u(e.createElement(i,Object.assign({className:C(o||m,a,d,p),ref:n},l)))})),Wl=e.forwardRef(((t,n)=>{const{direction:o}=e.useContext(A),[r,a]=e.useState([]),{prefixCls:i,className:l,rootClassName:s,children:c,hasSider:u,tagName:p,style:m}=t,g=Ll(t,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),h=ie(g,["suffixCls"]),{getPrefixCls:b,className:v,style:y}=X("layout"),w=b("layout",i),x=function(e,t,n){return"boolean"==typeof n?n:!!e.length||d(t).some((e=>e.type===ja))}(r,c,u),[$,E,S]=Ea(w),O=C(w,{[`${w}-has-sider`]:x,[`${w}-rtl`]:"rtl"===o},v,l,s,E,S),k=e.useMemo((()=>({siderHook:{addSider:e=>{a((t=>[].concat(f(t),[e])))},removeSider:e=>{a((t=>t.filter((t=>t!==e))))}}})),[]);return $(e.createElement(wa.Provider,{value:k},e.createElement(p,Object.assign({ref:n,className:O,style:Object.assign(Object.assign({},y),m)},h),c)))})),Vl=Hl({tagName:"div",displayName:"Layout"})(Wl),ql=Hl({suffixCls:"header",tagName:"header",displayName:"Header"})(Fl),Xl=Hl({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(Fl),Gl=Hl({suffixCls:"content",tagName:"main",displayName:"Content"})(Fl),Kl=Vl;Kl.Header=ql,Kl.Footer=Xl,Kl.Content=Gl,Kl.Sider=ja,Kl._InternalSiderContext=_a;const Yl=80*Math.PI,Ul=t=>{const{dotClassName:n,style:o,hasCircleCls:r}=t;return e.createElement("circle",{className:C(`${n}-circle`,{[`${n}-circle-bg`]:r}),r:40,cx:50,cy:50,strokeWidth:20,style:o})},Ql=({percent:t,prefixCls:n})=>{const o=`${n}-dot`,r=`${o}-holder`,a=`${r}-hidden`,[i,l]=e.useState(!1);k((()=>{0!==t&&l(!0)}),[0!==t]);const s=Math.max(Math.min(t,100),0);if(!i)return null;const c={strokeDashoffset:""+Yl/4,strokeDasharray:`${Yl*s/100} ${Yl*(100-s)/100}`};return e.createElement("span",{className:C(r,`${o}-progress`,s<=0&&a)},e.createElement("svg",{viewBox:"0 0 100 100",role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":s},e.createElement(Ul,{dotClassName:o,hasCircleCls:!0}),e.createElement(Ul,{dotClassName:o,style:c})))};function Zl(t){const{prefixCls:n,percent:o=0}=t,r=`${n}-dot`,a=`${r}-holder`,i=`${a}-hidden`;return e.createElement(e.Fragment,null,e.createElement("span",{className:C(a,o>0&&i)},e.createElement("span",{className:C(r,`${n}-dot-spin`)},[1,2,3,4].map((t=>e.createElement("i",{className:`${n}-dot-item`,key:t}))))),e.createElement(Ql,{prefixCls:n,percent:o}))}function Jl(t){const{prefixCls:n,indicator:o,percent:r}=t,a=`${n}-dot`;return o&&e.isValidElement(o)?J(o,{className:C(o.props.className,a),percent:r}):e.createElement(Zl,{prefixCls:n,percent:r})}const es=new v("antSpinMove",{to:{opacity:1}}),ts=new v("antRotate",{to:{transform:"rotate(405deg)"}}),ns=e=>{const{componentCls:t,calc:n}=e;return{[t]:Object.assign(Object.assign({},V(e)),{position:"absolute",display:"none",color:e.colorPrimary,fontSize:0,textAlign:"center",verticalAlign:"middle",opacity:0,transition:`transform ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`,"&-spinning":{position:"relative",display:"inline-block",opacity:1},[`${t}-text`]:{fontSize:e.fontSize,paddingTop:n(n(e.dotSize).sub(e.fontSize)).div(2).add(2).equal()},"&-fullscreen":{position:"fixed",width:"100vw",height:"100vh",backgroundColor:e.colorBgMask,zIndex:e.zIndexPopupBase,inset:0,display:"flex",alignItems:"center",flexDirection:"column",justifyContent:"center",opacity:0,visibility:"hidden",transition:`all ${e.motionDurationMid}`,"&-show":{opacity:1,visibility:"visible"},[t]:{[`${t}-dot-holder`]:{color:e.colorWhite},[`${t}-text`]:{color:e.colorTextLightSolid}}},"&-nested-loading":{position:"relative",[`> div > ${t}`]:{position:"absolute",top:0,insetInlineStart:0,zIndex:4,display:"block",width:"100%",height:"100%",maxHeight:e.contentHeight,[`${t}-dot`]:{position:"absolute",top:"50%",insetInlineStart:"50%",margin:n(e.dotSize).mul(-1).div(2).equal()},[`${t}-text`]:{position:"absolute",top:"50%",width:"100%",textShadow:`0 1px 2px ${e.colorBgContainer}`},[`&${t}-show-text ${t}-dot`]:{marginTop:n(e.dotSize).div(2).mul(-1).sub(10).equal()},"&-sm":{[`${t}-dot`]:{margin:n(e.dotSizeSM).mul(-1).div(2).equal()},[`${t}-text`]:{paddingTop:n(n(e.dotSizeSM).sub(e.fontSize)).div(2).add(2).equal()},[`&${t}-show-text ${t}-dot`]:{marginTop:n(e.dotSizeSM).div(2).mul(-1).sub(10).equal()}},"&-lg":{[`${t}-dot`]:{margin:n(e.dotSizeLG).mul(-1).div(2).equal()},[`${t}-text`]:{paddingTop:n(n(e.dotSizeLG).sub(e.fontSize)).div(2).add(2).equal()},[`&${t}-show-text ${t}-dot`]:{marginTop:n(e.dotSizeLG).div(2).mul(-1).sub(10).equal()}}},[`${t}-container`]:{position:"relative",transition:`opacity ${e.motionDurationSlow}`,"&::after":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,zIndex:10,width:"100%",height:"100%",background:e.colorBgContainer,opacity:0,transition:`all ${e.motionDurationSlow}`,content:'""',pointerEvents:"none"}},[`${t}-blur`]:{clear:"both",opacity:.5,userSelect:"none",pointerEvents:"none","&::after":{opacity:.4,pointerEvents:"auto"}}},"&-tip":{color:e.spinDotDefault},[`${t}-dot-holder`]:{width:"1em",height:"1em",fontSize:e.dotSize,display:"inline-block",transition:`transform ${e.motionDurationSlow} ease, opacity ${e.motionDurationSlow} ease`,transformOrigin:"50% 50%",lineHeight:1,color:e.colorPrimary,"&-hidden":{transform:"scale(0.3)",opacity:0}},[`${t}-dot-progress`]:{position:"absolute",inset:0},[`${t}-dot`]:{position:"relative",display:"inline-block",fontSize:e.dotSize,width:"1em",height:"1em","&-item":{position:"absolute",display:"block",width:n(e.dotSize).sub(n(e.marginXXS).div(2)).div(2).equal(),height:n(e.dotSize).sub(n(e.marginXXS).div(2)).div(2).equal(),background:"currentColor",borderRadius:"100%",transform:"scale(0.75)",transformOrigin:"50% 50%",opacity:.3,animationName:es,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear",animationDirection:"alternate","&:nth-child(1)":{top:0,insetInlineStart:0,animationDelay:"0s"},"&:nth-child(2)":{top:0,insetInlineEnd:0,animationDelay:"0.4s"},"&:nth-child(3)":{insetInlineEnd:0,bottom:0,animationDelay:"0.8s"},"&:nth-child(4)":{bottom:0,insetInlineStart:0,animationDelay:"1.2s"}},"&-spin":{transform:"rotate(45deg)",animationName:ts,animationDuration:"1.2s",animationIterationCount:"infinite",animationTimingFunction:"linear"},"&-circle":{strokeLinecap:"round",transition:["stroke-dashoffset","stroke-dasharray","stroke","stroke-width","opacity"].map((t=>`${t} ${e.motionDurationSlow} ease`)).join(","),fillOpacity:0,stroke:"currentcolor"},"&-circle-bg":{stroke:e.colorFillSecondary}},[`&-sm ${t}-dot`]:{"&, &-holder":{fontSize:e.dotSizeSM}},[`&-sm ${t}-dot-holder`]:{i:{width:n(n(e.dotSizeSM).sub(n(e.marginXXS).div(2))).div(2).equal(),height:n(n(e.dotSizeSM).sub(n(e.marginXXS).div(2))).div(2).equal()}},[`&-lg ${t}-dot`]:{"&, &-holder":{fontSize:e.dotSizeLG}},[`&-lg ${t}-dot-holder`]:{i:{width:n(n(e.dotSizeLG).sub(e.marginXXS)).div(2).equal(),height:n(n(e.dotSizeLG).sub(e.marginXXS)).div(2).equal()}},[`&${t}-show-text ${t}-text`]:{display:"block"}})}},os=H("Spin",(e=>{const t=F(e,{spinDotDefault:e.colorTextDescription});return[ns(t)]}),(e=>{const{controlHeightLG:t,controlHeight:n}=e;return{contentHeight:400,dotSize:t/2,dotSizeSM:.35*t,dotSizeLG:n}})),rs=[[30,.05],[70,.03],[96,.01]];let as;const is=t=>{var n;const{prefixCls:o,spinning:r=!0,delay:a=0,className:i,rootClassName:l,size:s="default",tip:c,wrapperClassName:u,style:d,children:p,fullscreen:m=!1,indicator:f,percent:g}=t,h=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["prefixCls","spinning","delay","className","rootClassName","size","tip","wrapperClassName","style","children","fullscreen","indicator","percent"]),{getPrefixCls:b,direction:v,className:y,style:w,indicator:x}=X("spin"),$=b("spin",o),[E,S,O]=os($),[k,_]=e.useState((()=>r&&!function(e,t){return!!e&&!!t&&!Number.isNaN(Number(t))}(r,a))),I=function(t,n){const[o,r]=e.useState(0),a=e.useRef(null),i="auto"===n;return e.useEffect((()=>(i&&t&&(r(0),a.current=setInterval((()=>{r((e=>{const t=100-e;for(let n=0;n<rs.length;n+=1){const[o,r]=rs[n];if(e<=o)return e+t*r}return e}))}),200)),()=>{clearInterval(a.current)})),[i,t]),i?o:n}(k,g);e.useEffect((()=>{if(r){const e=Ri(a,(()=>{_(!0)}));return e(),()=>{var t;null===(t=null==e?void 0:e.cancel)||void 0===t||t.call(e)}}_(!1)}),[a,r]);const j=e.useMemo((()=>void 0!==p&&!m),[p,m]),R=C($,y,{[`${$}-sm`]:"small"===s,[`${$}-lg`]:"large"===s,[`${$}-spinning`]:k,[`${$}-show-text`]:!!c,[`${$}-rtl`]:"rtl"===v},i,!m&&l,S,O),B=C(`${$}-container`,{[`${$}-blur`]:k}),N=null!==(n=null!=f?f:x)&&void 0!==n?n:as,T=Object.assign(Object.assign({},w),d),P=e.createElement("div",Object.assign({},h,{style:T,className:R,"aria-live":"polite","aria-busy":k}),e.createElement(Jl,{prefixCls:$,indicator:N,percent:I}),c&&(j||m)?e.createElement("div",{className:`${$}-text`},c):null);return E(j?e.createElement("div",Object.assign({},h,{className:C(`${$}-nested-loading`,u,S,O)}),k&&e.createElement("div",{key:"loading"},P),e.createElement("div",{className:B,key:"container"},p)):m?e.createElement("div",{className:C(`${$}-fullscreen`,{[`${$}-fullscreen-show`]:k},l,S,O)},P):P)};is.setDefaultIndicator=e=>{as=e};let ls=null,ss=e=>e(),cs=[],us={};function ds(){const{getContainer:e,rtl:t,maxCount:n,top:o,bottom:r,showProgress:a,pauseOnHover:i}=us,l=(null==e?void 0:e())||document.body;return{getContainer:()=>l,rtl:t,maxCount:n,top:o,bottom:r,showProgress:a,pauseOnHover:i}}const ps=O.forwardRef(((t,n)=>{const{notificationConfig:o,sync:r}=t,{getPrefixCls:a}=e.useContext(A),i=us.prefixCls||a("notification"),l=e.useContext(ze),[s,c]=De(Object.assign(Object.assign(Object.assign({},o),{prefixCls:i}),l.notification));return O.useEffect(r,[]),O.useImperativeHandle(n,(()=>{const e=Object.assign({},s);return Object.keys(e).forEach((t=>{e[t]=(...e)=>(r(),s[t].apply(s,e))})),{instance:e,sync:r}})),c})),ms=O.forwardRef(((e,t)=>{const[n,o]=O.useState(ds),r=()=>{o(ds)};O.useEffect(r,[]);const a=Me(),i=a.getRootPrefixCls(),l=a.getIconPrefixCls(),s=a.getTheme(),c=O.createElement(ps,{ref:t,sync:r,notificationConfig:n});return O.createElement(Ae,{prefixCls:i,iconPrefixCls:l,theme:s},a.holderRender?a.holderRender(c):c)}));function fs(){if(!ls){const e=document.createDocumentFragment(),t={fragment:e};return ls=t,void ss((()=>{Pe()(O.createElement(ms,{ref:e=>{const{instance:n,sync:o}=e||{};Promise.resolve().then((()=>{!t.instance&&n&&(t.instance=n,t.sync=o,fs())}))}}),e)}))}ls.instance&&(cs.forEach((e=>{switch(e.type){case"open":ss((()=>{ls.instance.open(Object.assign(Object.assign({},us),e.config))}));break;case"destroy":ss((()=>{null==ls||ls.instance.destroy(e.key)}))}})),cs=[])}function gs(e){cs.push({type:"open",config:e}),fs()}const hs={open:gs,destroy:e=>{cs.push({type:"destroy",key:e}),fs()},config:function(e){us=Object.assign(Object.assign({},us),e),ss((()=>{var e;null===(e=null==ls?void 0:ls.sync)||void 0===e||e.call(ls)}))},useNotification:Te,_InternalPanelDoNotUseOrYouWillBeFired:Ne};["success","info","warning","error"].forEach((e=>{hs[e]=t=>gs(Object.assign(Object.assign({},t),{type:e}))}));var bs={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"},vs=function(t,n){return e.createElement(D,p({},t,{ref:n,icon:bs}))},ys=e.forwardRef(vs),ws={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"},xs=function(t,n){return e.createElement(D,p({},t,{ref:n,icon:ws}))},Cs=e.forwardRef(xs),$s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"},Es=function(t,n){return e.createElement(D,p({},t,{ref:n,icon:$s}))},Ss=e.forwardRef(Es);const Os=e=>{const{lineWidth:t,fontSizeIcon:n,calc:o}=e,r=e.fontSizeSM;return F(e,{tagFontSize:r,tagLineHeight:L(o(e.lineHeightSM).mul(r).equal()),tagIconSize:o(n).sub(o(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},ks=e=>({defaultBg:new me(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),_s=H("Tag",(e=>(e=>{const{paddingXXS:t,lineWidth:n,tagPaddingHorizontal:o,componentCls:r,calc:a}=e,i=a(o).sub(n).equal(),l=a(t).sub(n).equal();return{[r]:Object.assign(Object.assign({},V(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:i,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${L(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${r}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${r}-close-icon`]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${r}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${r}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:i}}),[`${r}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}})(Os(e))),ks);const Is=e.forwardRef(((t,n)=>{const{prefixCls:o,style:r,className:a,checked:i,onChange:l,onClick:s}=t,c=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:u,tag:d}=e.useContext(A),p=u("tag",o),[m,f,g]=_s(p),h=C(p,`${p}-checkable`,{[`${p}-checkable-checked`]:i},null==d?void 0:d.className,a,f,g);return m(e.createElement("span",Object.assign({},c,{ref:n,style:Object.assign(Object.assign({},r),null==d?void 0:d.style),className:h,onClick:e=>{null==l||l(!i),null==s||s(e)}})))})),js=Le(["Tag","preset"],(e=>(e=>Jt(e,((t,{textColor:n,lightBorderColor:o,lightColor:r,darkColor:a})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:n,background:r,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:a,borderColor:a},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}))))(Os(e))),ks);const Rs=(e,t,n)=>{const o="string"!=typeof(r=n)?r:r.charAt(0).toUpperCase()+r.slice(1);var r;return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${n}`],background:e[`color${o}Bg`],borderColor:e[`color${o}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},Bs=Le(["Tag","status"],(e=>{const t=Os(e);return[Rs(t,"success","Success"),Rs(t,"processing","Info"),Rs(t,"error","Error"),Rs(t,"warning","Warning")]}),ks);const Ns=e.forwardRef(((t,n)=>{const{prefixCls:o,className:r,rootClassName:a,style:i,children:l,icon:s,color:c,onClose:u,bordered:d=!0,visible:p}=t,m=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:f,direction:g,tag:h}=e.useContext(A),[b,v]=e.useState(!0),y=ie(m,["closeIcon","closable"]);e.useEffect((()=>{void 0!==p&&v(p)}),[p]);const w=jo(c),x=function(e){return Io.includes(e)}(c),$=w||x,E=Object.assign(Object.assign({backgroundColor:c&&!$?c:void 0},null==h?void 0:h.style),i),S=f("tag",o),[O,k,_]=_s(S),I=C(S,null==h?void 0:h.className,{[`${S}-${c}`]:$,[`${S}-has-color`]:c&&!$,[`${S}-hidden`]:!b,[`${S}-rtl`]:"rtl"===g,[`${S}-borderless`]:!d},r,a,k,_),j=e=>{e.stopPropagation(),null==u||u(e),e.defaultPrevented||v(!1)},[,R]=we(xe(t),xe(h),{closable:!1,closeIconRender:t=>He(t,e.createElement("span",{className:`${S}-close-icon`,onClick:j},t),(e=>({onClick:t=>{var n;null===(n=null==e?void 0:e.onClick)||void 0===n||n.call(e,t),j(t)},className:C(null==e?void 0:e.className,`${S}-close-icon`)})))}),B="function"==typeof m.onClick||l&&"a"===l.type,N=s||null,T=N?e.createElement(e.Fragment,null,N,l&&e.createElement("span",null,l)):l,P=e.createElement("span",Object.assign({},y,{ref:n,className:I,style:E}),T,R,w&&e.createElement(js,{key:"preset",prefixCls:S}),x&&e.createElement(Bs,{key:"status",prefixCls:S}));return O(B?e.createElement(Fe,{component:"Tag"},P):P)}));Ns.CheckableTag=Is;const Ts=e=>{const t={};return[1,2,3,4,5].forEach((n=>{t[`\n      h${n}&,\n      div&-h${n},\n      div&-h${n} > textarea,\n      h${n}\n    `]=((e,t,n,o)=>{const{titleMarginBottom:r,fontWeightStrong:a}=o;return{marginBottom:r,color:n,fontWeight:a,fontSize:e,lineHeight:t}})(e[`fontSizeHeading${n}`],e[`lineHeightHeading${n}`],e.colorTextHeading,e)})),t},Ps=e=>{const{componentCls:t}=e;return{"a&, a":Object.assign(Object.assign({},Ve(e)),{userSelect:"text",[`&[disabled], &${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},As=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:We[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:600},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),Ms=e=>{const{componentCls:t,paddingSM:n}=e,o=n;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(o).mul(-1).equal(),marginBottom:`calc(1em - ${L(o)})`},[`${t}-edit-content-confirm`]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorIcon,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},zs=e=>({[`${e.componentCls}-copy-success`]:{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}},[`${e.componentCls}-copy-icon-only`]:{marginInlineStart:0}}),Ds=e=>{const{componentCls:t,titleMarginTop:n}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,[`&${t}-secondary`]:{color:e.colorTextDescription},[`&${t}-success`]:{color:e.colorSuccessText},[`&${t}-warning`]:{color:e.colorWarningText},[`&${t}-danger`]:{color:e.colorErrorText,"a&:active, a&:focus":{color:e.colorErrorTextActive},"a&:hover":{color:e.colorErrorTextHover}},[`&${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},Ts(e)),{[`\n      & + h1${t},\n      & + h2${t},\n      & + h3${t},\n      & + h4${t},\n      & + h5${t}\n      `]:{marginTop:n},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:n}}}),As(e)),Ps(e)),{[`\n        ${t}-expand,\n        ${t}-collapse,\n        ${t}-edit,\n        ${t}-copy\n      `]:Object.assign(Object.assign({},Ve(e)),{marginInlineStart:e.marginXXS})}),Ms(e)),zs(e)),{"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),{"&-rtl":{direction:"rtl"}})}},Ls=H("Typography",(e=>[Ds(e)]),(()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"}))),Hs=t=>{const{prefixCls:n,"aria-label":o,className:r,style:a,direction:i,maxLength:l,autoSize:s=!0,value:c,onSave:u,onCancel:d,onEnd:p,component:m,enterIcon:f=e.createElement(Ss,null)}=t,g=e.useRef(null),h=e.useRef(!1),b=e.useRef(null),[v,y]=e.useState(c);e.useEffect((()=>{y(c)}),[c]),e.useEffect((()=>{var e;if(null===(e=g.current)||void 0===e?void 0:e.resizableTextArea){const{textArea:e}=g.current.resizableTextArea;e.focus();const{length:t}=e.value;e.setSelectionRange(t,t)}}),[]);const w=()=>{u(v.trim())},[x,$,E]=Ls(n),S=C(n,`${n}-edit-content`,{[`${n}-rtl`]:"rtl"===i,[`${n}-${m}`]:!!m},r,$,E);return x(e.createElement("div",{className:S,style:a},e.createElement(Dl,{ref:g,maxLength:l,value:v,onChange:({target:e})=>{y(e.value.replace(/[\n\r]/g,""))},onKeyDown:({keyCode:e})=>{h.current||(b.current=e)},onKeyUp:({keyCode:e,ctrlKey:t,altKey:n,metaKey:o,shiftKey:r})=>{b.current!==e||h.current||t||n||o||r||(e===oe.ENTER?(w(),null==p||p()):e===oe.ESC&&d())},onCompositionStart:()=>{h.current=!0},onCompositionEnd:()=>{h.current=!1},onBlur:()=>{w()},"aria-label":o,rows:1,autoSize:s}),null!==f?J(f,{className:`${n}-edit-content-confirm`}):null))};var Fs,Ws,Vs,qs;const Xs=qe(function(){if(qs)return Vs;qs=1;var e=Ws?Fs:(Ws=1,Fs=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],o=0;o<e.rangeCount;o++)n.push(e.getRangeAt(o));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach((function(t){e.addRange(t)})),t&&t.focus()}}),t={"text/plain":"Text","text/html":"Url",default:"Text"};return Vs=function(n,o){var r,a,i,l,s,c,u=!1;o||(o={}),r=o.debug||!1;try{if(i=e(),l=document.createRange(),s=document.getSelection(),(c=document.createElement("span")).textContent=n,c.ariaHidden="true",c.style.all="unset",c.style.position="fixed",c.style.top=0,c.style.clip="rect(0, 0, 0, 0)",c.style.whiteSpace="pre",c.style.webkitUserSelect="text",c.style.MozUserSelect="text",c.style.msUserSelect="text",c.style.userSelect="text",c.addEventListener("copy",(function(e){if(e.stopPropagation(),o.format)if(e.preventDefault(),void 0===e.clipboardData){r&&console.warn("unable to use e.clipboardData"),r&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var a=t[o.format]||t.default;window.clipboardData.setData(a,n)}else e.clipboardData.clearData(),e.clipboardData.setData(o.format,n);o.onCopy&&(e.preventDefault(),o.onCopy(e.clipboardData))})),document.body.appendChild(c),l.selectNodeContents(c),s.addRange(l),!document.execCommand("copy"))throw new Error("copy command was unsuccessful");u=!0}catch(d){r&&console.error("unable to copy using execCommand: ",d),r&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(o.format||"text",n),o.onCopy&&o.onCopy(window.clipboardData),u=!0}catch(p){r&&console.error("unable to copy using clipboardData: ",p),r&&console.error("falling back to prompt"),a=function(e){var t=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C";return e.replace(/#{\s*key\s*}/g,t)}("message"in o?o.message:"Copy to clipboard: #{key}, Enter"),window.prompt(a,n)}}finally{s&&("function"==typeof s.removeRange?s.removeRange(l):s.removeAllRanges()),c&&document.body.removeChild(c),i()}return u}}());var Gs=function(e,t,n,o){return new(n||(n=Promise))((function(r,a){function i(e){try{s(o.next(e))}catch(t){a(t)}}function l(e){try{s(o.throw(e))}catch(t){a(t)}}function s(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(i,l)}s((o=o.apply(e,t||[])).next())}))};const Ks=({copyConfig:t,children:n})=>{const[o,r]=e.useState(!1),[a,i]=e.useState(!1),l=e.useRef(null),s=()=>{l.current&&clearTimeout(l.current)},c={};t.format&&(c.format=t.format),e.useEffect((()=>s),[]);return{copied:o,copyLoading:a,onClick:E((e=>Gs(void 0,void 0,void 0,(function*(){var o;null==e||e.preventDefault(),null==e||e.stopPropagation(),i(!0);try{const a="function"==typeof t.text?yield t.text():t.text;Xs(a||((e,t=!1)=>t&&null==e?[]:Array.isArray(e)?e:[e])(n,!0).join("")||"",c),i(!1),r(!0),s(),l.current=setTimeout((()=>{r(!1)}),3e3),null===(o=t.onCopy)||void 0===o||o.call(t,e)}catch(a){throw i(!1),a}}))))}};function Ys(t,n){return e.useMemo((()=>{const e=!!t;return[e,Object.assign(Object.assign({},n),e&&"object"==typeof t?t:null)]}),[t])}const Us=e.forwardRef(((t,n)=>{const{prefixCls:o,component:r="article",className:a,rootClassName:i,setContentRef:l,children:s,direction:c,style:u}=t,d=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:p,direction:m,className:f,style:g}=X("typography"),h=null!=c?c:m,b=l?I(n,l):n,v=p("typography",o),[y,w,x]=Ls(v),$=C(v,f,{[`${v}-rtl`]:"rtl"===h},a,i,w,x),E=Object.assign(Object.assign({},g),u);return y(e.createElement(r,Object.assign({className:$,style:E,ref:b},d),s))}));function Qs(e){return!1===e?[!1,!1]:Array.isArray(e)?e:[e]}function Zs(e,t,n){return!0===e||void 0===e?t:e||n&&t}const Js=e=>["string","number"].includes(typeof e),ec=({prefixCls:t,copied:n,locale:o,iconOnly:r,tooltips:a,icon:i,tabIndex:l,onCopy:s,loading:c})=>{const u=Qs(a),d=Qs(i),{copied:p,copy:m}=null!=o?o:{},f=n?p:m,g=Zs(u[n?1:0],f),h="string"==typeof g?g:f;return e.createElement(Bo,{title:g},e.createElement("button",{type:"button",className:C(`${t}-copy`,{[`${t}-copy-success`]:n,[`${t}-copy-icon-only`]:r}),onClick:s,"aria-label":h,tabIndex:l},n?Zs(d[1],e.createElement(ro,null),!0):Zs(d[0],c?e.createElement(Xe,null):e.createElement(ys,null),!0)))},tc=e.forwardRef((({style:t,children:n},o)=>{const r=e.useRef(null);return e.useImperativeHandle(o,(()=>({isExceed:()=>{const e=r.current;return e.scrollHeight>e.clientHeight},getHeight:()=>r.current.clientHeight}))),e.createElement("span",{"aria-hidden":!0,ref:r,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},t)},n)}));function nc(e,t){let n=0;const o=[];for(let r=0;r<e.length;r+=1){if(n===t)return o;const a=e[r],i=n+(Js(a)?String(a).length:1);if(i>t){const e=t-n;return o.push(String(a).slice(0,e)),o}o.push(a),n=i}return e}const oc=0,rc=4,ac={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function ic(t){const{enableMeasure:n,width:o,text:r,children:a,rows:i,expanded:l,miscDeps:s,onEllipsis:c}=t,u=e.useMemo((()=>d(r)),[r]),p=e.useMemo((()=>(e=>e.reduce(((e,t)=>e+(Js(t)?String(t).length:1)),0))(u)),[r]),m=e.useMemo((()=>a(u,!1)),[r]),[g,h]=e.useState(null),b=e.useRef(null),v=e.useRef(null),y=e.useRef(null),w=e.useRef(null),x=e.useRef(null),[C,$]=e.useState(!1),[E,S]=e.useState(oc),[O,_]=e.useState(0),[I,j]=e.useState(null);k((()=>{S(n&&o&&p?1:oc)}),[o,r,i,n,u]),k((()=>{var e,t,n,o;if(1===E){S(2);const e=v.current&&getComputedStyle(v.current).whiteSpace;j(e)}else if(2===E){const r=!!(null===(e=y.current)||void 0===e?void 0:e.isExceed());S(r?3:rc),h(r?[0,p]:null),$(r);const a=(null===(t=y.current)||void 0===t?void 0:t.getHeight())||0,l=1===i?0:(null===(n=w.current)||void 0===n?void 0:n.getHeight())||0,s=(null===(o=x.current)||void 0===o?void 0:o.getHeight())||0,u=Math.max(a,l+s);_(u+1),c(r)}}),[E]);const R=g?Math.ceil((g[0]+g[1])/2):0;k((()=>{var e;const[t,n]=g||[0,0];if(t!==n){const o=((null===(e=b.current)||void 0===e?void 0:e.getHeight())||0)>O;let r=R;n-t===1&&(r=o?t:n),h(o?[t,r]:[r,n])}}),[g,R]);const B=e.useMemo((()=>{if(!n)return a(u,!1);if(3!==E||!g||g[0]!==g[1]){const t=a(u,!1);return[rc,oc].includes(E)?t:e.createElement("span",{style:Object.assign(Object.assign({},ac),{WebkitLineClamp:i})},t)}return a(l?u:nc(u,g[0]),C)}),[l,E,g,u].concat(f(s))),N={width:o,margin:0,padding:0,whiteSpace:"nowrap"===I?"normal":"inherit"};return e.createElement(e.Fragment,null,B,2===E&&e.createElement(e.Fragment,null,e.createElement(tc,{style:Object.assign(Object.assign(Object.assign({},N),ac),{WebkitLineClamp:i}),ref:y},m),e.createElement(tc,{style:Object.assign(Object.assign(Object.assign({},N),ac),{WebkitLineClamp:i-1}),ref:w},m),e.createElement(tc,{style:Object.assign(Object.assign(Object.assign({},N),ac),{WebkitLineClamp:1}),ref:x},a([],!0))),3===E&&g&&g[0]!==g[1]&&e.createElement(tc,{style:Object.assign(Object.assign({},N),{top:400}),ref:b},a(nc(u,R),!0)),1===E&&e.createElement("span",{style:{whiteSpace:"inherit"},ref:v}))}const lc=({enableEllipsis:t,isEllipsis:n,children:o,tooltipProps:r})=>(null==r?void 0:r.title)&&t?e.createElement(Bo,Object.assign({open:!!n&&void 0},r),o):o;const sc=["delete","mark","code","underline","strong","keyboard","italic"],cc=e.forwardRef(((t,n)=>{var o;const{prefixCls:r,className:a,style:i,type:l,disabled:s,children:c,ellipsis:u,editable:p,copyable:m,component:g,title:h}=t,b=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:v,direction:y}=e.useContext(A),[w]=Ge("Text"),x=e.useRef(null),$=e.useRef(null),E=v("typography",r),S=ie(b,sc),[O,_]=Ys(p),[j,R]=K(!1,{value:_.editing}),{triggerType:B=["icon"]}=_,N=e=>{var t;e&&(null===(t=_.onStart)||void 0===t||t.call(_)),R(e)},T=(t=>{const n=e.useRef(void 0);return e.useEffect((()=>{n.current=t})),n.current})(j);k((()=>{var e;!j&&T&&(null===(e=$.current)||void 0===e||e.focus())}),[j]);const P=e=>{null==e||e.preventDefault(),N(!0)},M=e=>{var t;null===(t=_.onChange)||void 0===t||t.call(_,e),N(!1)},z=()=>{var e;null===(e=_.onCancel)||void 0===e||e.call(_),N(!1)},[D,L]=Ys(m),{copied:H,copyLoading:F,onClick:W}=Ks({copyConfig:L,children:c}),[V,q]=e.useState(!1),[X,G]=e.useState(!1),[Y,U]=e.useState(!1),[Q,Z]=e.useState(!1),[J,ee]=e.useState(!0),[te,ne]=Ys(u,{expandable:!1,symbol:e=>e?null==w?void 0:w.collapse:null==w?void 0:w.expand}),[oe,re]=K(ne.defaultExpanded||!1,{value:ne.expanded}),ae=te&&(!oe||"collapsible"===ne.expandable),{rows:le=1}=ne,se=e.useMemo((()=>ae&&(void 0!==ne.suffix||ne.onEllipsis||ne.expandable||O||D)),[ae,ne,O,D]);k((()=>{te&&!se&&(q(pn("webkitLineClamp")),G(pn("textOverflow")))}),[se,te]);const[ce,ue]=e.useState(ae),de=e.useMemo((()=>!se&&(1===le?X:V)),[se,X,V]);k((()=>{ue(de&&ae)}),[de,ae]);const pe=ae&&(ce?Q:Y),me=ae&&1===le&&ce,fe=ae&&le>1&&ce,[ge,he]=e.useState(0),be=e=>{var t;U(e),Y!==e&&(null===(t=ne.onEllipsis)||void 0===t||t.call(ne,e))};e.useEffect((()=>{const e=x.current;if(te&&ce&&e){const t=function(e){const t=document.createElement("em");e.appendChild(t);const n=e.getBoundingClientRect(),o=t.getBoundingClientRect();return e.removeChild(t),n.left>o.left||o.right>n.right||n.top>o.top||o.bottom>n.bottom}(e);Q!==t&&Z(t)}}),[te,ce,c,fe,J,ge]),e.useEffect((()=>{const e=x.current;if("undefined"==typeof IntersectionObserver||!e||!ce||!ae)return;const t=new IntersectionObserver((()=>{ee(!!e.offsetParent)}));return t.observe(e),()=>{t.disconnect()}}),[ce,ae]);const ve=((t,n,o)=>e.useMemo((()=>!0===t?{title:null!=n?n:o}:e.isValidElement(t)?{title:t}:"object"==typeof t?Object.assign({title:null!=n?n:o},t):{title:t}),[t,n,o]))(ne.tooltip,_.text,c),ye=e.useMemo((()=>{if(te&&!ce)return[_.text,c,h,ve.title].find(Js)}),[te,ce,h,ve.title,pe]);if(j)return e.createElement(Hs,{value:null!==(o=_.text)&&void 0!==o?o:"string"==typeof c?c:"",onSave:M,onCancel:z,onEnd:_.onEnd,prefixCls:E,className:a,style:i,direction:y,component:g,maxLength:_.maxLength,autoSize:_.autoSize,enterIcon:_.enterIcon});const we=()=>{const{expandable:t,symbol:n}=ne;return t?e.createElement("button",{type:"button",key:"expand",className:`${E}-${oe?"collapse":"expand"}`,onClick:e=>((e,t)=>{var n;re(t.expanded),null===(n=ne.onExpand)||void 0===n||n.call(ne,e,t)})(e,{expanded:!oe}),"aria-label":oe?w.collapse:null==w?void 0:w.expand},"function"==typeof n?n(oe):n):null},xe=()=>{if(!O)return;const{icon:t,tooltip:n,tabIndex:o}=_,r=d(n)[0]||(null==w?void 0:w.edit),a="string"==typeof r?r:"";return B.includes("icon")?e.createElement(Bo,{key:"edit",title:!1===n?"":r},e.createElement("button",{type:"button",ref:$,className:`${E}-edit`,onClick:P,"aria-label":a,tabIndex:o},t||e.createElement(Cs,{role:"button"}))):null},Ce=t=>[t&&we(),xe(),D?e.createElement(ec,Object.assign({key:"copy"},L,{prefixCls:E,copied:H,locale:w,onCopy:W,loading:F,iconOnly:null==c})):null];return e.createElement(Qt,{onResize:({offsetWidth:e})=>{he(e)},disabled:!ae},(o=>e.createElement(lc,{tooltipProps:ve,enableEllipsis:ae,isEllipsis:pe},e.createElement(Us,Object.assign({className:C({[`${E}-${l}`]:l,[`${E}-disabled`]:s,[`${E}-ellipsis`]:te,[`${E}-ellipsis-single-line`]:me,[`${E}-ellipsis-multiple-line`]:fe},a),prefixCls:r,style:Object.assign(Object.assign({},i),{WebkitLineClamp:fe?le:void 0}),component:g,ref:I(o,x,n),direction:y,onClick:B.includes("text")?P:void 0,"aria-label":null==ye?void 0:ye.toString(),title:h},S),e.createElement(ic,{enableMeasure:ae&&!ce,text:c,rows:le,width:ge,onEllipsis:be,expanded:oe,miscDeps:[H,oe,F,O,D,w].concat(f(sc.map((e=>t[e]))))},((n,o)=>function({mark:t,code:n,underline:o,delete:r,strong:a,keyboard:i,italic:l},s){let c=s;function u(t,n){n&&(c=e.createElement(t,{},c))}return u("strong",a),u("u",o),u("del",r),u("code",n),u("mark",t),u("kbd",i),u("i",l),c}(t,e.createElement(e.Fragment,null,n.length>0&&o&&!oe&&ye?e.createElement("span",{key:"show-content","aria-hidden":!0},n):n,(t=>[t&&!oe&&e.createElement("span",{"aria-hidden":!0,key:"ellipsis"},"..."),ne.suffix,Ce(t)])(o)))))))))}));const uc=e.forwardRef(((t,n)=>{var{ellipsis:o,rel:r}=t,a=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["ellipsis","rel"]);const i=Object.assign(Object.assign({},a),{rel:void 0===r&&"_blank"===a.target?"noopener noreferrer":r});return delete i.navigate,e.createElement(cc,Object.assign({},i,{ref:n,ellipsis:!!o,component:"a"}))})),dc=e.forwardRef(((t,n)=>e.createElement(cc,Object.assign({ref:n},t,{component:"div"}))));const pc=(t,n)=>{var{ellipsis:o}=t,r=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["ellipsis"]);const a=e.useMemo((()=>o&&"object"==typeof o?ie(o,["expandable","rows"]):o),[o]);return e.createElement(cc,Object.assign({ref:n},r,{ellipsis:a,component:"span"}))},mc=e.forwardRef(pc);const fc=[1,2,3,4,5],gc=e.forwardRef(((t,n)=>{const{level:o=1}=t,r=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}(t,["level"]),a=fc.includes(o)?`h${o}`:"h1";return e.createElement(cc,Object.assign({ref:n},r,{component:a}))})),hc=Us;function bc(e="",t={},n=!1){var o,r,a;try{return(null==(o=null==e?void 0:e.startsWith)?void 0:o.call(e,"for (;;);"))&&(e=e.replace("for (;;);","")),n&&(null==(r=null==e?void 0:e.includes)?void 0:r.call(e,"\n"))&&!(null==(a=null==e?void 0:e.startsWith)?void 0:a.call(e,"["))&&(e="["+e.split("\n").join(",")+"]"),JSON.parse(e)}catch(i){return console.log("Cannot parse JSON",i,e),t}}hc.Text=mc,hc.Link=uc,hc.Title=gc,hc.Paragraph=dc;const vc=Object.freeze(Object.defineProperty({__proto__:null,parseSafe:bc},Symbol.toStringTag,{value:"Module"}));var yc=(e=>(e.FBAndroid="EAAA (FBAndroid)",e.EAAB="EAAB (Ads campaigns)",e.EAAI="EAAI (Ads manager)",e.EAAM="EAAM (Measurement hub)",e.EAAG="EAAG (Business manager)",e.EAADo1="EAADo1 (Business dialog)",e))(yc||{});const wc={uid:null,fb_dtsg:null,urlToId:{},access_token:{},extVersion:void 0};function xc(e,t){wc[e]=t}async function Cc(e={},t="",n=!0,o={},r={}){let a="";const i=await kc();if(!i)return hs.error({message:"Missing fb_dtsg token",description:"Please reload the page and try again",duration:0}),null;a="string"==typeof e?"&q="+encodeURIComponent(e)+"&fb_dtsg="+i:Ec({...n?{dpr:1,__a:1,__aaid:0,__ccg:"GOOD",__comet_req:15,server_timestamps:!0,fb_dtsg:i}:{},...o,...e});const l=await Ke(t||"https://www.facebook.com/api/graphql/",{body:a,method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded",...r},credentials:"include"});return $c(l),l}async function $c(e){var t;try{const n=bc(e,{},!0),o=(null==(t=null==n?void 0:n.errors)?void 0:t.find((e=>"CRITICAL"===e.severity)))||(null==n?void 0:n.error);if(o){const{summary:e=(null==n?void 0:n.errorSummary)||"",message:t="",description_raw:r,debug_info:a="",description:i=(null==n?void 0:n.errorDescription)||""}=o;if(e||t){let o;if(console.log(n),null==r?void 0:r.__html){const e=document.createElement("div");e.innerHTML=(null==r?void 0:r.__html)||r,o=e.innerText,e.remove()}const{trans:l}=await Ye((async()=>{const{trans:e}=await Promise.resolve().then((()=>Lc));return{trans:e}}),void 0,import.meta.url);hs.error({message:"Facebook response Error",description:[e,a,t,o||i||""].filter(Boolean).join(". ")+"  -  "+l({vi:"(Bấm để xem lỗi thường gặp trong FB AIO)",en:"(Click to see common errors in FB AIO)"}),onClick:()=>{window.open("/#/FAQ","_self")},duration:0})}}}catch(n){}}function Ec(e={}){const t=[];for(const n in e){const o=encodeURIComponent(n),r="string"==typeof e[n]?e[n]:JSON.stringify(e[n]),a=encodeURIComponent(r);t.push(o+"="+a)}return t.join("&")}async function Sc(){try{if(!wc.extVersion){const e=await Ue("chrome.runtime.getManifest"),t=null==e?void 0:e.version;t&&(wc.extVersion=t)}return wc.extVersion||"unknown"}catch(e){return console.log(e),"unknown"}}async function Oc(e){const t=await Ic();if("100006164142110"===t||"100004848287494"===t)return void console.log("track event",t,e);return await Ke(Qe.FB_AIO.server+"/count",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({script:"fb_aio_"+e,version:await Sc(),uid:t})})}async function kc(){if(wc.fb_dtsg)return wc.fb_dtsg;for(let t of[async()=>(await Ke("https://www.facebook.com/policies_center/")).match(/DTSGInitData",\[\],\{"token":"(.*?)"/)[1],async function e(){var t,n;let o=await Ke("https://www.facebook.com/help/contact/1417759018475333?helpref=faq_content&refid=69",{credentials:"include"});if(!o.includes("fast_switch_site")){return null==(t=o.match(/name="fb_dtsg" value="(.*?)"/))?void 0:t[1]}{const t=null==(n=/"(https:\/\/m\.facebook\.com\/a\/preferences\.php\?fast_switch_site[a-zA-Z0-9&=\-_%.;]+)"/.exec(o))?void 0:n[1];if(t)return o=await Ke(t,{credentials:"include"}),await e()}},async()=>{var e;let t=await Ke("https://mbasic.facebook.com/photos/upload/");return null==(e=RegExp(/name="fb_dtsg" value="(.*?)"/).exec(t))?void 0:e[1]},async()=>{var e,t;const n=await Ke("https://m.facebook.com/home.php",{headers:{Accept:"text/html"}});return(null==(e=RegExp(/"dtsg":{"token":"([^"]+)"/).exec(n))?void 0:e[1])||(null==(t=RegExp(/"name":"fb_dtsg","value":"([^"]+)/).exec(n))?void 0:t[1])}])try{const e=await t();if(e)return wc.fb_dtsg=e,wc.fb_dtsg}catch(e){console.error(e)}return null}async function _c(e,t=!1,n=!1){var o,r,a,i,l,s;if(wc.access_token[e]&&!n)return wc.access_token[e];if("EAAA (FBAndroid)"===e){const n=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(e=>("x"===e?16*Math.random()|0:4*Math.random()|8).toString(16))),a=await Ic(),i=await Cc({doc_id:"6494107973937368",variables:{input:{client_mutation_id:"4",actor_id:a,config_enum:"GDP_CONFIRM",device_id:null,experience_id:n(),extra_params_json:JSON.stringify({app_id:"350685531728",kid_directed_site:"false",logger_id:`"${n()}"`,next:'"confirm"',redirect_uri:'"https://www.facebook.com/connect/login_success.html"',response_type:'"token"',return_scopes:"false",scope:'["user_subscriptions","user_videos","user_website","user_work_history","friends_about_me","friends_actions.books","friends_actions.music","friends_actions.news","friends_actions.video","friends_activities","friends_birthday","friends_education_history","friends_events","friends_games_activity","friends_groups","friends_hometown","friends_interests","friends_likes","friends_location","friends_notes","friends_photos","friends_questions","friends_relationship_details","friends_relationships","friends_religion_politics","friends_status","friends_subscriptions","friends_videos","friends_website","friends_work_history","ads_management","create_event","create_note","export_stream","friends_online_presence","manage_friendlists","manage_notifications","manage_pages","photo_upload","publish_stream","read_friendlists","read_insights","read_mailbox","read_page_mailboxes","read_requests","read_stream","rsvp_event","share_item","sms","status_update","user_online_presence","video_upload","xmpp_login"]',steps:"{}",tp:'"unspecified"',cui_gk:'"[PASS]:""',is_limited_login_shim:"false"}),flow_name:"GDP",flow_step_type:"STANDALONE",outcome:"APPROVED",source:"gdp_delegated",surface:"FACEBOOK_COMET"}}});try{const n=bc(i);console.log(n);const a=null==(r=null==(o=null==n?void 0:n.data)?void 0:o.run_post_flow_action)?void 0:r.uri;if(!a)throw Error("Invalid response (0)");let l=a.split("?")[1];if(!l)throw Error("Invalid response (1)");let s=new URLSearchParams(l);if(!s.has("close_uri"))throw Error("Invalid response (2)");let c=decodeURIComponent(s.get("close_uri"));if(!c.includes("#access_token="))throw Error("Invalid response (3)");let u=new URLSearchParams(c.split("#")[1]).get("access_token");if(!u)throw Error("Invalid response (4)");return wc.access_token[e]=u,u||t||hs.warning({message:"Cannot get FBAndroid token",description:"Some features may not work properly",duration:0}),u}catch(c){return console.error(c),null}}if("EAADo1 (Business dialog)"===e){const n=await kc(),o=new URLSearchParams({fb_dtsg:n||""}).toString(),r=null==(a=(await Ke("https://www.facebook.com/dialog/oauth/business/cancel/?app_id=256002347743983&version=v19.0&logger_id=&user_scopes[0]=email&user_scopes[1]=read_insights&user_scopes[2]=read_page_mailboxes&user_scopes[3]=pages_show_list&redirect_uri=fbconnect%3A%2F%2Fsuccess&response_types[0]=token&response_types[1]=code&display=page&action=finish&return_scopes=false&return_format[0]=access_token&return_format[1]=code&tp=unspecified&sdk=&selected_business_id=&set_token_expires_in_60_days=false",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:o})).match(new RegExp("(?<=access_token=)(.*?)(?=\\&)")))?void 0:a[0];return r||t||hs.warning({message:"Cannot get EAADo1 token",description:"Some features may not work properly"}),wc.access_token[e]=r||null,r}if("EAAG (Business manager)"===e){const n=await Ke("https://business.facebook.com/business_locations"),o=null==(i=RegExp(/(EAAG\w+)/).exec(n))?void 0:i[1];return o||t||hs.warning({message:"Cannot get EAAG token",description:"Some features may not work properly",duration:0}),wc.access_token[e]=o||null,o}if("EAAB (Ads campaigns)"===e){for(const t of[async()=>{var e;const t=e=>{var t;return null==(t=null==e?void 0:e.match(/window\.__accessToken="([^"]+)"/))?void 0:t[1]};console.log("checking adsmanager");let n=await Ke("https://adsmanager.facebook.com/adsmanager/manage/campaigns",{method:"GET",credentials:"include"}),o=t(n);if(o)return o;const r=null==(e=null==n?void 0:n.match(/campaigns\?act=([0-9]+)&/))?void 0:e[1];return r&&(console.log("checking adsmanager with act",r),n=await Ke(`https://adsmanager.facebook.com/adsmanager/manage/campaigns?nav_source=no_referrer&breakdown_regrouping=1&act=${r}`),o=t(n),o)?o:null}]){const n=await t();if(console.log(n),n)return wc.access_token[e]=n||null,n}t||hs.warning({message:"Cannot get EAAB token",description:"Some features may not work properly",duration:0})}if("EAAI (Ads manager)"===e){const n=null==(l=(await Ke("https://www.facebook.com/ajax/bootloader-endpoint/?modules=AdsCanvasComposerDialog.react&__a=1")).match(/"access_token"\s*:\s*"([^"]+)"/))?void 0:l[1];return n||t||hs.warning({message:"Cannot get EAAI token",description:"Some features may not work properly"}),wc.access_token[e]=n||null,n}if("EAAM (Measurement hub)"===e){const n=await Ke("https://business.facebook.com/traffic-analysis/?nav_source=flyout_menu&business_id=505054504589818");console.log(n);const o=null==(s=RegExp(/(EAAM\w+)/).exec(n))?void 0:s[1];return o||t||hs.warning({message:"Cannot get EAAM token",description:"Some features may not work properly"}),wc.access_token[e]=o||null,o}}async function Ic(){if(!wc.uid)try{for(let e of["i_user","c_user"]){const t=await Ue("chrome.cookies.get",[{url:"https://www.facebook.com",name:e}]);if(wc.uid=null==t?void 0:t.value,wc.uid)break}}catch(e){const t="fb_aio_tmp_uid";let n=localStorage.getItem(t);return n||(n=(new Date).getTime().toString(),localStorage.setItem(t,n)),n}return wc.uid}function jc(e,t=500){return`https://graph.facebook.com/${e}/picture?height=${t}&access_token=${wc.access_token.EAAB||"6628568379%7Cc1e620fa708a1d5696fb991c1bde5662"}`}var Rc=(e=>(e.User="user",e.Page="page",e.Group="group",e.IGUser="ig_user",e.TikTokUser="tiktok_user",e.ThreadsUser="threads_user",e))(Rc||{});async function Bc(e,t="DEFAULT"){var n,o;const r=bc(await Cc({fb_api_req_friendly_name:"CometHovercardQueryRendererQuery",variables:{actionBarRenderLocation:"WWW_COMET_HOVERCARD",context:t,entityID:e,scale:2},doc_id:"27838033792508877"}));console.log(r);const a=null==(n=null==r?void 0:r.data)?void 0:n.node;if(!a)throw new Error("Wrong ID / Entity not found");const i=a.__typename.toLowerCase();if(!Object.values(Rc).includes(i))throw new Error("Not supported type: "+i);const l=a.comet_hovercard_renderer[i];let s;s="page"===i?"page":"user"!==i?"group":(null==(o=l.profile_plus_transition_path)?void 0:o.startsWith("PAGE"))||"ADDITIONAL_PROFILE_CREATION"===l.profile_plus_transition_path?"page":"user";const c=a.id||l.id;return{type:s,id:c,uid:c,name:l.name,avatar:l.profile_picture.uri,avatarBig:jc(l.id),url:l.profile_url||l.url,raw:r}}async function Nc(e){var t,n;const o=await Ke("https://graph.facebook.com/v12.0/"+ +e+"?fields="+["gender","email","birthday","mobile_phone","events.limit(100){start_time,type,created_time,end_time,name,place}","about","quotes","tagged"].join(",")+"&access_token="+await _c("EAAB (Ads campaigns)"));$c(o);const r=bc(o);return console.log(r),(null==(t=r.hometown)?void 0:t.id)&&(r.hometown=await Tc(r.hometown.id)),(null==(n=r.location)?void 0:n.id)&&(r.location=await Tc(r.location.id)),console.log(r),r}async function Tc(e){const t=await Ke("https://graph.facebook.com/v12.0/"+e+"?fields=location,name&access_token="+await _c("EAAB (Ads campaigns)"));$c(t);const n=bc(t);return console.log(n),n}function Pc(e){return(null==e?void 0:e.includes("https://"))?e:`https://fb.com/${e}`}function Ac(e=""){return!e||e.includes("453178253_471506465671661_2781666950760530985_n")}const Mc=Object.freeze(Object.defineProperty({__proto__:null,ACCESS_TOKEN_TYPE:yc,DefaultEntityAbout:{type:"user",id:"",uid:"",name:"unknown",avatar:"",url:"https://fb.com"},TargetType:Rc,checkErrorReponse:$c,fetchGraphQl:Cc,getAccessToken:_c,getEntityAbout:Bc,getEntityAboutFull:Nc,getExtVersion:Sc,getFbDtsg:kc,getFbUrlFromId:Pc,getGGMapDirUrl:function(e,t){return`https://www.google.com/maps/dir/?api=1&destination=${e},${t}`},getMyUid:Ic,getPlaceInfo:Tc,getSharerUrl:function({url:e,quote:t}){let n="";return e&&(n+="u="+encodeURIComponent(e.replace("https://",""))),t&&(n+="&quote="+encodeURIComponent(t.split(" ").join("+"))),`https://www.facebook.com/sharer/sharer.php?${n}`},getUserAvatarFromUid:jc,getUserInfoFromUid:async function(e){var t,n,o,r,a,i,l,s,c,u,d,p,m,f,g,h,b,v,y,w,x,C,$,E,S,O,k,_,I;const j=await Cc({fb_api_req_friendly_name:"ProfileCometHeaderQuery",doc_id:"4159355184147969",variables:{userID:e,shouldDeferProfilePic:!1,useVNextHeader:!1,scale:1.5}}),R=bc((null==(t=null==j?void 0:j.split("\n"))?void 0:t[0])||"{}");return{uid:e,gender:null==(o=null==(n=null==R?void 0:R.data)?void 0:n.user)?void 0:o.gender,name:null==(a=null==(r=null==R?void 0:R.data)?void 0:r.user)?void 0:a.name,alternateName:null==(l=null==(i=null==R?void 0:R.data)?void 0:i.user)?void 0:l.alternate_name,avatar:{id:null==(u=null==(c=null==(s=null==R?void 0:R.data)?void 0:s.user)?void 0:c.profile_photo)?void 0:u.id,link:null==(m=null==(p=null==(d=null==R?void 0:R.data)?void 0:d.user)?void 0:p.profile_photo)?void 0:m.url,uri:null==(h=null==(g=null==(f=null==R?void 0:R.data)?void 0:f.user)?void 0:g.profilePicLarge)?void 0:h.uri},cover:{id:null==(w=null==(y=null==(v=null==(b=null==R?void 0:R.data)?void 0:b.user)?void 0:v.cover_photo)?void 0:y.photo)?void 0:w.id,link:null==(E=null==($=null==(C=null==(x=null==R?void 0:R.data)?void 0:x.user)?void 0:C.cover_photo)?void 0:$.photo)?void 0:E.url,uri:null==(I=null==(_=null==(k=null==(O=null==(S=null==R?void 0:R.data)?void 0:S.user)?void 0:O.cover_photo)?void 0:k.photo)?void 0:_.image)?void 0:I.uri}}},isDefaultAvatar:Ac,searchUser:async function(e,t=!0){return bc(await Cc({doc_id:"7561210460668291",variables:{count:5,allow_streaming:!1,args:{callsite:"COMET_GLOBAL_SEARCH",config:{exact_match:t,high_confidence_config:null,intercept_config:null,sts_disambiguation:null,watch_config:null},experience:{client_defined_experiences:[],encoded_server_defined_params:null,fbid:null,type:"PEOPLE_TAB"},filters:[],text:e},cursor:null,feedbackSource:23,fetch_filters:!0,renderLocation:"search_results_page",scale:2,stream_initial_count:0,useDefaultActor:!1}}))},setCACHED:xc,trackEvent:Oc,wrapGraphQlParams:Ec},Symbol.toStringTag,{value:"Module"}));function zc(){const e=et(Ze.language),t=et(Ze.setLanguage);return{language:e,setLanguage:t,ti:t=>Dc(t,e)}}const Dc=(e,t=Ze.language(et.getState())||Je.en)=>null==e?"":"string"==typeof e||"number"==typeof e?e:e[t]??e[Je.en]??"?",Lc=Object.freeze(Object.defineProperty({__proto__:null,default:zc,trans:Dc},Symbol.toStringTag,{value:"Module"}));function Hc(){const{notification:e,message:t}=tt.useApp(),n=et(Ze.addNotification),o=et(Ze.setNotifications),r=et(Ze.notifications);return{message:t,notification:{...e,open:(t,o=!1)=>{Ye((()=>Promise.resolve().then((()=>ku))),void 0,import.meta.url).then((e=>{n({id:e.uuid(),time:Date.now(),type:t.type,message:Fc(t.message),description:Fc(t.description)})})),o||e.open(t)}},notifications:r,remove(e){Ye((()=>Promise.resolve().then((()=>Mc))),void 0,import.meta.url).then((e=>{e.trackEvent("Notifications:remove")})),o(r.filter((t=>t.id!==e)))},clear(){Ye((()=>Promise.resolve().then((()=>Mc))),void 0,import.meta.url).then((e=>{e.trackEvent("Notifications:clear")})),o([])}}}function Fc(e){if(!e)return"";if("string"==typeof e)return e;const t=e.props&&e.props.children;return t instanceof Array?t.map(Fc).join("\n"):Fc(t)}function Wc(){return{profile:et(Ze.profile),setProfile:et(Ze.setProfile)}}var Vc,qc;const Xc=qe(function(){if(qc)return Vc;qc=1;var e=nt(),t=ot(),n=rt(),o=at(),r=it(),a=lt(),i=st(),l=ct(),s=Object.prototype.hasOwnProperty;return Vc=function(c){if(null==c)return!0;if(r(c)&&(o(c)||"string"==typeof c||"function"==typeof c.splice||a(c)||l(c)||n(c)))return!c.length;var u=t(c);if("[object Map]"==u||"[object Set]"==u)return!c.size;if(i(c))return!e(c).length;for(var d in c)if(s.call(c,d))return!1;return!0}}());const Gc=/^\.+(\\|\/)|^\.+$/,Kc=/\.+$/;function Yc(e){const t=[],n=Object.keys(e[0]);t.push(n.join(","));for(const o of e){const e=n.map((e=>{const t=o[e];return`"${(null==t?void 0:t.toString().replace(/"/g,'\\"'))||""}"`}));t.push(e.join(","))}return t.join("\n")}function Uc(e,t={},n=Dc({en:"en-US",vi:"vi-VN"})){if(!e)return"";const o=e instanceof Date?e:new Date(parseInt(e));return new Date(o).toLocaleString(n,{hour:"numeric",minute:"numeric",hour12:!1,day:"2-digit",month:"2-digit",year:"numeric",...t})}function Qc(e){return e<10?`0${e}`:e}function Zc(e=new Date,t="-"){const n=e instanceof Date?e:new Date(parseInt(e)),o=n.getFullYear(),r=n.getMonth()+1,a=n.getDate();return[o,Qc(r),Qc(a)].join(t)}async function Jc(e,t=null,n=1e3){if(!t)return eu(e);for(let o=0;o<e;o+=n){if(t(e-o))return!1;await eu(n)}return!0}function eu(e){return new Promise((t=>setTimeout((()=>t(!0)),e)))}function tu(e="",t=""){return nu(e).normalize("NFD").replace(/[\u0300-\u036f]/g,t).replace(/[^a-zA-Z0-9]/g,t)}function nu(e){var t="àáãảạăằắẳẵặâầấẩẫậèéẻẽẹêềếểễệđùúủũụưừứửữựòóỏõọôồốổỗộơờớởỡợìíỉĩịäëïîöüûñçýỳỹỵỷ";e=e.toLowerCase();for(var n=0;n<76;n++)e=e.replace(RegExp(t[n],"gi"),"aaaaaaaaaaaaaaaaaeeeeeeeeeeeduuuuuuuuuuuoooooooooooooooooiiiiiaeiiouuncyyyyy"[n]);return e=e.trim().replace(/[^a-z0-9\-]/g,"-").replace(/-+/g,"-")}function ou(e,t=","){return e?e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,t):0}function ru(e,t=0){if(!(e=Number(e)))return"?";const n=["B","KB","MB","GB","TB","PB","EB","ZB","YB"];let o=0;for(;e>=1024&&o<n.length-1;)e/=1024,o++;return e.toFixed(t)+n[o]}function au(e,t){return e?e.length<=t?e:e.slice(0,t)+"...":""}function iu(e){if(!e)return"00:00";const t=Math.floor(e/86400),n=Math.floor(e%86400/3600),o=Math.floor(e%3600/60),r=Math.floor(e%3600%60);let a="";return t>0&&(a+=`${t}:`),n>0&&(a+=n<10?`0${n}:`:`${n}:`),a+=o<10?`0${o}:`:`${o}:`,a+=r<10?`0${r}`:`${r}`,a}function lu(e,t,n=0){const o=t.slice(0,e),r=t.slice(e),a=[];let i=!1;return{start:()=>new Promise((e=>{let t=0;async function n(o){if(t===r.length||i)return void e(a);r[t](t,o).then((e=>(n(o),a.push(e),e))),t++}o.forEach(((e,o)=>{e(t,o).then((e=>(n(o),a.push(e),e)))}))})),stop:()=>{i=!0}}}const su={};function cu(e,t){var n,o,r,a;if(!t)if(document.documentElement.lang)t=document.documentElement.lang;else if(navigator.language)t=navigator.language;else try{t=null==(a=null==(r=new URL(null==(o=null==(n=Array.from(document.querySelectorAll("head > link[rel='search']")))?void 0:n.find((e=>{var t;return null==(t=null==e?void 0:e.getAttribute("href"))?void 0:t.includes("?locale=")})))?void 0:o.getAttribute("href")))?void 0:r.searchParams)?void 0:a.get("locale")}catch{console.log("Cannot find browser locale. Use en as default for number formatting."),t="en"}let i,l;switch(e){case"compactLong":i="compact",l="long";break;case"standard":i="standard",l="short";break;default:i="compact",l="short"}let s=t+i+l;if(!su[s]){const e=Intl.NumberFormat(t,{notation:i,compactDisplay:l});su[s]=e}return su[s]}function uu(e,t="",n=""){return cu(t,n).format(e)}function du(e=[],t=[],n=e=>e.id||e){const o=[];for(const r of t){const t=n(r);-1===e.findIndex((e=>n(e)===t))&&o.push(r)}return o}function pu({url:e,options:t={},checkAbortFn:n,intervalMs:o=500}){return new Promise(((r,a)=>{const i=new AbortController,l=i.signal;fetch(e,{...t,signal:l}).then(r).catch(a);const s=setInterval((()=>{n()&&(i.abort(),clearInterval(s),a())}),o)}))}function mu(e,t=!0){if(!e)return{};if(e.edges&&(!t||e.page_info))return e;for(let n in e)if("object"==typeof e[n]&&null!==e[n]){let o=mu(e[n],t);if(!Xc(o))return o}return{}}function fu(e,t){var n;return null==(n=null==e?void 0:e.find((e=>null==e?void 0:e[t])))?void 0:n[t]}function gu(e,t,n=!0,o=!1,r=[]){if(!e||"object"!=typeof e)return n?null:[];const a=Array.isArray(t)?t:t.split("."),i=[],l=[{currentObj:e,currentPathIndex:0,correctPath:!1}];let s=!1;for(;l.length;){const{currentObj:e,currentPathIndex:t,correctPath:c}=l.pop();if(t===a.length){const t=o?c?e:null:e;if(n)return t;i.push(t);continue}const u=a[t];r.length&&r.includes(u)||"object"==typeof e&&null!==e&&(u in e&&(s=!0,l.push({currentObj:e[u],currentPathIndex:t+1,correctPath:!0})),o&&s||Object.entries(e).forEach((([e,n])=>{e===u||r.includes(e)||"object"!=typeof n||null===n||l.push({currentObj:n,currentPathIndex:t,correctPath:!1})})))}return n?null:i}function hu(e){const t=Date.now();return vu(Math.abs(t-e))}function bu(e,t,n){const o=new Date,r=new Date(e,t,n);let a=o.getFullYear()-r.getFullYear();const i=o.getMonth()-r.getMonth();return(i<0||0===i&&o.getDate()<r.getDate())&&a--,a}function vu(e){return e<6e4?`${Math.floor(e/1e3)}${Dc({vi:" giây",en:" sec"})}`:e<36e5?`${Math.floor(e/6e4)}${Dc({vi:" phút",en:" min"})}`:e<864e5?`${Math.floor(e/36e5)}${Dc({vi:" giờ",en:" hour"})}`:e<2592e6?`${Math.floor(e/864e5)}${Dc({vi:" ngày",en:" day"})}`:e<31536e6?`${Math.floor(e/2592e6)}${Dc({vi:" tháng",en:" month"})}`:`${Math.floor(e/31536e6)}${Dc({vi:" năm",en:" year"})}`}function yu(e,t){return Math.floor(Math.random()*(t-e+1)+e)}function wu(e="",t=""){return!(!(null==e?void 0:e.length)||!(null==t?void 0:t.length))&&tu(t).includes(tu(e))}function xu(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}))}function Cu(e){return e&&Array.isArray(e)?e.slice().reverse():[]}function $u(e){return!/^[\saAàÀảẢãÃáÁạẠăĂằẰẳẲẵẴắẮặẶâÂầẦẩẨẫẪấẤậẬbBcCdDđĐeEèÈẻẺẽẼéÉẹẸêÊềỀểỂễỄếẾệỆfFgGhHiIìÌỉỈĩĨíÍịỊjJkKlLmMnNoOòÒỏỎõÕóÓọỌôÔồỒổỔỗỖốỐộỘơƠờỜởỞỡỠớỚợỢpPqQrRsStTuUùÙủỦũŨúÚụỤưƯừỪửỬữỮứỨựỰvVwWxXyYỳỲỷỶỹỸýÝỵỴzZ]*$/u.test(e.normalize("NFC"))}function Eu(e=0,t=0){return 1e3*Math.round((e+t)/2/1e3)}function Su(e,t){return Array.from(new Set(e.map((e=>e[t])))).map((n=>({count:e.filter((e=>e[t]==n)).length,text:n,value:n}))).sort(((e,t)=>t.count-e.count)).map((e=>({text:au(e.text,100)+" ("+e.count+")",value:e.value})))}function Ou(e,t="_",n=100){return function(e,t={}){const n=/[\u0000-\u001F\u0080-\u009F]/g,o=/([<>:"/\\|?*\u0000-\u001F]){2,}/g;if("string"!=typeof e)throw new TypeError("Expected a string");const r=void 0===t.replacement?"!":t.replacement;if(/[<>:"/\\|?*\u0000-\u001F]/g.test(r)&&n.test(r))throw new Error("Replacement string cannot contain reserved filename characters");r.length>0&&(e=e.replace(o,"$1")),e=(e=(e=(e=(e=e.normalize("NFD")).replace(Gc,r)).replace(/[<>:"/\\|?*\u0000-\u001F]/g,r)).replace(n,r)).replace(Kc,""),r.length>0&&("."===e[0]||"."!==e[0]||(e=r+e),"."===e[e.length-1]&&(e+=r));e=/^(con|prn|aux|nul|com\d|lpt\d)$/i.test(e)?e+r:e;const a="number"==typeof t.maxLength?t.maxLength:100;if(e.length>a){const t=e.lastIndexOf(".");if(-1===t)e=e.slice(0,a);else{const n=e.slice(0,t),o=e.slice(t);e=n.slice(0,Math.max(1,a-o.length))+o}}return e}(au(e,n),{replacement:t,maxLength:n}).trim().replace(/\.+$/,"").replace(/\s+/g,t).replace(/[\uD800-\uDFFF]/g,"").replace(/(^[\s\u200b]*|[\s\u200b]*$)/g,"").replace(new RegExp("\\p{Cf}","gu"),"").toLowerCase()}const ku=Object.freeze(Object.defineProperty({__proto__:null,capitalizeFirstLetter:function(e=""){return e?e.charAt(0).toUpperCase()+e.slice(1):""},createFilters:Su,deepEqual:function e(t,n){if(t===n)return!0;if("object"==typeof t&&null!=t&&"object"==typeof n&&null!=n){if(Object.keys(t).length!=Object.keys(n).length)return!1;for(let o in t){if(!Object.hasOwn(n,o))return!1;if(!e(t[o],n[o]))return!1}return!0}return!1},deepFind:gu,fetchWithAbort:pu,findDataObject:mu,findInEachItem:fu,formatNumber:uu,formatNumberWithCommas:ou,formatSeconds:iu,formatSize:ru,formatTime:Uc,formatTimeDiff:vu,formatTimeYYYYMMDD:Zc,getAge:bu,getBlobFromUrlWithProgress:async function(e,t){const n=await fetch(e,{});if(!n.ok)throw new Error(`Error: ${n.status} - ${n.statusText}`);const o=n.headers.get("content-length"),r=parseInt(o,10);let a=0;const i=n.body.getReader(),l=[],s=Date.now();for(;;){const{done:e,value:n}=await i.read();if(e)break;a+=n.byteLength;const o=(Date.now()-s+1)/1e3;null==t||t({loaded:a,total:r,speed:a/o}),l.push(n)}return new Blob(l,{type:n.headers.get("content-type")})},getMidTime:Eu,getNumberFormatter:cu,isMobile:function(){let e=!1;var t;return t=navigator.userAgent||navigator.vendor||window.opera,(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(t)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(t.substr(0,4)))&&(e=!0),e},isStrangeName:$u,limitString:au,objectToCsv:Yc,padZero:Qc,promiseAllStepN:lu,randInt:yu,removeAccent:tu,removeDuplicate:du,reverseArray:Cu,safeFilename:Ou,sleep:Jc,softEqualString:wu,stringToSlug:nu,timeToNow:hu,uuid:xu},Symbol.toStringTag,{value:"Module"})),_u=1752175771627,Iu=1750301873273,ju=({children:t,timeout:n=5e3,onTimeout:o=Ru})=>(e.useEffect((()=>{const e=setTimeout((()=>{null==o||o()}),n);return()=>clearTimeout(e)}),[]),ut.jsx(ut.Fragment,{children:t}));async function Ru(){const e=(await Ye((async()=>{const{default:e}=await import("./sweetalert2.esm.all-BZxvatOx.js");return{default:e}}),[],import.meta.url)).default,{trans:t}=await Ye((async()=>{const{trans:e}=await Promise.resolve().then((()=>Lc));return{trans:e}}),void 0,import.meta.url);e.fire({icon:"question",title:t({en:"Loading too slow?",vi:"Tải trang chậm?"}),text:t({en:"Maybe there is an error. Reload website may help.",vi:"Có thể có lỗi nào đó. Hãy thử tải lại trang và thử lại."}),showCancelButton:!0,confirmButtonText:t({en:"Yes, Reload page",vi:"Có, Tải lại trang"}),cancelButtonText:t({en:"No, Wait for it",vi:"Không, Tôi sẽ chờ"}),reverseButtons:!0}).then((e=>{e.isConfirmed&&window.location.reload()}))}const Bu=ut.jsx(ju,{children:ut.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",width:"100%"},children:ut.jsx(is,{delay:200,size:"large"})})}),Nu=ut.jsx(ju,{children:ut.jsx(Ce,{active:!0})}),Tu=ut.jsx(ju,{children:ut.jsx(Ce.Image,{active:!0})}),Pu=ut.jsx(ju,{children:ut.jsx(Ce.Button,{active:!0})}),Au={fallback:Bu},Mu=(e,t)=>dt((()=>Ye((()=>import("./Redirector-BG53AR2f.js")),__vite__mapDeps([0,1,2]),import.meta.url).then((n=>{const o=n.default;return n=>O.createElement(o,{...n,to:e,search:t})}))),Au),zu=dt((()=>Ye((()=>import("./index-B5QOFicW.js")),__vite__mapDeps([3,1,2,4,5]),import.meta.url)),Au),Du=dt((()=>Ye((()=>import("./Disclaimer-D85ZEEs0.js")),__vite__mapDeps([6,1,2,7,8,9,10]),import.meta.url)),Au),Lu=dt((()=>Ye((()=>import("./FAQ-iwq4vu2Q.js")),__vite__mapDeps([11,1,2,7,8,9,10,12,13]),import.meta.url)),Au),Hu=dt((()=>Ye((()=>import("./ExtUpdated-Czm1FRkm.js")),__vite__mapDeps([14,1,2,15]),import.meta.url)),Au),Fu=dt((()=>Ye((()=>import("./Dashboard-DteWvCcQ.js")),__vite__mapDeps([16,1,2,7,8,9,10,17]),import.meta.url)),Au),Wu=dt((()=>Ye((()=>import("./AllMessages-LP8i35DW.js")),__vite__mapDeps([18,1,2,19,20,5,21,22,23,24,25,15,26,27,28,29,4,30,31,32,33,34,12,13,35,36,37,38,39,40,41,42,10,43,9,44,45,46]),import.meta.url)),Au),Vu=dt((()=>Ye((()=>import("./FirstMessages-kZCBEyr1.js")),__vite__mapDeps([47,1,2,31,23,24,25,15,26,27,28,29,4,5,7,8,9,10,48,21,49,22,41,37,42,13,50,43,44,45,46,36]),import.meta.url)),Au),qu=dt((()=>Ye((()=>import("./AllFriends-ChasWEW6.js")),__vite__mapDeps([51,1,2,31,33,34,12,13,35,36,37,38,39,40,20,5,21,22,41,42,29,10,43,27,28,9,19,23,52,53,54,55,56,24,25,15,26,4,57,32,58,59,60,17,61,62,63,64,65,7,8,44,45,46,66]),import.meta.url)),Au),Xu=dt((()=>Ye((()=>import("./FriendRequests-DkKszU9E.js")),__vite__mapDeps([67,1,2,33,34,12,13,35,36,37,38,39,40,20,5,21,22,41,42,29,10,43,27,28,9,68,23,60,25,15,26,4,17,65,7,8,44,45,46]),import.meta.url)),Au),Gu=dt((()=>Ye((()=>import("./index-D-JvvpDI.js")),__vite__mapDeps([69,1,2,23,7,8,9,10,46,28,36,70,21,41,37,42,22,13,29,27,4,5]),import.meta.url)),Au),Ku=dt((()=>Ye((()=>import("./index-C09Nl4fP.js")),__vite__mapDeps([71,1,2,23,7,8,9,10,4,5]),import.meta.url)),Au),Yu=dt((()=>Ye((()=>import("./index-CCwFJ0Qh.js")),__vite__mapDeps([72,1,2,23,7,8,9,10,4,5]),import.meta.url)),Au),Uu=dt((()=>Ye((()=>import("./index-c8qoPkv2.js")),__vite__mapDeps([73,1,2,31,52,19,20,5,21,22,23,74,75,25,15,26,27,28,29,4,57,32,58,59,12,13,60,40,39,17,65,7,8,9,10,33,34,35,36,37,38,41,42,43,44,45]),import.meta.url)),Au),Qu=dt((()=>Ye((()=>import("./index-D8FV2cZb.js")),__vite__mapDeps([76,1,2,31,52,23,19,20,5,21,22,77,57,32,58,59,4,12,13,33,34,35,36,37,38,39,40,41,42,29,10,43,27,28,9,60,25,15,26,17,65,7,8,46,66,45]),import.meta.url)),Au),Zu=dt((()=>Ye((()=>import("./VIP-BzUqNdLn.js")),__vite__mapDeps([78,1,2,25,15,26,27,28,29,4,5,79,7,8,9,10,34,35,36,37,13,38,39,40,20,21,22,41,42,43]),import.meta.url)),Au),Ju=dt((()=>Ye((()=>import("./index-DAum7_7a.js")),__vite__mapDeps([80,1,2,81,33,34,12,13,35,36,37,38,39,40,20,5,21,22,41,42,29,10,43,27,28,9,19,60,25,15,26,4,17,23,57,32,58,59,65,7,8,53,54,44,45,46]),import.meta.url)),Au),ed=dt((()=>Ye((()=>import("./Shortcuts-BLAfYAwQ.js")),__vite__mapDeps([82,1,2,7,8,9,10,50,41,37,21,42,22,13,29,43]),import.meta.url)),Au),td=dt((()=>Ye((()=>import("./AutoRun-C_QIEDH2.js")),__vite__mapDeps([83,1,2,23,32,84,9,10,50,41,37,21,42,22,13,29,43,8,40,39,17]),import.meta.url)),Au),nd=dt((()=>Ye((()=>import("./Tip-CTD3sRGu.js")),__vite__mapDeps([85,1,2,7,8,9,10,23,27,28,29,4,5,50,41,37,21,42,22,13,43]),import.meta.url)),Au),od=dt((()=>Ye((()=>import("./index-B5mnKw3Z.js")),__vite__mapDeps([86,1,2,54,33,34,12,13,35,36,37,38,39,40,20,5,21,22,41,42,29,10,43,27,28,9,23,19,31,60,25,15,26,4,17,65,7,8,44,45,46]),import.meta.url)),Au),rd=dt((()=>Ye((()=>import("./index-GOnsP9v3.js")),__vite__mapDeps([87,1,2,55,56,31,23,33,34,12,13,35,36,37,38,39,40,20,5,21,22,41,42,29,10,43,27,28,9,19,26,25,15,4,60,17,32,65,7,8,88,44,45,46,89,48,49,59]),import.meta.url)),Au),ad=dt((()=>Ye((()=>import("./InvisibleText-BbGrG0bl.js")),__vite__mapDeps([90,1,2,23,91,7,8,9,10,45,40,39,27,28,29,17]),import.meta.url)),Au),id=dt((()=>Ye((()=>import("./HackDuckRace-CmjLwOqc.js")),__vite__mapDeps([92,1,2,93,23,7,8,9,10,17,46,28,36,27,29]),import.meta.url)),Au),ld=dt((()=>Ye((()=>import("./AccountsStore-etvUhsV6.js")),__vite__mapDeps([94,1,2,7,8,9,10]),import.meta.url)),Au),sd=dt((()=>Ye((()=>import("./Checkout-DKoramAx.js")),__vite__mapDeps([95,1,2,23,8,9,10,64,89,4,5,17,27,28,29,46,36,35,37,13,38,39,40,20,21,22,41,42,43,44,45]),import.meta.url)),Au),cd=dt((()=>Ye((()=>import("./Tutorial-DLivMi4O.js")),__vite__mapDeps([96,1,2,7,8,9,10,89,4,5,46,28,36]),import.meta.url)),Au),ud=dt((()=>Ye((()=>import("./index-Bh0HZd6B.js")),__vite__mapDeps([97,1,2,7,8,9,10,23,4,5]),import.meta.url)),Au),dd=dt((()=>Ye((()=>import("./AIOBuff-BnV-m9Hh.js")),__vite__mapDeps([98,1,2,7,8,9,10,17,46,28,36]),import.meta.url)),Au),pd=dt((()=>Ye((()=>import("./index-CotK2-Bi.js")),__vite__mapDeps([99,1,2,7,8,9,10,4,5]),import.meta.url)),Au),md=dt((()=>Ye((()=>import("./index-BatTeoJ3.js").then((e=>e.i))),__vite__mapDeps([100,1,2,7,8,9,10]),import.meta.url)),Au),fd=dt((()=>Ye((()=>import("./Updates-BJWNVgkT.js")),__vite__mapDeps([101,1,2,7,8,9,10,34,27,28,29]),import.meta.url)),Au),gd=dt((()=>Ye((()=>import("./Tinder-C_lpu73L.js")),__vite__mapDeps([102,1,2,7,8,9,10,23,103,104,105,34,106,45,40,39,20,5,21,22,27,28,29,50,41,37,42,13,43,17,46,36]),import.meta.url)),Au),hd=dt((()=>Ye((()=>import("./UnlockFacebook-C_nosh3F.js")),__vite__mapDeps([107,1,2,7,8,9,10,46,28,36]),import.meta.url)),Au),bd=dt((()=>Ye((()=>import("./index-PO01UXcZ.js")),__vite__mapDeps([108,1,2,7,8,9,10,23,32,59,21,27,28,29,41,37,42,22,13,4,5,89,66,45]),import.meta.url)),Au),vd=dt((()=>Ye((()=>import("./HackWheelOfNames-BaccxXRs.js")),__vite__mapDeps([109,1,2,7,8,9,10,23,93,25,15,26,27,28,29,4,5,17,58]),import.meta.url)),Au),yd=dt((()=>Ye((()=>import("./index-BwUwN3wG.js")),__vite__mapDeps([110,1,2,7,8,9,10,23,4,5,89,41,37,21,42,22,13,29,27,28,50,43,59,17,40,39]),import.meta.url)),Au),wd=dt((()=>Ye((()=>import("./index-CiPeuwxC.js")),__vite__mapDeps([111,1,2]),import.meta.url)),Au),xd=dt((()=>Ye((()=>import("./GetAccessToken-CISR6svj.js")),__vite__mapDeps([112,1,2,7,8,9,10,17,27,28,29]),import.meta.url)),Au),Cd=dt((()=>Ye((()=>import("./index-BSSD-6dh.js")),__vite__mapDeps([113,1,2,83,23,32,84,9,10,50,41,37,21,42,22,13,29,43,8,40,39,17,7,104,58]),import.meta.url)),Au),$d=dt((()=>Ye((()=>import("./index-nF4hU1vs.js")),__vite__mapDeps([114,1,2,7,8,9,10,23,4,5,42]),import.meta.url)),Au),Ed=dt((()=>Ye((()=>import("./index-fhcER1I_.js")),__vite__mapDeps([115,1,2,7,8,9,10,23,4,5]),import.meta.url)),Au),Sd=dt((()=>Ye((()=>import("./Comparison-BUeFg8ZB.js")),__vite__mapDeps([116,1,2,7,8,9,10,33,34,12,13,35,36,37,38,39,40,20,5,21,22,41,42,29,43,27,28]),import.meta.url)),Au),Od=dt((()=>Ye((()=>import("./UsefulScripts-GULpB0IR.js")),__vite__mapDeps([117,1,2,7,8,9,10]),import.meta.url)),Au),kd=dt((()=>Ye((()=>import("./Support-Bkpt2tE5.js")),__vite__mapDeps([118,1,2,7,8,9,10,46,28,36,17]),import.meta.url)),Au),_d=[{label:{en:"Dashboard",vi:"Trang chủ"},icon:"fa-solid fa-house fa-lg",path:"/",dontNeedExt:!0,showFooter:!0,element:Fu},{label:{en:"Pricing",vi:"Bảng giá"},badgeFn:()=>({bg:"#f008",color:"white",text:Dc({vi:"Mua 1 được 3",en:"Buy 1 get 3"})}),icon:"fa-solid fa-tags fa-lg",path:"/checkout",dontNeedExt:!0,element:sd},{key:"features",label:{en:"Features",vi:"Tính năng"},icon:"fa-solid fa-puzzle-piece fa-lg",badgeFn:()=>({bg:"gray",color:"white",text:hu(_u)+" "+Dc({vi:"trước",en:"ago"})}),children:[{label:{en:"All 💎",vi:"Tất cả 💎"},icon:"fa-solid fa-crown",path:"/vip",dontNeedExt:!0,element:Zu},{label:{en:"Updates",vi:"Cập nhật"},icon:"fa-solid fa-circle-up",path:"/updates",dontNeedExt:!0,element:fd,badgeFn:()=>({bg:"gray",color:"white",text:hu(_u)+" "+Dc({vi:"trước",en:"ago"})})},{label:{en:"Coming soon",vi:"Sắp ra mắt"},icon:"fa-solid fa-hourglass-half",path:"/coming-soon",badge:pt.NEW,dontNeedExt:!0,element:bd},{label:{en:"Comparison",vi:"So sánh"},icon:"fa-solid fa-scale-balanced",path:"/comparison",dontNeedExt:!0,badge:pt.NEW,element:Sd},{divider:!0},{label:{en:"Tip",vi:"Mẹo"},icon:"fa-solid fa-lightbulb",path:"/tip",dontNeedExt:!0,element:nd},{label:{en:"Shortcuts",vi:"Phím tắt"},icon:"fa-solid fa-link",path:"/shortcuts",dontNeedExt:!0,element:ed},{label:{en:"Tutorial",vi:"Hướng dẫn"},icon:"fa-solid fa-book",path:"/tutorial",dontNeedExt:!0,element:cd}]},{divider:!0},{label:{en:"Bulk Downloader",vi:"Tải Hàng Loạt"},badge:pt.HOT,icon:"fa-solid fa-arrows-down-to-line fa-lg fa-bounce",path:"/bulk-downloader",element:Gu},{label:{en:"Downloader",vi:"Trình Tải xuống"},icon:"fa-solid fa-arrow-down fa-lg",path:"/downloader",element:$d},{divider:!0},{key:"auto",label:{en:"Automation",vi:"Tự động hoá"},icon:"fa-solid fa-robot fa-lg fa-beat",children:[{label:{en:"AutoRun",vi:"Tự chạy"},badge:pt.HOT,icon:"fa-solid fa-play",path:"/autorun",element:td},{label:{en:"APIs",vi:"APIs"},icon:"fa-solid fa-plug",path:"/apis",badgeFn:()=>({bg:"gray",color:"white",text:hu(Iu)+" "+Dc({vi:"trước",en:"ago"})}),element:yd},{label:{en:"Comment",vi:"Bình luận"},icon:"fa-solid fa-comment",path:"/auto/comment",badge:pt.WIP,element:ud}]},{key:"clean-fb",label:{en:"Clean Facebook",vi:"Dọn Facebook"},icon:"fa-solid fa-broom fa-lg",children:[{label:{en:"Posts",vi:"Bài viết"},icon:"fa-solid fa-newspaper",path:"/posts",badge:pt.HOT,element:rd},{label:{en:"Friends",vi:"Lọc bạn bè"},icon:"fa-solid fa-users",path:"/friends",badge:pt.HOT,element:qu},{label:{en:"Blocked",vi:"Đang chặn"},icon:"fa-solid fa-ban",path:"/blocked",element:od},{label:{en:"Friend requests",vi:"Lời mời kết bạn"},icon:"fa-solid fa-user-plus",path:"/friend-requests",element:Xu},{label:{en:"Following",vi:"Đang theo dõi"},icon:"fa-solid fa-person-walking-arrow-right",path:"/follows",element:Ju},{label:{en:"Groups",vi:"Nhóm"},icon:"fa-solid fa-users-line",path:"/groups",element:Uu},{label:{en:"Pages",vi:"Trang"},icon:"fa-solid fa-flag",path:"/pages",element:Qu},{label:{en:"Activities",vi:"Hoạt động"},icon:"fa-solid fa-clock-rotate-left",path:"/activities",badge:pt.WIP,element:md}]},{key:"discover",label:{en:"Discover",vi:"Khám phá"},icon:"fa-solid fa-eye fa-lg",children:[{label:{en:"Stories",vi:"Tin (Story)"},icon:"fa-solid fa-heart",path:"/stories",badge:pt.HOT,element:Yu},{label:{en:"Messages",vi:"Tin nhắn"},icon:"fa-solid fa-comments",path:"/messages/all",badge:pt.HOT,element:Wu},{label:{en:"First messages",vi:"Tin nhắn đầu tiên"},icon:"fa-solid fa-clock-rotate-left",path:"/messages/first",element:Vu},{label:{en:"Reels",vi:"Reels"},icon:"fa-solid fa-film",path:"/reels",element:Ku},{label:{en:"Map",vi:"Bản đồ"},icon:"fa-solid fa-map",path:"/map",dontNeedExt:!0,element:Ed,hideFooter:!0},{label:{en:"Tinder",vi:"Tinder"},icon:"fa-solid fa-heart",path:"/tinder",dontNeedExt:!0,element:gd}]},{hidden:!0,key:"downloader-folder",label:{en:"Downloader",vi:"Tải xuống"},icon:"fa-solid fa-arrow-down",children:[{label:{en:"Video down",vi:"Tải video"},badge:pt.HOT,icon:"fa-solid fa-film",path:"/video-downloader",element:Mu("/downloader","tab=video")},{label:{en:"Document down",vi:"Tải tài liệu"},badge:pt.NEW,icon:"fa-solid fa-book",path:"/doc-downloader",element:Mu("/downloader","tab=doc")}]},{key:"tools",label:{en:"Tools",vi:"Tiện ích"},icon:"fa-solid fa-star fa-lg",children:[{label:{en:"Web timer",vi:"Web timer"},icon:"fa-solid fa-clock",path:"/web-timer",element:Cd},{label:{en:"Access token",vi:"Access token"},icon:"fa-solid fa-key",path:"/get-access-token",badge:pt.HOT,element:xd},{label:{en:"Search tools",vi:"Máy tìm kiếm"},icon:"fa-solid fa-magnifying-glass",path:"/search",badge:pt.NEW,dontNeedExt:!0,element:pd},{label:{en:"Invisible text",vi:"Chữ tàng hình"},icon:"fa-solid fa-eye-slash",path:"/invisible-text",dontNeedExt:!0,element:ad},{label:"WheelOfNames",icon:"fa-solid fa-dharmachakra",path:"/wheel-of-names",badge:pt.HOT,element:vd},{label:"Hack duckrace",icon:"fa-solid fa-dice",path:"/duckrace",element:id},{dontNeedExt:!0,label:"Useful Scripts",icon:"fa-solid fa-code",path:"/useful-scripts",badge:pt.NEW,element:Od},{hidden:!0,label:{en:"Facebook Nokia",vi:"Facebook Nokia"},icon:"fa-brands fa-facebook",path:"/relax/fb-old",dontNeedExt:!0,element:wd}]},{key:"store",label:{en:"Store",vi:"Cửa hàng"},icon:"fa-solid fa-store fa-lg",children:[{label:{en:"AIO Buff",vi:"AIO Buff"},icon:"fa-solid fa-rocket",path:"/aiobuff",dontNeedExt:!0,element:dd},{label:{en:"Unlock",vi:"Mở khóa"},icon:"fa-solid fa-unlock",path:"/store/unlock",dontNeedExt:!0,element:hd},{label:{en:"Accounts",vi:"Tài khoản"},icon:"fa-solid fa-user",path:"/store/accounts",badge:pt.NEW,dontNeedExt:!0,element:ld}]},{divider:!0},{label:"FAQ",icon:"fa-solid fa-circle-question fa-lg",path:"/FAQ",dontNeedExt:!0,element:Lu},{label:{vi:"Hỗ trợ",en:"Support"},icon:"fa-solid fa-headset fa-lg",path:"/support",dontNeedExt:!0,element:kd},{hidden:!0,key:"hidden",label:{en:"Hidden",vi:"Ẩn"},icon:"fa-solid fa-eye fa-lg",children:[{path:"/ext-updated",element:Hu},{path:"/disclaimer",element:Du},{path:"/dev",element:zu}]}],Id=""+new URL("logo-BgUNo_GL.png",import.meta.url).href,{Header:jd,Sider:Rd,Content:Bd,Footer:Nd}=Kl,Td=dt((()=>Ye((()=>import("./LanguagePicker-CeN5tnUe.js")),__vite__mapDeps([119,1,2,20,5,21,22]),import.meta.url))),Pd=dt((()=>Ye((()=>import("./ProfileHeader-DNKCz-3I.js")),__vite__mapDeps([120,1,2,25,15,26,27,28,29,4,5,65,44,10,45]),import.meta.url))),Ad=dt((()=>Ye((()=>import("./Notifications-BvIEU1Cy.js")),__vite__mapDeps([121,1,2,45,50,10,41,37,21,42,22,13,29,9,43,8]),import.meta.url))),Md=dt((()=>Ye((()=>import("./GlobalSearch-CgICNTjl.js")),__vite__mapDeps([122,1,2,106,79,59,21,27,28,29,17]),import.meta.url))),zd=dt((()=>Ye((()=>import("./index-6ffL1WDz.js").then((e=>e.i))),__vite__mapDeps([123,1,2]),import.meta.url))),Dd=dt((()=>Ye((()=>import("./index.es-CS4dvFDh.js")),__vite__mapDeps([124,1,2]),import.meta.url).then((e=>({default:e.TypeAnimation}))))),Ld=dt((()=>Ye((()=>import("./NotFound-Df9pngtk.js")),__vite__mapDeps([125,1,2,7,8,9,10,42,50,41,37,21,22,13,29,43]),import.meta.url))),Hd=t=>(e.useEffect((()=>{document.title=t.title||""}),[t.title]),t.children);function Fd(e,t){return e.filter((e=>!e.hidden)).map((e=>{if(e.divider)return{type:"divider"};const n=e.badgeFn?e.badgeFn():e.badge,o=n&&ut.jsx(Uo,{color:n.bg,style:{color:n.color},count:t(n.text)}),r=ut.jsxs(ul,{children:[e.badgeBefore&&o,(e.label||e.typing)&&(e.typing?ut.jsx(Dd,{sequence:[...Array.isArray(e.typing)?e.typing.map((e=>t(e))):[t(e.typing)],2e3,""],speed:20,deletionSpeed:60,repeat:1/0},t(e.label)):t(e.label)),e.image&&ut.jsx("img",{src:e.image,style:{height:40}}),!e.badgeBefore&&o]}),a=e.key||e.path||t(e.label);return{key:a,label:e.path?ut.jsx(yt,{to:e.path,target:e.newTab?"_blank":"_self",onClick:()=>{Oc("Menu:click:"+a)},children:r}):r,icon:e.icon?ut.jsx("i",{className:e.icon}):null,children:e.children?Fd(e.children,t):null}}))}function Wd(e,t){let n=[];return e.forEach((e=>{e.path&&n.push({path:e.path,element:ut.jsx(Hd,{title:t(e.label)+" | FB AIO",children:e.element?ut.jsx(e.element,{}):null})}),e.children&&(n=n.concat(Wd(e.children,t)))})),n}const Vd=Object.freeze(Object.defineProperty({__proto__:null,default:function(){const{notification:t,message:n}=Hc(),{ti:o,language:r}=zc(),a=mt(),i=et(Ze.hydrated),l=et(Ze.compactMode),s=et(Ze.setCompactMode),c=et(Ze.smoothScroll),u=et(Ze.setSmoothScroll),d=et(Ze.darkMode),p=et(Ze.setDarkMode),m=et(Ze.muted),f=et(Ze.setMuted),{setProfile:g,profile:h}=Wc(),b=e.useRef(null),[v,y]=e.useState(!1),[w,x]=e.useState(!1),[C,$]=e.useState(!1),[E,S]=e.useState(!0),[O,k]=e.useState(null),[_,I]=e.useState(!1),j=e.useRef(!1),R=function(t=0){const[n,o]=e.useState(t);return e.useEffect((()=>{const e=document.querySelector(".ant-layout-sider-trigger");e&&(e.style.bottom=n-t+"px")}),[n]),e.useEffect((()=>{const e=()=>{const e=parseInt(getComputedStyle(document.body).paddingBottom,10)||0;o(e+t)};e();const n=new MutationObserver(e);return n.observe(document.body,{attributes:!0,attributeFilter:["style"]}),window.addEventListener("resize",e),()=>{n.disconnect(),window.removeEventListener("resize",e)}}),[t]),n}(24),B=e.useRef(!1);e.useEffect((()=>{i&&(c?(B.current=!0,Ye((()=>import("./smoothScroll-DH7H2UuZ.js")),[],import.meta.url).then((e=>{e.enableSmoothScroll()}))):B.current&&(B.current=!1,Ye((()=>import("./smoothScroll-DH7H2UuZ.js")),[],import.meta.url).then((e=>{e.disableSmoothScroll()}))))}),[i,c]),e.useEffect((()=>{b.current&&b.current.scrollTo(0,0)}),[a.pathname]);const[N,T]=e.useMemo((()=>[Fd(_d,o),Wd(_d,o)]),[r]),P=e.useMemo((()=>_d.map((e=>e.children||e)).flat().find((e=>e.path===a.pathname))),[a.pathname]);e.useEffect((()=>{if(null==P?void 0:P.confirmOnLeave){const e=new AbortController;return window.addEventListener("beforeunload",(e=>{e.preventDefault(),e.returnValue=""}),{signal:e.signal}),()=>{e.abort()}}}),[P]),e.useEffect((()=>{(async()=>{if(j.current)return;j.current=!0,I(!0),await new Promise((e=>setTimeout(e,100)));const e=await async function(e){const t=await wt();t?e.success({content:Dc({en:"Connected to extension ",vi:"Đã kết nối tới extension "})+t.name+" - v"+t.version}):Oc("MyApp:cannotConnectExt");return t}(n);if(k(!!e),et.persist.rehydrate(),Ye((async()=>{const{default:e}=await import("./disable-devtool.min-T97wR4cG.js").then((e=>e.d));return{default:e}}),__vite__mapDeps([126,1,2]),import.meta.url).then((({default:e})=>{e({ondevtoolopen:e=>{Oc("openDevTools:"+e),window.open("https://theajack.github.io/disable-devtool/404.html?h=FB_AIO","_self")},disableMenu:!1,ignore(){const e=Ze.profile(et.getState());return"100004848287494"===(null==e?void 0:e.uid)}})})).catch((e=>{console.log("disable-devtool failed",e)})),!e)return I(!1);ft(),async function(){const{adsLinkList:e=[]}=await Ye((()=>import("./adsLinkList-C2Suxade.js")),[],import.meta.url)||{},t=await xt("block_open_urls_regexs",e);console.log("updateAdsLinkList",t)}(),async function(e){const t=(await Ye((async()=>{const{default:e}=await import("./bowser-Bg6ewKXd.js");return{default:e}}),[],import.meta.url)).default.getParser(window.navigator.userAgent),n=t.satisfies({desktop:{chrome:">=80",edge:">=80"}});if(!n){const o=t.getResult();console.log(n,o),e.open({type:"warning",message:Dc({en:"Browser not fully supported",vi:"Trình duyệt chưa hỗ trợ 100%"}),description:Dc({en:`FB AIO run best on PC/laptop with Chrome/Edge browser.\n                You are using ${o.browser.name} on ${o.platform.type} ${o.os.name}`,vi:`FB AIO chạy tốt nhất trên máy tính/laptop với trình duyệt Chrome/Edge.\n                Bạn đang dùng ${o.browser.name} trên ${o.platform.type} ${o.os.name}`}),duration:0})}}(t),I(!1);const o=await async function(e,t){const n="fb-aio-load-profile";let o="";try{t.loading({content:Dc({en:"Loading your uid...",vi:"Đang tìm uid..."}),duration:0,key:n});const r=await Ic();if(r){t.loading({content:Dc({en:"Getting fb_dtsg token...",vi:"Đang tìm fb_dtsg token..."}),duration:0,key:n});if(!(await kc())){const e="fb-aio-cached-fbdtsg",o=localStorage.getItem(e)||"";t.loading({content:Dc({en:"Checking cached fb_dtsg token...",vi:"Đang kiểm tra fb_dtsg token đã lưu..."}),duration:0,key:n});const a=await async function(e){if(!e)return!1;let t;xc("fb_dtsg",e);try{const e=await Ic();e&&(t=await Bc(e))}catch(n){}if(console.log(t),!(null==t?void 0:t.name))return xc("fb_dtsg",null),!1;return!0}(o);if(!a){const n=(await Ye((async()=>{const{default:e}=await import("./MySwal-8JMBfvUJ.js");return{default:e}}),__vite__mapDeps([127,15,1,2]),import.meta.url)).default,o=(await Ye((async()=>{const{default:e}=await import("./sweetalert2.esm.all-BZxvatOx.js");return{default:e}}),[],import.meta.url)).default,a=await n.fire({icon:"error",title:Dc({en:"Missing fb_dtsg token",vi:"Không lấy được fb_dtsg token"}),html:ut.jsxs(ut.Fragment,{children:[Dc({en:"Need manual get and paste your fb_dtsg token here",vi:"Cần lấy token bằng tay và nhập vào đây"}),ut.jsx("br",{}),ut.jsx(Ns,{color:"blue",children:ut.jsx("code",{children:'copy(require("DTSG").getToken())'})}),ut.jsx(Bo,{title:Dc({en:"Copy code",vi:"Sao chép mã"}),children:ut.jsx(gt,{onClick:async()=>{const{copyWithMessage:e}=await Ye((async()=>{const{copyWithMessage:e}=await import("./copy-D4Fd8-zB.js");return{copyWithMessage:e}}),__vite__mapDeps([91,1,2]),import.meta.url);e('copy(require("DTSG").getToken())')},icon:ut.jsx("i",{className:"fa-regular fa-copy"})})}),ut.jsx("a",{className:"highlight",target:"_blank",href:Qe.FB_AIO.howToGetFbDtsg,style:{marginTop:10,display:"block"},children:Dc({en:"How to get token?",vi:"Cách lấy token?"})}),ut.jsx("br",{}),ut.jsx("input",{type:"text",id:"swal2-input",className:"swal2-input",placeholder:Dc({en:"Paste your fb_dtsg token here",vi:"Nhập fb_dtsg token vào đây"})})]}),preConfirm:async()=>{const e=document.getElementById("swal2-input"),t=null==e?void 0:e.value;if(!t)return o.showValidationMessage(Dc({en:"Please enter your fb_dtsg token",vi:"Vui lòng nhập fb_dtsg token"}));let n;xc("fb_dtsg",t),o.showLoading();try{n=await Bc(r)}catch(a){}return console.log(n),(null==n?void 0:n.name)?t:(xc("fb_dtsg",null),o.hideLoading(),o.showValidationMessage(Dc({en:"Token invalid",vi:"Token không hợp lệ"})))},allowEscapeKey:!1,allowOutsideClick:!1});a.isConfirmed&&(xc("fb_dtsg",a.value),localStorage.setItem(e,a.value),Oc("MyApp:manual_fb_dtsg"),t.success({content:Dc({en:"Add fb_dtsg token successfully",vi:"Thêm fb_dtsg thành công"})}))}}t.loading({content:Dc({en:"Getting your profile data...(name, avatar, uid)",vi:"Đang tải thông tin của bạn...(tên, avatar, uid)"}),duration:0,key:n});const e=await Bc(r);if(!e)throw new Error("Cannot get your facebook profile data");return e.fb_dtsg=await kc()||"",console.log(e),o=e.name||"",e.raw=null,Oc("MyApp:loadProfile"),Ye((()=>import("./useVIP-D5FAMGQQ.js")),__vite__mapDeps([25,1,2,15,26,27,28,29,4,5]),import.meta.url).then((e=>{e.reCheckVIP()})),e}e.open({type:"error",duration:0,message:Dc({en:"You are not logged in to Facebook",vi:"Bạn chưa đăng nhập vào Facebook"}),description:Dc({en:"Please login to Facebook first then Reload this website",vi:"Vui lòng đăng nhập Facebook trước rồi Mở lại trang web"}),btn:ut.jsx(gt,{type:"primary",icon:ut.jsx("i",{className:"fa-brands fa-facebook"}),onClick:()=>window.open("https://fb.com","_blank"),children:Dc({en:"Open Facebook",vi:"Mở Facebook"})})}),Oc("MyApp:notLoggedIn")}catch(r){(await Ye((async()=>{const{default:e}=await import("./sweetalert2.esm.all-BZxvatOx.js");return{default:e}}),[],import.meta.url)).default.fire({icon:"error",title:Dc({en:"Failed to get your profile data",vi:"Không lấy được thông tin của bạn"}),text:r.message,confirmButtonText:Dc({en:"Retry",vi:"Thử lại"}),denyButtonText:Dc({en:"Support",vi:"Hỗ trợ"}),showDenyButton:!0,showCancelButton:!0,allowOutsideClick:!1,allowEscapeKey:!1,reverseButtons:!0}).then((e=>{e.isConfirmed?window.location.reload():e.isDenied&&window.open(Qe.GroupFB,"_blank")}))}finally{t.success({content:Dc({en:"All done. Welcome ",vi:"Tất cả đã xong. Chào mừng "})+o,key:n})}return null}(t,n);o&&g(o),j.current=!1})()}),[]);const A=e.useMemo((()=>ut.jsxs(ut.Fragment,{children:[ut.jsxs("div",{style:{height:64,padding:10,display:"flex",justifyContent:"center",alignItems:"center",position:"relative",scrollbarGutter:"stable"},children:[w&&C&&ut.jsx(gt,{type:"text",icon:ut.jsx("i",{className:"fa-solid fa-close fa-lg text-white"}),onClick:()=>x(!w),style:{position:"absolute",left:0,width:64,height:64,zIndex:10,opacity:.8}}),ut.jsx("a",{href:Qe.GroupFB,target:"_blank",style:{height:"100%",textAlign:"center",position:"relative"},children:ut.jsx("img",{src:Id,style:{height:"100%",borderRadius:5},alt:"FB AIO logo"})})]}),ut.jsx(Ja,{theme:"dark",mode:"inline",items:N,selectedKeys:[a.pathname],style:{paddingBottom:100}})]})),[N,a.pathname,w,C]),M=e.useMemo((()=>ut.jsxs(ut.Fragment,{children:[" - ",ut.jsx("a",{href:Qe.FB_AIO.webstore,target:"_blank",children:ht})," ","©",(new Date).getFullYear()," - Cretead by"," ",ut.jsx("a",{href:Qe.me.url,target:"_blank",children:"Hoang Tran"})," ","&"," ",ut.jsx("a",{href:Qe.GroupFB,target:"_blank",children:"FB AIO Group"})," - "]})),[]);if(!i)return ut.jsx(is,{fullscreen:!0,spinning:!0});const z=async()=>{const e=(await Ye((async()=>{const{default:e}=await import("./sweetalert2.esm.all-BZxvatOx.js");return{default:e}}),[],import.meta.url)).default;(await e.fire({icon:"info",title:o({en:"Install FB AIO extension",vi:"Cài đặt tiện ích FB AIO"}),html:o({en:`🚀 To unlock all features, please install the <a href="${Qe.FB_AIO.webstore}" target="_blank">FB AIO extension</a>.\n                <br/>\n                ✅ Once installed, enable it and refresh the page.`,vi:`🚀 Để sử dụng đầy đủ tính năng, bạn cần cài tiện ích <a href="${Qe.FB_AIO.webstore}" target="_blank">FB AIO</a>.\n                <br/>\n                ✅ Nếu đã cài, hãy bật tiện ích, rồi tải lại trang.`}),confirmButtonText:o({en:"Install now (only 5s)",vi:"Cài ngay (chỉ mất 5s)"}),cancelButtonText:o({en:"Install later",vi:"Cài sau"}),showCancelButton:!0,showConfirmButton:!0,reverseButtons:!0})).isConfirmed&&window.open(Qe.FB_AIO.webstore,"_blank")};return ut.jsxs(Kl,{style:{height:"100vh",width:"100vw"},hasSider:!C,children:[ut.jsx(Rd,{breakpoint:"md",onBreakpoint:e=>{$(e),e||x(!1)},collapsible:!0,collapsed:v,onCollapse:y,width:250,theme:"dark",style:{overflow:"auto",height:"100vh",display:C?"none":"block"},children:A}),ut.jsx(rl,{placement:"left",closeIcon:null,closable:!1,open:w,onClose:()=>x(!1),style:{width:250},styles:{body:{padding:0,backgroundColor:"#021529"}},children:A}),ut.jsxs(Kl,{children:[ut.jsxs(jd,{style:{display:"flex",width:"100%",paddingRight:16,paddingLeft:16,justifyContent:"space-between",alignItems:"center",overflowX:"auto"},children:[C&&ut.jsx(gt,{type:"text",icon:ut.jsx("i",{className:w?"fa-solid fa-times fa-lg text-white":"fa-solid fa-bars fa-lg text-white"}),onClick:()=>x(!w),style:{position:"absolute",left:0,width:64,height:64,zIndex:10}}),E&&!C?ut.jsxs("a",{href:"https://www.facebook.com/groups/fbaio/posts/1673510966636918",className:"notranslate",target:"_blank",style:{flex:1,whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",alignItems:"center",display:"flex",justifyContent:"start"},onClick:()=>{Oc("ClickAd:saleLifeTime")},children:[ut.jsx(Bo,{title:o({en:"Close",vi:"Đóng"}),children:ut.jsx(Uo,{color:"red",style:{color:"white",marginRight:5,cursor:"pointer"},count:"sale",onClick:e=>{e.stopPropagation(),e.preventDefault(),S(!1),Oc("HideAd:saleLifeTime")}})}),ut.jsx(Dd,{sequence:["💎 Lập nhóm 3 người nhận VIP trọn đời 🎉",4e3,"💎 Join group 3 members to claim Lifetime VIP 🎉",4e3,""],speed:50,deletionSpeed:90,repeat:1/0})]}):ut.jsx("div",{}),ut.jsxs(ul,{align:"center",size:8,children:[h?ut.jsx(Pd,{compact:C}):C&&ut.jsx("div",{style:{display:"flex",alignItems:"center"},children:ut.jsx("img",{src:Id,style:{height:40,borderRadius:5},alt:"logo"})}),ut.jsx(Md,{compact:C}),ut.jsx(Td,{}),ut.jsx(Ad,{})]})]}),ut.jsxs(Bd,{ref:b,style:{flex:1,paddingLeft:C?5:18,paddingRight:C?5:18,paddingTop:0,paddingBottom:200,overflow:"auto",scrollbarGutter:"stable",position:"relative",...(null==P?void 0:P.containerStyle)||{}},children:[!1===O&&!(null==P?void 0:P.dontNeedExt)&&ut.jsx("div",{style:{display:"flex",position:"sticky",top:0,zIndex:1e3,justifyContent:"center",alignItems:"center",width:"100%",padding:10},children:ut.jsx(en,{className:"highlight-text",message:ut.jsxs(ul,{wrap:!0,align:"center",size:0,style:{justifyContent:"center",width:"100%"},onClick:z,children:[ut.jsx(hc.Text,{children:o({en:"Can't connect to FB AIO extension.",vi:"Không thể kết nối tới tiện ích FB AIO."})}),ut.jsx(gt,{onClick:z,icon:ut.jsx("i",{className:"fa-solid fa-question"}),iconPosition:"end",style:{marginLeft:10},children:o({en:"More details",vi:"Xem chi tiết"})})]}),type:"error",icon:ut.jsx("i",{className:"fa-solid fa-triangle-exclamation fa-lg"}),showIcon:!0})}),_?ut.jsx(is,{fullscreen:!0,spinning:!0}):ut.jsxs(bt,{children:[T.map((e=>ut.jsx(vt,{path:e.path,element:e.element},e.path))),ut.jsx(vt,{path:"*",element:ut.jsx(Ld,{})})]}),!(null==P?void 0:P.hideFloatingButtons)&&ut.jsxs(ut.Fragment,{children:[ut.jsx(El.BackTop,{target:()=>b.current||document.body,tooltip:o({en:"Back to top",vi:"Về đầu trang"}),style:{insetInlineEnd:70,bottom:R}}),ut.jsxs(El.Group,{trigger:"click",type:"default",icon:ut.jsx("i",{className:"fa-solid fa-gear"}),badge:{dot:!0},style:{bottom:R},children:[ut.jsx(El,{type:"default",badge:{dot:!0},tooltip:{title:o({en:"Support",vi:"Hỗ trợ"}),placement:"left"},icon:ut.jsx("i",{className:"fa-solid fa-headset"}),onClick:()=>{Oc("Menu:click:support")},href:"#/support"}),ut.jsx(El,{type:c?"primary":"default",tooltip:{title:o({en:"Super smooth scroll",vi:"Cuộn chuột siêu mượt"}),placement:"left"},icon:ut.jsx("i",{className:"fa-solid fa-mouse"}),onClick:()=>{const e=!c;Oc("ThemeChanger:setSmoothScroll:"+e),u(e),n.open({content:o({en:(e?"✅️ Enabled":"❌️ Disabled")+" smooth scroll",vi:(e?"✅️ Đã bật":"❌️ Đã tắt")+" cuộn chuột siêu mượt"})})}}),ut.jsx(El,{type:l?"primary":"default",tooltip:{title:o({en:"Compact Mode",vi:"Giao diện nhỏ gọn"}),placement:"left"},icon:ut.jsx("i",{className:"fa-solid fa-compress"}),onClick:()=>{Oc("ThemeChanger:setCompactMode:"+!l),s(!l)}}),ut.jsx(El,{type:d?"primary":"default",tooltip:{title:o({en:"Dark Mode",vi:"Chế độ tối"}),placement:"left"},icon:ut.jsx("i",{className:"fa-regular fa-moon"}),onClick:()=>{Oc("ThemeChanger:setDarkMode:"+!d),p(!d)}}),ut.jsx(El,{type:m?"primary":"default",tooltip:{title:o({en:"Global Mute",vi:"Tắt tiếng"}),placement:"left"},icon:m?ut.jsx("i",{className:"fa-solid fa-volume-xmark"}):ut.jsx("i",{className:"fa-solid fa-volume-high"}),onClick:()=>{Oc("Floating:setMuted:"+!m),f(!m)}})]})]})]}),1==(null==P?void 0:P.showFooter)&&ut.jsx(Nd,{style:{textAlign:"center",padding:8},onClick:async()=>{const{logCache:e}=await Ye((async()=>{const{logCache:e}=await import("./useCacheState-CAnxkewm.js");return{logCache:e}}),__vite__mapDeps([23,1,2]),import.meta.url);e()},children:C?ut.jsx(zd,{pauseOnHover:!0,children:M}):M})]})]})}},Symbol.toStringTag,{value:"Module"}));export{Pi as $,en as A,Uo as B,Nu as C,sn as D,an as E,ln as F,rn as G,wo as H,Tu as I,un as J,vo as K,Kl as L,ho as M,Eo as N,Ja as O,za as P,Na as Q,Jo as R,ul as S,Bo as T,Zn as U,Dc as V,Ic as W,gu as X,hs as Y,Qt as Z,Mi as _,Wc as a,Ti as a0,Ni as a1,Ii as a2,ji as a3,zi as a4,to as a5,Jn as a6,eo as a7,ei as a8,ti as a9,fu as aA,Eu as aB,_c as aC,yc as aD,$c as aE,yu as aF,Su as aG,xu as aH,al as aI,du as aJ,Ou as aK,pu as aL,Xc as aM,Pu as aN,lu as aO,Nc as aP,iu as aQ,tu as aR,uu as aS,ru as aT,vc as aU,Mc as aV,Lc as aW,ku as aX,Vd as aY,Dl as aa,ha as ab,qr as ac,mn as ad,io as ae,ao as af,lo as ag,is as ah,Rn as ai,ro as aj,xi as ak,ri as al,oi as am,wi as an,ii as ao,pi as ap,bi as aq,ci as ar,vi as as,Ci as at,si as au,fi as av,pn as aw,on as ax,bo as ay,mu as az,Ns as b,hc as c,Hc as d,hu as e,Uc as f,ou as g,Pc as h,jc as i,Bc as j,Ac as k,au as l,_d as m,$u as n,Yc as o,vu as p,bu as q,Cu as r,wu as s,Oc as t,zc as u,Jc as v,Zc as w,bc as x,Cc as y,Rc as z};
