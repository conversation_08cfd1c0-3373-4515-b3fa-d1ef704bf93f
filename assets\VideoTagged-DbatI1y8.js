const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./VideoViewer-DSZVlhxl.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./videos-D2LbKcXH.js","./MyApp-DW5WH4Ub.js","./useVIP-D5FAMGQQ.js","./sweetalert2.esm.all-BZxvatOx.js","./getIds-DAzc-wzf.js","./index-Bg865k-U.js","./EyeOutlined-CNKPohhw.js","./SearchOutlined--mXbzQ68.js","./index-IxNfZAD-.js","./Dropdown-BcL-JHCf.js"])))=>i.map(i=>d[i]);
import{r as e,b0 as i,b5 as n,b1 as t,aN as o}from"./index-Cak6rALw.js";import{u as d,d as l,t as r,S as a,i as s,h as m,g as u,aQ as v,l as c,e as p,f as g,T as h,o as j,b as f,I as x}from"./MyApp-DW5WH4Ub.js";import{a as y}from"./tagged-BWZdodWv.js";import w from"./useCacheState-CAnxkewm.js";import k from"./MyTable-DnjDmN7z.js";import{E as b}from"./ExportButton-CbyepAf_.js";import{I as V}from"./index-CDSnY0KE.js";import"./index-ByRdMNW-.js";import"./index-BzMPigdO.js";import"./DownOutlined-B-JcS-29.js";import"./Table-BO80-bCE.js";import"./addEventListener-C4tIKh7N.js";import"./List-B6ZMXgC_.js";import"./index-D6X7PZwe.js";import"./index-CxAc8H5Q.js";import"./index-QU7lSj_a.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-C5gzSBcY.js";import"./index-SRy1SMeO.js";import"./SearchOutlined--mXbzQ68.js";import"./useBreakpoint-CPcdMRfj.js";import"./Pagination-2X6SDgot.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./row-BMfM-of4.js";const _=t((()=>o((()=>import("./VideoViewer-DSZVlhxl.js")),__vite__mapDeps([0,1,2,3,4]),import.meta.url)),{fallback:x});function I({target:t}){const{ti:x}=d(),{message:I}=l(),[T,N]=w("VideoTagged."+(null==t?void 0:t.id),[]),[L,E]=w("VideoTagged.loading."+(null==t?void 0:t.id),!1),[C,P]=w("VideoTagged.loadMore."+(null==t?void 0:t.id),!1),[A,D]=e.useState(!0);e.useEffect((()=>{R()}),[null==t?void 0:t.id]);const O=e.useRef(null==t?void 0:t.id),R=(e=!1)=>{if(!(null==t?void 0:t.id)||L)return;const i=O.current!==(null==t?void 0:t.id);(e||!T.length||i)&&(O.current=null==t?void 0:t.id,r("VideoTagged:onClickReload"),E(!0),y(t.id).then((e=>{(null==e?void 0:e.length)?(console.log(e),N(e),D(!0)):(I.info(x({en:"No data: Video Tagged",vi:"Không có dữ liệu: Lượt tag video"})),D(!1))})).finally((()=>{E(!1)})))},S=i.jsx(n,{icon:i.jsx("i",{className:"fa-solid fa-play"}),loading:C,disabled:!A||L,onClick:async()=>{var e;if(!(null==t?void 0:t.id)||C)return;const{checkVIP:i}=await o((async()=>{const{checkVIP:e}=await import("./useVIP-D5FAMGQQ.js");return{checkVIP:e}}),__vite__mapDeps([5,1,2,4,6,7,8,9,10,11,12]),import.meta.url);await i()&&(r("Tagged:onClickLoadMore"),P(!0),y(t.id,null==(e=T[T.length-1])?void 0:e.cursor).then((e=>{const i=new Set(T.map((e=>e.id))),n=e.filter((e=>!i.has(e.id)));n.length?(N((e=>[...e,...n])),D(!0)):(I.info(x({en:"No more data",vi:"Không còn dữ liệu"})),D(!1))})).finally((()=>P(!1))))},children:x(A?{en:"Load more",vi:"Tải thêm"}:{en:"No more data",vi:"Không còn dữ liệu"})}),M=[{title:"#",key:"index",dataIndex:"index"},{title:x({en:"Owner",vi:"Người tag"}),key:"from",dataIndex:"from",render:(e,n,t)=>{var o,d,l,r,u;return(null==(o=null==n?void 0:n.from)?void 0:o.id)?i.jsxs(a,{children:[i.jsx(V,{src:s(null==(d=null==n?void 0:n.from)?void 0:d.id),style:{width:45,height:45,borderRadius:5}}),i.jsxs(a,{direction:"vertical",size:0,children:[i.jsx("a",{href:m(null==(l=null==n?void 0:n.from)?void 0:l.id),target:"_blank",rel:"noreferrer",children:i.jsx("b",{children:null==(r=null==n?void 0:n.from)?void 0:r.name})}),i.jsx("span",{style:{opacity:.5},children:null==(u=null==n?void 0:n.from)?void 0:u.id})]})]}):x({en:"Anonymous",vi:"Ẩn danh"})},filters:Array.from(new Set(null==T?void 0:T.map((e=>{var i;return null==(i=e.from)?void 0:i.id})))).map((e=>{var i,n;const t=T.filter((i=>{var n;return(null==(n=i.from)?void 0:n.id)===e}));return{value:e||"",text:((null==(n=null==(i=null==t?void 0:t[0])?void 0:i.from)?void 0:n.name)||x({en:"Anonymous",vi:"Ẩn danh"}))+` (${null==t?void 0:t.length})`,count:null==t?void 0:t.length}})).sort(((e,i)=>i.count-e.count)),onFilter:(e,i)=>{var n;return(null==(n=i.from)?void 0:n.id)==e},width:250},{title:x({en:"Video",vi:"Video"}),key:"video",dataIndex:"video",render:(e,n,t)=>i.jsx(V,{src:null==n?void 0:n.picture,style:{width:200,height:150,objectFit:"contain"},preview:{destroyOnHidden:!0,toolbarRender:()=>null,imageRender:()=>i.jsx(_,{forceGetVideoInfo:!0,info:{id:null==n?void 0:n.id,source:null==n?void 0:n.source}})}})},{title:x({en:"Views",vi:"Lượt xem"}),key:"views",dataIndex:"views",render:(e,n,t)=>i.jsx("span",{children:u(null==n?void 0:n.views)}),sorter:(e,i)=>e.views-i.views,width:100},{title:x({en:"Length",vi:"Độ dài"}),key:"length",dataIndex:"length",render:(e,n,t)=>i.jsx("span",{children:v(null==n?void 0:n.length)}),sorter:(e,i)=>e.length-i.length,width:100},{title:x({en:"Content",vi:"Nội dung"}),key:"message",dataIndex:"message",render:(e,n,o)=>{var d;return i.jsx("div",{style:{wordBreak:"break-word",maxWidth:400},children:(null==(d=c(null==n?void 0:n.description,200))?void 0:d.replace(null==t?void 0:t.name,""))||(null==t?void 0:t.name)})}},{title:x({en:"Created at",vi:"Thời gian"}),key:"created_time",dataIndex:"created_time",render:(e,n,t)=>i.jsxs("span",{children:[p(new Date(e).getTime())," - ",g(new Date(e))]})},{title:x({en:"Action",vi:"Hành động"}),key:"action",dataIndex:"action",render:(e,t,o)=>i.jsx(h,{title:x({en:"View post",vi:"Xem bài viết"}),children:i.jsx(n,{target:"_blank",href:m((null==t?void 0:t.permalink_url)||(null==t?void 0:t.id)),icon:i.jsx("i",{className:"fa-solid fa-external-link"})})}),width:100}];return i.jsx(k,{data:T.map(((e,i)=>({...e,index:i+1}))),columns:M,searchable:!0,keyExtractor:e=>e.id,size:"small",renderTitle:()=>i.jsxs(i.Fragment,{children:[i.jsxs(a.Compact,{children:[i.jsx(n,{type:"primary",icon:i.jsx("i",{className:"fa-solid fa-rotate-right"}),loading:L,disabled:C,onClick:()=>R(!0),children:x({en:"Reload",vi:"Tải lại"})}),S]}),i.jsx(b,{data:T,options:[{key:"json",label:".json",prepareData:e=>({fileName:(null==t?void 0:t.name)+"_tagged_video.json",data:JSON.stringify(e,null,4)})},{key:"csv",label:".csv",prepareData:e=>({fileName:(null==t?void 0:t.name)+"_tagged_video.csv",data:j(e.map((e=>{var i,n;return{...e,from:null==(i=e.from)?void 0:i.id,fromName:null==(n=e.from)?void 0:n.name}})))})}]}),i.jsx(f,{color:"red",children:(null==T?void 0:T.length)+" "+x({en:"Tagged Video",vi:"Lượt tag video"})})]}),footer:i.jsx("div",{style:{width:"100%",display:"flex",justifyContent:"center"},children:S})})}export{I as default};
