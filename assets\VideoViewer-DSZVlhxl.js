const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./VideoWithMuted-CYlwayKv.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./AudioWithMuted-Cry1ElD_.js","./MyApp-DW5WH4Ub.js","./FileSaver.min-B-NmbgGb.js"])))=>i.map(i=>d[i]);
import{r as e,cP as i,b0 as t,b5 as a,aN as s,b1 as n}from"./index-Cak6rALw.js";import{getVideoInfo as o}from"./videos-D2LbKcXH.js";import{u as l,S as r,T as c,aT as d,ah as u,I as m}from"./MyApp-DW5WH4Ub.js";const h=n((()=>s((()=>import("./VideoWithMuted-CYlwayKv.js")),__vite__mapDeps([0,1,2]),import.meta.url)),{fallback:m}),f=n((()=>s((()=>import("./AudioWithMuted-Cry1ElD_.js")),__vite__mapDeps([3,1,2]),import.meta.url)),{fallback:m});function p({info:n,style:m,videoStyle:p,buttonTop:v=!1,getVideoInfoFn:j=o,defaultVariantTitle:x={en:"Default",vi:"Mặc định"},forceGetVideoInfo:y=!1}){var _;const{ti:g}=l(),[w,b]=e.useState(null),[A,C]=e.useState({}),[E,k]=e.useState(0),[T,V]=e.useState(!1),[I,R]=e.useState(n),N=(null==I?void 0:I.variants)||n.variants||[],O=(null==w?void 0:w.source)||(null==I?void 0:I.source);e.useEffect((()=>{(!n.source||y)&&n.id&&j?(V(!0),j(n.id).then((e=>{R(e)})).finally((()=>{V(!1)}))):R(n)}),[n.source,n.id,j]);const S=e.useRef(!1);e.useEffect((()=>{!(null==N?void 0:N.length)||Object.keys(A).length||S.current||(S.current=!0,Promise.all(N.map((e=>i(e.source).then((({url:i,size:t})=>{C((i=>({...i,[e.id]:t})))})).catch((()=>{console.log("ERROR get size",e)}))))).finally((()=>{S.current=!1})))}),[A,N]),e.useEffect((()=>{O&&i(O).then((({url:e,size:i})=>{k(i)}))}),[O]);const D=t.jsxs(r.Compact,{style:{maxWidth:600,flexWrap:"wrap",justifyContent:"center"},children:[t.jsx(c,{title:E?d(E,1):"",children:t.jsx(a,{type:w?"default":"primary",onClick:()=>b(null),children:g(x)})}),null==N?void 0:N.map(((e,i)=>t.jsx(c,{title:(e.isAudio?"":e.width+"x"+e.height)+(A[e.id]?" - "+d(A[e.id],1):""),children:t.jsxs(a,{type:(null==w?void 0:w.id)===e.id?"primary":"default",onClick:async()=>{b(e);const{trackEvent:i}=await s((async()=>{const{trackEvent:e}=await import("./MyApp-DW5WH4Ub.js").then((e=>e.aV));return{trackEvent:e}}),__vite__mapDeps([4,1,2]),import.meta.url);i("VideoViewer:onChangeVariant:"+(e.qualityLabel||e.mimeType))},icon:e.isAudio?t.jsx("i",{className:"fa-solid fa-music"}):null,children:[e.qualityClass," ",e.qualityLabel||e.mimeType]})},e.id)))]});return t.jsxs(r,{direction:"vertical",style:{padding:15,borderRadius:10,backgroundColor:"#222",justifyContent:"center",alignItems:"center",overflowY:"auto",...m},children:[!T&&v&&D,T?t.jsx(u,{tip:g({en:"Loading video...",vi:"Đang tải video..."}),children:t.jsx("div",{style:{width:150,height:50}})}):null,(null==w?void 0:w.isAudio)?t.jsx(f,{src:O}):O?t.jsx(h,{src:O,style:{maxWidth:"90vw",maxHeight:"70vh",borderRadius:10,...p},children:null==(_=null==w?void 0:w.urls)?void 0:_.map(((e,i)=>t.jsx("source",{src:e},i)))}):null,O&&t.jsxs(r.Compact,{children:[t.jsx(c,{placement:"bottom",title:t.jsxs(t.Fragment,{children:[g({en:"If cannot download, try open FB_AIO in ",vi:"Nếu không tải được, hãy thử mở FB_AIO bằng "}),t.jsxs("a",{href:"https://coccoc.com/",target:"_blank",style:{color:"inherit",textDecoration:"underline"},children:[g({en:"Coc Coc Browser",vi:"Trình duyệt Cốc Cốc"})," ",t.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"})]})]}),children:t.jsx(a,{icon:t.jsx("i",{className:"fa-solid fa-download"}),onClick:async()=>{const{saveAs:e}=await s((async()=>{const{saveAs:e}=await import("./FileSaver.min-B-NmbgGb.js").then((e=>e.F));return{saveAs:e}}),__vite__mapDeps([5,1,2]),import.meta.url);e(O,(null==w?void 0:w.isAudio)?"audio.mp3":"video.mp4")},children:g({en:"Download",vi:"Tải xuống"})})}),t.jsx(a,{icon:t.jsx("i",{className:"fa-solid fa-arrow-up-right-from-square"}),onClick:()=>window.open(O,"_blank","noreferrer,noopener"),children:g({en:"New tab",vi:"Tab mới"})})]}),!T&&!v&&D]})}export{p as default};
