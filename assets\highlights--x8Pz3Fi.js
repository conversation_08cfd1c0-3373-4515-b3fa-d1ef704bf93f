import{x as i,az as o}from"./MyApp-DW5WH4Ub.js";import{fetchInstaGraphQl as e,getBiggestUrl as n}from"./core-Dw7OsFL6.js";async function l(n){const l=await e({fb_api_req_friendly_name:"PolarisProfileStoryHighlightsTrayContentDirectQuery",variables:{user_id:n},doc_id:"7612410165515693"}),t=i(l),{edges:a=[],page_info:r={}}=o(t);return console.log(t),a.map((i=>{var o,e,n,l,t;return{id:null==(o=null==i?void 0:i.node)?void 0:o.id,title:null==(e=null==i?void 0:i.node)?void 0:e.title,cover:null==(t=null==(l=null==(n=null==i?void 0:i.node)?void 0:n.cover_media)?void 0:l.cropped_image_version)?void 0:t.url}}))}async function t(l){var t,a,r;const d=await e({fb_api_req_friendly_name:"PolarisStoriesV3HighlightsPageQuery",variables:{initial_reel_id:l,reel_ids:[l],first:3,last:2},doc_id:"7854629807955730"}),s=i(d);console.log(s);const{edges:u=[],page_info:_={}}=o(s);return null==(r=null==(a=null==(t=null==u?void 0:u[0])?void 0:t.node)?void 0:a.items)?void 0:r.map((i=>{var o,e;const l=i||{};return{id:l.id,type:"",width:l.original_width,height:l.original_height,image:n(null==(o=l.image_versions2)?void 0:o.candidates),video:n(l.video_versions),comment_count:l.comment_count,like_count:l.like_count,play_count:l.play_count,view_count:l.view_count,cursor:"",raw:i,caption:(null==(e=l.caption)?void 0:e.text)||"",created_at:1e3*(l.taken_at||0)}}))}export{t as a,l as g};
