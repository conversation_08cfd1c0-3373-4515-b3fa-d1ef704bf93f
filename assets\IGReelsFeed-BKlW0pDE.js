const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./VideoViewer-DSZVlhxl.js","./index-Cak6rALw.js","./index-DIX6R8z-.css","./videos-D2LbKcXH.js","./MyApp-DW5WH4Ub.js"])))=>i.map(i=>d[i]);
import{bc as e,r as s,b0 as i,b1 as r,b5 as o,bk as t,aN as a}from"./index-Cak6rALw.js";import{u as n,t as l,A as d,S as m,aS as c,e as p,T as j,c as x,l as h,I as u}from"./MyApp-DW5WH4Ub.js";import f from"./Collection-BYcChfHL.js";import{getInstaReelsFeed as g}from"./reels-CadhT4bZ.js";import{getInstaUrlFromId as b}from"./core-Dw7OsFL6.js";import{L as v}from"./index-jrOnBmdW.js";import{I as y}from"./index-CDSnY0KE.js";import{A as w}from"./index-DdM4T8-Q.js";import"./index-ByRdMNW-.js";import"./useCacheState-CAnxkewm.js";import"./react-hotkeys-hook.esm-wjq6pdfC.js";import"./index-PbLYNhdQ.js";import"./index-QU7lSj_a.js";import"./index-CxAc8H5Q.js";import"./index-Dg3cFar0.js";import"./Dropdown-BcL-JHCf.js";import"./PurePanel-BvHjNOhQ.js";import"./move-ESLFEEsv.js";import"./index-Bg865k-U.js";import"./EyeOutlined-CNKPohhw.js";import"./SearchOutlined--mXbzQ68.js";import"./row-BMfM-of4.js";import"./useBreakpoint-CPcdMRfj.js";import"./index-CC5caqt_.js";import"./index-C5gzSBcY.js";import"./List-B6ZMXgC_.js";import"./index-SRy1SMeO.js";import"./DownOutlined-B-JcS-29.js";import"./Pagination-2X6SDgot.js";import"./col-CzA09p0P.js";import"./videos-D2LbKcXH.js";import"./addEventListener-C4tIKh7N.js";const k=r((()=>a((()=>import("./VideoViewer-DSZVlhxl.js")),__vite__mapDeps([0,1,2,3,4]),import.meta.url)),{fallback:u});function I(){const{ti:r}=n(),a=e();s.useEffect((()=>{l("IGReelsFeed:onLoad")}),[]);return i.jsx(f,{collectionName:"IGReelsFeed",fetchNext:async(e,s)=>{var i;return await g(s||(null==(i=null==e?void 0:e[e.length-1])?void 0:i.cursor)||"")},renderItem:(e,s)=>i.jsx(v.Item,{children:i.jsxs("div",{className:"show-on-hover-trigger",children:[i.jsxs("div",{style:{position:"relative"},children:[i.jsx(y,{src:e.image,style:{width:200,height:300,borderRadius:10,objectFit:"cover"},preview:{destroyOnClose:!0,toolbarRender:()=>null,imageRender:()=>i.jsx(k,{info:{source:e.video,variants:e.variants}})}}),i.jsxs("div",{style:{color:"#eee",background:"linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%)",position:"absolute",bottom:0,left:0,padding:"5px 10px",width:"100%",paddingTop:"50px",pointerEvents:"none",display:"flex",flexDirection:"column"},children:[e.music&&i.jsxs(m,{children:[i.jsx("i",{className:"fa-solid fa-music"}),e.music.title]}),i.jsxs(m,{children:[i.jsx("i",{className:"fa-regular fa-heart"}),c(e.like_count)]}),i.jsxs(m,{children:[i.jsx("i",{className:"fa-regular fa-comment"}),c(e.comment_count)]}),i.jsxs(m,{children:[i.jsx("i",{className:"fa-regular fa-clock"}),p(e.created_at)]})]}),i.jsxs(m,{className:"show-on-hover-item",direction:"horizontal",style:{position:"absolute",bottom:10,right:10},size:3,children:[i.jsx(j,{title:r({en:"Open in Bulk Downloader",vi:"Mở trong Tải hàng loạt"}),children:i.jsx(o,{icon:i.jsx("i",{className:"fa-solid fa-search"}),onClick:()=>{a("/bulk-downloader",{state:{targetId:e.user.username,platform:t.Instagram}})}})}),i.jsx(o,{icon:i.jsx("i",{className:"fa-solid fa-up-right-from-square"}),target:"_blank",href:b("reel/"+e.code)})]})]}),i.jsx("div",{style:{width:200,display:"flex",flexDirection:"column"},children:i.jsx(j,{title:e.caption,children:i.jsx(x.Text,{children:h(e.caption,80)})})}),i.jsxs("div",{style:{position:"absolute",top:5,left:5,right:5,display:"flex"},children:[i.jsx("div",{style:{position:"relative",height:40},children:i.jsx(w,{onClick:()=>window.open(b(e.user.username),"_blank"),src:e.user.avatar,size:40,style:{borderWidth:2,borderColor:"#0866FF",marginRight:3,cursor:"pointer"}})}),i.jsx("div",{style:{display:"inline-block",background:"#1119",padding:"3px 5px",borderRadius:5,pointerEvents:"none",alignSelf:"center",flex:1},children:i.jsx(x.Text,{style:{color:"#eee",fontWeight:"bold"},children:e.user.username})})]})]})}),downloadItem:async(e,s)=>({name:e.id+".mp4",url:e.video}),getItemCursor:e=>null==e?void 0:e.cursor,rowKey:e=>e.id,header:()=>i.jsx(d,{type:"info",message:i.jsxs(i.Fragment,{children:[i.jsx("i",{className:"fa fa-eye-slash",style:{color:"gray"}})," ",r({en:"Watch all Reels without being tracked by Instagram",vi:"Lướt Reels ẩn danh - Không bị Instagram theo dõi"})]})})})}export{I as default};
